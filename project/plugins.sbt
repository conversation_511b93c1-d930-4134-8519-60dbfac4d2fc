logLevel := Level.Warn

externalResolvers := Seq(
  Resolver.defaultLocal,
  "Agoda Maven".at("https://repo-hkg.agodadev.io/agoda-maven/"),
  "Agoda Snapshot".at("https://repo-hkg.agodadev.io/agoda-maven-local-snapshot/")
)

addDependencyTreePlugin

addSbtPlugin("org.scoverage" % "sbt-scoverage" % "2.3.0")

addSbtPlugin("io.stryker-mutator" % "sbt-stryker4s" % "0.16.1_loggingv3")

addSbtPlugin("com.github.sbt" % "sbt-release" % "1.4.0")

addSbtPlugin("com.eed3si9n" % "sbt-buildinfo" % "0.7.0")

addSbtPlugin("com.github.sbt" % "sbt-native-packager" % "1.9.6")

addSbtPlugin("org.scalastyle" %% "scalastyle-sbt-plugin" % "1.0.0")

addSbtPlugin("org.scalameta" % "sbt-scalafmt" % "2.4.6")

addSbtPlugin("com.waioeka.sbt" % "cucumber-plugin" % "0.2.1")

addSbtPlugin("io.gatling" % "gatling-sbt" % "2.2.2")

addSbtPlugin("pl.project13.scala" % "sbt-jmh" % "0.3.3")

addSbtPlugin("com.agoda.commons" % "ag-codegen-sbt-plugin" % "0.6.7-RC3")

addSbtPlugin("com.agoda.commons" % "sbt-agoda" % "0.4.0")

//For Profile compile time
//addCompilerPlugin("ch.epfl.scala" %% "scalac-profiling" % "1.0.0")
//addSbtPlugin("ch.epfl.scala" % "sbt-bloop" % "1.4.6")

ThisBuild / libraryDependencySchemes ++= Seq(
  "org.scala-lang.modules" %% "scala-xml" % VersionScheme.Always
)
