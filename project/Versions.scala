import sbt._

//////////////////////////////////////////////////////////////////////////////
// DEPENDENCY VERSIONS
//////////////////////////////////////////////////////////////////////////////
object Versions {

  object Scala {
    val SCALA_2_12 = "2.12.18"
  }

  object Agoda {
    val dfapi              = "4.23.5"
    val contentPlatform    = "0.8.798" //  https://gitlab.agodadev.io/IT-Platform/content-platform
    val nonPropertyContent = "2.79.0"  //  https://gitlab.agodadev.io/IT-Platform/non-property-content
    val commons            = "0.16.3"  //  https://gitlab.agodadev.io/IT-Platform/commons
    val cacheRemote        = "0.15.2"  //  https://gitlab.agodadev.io/IT-Platform/commons
    val whiteLabelClient =
      "10.1105.0" //  https://gitlab.agodadev.io/white-label/white-label/-/blob/master/client/scala/
    val agTracing    = "2.2.1"  //  https://gitlab.agodadev.io/common-libs/ag-tracing
    val logging      = "0.11.1" //  https://gitlab.agodadev.io/common-libs/ag-logging
    val agCouchbase  = "2.4.0"  //  https://gitlab.agodadev.io/common-libs/ag-couchbase
    val agSql        = "3.1.0"  //  https://gitlab.agodadev.io/common-libs/ag-sql
    val agHttpClient = "2.11.0" //  https://gitlab.agodadev.io/common-libs/ag-http-client
    val agHttpServer = "1.12.0" //  https://gitlab.agodadev.io/common-libs/ag-http-server
    // [0.4.15 has been overwritten, use rerelease]
    val agObservability    = "0.4.15-restore.2" //  https://gitlab.agodadev.io/common-libs/ag-observability
    val agSerialization    = "1.5.0"            //  https://gitlab.agodadev.io/common-libs/ag-commons
    val agConcurrency      = "1.5.0"            //  https://gitlab.agodadev.io/common-libs/ag-commons
    val agStream           = "0.9.0"
    val agCompression      = "1.5.0"            //  https://gitlab.agodadev.io/common-libs/ag-commons
    val agConfiguration    = "0.15.2"           //  https://gitlab.agodadev.io/common-libs/ag-commons
    val agProtobuf         = "3.804.0"          //  https://gitlab.agodadev.io/common-libs/ag-protobufs
    val agProtobufPapi     = "3.458.0"          //  https://gitlab.agodadev.io/common-libs/ag-protobufs
    val agProtobufProperty = "3.305.0"          //  https://gitlab.agodadev.io/common-libs/ag-protobufs
    val promoApi           = "1.1.9"            //  https://gitlab.agodadev.io/IT-Platform/gandalf
    val agVault            = "1.1.0"            //  https://gitlab.agodadev.io/common-libs/ag-vault
    val experiment         = "4.3.6"            //  https://gitlab.agodadev.io/common-libs/ag-experiments-scala

    val supplierCommon     = "0.1.5"   //  https://gitlab.agodadev.io/IT-Platform/supplier-common
    val papistreaming      = "2.3.3"   //  https://github.agodadev.io/IT-Platform/papi-stream
    val universalCmsClient = "0.3.3"   //  https://github.agodadev.io/it-whitelabel/universal-cms-client
    val upi                = "0.5.76"  //  https://gitlab.agodadev.io/packages/upi
    val bundler            = "1.369.0" //  https://gitlab.agodadev.io/packages/bundler

    // upgrading beyond this version without fixing SPL exposure multiwatcher will constantly log exception
    val agConsul = "3.0.3" //  https://gitlab.agodadev.io/common-libs/ag-consul

    val agGraphQL             = "0.6.2"         //  https://gitlab.agodadev.io/IT-Platform/ag-graphql
    val paymentApiClientUtils = "0.3.5"         //  https://github.agodadev.io/ebe/payment-api-client-utils
    val enumcommons           = "0.9.8"         //  https://gitlab.agodadev.io/IT-Platform/enum-commons
    val styxBotProfileParser  = "1.0.1"         //  https://gitlab.agodadev.io/styx/botprofile-parser
    val serviceContext        = "0.6.1"         //  https://gitlab.agodadev.io/IT-Platform/service-context
    val agCache               = "0.8.0"         //  https://gitlab.agodadev.io/common-libs/ag-cache
    val cancellation          = "1.0.6"         //  https://gitlab.agodadev.io/accommodation-pricing/cancellation
    val agGrpcV0              = "2.8.8"         //  https://gitlab.agodadev.io/common-libs/ag-grpc
    val sangria               = "3.0.0-agoda.1" //  https://gitlab.agodadev.io/IT-Platform/sangria
    val supplyCacheSql        = "7.24.7"        //  https://gitlab.agodadev.io/IT-Platform/supply-cache-api
    val consulClient        = "1.4.5"  //  https://gitlab.agodadev.io/common-libs/ag-consul/-/tree/master/shaded-ecwid
    val trafficForwarder    = "4.18.1" // https://gitlab.agodadev.io/common-libs/acm
    val growthProgramClient = "0.1.2"  //  https://gitlab.agodadev.io/full-stack/supply/supply-growth/growth-program
    val fsOnlineClient      = "2.0.0"
    val regulatoryBlockingClient = "0.96.0" //  https://gitlab.agodadev.io/legal-tech/regulatory-blocking-system
    val agodaJvmTesting          = "0.0.5"  // https://gitlab.agodadev.io/agoda-e2e/jvm-testing-library
    val agJdbcCache              = "1.0.23" //  https://gitlab.agodadev.io/common-libs/incubating/ag-jdbc-cache

  }

  val sangriaCirce         = "1.3.2"
  val graphqlJava          = "21.5"
  val asyncHttpClient      = "2.10.2"
  val jts                  = "1.14.0"
  val cucumber             = "6.10.0"
  val userContext          = "1.0.14"
  val rflows               = "0.13.1"
  val productCollection    = "1.4.5"
  val scalaTest            = "3.2.3"
  val scalaTestPlusMockito = "3.2.10.0"
  val slf4j                = "2.0.0"
  val akka                 = "2.6.18"
  val akkaHttp             = "10.2.4"
  val akkaForClient        = "2.5.26"
  val akkaHttpForClient    = "10.1.11"
  val scaldi               = "0.5.8"
  val specs2               = "4.6.0"
  val scalaMock            = "3.6.0"
  val mockito              = "2.23.4"
  val jackson              = "2.11.4"
  val jacksonModule        = "2.11.4-compat-2.9.x"
  val circeOptics          = "0.14.1"
  val typesafeConfig       = "1.4.0"
  val zenith_model         = "1.2.4"
  val zenithClient         = "1.2.10"
  val elastic4s            = "7.17.4"
  val elasticsearch        = "7.17.4"
  val esTestcontainers     = "1.19.1"
  val netty                = "4.1.53.Final"
  val openTelemetry        = "1.24.0"
  val openTelemetryAlpha   = s"$openTelemetry-alpha"
  val tapir                = "1.7.2"
  val pact4s               = "0.16.1"

  /** Require by Elastic * */
  val akkaHttpClient          = "1.7.0"
  val supplierPricing         = "8.1.45"
  val supplierExposureControl = "0.9.5"
  val sentries                = "0.8.0"
  val apache_common_lang      = "3.6"
  val apache_common_math      = "3.6.1"
  val capi                    = "4.6.11"
  val enumeratum              = "1.6.0"
  val joda                    = "2.10.1"
  val jarvis                  = "1.1.87"
  val adpMessaging            = "2.9.4.3"
  val hikari                  = "2.4.3"
  val guava                   = "25.1-jre"
  val scalacache              = "0.28.0"
  val commonsLogging          = "1.2"
  val scalalog                = "3.5.0"
  val couchbaseClient         = "1.4.9"
  val httpcore                = "4.3"
  val logback                 = "1.3.5"
  val jettison                = "1.1"
  val commonsCodec            = "1.5"
  val fst                     = "2.44"
  val akkaHttpPlayJson        = "1.17.0"
  val slService               = "1.3.1"

  val archaiusScala = "0.7.6" // dynamic config

  val gatling = "2.2.5"

  val monadicForPlugin = "0.3.1"

  val lucene = "6.3.0"

  val pointLocation6709 = "4.2.1"

  /** Lib for test ADP Messaging Message
    */
  val kiteDataHive      = "1.0.0-cdh5.8.0"
  val hiveMetastore     = "1.1.0-cdh5.8.0"
  val avroMapred        = "1.8.2"
  val nameOf            = "1.0.3"
  val priceStreamClient = "0.3.5"

  val sttp          = "3.1.9"
  val akkaHttpCors  = "1.1.1"
  val akkaHttpCirce = "1.37.0"
  val wiremock      = "2.23.2"

  val circe       = "0.14.3"
  val circeDerive = "0.13.0-M5"
  val circeYaml   = "0.14.2"

  val grpc         = "1.47.1"
  val json4s       = "0.9.3"
  val nscalaTime   = "2.22.0"
  val jtsVersion   = "1.14.0"
  val msJdbc       = "9.2.1.jre8"
  val asm          = "5.1"
  val lz4          = "1.8.0"
  val diffx        = "0.8.3"
  val mockitoScala = "1.17.37"

  val snakeYaml = "2.0"

  def commonExclude(in: ModuleID): ModuleID = {
    val moduleID = commonExcludeWithoutCommonsLogging(in)
      .exclude("commons-logging", "commons-logging")
      .exclude("io.netty", "netty")
      .exclude("io.netty", "netty-handler")
      .exclude("io.netty", "netty-codec-http")
      .exclude("io.netty", "grpc-netty")
    excludeAgProtobufs(moduleID)
  }

  def commonExcludeWithoutCommonsLogging(in: ModuleID): ModuleID =
    in.exclude("com.agoda.adp.messaging", "adp-messaging-client-shaded")
      .exclude("org.jboss.netty", "netty")
      .exclude("io.netty", "netty")
      .exclude("io.netty", "netty-handler")
      .exclude("io.netty", "netty-codec-http")
      .exclude("com.agoda.experiments", "experiments-platform-client")
      .exclude("org.slf4j", "slf4j-log4j12")
      .exclude("com.typesafe.akka", s"akka-http-core_2.12")
      .exclude("com.typesafe.akka", s"akka-http_2.12")
      .exclude("com.typesafe.akka", s"akka-http-jackson_2.12")
      .exclude("com.typesafe.akka", s"akka-actor_2.12")
      .exclude("com.typesafe.akka", s"akka-remote_2.12")
      .exclude("com.typesafe.akka", s"akka-stream_2.12")
      .exclude("com.typesafe.akka", s"akka-slf4j_2.12")
      .exclude("com.typesafe.akka", s"akka-testkit_2.12")
      .exclude("org.scalatest", s"scalatest_2.12")
      .exclude("org.specs2", s"specs2_2.12")
      .exclude("org.mockito", "mockito-core")
      .exclude("com.codahale.metrics", "metrics-core")
      .exclude("com.codahale.metrics", "metrics-graphite")
      .exclude("com.codahale.metrics", "metrics-json")
      .exclude("com.codahale.metrics", "metrics-jvm")
      .exclude("com.fasterxml.jackson.core", "jackson-core")
      .exclude("com.fasterxml.jackson.core", "jackson-databind")
      .exclude("com.fasterxml.jackson.core", "jackson-annotations")
      .exclude("com.fasterxml.jackson.core", s"jackson-module-scala_2.12")
      .exclude("com.fasterxml.jackson.datatype", "jackson-datatype-jdk8")
      .exclude("com.fasterxml.jackson.datatype", "jackson-datatype-jsr310")
      .exclude("com.fasterxml.jackson.dataformat", "jackson-dataformat-smile")
      .exclude("com.fasterxml.jackson.dataformat", "jackson-dataformat-yaml")
      .exclude("com.fasterxml.jackson.dataformat", "jackson-dataformat-cbor")
      .exclude("com.fasterxml.jackson.module", s"jackson-module-scala_2.12")
      .exclude("com.agoda", "cassandra-wrapper")
      .exclude("com.agoda", s"supplier-pricing_2.12")
      .exclude("org.asynchttpclient", "async-http-client")
      .exclude("org.asynchttpclient", "async-http-client-netty-utils")
      .exclude("com.trueaccord.lenses", "lenses")
      .exclude("com.sksamuel.elastic4s", "elastic4s-core")
      .exclude("com.ning", "async-http-client")
      .exclude("com.agoda.commons", s"ag-http-server_2.12")
      .exclude("com.agoda.commons", s"ag-http-server-api_2.12")
      .exclude("com.agoda.commons", s"ag-http-server-circe_2.12")
      .exclude("com.agoda.platform", s"ag-gatekeeper_2.12")
      .exclude("com.agoda.platform", s"ag-gatekeeper-akka_2.12")
      .exclude("com.agoda.platform", s"ag-gatekeeper-api_2.12")
      .exclude("com.agoda.commons", "ag-tracing-api_2.12")
      .exclude("com.agoda.commons", "ag-tracing-core_2.12")
      .exclude("com.agoda.commons", "ag-tracing-noop_2.12")
      .exclude("com.agoda.commons", "ag-tracing-rpc-akka-http_2.12")
      .exclude("com.agoda.commons", "ag-tracing-rpc-grpc_2.12")
      .exclude("com.agoda.commons", "ag-tracing-rpc_2.12")
      .exclude("com.agoda.dfapi", "ypl-models_2.12")
      .exclude("com.agoda.dfapi", "dfapi_price-calculation_2.12")
      .exclude("log4j", "log4j")

  // TODO: remove this after migrate to ScalaPB 0.11.x
  def excludeAgProtobufs(in: ModuleID): ModuleID =
    in.exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-utils_2.12")
      .exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-cupid_2.12")
      .exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-df_2.12")
      .exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-mix-n-save_2.12")
      .exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-ota_2.12")
      .exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x-spapi_2.12")
      .exclude("com.agoda.commons", "ag-protobuf-scalapb-0-11-x_2.12")

}
