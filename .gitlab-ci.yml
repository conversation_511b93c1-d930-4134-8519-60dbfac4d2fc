default:
  interruptible: true

.changes_for_cache_populator: &changes_for_cache_populator
  changes:
    - "cache-client/**/*"
    - "cache-populator/**/*"
    - "database/SQL/cdb/app_papi_search_ro/stored procedures/propertyapi.cache_propertyinfolist_fetch.sql"
    - "database/SQL/cdb/app_papi_search_ro/stored procedures/propertyapi.cache_dmclist_fetch.sql"
    - "database/SQL/cdb/app_papi_search_ro/stored procedures/propertyapi.cache_dmclist_fetch_b_variant.sql"


.changes_for_log_analyzer: &changes_for_log_analyzer
  changes:
    - "project/Versions.scala"
    - "project/build.properties"
    - "project/plugins.sbt"
    - "build.sbt"

# SBT_WORKDIR variable will come from .sbt devops template
.sbt_cache_paths: &sbt_cache_paths
  paths:
    - "${SBT_WORKDIR}/sbt-cache/ivy/cache"
    - "${SBT_WORKDIR}/sbt-cache/boot"
    - "${SBT_WORKDIR}/sbt-cache/sbtboot"
    - "${SBT_WORKDIR}/sbt-cache/coursier"

.common_rules:
  rules:
    - &merge_request_rule
      if: '$CI_PIPELINE_SOURCE == "merge_request_event"'
    - &merge_train_rule
      if: '$CI_MERGE_REQUEST_EVENT_TYPE == "merge_train"'
    - &develop_branch_rule
      if: '$CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push"'
    - &releases_branch_rule
      if: '$CI_COMMIT_BRANCH == "releases"'
    - &schedule_pipeline_rule
      if: '$CI_PIPELINE_SOURCE == "schedule"'
    - &create_package_rule
      if: '$CI_COMMIT_BRANCH == "develop" && $CREATE_PACKAGE == "true"'
    - &stale_mr_notifier_rule
      if: '$SCHEDULE_STALE_MR_NOTIFY == "true"'
    - &stability_pipeline_rule
      if: '$SCHEDULE_NAME == "pipeline-stability"'
    - &reset_cluster_rule
      if: '$RESET_CLUSTER == "true"'
    - &dependency_verification_rule
      if: '$VERIFICATION_PIPELINE == "true"'
    - &acm_schedule_every_dcs
      if: '$SCHEDULE_NAME == "acm-schedule-every-dcs"'
    - &renovate_bot_tag
      if: '$CI_MERGE_REQUEST_LABELS == "renovate-bot"'

include:
  - local: "/ci/templates/workflow.gitlab-ci.yml"
  - local: "/ci/templates/common.gitlab-ci.yml" # This file will have the inclusion of remote gitlab template files
  - local: "/ci/templates/dependency-verification.gitlab-ci.yml"
  - local: "/ci/templates/content-dependency-verification.gitlab-ci.yml"
  - local: "/ci/templates/cdb-dependency-verification.gitlab-ci.yml"
  - local: "/ci/templates/df-dependency-verification.gitlab-ci.yml"
  - local: "/ci/templates/s2h-it-test.gitlab-ci.yml"
  - local: "/ci/templates/reset-cluster.gitlab-ci.yml"
  - local: "/ci/templates/k8s-tests.gitlab-ci.yml"
  - local: "/ci/templates/slo_monitoring.yml"

  - component: gitlab.agodadev.io/devops/cicd-components/container/kaniko@v2
    rules:
      - *stability_pipeline_rule
      - *develop_branch_rule
      - *releases_branch_rule
      - *merge_request_rule
      - *create_package_rule
    inputs:
      stage: build
      prefix: papi-
      dockerfile: $CI_PROJECT_DIR/DockerfileGitlab
      image: "$HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH"
      tags: "$HARBOR_TAG,latest"
      harbor_password: "$ENCODED_HARBOR_PASSWORD"
      build_needs:
        - job: prepare_variables
        - job: sbt_compile_with_push_cache
          optional: true
        - job: sbt_compile_without_push_cache
          optional: true

  - component: gitlab.agodadev.io/devops/cicd-components/container/kaniko@v2
    rules:
      - <<: *develop_branch_rule
        <<: *changes_for_cache_populator
      - <<: *releases_branch_rule
        <<: *changes_for_cache_populator
      - <<: *merge_request_rule
        <<: *changes_for_cache_populator
    inputs:
      stage: build
      prefix: cache-populator-
      dockerfile: $CI_PROJECT_DIR/cache-populator/Dockerfile
      image: "$CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH"
      tags: "$CACHE_POPULATOR_HARBOR_TAG,latest"
      harbor_password: "$ENCODED_HARBOR_PASSWORD"
      build_needs:
        - job: prepare_variables
        - job: sbt_compile_cache_populator_with_push_cache
          optional: true
        - job: sbt_compile_cache_populator_without_push_cache
          optional: true

  - project: "devops/ci-templates"
    ref: master
    file:
      - "/templates/PrivateCloud/Deployment.gitlab-ci.yml"
      - "/templates/Kube/Kube.gitlab-ci.yml"
      - "/templates/Scala/Sbt.gitlab-ci.yml"
      - "/templates/Scala/Sbt_cucumber.gitlab-ci.yml"
  - project: 'bpf-velocity/ci-templates'
    ref: master
    file:
      - '/templates/pact/Pact_Verify.gitlab-ci.yml'
      - '/templates/pact/Pact.gitlab-ci.yml'

  - project: 'agoda-e2e/testing-ci-templates'
    ref: 'main'
    file:
      - '/templates/mutation/mutation.gitlab-ci.yml'

variables:
  CDB_UNITS: 4
  PAPI_UNITS: 1
  EXTERNAL_TRIGGER_PROJECT_NAME: "propertyapi"
  CANDIDATE_PACKAGE_DEPLOY_PLAN: ""
  CANDIDATE_PACKAGE_PACKAGE_NAME: ""
  CANDIDATE_PACKAGE_SERVER_LIST: ""
  CREATE_PACKAGE: "false"

stages:
  - .pre
  - pre_build
  - build
  - upload
  - deploy
  - test
  #  - chaos_test
  - dependencies_verification_getversion
  - dependencies_verification_get_digest
  - dependencies_verification_test
  - dependencies_verification_release
  - dependencies_verification_cleanup
  - notify_outdated_verfied_dependencies
  - notify_stale
  - monitor
  - bump
  - commit
  - push
  - pre_release
  - notify_release
  - publish

check_new_commit_develop:
  extends: [ .git_remote_push_set ]
  stage: .pre
  rules:
    - *create_package_rule
  script:
    - |
      #!/bin/bash
      git remote -v
      git fetch origin
      git log -300 --pretty=format:"%s" | grep 'Change version to' -B 200 -m 1 | sed '$d' > git_history.txt || true
      result=$(<git_history.txt)
      echo $result
      if [ -z "$result" ]; then
         exit 1
      fi

#chaos_test:
#  stage: chaos_test
#  rules:
#    - <<: *merge_request_rule
#      when: manual
#      allow_failure: true
#  trigger:
#    include:
#      - local: "/ci/templates/chaos_test.gitlab-ci.yml"
#    strategy: depend

dont-interrupt-me:
  stage: .pre
  rules:
    - *schedule_pipeline_rule
  needs: [ ]
  variables:
    GIT_STRATEGY: none
  script:
    - echo "This jobs makes sure this pipeline won't be interrupted! See https://docs.gitlab.com/ee/ci/yaml/#interruptible."
  interruptible: false
  allow_failure: true

prepare_variables:
  stage: .pre
  rules:
    - *stability_pipeline_rule
    - *develop_branch_rule
    - *releases_branch_rule
    - *merge_request_rule
    - *create_package_rule
  variables:
    ALPINE_GIT_TAG: "v2.30.2"
  cache: [ ]
  image:
    name: reg-hk.agodadev.io/dockerhub/alpine/git:$ALPINE_GIT_TAG
    entrypoint: [ "" ]
  script:
    - |
      env | grep '^CI_'
    - git fetch
    - |
      HAS_DB_CHANGED=false
      DATABASE_CHANGED=false
      COMMON_DATABASE_CHANGED=false
      PROPERTY_DATABASE_CHANGED=false
      COMPLEXSEARCH_DATABASE_CHANGED=false
      git diff --quiet origin/develop HEAD -- database || DATABASE_CHANGED=true
      git diff --quiet origin/develop HEAD -- common/src/main/scala/com/agoda/papi/search/common/externaldependency/repository || COMMON_DATABASE_CHANGED=true
      git diff --quiet origin/develop HEAD -- property/src/main/scala/com/agoda/papi/search/property/externaldependency/repository || PROPERTY_DATABASE_CHANGED=true
      git diff --quiet origin/develop HEAD -- complexsearch/src/main/scala/com/agoda/papi/search/complexsearch/externaldependency/repository || COMPLEXSEARCH_DATABASE_CHANGED=true
      if [[ "$DATABASE_CHANGED" == true || "$COMMON_DATABASE_CHANGED" == true || "$PROPERTY_DATABASE_CHANGED" == true || "$COMPLEXSEARCH_DATABASE_CHANGED" == true ]]; then
        echo "DB Change database folder is $DATABASE_CHANGED, common folder is $COMMON_DATABASE_CHANGED, property folder is $PROPERTY_DATABASE_CHANGED, complexsearch folder is $COMPLEXSEARCH_DATABASE_CHANGED"
        HAS_DB_CHANGED=true
      else
        echo "db hasn't changed"
      fi

      HAS_CACHE_POP_CHANGED="false"
      FIRST="origin/develop"
      LAST="HEAD"
      # On Release pipeline, there is no easy to get list of file changes
      # The following scripts will extract the commit of previous-version to use it as a starting-point
      if [[ "$CI_COMMIT_BRANCH" == "develop" && $CI_PIPELINE_SOURCE == "schedule" && $CREATE_PACKAGE == "true" ]]
      then
        # git log -1 --grep will print a commit-hash and commit-message separated by whitespace of previous "Change version to"
        # cut will extract only commit-hash
        FIRST=$(git log -1 --grep "Change version to " --pretty=format:"%h %s" | cut -d ' ' -f 1)
      fi
      echo "commits to check cache-populator change\nFIRST=$FIRST LAST=$LAST"
      git diff --quiet $FIRST $LAST -- cache-client || HAS_CACHE_POP_CHANGED=true
      git diff --quiet $FIRST $LAST -- cache-populator || HAS_CACHE_POP_CHANGED=true
      # TODO: Define all SPs related with Cache-Populator in a single place
      git diff --quiet $FIRST $LAST -- "database/SQL/cdb/app_papi_search_ro/stored procedures/propertyapi.cache_propertyinfolist_fetch.sql" || HAS_CACHE_POP_CHANGED=true
      git diff --quiet $FIRST $LAST -- "database/SQL/cdb/app_papi_search_ro/stored procedures/propertyapi.cache_dmclist_fetch.sql" || HAS_CACHE_POP_CHANGED=true
      git diff --quiet $FIRST $LAST -- "database/SQL/cdb/app_papi_search_ro/stored procedures/propertyapi.cache_dmclist_fetch_b_variant.sql" || HAS_CACHE_POP_CHANGED=true
      echo "HAS_CACHE_POP_CHANGED=$HAS_CACHE_POP_CHANGED"

      BASE_VERSION=$(egrep -o '".*?"' version.sbt | tr -d '"')
      CACHE_POPULATOR_BASE_VERSION=$(egrep -o '".*?"' cache-client/version.sbt | tr -d '"')
      PIPELINE_VERSION=$BASE_VERSION"_gl_"$CI_PIPELINE_ID
      DB_SQL_PACKAGE_NAME="propertyapi_search_sql_gl_"$BASE_VERSION"_"$CI_PIPELINE_ID".zip"
      PIPELINE_PACKAGE_NAME=$PIPELINE_VERSION".yml"
      TRIGGER_AFFILIATE_TESTS="false"
      TRIGGER_FUZZ_TESTS="false"
      PUSH_LATEST="false"
      if [[  "$CI_COMMIT_BRANCH" == "releases" ]]
      then
        PUSH_LATEST="true"
        HARBOR_SUB_PATH="release"
        HARBOR_TAG="$BASE_VERSION.gl"
        CACHE_POPULATOR_HARBOR_TAG="$CACHE_POPULATOR_BASE_VERSION"
        PREFIX="gl-releases"
        VERSION=$BASE_VERSION
        TRIGGER_AFFILIATE_TESTS="$TRIGGER_AFFILIATE_TESTS_FOR_RELEASES"
        DB_SQL_PACKAGE_NAME="propertyapi_search_sql_gl_"$BASE_VERSION".zip"
        FAKE_API_IMAGE_TAG="latest"
        FAKE_API_IMAGE_REPO="e2e-test/fake-api/propertyapisearchdocker/release"
      elif [[ "$CI_COMMIT_BRANCH" == "develop" ]]
      then
        PUSH_LATEST="true"
        HARBOR_SUB_PATH="develop"
        HARBOR_TAG=$PIPELINE_VERSION
        CACHE_POPULATOR_HARBOR_TAG=$CACHE_POPULATOR_BASE_VERSION
        PREFIX=$(echo "gl-develop-$CI_PIPELINE_ID")
        VERSION=$(echo "$BASE_VERSION.gl.$CI_PIPELINE_ID")
        TRIGGER_AFFILIATE_TESTS="$TRIGGER_AFFILIATE_TESTS_FOR_DEVELOP"
        TRIGGER_FUZZ_TESTS="true"
        FAKE_API_IMAGE_TAG="latest"
        FAKE_API_IMAGE_REPO="e2e-test/fake-api/propertyapisearchdocker/develop"
      elif [[ "$CI_PIPELINE_SOURCE" == "merge_request_event" ]]
      then
        HARBOR_SUB_PATH="merge_request"
        HARBOR_TAG=$CI_MERGE_REQUEST_IID.$CI_PIPELINE_ID
        CACHE_POPULATOR_HARBOR_TAG=$CACHE_POPULATOR_BASE_VERSION.gl.$CI_PIPELINE_ID
        PREFIX=$(echo "gl-merge-request-$CI_MERGE_REQUEST_IID")
        VERSION=$(echo "$BASE_VERSION.gl.$CI_PIPELINE_ID")
        FAKE_API_IMAGE_TAG=$CI_MERGE_REQUEST_IID.$CI_PIPELINE_IID
        FAKE_API_IMAGE_REPO="e2e-test/fake-api/propertyapisearchdocker/merge_request"
      elif [[ "$CI_PIPELINE_SOURCE" == "external_pull_request_event" ]]
      then
        HARBOR_SUB_PATH="pull"
        HARBOR_TAG=$CI_EXTERNAL_PULL_REQUEST_IID
        CACHE_POPULATOR_HARBOR_TAG=$CI_EXTERNAL_PULL_REQUEST_IID
        PREFIX=$(echo "gl-pull-$CI_EXTERNAL_PULL_REQUEST_IID")
        VERSION=$(echo "$BASE_VERSION.gl.$CI_PIPELINE_ID")
        FAKE_API_IMAGE_TAG=$CI_EXTERNAL_PULL_REQUEST_IID
        FAKE_API_IMAGE_REPO="e2e-test/fake-api/propertyapisearchdocker/pull"
      else
        HARBOR_SUB_PATH="pipeline"
        PREFIX=$(echo "gl-pipeline-$CI_PIPELINE_ID")
        VERSION=$(echo "$BASE_VERSION.gl.$CI_PIPELINE_ID")
        HARBOR_TAG=$VERSION
        CACHE_POPULATOR_HARBOR_TAG=$(echo "$CACHE_POPULATOR_BASE_VERSION.gl.$CI_PIPELINE_ID")
        FAKE_API_IMAGE_TAG=$CI_PIPELINE_IID
        FAKE_API_IMAGE_REPO="e2e-test/fake-api/propertyapisearchdocker/pipeline"
      fi

      DB_PREFIX=""
      ENABLE_CACHE_POP="false"
      K8S_TEST_VALUE_FILES="papi-search/papi-search-default-resource.yaml,papi-search/papi-search-pull-nocdb.yaml"
      if [[ $HAS_DB_CHANGED == "true" || $HAS_CACHE_POP_CHANGED == "true" ]]
      then
        K8S_TEST_VALUE_FILES="papi-search/papi-search-default-resource.yaml,papi-search/papi-search-pull.yaml"
        K8S_TEST_DB_PREFIX=$(echo "-propertyapi-$PREFIX")
        ENABLE_CACHE_POP=$HAS_CACHE_POP_CHANGED
      fi

      HARBOR_IMAGE=$HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH

      K8S_TEST_EXTRA_PARAMS="--set propertyapi.fullnameOverride=propertyapi-$PREFIX \
      --set propertyapi.image.repository=${HARBOR_REGISTRY}/${HARBOR_IMAGE} \
      --set propertyapi.image.tag=$HARBOR_TAG --set propertyapi.cdb.host=cdb$K8S_TEST_DB_PREFIX \
      --set propertyapi.papiCachePopulator.image.repository=$HARBOR_REGISTRY/$CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH \
      --set propertyapi.papiCachePopulator.image.tag=${CACHE_POPULATOR_HARBOR_TAG} \
      --set propertyapi.papiCachePopulator.enabled=${ENABLE_CACHE_POP} \
      --set cdb.fullnameOverride=cdb$K8S_TEST_DB_PREFIX" \

      for i in $(echo $K8S_TEST_VALUE_FILES | tr "," "\n")
        do
            K8S_TEST_EXTRA_PARAMS="-f https://devops.pages.agodadev.io/container/agoda-in-a-box/$i $K8S_TEST_EXTRA_PARAMS"
        done

      CACHE_POPULATOR_VERSION_BUMPED=false
      if [[ $HAS_CACHE_POP_CHANGED == "true" ]]
      then
        echo "checking if cache populator version is bumped"
        git diff --quiet $FIRST $LAST -- cache-client/version.sbt || CACHE_POPULATOR_VERSION_BUMPED=true
      fi

      echo "PIPELINE_VERSION=$PIPELINE_VERSION" >> build.env
      echo "PIPELINE_PACKAGE_NAME=$PIPELINE_PACKAGE_NAME" >> build.env
      echo "BASE_VERSION=$BASE_VERSION" >> build.env
      echo "CACHE_POPULATOR_BASE_VERSION=$CACHE_POPULATOR_BASE_VERSION" >> build.env
      echo "DB_SQL_PACKAGE_NAME=$DB_SQL_PACKAGE_NAME" >> build.env
      echo "HARBOR_IMAGE=$HARBOR_IMAGE" >> build.env
      echo "HARBOR_SUB_PATH=$HARBOR_SUB_PATH" >> build.env
      echo "HARBOR_IMAGE_PATH=$HARBOR_REGISTRY/$HARBOR_IMAGE" >> build.env
      echo "CACHEPOPULATOR_HARBOR_PROJECT_PATH=$CACHEPOPULATOR_HARBOR_PROJECT_PATH" >> build.env
      echo "HARBOR_TAG=$HARBOR_TAG" >> build.env
      echo "CACHE_POPULATOR_HARBOR_TAG=$CACHE_POPULATOR_HARBOR_TAG" >> build.env
      echo "PUSH_LATEST=$PUSH_LATEST" >> build.env
      echo "PREFIX=$PREFIX" >> build.env
      echo "K8S_TEST_EXTRA_PARAMS=$K8S_TEST_EXTRA_PARAMS" >> build.env
      echo "VERSION=$VERSION" >> build.env
      echo "DOCKER_WORKDIR=." >> build.env
      echo "HAS_DB_CHANGED=$HAS_DB_CHANGED" >> build.env
      echo "IMAGE=$HARBOR_REGISTRY/$HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH:$HARBOR_TAG" >> build.env
      echo "CACHE_POPULATOR_IMAGE=$HARBOR_REGISTRY/$CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH" >> build.env
      echo "LATEST_IMAGE=$HARBOR_REGISTRY/$HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH:latest" >> build.env
      echo "TRIGGER_AFFILIATE_TESTS=$TRIGGER_AFFILIATE_TESTS" >> build.env
      echo "TRIGGER_FUZZ_TESTS=$TRIGGER_FUZZ_TESTS" >> build.env
      echo "HAS_CACHE_POP_CHANGED=$HAS_CACHE_POP_CHANGED" >> build.env
      PAPI_MAJORITY_PROD_VERSION=$(wget -qO- "http://propertyapisearchdocker-fe.hk.agoda.is/version")
      echo "PAPI_MAJORITY_PROD_VERSION=$PAPI_MAJORITY_PROD_VERSION.gl" >> build.env
      echo "CACHE_POPULATOR_VERSION_BUMPED=$CACHE_POPULATOR_VERSION_BUMPED" >> build.env
      echo "FAKE_API_IMAGE_REPO=$FAKE_API_IMAGE_REPO" >> build.env
      echo "FAKE_API_IMAGE_TAG=$FAKE_API_IMAGE_TAG" >> build.env
      UNITS=$PAPI_UNITS
      if [ "$HAS_DB_CHANGED" == true ]; then
        UNITS=$(($UNITS+$CDB_UNITS))
      fi
      echo "UNITS=$UNITS" >> build.env

      cat build.env
  artifacts:
    reports:
      dotenv: build.env
    expire_in: 1 day

scala_format:
  extends: [ .sbt, .pull_cache ]
  rules:
    - *merge_request_rule
  cache: [ ]
  needs: [ ]
  stage: .pre
  script:
    - sbt scalafmtCheckAll || (echo "Please execute 'sbt scalafmtAll'" | exit 1)

generate_kubeconfig:
  stage: .pre
  needs: [ ]
  rules:
    - *reset_cluster_rule
  extends: [ .korca_get_kubeconfig ]
  variables:
    NAMESPACE: $NAMESPACE_PAPI_CI_1
  retry: 1

generate_kubeconfig_verify_ci1:
  stage: .pre
  needs: [ ]
  rules:
    - *dependency_verification_rule
  extends: [ .korca_get_kubeconfig ]
  variables:
    NAMESPACE: $NAMESPACE_PAPI_VERIFY_CI_1

sbt_compile_with_push_cache:
  extends: [ .papi_sbt_compile ]
  needs:
    - prepare_variables
  rules:
    - *releases_branch_rule
  variables:
    COMPILE_TEST_CODE: "true"
    CACHE_KEY: "propertyapi-sbt-cache"
  cache:
    - key: ${CACHE_KEY}
      policy: push
      when: on_success
      <<: *sbt_cache_paths
    - key: ${CACHE_KEY}
      policy: push
      unprotect: true
      when: on_success
      <<: *sbt_cache_paths
  stage: pre_build

sbt_compile_cache_populator_with_push_cache:
  extends: [ .cache_populator_sbt_compile ]
  needs:
    - prepare_variables
  rules:
    - <<: *releases_branch_rule
      <<: *changes_for_cache_populator
  variables:
    COMPILE_TEST_CODE: "true"
    CACHE_KEY: "papicachepopulator-sbt-cache"
  cache:
    - key: ${CACHE_KEY}
      policy: push
      when: on_success
      <<: *sbt_cache_paths
    - key: ${CACHE_KEY}
      policy: push
      unprotect: true
      when: on_success
      <<: *sbt_cache_paths
  stage: pre_build

sbt_compile_without_push_cache:
  extends: [ .papi_sbt_compile, .pull_cache ]
  needs:
    - prepare_variables
  rules:
    - *stability_pipeline_rule
    - *merge_request_rule
    - *develop_branch_rule
    - *create_package_rule
  stage: pre_build

sbt_compile_cache_populator_without_push_cache:
  extends: [ .cache_populator_sbt_compile ]
  needs:
    - prepare_variables
  rules:
    - *stability_pipeline_rule
    - <<: *merge_request_rule
      <<: *changes_for_cache_populator
    - <<: *develop_branch_rule
      <<: *changes_for_cache_populator
  stage: pre_build
  variables:
    CACHE_KEY: "papicachepopulator-sbt-cache"
  cache:
    policy: pull


cache_populator_bump_version_check:
  rules:
    - <<: *merge_request_rule
      <<: *changes_for_cache_populator
  needs:
    - job: prepare_variables
      artifacts: true
  stage: .pre
  script:
    - |
      if [[ $HAS_CACHE_POP_CHANGED != "true" ]]
      then
        echo "No changes in cache populator, skipping bump version check"
        exit 0
      fi
    - echo "Checking if cache populator version is bumped"
    - |
      if [[ $CACHE_POPULATOR_VERSION_BUMPED == "true" ]]
      then
        echo "Cache populator version is bumped"
      else
        echo "Cache populator version is not bumped. please bump the version in cache-client/version.sbt"
        exit 1
      fi

#deploy cache populator to prelive on develop branch only for push pipeline
deploy_cache_populator_to_prelive_for_acm_test:
  extends: [ .pc_deploy ]
  rules:
    - <<: *develop_branch_rule
      <<: *changes_for_cache_populator
  stage: deploy
  variables:
    PRELIVE_ENV: "prelive-mr"
    PC_IMAGE_TAG: "$CACHE_POPULATOR_HARBOR_TAG"
    PC_IMAGE_REPO: "$CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH"
    PC_YAML_TEMPLATE: ./privatecloud-deployment/cache/prelive-papicachepopulator.yaml

# needed for deploying new cache populator image to papi-ci1
deploy_cache_populator_to_papi_ci1:
  extends: [ .kube_deploy ]
  stage: deploy
  rules:
    - <<: *develop_branch_rule
      <<: *changes_for_cache_populator
  variables:
    NAMESPACE: $NAMESPACE_PAPI_CI_1
    RELEASE_NAME: "cache-populator-papi-ci1"
    EXTRA_PARAMS: "--set deps.papicachepopulator=true --set papicachepopulator.image.repository=${CACHE_POPULATOR_IMAGE} --set papicachepopulator.image.tag=${CACHE_POPULATOR_HARBOR_TAG}"
    TIMEOUT: "600s"

code_quality:
  cache: [ ]
  stage: test
  extends: [ .code_quality ]
  rules:
    - *stability_pipeline_rule
    - *develop_branch_rule
    - *merge_request_rule
  needs: [ ]
  variables:
    ENGINE_MEMORY_LIMIT_BYTES: 2000000000

deploy_to_devstack:
  stage: deploy
  rules:
    - *merge_request_rule
    - *stability_pipeline_rule
  extends: .devstack_deploy
  needs:
    - job: "papi-build"
      optional: true
    - job: "prepare_variables"
  variables:
    DEVSTACK_WAIT: "true"
    HAS_CACHE_POP_CHANGED: $HAS_CACHE_POP_CHANGED
    DEVSTACK_ENV: dev
    DEVSTACK_TTL: 35m
    DEVSTACK_WAIT_TIMEOUT: "1200"
    DEVSTACK_CONFIG_FILE: $CI_PROJECT_DIR/devstack.yaml
    DEVSTACK_GRANTS_TEAMS: "team-it-propertyapi-search"
    DEVSTACK_OVERRIDE: |
      environments:
        dev:
          spec:
            artifact:
              image:
                repository: $HARBOR_IMAGE
                tag: "$HARBOR_TAG"
    DEVSTACK_OVERRIDE_WITH_CACHE_POPULATOR: |
      environments:
        dev:
          spec:
            initContainers:
              - name: run-cache-populator
                resources:
                  limits:
                    cpu: "2"
                    memory: 4Gi
                  requests:
                    cpu: "2"
                    memory: 4Gi
                artifact:
                  image:
                    repository: $CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH
                    tag: "$CACHE_POPULATOR_HARBOR_TAG"
                envs:
                  JAVA_OPTS: >
                    -Dconfig.file=/opt/cache-populator/conf/devstack.conf
                    -Dlogback.configurationFile=/opt/cache-populator/conf/logback.xml
                    -Xms2048m
                    -Xmx2048m
                command:
                  - /bin/sh
                  - -c
                  - /opt/cache-populator/cache_populator_start_ci.sh
            artifact:
              image:
                repository: $HARBOR_IMAGE
                tag: "$HARBOR_TAG"
  script:
    # if we have cache populator change. inject the init container for cache populator in the DEVSTACK_OVERRIDE
    #    - |
    #      if [[ $HAS_CACHE_POP_CHANGED == "true" ]]
    #      then
    #        echo "cache populator change detected, enabling cache populator init container"
    #        export DEVSTACK_OVERRIDE=$(echo "$DEVSTACK_OVERRIDE_WITH_CACHE_POPULATOR")
    #      else
    #        export DEVSTACK_OVERRIDE=$(echo "$DEVSTACK_OVERRIDE")
    #      fi
    - export DEVSTACK_OVERRIDE=$(echo "$DEVSTACK_OVERRIDE")
    - !reference [ .devstack_deploy, script ]
    - |
      CDB_INGRESS=$(cat "$DEVSTACK_OUTPUT_FILE" | jq -r '.dependencies[] | select(.name == "cdb") | .endpoint.ingress')
      CDB_NODE=$(echo "$CDB_INGRESS" | awk -F: '{print $1}')
      CDB_NODEPORT=$(echo "$CDB_INGRESS" | awk -F: '{print $2}')
    - echo "CDB_NODE=$CDB_NODE" >> devstack_out.env
    - echo "CDB_NODEPORT=$CDB_NODEPORT" >> devstack_out.env

integration_test:
  retry: 1
  needs:
    - deploy_to_devstack
    - prepare_variables
  stage: test
  variables:
    HAS_DB_CHANGED: $HAS_DB_CHANGED
  extends: [ .integration_test_template ]
  rules:
    - *merge_request_rule


deploy_dev_ci_devstack:
  allow_failure: true
  stage: deploy
  rules:
    - *merge_request_rule
    - *stability_pipeline_rule
  extends: .devstack_deploy
  needs:
    - job: "papi-build"
      optional: true
    - job: "prepare_variables"
  variables:
    DEVSTACK_WAIT: "true"
    HAS_CACHE_POP_CHANGED: $HAS_CACHE_POP_CHANGED
    DEVSTACK_ENV: dev-ci
    DEVSTACK_TTL: 35m
    DEVSTACK_WAIT_TIMEOUT: "1200"
    DEVSTACK_CONFIG_FILE: $CI_PROJECT_DIR/dev-ci-devstack.yaml
    DEVSTACK_GRANTS_TEAMS: "team-it-propertyapi-search"
    DEVSTACK_OVERRIDE: |
      environments:
        dev-ci:
          spec:
            artifact:
              image:
                repository: $HARBOR_IMAGE
                tag: "$HARBOR_TAG"
    DEVSTACK_OVERRIDE_WITH_CACHE_POPULATOR: |
      environments:
        dev-ci:
          spec:
            initContainers:
              - name: run-cache-populator
                resources:
                  limits:
                    cpu: "2"
                    memory: 4Gi
                  requests:
                    cpu: "2"
                    memory: 4Gi
                artifact:
                  image:
                    repository: $CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH
                    tag: "$CACHE_POPULATOR_HARBOR_TAG"
                envs:
                  JAVA_OPTS: >
                    -Dconfig.file=/opt/cache-populator/conf/devstack.conf
                    -Dlogback.configurationFile=/opt/cache-populator/conf/logback.xml
                    -Xms2048m
                    -Xmx2048m
                command:
                  - /bin/sh
                  - -c
                  - /opt/cache-populator/cache_populator_start_ci.sh
            artifact:
              image:
                repository: $HARBOR_IMAGE
                tag: "$HARBOR_TAG"
  script:
    # if we have cache populator change. inject the init container for cache populator in the DEVSTACK_OVERRIDE
    #    - |
    #      if [[ $HAS_CACHE_POP_CHANGED == "true" ]]
    #      then
    #        echo "cache populator change detected, enabling cache populator init container"
    #        export DEVSTACK_OVERRIDE=$(echo "$DEVSTACK_OVERRIDE_WITH_CACHE_POPULATOR")
    #      else
    #        export DEVSTACK_OVERRIDE=$(echo "$DEVSTACK_OVERRIDE")
    #      fi
    - export DEVSTACK_OVERRIDE=$(echo "$DEVSTACK_OVERRIDE")
    - !reference [ .devstack_deploy, script ]
    - |
      CDB_INGRESS=$(cat "$DEVSTACK_OUTPUT_FILE" | jq -r '.dependencies[] | select(.name == "cdb") | .endpoint.ingress')
      CDB_NODE=$(echo "$CDB_INGRESS" | awk -F: '{print $1}')
      CDB_NODEPORT=$(echo "$CDB_INGRESS" | awk -F: '{print $2}')
    - echo "CDB_NODE=$CDB_NODE" >> devstack_out.env
    - echo "CDB_NODEPORT=$CDB_NODEPORT" >> devstack_out.env

fake-api:integration_test:
  stage: test
  needs:
    - deploy_fake_api_to_devstack
    - build_and_publish_fake_api
  allow_failure: true
  rules:
    - <<: *merge_request_rule
  extends: [ .sbt_test_with_code_coverage, .pull_cache ]
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
    CODECOV_FLAG: "integration-test-with-fake-api"
  script:
    - PROPERTYAPI_HOST_PORT=$DEVSTACK_PRIMARY_ENDPOINT sbt "fakeApi/testOnly com.agoda.papi.search.integrationtest.*"
    - bash <(curl -s $CODECOV_BASH) -F $CODECOV_FLAG -t $CODECOV_TOKEN

dev-ci-env:integration_test:
  allow_failure: true
  needs:
    - deploy_dev_ci_devstack
    - prepare_variables
  stage: test
  variables:
    HAS_DB_CHANGED: $HAS_DB_CHANGED
  extends: [ .integration_test_template ]
  retry: 0
  rules:
    - *merge_request_rule
    - *stability_pipeline_rule

integration_test_stability:
  needs:
    - deploy_to_devstack
    - prepare_variables
  stage: test
  variables:
    HAS_DB_CHANGED: $HAS_DB_CHANGED
  extends: [ .integration_test_template ]
  rules:
    - *stability_pipeline_rule

#run_mutation_tests:
#  stage: test
#  allow_failure: true
#  needs: []
#  variables:
#    BASE_BRANCH: "origin/develop"
#    BRANCH: $CI_COMMIT_REF_NAME
#    AGGREGATOR_ID: 996
#  rules:
#    - if: '$CI_PIPELINE_SOURCE == "schedule" && $TEST_RUN_TYPE == "mutation"'
#      variables:
#        EXCLUDE_DIRECTORY: $EXCLUDE_DIRECTORY
#    - if: '($CI_PIPELINE_SOURCE == "merge_request_event") && $CI_COMMIT_REF_NAME != "develop" && $CI_COMMIT_REF_NAME != "releases"'
#      variables:
#        EXCLUDE_DIRECTORY: "core"
#  trigger:
#    include:
#      - project: "agoda-e2e/mutation-tests"
#        ref: master
#        file: "template/run-mutation-tests.gitlab-ci.yml"
#    strategy: depend

tSQLt_SQLUnitTest:
  stage: test
  rules:
    - *stability_pipeline_rule
    - *merge_request_rule
  needs: [ ]
  variables:
    SQL_PATH: "database/SQL"
    CODECOV_TOKEN: $CODECOV_TOKEN
  trigger:
    include:
      - project: agoda-e2e/sql-tests-service
        file: /ci-templates/sql_unit_test.yml
    strategy: depend

stored_procedure_tests:
  extends: [ .sbt_docker, .pull_cache ]
  rules:
    - *stability_pipeline_rule
    - *merge_request_rule
  stage: test
  needs: [ ]
  tags:
    - m1.xlarge
  script:
    - |
      docker-compose -f ./sp-tests/sp-test-docker-compose.yml up -d
      docker ps
      sed -i 's/localhost/docker/g' sp-tests/src/sptest/resources/application.conf
      sed -i 's/localhost/docker/g' sp-tests/src/sptest/resources/pinto-write.conf
      sbt clean spTests/"compile;reload;sptest:test"
  artifacts:
    reports:
      junit: ./sp-tests/target/test-reports/TEST-*.xml
    expire_in: 1 day

unit_tests:
  extends: [ .sbt-docker, .sbt_test_with_code_coverage, .pull_cache ]
  rules:
    - *merge_request_rule
  stage: test
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
    CODECOV_FLAG: "unit-tests"
  needs: [ ]
  tags:
    - m1.xlarge
  script:
    - sbt onlyUnitTests
    - bash <(curl -s $CODECOV_BASH) -F $CODECOV_FLAG -t $CODECOV_TOKEN
  retry:
    max: 1

unit_test_stability:
  extends: [ .sbt-docker, .sbt_test_with_code_coverage, .pull_cache ]
  rules:
    - *stability_pipeline_rule
  stage: test
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
  needs: [ ]
  script:
    - sbt onlyUnitTests
    - bash <(curl -s $CODECOV_BASH) -F $CODECOV_FLAG -t $CODECOV_TOKEN
  tags:
    - m1.xlarge

functional_tests:
  extends: [ .sbt-docker, .sbt_test_with_code_coverage, .pull_cache ]
  rules:
    - *merge_request_rule
  stage: test
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
    CODECOV_FLAG: "unit-tests"
  needs: [ ]
  tags:
    - m1.xlarge
  script:
    - sbt onlyFunctionalTests
    - bash <(curl -s $CODECOV_BASH) -F $CODECOV_FLAG -t $CODECOV_TOKEN
  retry:
    max: 1

functional_test_stability:
  extends: [ .sbt-docker, .sbt_test_with_code_coverage, .pull_cache ]
  rules:
    - *stability_pipeline_rule
  stage: test
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
  needs: [ ]
  script:
    - sbt onlyFunctionalTests
    - bash <(curl -s $CODECOV_BASH) -F $CODECOV_FLAG -t $CODECOV_TOKEN
  tags:
    - m1.xlarge

unit_integration_tests:
  extends: [ .sbt_test_with_code_coverage, .pull_cache ]
  rules:
    - *stability_pipeline_rule
    - *merge_request_rule
  stage: test
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
  needs: [ ]
  script:
    - sbt integrationTest/UnitIntegrationTest/test
  tags:
    - m1.xlarge
  retry:
    max: 1

log_analyzer_test:
  resource_group: log_analyzer_test
  stage: test
  variables:
    HARBOR_IMAGE: $HARBOR_IMAGE
    HARBOR_TAG: $HARBOR_TAG
    MAJORITY_PROD_VERSION: $PAPI_MAJORITY_PROD_VERSION
  rules:
    - if: $CI_COMMIT_REF_NAME == $SKIP_LOG_ANALYSIS_BRANCH
      when: never
    - <<: *merge_train_rule
      <<: *changes_for_log_analyzer
    - *create_package_rule
  trigger:
    include:
      - local: "/ci/templates/log_analyzer_test.yml"
    strategy: depend

acm_test_pvc:
  resource_group: acm_test
  stage: test
  variables:
    HARBOR_IMAGE: $HARBOR_IMAGE
    HARBOR_TAG: $HARBOR_TAG
    MAJORITY_PROD_VERSION: $PAPI_MAJORITY_PROD_VERSION
  rules:
    - *create_package_rule
  trigger:
    include:
      - local: "/ci/templates/acm_pvc.gitlab-ci.yml"
    strategy: depend

acm_test_on_merge_pvc:
  resource_group: acm_test
  rules:
    - <<: *merge_request_rule
      when: manual
      allow_failure: true
  stage: test
  variables:
    DB_SQL_PACKAGE_NAME: $DB_SQL_PACKAGE_NAME
    HARBOR_IMAGE: $HARBOR_IMAGE
    HARBOR_TAG: $HARBOR_TAG
    HARBOR_SUB_PATH: $HARBOR_SUB_PATH
    HARBOR_PROJECT_PATH: $HARBOR_PROJECT_PATH
    HAS_CACHE_POP_CHANGED: $HAS_CACHE_POP_CHANGED
    HAS_DB_CHANGED: $HAS_DB_CHANGED
    CACHEPOPULATOR_HARBOR_PROJECT_PATH: $CACHEPOPULATOR_HARBOR_PROJECT_PATH
    CACHE_POPULATOR_HARBOR_TAG: $CACHE_POPULATOR_HARBOR_TAG
    MAJORITY_PROD_VERSION: $PAPI_MAJORITY_PROD_VERSION
  trigger:
    include:
      - local: "/ci/templates/acm_mr_pvc.gitlab-ci.yml"
    strategy: depend

# when we've cache pop change. the first run might fail. we need to rerun it
regression_test_pvc:
  rules:
    - *releases_branch_rule
    - *renovate_bot_tag
  stage: test
  resource_group: regression_test
  variables:
    ENV_NAME: "papisearchregression"
    ENVIRONMENT: "papisearchregression"
    CANDIDATE_PACKAGE_ENDPOINT: "propertyapisearchdocker-papisearchregression.privatecloud.hk.agoda.is"
    CANDIDATE_PACKAGE_NAME: $HARBOR_TAG
    HARBOR_IMAGE: $HARBOR_IMAGE
  trigger:
    include:
      - local: "/ci/templates/regression-test.gitlab-ci.yml"
    strategy: depend

publish_openapi_snapshot:
  stage: publish
  needs:
    - job: prepare_variables
      artifacts: true
  rules:
    - <<: *merge_request_rule
      when: manual
      allow_failure: true
  variables:
    PARENT_PIPELINE_ID: $CI_PIPELINE_ID
    SBT_COMMAND: sbt openapispecGenerator/run
    OPENAPISPEC_FILEPATH: openapispec-generator/openapi_spec.yml
    GROUP_ID: "propertyapisearchdocker"
    ARTIFACT_ID: "endpoint-v2-snapshot"
    VERSION: $BASE_VERSION-$CI_PIPELINE_ID-snapshot
  trigger:
    include:
      - local: '/ci/templates/publish_openapi_spec.yml'
    strategy: depend

publish_openapi:
  stage: publish
  needs:
    - job: prepare_variables
      artifacts: true
  rules:
    - *releases_branch_rule
  variables:
    PARENT_PIPELINE_ID: $CI_PIPELINE_ID
    SBT_COMMAND: sbt openapispecGenerator/run
    OPENAPISPEC_FILEPATH: openapispec-generator/openapi_spec.yml
    GROUP_ID: "propertyapisearchdocker"
    ARTIFACT_ID: "endpoint-v2"
    VERSION: $BASE_VERSION
  trigger:
    include:
      - local: '/ci/templates/publish_openapi_spec.yml'
    strategy: depend

get_bump_version:
  extends: [ .bump_version ]
  rules:
    - *create_package_rule
  variables:
    VERSION_FILE: "version.sbt"
  stage: bump

commit_version:
  extends: [ .git_push ]
  stage: commit
  rules:
    - *create_package_rule
  needs:
    - job: get_bump_version
      artifacts: true
  variables:
    FILES_TO_COMMIT: "version.sbt papi-docker-compose/docker-compose.yml"
    COMMIT_MESSAGE: "Change version to $VERSION"
  before_script:
    # We can't use BASE_VERSION from our '.pre' job because it gets overridden by dotenv of '.bump_version' template
    - PAPI_VERSION=$(egrep -o '".*?"' version.sbt | tr -d '"')
    - echo $PAPI_VERSION
    - sed -i "s/propertyapi\/release:.*/propertyapi\/release:$PAPI_VERSION/g" papi-docker-compose/docker-compose.yml
    - cat papi-docker-compose/docker-compose.yml
    - COMMIT_MESSAGE="Change version to $PAPI_VERSION"
    - !reference [ .git_push, before_script ]

push_to_releases:
  extends: [ .git_push ]
  stage: push
  rules:
    - *create_package_rule
  needs:
    - job: commit_version
  variables:
    GIT_PULL_BRANCH: "develop"
    GIT_PUSH_BRANCH: "releases"
    GIT_PULL_OPTION: "--ff --allow-unrelated-histories"
  script:
    - git push -u origin ${GIT_PUSH_BRANCH}

notify_release:
  stage: notify_release
  rules:
    - *releases_branch_rule
  variables:
    RELEASE_VERSION: $VERSION
    PARENT_PIPELINE_URL: $CI_PIPELINE_URL
  trigger:
    include:
      - local: "/ci/templates/notify-release.gitlab-ci.yml"
    strategy: depend

notify_stale:
  stage: notify_stale
  rules:
    - *stale_mr_notifier_rule
  variables:
    PROJECT_ID: $CI_PROJECT_ID
    GITLAB_PRIVATE_TOKEN: $GITLAB_PRIVATE_TOKEN_STALE_PR
    SLACK_CHANNELS: $SLACK_CHANNELS
    SLACK_HOOK_URL: $SLACK_HOOK_URL
    SLACK_USER: $SLACK_USER
  trigger:
    include:
      - local: "/ci/templates/mr-stale.gitlab-ci.yml"
    strategy: depend

# deploy cache populator after merging to releases branch
deploy_cache_populator_release_to_prelive:
  extends: [ .pc_deploy ]
  environment:
    name: cache-populator/prelive
    deployment_tier: staging
  rules:
    - <<: *releases_branch_rule
      <<: *changes_for_cache_populator
  stage: deploy
  variables:
    PRELIVE_ENV: "prelive"
    PC_IMAGE_TAG: "$CACHE_POPULATOR_HARBOR_TAG"
    PC_IMAGE_REPO: "$CACHEPOPULATOR_HARBOR_PROJECT_PATH/$HARBOR_SUB_PATH"
    PC_YAML_TEMPLATE: ./privatecloud-deployment/cache/prelive-papicachepopulator.yaml

deploy_cache_populator_standby_to_prod:
  extends: [ .pc_deploy ]
  environment:
    name: cache-populator/prod-standby
    deployment_tier: production
  rules:
    - <<: *releases_branch_rule
      <<: *changes_for_cache_populator
  stage: deploy
  before_script:
    - export PC_IMAGE_TAG=$(curl http://propertyapisearchdocker-fe.hk.agoda.is/cache/version)
  variables:
    PROD_ENV: "prod-standby"
    PC_IMAGE_REPO: "papi/search/cache-populator/standby/release"
    PC_YAML_TEMPLATE: ./privatecloud-deployment/cache/prod-papicachepopulator.yaml

deploy_cache_populator_release_to_prod:
  extends: [ .pc_deploy ]
  allow_failure: true
  timeout: 5h
  environment:
    name: cache-populator/prod
    deployment_tier: production
  rules:
    - <<: *releases_branch_rule
      <<: *changes_for_cache_populator
  stage: deploy
  variables:
    PROD_ENV: "prod"
    PC_IMAGE_TAG: "$CACHE_POPULATOR_HARBOR_TAG"
    PC_IMAGE_REPO: "papi/search/cache-populator/release"
    PC_YAML_TEMPLATE: ./privatecloud-deployment/cache/prod-papicachepopulator.yaml
  script:
    # script from .pc_deploy
    - !reference [ .pc_deploy, script ]
    # wait script to check cache population status
    - |
      echo "Waiting for cache population to complete for all DCs..."
      INGRESS_URLS=(
        "papicachepopulator-prod.privatecloud.am.agoda.is"
        "papicachepopulator-prod.privatecloud.as.agoda.is"
        "papicachepopulator-prod.privatecloud.hk.agoda.is"
        "papicachepopulator-prod.privatecloud.sg.agoda.is"
      )
      MAX_RETRIES=19
      RETRY_INTERVAL=900

      for ((i=1; i<=MAX_RETRIES; i++)); do
        ALL_POPULATED=true
        for URL in "${INGRESS_URLS[@]}"; do
          echo "Checking cache population status for $URL..."
          STATUS=$(curl -s https://$URL/populated --noproxy ".is")
          if [[ "$STATUS" != "true" ]]; then
            echo "Cache not populated for $URL. Status: $STATUS"
            ALL_POPULATED=false
          else
            echo "Cache populated for $URL."
          fi
        done

        if [[ "$ALL_POPULATED" == "true" ]]; then
          echo "Cache population completed for all DCs"
          break
        fi

        if [[ $i -lt MAX_RETRIES ]]; then
          echo "Retrying in $RETRY_INTERVAL seconds... (Attempt $i of $MAX_RETRIES)"
          sleep $RETRY_INTERVAL
        else
          echo "Cache population did not complete within the maximum wait time of 5 hours. kindly visit: https://grafana.agodadev.io/d/qacNi5mIk/papi-cache-populator to check the status"
          exit 1
        fi
      done

# papi-sign is being made optional due to pipeline validity error on pipelines where papi-sign is not present
# as papi-sign is being included dynamically through ci/cd component. there is no side effect for this change since
# both papi-sign and deploy_papi:prod have release branch rule. and if papi-sign job failed, deploy_papi:prod will be skipped
deploy_papi:prod:
  extends: [ .pc_deploy ]
  needs:
    - job: prepare_variables
      artifacts: true
    - job: papi-sign
      optional: true
    - job: deploy_cache_populator_release_to_prod
      optional: true
  timeout: 5h
  environment:
    name: prod
    deployment_tier: production
  rules:
    - <<: *releases_branch_rule
  when: manual
  stage: deploy
  variables:
    PC_IMAGE_TAG: "$HARBOR_TAG"
    PC_YAML_TEMPLATE: ./privatecloud-deployment/prod/prod.yaml
    DEPLOY_MONITOR_SECONDS: $PROD_DEPLOY_MONITOR_SECONDS

deploy_papi:regression:
  extends: [ .pc_deploy ]
  environment:
    name: non-prod/regression
    deployment_tier: testing
  rules:
    - <<: *releases_branch_rule
  when: manual
  stage: deploy
  variables:
    PC_IMAGE_TAG: "$HARBOR_TAG"
    PC_YAML_TEMPLATE: ./privatecloud-deployment/non-prod/regression.yaml
    DEPLOY_MONITOR_SECONDS: 200

deploy_papi:regression-stability:
  extends: [ .pc_deploy ]
  environment:
    name: non-prod/papisearchregression-stability
    deployment_tier: testing
  rules:
    - <<: *merge_request_rule
    - <<: *releases_branch_rule
  when: manual
  stage: deploy
  variables:
    ENV_NAME: "papisearchregression-stability"
    ENVIRONMENT: papisearchregression
    PC_IMAGE_TAG: $HARBOR_TAG
    PC_IMAGE_REPO: $HARBOR_IMAGE
    PC_YAML_TEMPLATE: './privatecloud-deployment/regression/papisearchregression.yaml'

deploy_papi:prelive:
  extends: [ .pc_deploy ]
  environment:
    name: non-prod/$PRELIVE_ENV
    deployment_tier: testing
  rules:
    - <<: *merge_request_rule
    - <<: *releases_branch_rule
  when: manual
  stage: deploy
  parallel:
    matrix:
      - PRELIVE_ENV:
        - "prelive-contributor"
        - "prelive-maintainer"
        - "prelive-bfos"
  variables:
    ENV_NAME: "$PRELIVE_ENV"
    ENVIRONMENT: prelive
    PC_IMAGE_TAG: $HARBOR_TAG
    PC_IMAGE_REPO: $HARBOR_IMAGE
    PC_YAML_TEMPLATE: './privatecloud-deployment/regression/papisearchregression.yaml'

deploy_papi:non-prod:
  extends: [ .pc_deploy ]
  needs:
    - job: prepare_variables
      artifacts: true
    - job: papi-build
      optional: true
  when: manual
  allow_failure: true
  stage: deploy
  variables:
    PC_IMAGE_TAG: $HARBOR_TAG
    PC_IMAGE_REPO: $HARBOR_IMAGE
    ENVIRONMENT: "acm"
    RESOURCE_POOL: "poc"
    IS_EPHEMERAL: "false"
    PC_YAML_TEMPLATE: './privatecloud-deployment/non-prod/acm_env.yaml'
  parallel:
    matrix:
      - ACM_ENV:
          - "acm-maintainer-baseline-1"
          - "acm-maintainer-candidate-1"
          - "acm-main-base-2"
          - "acm-main-cand-2"
          - "acm-main-base-3"
          - "acm-main-cand-3"
          - "prelive-contributor"
  rules:
    - <<: *merge_train_rule
      when: never
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $ACM_ENV == "prelive-contributor"'
      variables:
        RESOURCE_POOL: "mesh"
        ENVIRONMENT: "prelive"
    - if: '$CI_PIPELINE_SOURCE == "merge_request_event" && $ACM_ENV != "prelive-contributor"'
    - if: '$CI_COMMIT_BRANCH == "develop" && $CI_PIPELINE_SOURCE == "push" && $ACM_ENV != "prelive-contributor"'
    - if: '$CI_COMMIT_BRANCH == "releases" && $ACM_ENV != "prelive-contributor"'
  environment:
    name: non-prod/$ACM_ENV
    deployment_tier: staging

deploy_papi:qa:
  extends: [ .pc_deploy ]
  needs:
    - job: prepare_variables
      artifacts: true
    - job: papi-build
      optional: true
  allow_failure: true
  stage: deploy
  variables:
    PC_IMAGE_TAG: $HARBOR_TAG
    PC_IMAGE_REPO: $HARBOR_IMAGE
    ENVIRONMENT: "qa"
    RESOURCE_POOL: "qa"
    IS_EPHEMERAL: "false"
    PC_YAML_TEMPLATE: './privatecloud-deployment/non-prod/qa.yaml'
  environment:
    name: non-prod/qa
    deployment_tier: testing
  rules:
    - *releases_branch_rule

publish_libraries:
  extends: [ .sbt_publish, .pull_cache ]
  rules:
    - *releases_branch_rule
  stage: publish

publish_client_snapshot:
  extends: [ .sbt_publish, .pull_cache ]
  stage: publish
  rules:
    - <<: *merge_request_rule
      when: manual
      allow_failure: true
  before_script:
    - |
      BASE_VERSION=`egrep -E -o "[0-9]*\.[0-9]*\.[0-9]*[-._a-zA-Z0-9]*" version.sbt`
      MR_VERSION=$BASE_VERSION+$CI_PIPELINE_ID-SNAPSHOT
      eval 'sed -i.back 's/$BASE_VERSION/$MR_VERSION/g' ./version.sbt'
      cat version.sbt

acm_schedule_every_dcs:
  stage: test
  variables:
    DCS: $DCS
    ACM_ENV: $ACM_ENV
    ACM_VAULT_ROLE_ID: $ACM_VAULT_ROLE_ID
    ACM_VAULT_SECRET_ID: $ACM_VAULT_SECRET_ID
    ACM_TRAFFIC_TYPE: $ACM_TRAFFIC_TYPE
    ACM_STRATEGY_OVERRIDE: $ACM_STRATEGY_OVERRIDE
    RESOURCE_POOL: $RESOURCE_POOL
  rules:
    - *acm_schedule_every_dcs
  trigger:
    include:
      - local: "/ci/templates/acm-schedule-every-dcs.gitlab-ci.yml"
    strategy: depend

build_and_publish_fake_api:
  extends: [ .sbt_docker, .pull_cache ]
  rules:
    - <<: *merge_request_rule
    - <<: *develop_branch_rule
    - <<: *releases_branch_rule
    - <<: *stability_pipeline_rule
  needs:
    - prepare_variables
  stage: build
  allow_failure: true
  before_script:
    - export DECODED_PASS=`echo "$FAKE_API_HARBOR_PASSWORD" | base64 -d`
    - echo "{\"auths\":{\"$HARBOR_URL\":{\"username\":\"$FAKE_API_HARBOR_USER\",\"password\":\"$DECODED_PASS\"}},\"proxies\":{\"default\":{\"httpProxy\":\"$http_proxy\",\"httpsProxy\":\"$https_proxy\",\"noProxy\":\"$no_proxy\"}}}" > /root/.docker/config.json
    - timeout 30s sh -c "until docker version; do echo "..."; sleep 1; done" || true
  script:
    - time sbt clean fakeApi/stage
    - FAKE_API_IMAGE_PATH="$HARBOR_REGISTRY/$FAKE_API_IMAGE_REPO:$FAKE_API_IMAGE_TAG"
    - echo $FAKE_API_IMAGE_PATH
    - docker build -f fake-api/Dockerfile -t "$FAKE_API_IMAGE_PATH" .
    - docker push $FAKE_API_IMAGE_PATH
  tags:
    - m1.xlarge
  artifacts:
    paths:
      # Another hack to prevent fake_api_test job to re-compile the whole project
      - $CI_PROJECT_DIR/**/target
      - $CI_PROJECT_DIR/target

fake_api_publish_devstack:
  stage: build
  extends:
    - .devstack_publish
  rules:
    - <<: *merge_request_rule
      when: manual
    - <<: *develop_branch_rule
    - <<: *releases_branch_rule
  needs:
    - build_and_publish_fake_api
    - prepare_variables
  allow_failure: true
  variables:
    DEVSTACK_PUBLISH_VERSION: $FAKE_API_IMAGE_TAG
    DEVSTACK_ENV: fake-api
    DEVSTACK_CONFIG_FILE: $CI_PROJECT_DIR/fake-api/devstack.yaml
    DEVSTACK_OVERRIDE: |
      environments:
        fake-api:
          spec:
            artifact:
              image:
                repository: $FAKE_API_IMAGE_REPO
                tag: "$FAKE_API_IMAGE_TAG"

deploy_fake_api_to_devstack:
  stage: deploy
  rules:
    - <<: *merge_request_rule
    - <<: *releases_branch_rule
    - <<: *stability_pipeline_rule
  extends: .devstack_deploy
  needs:
    - build_and_publish_fake_api
    - prepare_variables
  variables:
    DEVSTACK_WAIT: "true"
    DEVSTACK_ENV: fake-api
    DEVSTACK_TTL: 35m
    DEVSTACK_WAIT_TIMEOUT: "1200"
    DEVSTACK_CONFIG_FILE: $CI_PROJECT_DIR/fake-api/devstack.yaml
    DEVSTACK_GRANTS_TEAMS: "team-it-propertyapi-search"
    DEVSTACK_OVERRIDE: |
      environments:
        fake-api:
          spec:
            artifact:
              image:
                repository: $FAKE_API_IMAGE_REPO
                tag: "$FAKE_API_IMAGE_TAG"

fake_api_test:
  stage: test
  needs:
    - deploy_fake_api_to_devstack
    - build_and_publish_fake_api
  rules:
    - <<: *merge_request_rule
    - <<: *releases_branch_rule
    - <<: *stability_pipeline_rule
  extends: [ .sbt_test_with_code_coverage, .pull_cache ]
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
    CODECOV_FLAG: "fake-api-tests"
  script:
    - PROPERTYAPI_HOST_PORT=$DEVSTACK_PRIMARY_ENDPOINT sbt "fakeApi/testOnly com.agoda.papi.search.fakeapitest.*"
    - bash <(curl -s $CODECOV_BASH) -F $CODECOV_FLAG -t $CODECOV_TOKEN

test_fake_api_contracts:
  stage: test
  needs:
    - deploy_fake_api_to_devstack
  rules:
    - <<: *merge_request_rule
    - <<: *releases_branch_rule
    - <<: *stability_pipeline_rule
  retry: 1
  extends:
    - .sbt_test_with_code_coverage
  variables:
    CODECOV_TOKEN: $CODECOV_TOKEN
    BROKER_URL: "http://pactbroker.agoda.local"
    CONTRACT_TAG: "production"
    DEVSTACK_URL: $DEVSTACK_PRIMARY_ENDPOINT
    VERIFICATION_TIMEOUT: 240
    SBT_VERSION: 17.0.4_1.8.0_2.12.17
  allow_failure: true
  script:
    - sbt "pactTest/pact:test"

publish_fake_api_openapi_snapshot:
  stage: publish
  needs:
    - job: prepare_variables
      artifacts: true
  rules:
    - <<: *merge_request_rule
      when: manual
      allow_failure: true
  variables:
    PARENT_PIPELINE_ID: $CI_PIPELINE_ID
    SBT_COMMAND: sbt fakeApi/'runMain com.agoda.papi.search.fakeapi.OpenAPISpecGenerator'
    OPENAPISPEC_FILEPATH: fake-api/fake_papi_openapi_spec.yml
    GROUP_ID: "propertyapisearchdocker"
    ARTIFACT_ID: "fake-api-snapshot"
    VERSION: $BASE_VERSION-$CI_PIPELINE_ID-snapshot
  trigger:
    include:
      - local: '/ci/templates/publish_openapi_spec.yml'
    strategy: depend

publish_fake_api_openapi:
  stage: publish
  needs:
    - job: prepare_variables
      artifacts: true
  rules:
    - *releases_branch_rule
  variables:
    PARENT_PIPELINE_ID: $CI_PIPELINE_ID
    SBT_COMMAND: sbt fakeApi/'runMain com.agoda.papi.search.fakeapi.OpenAPISpecGenerator'
    OPENAPISPEC_FILEPATH: fake-api/fake_papi_openapi_spec.yml
    GROUP_ID: "propertyapisearchdocker"
    ARTIFACT_ID: "fake-api"
    VERSION: $BASE_VERSION
  trigger:
    include:
      - local: '/ci/templates/publish_openapi_spec.yml'
    strategy: depend

mutation-test:
  extends: .mutation-test-scala-incremental-git
  image: images.agodadev.io/library/scala-sbt-azulzulu:8u282_1.5.1_2.12.13
  rules:
    - *merge_request_rule
  retry: 1
  stage: test
  needs: []
  parallel:
    matrix:
      - PROJECT_PATH: api
        SUB_PROJECT: api
      - PROJECT_PATH: property
        SUB_PROJECT: property
      - PROJECT_PATH: complexsearch
        SUB_PROJECT: complexsearch
      - PROJECT_PATH: cache-populator
        SUB_PROJECT: cachePopulator
      - PROJECT_PATH: cache-client
        SUB_PROJECT: cacheClient
      - PROJECT_PATH: internal-model
        SUB_PROJECT: internalModel
      - PROJECT_PATH: common
        SUB_PROJECT: common
  allow_failure: true
  tags:
    - m1.xxlarge

mutation-test-mr-comment:
  stage: test
  needs: [mutation-test]
  rules:
    - *merge_request_rule
  extends:
    - .mr-comment-mutation-score
  variables:
    UPDATE_COMMENT: modify
    MUTATION_REPORTS_PATH: "*/target/stryker4s-report/*"
  allow_failure: true
  script:
    - |
      MESSAGE="Reports for mutation tests:"$'\n'
      while read -r REPORT_PATH; do
        RELATIVE_PATH=$(echo "$REPORT_PATH" | sed "s|$CI_PROJECT_DIR/||")
        MESSAGE+="- $CI_PROJECT_URL/-/jobs/$CI_JOB_ID/artifacts/file/$RELATIVE_PATH"$'\n'
      done < <(find "$CI_PROJECT_DIR" -type f -path "$MUTATION_REPORTS_PATH/index.html")
    - echo "$MESSAGE"
    - !reference [.mr-comment-mutation-score, script]
