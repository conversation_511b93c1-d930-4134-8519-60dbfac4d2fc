package com.agoda.property.utils

import akka.http.scaladsl.model.HttpHeader
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.platform.service.context.ServiceHeaders

import scala.collection.immutable.Seq
import request.PropertyContext

import scala.util.Try

object GlobalContextHelper {
  import java.net.InetAddress
  private val addr: InetAddress = InetAddress.getLocalHost
  private val hostname: String  = addr.getHostName

  private val papiClientVersion: String = Package.getPackages
    .find(p => p.getImplementationVendor == "com.agoda.propertyapi" && p.getImplementationTitle == "client")
    .map(_.getImplementationVersion)
    .getOrElse("")

  private val hostnameHeader: HttpHeader          = RawHeader(ServiceHeaders.AgCallerHostname.toString, hostname)
  private val clientVersionHeader: HttpHeader     = RawHeader(ServiceHeaders.AgCallerVersion.toString, getAppVersion())
  private val clientAppNameHeader: HttpHeader     = RawHeader("AG-CALLER-APP-NAME", getAppName())
  private val papiClientVersionHeader: HttpHeader = RawHeader("PAPI-CLIENT-VERSION", papiClientVersion)
  val staticGlobalContextHeaders =
    Seq(hostnameHeader, clientVersionHeader, clientAppNameHeader, papiClientVersionHeader)

  def getGlobalContextHeader(context: PropertyContext, customHeaders: Seq[HttpHeader] = Seq.empty): Seq[HttpHeader] = {
    val headerList: Seq[(String, Option[String])] = if (context != null) {
      Seq(
        (ServiceHeaders.AgUserId.toString         -> Try(context.userId).toOption.flatten),
        (ServiceHeaders.AgMemberId.toString       -> Try(context.memberId.toString).toOption),
        (ServiceHeaders.AgLanguageLocale.toString -> Try(context.locale).toOption),
        (ServiceHeaders.AgPlatformId.toString     -> Try(context.platform.toString).toOption),
        ("AG-PAGE-TYPE-ID"                        -> Try(context.pageTypeId.map(_.toString)).toOption.flatten),
        (ServiceHeaders.AgOrigin.toString         -> Try(context.origin).toOption),
        (ServiceHeaders.AgDeviceTypeId.toString   -> Try(context.deviceTypeId.map(_.toString)).toOption.flatten),
        (ServiceHeaders.AgCid.toString            -> Try(context.cid.toString).toOption),
        (ServiceHeaders.AgWhiteLabelKey.toString  -> Try(context.whiteLabelKey).toOption.flatten),
        (ServiceHeaders.AgCorrelationId.toString  -> Try(context.searchId).toOption),
        (ServiceHeaders.AgInitiatorIP.toString    -> Try(context.ipAddress).toOption.flatten),
        (ServiceHeaders.AgPartnerClaim.toString   -> Try(context.partnerClaimToken).toOption.flatten)
      )
    } else {
      Seq.empty
    }
    headerList.filter(_._2.isDefined).map(h => RawHeader(h._1, h._2.getOrElse(""))) ++
      staticGlobalContextHeaders ++
      customHeaders.filter(h => ServiceHeaders.isKnownHeader(h.name()))
  }

  private def getAppName(): String = {
    val systemAppName = System.getProperty("appname")
    if (systemAppName != null && systemAppName != "") {
      systemAppName
    } else {
      BuildHelper.appName
    }
  }

  private def getAppVersion(): String =
    BuildHelper.version
}
