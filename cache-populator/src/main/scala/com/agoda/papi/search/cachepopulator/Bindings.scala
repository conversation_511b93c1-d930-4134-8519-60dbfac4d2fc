package com.agoda.papi.search.cachepopulator

import cats.effect.{ ContextShift, IO }
import cats.Parallel
import cats.Parallel.Aux
import cats.implicits._
import com.agoda.commons.cache.multicache.MultiCache
import com.agoda.commons.logging.metrics.{ AdpMetricsReporter, MetricsReporter }
import com.agoda.commons.observability.sdk.AgOpenTelemetry
import com.agoda.commons.sql.{ Sql, SqlConnection }
import com.agoda.commons.sql.hikari.HikariDataSourcePool
import com.agoda.commons.sql.hikari.load.balancing.{ DataSourcePoolLoadBalancer, HikariDataSourceResource }
import com.agoda.commons.sql.settings.MetricsSettings.ReportSettings
import com.agoda.commons.sql.settings.SqlSettingsBuilder
import com.agoda.papi.search.cacheclient._
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.cacheclient.repository.{
  BigCityRepositorySource,
  BigCityRepositorySourceImpl,
  CityIdsFetcherRepositoryAgsql,
  CityIdsFetcherRepositoryAgsqlImpl,
  PropertyInfoRepositoryAgsql,
  PropertyInfoRepositoryAgsqlImpl
}
import com.agoda.papi.search.cacheclient.transformer.PropertyInfoListTransformer
import com.agoda.papi.search.cachepopulator.ExecutionContexts.Implicits.defaultContext
import com.agoda.papi.search.cachepopulator.flow.{ CachePopulationFlow, PropertyFetcherFlow }
import com.agoda.papi.search.cachepopulator.settings.{ CachePopulatorSettings, VaultAuthenticationSettings }
import com.typesafe.config.ConfigFactory
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.opentracingshim.OpenTracingShim
import scalacache.CacheConfig
import scalacache.modes.scalaFuture.mode
import scaldi.Module

import scala.concurrent.Future

object Bindings {

  val config = ConfigFactory.load()

  private val vaultCredentialsOpt =
    if (config.getBoolean("ag-vault.enable")) Some(VaultAuthenticationSettings(config)) else None

  class CommonModule extends Module {
    private val agOpenTelemetry = AgOpenTelemetry.of(config).registerGlobal()
    bind[OpenTelemetry].toNonLazy(agOpenTelemetry)
    bind[io.opentelemetry.api.trace.Tracer] to inject[OpenTelemetry].getTracer("papicachepopulator")

    bind[io.opentracing.Tracer].to {
      val otel = inject[OpenTelemetry]
      OpenTracingShim.createTracerShim(otel)
    }

    bind[CachePopulatorSettings] to CachePopulatorSettings(config)
    bind[MetricsReporter] to AdpMetricsReporter
  }

// cdb deps

  class CDBModule extends Module {

    implicit private val dbIOContextShift: ContextShift[IO] = IO.contextShift(ExecutionContexts.ioContext)

    bind[HikariDataSourcePool] to HikariDataSourcePool
      .builder("cdb", config)
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    bind[DataSourcePoolLoadBalancer[HikariDataSourceResource]] to new DataSourcePoolLoadBalancer[
      HikariDataSourceResource
    ](
      inject[HikariDataSourcePool].getPoolName,
      inject[HikariDataSourcePool].getDatasourcesState,
      DataSourcePoolLoadBalancer.DEFAULT_WEIGHT_STRATEGY
    )

    bind[IO[Sql[IO, SqlConnection[IO]]]].to {

      val sqlSettings = SqlSettingsBuilder("cdb")
        .withConfig(config)
        .withOpenTelemetry(inject[OpenTelemetry])
        .withDataSourcePool(inject[HikariDataSourcePool])
        .withReportSettings(
          ReportSettings(
            totalExecutionTime = true,
            perCallExecutionTime = true,
            perCallProcessingTime = false
          )
        )
        .build()

      Sql[IO](sqlSettings)
    }

    bind[BigCityRepositorySource] to new BigCityRepositorySourceImpl(inject[IO[Sql[IO, SqlConnection[IO]]]])

    bind[PropertyInfoRepositoryAgsql] to new PropertyInfoRepositoryAgsqlImpl(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[BigCityRepositorySource]
    )

    bind[CityIdsFetcherRepositoryAgsql] to new CityIdsFetcherRepositoryAgsqlImpl(inject[IO[Sql[IO, SqlConnection[IO]]]])
    bind[PropertyInfoListTransformer] to new PropertyInfoListTransformer

    bind[CDBService] to new CDBServiceImpl(
      inject[CityIdsFetcherRepositoryAgsql],
      inject[PropertyInfoRepositoryAgsql],
      inject[PropertyInfoListTransformer]
    )

  }

  class CouchbaseModule extends Module {

    // couchbase deps
    val propertyInfoCacheKeyBuilder    = new PropertyInfoCacheKeyBuilder
    val cacheCompressor                = new LZ4HCacheCompressor
    implicit val propertyInfoListCodec = new Protobuf3WithCompressionPropertyInfoListCodec(cacheCompressor)

    val agCacheProvider =
      new AgCacheProvider(
        AdpMetricsReporter,
        config,
        vaultCredentialsOpt.map(_.getCouchbaseAuthenticationSettings),
        vaultCredentialsOpt.flatMap(_.getCouchbaseCBDATAAuthenticationSettigs)
      )

    implicit val parallelFuture: Aux[Future, Future] = Parallel.identity[Future]

    val multiCache: MultiCache[Future, PropertyInfoList] = agCacheProvider.buildMultiCaches(
      sys.env.getOrElse("AG_SERVICE_NAME", "papicachepopulator"),
      CacheConfig.defaultCacheConfig
    )

    bind[CacheWriter[PropertyInfoList]] to new CacheWriterImpl(
      propertyInfoCacheKeyBuilder,
      multiCache,
      inject[CachePopulatorSettings]
    )

    bind[CacheCompletionKeyReader] to new CacheCompletionKeyReader(
      propertyInfoCacheKeyBuilder,
      multiCache
    )

  }

  class FlowModule extends Module {

    bind[PropertyFetcherFlow] to new PropertyFetcherFlow(
      inject[CDBService],
      inject[CachePopulatorSettings]
    )(ExecutionContexts.defaultContext)

    bind[CachePopulationFlow] to new CachePopulationFlow(
      inject[CacheWriter[PropertyInfoList]],
      inject[CachePopulatorSettings]
    )(ExecutionContexts.defaultContext)

  }

}
