package com.agoda.papi.search.cachepopulator

import scala.concurrent.{ ExecutionContext, Future }
import scala.concurrent.duration.FiniteDuration
import akka.stream.scaladsl.{ Keep, Sink, Source }
import akka.stream.{ KillSwitches, Materializer, UniqueKillSwitch }
import akka.Done
import com.agoda.commons.concurrency.v1.scheduling.Scheduler
import com.agoda.commons.concurrency.v1.scheduling.Scheduler.Cancellable
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.cacheclient.{ CDBService, CityIdType }
import com.agoda.papi.search.cachepopulator.dto.{ CityWithCachePopulationFailed, CityWithCachePopulationPassed }
import com.agoda.papi.search.cachepopulator.flow.{ CachePopulationFlow, PropertyFetcherFlow }
import com.agoda.papi.search.cachepopulator.settings.CachePopulatorSettings
import com.typesafe.scalalogging.LazyLogging

import java.util.concurrent.atomic.{ <PERSON>Integer, AtomicReference }
import scala.util.{ Failure, Success, Try }

trait CachePopulator {

  def runWithSchedule(initDelay: FiniteDuration, interval: FiniteDuration)(implicit
      scheduler: Scheduler,
      ec: ExecutionContext
  ): Cancellable

  def run(): Future[Unit]

  def shutdown(): Unit
}

class PropertyInfoCachePopulator(
    cdbService: CDBService,
    cacheWriter: CacheWriter[PropertyInfoList],
    reportingService: MetricsReporter,
    settings: CachePopulatorSettings,
    propertyFetcherFlow: PropertyFetcherFlow,
    cachePopulationFlow: CachePopulationFlow
)(implicit ec: ExecutionContext, materializer: Materializer)
    extends CachePopulator with LazyLogging {

  val produced: AtomicInteger = new AtomicInteger(0)
  val consumed: AtomicInteger = new AtomicInteger(0)

  private val killSwitch: AtomicReference[UniqueKillSwitch] =
    new AtomicReference[UniqueKillSwitch]()

  override def runWithSchedule(initDelay: FiniteDuration, interval: FiniteDuration)(implicit
      scheduler: Scheduler,
      ec: ExecutionContext
  ): Cancellable = {
    scheduler.schedule(settings.lagUpdateSchedulerSettings.initDelay, settings.lagUpdateSchedulerSettings.interval)(
      updateLag()
    )
    scheduler.schedule(initDelay, interval)(run())
  }

  private def updateLag(): Unit =
    reportingService.report(
      CachePopulatorMeasurement.MetricsName.ConsumerRemaining,
      produced.longValue() - consumed.longValue(),
      Map.empty
    )

  override def run(): Future[Unit] = {
    val graph = Source
      .future(getCityIds)
      .mapConcat(identity)
      .viaMat(KillSwitches.single)(Keep.right)
      .viaMat(propertyFetcherFlow.getPropertyFetcherFlowWithRestartSettings)(Keep.left)
      .viaMat(cachePopulationFlow.getCachePopulationFlowWithRestartSettings)(Keep.left)
      .toMat(sink)(Keep.both)
      .mapMaterializedValue { tuple =>
        killSwitch.set(tuple._1)
        tuple._2
      }

    graph.run().transformWith {
      case Success(_) =>
        if ((produced.get() <= 0) || (produced.get() > 0 && produced.get() > consumed.get())) {
          logger.error(s"Cache population failed to complete. Produced: ${produced.get()}, Consumed: ${consumed.get()}")
          reportingService.report(
            CachePopulatorMeasurement.MetricsName.CachePopulationCompleted,
            -1,
            Map.empty
          )
          Future.failed[Unit](
            new RuntimeException(
              "Cache population failed to complete. Number of produced city-ids is still higher than processed city-ids " +
                "even if the flow is complete. produced: ${produced.get()}, consumed: ${consumed.get()}"
            )
          )
        } else {
          cacheWriter.writeIsReady().transform {
            case Success(_) =>
              reportingService.report(
                CachePopulatorMeasurement.MetricsName.CachePopulationCompleted,
                1,
                Map.empty
              )
              logger.info("Cache population completed successfully")
              Success(())
            case Failure(ex) =>
              logger.error(s"Cache population failed to write is-ready key", ex)
              reportingService.report(
                CachePopulatorMeasurement.MetricsName.CachePopulationCompleted,
                -1,
                Map.empty
              )
              Failure[Unit](ex)
          }
        }
      case Failure(exception) =>
        logger.error("Cache population failed to complete with: ", exception)
        Future.failed[Unit](exception)
    }
  }

  private def getCityIds: Future[List[CityIdType]] = {
    val cityIdsF = retry(settings.retrySettings.maxRetries, () => cdbService.getCityIds)
    cityIdsF.onComplete { case Success(cityIds) =>
      produced.set(cityIds.size)
      consumed.set(0)
      reportingService.report(
        CachePopulatorMeasurement.MetricsName.ProducerProcess,
        produced.longValue(),
        Map(CachePopulatorMeasurement.Tags.Success -> "true")
      )
    }
    cityIdsF
  }

  def retry[T](n: Int, expr: () => Future[T])(implicit ec: ExecutionContext): Future[T] =
    expr().recoverWith {
      case e if n > 0 =>
        logger.warn("Failed with " + e.getMessage + s" Retry left: $n. Retrying...")
        retry(n - 1, expr)
      case e =>
        logger.error("Failed eventually with: ", e)
        Future.failed(e)
    }

  private def sink: Sink[Either[CityWithCachePopulationFailed, CityWithCachePopulationPassed], Future[Done]] =
    Sink.foreach {
      case Left(CityWithCachePopulationFailed(cityId, exception)) =>
        logger.error(s"Cache population failed for cityId: $cityId", exception)
        reportingService.report(
          CachePopulatorMeasurement.MetricsName.ConsumerProcess,
          1L,
          Map(
            CachePopulatorMeasurement.Tags.Success -> "false"
          )
        )
      case Right(CityWithCachePopulationPassed(cityId, _)) =>
        consumed.incrementAndGet()
        logger.info(s"Produced: ${produced.get()}, Consumed: ${consumed.get()}")
        reportingService.report(
          CachePopulatorMeasurement.MetricsName.ConsumerProcess,
          1L,
          Map(
            CachePopulatorMeasurement.Tags.Success -> "true"
          )
        )
    }

  override def shutdown(): Unit = {
    logger.info("Shutting down cache population")
    killSwitch.get().shutdown()
  }

}
