package com.agoda.papi.search.api.http.graphql.schema

import com.agoda.common.graphql.resolver.RemoteSupportedResolverBasedAstSchemaBuilder
import com.agoda.papi.pricing.graphql.PricingRequestValidators._
import com.agoda.content.models.request.sf.TopicsRequest

import scala.concurrent.ExecutionContext
import io.circe.{ <PERSON><PERSON><PERSON>, <PERSON><PERSON> }
import sangria.macros.derive._
import sangria.marshalling.circe._
import sangria.schema._
import com.agoda.common.graphql.schema.ScalarSchema._
import com.agoda.content.models.request.sf.{ ContentExtraRoomSummaryRequest, ContentSummaryRequest }
import com.agoda.papi.search.internalmodel.request._
import com.agoda.papi.search.internalmodel.response.{ PropertySponsoredDetail => PropertySponsoredDetailResponse, _ }
import com.agoda.papi.search.complexsearch.model._
import com.agoda.content.models.circe.Serialization._
import com.agoda.content.models.db.roomsummary.RoomSummaryResponse
import com.agoda.content.models.propertycontent.ContentPropertyFacility
import com.agoda.core.search.models.request.{ BaseRequest, ComplexSearchRequest, PollingInfoRequest }
import com.agoda.core.search.models.response.PollingInfoResponse
import com.agoda.core.search.validation._
import com.agoda.papi.content.graphql.RequestSchema._
import com.agoda.papi.pricing.graphql.RequestSchema._
import models.starfruit.{ PricingPropertiesRequest, PricingRequestParameters }
import com.agoda.pricing.models.circe.Encoders._
import PropertyRequestSchema._
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.complexsearch.graphql.validator.{
  ComplexSearchValidator,
  PriceTrendSearchValidator,
  PropertyAllotmentSearchValidator,
  PropertyDetailsValidator
}
import com.agoda.papi.search.internalmodel.{
  AggregationRequestDeferred,
  BenefitInformationDeferred,
  BookingHistoryDeferred,
  ComplexSearchDeferred,
  ContentDetail,
  ContentDetailDeferred,
  ContentEngagementDeferred,
  ContentExperienceDeferred,
  ContentFeaturesDeferred,
  ContentHighlightsDeferred,
  ContentImagesDeferred,
  ContentInformationDeferred,
  ContentInformationSummaryDeferred,
  ContentLocalInformationDeferred,
  ContentQnaDeferred,
  ContentRateCategoryDeferred,
  ContentReviewScoreDeferred,
  ContentReviewSummariesDeferred,
  ContentTopicsDeferred,
  CreditCardCampaignInfoDeferred,
  CreditCardCampaignInfosDeferred,
  EnrichmentDeferred,
  ExtraAgodaHomesRequestDeferred,
  FeaturedAgodaHomesRequestDeferred,
  FeaturedLuxuryHotelsRequestDeferred,
  FeaturedPulsePropertiesRequestDeferred,
  GeoPlacesDeferred,
  GrowthProgramInfoDeferred,
  HighlyRatedAgodaHomesRequestDeferred,
  HostProfileDeferred,
  HotelRoomInformationDeferred,
  IsConnectedTripApplicableDeferred,
  IsPopularDeferred,
  PointMaxInformationDeferred,
  PollingInfoDeferred,
  PriceBreakerSummaryEnvelope,
  PriceStreamFetchMetaLabResponseDeferred,
  PriceStreamFetchPropertyMetaInfoResponseDeferred,
  PriceTrendSearchDetailsDeferred,
  PropertyAllotmentSearchDetailsDeferred,
  PropertyBannerCampaignInfoDeferred,
  PropertyDetail,
  PropertyDetailsDeferred,
  PropertyRequestDeferred,
  PropertySearchRequestWrapper,
  PropertySponsoredDetail,
  PropertySummaryEnrichment,
  PropertySummaryEnvelope,
  PropertySummaryModel,
  PropertySummarySearchDeferred,
  RoomSummaryDeferred,
  SearchBannerCampaignInfoDeferred,
  SearchEnrichmentDeferred,
  SearchEnrichmentResponse,
  SearchResultDeferred,
  SupplierInformationDeferred
}
import com.agoda.papi.search.complexsearch.model.serializer.decoder.RequestDecoders.{
  BannerCampaignRequestDecoder,
  GrowthProgramInfoRequestDecoder,
  PriceStreamMetaLabRequestDecoder,
  PropertyDetailsRequestDecoder
}
import com.agoda.papi.search.internalmodel.growthprogram.PAPIGrowthProgramInfo
import com.agoda.papi.search.internalmodel.pricestream.{ PriceStreamPropertyMetaRanking, PriceStreamRankMetric }
import com.agoda.papi.search.internalmodel.promocode.BannerCampaignInfo
import io.circe.optics.JsonPath._
import com.agoda.pricestream.models.response._
import com.agoda.property.search.models._

import scala.util.Try

/** <AUTHOR>   mkhan
  */
object StaticSchema {

  import EnumSchema._

  /** Import Enum and scalar schema from different services (Rocks) schema */
  import com.agoda.papi.pricing.graphql.ScalarSchema._
  import RequestSchema._
  import ResponseSchema._
  import CoreEnumSchema.{ TravellerTypeEnum, TravellerTypeType }
  import com.agoda.core.search.models._
  import com.agoda.papi.search.internalmodel.serializer.decoder.RequestDecoders._

  /** Derive final request and response schema for this services */
  implicit val GeoPlaceType               = deriveObjectType[GraphQLContext, GeoPlace]()
  implicit val TopSellingPointInfoType    = deriveObjectType[GraphQLContext, TopSellingPointInfo]()
  implicit val UniqueSellingPointInfoType = deriveObjectType[GraphQLContext, UniqueSellingPointInfo]()
  implicit val BookingCountInfoType       = deriveObjectType[GraphQLContext, BookingCountInfo]()

  implicit val BookingHistoryInfoType = deriveObjectType[GraphQLContext, BookingHistoryInfo](
    ExcludeFields("lastBookingWithinThreshold")
  )

  implicit val ContentPropertyFacilityType = deriveObjectType[GraphQLContext, PropertyFacility]()
  implicit val HotelRoomInformationType    = deriveObjectType[GraphQLContext, HotelRoomInformation]()
  implicit val PricingBadgesType           = deriveObjectType[GraphQLContext, PricingBadges]()
  implicit val AlternateDateInfoType       = deriveObjectType[GraphQLContext, AlternateDateInfo]()
  implicit val SoldOutPriceType            = deriveObjectType[GraphQLContext, SoldOutPrice]()

  implicit val AlternateDatePricingType = deriveObjectType[GraphQLContext, AlternateDatePricing](
    ExcludeFields("pricing")
  )

  implicit val SoldOutSummaryType          = deriveObjectType[GraphQLContext, SoldOutSummary]()
  implicit val DatelessSummaryType         = deriveObjectType[GraphQLContext, DatelessSummary]()
  implicit val PropertySponsoredDetailType = deriveObjectType[GraphQLContext, PropertySponsoredDetail]()
  implicit val SupplierInformationType     = deriveObjectType[GraphQLContext, SupplierInformation]()
  implicit val BenefitInformationType      = deriveObjectType[GraphQLContext, BenefitInformation]()
  implicit val PointMaxInformationType     = deriveObjectType[GraphQLContext, PointMaxInformation]()
  implicit val CreditCardCampaignInfoType  = deriveObjectType[GraphQLContext, CreditCardCampaignInfo]()

  implicit val PriceStreamRankMetricType          = deriveObjectType[GraphQLContext, PriceStreamRankMetric]()
  implicit val PriceStreamPropertyMetaRankingType = deriveObjectType[GraphQLContext, PriceStreamPropertyMetaRanking]()
  implicit val PriceStreamPropertyMetaInfoResponseType =
    deriveObjectType[GraphQLContext, PriceStreamPropertyMetaInfoResponse]()

  implicit val PriceStreamPropertyAttributeType  = deriveObjectType[GraphQLContext, PropertyAttribute]()
  implicit val PriceStreamPropertyAttributesType = deriveObjectType[GraphQLContext, PropertyAttributes]()
  implicit val PriceStreamMetaLabResponseType =
    deriveObjectType[GraphQLContext, PriceStreamPropertyAttributesResponse]()
  implicit val PollingInfoResponseType = deriveObjectType[GraphQLContext, PollingInfoResponse]()
  implicit val BannerCampaignInfoType  = deriveObjectType[GraphQLContext, BannerCampaignInfo]()
  implicit val GrowthProgramInfoType   = deriveObjectType[GraphQLContext, PAPIGrowthProgramInfo]()

  implicit val CityPriceByCheckInType             = deriveObjectType[GraphQLContext, PriceByCheckIn]()
  implicit val PriceTrendSearchDetailsType        = deriveObjectType[GraphQLContext, PriceTrendSearchDetails]()
  implicit val PropertyAllotmentSearchDetailsType = deriveObjectType[GraphQLContext, PropertyAllotmentSearchDetails]()

  implicit val DistanceInfoResponseType       = deriveObjectType[GraphQLContext, DistanceInfo]()
  implicit val DistanceEnrichInfoResponseType = deriveObjectType[GraphQLContext, DistanceEnrichInfo]()

  implicit val PropertyFlexibleDateResponseType = deriveObjectType[GraphQLContext, PropertyFlexibleDate]()

  implicit val PropertySummaryEnrichmentType = deriveObjectType[GraphQLContext, PropertySummaryEnrichment](
    ExcludeFields("propertySummaryModel"),
    AddFields[GraphQLContext, PropertySummaryEnrichment](
      Field(
        "bookingHistory",
        OptionType(BookingHistoryInfoType),
        Some("BookingHistoryInfoType"),
        resolve = context => BookingHistoryDeferred(context.value.propertySummaryModel)
      ),
      Field(
        "isPopular",
        OptionType(BooleanType),
        Some("Whether this property is popular among customers"),
        resolve = context => IsPopularDeferred(context.value.propertySummaryModel.map(_.propertyId))
      ),
      Field(
        "roomInformation",
        OptionType(HotelRoomInformationType),
        Some("Consolidated room level information"),
        resolve = context => HotelRoomInformationDeferred(context.value.propertySummaryModel)
      )
    )
  )

  /** Internal Use only * */
  implicit val SearchEnrichmentResponseType = ObjectType(
    "SearchEnrichment",
    "SearchEnrichment - Keep Enrichment per search",
    fields[GraphQLContext, SearchEnrichmentResponse](
      Field(
        "suppliersInformation",
        ListType(SupplierInformationType),
        Some("suppliersInformation"),
        resolve = context =>
          new SupplierInformationDeferred(
            context.value.propertySearchRequestWrapper,
            context.value.cumulativePropertiesResponse
          )
      ),
      Field(
        "pointMaxInformation",
        OptionType(PointMaxInformationType),
        Some("pointMaxInformation"),
        resolve = context => new PointMaxInformationDeferred(context.value.propertySearchRequestWrapper)
      ),
      Field(
        "benefitInformation",
        ListType(BenefitInformationType),
        Some("benefitInformation"),
        resolve = context =>
          new BenefitInformationDeferred(
            context.value.propertySearchRequestWrapper,
            context.value.cumulativePropertiesResponse
          )
      ),
      Field(
        "creditCardCampaignInfo",
        OptionType(CreditCardCampaignInfoType),
        Some("Credit card campaign promotion details"),
        resolve = context =>
          CreditCardCampaignInfoDeferred(
            context.value.propertySearchRequestWrapper,
            context.value.cumulativePropertiesResponse
          )
      ),
      Field(
        "creditCardCampaignInfos",
        ListType(CreditCardCampaignInfoType),
        Some("Credit cards campaign promotion details"),
        resolve = context =>
          CreditCardCampaignInfosDeferred(
            context.value.propertySearchRequestWrapper,
            context.value.cumulativePropertiesResponse
          )
      ),
      Field(
        "isConnectedTripApplicable",
        OptionType(BooleanType),
        Some("check if any of the hotel is Connected Trip Eligible"),
        resolve = context =>
          IsConnectedTripApplicableDeferred(
            context.value.propertySearchRequestWrapper,
            context.value.cumulativePropertiesResponse
          )
      ),
      Field(
        "pageToken",
        OptionType(StringType),
        Some("keep state of paging"),
        resolve = context => context.value.cumulativePropertiesResponse.pageToken
      )
    )
  )

  implicit val PriceBreakerSummaryType = ObjectType(
    "PriceBreakerSummary",
    "PriceBreakerSummary - Content and Pricing summary",
    fields[GraphQLContext, PriceBreakerSummaryEnvelope](
      Field("propertyId", sangria.schema.LongType, Some("propertyId"), resolve = context => context.value.propertyId),
      Field(
        "propertyResultType",
        propertyResultTypeType,
        Some("Type of property have been return"),
        resolve = context => context.value.propertyResultType
      )
    )
  )

  implicit val PropertySummaryType = ObjectType(
    "PropertySummary",
    "PropertySummary - Content and Pricing summary",
    fields[GraphQLContext, PropertySummaryEnvelope](
      Field("propertyId", sangria.schema.LongType, Some("propertyId"), resolve = context => context.value.propertyId),
      Field(
        "propertyResultType",
        propertyResultTypeType,
        Some("Type of property have been return"),
        resolve = context => context.value.propertyResultType
      ),
      Field(
        "enrichment",
        OptionType(PropertySummaryEnrichmentType),
        Some("PropertySearchEnrichmentType"),
        resolve = context =>
          EnrichmentDeferred(
            PropertySummaryModel(
              context.value.propertyId,
              Some(context.value.content.t),
              context.value.pricing.map(_.t),
              context.value.propertyResultType,
              context.value.overrideCheckInOutDate,
              context.value.topSellingPointInfo,
              context.value.uniqueSellingPointInfo
            )
          )
      ),
      Field(
        "soldOut",
        OptionType(SoldOutSummaryType),
        Some("sold out property search result"),
        resolve = context => context.value.soldOut
      ),
      Field(
        "dateless",
        OptionType(DatelessSummaryType),
        Some("dateless property search result"),
        resolve = context => context.value.dateless
      ),
      Field(
        "sponsoredDetail",
        OptionType(PropertySponsoredDetailType),
        Some("sponsoredDetail"),
        resolve = context => context.value.sponsoredDetail
      ),
      Field(
        "metaLab",
        OptionType(PriceStreamPropertyAttributesType),
        Some("Metalab response"),
        resolve = context => context.value.metaLab
      ),
      Field(
        "priceBreakerProperties",
        OptionType(ListType(PriceBreakerSummaryType)),
        Some("priceBreakerProperties"),
        resolve = context => context.value.priceBreakerProperties
      )
    )
  )

  implicit val PropertySmartSearchResponseType = ObjectType(
    "PropertySearchResponse",
    "PropertySearchResponse - Content and Pricing summary",
    fields[GraphQLContext, PropertySmartSearchResponse](
      Field(
        "searchResult",
        GqlSearchResultType,
        Some("searchResult"),
        resolve = context => new SearchResultDeferred(context.value.searchResult)
      ),
      Field(
        name = "aggregation",
        fieldType = OptionType(AggregationResponseType),
        description = Some("Aggregated search data"),
        arguments = Nil,
        resolve = context => AggregationRequestDeferred(context.value.searchRequest, context.value.searchResult)
      ),
      Field(
        name = "geoPlaces",
        fieldType = OptionType(ListType(GeoPlaceType)),
        description = Some("List of PropertySummary for sold out properties"),
        arguments = Nil,
        resolve = context =>
          GeoPlacesDeferred(context.value.searchResult.searchInfo.objectInfo, context.value.searchRequest.searchContext)
      ),
      Field(
        name = "searchEnrichment",
        fieldType = OptionType(SearchEnrichmentResponseType),
        description = Some("Search Result Enrichment"),
        arguments = Nil,
        resolve = context =>
          new SearchEnrichmentDeferred(
            PropertySearchRequestWrapper(
              context.ctx.argumentProvider.args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.args.raw),
              Try(
                context.ctx.argumentProvider.args[PricingRequestParameters](PricingSummaryRequestArg, context.args.raw)
              ).toOption,
              None,
              None,
              context.value.searchRequest,
              context.ctx.globalContext,
              capiRequestContext = context.value.searchResult.capiRequestContext,
              capiEmailDomain = context.value.searchResult.capiEmailDomain
            ),
            context.value.searchResult
          )
      ),
      Field(
        name = "bannerCampaignInfo",
        fieldType = OptionType(ListType(BannerCampaignInfoType)),
        description = Some("bannerCampaignInfo"),
        arguments = BannerCampaignRequestTypeArg :: Nil,
        resolve = context =>
          SearchBannerCampaignInfoDeferred(
            context.value.searchRequest,
            context.ctx.argumentProvider.args(BannerCampaignRequestTypeArg, context.args.raw).t
          )
      )
    )
  )

  implicit val PropertySummarySearchResponseType = ObjectType(
    "PropertySummarySearchResponse",
    "PropertySummarySearchResponse - Content and Pricing summary",
    fields[GraphQLContext, PropertySummaryResponse](
      Field(
        "pollingInfo",
        OptionType(PollingInfoResponseType),
        Some("pollingInfo"),
        arguments = PollingInfoRequestTypeArg :: Nil,
        resolve = context =>
          PollingInfoDeferred(
            Try(
              context.ctx.argumentProvider
                .args[Option[PollingInfoRequest]](PollingInfoRequestTypeArg, context.args.raw)
                .t
            ).getOrElse(None)
          )
      )
    )
  )

  private def defineSearchQuery[T <: BaseRequest[ComplexSearchRequest]](
      namePrefix: String,
      argument: Argument[T],
      validation: Option[ValidationRule[T]] = None
  )(implicit decoder: Decoder[T]): Field[GraphQLContext, Unit] =
    Field(
      s"${namePrefix}Search",
      PropertySmartSearchResponseType,
      arguments = argument :: Nil,
      description = Some("Search response with content and DF"),
      resolve = context ⇒
        ComplexSearchDeferred(
          namePrefix,
          context.ctx.argumentProvider
            .args[T](argument, context.args.raw, Some(new ComplexSearchValidator[T](validation)))
        )
    )

  /** PropertyDetails * */

  // implicit val ContentResponseType = deriveObjectType[GraphQLContext, ContentDetail]()

  implicit val HostPropertyResponseType = deriveObjectType[GraphQLContext, HostPropertyResponse]()
  implicit val HostProfileResponseType  = deriveObjectType[GraphQLContext, HostProfileResponse]()

  implicit val ContentResponseType = ObjectType(
    "ContentDetail",
    "contentDetail",
    fields[GraphQLContext, ContentDetail](
      Field("propertyId", LongType, Some("propertyId"), resolve = context => context.value.propertyId),
      Field(
        "hostProfile",
        OptionType(HostProfileResponseType),
        Some("hostProfile"),
        arguments = HostProfileRequestTypeArg :: Nil,
        resolve = context =>
          HostProfileDeferred(
            context.value.propertyId,
            Try(context.ctx.argumentProvider.args(HostProfileRequestTypeArg, context.args.raw).t).toOption.flatten
          )
      )
    )
  )

  implicit val PropertyDetailType = ObjectType(
    "PropertyDetail",
    "Property Detail data",
    fields[GraphQLContext, PropertyDetail](
      Field("propertyId", LongType, Some("propertyId"), resolve = context => context.value.propertyId),
      Field(
        "contentDetail",
        OptionType(ContentResponseType),
        Some("contentDetail"),
        resolve = context => ContentDetailDeferred(context.value.propertyId)
      ),
      Field(
        "propertyMetaInfo",
        OptionType(PriceStreamPropertyMetaInfoResponseType),
        Some("propertyMetaInfo"),
        resolve = context => PriceStreamFetchPropertyMetaInfoResponseDeferred(context.value.propertyId, None)
      ),
      Field(
        name = "metaLab",
        fieldType = OptionType(PriceStreamMetaLabResponseType),
        description = Some("metaLabResponse"),
        arguments = PriceStreamMetaLabRequestTypeArg :: Nil,
        resolve = context =>
          PriceStreamFetchMetaLabResponseDeferred(
            context.value.propertyId,
            Try(context.ctx.argumentProvider.args(PriceStreamMetaLabRequestTypeArg, context.args.raw).t).toOption
          )
      ),
      Field(
        name = "bannerCampaignInfo",
        fieldType = OptionType(ListType(BannerCampaignInfoType)),
        description = Some("bannerCampaignInfo"),
        arguments = BannerCampaignRequestTypeArg :: Nil,
        resolve = context =>
          PropertyBannerCampaignInfoDeferred(
            context.value.propertyId,
            context.ctx.argumentProvider.args(BannerCampaignRequestTypeArg, context.args.raw).t
          )
      ),
      Field(
        name = "growthProgramInfo",
        fieldType = OptionType(GrowthProgramInfoType),
        description = Some("growthProgramInfo"),
        arguments = GrowthProgramInfoRequestTypeArg :: Nil,
        resolve = context =>
          GrowthProgramInfoDeferred(
            context.value.propertyId,
            context.ctx.argumentProvider.args(GrowthProgramInfoRequestTypeArg, context.args.raw).t
          )
      )
    )
  )

  implicit val PropertyDetailsResponseType = ObjectType(
    "PropertyDetailsResponse",
    "Properties search",
    fields[GraphQLContext, PropertyDetailsResponse](
      Field(
        "propertyDetails",
        ListType(PropertyDetailType),
        description = Some("Property Details"),
        resolve = context => PropertyDetailsDeferred(context.value.propertyDetailsRequest)
      )
    )
  )

  implicit val PriceTrendSearchResponseType = ObjectType(
    "PriceTrendSearchResponse",
    "PriceTrendSearchResponse - price trend",
    fields[GraphQLContext, PriceTrendSearchResponse](
      Field(
        "PriceTrendSearchDetails",
        PriceTrendSearchDetailsType,
        Some("PriceTrendSearchDetails"),
        resolve = context => PriceTrendSearchDetailsDeferred(context.value.priceTrendSearchRequest)
      )
    )
  )

  implicit val PropertyAllotmentSearchResponseType = ObjectType(
    "PropertyAllotmentSearchResponse",
    "Property allotment search",
    fields[GraphQLContext, PropertyAllotmentSearchResponse](
      Field(
        "PropertyAllotmentSearchDetails",
        PropertyAllotmentSearchDetailsType,
        Some("PropertyAllotmentSearchDetails"),
        resolve = context => PropertyAllotmentSearchDetailsDeferred(context.value.propertyAllotmentSearchRequest)
      )
    )
  )

  val staticQuery = ObjectType(
    "Query",
    fields[GraphQLContext, Unit](
      defineSearchQuery(
        namePrefix = "city",
        argument = CitySearchRequestTypeArg,
        validation = Some(CitySearchValidationRules)
      ),
      defineSearchQuery(
        namePrefix = "area",
        argument = AreaSearchRequestTypeArg,
        validation = Some(AreaSearchValidationRules)
      ),
      defineSearchQuery(
        namePrefix = "landmark",
        argument = LandmarkSearchRequestTypeArg
      ),
      defineSearchQuery(
        namePrefix = "radius",
        argument = RadiusSearchRequestTypeArg,
        validation = Some(RadiusSearchValidationRules)
      ),
      defineSearchQuery(
        namePrefix = "rectangle",
        argument = BoxSearchRequestTypeArg,
        validation = Some(RectangleSearchValidationRules)
      ),
      defineSearchQuery(
        namePrefix = "region",
        argument = RegionSearchRequestTypeArg
      ),
      defineSearchQuery(
        namePrefix = "topArea",
        argument = TopAreaSearchRequestTypeArg
      ),
      defineSearchQuery(
        namePrefix = "cityCenter",
        argument = CityCenterSearchRequestTypeArg
      ),
      defineSearchQuery(
        namePrefix = "state",
        argument = StateSearchRequestTypeArg
      ),
      Field(
        "propertyDetailsSearch",
        PropertyDetailsResponseType,
        arguments = PropertySearchRequestTypeArg :: Nil,
        description = Some("Search response with content and DF"),
        resolve = context =>
          PropertyDetailsResponse(
            context.ctx.argumentProvider
              .args[PropertyDetailsRequest](
                PropertySearchRequestTypeArg,
                context.args.raw,
                Some(new PropertyDetailsValidator[PropertyDetailsRequest]())
              )
              .t
          )
      ),
      Field(
        name = "recommendation",
        fieldType = OptionType(ListType(PropertySummaryType)),
        description = Some("Not implemented yet.List of recommended properties."),
        arguments = Nil,
        resolve = context => None
      ),
      Field(
        "propertySummarySearch",
        PropertySummarySearchResponseType,
        arguments = PropertySummarySearchRequestTypeArg :: Nil,
        description = Some("Properties summary search response"),
        resolve = context =>
          PropertySummaryResponse(
            context.ctx.argumentProvider
              .args[PropertySummarySearchRequest](
                PropertySummarySearchRequestTypeArg,
                context.args.raw,
                Some(new PropertyDetailsValidator[PropertySummarySearchRequest]())
              )
              .t
          )
      ),
      Field(
        "priceTrendSearch",
        fieldType = PriceTrendSearchResponseType,
        arguments = PriceTrendSearchRequestTypeArg :: Nil,
        description = Some("Price trend summary response"),
        resolve = context =>
          PriceTrendSearchResponse(
            context.ctx.argumentProvider
              .args[PriceTrendSearchRequest](
                PriceTrendSearchRequestTypeArg,
                context.args.raw,
                Some(new PriceTrendSearchValidator[PriceTrendSearchRequest]())
              )
              .t
          )
      ),
      Field(
        "propertyAllotmentSearch",
        fieldType = PropertyAllotmentSearchResponseType,
        arguments = PropertyAllotmentSearchRequestTypeArg :: Nil,
        description = Some("Property allotment response"),
        resolve = context =>
          PropertyAllotmentSearchResponse(
            context.ctx.argumentProvider
              .args[PropertyAllotmentSearchRequest](
                PropertyAllotmentSearchRequestTypeArg,
                context.args.raw,
                Some(new PropertyAllotmentSearchValidator[PropertyAllotmentSearchRequest]())
              )
              .t
          )
      )
    )
  )

  private val _schema = Schema(query = staticQuery, additionalTypes = scalars)

  def schema: Schema[GraphQLContext, Unit] = _schema

  private val masterRoomsOptics  = root.masterRooms.arr
  private val propertyIdOptics   = root.propertyId.long
  private val masterRoomIdOptics = root.masterRoomId.long
  private val childRoomIdsOptics = root.childRoomIds.arr
  private val defaultId          = -1L

  private val contentImagesDir = Directive("loadContentImages", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentReviewScoreDir =
    Directive("loadContentReviewScore", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentReviewSummariesDir =
    Directive("loadContentReviewSummaries", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentInformationSummaryDir =
    Directive("loadContentInformationSummary", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentInformationDir =
    Directive("loadContentInformation", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentLocalInformationDir =
    Directive("loadContentLocalInformation", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentEngagementDir =
    Directive("loadContentEngagement", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentExperiencesDir =
    Directive("loadContentExperiences", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentFeaturesDir = Directive("loadContentFeatures", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentHighlightsDir =
    Directive("loadContentHighlights", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentQnaDir = Directive("loadContentQna", locations = Set(DirectiveLocation.FieldDefinition))
  private val contentRateCategoryDir =
    Directive("loadContentRateCategory", locations = Set(DirectiveLocation.FieldDefinition))

  private val contentTopicsDir = Directive("loadContentTopics", locations = Set(DirectiveLocation.FieldDefinition))

  val propertyDetailsResolver: Seq[DirectiveResolver[GraphQLContext]] =
    Seq(
      DirectiveResolver[GraphQLContext](
        contentReviewSummariesDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentReviewSummariesDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentReviewSummariesRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentReviewScoreDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentReviewScoreDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentReviewScoreRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentImagesDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentImagesDeferred(
                value.propertyId,
                context.ctx.ctx.argumentProvider.args(ContentImagesRequestTypeArg, context.ctx.args.raw)
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentInformationSummaryDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentInformationSummaryDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentInformationSummaryRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentInformationDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentInformationDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentInformationRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentEngagementDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentEngagementDeferred(
                value.propertyId
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentFeaturesDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentFeaturesDeferred(
                value.propertyId,
                Try(context.ctx.ctx.argumentProvider.args(ContentFeaturesRequestTypeArg, context.ctx.args.raw)).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentQnaDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentQnaDeferred(
                value.propertyId,
                Try(context.ctx.ctx.argumentProvider.args(ContentQnaRequestTypeArg, context.ctx.args.raw)).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentHighlightsDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentHighlightsDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentHighlightsRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentLocalInformationDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentLocalInformationDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentLocalInformationRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentExperiencesDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentExperienceDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentExperienceRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentRateCategoryDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentRateCategoryDeferred(
                value.propertyId,
                Try(
                  context.ctx.ctx.argumentProvider.args(ContentRateCategoryRequestTypeArg, context.ctx.args.raw)
                ).toOption
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentTopicsDir,
        context =>
          context.ctx.value match {
            case value: ContentDetail =>
              ContentTopicsDeferred(
                value.propertyId,
                Try(context.ctx.ctx.argumentProvider.args(ContentTopicsRequestTypeArg, context.ctx.args.raw)).toOption
              )
            case _ => None
          }
      )
    )

  def propertiesSummarySearchResolvers: Seq[DirectiveResolver[GraphQLContext]] = {
    val propertiesSummarySearchDir =
      Directive("loadPropertySummarySearch", locations = Set(DirectiveLocation.FieldDefinition))

    Seq(
      DirectiveResolver[GraphQLContext](
        propertiesSummarySearchDir,
        context =>
          context.ctx.value match {
            case value: PropertySummaryResponse =>
              PropertySummarySearchDeferred(
                value.propertySummaryRequest.propertyIds,
                Try(
                  context.ctx.ctx.argumentProvider
                    .args[PricingRequestParameters](PricingSummaryRequestArg, context.ctx.args.raw)
                    .t
                ).toOption,
                Try(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw)
                    .t
                ).toOption
              )
            case _ => None
          }
      )
    )
  }

  // scalastyle:off
  def complexSearchResolvers: Seq[DirectiveResolver[GraphQLContext]] = {

    val contentDataLoaderDir = Directive("loadContent", locations = Set(DirectiveLocation.FieldDefinition))
    val pricingDataLoaderDir = Directive("loadPricing", locations = Set(DirectiveLocation.FieldDefinition))
    val alternatePricingDataLoaderDir =
      Directive("loadAlternatePricing", locations = Set(DirectiveLocation.FieldDefinition))
    val loadPropertyDir      = Directive("loadProperty", locations = Set(DirectiveLocation.FieldDefinition))
    val featuredAgodaHomeDir = Directive("featuredAgodaHome", locations = Set(DirectiveLocation.FieldDefinition))
    val highlyRatedAgodaHomesDir =
      Directive("highlyRatedAgodaHomes", locations = Set(DirectiveLocation.FieldDefinition))
    val extraAgodaHomesDir      = Directive("extraAgodaHomes", locations = Set(DirectiveLocation.FieldDefinition))
    val featuredLuxuryHotelsDir = Directive("featuredLuxuryHotels", locations = Set(DirectiveLocation.FieldDefinition))
    val featuredPulsePropertiesDir =
      Directive("featuredPulseProperties", locations = Set(DirectiveLocation.FieldDefinition))
    val masterRoomPath         = "masterRooms"
    val masterRoomDir          = Directive(masterRoomPath, locations = Set(DirectiveLocation.FieldDefinition))
    val contentPBDataLoaderDir = Directive("loadPBContent", locations = Set(DirectiveLocation.FieldDefinition))
    val pricingPBDataLoaderDir = Directive("loadPBPricing", locations = Set(DirectiveLocation.FieldDefinition))
    val contentRoomSummaryDataLoaderDir =
      Directive("loadContentRoomSummary", locations = Set(DirectiveLocation.FieldDefinition))

    Seq(
      DirectiveResolver[GraphQLContext](
        contentDataLoaderDir,
        context =>
          context.ctx.value match {
            case value: PropertySummaryEnvelope => value.content.json
            case _                              => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        pricingDataLoaderDir,
        context =>
          context.ctx.value match {
            case value: PropertySummaryEnvelope => value.pricing.map(_.json)
            case _                              => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentPBDataLoaderDir,
        context =>
          context.ctx.value match {
            case value: PriceBreakerSummaryEnvelope => value.content.json
            case _                                  => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        pricingPBDataLoaderDir,
        context =>
          context.ctx.value match {
            case value: PriceBreakerSummaryEnvelope => value.pricing.map(_.json)
            case _                                  => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        alternatePricingDataLoaderDir,
        context =>
          context.ctx.value match {
            case value: AlternateDatePricing => value.pricing
            case _                           => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        contentRoomSummaryDataLoaderDir,
        context =>
          context.ctx.value match {
            case value: PropertySummaryEnvelope =>
              RoomSummaryDeferred(
                value.propertyId,
                context.ctx.ctx.argumentProvider.args(ContentExtraRoomSummaryRequestTypeArg, context.ctx.args.raw),
                value.pricing
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        loadPropertyDir,
        context =>
          context.ctx.value match {
            case value: PropertySmartSearchResponse =>
              PropertyRequestDeferred(
                PropertySearchRequestWrapper(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw),
                  Try(
                    context.ctx.ctx.argumentProvider.args[PricingRequestParameters](
                      PricingSummaryRequestArg,
                      context.ctx.args.raw,
                      Some(pricingArgumentValidator)
                    )
                  ).toOption,
                  None,
                  None,
                  value.searchRequest,
                  context.ctx.ctx.globalContext,
                  metaLabRequest = Try(
                    context.ctx.ctx.argumentProvider
                      .args[PriceStreamMetaLabRequest](PriceStreamMetaLabRequestTypeArg, context.ctx.args.raw)
                      .t
                  ).toOption,
                  capiRequestContext = value.searchResult.capiRequestContext,
                  capiEmailDomain = value.searchResult.capiEmailDomain
                ),
                value.searchResult
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        featuredAgodaHomeDir,
        context =>
          context.ctx.value match {
            case value: PropertySmartSearchResponse =>
              FeaturedAgodaHomesRequestDeferred(
                PropertySearchRequestWrapper(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw),
                  Try(
                    context.ctx.ctx.argumentProvider.args[PricingRequestParameters](
                      PricingSummaryRequestArg,
                      context.ctx.args.raw,
                      Some(pricingArgumentValidator)
                    )
                  ).toOption,
                  None,
                  None,
                  value.searchRequest,
                  context.ctx.ctx.globalContext,
                  metaLabRequest = Try(
                    context.ctx.ctx.argumentProvider
                      .args[PriceStreamMetaLabRequest](PriceStreamMetaLabRequestTypeArg, context.ctx.args.raw)
                      .t
                  ).toOption,
                  capiRequestContext = value.searchResult.capiRequestContext,
                  capiEmailDomain = value.searchResult.capiEmailDomain
                ),
                value.searchResult
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        highlyRatedAgodaHomesDir,
        context =>
          context.ctx.value match {
            case value: PropertySmartSearchResponse =>
              HighlyRatedAgodaHomesRequestDeferred(
                PropertySearchRequestWrapper(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw),
                  Try(
                    context.ctx.ctx.argumentProvider.args[PricingRequestParameters](
                      PricingSummaryRequestArg,
                      context.ctx.args.raw,
                      Some(pricingArgumentValidator)
                    )
                  ).toOption,
                  None,
                  None,
                  value.searchRequest,
                  context.ctx.ctx.globalContext,
                  metaLabRequest = Try(
                    context.ctx.ctx.argumentProvider
                      .args[PriceStreamMetaLabRequest](PriceStreamMetaLabRequestTypeArg, context.ctx.args.raw)
                      .t
                  ).toOption,
                  capiRequestContext = value.searchResult.capiRequestContext,
                  capiEmailDomain = value.searchResult.capiEmailDomain
                ),
                value.searchResult
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        extraAgodaHomesDir,
        context =>
          context.ctx.value match {
            case value: PropertySmartSearchResponse =>
              ExtraAgodaHomesRequestDeferred(
                PropertySearchRequestWrapper(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw),
                  Try(
                    context.ctx.ctx.argumentProvider.args[PricingRequestParameters](
                      PricingSummaryRequestArg,
                      context.ctx.args.raw,
                      Some(pricingArgumentValidator)
                    )
                  ).toOption,
                  None,
                  None,
                  value.searchRequest,
                  context.ctx.ctx.globalContext,
                  metaLabRequest = Try(
                    context.ctx.ctx.argumentProvider
                      .args[PriceStreamMetaLabRequest](PriceStreamMetaLabRequestTypeArg, context.ctx.args.raw)
                      .t
                  ).toOption,
                  capiRequestContext = value.searchResult.capiRequestContext,
                  capiEmailDomain = value.searchResult.capiEmailDomain
                ),
                value.searchResult
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        featuredLuxuryHotelsDir,
        context =>
          context.ctx.value match {
            case value: PropertySmartSearchResponse =>
              FeaturedLuxuryHotelsRequestDeferred(
                PropertySearchRequestWrapper(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw),
                  Try(
                    context.ctx.ctx.argumentProvider.args[PricingRequestParameters](
                      PricingSummaryRequestArg,
                      context.ctx.args.raw,
                      Some(pricingArgumentValidator)
                    )
                  ).toOption,
                  None,
                  None,
                  value.searchRequest,
                  context.ctx.ctx.globalContext,
                  metaLabRequest = Try(
                    context.ctx.ctx.argumentProvider
                      .args[PriceStreamMetaLabRequest](PriceStreamMetaLabRequestTypeArg, context.ctx.args.raw)
                      .t
                  ).toOption,
                  capiRequestContext = value.searchResult.capiRequestContext,
                  capiEmailDomain = value.searchResult.capiEmailDomain
                ),
                value.searchResult
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        featuredPulsePropertiesDir,
        context =>
          context.ctx.value match {
            case value: PropertySmartSearchResponse =>
              FeaturedPulsePropertiesRequestDeferred(
                PropertySearchRequestWrapper(
                  context.ctx.ctx.argumentProvider
                    .args[ContentSummaryRequest](ContentSummaryRequestTypeArg, context.ctx.args.raw),
                  Try(
                    context.ctx.ctx.argumentProvider.args[PricingRequestParameters](
                      PricingSummaryRequestArg,
                      context.ctx.args.raw,
                      Some(pricingArgumentValidator)
                    )
                  ).toOption,
                  None,
                  None,
                  value.searchRequest,
                  context.ctx.ctx.globalContext,
                  metaLabRequest = Try(
                    context.ctx.ctx.argumentProvider
                      .args[PriceStreamMetaLabRequest](PriceStreamMetaLabRequestTypeArg, context.ctx.args.raw)
                      .t
                  ).toOption,
                  capiRequestContext = value.searchResult.capiRequestContext,
                  capiEmailDomain = value.searchResult.capiEmailDomain
                ),
                value.searchResult
              )
            case _ => None
          }
      ),
      DirectiveResolver[GraphQLContext](
        masterRoomDir,
        context => {
          // Filter from Json
          val json                      = context.ctx.value.asInstanceOf[Json]
          val masterRooms: Vector[Json] = masterRoomsOptics.getOption(json).getOrElse(Vector.empty)

          masterRooms.filter { master =>
            val propertyId: Long = propertyIdOptics.getOption(master).getOrElse(defaultId)
            val propertyOpt      = context.ctx.ctx.reportingDataBuilder.getProperty(propertyId)
            val cheapestRoomId: Option[Long] = propertyOpt.flatMap(p =>
              p.pricing.flatMap(
                _.t.offers.headOption.map(_.roomOffers.headOption.map(_.room.typeId).getOrElse(defaultId))
              )
            )
            val cheapestRoomIdStr = cheapestRoomId.getOrElse(defaultId).toString
            masterRoomIdOptics.getOption(master).getOrElse(defaultId).toString == cheapestRoomIdStr ||
            childRoomIdsOptics.getOption(master).getOrElse(Vector.empty).exists(_.noSpaces == cheapestRoomIdStr)
          }.toList
        }
      )
    )
  }
  // scalastyle:on

  def builder(implicit executionContext: ExecutionContext): ResolverBasedAstSchemaBuilder[GraphQLContext] =
    RemoteSupportedResolverBasedAstSchemaBuilder.resolverBased[GraphQLContext](
      Seq(InstanceCheck.field[GraphQLContext, Json], FieldResolver.defaultInput[GraphQLContext, Json]) ++
        complexSearchResolvers ++
        propertyDetailsResolver ++
        propertiesSummarySearchResolvers: _*
    )

}
