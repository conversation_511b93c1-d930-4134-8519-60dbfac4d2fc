package com.agoda.papi.search.api.http.rest.openapi.schema

import com.agoda.commons.traffic.{ TrafficInfo, TrafficType }
import com.agoda.core.search.models.{
  CancellationPoliciesResult,
  CancellationPoliciesResultEntry,
  PolicyPhase,
  PolicyPhaseFee
}
import com.agoda.core.search.models.request.{ CancellationRequestEntry, PollingInfoRequest, SearchHistory, TrackSteps }
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{
  ArrangementEntries,
  ArrangementEntry,
  ItemEntries,
  ItemEntry,
  ProductEntries,
  ProductEntry
}
import com.agoda.upi.models.request.{ CartBaseRequest, CartItemMetadata, CartMetadata }
import models.starfruit.ABTest
import org.joda.time.DateTime
import request.{ CancellationPolicyRequest, ForcedExperiment, PropertyContext }
import sttp.tapir.Schema.SName
import sttp.tapir.SchemaType.{ SDateTime, SString }
import sttp.tapir._

import java.util.Date

// This is the schema definition code to generate openapi spec for request and response models of cancellation policy endpoint v2
object CancellationPolicySchemaDefinition {
  implicit def schemaForIterable[T: Schema, C[X] <: Iterable[X]]: Schema[C[T]] =
    implicitly[Schema[T]].asIterable[C].copy(isOptional = false)
  implicit lazy val dateTimeSchema: Schema[DateTime]                                 = Schema(SDateTime())
  implicit lazy val dateSchema: Schema[Date]                                         = Schema.schemaForDate
  implicit lazy val charSchema: Schema[Char]                                         = Schema(SString[Char]())
  implicit lazy val abTestSchema: Schema[ABTest]                                     = Schema.derived
  implicit lazy val cancellationRequestEntrySchema: Schema[CancellationRequestEntry] = Schema.derived
  implicit lazy val forcedExperimentSchema: Schema[ForcedExperiment]                 = Schema.derived
  implicit lazy val experimentsSchema: Schema[request.Experiments]                   = Schema.derived
  implicit lazy val botSchema: Schema[request.Bot]                                   = Schema.derived

  implicit lazy val searchTypeSchema: Schema[com.agoda.core.search.models.enumeration.SearchType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.enumeration.SearchType")),
      validator = Validator.enumeration(
        com.agoda.core.search.models.enumeration.SearchTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val searchHistorySchema: Schema[SearchHistory] = Schema.derived

  implicit lazy val trafficTypeSchema: Schema[TrafficType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.commons.traffic.TrafficType")),
      validator = Validator.enumeration(
        List(
          com.agoda.commons.traffic.User,
          com.agoda.commons.traffic.UnknownBot,
          com.agoda.commons.traffic.KnownBot,
          com.agoda.commons.traffic.PrerenderBot,
          com.agoda.commons.traffic.Unknown
        ),
        s => Some(s.trafficType)
      )
    )

  implicit lazy val trafficInfoSchema: Schema[TrafficInfo]               = Schema.derived
  implicit lazy val trackStepsSchema: Schema[TrackSteps]                 = Schema.derived
  implicit lazy val pollingInfoRequestSchema: Schema[PollingInfoRequest] = Schema.derived

  implicit lazy val arrangementEntrySchema: Schema[ArrangementEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.ArrangementEntry")),
      validator = Validator.enumeration(ArrangementEntries.values.toList, s => Some(s.entryName))
    )

  implicit lazy val arrangementSchema: Schema[Arrangement] = Schema.derived

  implicit lazy val itemEntrySchema: Schema[ItemEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.ItemEntry")),
      validator = Validator.enumeration(ItemEntries.values.toList, s => Some(s.entryName))
    )

  implicit lazy val productEntrySchema: Schema[ProductEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.ProductEntry")),
      validator = Validator.enumeration(ProductEntries.values.toList, s => Some(s.entryName))
    )

  implicit lazy val cartItemMetadataSchema: Schema[CartItemMetadata]                   = Schema.derived
  implicit lazy val cartMetadataSchema: Schema[CartMetadata]                           = Schema.derived
  implicit lazy val cartBaseRequestSchema: Schema[CartBaseRequest]                     = Schema.derived
  implicit lazy val propertyContextSchema: Schema[PropertyContext]                     = Schema.derived
  implicit lazy val cancellationPolicyRequestSchema: Schema[CancellationPolicyRequest] = Schema.derived

  implicit lazy val cancellationTypeSchema: Schema[com.agoda.core.search.models.CancellationType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.CancellationType")),
      validator =
        Validator.enumeration(com.agoda.core.search.models.CancellationTypes.values.toList, s => Some(s.entryName))
    )

  implicit lazy val cancellationFeeTypeSchema: Schema[com.agoda.core.search.models.CancellationFeeType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.CancellationFeeType")),
      validator =
        Validator.enumeration(com.agoda.core.search.models.CancellationFeeTypes.values.toList, s => Some(s.entryName))
    )

  implicit lazy val policyPhaseFeeSchema: Schema[PolicyPhaseFee]                                   = Schema.derived
  implicit lazy val policyPhaseSchema: Schema[PolicyPhase]                                         = Schema.derived
  implicit lazy val cancellationPoliciesResultEntrySchema: Schema[CancellationPoliciesResultEntry] = Schema.derived
  implicit lazy val cancellationPoliciesResultSchema: Schema[CancellationPoliciesResult]           = Schema.derived
}
