package com.agoda.papi.search.api.config

import com.agoda.commons.dynamic.state.State
import com.typesafe.config.Config

import scala.util.Try

case class SsrDelayInjectConfig(
    experiment1Percentage: State[Double],
    experiment2Percentage: State[Double],
    experiment3Percentage: State[Double],
    enable: State[Boolean]
)

object SsrDelayInjectConfig {
  private val DefaultDelayPercentage = 20.0d

  def fromConsul(watchedConfig: State[Config]): SsrDelayInjectConfig = {
    val exp1Percentage = getPercentageFromConsul(watchedConfig, 1)
    val exp2Percentage = getPercentageFromConsul(watchedConfig, 2)
    val exp3Percentage = getPercentageFromConsul(watchedConfig, 3)
    val enable = watchedConfig.map { configFromConsul =>
      Try(configFromConsul.getBoolean("inject-delay.enable")).getOrElse(false)
    }

    SsrDelayInjectConfig(exp1Percentage, exp2Percentage, exp3Percentage, enable)
  }

  private def getPercentageFromConsul(watchedConfig: State[Config], experimentNumber: Int): State[Double] =
    watchedConfig.map { configFromConsul =>
      Try(configFromConsul.getDouble(s"inject-delay.experiment-$experimentNumber"))
        .getOrElse(DefaultDelayPercentage)
    }

}
