package com.agoda.papi.search.api.http.rest.openapi.schema

import com.agoda.papi.pricing.supply.models.GrowthProgramInfo
import com.agoda.papi.search.internalmodel.serializer.encoder.KeyEncoders
import io.circe.KeyEncoder
import request.{ PAPIRateCategoryRequest, PropertyRequest }
import sttp.tapir.Schema.SName
import sttp.tapir.{ Schema, _ }
import transformers.RecommendedUpgrade

// This is the schema definition code to generate openapi spec for request and response models of property endpoint v2
object PropertySchemaDefinition {
  import CancellationPolicySchemaDefinition._ // TODO: move common to common

  implicit lazy val travellerTypeSchema: Schema[com.agoda.core.search.models.enumeration.TravellerType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.enumeration.TravellerType")),
      validator = Validator.enumeration(
        com.agoda.core.search.models.enumeration.TravellerTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val sortFieldTypeSchema: Schema[com.agoda.core.search.models.enumeration.SortField] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.enumeration.SortField")),
      validator = Validator.enumeration(
        com.agoda.core.search.models.enumeration.SortFields.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val sortOrderTypeSchema: Schema[com.agoda.core.search.models.enumeration.SortOrder] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.enumeration.SortOrder")),
      validator = Validator.enumeration(
        com.agoda.core.search.models.enumeration.SortOrders.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RecommendationTypeSchema: Schema[enumerations.RecommendationType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("enumerations.RecommendationType")),
      validator = Validator.enumeration(
        enumerations.RecommendationTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ApplyTypeSchema: Schema[models.pricing.enums.ApplyType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.ApplyType")),
      validator = Validator.enumeration(
        models.pricing.enums.ApplyTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ExternalServiceModeSchema: Schema[request.ExternalServiceMode] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("request.ExternalServiceMode")),
      validator = Validator.enumeration(
        request.ExternalServiceModes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RecommendationFilterFieldTypeSchema: Schema[enumerations.RecommendationFilterFieldType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("enumerations.RecommendationFilterFieldType")),
      validator = Validator.enumeration(
        enumerations.RecommendationFilterFieldTypes.values.toList,
        s => Some(s.entryName)
      )
    )

//magnolia: could not find Schema.Typeclass for type enumerations.RecommendationFilterComparisonType
  implicit lazy val RecommendationFilterComparisonTypeSchema: Schema[enumerations.RecommendationFilterComparisonType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("enumerations.RecommendationFilterComparisonType")),
      validator = Validator.enumeration(
        enumerations.RecommendationFilterComparisonTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val SuggestedPriceSchema: Schema[models.starfruit.SuggestedPrice] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.SuggestedPrice")),
      validator = Validator.enumeration(
        models.starfruit.SuggestedPrice.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RequestedPriceSchema: Schema[models.pricing.enums.RequestedPrice] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RequestedPrice")),
      validator = Validator.enumeration(
        models.pricing.enums.RequestedPrice.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CampaignTypeSchema: Schema[com.agoda.papi.enums.campaign.CampaignType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.CampaignType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.CampaignTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val LoyaltyTypeSchema: Schema[models.starfruit.LoyaltyType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.LoyaltyType")),
      validator = Validator.enumeration(
        models.starfruit.LoyaltyTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val SearchTypeSchema: Schema[request.SearchType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("request.PapiSearchType")),
      validator = Validator.enumeration(
        request.SearchTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PaymentModelSchema: Schema[models.pricing.enums.PaymentModel] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.PaymentModel")),
      validator = Validator.enumeration(
        models.pricing.enums.PaymentModels.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CampaignDiscountTypeSchema: Schema[com.agoda.papi.enums.campaign.CampaignDiscountType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.CampaignDiscountType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.CampaignDiscountTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val DiscountTypeSchema: Schema[models.pricing.DiscountType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.DiscountType")),
      validator = Validator.enumeration(
        models.pricing.DiscountTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ChildRateTypeSchema: Schema[com.agoda.papi.enums.room.ChildRateType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.ChildRateType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.ChildRateType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CancellationGroupSchema: Schema[com.agoda.papi.enums.room.CancellationGroup] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.CancellationGroup")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.CancellationGroup.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val SwapRoomTypeSchema: Schema[models.pricing.enums.SwapRoomType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.SwapRoomType")),
      validator = Validator.enumeration(
        models.pricing.enums.SwapRoomTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val filterCriteriaSchema: Schema[com.agoda.papi.enums.request.FilterCriteria] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.request.FilterCriteria")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.request.FilterCriteria.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val StackDiscountOptionSchema: Schema[com.agoda.papi.enums.request.StackDiscountOption] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.request.StackDiscountOption")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.request.StackDiscountOption.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val FeatureFlagSchema: Schema[api.request.FeatureFlag] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("api.request.FeatureFlag")),
      validator = Validator.enumeration(
        api.request.FeatureFlag.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RocketmilePublishPriceLogicTypeSchema: Schema[models.starfruit.RocketmilePublishPriceLogicType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.RocketmilePublishPriceLogicType")),
      validator = Validator.enumeration(
        models.starfruit.RocketmilePublishPriceLogicTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RocketmileSortByFieldSchema: Schema[models.starfruit.RocketmileSortByField] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.RocketmileSortByField")),
      validator = Validator.enumeration(
        models.starfruit.RocketmileSortByFields.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val taxRegistrationTypeSchema: Schema[com.agoda.papi.enums.hotel.TaxRegistrationType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.hotel.TaxRegistrationType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.hotel.TaxRegistrationType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val demographicsFilterSchema: Schema[com.agoda.content.models.DemographicsFilter] = Schema.derived

  implicit lazy val cumulativeRequestSchema: Schema[com.agoda.content.models.CumulativeRequest] = Schema.derived

  implicit lazy val hourlySortParamsSchema: Schema[com.agoda.core.search.models.enumeration.HourlySortParams] =
    Schema.derived

  implicit lazy val sortParamsSchema: Schema[com.agoda.core.search.models.enumeration.SortParams] = Schema.derived

  implicit lazy val sortingSchema: Schema[com.agoda.core.search.models.enumeration.Sorting]         = Schema.derived
  implicit lazy val pageSchema: Schema[com.agoda.content.models.request.Page]                       = Schema.derived
  implicit lazy val demographicsRequestSchema: Schema[com.agoda.content.models.DemographicsRequest] = Schema.derived
  implicit lazy val keyImageSizeSchema: Schema[com.agoda.content.models.KeyImageSize]               = Schema.derived

  implicit lazy val filterDefinitionSchema: Schema[com.agoda.core.search.models.request.FilterDefinition] =
    Schema.string // TODO: Handle subtypes

  implicit lazy val imageSizeSchema: Schema[com.agoda.content.models.db.image.ImageSize] = Schema.derived
  implicit lazy val imageSizeRequestSchema: Schema[com.agoda.content.models.db.image.ImageSizeRequest] =
    Schema.derived
  implicit lazy val imagesRequestSchema: Schema[com.agoda.content.models.ImagesRequest] = Schema.derived

  implicit lazy val geoPointSchema: Schema[com.agoda.core.search.models.request.GeoPoint]             = Schema.derived
  implicit lazy val boxRequestSchema: Schema[request.BoxRequest]                                      = Schema.derived
  implicit lazy val entertainmentRequestSchema: Schema[com.agoda.content.models.EntertainmentRequest] = Schema.derived
  implicit lazy val featuresRequestSchema: Schema[com.agoda.content.models.FeaturesRequest]           = Schema.derived

  implicit lazy val reviewSnippetTopicRequestSchema: Schema[com.agoda.content.models.ReviewSnippetTopicRequest] =
    Schema.derived

  implicit lazy val filtersSchema: Schema[com.agoda.content.models.db.reviews.Filters] = Schema.derived

  implicit lazy val searchFilterSchema: Schema[com.agoda.content.models.SearchFilter] = Schema.derived

  implicit lazy val commentaryRequestSchema: Schema[com.agoda.content.models.CommentaryRequest] = Schema.derived

  implicit lazy val summariesRequestSchema: Schema[com.agoda.content.models.SummariesRequest] = Schema.derived

  implicit lazy val radiusRequestSchema: Schema[request.RadiusRequest] = Schema.derived

  implicit lazy val informationRequestSchema: Schema[com.agoda.content.models.InformationRequest] = Schema.derived

  implicit lazy val occupancyRequestSchema: Schema[com.agoda.content.models.OccupancyRequest] = Schema.derived

  implicit lazy val searchCriteriaSchema: Schema[request.SearchCriteria] = Schema.derived

  implicit lazy val emptyRequestSchema: Schema[com.agoda.content.models.EmptyRequest] = Schema.derived

  implicit lazy val summaryRequestSchema: Schema[com.agoda.content.models.SummaryRequest] = Schema.derived

  implicit lazy val imageSchema: Schema[logger.Image] = Schema.derived

  implicit lazy val localInformationRequestSchema: Schema[com.agoda.content.models.LocalInformationRequest] =
    Schema.derived

  implicit lazy val PartnerRequestSchema: Schema[models.starfruit.PartnerRequest] = Schema.derived

  implicit lazy val recommendationFilterSchema: Schema[request.RecommendationFilter] = Schema.derived

  implicit lazy val recommendationSchema: Schema[request.Recommendation] = Schema.derived

  implicit lazy val roomFilterCriteriaSchema: Schema[request.RoomFilterCriteria] = Schema.derived

  implicit lazy val RoomRequestPAPISchema: Schema[request.RoomRequestPAPI] = Schema.derived

  implicit lazy val experienceRequestSchema: Schema[com.agoda.content.models.ExperienceRequest] = Schema.derived

  implicit lazy val HighlightsRequestSchema: Schema[com.agoda.content.models.HighlightsRequest] = Schema.derived

  implicit lazy val ChildPromotionSchema: Schema[models.starfruit.ChildPromotion] = Schema.derived

  implicit lazy val CampaignInfoSchema: Schema[models.starfruit.CampaignInfo] = Schema.derived

  implicit lazy val LoyaltyPaymentRequestSchema: Schema[models.starfruit.LoyaltyPaymentRequest] = Schema.derived

  implicit lazy val CreditCardPaymentRequestSchema: Schema[request.CreditCardPaymentRequest] = Schema.derived

  implicit lazy val InstallmentInfoSchema: Schema[models.starfruit.InstallmentInfo] = Schema.derived

  implicit lazy val PaymentRequestSchema: Schema[request.PaymentRequest] = Schema.derived

  implicit lazy val SimulatePromotionDiscountSchema: Schema[api.request.SimulatePromotionDiscount] = Schema.derived

  implicit lazy val CampaignDiscountSchema: Schema[models.starfruit.CampaignDiscount] = Schema.derived

  implicit lazy val OverrideCampaignSchema: Schema[models.starfruit.OverrideCampaign] = Schema.derived

  implicit lazy val DiscountRequestSchema: Schema[models.starfruit.DiscountRequest] = Schema.derived

  implicit lazy val FilterRequestSchema: Schema[com.agoda.content.models.FilterRequest] = Schema.derived

  implicit lazy val SimulatePromotionSchema: Schema[api.request.SimulatePromotion] = Schema.derived

  implicit lazy val SupplierPullMetadataRequestSchema: Schema[models.starfruit.SupplierPullMetadataRequest] =
    Schema.derived

  implicit lazy val SimulateRequestDataSchema: Schema[api.request.SimulateRequestData] = Schema.derived

  implicit lazy val FeatureFlagRequestSchema: Schema[request.FeatureFlagRequest] = Schema.derived

  implicit lazy val SimulatePromotionCmsDataSchema: Schema[api.request.SimulatePromotionCmsData] = Schema.derived

  implicit lazy val ApplyDateSchema: Schema[Map[org.joda.time.DateTime, Int]] =
    Schema.schemaForMap[org.joda.time.DateTime, Int] { k =>
      KeyEncoder[org.joda.time.DateTime](KeyEncoders.dateTimeKeyEncoder).apply(k)
    }

  implicit lazy val SimulatePromotionConstraintsDataSchema: Schema[api.request.SimulatePromotionConstraintsData] =
    Schema.derived

  implicit lazy val CustomerSegmentSchema: Schema[models.pricing.proto.CustomerSegment] = Schema.derived

  implicit lazy val SimulatePromotionStackOptionsSchema: Schema[api.request.SimulatePromotionStackOptions] =
    Schema.derived

  implicit lazy val SimulatePromotionDataSchema: Schema[api.request.SimulatePromotionData] = Schema.derived

  implicit lazy val OccFilterSchema: Schema[models.starfruit.OccFilter] = Schema.derived

  implicit lazy val MatchedRoomCriteriaSchema: Schema[api.request.MatchedRoomCriteria] = Schema.derived

  implicit lazy val PriceStateRequestSchema: Schema[models.starfruit.PriceStateRequest] = Schema.derived

  implicit lazy val NationalitySpecificRequestSchema: Schema[com.agoda.content.models.NationalitySpecificRequest] =
    Schema.derived

  implicit lazy val FencedOriginObjectSchema: Schema[models.starfruit.FencedOriginObject] = Schema.derived

  implicit lazy val FencedRateKeySchema: Schema[models.starfruit.FencedRateKey] = Schema.derived

  implicit lazy val FencedRatePairSchema: Schema[models.starfruit.FencedRatePair] = Schema.derived

  implicit lazy val StringAndFencedOriginObjectSchema: Schema[models.starfruit.StringAndFencedOriginObject] =
    Schema.derived

  implicit lazy val FencedRateSchema: Schema[models.starfruit.FencedRate] =
    Schema.derived.name(Schema.SName("models.starfruit.DfFencedRate"))

  implicit lazy val OverrideRoomIdentifierSchema: Schema[models.starfruit.OverrideRoomIdentifier] = Schema.derived

  implicit lazy val RoomIdentifierSchema: Schema[models.starfruit.RoomIdentifier] = Schema.derived

  implicit lazy val USPRequestSchema: Schema[com.agoda.content.models.USPRequest] = Schema.derived

  implicit lazy val RoomSelectionRequestSchema: Schema[models.starfruit.RoomSelectionRequest] = Schema.derived

  implicit lazy val PackagingTokenSchema: Schema[com.agoda.core.search.models.request.PackagingToken] = Schema.derived

  implicit lazy val PackagingSchema: Schema[com.agoda.core.search.models.request.Packaging] = Schema.derived

  implicit lazy val ChildTypeSchema: Schema[models.starfruit.ChildType] = Schema.derived

  implicit lazy val ChildAgeRangeAssignmentSchema: Schema[models.starfruit.ChildAgeRangeAssignment] = Schema.derived

  implicit lazy val RoomAssignmentSchema: Schema[models.starfruit.RoomAssignment] = Schema.derived

  implicit lazy val RoomBundleRequestSchema: Schema[models.starfruit.RoomBundleRequest] = Schema.derived

  implicit lazy val SimplifiedRoomSelectionRequestSchema: Schema[models.starfruit.SimplifiedRoomSelectionRequest] =
    Schema.derived

  implicit lazy val RoomIdentifierFilterSchema: Schema[models.starfruit.RoomIdentifierFilter] = Schema.derived

  implicit lazy val SymmetricUidFilterSchema: Schema[models.starfruit.SymmetricUidFilter] = Schema.derived

  implicit lazy val CancellationGroupFilterSchema: Schema[models.starfruit.CancellationGroupFilter] = Schema.derived

  implicit lazy val PriceAdjustmentSchema: Schema[models.starfruit.PriceAdjustment] = Schema.derived

  implicit lazy val PackagingFilterContextSchema: Schema[models.starfruit.PackagingFilterContext] = Schema.derived

  implicit lazy val FeatureRequestSchema: Schema[models.starfruit.FeatureRequest] = Schema.derived

  implicit lazy val ExternalLoyaltyRequestSchema: Schema[models.starfruit.ExternalLoyaltyRequest] = Schema.derived

  implicit lazy val propertyPricingSchema: Schema[request.PropertyPricing] = Schema.derived

  implicit lazy val BackupRoomCriteriaSchema: Schema[api.request.BackupRoomCriteria] = Schema.derived

  implicit lazy val BookingFilterSchema: Schema[models.starfruit.BookingFilter] = Schema.derived

  implicit lazy val ReBookingRequestSchema: Schema[models.starfruit.ReBookingRequest] = Schema.derived

  implicit lazy val SelectedHourlySlotSchema: Schema[models.starfruit.SelectedHourlySlot] = Schema.derived

  implicit lazy val PriceFreezeSchema: Schema[models.starfruit.PriceFreeze] = Schema.derived

  implicit lazy val AmendmentRequestSchema: Schema[models.postbooking.AmendmentRequest] = Schema.derived

  implicit lazy val CustomBreakdownDetailSchema: Schema[models.postbooking.CustomBreakdownDetail] = Schema.derived

  implicit lazy val CustomPricingBreakdownSchema: Schema[models.postbooking.CustomPricingBreakdown] = Schema.derived

  implicit lazy val BookingRequestSchema: Schema[models.starfruit.BookingRequest] = Schema.derived

  implicit lazy val TopSellingPointRequestSchema: Schema[request.TopSellingPointRequest] = Schema.derived

  implicit lazy val SoldOutAlternateDatesRequestSchema: Schema[request.SoldOutAlternateDatesRequest] = Schema.derived

  implicit lazy val ContentEscapeRateCategoriesRequestSchema
      : Schema[com.agoda.content.models.request.sf.ContentEscapeRateCategoriesRequest] =
    Schema.derived

  implicit lazy val ContentRateCategoriesRequestSchema
      : Schema[com.agoda.content.models.request.sf.ContentRateCategoriesRequest] =
    Schema.derived

  implicit lazy val RateCategoryIdWithRoomSchema: Schema[com.agoda.content.models.RateCategoryIdWithRoom] =
    Schema.derived

  implicit lazy val RateCategoryDetailRequestSchema: Schema[com.agoda.content.models.RateCategoryDetailRequest] =
    Schema.derived

  implicit lazy val RateCategoryRequestSchema: Schema[com.agoda.content.models.RateCategoryRequest] =
    Schema.derived.name(Schema.SName("com.agoda.content.models.ContentRateCategoryRequest"))

  implicit lazy val PAPIRateCategoryRequestSchema: Schema[PAPIRateCategoryRequest] =
    Schema.derived.name(Schema.SName("request.RateCategoryRequest"))

  implicit lazy val RocketmilesPublishPriceRequestSchema: Schema[models.starfruit.RocketmilesPublishPriceRequest] =
    Schema.derived
  implicit lazy val propertyRequestSchema: Schema[PropertyRequest] = Schema.derived

  implicit lazy val LocalizationValueSchema: Schema[com.agoda.content.models.LocalizationValue] = Schema.derived

  implicit lazy val ContentLocationsSchema: Schema[com.agoda.content.models.propertycontent.ContentLocations] =
    Schema.derived

  implicit lazy val ImageLocalizationsSchema: Schema[com.agoda.content.models.db.image.ImageLocalizations] =
    Schema.derived

  implicit lazy val ImageSnippetSchema: Schema[com.agoda.content.models.db.image.ImageSnippet] = Schema.derived

  implicit lazy val ImageResponseSchema: Schema[com.agoda.content.models.db.image.ImageResponse] = Schema.derived

  implicit lazy val FeatureTagSchema: Schema[com.agoda.content.models.db.features.FeatureTag] = Schema.derived

  implicit lazy val FeatureSchema: Schema[com.agoda.content.models.db.features.Feature] = Schema.derived

  implicit lazy val FeatureGroupSchema: Schema[com.agoda.content.models.db.features.FeatureGroup] = Schema.derived

  implicit lazy val FeatureGroupTypeSchema: Schema[com.agoda.content.models.db.features.FeatureGroupType] =
    Schema.derived

  implicit lazy val HotelFacilitySchema: Schema[com.agoda.content.models.db.features.HotelFacility] = Schema.derived

  implicit lazy val HygienePlusSchema: Schema[com.agoda.content.models.db.features.HygienePlus] = Schema.derived

  implicit lazy val HygienePlusFacilityIdsSchema: Schema[com.agoda.content.models.db.features.HygienePlusFacilityIds] =
    Schema.derived

  implicit lazy val StaycationFacilityIdsSchema: Schema[com.agoda.content.models.db.features.StaycationFacilityIds] =
    Schema.derived

  implicit lazy val FeatureSummarySchema: Schema[com.agoda.content.models.db.features.FeatureSummary] = Schema.derived

  implicit lazy val FacilityHighlightSchema: Schema[com.agoda.content.models.db.features.FacilityHighlight] =
    Schema.derived

  implicit lazy val AdditionalInfoSchema: Schema[com.agoda.content.models.db.features.AdditionalInfo] = Schema.derived

  implicit lazy val FeatureGroupTypesSchema: Schema[com.agoda.content.models.db.features.FeatureGroupTypes] =
    Schema.derived

  implicit lazy val ReviewCountSchema: Schema[com.agoda.content.models.db.reviews.ReviewCount] = Schema.derived

  implicit lazy val ProviderSchema: Schema[com.agoda.content.models.db.common.Provider] = Schema.derived

  implicit lazy val RecommendationSchema: Schema[com.agoda.content.models.db.reviews.Recommendation] =
    Schema.derived.name(Schema.SName("com.agoda.content.models.db.reviews.ContentRecommendation"))

  implicit lazy val ReviewCommentSchema: Schema[com.agoda.content.models.db.reviews.ReviewComment] = Schema.derived

  implicit lazy val ReviewFilterSchema: Schema[com.agoda.content.models.db.reviews.ReviewFilter] = Schema.derived

  implicit lazy val ReviewFilterByProviderSchema: Schema[com.agoda.content.models.db.reviews.ReviewFilterByProvider] =
    Schema.derived

  implicit lazy val ReviewFilterGroupSchema: Schema[com.agoda.content.models.db.reviews.ReviewFilterGroup] =
    Schema.derived

  implicit lazy val ReviewCommentsSchema: Schema[com.agoda.content.models.db.reviews.ReviewComments] = Schema.derived

  implicit lazy val ReviewScoreRemarksSchema: Schema[com.agoda.content.models.db.reviews.ReviewScoreRemarks] =
    Schema.derived

  implicit lazy val ReviewScoreSubGradeSchema: Schema[com.agoda.content.models.db.reviews.ReviewScoreSubGrade] =
    Schema.derived

  implicit lazy val ContentReviewScoreGradeSchema: Schema[com.agoda.content.models.db.reviews.ReviewScoreGrade] =
    Schema.derived.name(Schema.SName("com.agoda.content.models.db.reviews.ContentReviewScoreGrade"))

  implicit lazy val ReviewRatingDistributionSchema
      : Schema[com.agoda.content.models.db.reviews.ReviewRatingDistribution] =
    Schema.derived

  implicit lazy val ReviewGroupScoreSchema: Schema[com.agoda.content.models.db.reviews.ReviewGroupScore] =
    Schema.derived

  implicit lazy val ReviewScoreRoomGradeSchema: Schema[com.agoda.content.models.db.reviews.ReviewScoreRoomGrade] =
    Schema.derived

  implicit lazy val ReviewRoomGroupScoreSchema: Schema[com.agoda.content.models.db.reviews.ReviewRoomGroupScore] =
    Schema.derived

  implicit lazy val ReviewScoresSchema: Schema[com.agoda.content.models.db.reviews.ReviewScores] = Schema.derived

  implicit lazy val ReviewSnippetSchema: Schema[com.agoda.content.models.db.reviews.ReviewSnippet] = Schema.derived

  implicit lazy val RecommendationScoreSchema: Schema[com.agoda.content.models.db.reviews.RecommendationScore] =
    Schema.derived

  implicit lazy val ReviewTopSnippetTopicSchema: Schema[com.agoda.content.models.db.reviews.ReviewTopSnippetTopic] =
    Schema.derived

  implicit lazy val TopSnippetPerTopicSchema: Schema[com.agoda.content.models.db.reviews.TopSnippetPerTopic] =
    Schema.derived

  implicit lazy val ReviewSnippetTopicResponseSchema
      : Schema[com.agoda.content.models.db.reviews.ReviewSnippetTopicResponse] =
    Schema.derived

  implicit lazy val ReviewSummaryFromAiSchema: Schema[com.agoda.content.models.db.reviews.ReviewSummaryFromAi] =
    Schema.derived

  implicit lazy val ReviewTagSchema: Schema[com.agoda.content.models.db.reviews.ReviewTag] = Schema.derived

  implicit lazy val ReviewSnippetsSchema: Schema[com.agoda.content.models.db.reviews.ReviewSnippets] = Schema.derived

  implicit lazy val ReviewTotalSchema: Schema[com.agoda.content.models.db.reviews.ReviewTotal] = Schema.derived

  implicit lazy val ReviewScorePerProviderSchema: Schema[com.agoda.content.models.db.reviews.ReviewScorePerProvider] =
    Schema.derived

  implicit lazy val TrendingReviewScoreSchema: Schema[com.agoda.content.models.db.reviews.TrendingReviewScore] =
    Schema.derived

  implicit lazy val BcomReviewScoreSchema: Schema[com.agoda.content.models.db.reviews.BcomReviewScore] = Schema.derived

  implicit lazy val CombinedReviewScoreResponseSchema
      : Schema[com.agoda.content.models.db.reviews.CombinedReviewScoreResponse] =
    Schema.derived

  implicit lazy val WordCloudSchema: Schema[com.agoda.content.models.db.reviews.WordCloud] = Schema.derived

  implicit lazy val WordCloudPerProviderSchema: Schema[com.agoda.content.models.db.reviews.WordCloudPerProvider] =
    Schema.derived

  implicit lazy val FacilityClassWordsCloudSchema: Schema[com.agoda.content.models.db.reviews.FacilityClassWordsCloud] =
    Schema.derived

  implicit lazy val ThirdPartyReviewResponseSchema
      : Schema[com.agoda.content.models.db.reviews.ThirdPartyReviewResponse] =
    Schema.derived

  implicit lazy val HostReviewAggregationSchema: Schema[com.agoda.content.models.db.reviews.HostReviewAggregation] =
    Schema.derived

  implicit lazy val ReviewTotalsSchema: Schema[com.agoda.content.models.db.reviews.ReviewTotals] = Schema.derived

  implicit lazy val AvailabilityFiltersNoProviderSchema
      : Schema[com.agoda.content.models.db.reviews.AvailabilityFiltersNoProvider] =
    Schema.derived

  implicit lazy val AvailableSchema: Schema[com.agoda.content.models.db.reviews.Available] = Schema.derived

  implicit lazy val ReviewFiltersAvailabilitySchema
      : Schema[com.agoda.content.models.db.reviews.ReviewFiltersAvailability] =
    Schema.derived

  implicit lazy val ReviewPositiveMentionCategorySchema
      : Schema[com.agoda.content.models.db.positiveMentions.ReviewPositiveMentionCategory] =
    Schema.derived

  implicit lazy val ReviewFacilityClassMentionsSchema
      : Schema[com.agoda.content.models.db.positiveMentions.ReviewFacilityClassMentions] =
    Schema.derived

  implicit lazy val ReviewSentimentSchema: Schema[com.agoda.content.models.db.positiveMentions.ReviewSentiment] =
    Schema.derived

  implicit lazy val ReviewPositiveMentionsSchema
      : Schema[com.agoda.content.models.db.positiveMentions.ReviewPositiveMentions] =
    Schema.derived

  implicit lazy val ReviewFacilityCounterSchema: Schema[com.agoda.content.models.db.reviews.ReviewFacilityCounter] =
    Schema.derived

  implicit lazy val ReviewCountersSchema: Schema[com.agoda.content.models.db.reviews.ReviewCounters] = Schema.derived

  implicit lazy val ReviewResponseSchema: Schema[com.agoda.content.models.db.reviews.ReviewResponse] = Schema.derived

  implicit lazy val ImageCategorySchema: Schema[com.agoda.content.models.db.image.ImageCategory] = Schema.derived

  implicit lazy val VideoResponseSchema: Schema[com.agoda.content.models.db.image.VideoResponse] = Schema.derived

  implicit lazy val MatterPortResponseSchema: Schema[com.agoda.content.models.db.image.MatterPortResponse] =
    Schema.derived

  implicit lazy val ImagesResponseSchema: Schema[com.agoda.content.models.db.image.ImagesResponse] = Schema.derived

  implicit lazy val HighlightItemSchema: Schema[com.agoda.content.models.db.highlights.HighlightItem] = Schema.derived

  implicit lazy val HighlightInformationSchema: Schema[com.agoda.content.models.db.highlights.HighlightInformation] =
    Schema.derived

  implicit lazy val HighlightSchema: Schema[com.agoda.content.models.db.highlights.Highlight] = Schema.derived

  implicit lazy val FavoriteFeaturesSchema: Schema[com.agoda.content.models.db.highlights.FavoriteFeatures] =
    Schema.derived

  implicit lazy val HighlightCategorySchema: Schema[com.agoda.content.models.db.highlights.HighlightCategory] =
    Schema.derived

  implicit lazy val HighlightDistanceInformationSchema
      : Schema[com.agoda.content.models.db.highlights.HighlightDistanceInformation] =
    Schema.derived

  implicit lazy val CityCenterInformationSchema: Schema[com.agoda.content.models.db.highlights.CityCenterInformation] =
    Schema.derived

  implicit lazy val LuxuryUniqueToPropertySchema
      : Schema[com.agoda.content.models.db.highlights.LuxuryUniqueToProperty] =
    Schema.derived

  implicit lazy val LuxuryFacilityServiceSchema: Schema[com.agoda.content.models.db.highlights.LuxuryFacilityService] =
    Schema.derived

  implicit lazy val LuxuryHighlightSchema: Schema[com.agoda.content.models.db.highlights.LuxuryHighlight] =
    Schema.derived

  implicit lazy val HighlightsSchema: Schema[com.agoda.content.models.db.highlights.Highlights] = Schema.derived

  implicit lazy val PropertyCollectionSchema: Schema[com.agoda.content.models.db.highlights.PropertyCollection] =
    Schema.derived

  implicit lazy val NHAHighlightItemSchema: Schema[com.agoda.content.models.db.nonHotelAccommodation.NHAHighlightItem] =
    Schema.derived

  implicit lazy val NHAHostLevelSchema: Schema[com.agoda.content.models.db.nonHotelAccommodation.NHAHostLevel] =
    Schema.derived

  implicit lazy val ChildRoomSchema: Schema[com.agoda.content.models.db.nonHotelAccommodation.ChildRoom] =
    Schema.derived

  implicit lazy val MasterRoomSchema: Schema[com.agoda.content.models.db.nonHotelAccommodation.MasterRoom] =
    Schema.derived

  implicit lazy val NonHotelAccommodationSchema
      : Schema[com.agoda.content.models.db.nonHotelAccommodation.NonHotelAccommodation] =
    Schema.derived

  implicit lazy val DisplayPriceSchema: Schema[models.starfruit.DisplayPrice] =
    Schema.derived.name(Schema.SName("models.starfruit.DfDisplayPrice"))

  implicit lazy val DisplayBasisSchema: Schema[models.starfruit.DisplayBasis] =
    Schema.derived.name(Schema.SName("models.starfruit.DfDisplayBasis"))

  implicit lazy val CmsDataGroupTypeSchema: Schema[transformers.CmsDataGroupType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("transformers.CmsDataGroupType")),
      validator = Validator.enumeration(
        transformers.CmsDataGroupTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CmsFlagDataSchema: Schema[transformers.CmsFlagData] = Schema.derived

  implicit lazy val LoyaltyItemTypeSchema: Schema[models.pricing.LoyaltyItemType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.LoyaltyItemType")),
      validator = Validator.enumeration(
        models.pricing.LoyaltyItem.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val LoyaltyDisplaySchema: Schema[models.starfruit.LoyaltyDisplay] = Schema.derived

  implicit lazy val EnrichedPackagingTokenSchema: Schema[transformers.EnrichedPackagingToken] = Schema.derived

  implicit lazy val PackageDisplayPriceSchema: Schema[models.starfruit.PackageDisplayPrice] = Schema.derived

  implicit lazy val PackageDisplayBasisSchema: Schema[models.starfruit.PackageDisplayBasis] = Schema.derived

  implicit lazy val PackageOriginalPriceAndSavingSchema: Schema[models.starfruit.PackageOriginalPriceAndSaving] =
    Schema.derived

  implicit lazy val PackageOptionalRebateTypePackageDisplayBasisMapSchema
      : Schema[Map[models.starfruit.PackageOptionalRebateType, models.starfruit.PackageDisplayBasis]] =
    Schema.schemaForMap[models.starfruit.PackageOptionalRebateType, models.starfruit.PackageDisplayBasis](_.toString)

  implicit lazy val PackageRebateSchema: Schema[models.starfruit.PackageRebate] = Schema.derived

  implicit lazy val PackageProductTypeSchema: Schema[models.pricing.enums.PackageProductType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.PackageProductType")),
      validator = Validator.enumeration(
        models.pricing.enums.PackageProductTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PackageSavingSchema: Schema[models.starfruit.PackageSaving] = Schema.derived

  implicit lazy val PackageDetailedSavingSchema: Schema[models.starfruit.PackageDetailedSaving] = Schema.derived

  implicit lazy val EnrichedPackageProductDisplayPriceSchema: Schema[transformers.EnrichedPackageProductDisplayPrice] =
    Schema.derived

  implicit lazy val EnrichedPackagingPricingSchema: Schema[transformers.EnrichedPackagingPricing] = Schema.derived

  implicit lazy val EnrichedPackagingSchema: Schema[transformers.EnrichedPackaging] = Schema.derived

  implicit lazy val EnrichedCancellationSchema: Schema[transformers.EnrichedCancellation] = Schema.derived

  implicit lazy val EnrichedTaxReceiptDetailSchema: Schema[transformers.EnrichedTaxReceiptDetail] = Schema.derived

  implicit lazy val EnrichedTaxReceiptSchema: Schema[transformers.EnrichedTaxReceipt] = Schema.derived

  implicit lazy val EnrichedPayLaterSchema: Schema[transformers.EnrichedPayLater] = Schema.derived

  implicit lazy val EnrichedNoCreditCardSchema: Schema[transformers.EnrichedNoCreditCard] = Schema.derived

  implicit lazy val EnrichedPayAtHotelSchema: Schema[transformers.EnrichedPayAtHotel] = Schema.derived

  implicit lazy val EnrichedPayAtCheckInSchema: Schema[transformers.EnrichedPayAtCheckIn] = Schema.derived

  implicit lazy val M150InfoSchema: Schema[transformers.M150Info] = Schema.derived

  implicit lazy val EnrichedNoPrePaymentRequiredSchema: Schema[transformers.EnrichedNoPrePaymentRequired] =
    Schema.derived

  implicit lazy val PaymentDisplayPriceSchema: Schema[com.agoda.payment.model.DisplayPrice] = Schema.derived

  implicit lazy val IntegerSchema: Schema[Integer] = Schema(SchemaType.SInteger())

  implicit lazy val EnrichedInstallmentPlanSchema: Schema[com.agoda.payment.model.EnrichedInstallmentPlan] =
    Schema.derived

  implicit lazy val InstallmentDetailsSchema: Schema[com.agoda.payment.model.InstallmentDetails] = Schema.derived

  implicit lazy val EnrichedPaymentSchema: Schema[transformers.EnrichedPayment] = Schema.derived

  implicit lazy val ZenithCheapestRoomSchema: Schema[transformers.ZenithCheapestRoom] = Schema.derived

  implicit lazy val RecomendationMseResultsSchema: Schema[transformers.RecomendationMseResults] = Schema.derived

  implicit lazy val intStringMapSchema: Schema[Map[Int, String]] = Schema.schemaForMap[Int, String](_.toString)

  implicit lazy val intJavaDateMapSchema: Schema[Map[Int, java.util.Date]] =
    Schema.schemaForMap[Int, java.util.Date](_.toString)

  implicit lazy val PropertyEngagementSchema: Schema[com.agoda.content.models.db.information.PropertyEngagement] =
    Schema.derived

  implicit lazy val BookingHistorySchema: Schema[transformers.BookingHistory] = Schema.derived

  implicit lazy val TopSellingPointSchema: Schema[transformers.TopSellingPoint] = Schema.derived

  implicit lazy val MatchScoreSchema: Schema[transformers.MatchScore] = Schema.derived

  implicit lazy val ZenithResponseEnrichedSchema: Schema[transformers.ZenithResponseEnriched] = Schema.derived

  implicit lazy val RecommendationCorrelationSchema: Schema[transformers.RecommendationCorrelation] = Schema.derived

  implicit lazy val CmsDataSchema: Schema[transformers.CmsData] = Schema.derived

  implicit lazy val RecommendationHeaderSchema: Schema[transformers.RecommendationHeader] = Schema.derived

  implicit lazy val PropertyTooltipSchema: Schema[transformers.PropertyTooltip] = Schema.derived

  implicit lazy val FilterTagSchema: Schema[transformers.FilterTag] = Schema.derived

  implicit lazy val FilterGroupSchema: Schema[transformers.FilterGroup] = Schema.derived

  implicit lazy val PropertyFiltersSchema: Schema[transformers.PropertyFilters] = Schema.derived

  implicit lazy val BedSummaryResponseSchema: Schema[com.agoda.content.models.db.information.BedSummaryResponse] =
    Schema.derived

  implicit lazy val BedResponseSchema: Schema[com.agoda.content.models.db.information.BedResponse] = Schema.derived

  implicit lazy val BedOptionResponseSchema: Schema[com.agoda.content.models.db.information.BedOptionResponse] =
    Schema.derived

  implicit lazy val BedroomConfigurationResponseSchema
      : Schema[com.agoda.content.models.db.information.BedroomConfigurationResponse] =
    Schema.derived

  implicit lazy val BedDetailResponseSchema: Schema[com.agoda.content.models.db.information.BedDetailResponse] =
    Schema.derived

  implicit lazy val BedConfigurationResponseSchema
      : Schema[com.agoda.content.models.db.information.BedConfigurationResponse] =
    Schema.derived

  implicit lazy val RatePlanMainImageSchema: Schema[transformers.RatePlanMainImage] = Schema.derived

  implicit lazy val RateCategoryCheckTimeSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryCheckTime] =
    Schema.derived

  implicit lazy val EnrichedRatePlanSchema: Schema[transformers.EnrichedRatePlan] = Schema.derived

  implicit lazy val BenefitUnitSchema: Schema[models.pricing.enums.BenefitUnit] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.BenefitUnit")),
      validator = Validator.enumeration(
        models.pricing.enums.BenefitUnits.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val BenefitTargetTypeSchema: Schema[com.agoda.papi.enums.room.BenefitTargetType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.BenefitTargetType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.BenefitTargetTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val StructuredBenefitInfoSchema: Schema[models.starfruit.StructuredBenefitInfo] = Schema.derived

  implicit lazy val EnrichedBenefitSchema: Schema[transformers.EnrichedBenefit] = Schema.derived

  implicit lazy val EnrichedChannelSchema: Schema[transformers.EnrichedChannel] = Schema.derived

  implicit lazy val RoomAllocationInfoSchema: Schema[models.starfruit.RoomAllocationInfo] = Schema.derived

  implicit lazy val EnrichedCapacitySchema: Schema[transformers.EnrichedCapacity] = Schema.derived

  implicit lazy val TimeIntervalSchema: Schema[models.starfruit.TimeInterval] = Schema.derived

  implicit lazy val ChargeTypeSchema: Schema[models.pricing.enums.ChargeType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.ChargeType")),
      validator = Validator.enumeration(
        models.pricing.enums.ChargeTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PayTypeSchema: Schema[models.starfruit.PayType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.PayType")),
      validator = Validator.enumeration(
        models.starfruit.PayTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PriceSchema: Schema[models.starfruit.Price] = Schema.derived

  implicit lazy val ChargeOptionSchema: Schema[models.pricing.enums.ChargeOption] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.ChargeOption")),
      validator = Validator.enumeration(
        models.pricing.enums.ChargeOptions.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val WhomToPayTypeSchema: Schema[com.agoda.papi.enums.room.WhomToPayType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.WhomToPayType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.WhomToPayType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val TaxBreakDownSchema: Schema[models.pricing.TaxBreakDown] = Schema.derived

  implicit lazy val ProcessingFeeBreakdownSchema: Schema[models.pricing.ProcessingFeeBreakdown] = Schema.derived

  implicit lazy val PartnersPriceBreakdownSchema: Schema[models.starfruit.PartnersPriceBreakdown] = Schema.derived

  implicit lazy val SubChargeTypeSchema: Schema[com.agoda.papi.enums.room.SubChargeType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.SubChargeType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.SubChargeType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val DailySchema: Schema[models.starfruit.Daily] = Schema.derived

  implicit lazy val DisplaySchema: Schema[models.starfruit.Display] = Schema.derived

  implicit lazy val EnrichedChargeBreakdownSchema: Schema[transformers.EnrichedChargeBreakdown] = Schema.derived

  implicit lazy val EnrichedChargeSchema: Schema[transformers.EnrichedCharge] = Schema.derived

  implicit lazy val ExtraInformationSchema: Schema[models.starfruit.ExtraInformation] = Schema.derived

  implicit lazy val PointsRangeSchema: Schema[models.starfruit.PointsRange] = Schema.derived

  implicit lazy val LoyaltyBurnInfoSchema: Schema[models.starfruit.LoyaltyBurnInfo] = Schema.derived

  implicit lazy val LoyaltyEarnInfoSchema: Schema[models.starfruit.LoyaltyEarnInfo]             = Schema.derived
  implicit lazy val BenefitOfferBreakdownSchema: Schema[models.starfruit.BenefitOfferBreakdown] = Schema.derived
  implicit lazy val BenefitDetailsSchema: Schema[models.starfruit.BenefitDetails]               = Schema.derived
  implicit lazy val PointDetailsSchema: Schema[models.starfruit.PointDetails]                   = Schema.derived
  implicit lazy val AccrualInfoSchema: Schema[models.starfruit.AccrualInfo]                     = Schema.derived
  implicit lazy val AccrualPolicySchema: Schema[models.starfruit.AccrualPolicy]                 = Schema.derived

  implicit lazy val LoyaltyPromotionBenefitTypeSchema: Schema[com.agoda.papi.enums.room.LoyaltyPromotionBenefitType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.LoyaltyPromotionBenefitType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.LoyaltyPromotionBenefitType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val LoyaltyPromotionBenefitCategorySchema
      : Schema[com.agoda.papi.enums.room.LoyaltyPromotionBenefitCategory] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.LoyaltyPromotionBenefitCategory")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.LoyaltyPromotionBenefitCategory.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val AccrualPolicyStartFromSchema: Schema[com.agoda.papi.enums.room.AccrualPolicyStartFrom] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.AccrualPolicyStartFrom")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.AccrualPolicyStartFrom.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val LoyaltyOfferSchema: Schema[models.starfruit.LoyaltyOffer] = Schema.derived

  implicit lazy val LoyaltyOfferSummarySchema: Schema[models.starfruit.LoyaltyOfferSummary] = Schema.derived

  implicit lazy val SummaryElementSchema: Schema[models.starfruit.SummaryElement] = Schema.derived

  implicit lazy val DisplaySummarySchema: Schema[models.starfruit.DisplaySummary] = Schema.derived

  implicit lazy val AffiliateSellRateSchema: Schema[models.starfruit.AffiliateSellRate] = Schema.derived

  implicit lazy val AffiliatePricesSchema: Schema[models.starfruit.AffiliatePrices] = Schema.derived

  implicit lazy val PartnersDailyRateSchema: Schema[models.starfruit.PartnersDailyRate] = Schema.derived

  implicit lazy val EnrichedPartnersRoomAndExtraBedChargeSchema
      : Schema[transformers.EnrichedPartnersRoomAndExtraBedCharge] =
    Schema.derived

  implicit lazy val EnrichedPartnersPriceSchema: Schema[transformers.EnrichedPartnersPrice] = Schema.derived

  implicit lazy val EnrichedPartnersSurchargeSchema: Schema[transformers.EnrichedPartnersSurcharge] = Schema.derived

  implicit lazy val EnrichedPartnerSurchargesSchema: Schema[transformers.EnrichedPartnerSurcharges] = Schema.derived

  implicit lazy val EnrichedPartnerPromotionSavingsSchema: Schema[transformers.EnrichedPartnerPromotionSavings] =
    Schema.derived

  implicit lazy val PartnerCancellationPolicySchema: Schema[models.starfruit.PartnerCancellationPolicy] =
    Schema.derived

  implicit lazy val EnrichedPartnerCancellationSchema: Schema[transformers.EnrichedPartnerCancellation] = Schema.derived

  implicit lazy val EnrichedPartnerTaxAndFeeBreakdownSchema: Schema[transformers.EnrichedPartnerTaxAndFeeBreakdown] =
    Schema.derived

  implicit lazy val EnrichedPartnerTaxAndFeeSchema: Schema[transformers.EnrichedPartnerTaxAndFee] = Schema.derived

  implicit lazy val EnrichedPartnersSchema: Schema[transformers.EnrichedPartners] = Schema.derived

  implicit lazy val EnrichedDiscountMessageSchema: Schema[transformers.EnrichedDiscountMessage] = Schema.derived

  implicit lazy val NetDisplayBasisSchema: Schema[models.starfruit.NetDisplayBasis] = Schema.derived

  implicit lazy val APSPeekSchema: Schema[models.starfruit.APSPeek] = Schema.derived

  implicit lazy val PromotionCodeTypeSchema: Schema[com.agoda.papi.enums.campaign.PromotionCodeType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.PromotionCodeType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.PromotionCodeType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PromotionPricePeekSchema: Schema[models.starfruit.PromotionPricePeek] = Schema.derived

  implicit lazy val ConsolidatedAppliedDiscountSchema: Schema[models.starfruit.ConsolidatedAppliedDiscount] =
    Schema.derived

  implicit lazy val ConsolidatedAppliedDiscountBreakdownItemSchema
      : Schema[models.starfruit.ConsolidatedAppliedDiscountBreakdownItem] = Schema.derived

  implicit lazy val RocketmilesSellPriceSchema: Schema[models.starfruit.RocketmilesSellPrice] = Schema.derived

  implicit lazy val RocketmilesForAffiliateSchema: Schema[models.starfruit.RocketmilesForAffiliate] = Schema.derived

  implicit lazy val RocketmilesBcomDetailsSchema: Schema[models.starfruit.RocketmilesBcomDetails] = Schema.derived

  implicit lazy val RocketmilesPriceSchema: Schema[models.starfruit.RocketmilesPrice] = Schema.derived

  implicit lazy val RocketmilesPricingSchema: Schema[models.starfruit.RocketmilesPricing] = Schema.derived

  implicit lazy val AdditionalPriceDisplayBasisSchema: Schema[transformers.AdditionalPriceDisplayBasis] = Schema.derived

  implicit lazy val PriceWithChannelSchema: Schema[transformers.PriceWithChannel] = Schema.derived

  implicit lazy val DisplayPricePercentageSchema: Schema[models.starfruit.DisplayPricePercentage] = Schema.derived

  implicit lazy val PackagePriceAndSavingSchema: Schema[transformers.PackagePriceAndSaving] = Schema.derived

  implicit lazy val SimplePriceBasisSchema: Schema[models.starfruit.SimplePriceBasis] = Schema.derived

  implicit lazy val RoomChannelDiscountBreakdownSchema: Schema[models.starfruit.RoomChannelDiscountBreakdown] =
    Schema.derived

  implicit lazy val ChannelDiscountSummarySchema: Schema[models.starfruit.ChannelDiscountSummary] = Schema.derived

  implicit lazy val AppliedDateDiscountSchema: Schema[models.starfruit.AppliedDateDiscount] = Schema.derived

  implicit lazy val ChannelDiscountInfoSchema: Schema[models.starfruit.ChannelDiscountInfo] = Schema.derived

  implicit lazy val PerBookDifferenceSchema: Schema[models.starfruit.PerBookDifference] = Schema.derived

  implicit lazy val PriceDifferenceSchema: Schema[models.starfruit.PriceDifference] = Schema.derived

  implicit lazy val PromotionCumulativeSchema: Schema[models.starfruit.PromotionCumulative] = Schema.derived

  implicit lazy val LongStaySchema: Schema[models.starfruit.LongStay] = Schema.derived

  implicit lazy val LoyaltyDisplayPriceSchema: Schema[models.starfruit.LoyaltyDisplayPrice] = Schema.derived

  implicit lazy val LoyaltyRewardUnitSchema: Schema[models.starfruit.LoyaltyRewardUnit] = Schema.derived

  implicit lazy val ExternalLoyaltyPricingSchema: Schema[models.starfruit.ExternalLoyaltyPricing] = Schema.derived

  implicit lazy val GoToTravelInfoSchema: Schema[models.starfruit.GoToTravelInfo] = Schema.derived

  implicit lazy val PriceBreakdownOccupancySchema: Schema[models.starfruit.PriceBreakdownOccupancy] = Schema.derived

  implicit lazy val PriceBreakdownPerOccupancyLineItemSchema
      : Schema[models.starfruit.PriceBreakdownPerOccupancyLineItem] =
    Schema.derived

  implicit lazy val AgeRangeSchema: Schema[models.starfruit.AgeRange] =
    Schema.derived.name(Schema.SName("models.starfruit.DfAgeRange"))

  implicit lazy val PriceBreakdownRateTypeSchema: Schema[models.starfruit.PriceBreakdownRateType] = Schema.derived

  implicit lazy val PriceBreakdownPerOccupancySchema: Schema[models.starfruit.PriceBreakdownPerOccupancy] =
    Schema.derived

  implicit lazy val EnrichedPricingSchema: Schema[transformers.EnrichedPricing] = Schema.derived

  implicit lazy val DiscountSchema: Schema[models.starfruit.Discount] = Schema.derived

  implicit lazy val AppliedPromotionSchema: Schema[transformers.AppliedPromotion] = Schema.derived

  implicit lazy val EnrichedPromotionSchema: Schema[transformers.EnrichedPromotion] = Schema.derived

  implicit lazy val EnrichedPointMaxSchema: Schema[transformers.EnrichedPointMax] = Schema.derived

  implicit lazy val EnrichedDMCPolicyTextSchema: Schema[transformers.EnrichedDMCPolicyText] = Schema.derived

  implicit lazy val RateModelSchema: Schema[models.starfruit.RateModel] = Schema.derived

  implicit lazy val dfEnrichedRatePlanSchema: Schema[models.starfruit.EnrichedRatePlan] =
    Schema.derived.name(Schema.SName("models.starfruit.DfEnrichedRatePlan"))

  implicit lazy val RatePlanSchema: Schema[models.starfruit.RatePlan] = Schema.derived

  implicit lazy val DfBenefitSchema: Schema[models.starfruit.Benefit] =
    Schema.derived.name(Schema.SName("models.starfruit.DfBenefit"))

  implicit lazy val ChannelSchema: Schema[models.starfruit.Channel] = Schema.derived

  implicit lazy val CapacitySchema: Schema[models.starfruit.Capacity] = Schema.derived

  implicit lazy val ChargeGroupSchema: Schema[models.pricing.enums.ChargeGroup] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.ChargeGroup")),
      validator = Validator.enumeration(
        models.pricing.enums.ChargeGroups.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ChargeSchema: Schema[models.starfruit.Charge] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCharge"))

  implicit lazy val DisplayChargeSchema: Schema[models.starfruit.DisplayCharge] = Schema.derived

  implicit lazy val CorTypeFlagSchema: Schema[models.pricing.enums.CorTypeFlag] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.CorTypeFlag")),
      validator = Validator.enumeration(
        models.pricing.enums.CorTypeFlags.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PartnersChargeSchema: Schema[models.starfruit.PartnersCharge] = Schema.derived

  implicit lazy val RateApplyTypeSchema: Schema[models.pricing.enums.RateApplyType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RateApplyType")),
      validator = Validator.enumeration(
        models.pricing.enums.RateApplyTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PartnersDisplayPriceSchema: Schema[models.starfruit.PartnersDisplayPrice] = Schema.derived

  implicit lazy val PartnersPricesSchema: Schema[models.starfruit.PartnersPrices] = Schema.derived

  implicit lazy val PartnersSurchargesSchema: Schema[models.starfruit.PartnersSurcharges] = Schema.derived

  implicit lazy val PartnersPromotionSavingsSchema: Schema[models.starfruit.PartnersPromotionSavings] = Schema.derived

  implicit lazy val PartnerCancellationSchema: Schema[models.starfruit.PartnerCancellation] = Schema.derived

  implicit lazy val PartnerTaxBreakdownSchema: Schema[models.starfruit.PartnerTaxBreakdown] = Schema.derived

  implicit lazy val PartnersSchema: Schema[models.starfruit.Partners] = Schema.derived

  implicit lazy val DiscountMessageSchema: Schema[models.starfruit.DiscountMessage] =
    Schema.derived.name(Schema.SName("models.starfruit.DfDiscountMessage"))

  implicit lazy val ChannelIdDisplayBasisMapSchema: Schema[Map[models.db.ChannelId, models.starfruit.DisplayBasis]] =
    Schema.schemaForMap[models.db.ChannelId, models.starfruit.DisplayBasis](_.toString)

  implicit lazy val DfAdditionalPriceDisplayBasisSchema: Schema[models.starfruit.AdditionalPriceDisplayBasis] =
    Schema.derived.name(Schema.SName("models.starfruit.DfAdditionalPriceDisplayBasis"))

  implicit lazy val DfPriceWithChannelSchema: Schema[models.starfruit.PriceWithChannel] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPriceWithChannel"))

  implicit lazy val DfPackagePriceAndSavingSchema: Schema[models.starfruit.PackagePriceAndSaving] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPackagePriceAndSaving"))

  implicit lazy val IneligiblePromotionReasonSchema: Schema[com.agoda.papi.enums.campaign.IneligiblePromotionReason] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.IneligiblePromotionReason")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.InelegiblePromotionReasons.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PromotionCapSchema: Schema[models.starfruit.PromotionCap] = Schema.derived

  implicit lazy val PromotionResultInfoSchema: Schema[models.starfruit.PromotionResultInfo] = Schema.derived

  implicit lazy val PromotionInfoMessageSchema: Schema[models.starfruit.PromotionInfoMessage] = Schema.derived

  implicit lazy val PromotionTypeSchema: Schema[com.agoda.papi.enums.campaign.PromotionType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.PromotionType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.PromotionTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CampaignValidDateTypeSchema: Schema[com.agoda.papi.enums.campaign.CampaignValidDateType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.CampaignValidDateType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.CampaignValidDateType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CampaignSchema: Schema[models.starfruit.Campaign] = Schema.derived

  implicit lazy val CidCampaignsSchema: Schema[models.starfruit.CidCampaigns] = Schema.derived

  implicit lazy val DispatchAvailabilityTypeSchema: Schema[models.enums.DispatchAvailabilityType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.enums.DispatchAvailabilityType")),
      validator = Validator.enumeration(
        models.enums.DispatchAvailabilityTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RatePlanStatusSchema: Schema[models.pricing.enums.RatePlanStatus] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RatePlanStatus")),
      validator = Validator.enumeration(
        models.pricing.enums.RatePlanStatuses.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val AdditionalRateSchema: Schema[models.starfruit.AdditionalRate] =
    Schema.derived.name(Schema.SName("models.starfruit.DfAdditionalRate"))

  implicit lazy val PricingSchema: Schema[models.starfruit.Pricing] = Schema.derived

  implicit lazy val PackagingResponseTokenSchema: Schema[models.starfruit.PackagingResponseToken] = Schema.derived

  implicit lazy val PackageProductDisplayPriceSchema: Schema[models.starfruit.PackageProductDisplayPrice] =
    Schema.derived

  implicit lazy val PackagePricingSchema: Schema[models.starfruit.PackagePricing] = Schema.derived

  implicit lazy val DfPackagingSchema: Schema[models.starfruit.Packaging] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPackaging"))

  implicit lazy val DfEnrichedPromotionSchema: Schema[models.starfruit.EnrichedPromotion] =
    Schema.derived.name(Schema.SName("models.starfruit.DfEnrichedPromotion"))

  implicit lazy val PromotionSchema: Schema[models.starfruit.Promotion] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPromotion"))

  implicit lazy val LocalDatePromotionMapSchema
      : Schema[Map[org.joda.time.LocalDate, List[models.starfruit.Promotion]]] =
    Schema.schemaForMap[org.joda.time.LocalDate, List[models.starfruit.Promotion]](_.toString)

  implicit lazy val PointsMaxSchema: Schema[models.starfruit.PointsMax] = Schema.derived

  implicit lazy val TaxReceiptTypeSchema: Schema[models.starfruit.TaxReceiptType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.TaxReceiptType")),
      validator = Validator.enumeration(
        models.starfruit.TaxReceiptType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val TaxReceiptDetailSchema: Schema[models.starfruit.TaxReceiptDetail] = Schema.derived

  implicit lazy val TaxReceiptSchema: Schema[models.starfruit.TaxReceipt] = Schema.derived

  implicit lazy val PayLaterSchema: Schema[models.starfruit.PayLater] = Schema.derived

  implicit lazy val CancellationChargeSettingTypeSchema
      : Schema[com.agoda.papi.enums.room.CancellationChargeSettingType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.CancellationChargeSettingType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.CancellationChargeSettingType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CancellationFeeTypeSchema: Schema[com.agoda.papi.enums.room.CancellationFeeType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.RoomCancellationFeeType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.CancellationFeeType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val CancellationFeeSchema: Schema[models.CancellationFee] = Schema.derived

  implicit lazy val PolicyPhaseSchema: Schema[models.starfruit.PolicyPhase] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPolicyPhase"))

  implicit lazy val CancellationPoliciesResultEntrySchema: Schema[models.starfruit.CancellationPoliciesResultEntry] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCancellationPoliciesResultEntry"))

  implicit lazy val CancellationPoliciesResultSchema: Schema[models.starfruit.CancellationPoliciesResult] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCancellationPoliciesResult"))

  implicit lazy val CancellationSchema: Schema[models.starfruit.Cancellation] = Schema.derived

  implicit lazy val WaiverSchema: Schema[models.starfruit.Waiver] = Schema.derived

  implicit lazy val CreditCardSchema: Schema[models.starfruit.CreditCard] = Schema.derived

  implicit lazy val PrePaymentSchema: Schema[models.starfruit.PrePayment] = Schema.derived

  implicit lazy val PayAtHotelSchema: Schema[models.starfruit.PayAtHotel] = Schema.derived

  implicit lazy val LoyaltyReasonSchema: Schema[models.starfruit.LoyaltyReason] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.LoyaltyReason")),
      validator = Validator.enumeration(
        models.starfruit.LoyaltyReasons.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val LoyaltyPaymentBoundariesSchema: Schema[models.starfruit.LoyaltyPaymentBoundaries] = Schema.derived

  implicit lazy val NoPrePaymentRequiredSchema: Schema[models.starfruit.NoPrePaymentRequired] = Schema.derived

  implicit lazy val PayAtCheckInSchema: Schema[models.starfruit.PayAtCheckIn] = Schema.derived

  implicit lazy val NoCreditCardRequiredSchema: Schema[models.starfruit.NoCreditCardRequired] = Schema.derived

  implicit lazy val PaymentSchema: Schema[models.starfruit.Payment] = Schema.derived

  implicit lazy val DmcTaxSchema: Schema[models.pricing.DmcTax] = Schema.derived
  implicit lazy val DmcTaxMapSchema: Schema[Map[Int, models.pricing.DmcTax]] =
    Schema.schemaForMap[Int, models.pricing.DmcTax](_.toString)

  implicit lazy val DmcDataSchema: Schema[models.pricing.DmcData] = Schema.derived

  implicit lazy val GiftCardSchema: Schema[models.starfruit.GiftCard] =
    Schema.derived.name(Schema.SName("models.starfruit.DfGiftCard"))

  implicit lazy val PseudoCouponSchema: Schema[models.starfruit.PseudoCoupon] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPseudoCoupon"))

  implicit lazy val RepurposeDiscountMethodSchema: Schema[models.pricing.enums.RepurposeDiscountMethod] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RepurposeDiscountMethod")),
      validator = Validator.enumeration(
        models.pricing.enums.RepurposeDiscountMethod.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RateRepurposeInfoSchema: Schema[models.pricing.RateRepurposeInfo] = Schema.derived

  implicit lazy val AccountingEntitySchema: Schema[models.pricing.AccountingEntity] = Schema.derived

  implicit lazy val BenefitParameterSchema: Schema[models.pricing.BenefitParameter] = Schema.derived

  implicit lazy val BenefitValueSchema: Schema[com.agoda.papi.ypl.models.hotel.BenefitValue] = Schema.derived

  implicit lazy val BenefitSchema: Schema[models.pricing.Benefit] =
    Schema.derived.name(Schema.SName("models.pricing.DfPriceBenefit"))

  implicit lazy val RateCategorySchema: Schema[models.pricing.RateCategory] = Schema.derived

  implicit lazy val FireDrillContractTypeSchema: Schema[com.agoda.papi.enums.hotel.FireDrillContractType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.hotel.FireDrillContractType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.hotel.FireDrillContractType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val FireDrillContractSchema: Schema[models.pricing.FireDrillContract] = Schema.derived

  implicit lazy val ExchangeRateSchema: Schema[models.pricing.ExchangeRate] = Schema.derived

  implicit lazy val BookingRateTypeSchema: Schema[models.pricing.enums.BookingRateType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.BookingRateType")),
      validator = Validator.enumeration(
        models.pricing.enums.BookingRateTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val BookingItemTypeSchema: Schema[models.pricing.enums.BookingItemType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.BookingItemType")),
      validator = Validator.enumeration(
        models.pricing.enums.BookingItemTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ItemBreakdownSchema: Schema[models.starfruit.ItemBreakdown] = Schema.derived

  implicit lazy val SurchargeTypeClassificationSchema: Schema[com.agoda.papi.enums.room.SurchargeTypeClassification] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.SurchargeTypeClassification")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.SurchargeTypeClassification.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PriusOutputSchema: Schema[models.db.PriusOutput] = Schema.derived

  implicit lazy val PointMaxDataSchema: Schema[models.db.PointMaxData] = Schema.derived

  implicit lazy val DfGiftCardSchema: Schema[models.pricing.GiftCard] =
    Schema.derived.name(Schema.SName("models.pricing.DfPriceGiftCard"))

  implicit lazy val CashbackSchema: Schema[models.pricing.Cashback] =
    Schema.derived.name(Schema.SName("models.pricing.DfPriceCashback"))

  implicit lazy val ExternalLoyaltyInfoSchema: Schema[models.starfruit.ExternalLoyaltyInfo] = Schema.derived

  implicit lazy val PaymentBreakdownSchema: Schema[models.starfruit.PaymentBreakdown] = Schema.derived

  implicit lazy val BookingCashbackPaymentSchema: Schema[models.starfruit.BookingCashbackPayment] = Schema.derived

  implicit lazy val BookingInfoSchema: Schema[models.starfruit.BookingInfo] = Schema.derived

  implicit lazy val RoomExtraInfoSchema: Schema[models.pricing.RoomExtraInfo] = Schema.derived

  implicit lazy val DfPromotionSchema: Schema[models.pricing.Promotion] =
    Schema.derived.name(Schema.SName("models.pricing.DfPricePromotion"))

  implicit lazy val DFPriceSchema: Schema[models.starfruit.PropertyPricingJsonProtocol.DFPrice] = Schema.derived

  implicit lazy val DownliftSchema: Schema[models.pricing.Downlift] = Schema.derived

  implicit lazy val DownliftDataSchema: Schema[models.pricing.DownliftData] = Schema.derived

  implicit lazy val CORInfoSchema: Schema[models.pricing.CORInfo] = Schema.derived

  implicit lazy val CORMaxRoomNightValueSchema: Schema[models.pricing.CORMaxRoomNightValue] = Schema.derived

  implicit lazy val CORRackRateSchema: Schema[models.pricing.CORRackRate] = Schema.derived

  implicit lazy val CORCalculatedSchema: Schema[models.pricing.CORCalculated] = Schema.derived

  implicit lazy val DfDiscountMessageSchema: Schema[models.pricing.DiscountMessage] =
    Schema.derived.name(Schema.SName("models.pricing.DfPriceDiscountMessage"))

  implicit lazy val CORStackedRateSchema: Schema[models.pricing.CORStackedRate] = Schema.derived

  implicit lazy val CORCouponSchema: Schema[models.pricing.CORCoupon] = Schema.derived

  implicit lazy val CrossOutRatesSchema: Schema[models.pricing.CrossOutRates] = Schema.derived

  implicit lazy val CORMinRoomNightValueSchema: Schema[models.pricing.CORMinRoomNightValue] = Schema.derived

  implicit lazy val RebateSchema: Schema[models.pricing.Rebate] =
    Schema.derived.name(Schema.SName("models.pricing.DfPriceRebate"))

  implicit lazy val DfPseudoCouponSchema: Schema[models.pricing.PseudoCoupon] =
    Schema.derived.name(Schema.SName("models.pricing.DfPricePseudoCoupon"))

  implicit lazy val LocalDatePromotionSchema: Schema[Map[org.joda.time.LocalDate, models.pricing.Promotion]] =
    Schema.schemaForMap[org.joda.time.LocalDate, models.pricing.Promotion](_.toString)

  implicit lazy val LocalDatePromotionListSchema: Schema[Map[org.joda.time.LocalDate, List[models.pricing.Promotion]]] =
    Schema.schemaForMap[org.joda.time.LocalDate, List[models.pricing.Promotion]](_.toString)

  implicit lazy val DFPriceElementSchema: Schema[models.starfruit.PropertyPricingJsonProtocol.DFPriceElement] =
    Schema.derived

  implicit lazy val DFPriceSummarySchema: Schema[models.starfruit.PropertyPricingJsonProtocol.DFPriceSummary] =
    Schema.derived

  implicit lazy val DFPromotionWrapperSchema: Schema[models.starfruit.PropertyPricingJsonProtocol.DFPromotionWrapper] =
    Schema.derived

  implicit lazy val DownliftSourceSchema: Schema[models.pricing.enums.DownliftSource] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.DownliftSource")),
      validator = Validator.enumeration(
        models.pricing.enums.DownliftSources.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val DFDownliftDataWrapperSchema
      : Schema[models.starfruit.PropertyPricingJsonProtocol.DFDownliftDataWrapper] =
    Schema.derived

  implicit lazy val DFDailyPromotionWrapperSchema
      : Schema[models.starfruit.PropertyPricingJsonProtocol.DFDailyPromotionWrapper] =
    Schema.derived

  implicit lazy val DFPriceInfoSchema: Schema[models.starfruit.PropertyPricingJsonProtocol.DFPriceInfo] =
    Schema.derived

  implicit lazy val RoomFeaturesSchema: Schema[models.starfruit.RoomFeatures] = Schema.derived

  implicit lazy val CampaignFeaturesSchema: Schema[models.starfruit.CampaignFeatures] = Schema.derived

  implicit lazy val PaymentFeatureSchema: Schema[models.starfruit.PaymentFeature] = Schema.derived

  implicit lazy val BORInfoSchema: Schema[models.starfruit.BORInfo] =
    Schema.derived.name(Schema.SName("models.starfruit.DfBORInfo"))

  implicit lazy val BORTooltipSchema: Schema[models.starfruit.BORTooltip] =
    Schema.derived.name(Schema.SName("models.starfruit.DfBORTooltip"))

  implicit lazy val BookOnRequestSchema: Schema[models.starfruit.BookOnRequest] = Schema.derived

  implicit lazy val CorBreakdownItemSchema: Schema[models.starfruit.CorBreakdownItem] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCorBreakdownItem"))

  implicit lazy val CorBreakdownSchema: Schema[models.starfruit.CorBreakdown] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCorBreakdown"))

  implicit lazy val CMSIdMapSchema: Schema[Map[Int, models.db.CMSId]] =
    Schema.schemaForMap[Int, models.db.CMSId](_.toString)

  implicit lazy val PricingMessageTextSchema: Schema[models.starfruit.PricingMessageText] = Schema.derived

  implicit lazy val PricingMessageVariableTypeSchema: Schema[models.starfruit.PricingMessageVariableType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.PricingMessageVariableType")),
      validator = Validator.enumeration(
        models.starfruit.PricingMessageVariableType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PricingMessageLocalizationTypeSchema: Schema[models.starfruit.PricingMessageLocalizationType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.PricingMessageLocalizationType")),
      validator = Validator.enumeration(
        models.starfruit.PricingMessageLocalizationType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PricingMessageRoomParameterSchema: Schema[models.starfruit.PricingMessageRoomParameter] =
    Schema.derived

  implicit lazy val PricingMessageSchema: Schema[models.starfruit.PricingMessage] = Schema.derived

  implicit lazy val DFPricingMessageFormattedTextSchema: Schema[models.starfruit.DFPricingMessageFormattedText] =
    Schema.derived

  implicit lazy val DFPricingMessageFormattedSchema: Schema[models.starfruit.DFPricingMessageFormatted] =
    Schema.derived

  implicit lazy val DFPricingMessagesSchema: Schema[models.starfruit.DFPricingMessages] = Schema.derived

  implicit lazy val RatePlanStatusTypeSchema: Schema[models.starfruit.RatePlanStatusType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.RatePlanStatusType")),
      validator = Validator.enumeration(
        models.starfruit.RatePlanStatusTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val SupplierFinancialDataSchema
      : Schema[models.starfruit.PropertyPricingJsonProtocol.SupplierFinancialData] =
    Schema.derived

  implicit lazy val PartialRefundInfoSchema: Schema[models.starfruit.PartialRefundInfo] = Schema.derived

  implicit lazy val CancellationUpSellsSchema: Schema[models.starfruit.CancellationUpSells] = Schema.derived

  implicit lazy val BreakfastWithCancellationUpSellsSchema: Schema[models.starfruit.BreakfastWithCancellationUpSells] =
    Schema.derived

  implicit lazy val AgodaRefundInfoSchema: Schema[models.pricing.AgodaRefundInfo] = Schema.derived

  implicit lazy val PricingChildRateTypeSchema: Schema[com.agoda.papi.enums.room.PricingChildRateType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.PricingChildRateType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.PricingChildRateType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ChildRateSettingSchema: Schema[models.starfruit.ChildRateSetting] = Schema.derived

  implicit lazy val CartEntrySchema: Schema[com.agoda.upi.models.enums.CartEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.CartEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.CartEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val OfferEntrySchema: Schema[com.agoda.upi.models.enums.OfferEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.OfferEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.OfferEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiIdentifierSchema: Schema[com.agoda.upi.models.common.Identifier] = Schema.derived

  implicit lazy val InfoSchema: Schema[com.agoda.upi.models.common.Info] = Schema.derived

  implicit lazy val CancellationGroupEntrySchema: Schema[com.agoda.upi.models.enums.CancellationGroupEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.CancellationGroupEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.CancellationGroupEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PaymentModelEntrySchema: Schema[com.agoda.upi.models.enums.PaymentModelEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.PaymentModelEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.PaymentModelEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ProductAttributeSchema: Schema[com.agoda.upi.models.common.ProductAttribute] = Schema.derived

  implicit lazy val PassengerEntrySchema: Schema[com.agoda.upi.models.enums.PassengerEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.PassengerEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.PassengerEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val SavingSchema: Schema[com.agoda.upi.models.common.Saving] = Schema.derived

  implicit lazy val DetailedSavingSchema: Schema[com.agoda.upi.models.common.DetailedSaving] = Schema.derived

  implicit lazy val UpiRebateEntrySchema: Schema[com.agoda.upi.models.enums.RebateEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.RebateEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.RebateEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiRebateSchema: Schema[com.agoda.upi.models.common.Rebate] =
    Schema.derived.name(Schema.SName("com.agoda.upi.models.common.UpiRebate"))

  implicit lazy val UpiPriceAndSavingSchema: Schema[com.agoda.upi.models.common.PriceAndSaving] = Schema.derived

  implicit lazy val UpiAdditionalRateSchema: Schema[com.agoda.upi.models.common.AdditionalRate] =
    Schema.derived.name(Schema.SName("com.agoda.upi.models.common.UpiAdditionalRate"))

  implicit lazy val CartProductPriceSchema: Schema[com.agoda.upi.models.cart.product.CartProductPrice] =
    Schema.derived

  implicit lazy val CartProductPassengerDisplaySchema
      : Schema[com.agoda.upi.models.cart.product.CartProductPassengerDisplay] =
    Schema.derived

  implicit lazy val CartProductDisplaySchema: Schema[com.agoda.upi.models.cart.product.CartProductDisplay] =
    Schema.derived

  implicit lazy val UpiChargeEntrySchema: Schema[com.agoda.upi.models.enums.ChargeEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.ChargeEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.ChargeEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiDisplayPriceSchema: Schema[com.agoda.upi.models.common.DisplayPrice] =
    Schema.derived.name(Schema.SName("com.agoda.upi.models.common.UpiDisplayPrice"))

  implicit lazy val UpiPayEntrySchema: Schema[com.agoda.upi.models.enums.PayEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.PayEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.PayEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiApplyEntrySchema: Schema[com.agoda.upi.models.enums.ApplyEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.ApplyEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.ApplyEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiChargeOptionEntrySchema: Schema[com.agoda.upi.models.enums.ChargeOptionEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.ChargeOptionEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.ChargeOptionEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiChargeBreakdownSchema: Schema[com.agoda.upi.models.common.ChargeBreakdown] = Schema.derived

  implicit lazy val UpiChargeSchema: Schema[com.agoda.upi.models.common.Charge] =
    Schema.derived.name(Schema.SName("com.agoda.upi.models.common.UpiCharge"))

  implicit lazy val CartProductPricingSchema: Schema[com.agoda.upi.models.cart.product.CartProductPricing] =
    Schema.derived

  implicit lazy val CartTotalPriceSchema: Schema[com.agoda.upi.models.cart.pricing.CartTotalPrice] = Schema.derived

  implicit lazy val CartTotalSchema: Schema[com.agoda.upi.models.cart.pricing.CartTotal] = Schema.derived

  implicit lazy val CartDifferentialPriceSchema: Schema[com.agoda.upi.models.cart.pricing.CartDifferentialPrice] =
    Schema.derived

  implicit lazy val CartDifferentialSchema: Schema[com.agoda.upi.models.cart.pricing.CartDifferential] = Schema.derived

  implicit lazy val CartPassengerDisplaySchema: Schema[com.agoda.upi.models.cart.pricing.CartPassengerDisplay] =
    Schema.derived

  implicit lazy val CartDisplaySchema: Schema[com.agoda.upi.models.cart.pricing.CartDisplay] = Schema.derived

  implicit lazy val UPILoyaltyRewardUnitSchema: Schema[com.agoda.upi.models.common.UPILoyaltyRewardUnit] =
    Schema.derived

  implicit lazy val SplitTenderContextSchema: Schema[com.agoda.upi.models.common.SplitTenderContext] = Schema.derived

  implicit lazy val LoyaltyEarnOfferSchema: Schema[com.agoda.upi.models.cart.pricing.LoyaltyEarnOffer] = Schema.derived

  implicit lazy val LoyaltyBurnOfferSchema: Schema[com.agoda.upi.models.cart.pricing.LoyaltyBurnOffer] = Schema.derived

  implicit lazy val ExternalLoyaltyOfferSchema: Schema[com.agoda.upi.models.cart.pricing.ExternalLoyaltyOffer] =
    Schema.derived

  implicit lazy val CartPriceSchema: Schema[com.agoda.upi.models.cart.pricing.CartPrice] = Schema.derived

  implicit lazy val CartRegularDisplaySchema: Schema[com.agoda.upi.models.cart.pricing.CartRegularDisplay] =
    Schema.derived

  implicit lazy val DifferentialLoyaltyEarnOfferSchema
      : Schema[com.agoda.upi.models.cart.pricing.DifferentialLoyaltyEarnOffer] = Schema.derived

  implicit lazy val DifferentialLoyaltyBurnOfferSchema
      : Schema[com.agoda.upi.models.cart.pricing.DifferentialLoyaltyBurnOffer] = Schema.derived

  implicit lazy val DifferentialExternalLoyaltyOfferSchema
      : Schema[com.agoda.upi.models.cart.pricing.DifferentialExternalLoyaltyOffer] = Schema.derived

  implicit lazy val CartChangePriceSchema: Schema[com.agoda.upi.models.cart.pricing.CartChangePrice] =
    Schema.derived

  implicit lazy val CartChangeDisplaySchema: Schema[com.agoda.upi.models.cart.pricing.CartChangeDisplay] =
    Schema.derived

  implicit lazy val CartBasisDisplaySchema: Schema[com.agoda.upi.models.cart.pricing.CartBasisDisplay] =
    Schema.derived

  implicit lazy val CartBasisSchema: Schema[com.agoda.upi.models.cart.pricing.CartBasis] = Schema.derived

  implicit lazy val UpiRewardsSchema: Schema[com.agoda.upi.models.common.Rewards] = Schema.derived

  implicit lazy val CartPricingSchema: Schema[com.agoda.upi.models.cart.pricing.CartPricing] = Schema.derived

  implicit lazy val CartProductSchema: Schema[com.agoda.upi.models.cart.product.CartProduct] = Schema.derived

  implicit lazy val UpiQuantityEntrySchema: Schema[com.agoda.upi.models.enums.QuantityEntry] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.upi.models.enums.QuantityEntry")),
      validator = Validator.enumeration(
        com.agoda.upi.models.enums.QuantityEntries.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val UpiQuantitySchema: Schema[com.agoda.upi.models.common.Quantity] = Schema.derived

  implicit lazy val CartSchema: Schema[com.agoda.upi.models.cart.Cart] = Schema.derived

  implicit lazy val DfCheckInInformationSchema: Schema[models.starfruit.CheckInInformation] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCheckInInformation"))

  implicit lazy val LocalVoucherSchema: Schema[models.starfruit.LocalVoucher] = Schema.derived

  implicit lazy val StayPackageTypeSchema: Schema[com.agoda.papi.enums.hotel.StayPackageType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.hotel.StayPackageType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.hotel.StayPackageTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RareRoomTypeSchema: Schema[models.pricing.enums.RareRoomType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RareRoomType")),
      validator = Validator.enumeration(
        models.pricing.enums.RareRoomTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PulseCampaignMetadataSchema: Schema[models.starfruit.PulseCampaignMetadata] = Schema.derived

  implicit lazy val RoomCategoryChangeTypeSchema: Schema[models.pricing.enums.RoomCategoryChangeType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RoomCategoryChangeType")),
      validator = Validator.enumeration(
        models.pricing.enums.RoomCategoryChangeType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val OriginalPriceSchema: Schema[models.starfruit.OriginalPrice] =
    Schema.derived.name(Schema.SName("models.starfruit.DfOriginalPrice"))

  implicit lazy val DfOriginalRoomDetailSchema: Schema[models.starfruit.OriginalRoomDetail] =
    Schema.derived.name(Schema.SName("models.starfruit.DfOriginalRoomDetail"))

  implicit lazy val WalletPromoCodeCampaignSchema: Schema[models.starfruit.WalletPromoCodeCampaign] = Schema.derived

  implicit lazy val PromoCmsDataSchema: Schema[models.starfruit.PromoCmsData] = Schema.derived

  implicit lazy val DfCashbackSchema: Schema[models.starfruit.Cashback] =
    Schema.derived.name(Schema.SName("models.starfruit.DfCashback"))

  implicit lazy val RoomOfferNameTypeSchema: Schema[models.pricing.enums.RoomOfferNameType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.RoomOfferNameType")),
      validator = Validator.enumeration(
        models.pricing.enums.RoomOfferNameType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RoomOfferBookingHistorySchema: Schema[models.pricing.RoomOfferBookingHistory] = Schema.derived

  implicit lazy val RoomNightUpSellSchema: Schema[models.starfruit.RoomNightUpSell] = Schema.derived

  implicit lazy val GiftCardSummarySchema: Schema[models.starfruit.GiftCardSummary] = Schema.derived

  implicit lazy val CashbackSummarySchema: Schema[models.starfruit.CashbackSummary] = Schema.derived

  implicit lazy val RewardOptionSchema: Schema[models.starfruit.RewardOption] = Schema.derived

  implicit lazy val AsoBenefitDiscountInfoSchema: Schema[models.starfruit.AsoBenefitDiscountInfo] = Schema.derived

  implicit lazy val M150TransparencyVersionSchema: Schema[models.pricing.enums.M150TransparencyVersionType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.M150TransparencyVersionType")),
      validator = Validator.enumeration(
        models.pricing.enums.M150TransparencyVersionType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val MinBookingCountForSuperAggSchema: Schema[models.starfruit.MinBookingCountForSuperAgg] =
    Schema.derived

  implicit lazy val CampaignMessageSchema: Schema[models.starfruit.CampaignMessage] = Schema.derived

  implicit lazy val CampaignMessagesMapSchema: Schema[Map[Int, models.starfruit.CampaignMessage]] =
    Schema.schemaForMap[Int, models.starfruit.CampaignMessage](_.toString)

  implicit lazy val M150Schema: Schema[models.starfruit.M150] = Schema.derived

  implicit lazy val DFRoomToolTipSchema: Schema[models.starfruit.DFRoomToolTip] = Schema.derived

  implicit lazy val ExternalLoyaltyItemTypeSchema: Schema[com.agoda.papi.enums.room.ExternalLoyaltyItemType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.ExternalLoyaltyItemType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.ExternalLoyaltyItem.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ExternalLoyaltyDisplayItemSchema: Schema[models.starfruit.ExternalLoyaltyDisplayItem] =
    Schema.derived

  implicit lazy val ExternalLoyaltyDisplaySchema: Schema[models.starfruit.ExternalLoyaltyDisplay] = Schema.derived

  implicit lazy val PointsMaxBadgeSchema: Schema[models.starfruit.PointsMaxBadge] =
    Schema.derived.name(Schema.SName("models.starfruit.DfPointsMaxBadge"))

  implicit lazy val SmartSaverInfoSchema: Schema[models.starfruit.SmartSaverInfo] = Schema.derived

  implicit lazy val SmartFlexInfoSchema: Schema[models.starfruit.SmartFlexInfo] = Schema.derived

  implicit lazy val FinProductsSchema: Schema[models.starfruit.FinProducts] = Schema.derived

  implicit lazy val FinanceProductInfoSchema: Schema[models.starfruit.FinanceProductInfo] = Schema.derived

  implicit lazy val OccupancyMessageSchema: Schema[models.starfruit.OccupancyMessage] =
    Schema.derived.name(Schema.SName("models.starfruit.DfOccupancyMessage"))

  implicit lazy val PerBookCapacitySchema: Schema[models.starfruit.PerBookCapacity] = Schema.derived

  implicit lazy val DfRoomSchema: Schema[models.starfruit.Room] = Schema.derived

  implicit lazy val PartnerSchema: Schema[models.starfruit.Partner] = Schema.derived

  implicit lazy val OccDisplaySchema: Schema[models.starfruit.OccupancyDisplay] = Schema.derived

  implicit lazy val PayingPeopleSchema: Schema[models.starfruit.PayingPeople] = Schema.derived

  implicit lazy val PayingChildAgesSchema: Schema[models.starfruit.PayingChildAges] = Schema.derived

  implicit lazy val MaxAllowedFreeChildAgeRangeSchema: Schema[models.starfruit.MaxAllowedFreeChildAgeRange] =
    Schema.derived

  implicit lazy val RoomTooltipSchema: Schema[transformers.RoomTooltip] = Schema.derived

  implicit lazy val PapiPointsMaxBadgeSchema: Schema[transformers.PointsMaxBadge] = Schema.derived

  implicit lazy val PapiDfPromotionWrapperSchema: Schema[transformers.DFPromotionWrapper] =
    Schema.derived.name(Schema.SName("transformers.PAPIDFPromotionWrapper"))

  implicit lazy val PapiDfDownliftDataWrapperSchema: Schema[transformers.DFDownliftDataWrapper] =
    Schema.derived.name(Schema.SName("transformers.PAPIDFDownliftDataWrapper"))

  implicit lazy val PapiDfDailyPromotionWrapperSchema: Schema[transformers.DFDailyPromotionWrapper] =
    Schema.derived.name(Schema.SName("transformers.PAPIDFDailyPromotionWrapper"))

  implicit lazy val PriceInfosSchema: Schema[transformers.PriceInfos] = Schema.derived

  implicit lazy val PapiCampaignTypeSchema: Schema[enumerations.CampaignType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("enumerations.PapiCampaignType")),
      validator = Validator.enumeration(
        enumerations.CampaignTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val EnrichedCampaignPromotionSchema: Schema[transformers.EnrichedCampaignPromotion] = Schema.derived

  implicit lazy val PapiBORInfoSchema: Schema[transformers.BORInfo] = Schema.derived

  implicit lazy val PapiBORTooltipSchema: Schema[transformers.BORTooltip] = Schema.derived

  implicit lazy val EnrichedBorSchema: Schema[transformers.EnrichedBOR] = Schema.derived

  implicit lazy val EnrichedCampaignSchema: Schema[transformers.EnrichedCampaign] = Schema.derived

  implicit lazy val PapiCorBreakdownItemSchema: Schema[transformers.CorBreakdownItem] = Schema.derived

  implicit lazy val PapiCorBreakdownSchema: Schema[transformers.CorBreakdown] = Schema.derived

  implicit lazy val PapiCorSummarySchema: Schema[transformers.CorSummary] = Schema.derived

  implicit lazy val RoomPricingMessageSchema: Schema[transformers.RoomPricingMessage] = Schema.derived

  implicit lazy val PapiFencedRateSchema: Schema[transformers.FencedRate] = Schema.derived

  implicit lazy val PriceChangeSchema: Schema[metapricing.PriceChange] =
    Schema.derived.name(Schema.SName("metapricing.PapiPriceChange"))

  implicit lazy val DFPriceChangeSchema: Schema[models.pricing.PriceChange] =
    Schema.derived.name(Schema.SName("models.pricing.DfPriceChange"))

  implicit lazy val AlternativeDateWithPriceSchema: Schema[transformers.AlternativeDateWithPrice] = Schema.derived

  implicit lazy val EnrichedPastBookingInfoSchema: Schema[transformers.EnrichedPastBookingInfo] = Schema.derived

  implicit lazy val EnrichedPastBookingInfoResponseSchema: Schema[transformers.EnrichedPastBookingInfoResponse] =
    Schema.derived

  implicit lazy val PricingDisplayPresentationSchema: Schema[transformers.PricingDisplayPresentation] = Schema.derived

  implicit lazy val PapiOriginalPriceSchema: Schema[transformers.OriginalPrice] = Schema.derived

  implicit lazy val OriginalRoomDetailSchema: Schema[transformers.OriginalRoomDetail] = Schema.derived

  implicit lazy val EnrichedPromoCmsDataSchema: Schema[transformers.EnrichedPromoCmsData] = Schema.derived

  implicit lazy val EnrichedCampaignMessageSchema: Schema[transformers.EnrichedCampaignMessage] = Schema.derived

  implicit lazy val EnrichedCampaignMessagesMapSchema: Schema[Map[Int, transformers.EnrichedCampaignMessage]] =
    Schema.schemaForMap[Int, transformers.EnrichedCampaignMessage](_.toString)

  implicit lazy val CampaignStatusTypeSchema: Schema[com.agoda.papi.enums.campaign.CampaignStatusType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.campaign.CampaignStatusType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.campaign.CampaignStatusTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val EnrichedWalletPromotionSchema: Schema[transformers.EnrichedWalletPromotion] = Schema.derived

  implicit lazy val PapiOccupancyMessageSchema: Schema[transformers.OccupancyMessage] = Schema.derived

  implicit lazy val EnrichedChildRoomSchema: Schema[transformers.EnrichedChildRoom] = Schema.derived

  implicit lazy val RoomFeatureResponseSchema: Schema[com.agoda.content.models.db.information.RoomFeatureResponse] =
    Schema.derived

  implicit lazy val GroupMseRoomSchema: Schema[transformers.GroupMseRoom] = Schema.derived

  implicit lazy val RoomMetadataLocalizationSchema
      : Schema[com.agoda.content.models.db.information.RoomMetadataLocalization] =
    Schema.derived

  implicit lazy val BedResponseLocalizationSchema
      : Schema[com.agoda.content.models.db.information.BedResponseLocalization] =
    Schema.derived

  implicit lazy val BedOptionResponseLocalizationSchema
      : Schema[com.agoda.content.models.db.information.BedOptionResponseLocalization] =
    Schema.derived

  implicit lazy val BedroomConfigurationResponseLocalizationSchema
      : Schema[com.agoda.content.models.db.information.BedroomConfigurationResponseLocalization] =
    Schema.derived

  implicit lazy val BedDetailResponseLocalizationSchema
      : Schema[com.agoda.content.models.db.information.BedDetailResponseLocalization] =
    Schema.derived

  implicit lazy val BedConfigurationResponseLocalizationSchema
      : Schema[com.agoda.content.models.db.information.BedConfigurationResponseLocalization] =
    Schema.derived

  implicit lazy val MasterRoomResponseLocalizationSchema
      : Schema[com.agoda.content.models.db.information.MasterRoomResponseLocalization] =
    Schema.derived

  implicit lazy val MasterRoomReviewInformationSchema
      : Schema[com.agoda.content.models.db.information.MasterRoomReviewInformation] =
    Schema.derived

  implicit lazy val CustomizableRoomGridOptionsSchema: Schema[models.starfruit.CustomizableRoomGridOptions] =
    Schema.derived

  implicit lazy val RecommendedUpgradeSchema: Schema[RecommendedUpgrade] = Schema.derived

  implicit lazy val EnrichedMasterRoomSchema: Schema[transformers.EnrichedMasterRoom] = Schema.derived

  implicit lazy val RenovationSchema: Schema[com.agoda.content.models.db.information.Renovation] = Schema.derived

  implicit lazy val RemarksSchema: Schema[com.agoda.content.models.db.information.Remarks] = Schema.derived

  implicit lazy val GeoInfoSchema: Schema[com.agoda.content.models.db.information.GeoInfo] = Schema.derived

  implicit lazy val ColorSchema: Schema[com.agoda.content.models.db.information.Color] = Schema.derived

  implicit lazy val StarRatingSchema: Schema[com.agoda.content.models.db.information.StarRating] = Schema.derived

  implicit lazy val SellingPointSchema: Schema[com.agoda.content.models.db.information.SellingPoint] = Schema.derived

  implicit lazy val GeneralRoomInformationResponseSchema
      : Schema[com.agoda.content.models.db.information.GeneralRoomInformationResponse] =
    Schema.derived

  implicit lazy val GoldCircleAwardSchema: Schema[com.agoda.content.models.db.information.GoldCircleAward] =
    Schema.derived

  implicit lazy val AdvanceGuaranteeProgramSchema
      : Schema[com.agoda.content.models.db.information.AdvanceGuaranteeProgram] =
    Schema.derived

  implicit lazy val AwardsAndAccoladesSchema: Schema[com.agoda.content.models.db.information.AwardsAndAccolades] =
    Schema.derived

  implicit lazy val InformationSummaryLocalizationsSchema
      : Schema[com.agoda.content.models.db.information.InformationSummaryLocalizations] =
    Schema.derived

  implicit lazy val HotelThemeSchema: Schema[com.agoda.content.models.db.information.HotelTheme] = Schema.derived

  implicit lazy val HotelAccommodationTypeSchema
      : Schema[com.agoda.content.models.db.information.HotelAccommodationType] =
    Schema.derived

  implicit lazy val AtmosphereSchema: Schema[com.agoda.content.models.db.information.Atmosphere] = Schema.derived

  implicit lazy val PropertyLinksSchema: Schema[com.agoda.content.models.db.information.PropertyLinks] = Schema.derived

  implicit lazy val NhaSummarySchema: Schema[com.agoda.content.models.db.information.NhaSummary] = Schema.derived

  implicit lazy val ArrivalGuideSchema: Schema[com.agoda.content.models.db.information.ArrivalGuide] = Schema.derived

  implicit lazy val CheckinMethodSchema: Schema[com.agoda.content.models.db.information.CheckinMethod] = Schema.derived

  implicit lazy val AsqInfoSchema: Schema[com.agoda.content.models.db.information.AsqInfo] = Schema.derived

  implicit lazy val LuxuryAwardSchema: Schema[com.agoda.content.models.db.information.LuxuryAward] = Schema.derived

  implicit lazy val LuxurySchema: Schema[com.agoda.content.models.db.information.Luxury] = Schema.derived

  implicit lazy val TagViewResponseSchema: Schema[com.agoda.content.models.db.information.TagViewResponse] =
    Schema.derived

  implicit lazy val HotelTagViewResponseSchema: Schema[com.agoda.content.models.db.information.HotelTagViewResponse] =
    Schema.derived

  implicit lazy val InformationSummaryResponseSchema
      : Schema[com.agoda.content.models.db.information.InformationSummaryResponse] =
    Schema.derived

  implicit lazy val PaxTypeSchema: Schema[models.pricing.enums.PaxType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.PaxType")),
      validator = Validator.enumeration(
        models.pricing.enums.PaxTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val PartnerPaxOptionSchema: Schema[models.starfruit.PartnerPaxOption] = Schema.derived

  implicit lazy val ContentGeoInfoSchema: Schema[com.agoda.content.models.propertycontent.ContentGeoInfo] =
    Schema.derived

  implicit lazy val LandMarkGroupSchema: Schema[com.agoda.content.models.db.information.LandMarkGroup] =
    Schema.derived

  implicit lazy val PlaceSchema: Schema[com.agoda.content.models.db.information.Place] = Schema.derived

  implicit lazy val NearbyPropertySchema: Schema[com.agoda.content.models.db.information.NearbyProperty] =
    Schema.derived

  implicit lazy val RestaurantSchema: Schema[com.agoda.content.models.db.information.Restaurant] = Schema.derived

  implicit lazy val CuisineSchema: Schema[com.agoda.content.models.db.information.Cuisine] = Schema.derived

  implicit lazy val TransportationModeSchema: Schema[com.agoda.content.models.db.information.TransportationMode] =
    Schema.derived

  implicit lazy val TransportationTypeSchema: Schema[com.agoda.content.models.db.information.TransportationType] =
    Schema.derived

  implicit lazy val TransportationSchema: Schema[com.agoda.content.models.db.information.Transportation] =
    Schema.derived

  implicit lazy val LocalPlacesSummarySchema: Schema[com.agoda.content.models.db.information.LocalPlacesSummary] =
    Schema.derived

  implicit lazy val SummaryLandmarkGroupSchema: Schema[com.agoda.content.models.db.information.SummaryLandmarkGroup] =
    Schema.derived

  implicit lazy val LocalPlacesOfInterestSummarySchema
      : Schema[com.agoda.content.models.db.information.LocalPlacesOfInterestSummary] =
    Schema.derived

  implicit lazy val LocationSubscoreSchema: Schema[com.agoda.content.models.db.information.LocationSubscore] =
    Schema.derived

  implicit lazy val WalkableCategorySchema: Schema[com.agoda.content.models.db.information.WalkableCategory] =
    Schema.derived

  implicit lazy val WalkablePlacesSchema: Schema[com.agoda.content.models.db.information.WalkablePlaces] =
    Schema.derived

  implicit lazy val LocalInformationSchema: Schema[com.agoda.content.models.db.information.LocalInformation] =
    Schema.derived

  implicit lazy val CheckInOutTimeInfoSchema: Schema[com.agoda.content.models.db.checkInOut.CheckInOutTimeInfo] =
    Schema.derived

  implicit lazy val TimeInfoSchema: Schema[com.agoda.content.models.db.checkInOut.TimeInfo] = Schema.derived

  implicit lazy val CheckInOutTimeSchema: Schema[com.agoda.content.models.db.checkInOut.CheckInOutTime] = Schema.derived

  implicit lazy val CheckInOutSchema: Schema[com.agoda.content.models.db.checkInOut.CheckInOut] = Schema.derived

  implicit lazy val AirportSchema: Schema[com.agoda.content.models.db.synopsis.Airport] = Schema.derived

  implicit lazy val AirportTransferSchema: Schema[com.agoda.content.models.db.synopsis.AirportTransfer] =
    Schema.derived

  implicit lazy val NeighborhoodSchema: Schema[com.agoda.content.models.db.synopsis.Neighborhood] = Schema.derived

  implicit lazy val AggregatedFacilitySchema: Schema[com.agoda.content.models.db.synopsis.AggregatedFacility] =
    Schema.derived

  implicit lazy val SynopsisSchema: Schema[com.agoda.content.models.db.synopsis.Synopsis] = Schema.derived

  implicit lazy val LandmarkSchema: Schema[com.agoda.content.models.db.experiences.Landmark] = Schema.derived

  implicit lazy val ExperienceSchema: Schema[com.agoda.content.models.db.experiences.Experience] = Schema.derived

  implicit lazy val ExperiencesSchema: Schema[com.agoda.content.models.db.experiences.Experiences] = Schema.derived

  implicit lazy val EnrichedPseudoCouponMessageSchema: Schema[transformers.EnrichedPseudoCouponMessage] = Schema.derived

  implicit lazy val PaymentModelCancellationMapSchema
      : Schema[Map[models.pricing.enums.PaymentModel, Set[models.starfruit.Cancellation]]] =
    Schema.schemaForMap[models.pricing.enums.PaymentModel, Set[models.starfruit.Cancellation]](_.toString)

  implicit lazy val HotelAggregatedPaymentSchema: Schema[models.starfruit.HotelAggregatedPayment] = Schema.derived

  implicit lazy val DfChildPolicySchema: Schema[models.starfruit.ChildPolicy] =
    Schema.derived.name(Schema.SName("models.starfruit.DfChildPolicy"))

  implicit lazy val DfHotelAggregatedOptionsSchema: Schema[models.starfruit.HotelAggregatedOptions] = Schema.derived

  implicit lazy val HotelFeaturesSchema: Schema[models.starfruit.HotelFeatures] = Schema.derived

  implicit lazy val EnrichedPriceMessagingSchema: Schema[transformers.EnrichedPriceMessaging] = Schema.derived

  implicit lazy val AvailabilityTypeSchema: Schema[enumerations.AvailabilityType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("enumerations.AvailabilityType")),
      validator = Validator.enumeration(
        enumerations.AvailabilityTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val EnrichedBookingCancellationSchema: Schema[transformers.EnrichedBookingCancellation] = Schema.derived

  implicit lazy val BookingItemBreakdownSchema: Schema[models.starfruit.BookingItemBreakdown] = Schema.derived

  implicit lazy val BookingReferencePriceSchema: Schema[models.starfruit.BookingReferencePrice] = Schema.derived

  implicit lazy val BookingSummaryPriceSchema: Schema[models.starfruit.BookingSummaryPrice] = Schema.derived

  implicit lazy val BookingSurchargesItemSchema: Schema[models.starfruit.BookingSurchargesItem] = Schema.derived

  implicit lazy val BookingSurchargesItemsSchema: Schema[models.starfruit.BookingSurchargesItems] = Schema.derived

  implicit lazy val BookingSummaryPricesSchema: Schema[models.starfruit.BookingSummaryPrices] = Schema.derived

  implicit lazy val BookingPriceSchema: Schema[models.starfruit.BookingPrice] = Schema.derived

  implicit lazy val EnrichedBookingGiftCardEarningSchema: Schema[transformers.EnrichedBookingGiftCardEarning] =
    Schema.derived

  implicit lazy val BookingPartnerLoyaltyPointSchema: Schema[models.starfruit.BookingPartnerLoyaltyPoint] =
    Schema.derived

  implicit lazy val EnrichedBookingBenefitSchema: Schema[transformers.EnrichedBookingBenefit] = Schema.derived

  implicit lazy val EnrichedBookingRateCategorySchema: Schema[transformers.EnrichedBookingRateCategory] = Schema.derived

  implicit lazy val EnrichedFireDrillContractSchema: Schema[transformers.EnrichedFireDrillContract] = Schema.derived

  implicit lazy val EnrichedBookingSellInfoSchema: Schema[transformers.EnrichedBookingSellInfo] = Schema.derived

  implicit lazy val EnrichedBookingYCSPromotionSchema: Schema[transformers.EnrichedBookingYCSPromotion] = Schema.derived

  implicit lazy val BookingPaymentSchema: Schema[models.starfruit.BookingPayment] = Schema.derived

  implicit lazy val BORSupplierSchema: Schema[transformers.BORSupplier] = Schema.derived

  implicit lazy val SpecialRequestIdSchema: Schema[enumerations.SpecialRequestId] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("enumerations.SpecialRequestId")),
      validator = Validator.enumeration(
        enumerations.SpecialRequestIds.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val EnrichedBookingCashbackEarningSchema: Schema[transformers.EnrichedBookingCashbackEarning] =
    Schema.derived

  implicit lazy val EnrichedBookingRoomSchema: Schema[transformers.EnrichedBookingRoom] = Schema.derived

  implicit lazy val BookingExternalLoyaltyPaymentSchema: Schema[models.starfruit.BookingExternalLoyaltyPayment] =
    Schema.derived

  implicit lazy val EnrichedEBEPaymentSchema: Schema[transformers.EnrichedEBEPayment] = Schema.derived

  implicit lazy val EnrichedEBECreditCardSchema: Schema[transformers.EnrichedEBECreditCard] = Schema.derived

  implicit lazy val EnrichedCampaignDiscountSchema: Schema[transformers.EnrichedCampaignDiscount] = Schema.derived

  implicit lazy val StayTypeSchema: Schema[com.agoda.papi.enums.hotel.StayType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.hotel.StayType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.hotel.StayTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val EnrichedEBEHotelSchema: Schema[transformers.EnrichedEBEHotel] = Schema.derived

  implicit lazy val EnrichedEBEBookingSchema: Schema[transformers.EnrichedEBEBooking] = Schema.derived

  implicit lazy val EnrichedBookingItemSchema: Schema[transformers.EnrichedBookingItem] = Schema.derived

  implicit lazy val TaxTypeSchema: Schema[models.pricing.enums.TaxType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.pricing.enums.TaxType")),
      validator = Validator.enumeration(
        models.pricing.enums.TaxTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val EnrichedSupplierSummarySchema: Schema[transformers.EnrichedSupplierSummary] = Schema.derived

  implicit lazy val EnrichedSupplierSummaryMapSchema: Schema[Map[Long, transformers.EnrichedSupplierSummary]] =
    Schema.schemaForMap[Long, transformers.EnrichedSupplierSummary](_.toString)

  implicit lazy val PropertyUspSchema: Schema[transformers.PropertyUsp] = Schema.derived

  implicit lazy val PropertyUspRankSchema: Schema[transformers.PropertyUspRank] = Schema.derived

  implicit lazy val AlternativeRoomSchema: Schema[models.starfruit.AlternativeRoom] = Schema.derived

  implicit lazy val ValueCalculationMethodTypeSchema: Schema[com.agoda.papi.enums.room.ValueCalculationMethodType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.ValueCalculationMethodType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.ValueCalculationMethodType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ValueMethodTypeSchema: Schema[com.agoda.papi.enums.room.ValueMethodType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.ValueMethodType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.ValueMethodType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val GeoTypeSchema: Schema[com.agoda.papi.enums.room.GeoType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.papi.enums.room.GeoType")),
      validator = Validator.enumeration(
        com.agoda.papi.enums.room.GeoType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val TaxMetaDataSchema: Schema[models.starfruit.TaxMetaData] = Schema.derived

  implicit lazy val BookingDatesSchema: Schema[com.agoda.core.search.models.response.BookingDates] = Schema.derived

  implicit lazy val FriendlinessTypeSchema: Schema[com.agoda.core.search.models.enumeration.FriendlinessType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.enumeration.FriendlinessType")),
      validator = Validator.enumeration(
        com.agoda.core.search.models.enumeration.FriendlinessType.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val ReviewScoreGradeSchema: Schema[com.agoda.core.search.models.response.ReviewScoreGrade] =
    Schema.derived

  implicit lazy val ReviewInfoSchema: Schema[com.agoda.core.search.models.response.ReviewInfo] = Schema.derived

  implicit lazy val FriendsInfoTypeSchema: Schema[com.agoda.core.search.models.response.FriendsInfoType] =
    Schema.derived

  implicit lazy val UniqueSellingPointSchema: Schema[com.agoda.content.models.response.UniqueSellingPoint] =
    Schema.derived

  implicit lazy val USPResponseSchema: Schema[com.agoda.content.models.response.USPResponse] = Schema.derived

  implicit lazy val SymbolAndTextSchema: Schema[transformers.SymbolAndText] = Schema.derived

  implicit lazy val BeddingInfoSchema: Schema[transformers.BeddingInfo] = Schema.derived

  implicit lazy val HotelAggregationInfoSchema: Schema[transformers.HotelAggregationInfo] = Schema.derived

  implicit lazy val SoldOutPricingSchema: Schema[transformers.SoldOutPricing] = Schema.derived

  implicit lazy val SoldOutPricesTypeSchema: Schema[types.SoldOutPricesType] =
    Schema.schemaForMap(_.toString)

  implicit lazy val SoldOutRoomInfoSchema: Schema[transformers.SoldOutRoomInfo] = Schema.derived
  implicit lazy val RoomIdTypeSoldOutRoomInfoMapSchema: Schema[Map[types.RoomIdType, transformers.SoldOutRoomInfo]] =
    Schema.schemaForMap(_.toString)

  implicit lazy val EnrichedSoldOutRoomsSchema: Schema[transformers.EnrichedSoldOutRooms] = Schema.derived

  implicit lazy val CityBookingHistorySchema: Schema[transformers.CityBookingHistory] = Schema.derived

  implicit lazy val BookingFormHistorySchema: Schema[transformers.BookingFormHistory] = Schema.derived

  implicit lazy val PropertyLastYearBookingCountSchema
      : Schema[com.agoda.pricestream.models.response.PropertyLastYearBookingCount] =
    Schema.derived

  implicit lazy val BookingTimeStampSchema: Schema[com.agoda.pricestream.models.response.BookingTimeStamp] =
    Schema.derived

  implicit lazy val MasterRoomIdBookingTimeStampMapSchema: Schema[
    Map[com.agoda.pricestream.models.Types.MasterRoomId, com.agoda.pricestream.models.response.BookingTimeStamp]
  ] =
    Schema.schemaForMap[
      com.agoda.pricestream.models.Types.MasterRoomId,
      com.agoda.pricestream.models.response.BookingTimeStamp
    ](_.toString)

  implicit lazy val MasterRoomIdBookingTimeStampOptionMapSchema: Schema[
    Map[com.agoda.pricestream.models.Types.MasterRoomId, Option[com.agoda.pricestream.models.response.BookingTimeStamp]]
  ] =
    Schema.schemaForMap[com.agoda.pricestream.models.Types.MasterRoomId, Option[
      com.agoda.pricestream.models.response.BookingTimeStamp
    ]](_.toString)

  implicit lazy val PropertyRecentBookingSchema: Schema[com.agoda.pricestream.models.response.PropertyRecentBooking] =
    Schema.derived

  implicit lazy val PsRankMetricSchema: Schema[com.agoda.pricestream.models.response.RankMetric] =
    Schema.derived.name(Schema.SName("com.agoda.pricestream.models.response.PsRankMetric"))

  implicit lazy val PropertyMetaRankingSchema: Schema[com.agoda.pricestream.models.response.PropertyMetaRanking] =
    Schema.derived

  implicit lazy val PropertyPriceSchemaResponseSchema
      : Schema[com.agoda.pricestream.models.response.PropertyPriceSchemaResponse] =
    Schema.derived

  implicit lazy val PropertyPriceSchema: Schema[com.agoda.pricestream.models.response.PropertyPriceSchema] =
    Schema.derived

  implicit lazy val PropertyMetaInfoSchema: Schema[com.agoda.pricestream.models.response.PropertyMetaInfo] =
    Schema.derived

  implicit lazy val ChildrenFreePolicySchema: Schema[com.agoda.content.models.db.personalized.ChildrenFreePolicy] =
    Schema.derived

  implicit lazy val PersonalizedInformationSchema
      : Schema[com.agoda.content.models.db.personalized.PersonalizedInformation] =
    Schema.derived

  implicit lazy val SimilarPropertyInfoSchema: Schema[ranking.SimilarPropertyInfo] = Schema.derived

  implicit lazy val RankingExplanationMessageSchema: Schema[ranking.RankingExplanationMessage] = Schema.derived

  implicit lazy val InternalUtilityDataFromDFSchema: Schema[transformers.InternalUtilityDataFromDF] = Schema.derived

  implicit lazy val LongStringMapSchema: Schema[scala.collection.immutable.Map[Long, String]] =
    Schema.schemaForMap[Long, String](_.toString)

  implicit lazy val InternalUtilityDataFromContentSchema: Schema[transformers.InternalUtilityDataFromContent] =
    Schema.derived

  implicit lazy val InternalUtilityDataSchema: Schema[transformers.InternalUtilityData] = Schema.derived

  implicit lazy val LandmarkInfoSchema: Schema[transformers.LandmarkInfo] = Schema.derived

  implicit lazy val AreaBookingCountModelSchema: Schema[transformers.AreaBookingCountModel] = Schema.derived

  implicit lazy val PricingSummarySchema: Schema[transformers.PricingSummary] = Schema.derived

  implicit lazy val AlternateDateSummarySchema: Schema[transformers.AlternateDateSummary] = Schema.derived

  implicit lazy val StayOccupancySchema: Schema[models.starfruit.StayOccupancy] = Schema.derived

  implicit lazy val BookingMaxOccupancySchema: Schema[models.starfruit.BookingMaxOccupancy] = Schema.derived

  implicit lazy val RateCategoryImageSchema: Schema[com.agoda.content.models.db.rateCategories.RateCategoryImage] =
    Schema.derived

  implicit lazy val RateCategoryAmendmentPolicyRulesSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryAmendmentPolicyRules] =
    Schema.derived

  implicit lazy val RateCategoryPolicyPriceSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryPolicyPrice] =
    Schema.derived

  implicit lazy val RateCategoryCancellationChargeRateSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryCancellationChargeRate] =
    Schema.derived

  implicit lazy val RateCategoryCancellationPolicySchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryCancellationPolicy] =
    Schema.derived

  implicit lazy val RateCategoryChildPolicySchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryChildPolicy] =
    Schema.derived

  implicit lazy val RateCategoryChildPoliciesSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryChildPolicies] =
    Schema.derived

  implicit lazy val RoomTypeWithDescriptionSchema
      : Schema[com.agoda.content.models.db.rateCategories.RoomTypeWithDescription] =
    Schema.derived

  implicit lazy val RateCategoryDetailSchema: Schema[com.agoda.content.models.db.rateCategories.RateCategoryDetail] =
    Schema.derived

  implicit lazy val RateCategoryResponseSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoryResponse] =
    Schema.derived

  implicit lazy val RateCategoriesResponseSchema
      : Schema[com.agoda.content.models.db.rateCategories.RateCategoriesResponse] =
    Schema.derived

  implicit lazy val MetaApiRankMetricSchema: Schema[metaapi.RankMetric] =
    Schema.derived

  implicit lazy val PropertyRankingSchema: Schema[metaapi.PropertyRanking] = Schema.derived

  implicit lazy val BathFacilitySchema: Schema[com.agoda.content.models.db.bathInformation.BathFacility] =
    Schema.derived

  implicit lazy val BathOtherSchema: Schema[com.agoda.content.models.db.bathInformation.BathOther] =
    Schema.derived

  implicit lazy val BathUsagePeriodSchema: Schema[com.agoda.content.models.db.bathInformation.BathUsagePeriod] =
    Schema.derived

  implicit lazy val BathTimeSchema: Schema[com.agoda.content.models.db.bathInformation.BathTime] =
    Schema.derived

  implicit lazy val BathInOutDoorSchema: Schema[com.agoda.content.models.db.bathInformation.BathInOutDoor] =
    Schema.derived

  implicit lazy val BathInformationSchema: Schema[com.agoda.content.models.db.bathInformation.BathInformation] =
    Schema.derived

  implicit lazy val BathInformationResponseSchema
      : Schema[com.agoda.content.models.db.bathInformation.BathInformationResponse] =
    Schema.derived

  implicit lazy val ConnectionSchema: Schema[com.agoda.content.models.db.transportationInformation.Connection] =
    Schema.derived

  implicit lazy val PlacesSchema: Schema[com.agoda.content.models.db.transportationInformation.Places] =
    Schema.derived

  implicit lazy val WaypointSchema: Schema[com.agoda.content.models.db.transportationInformation.Waypoint] =
    Schema.derived

  implicit lazy val TransportationInformationResponseSchema
      : Schema[com.agoda.content.models.db.transportationInformation.TransportationInformationResponse] =
    Schema.derived

  implicit lazy val PropertyLongStayPromotionSchema
      : Schema[com.agoda.pricestream.models.response.PropertyLongStayPromotion] =
    Schema.derived

  implicit lazy val LongDateTimeMapSchema: Schema[Map[Long, org.joda.time.DateTime]] =
    Schema.schemaForMap[Long, org.joda.time.DateTime](_.toString)

  implicit lazy val MetaEngagementSchema: Schema[transformers.MetaEngagement] = Schema.derived

  implicit lazy val EnrichedSuggestedRoomSchema: Schema[transformers.EnrichedSuggestedRoom] = Schema.derived

  implicit lazy val EnrichedMultiRoomSuggestionSchema: Schema[transformers.EnrichedMultiRoomSuggestion] =
    Schema.derived

  implicit lazy val EnrichMasterRoomBundleSegmentSchema: Schema[transformers.EnrichMasterRoomBundleSegment] =
    Schema.derived

  implicit lazy val RoomBundleSchema: Schema[transformers.RoomBundle] = Schema.derived

  implicit lazy val RocketmilesPublishedPriceSchema: Schema[models.starfruit.RocketmilesPublishedPrice] =
    Schema.derived

  implicit lazy val RocketmilesPublishedPricingBasisSchema: Schema[models.starfruit.RocketmilesPublishedPricingBasis] =
    Schema.derived

  implicit lazy val RocketmilesTrustedSupplierSchema: Schema[models.starfruit.RocketmilesTrustedSupplier] =
    Schema.derived

  implicit lazy val RocketmilesSupplierFinancialDataWithIdSchema
      : Schema[models.starfruit.RocketmilesSupplierFinancialDataWithId] =
    Schema.derived

  implicit lazy val RocketmilesComputedPublishPriceSchema: Schema[models.starfruit.RocketmilesComputedPublishPrice] =
    Schema.derived

  implicit lazy val RocketMilesRoomAwareMatchingLevelTypeSchema
      : Schema[models.starfruit.RocketMilesRoomAwareMatchingLevelType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("models.starfruit.RocketMilesRoomAwareMatchingLevelType")),
      validator = Validator.enumeration(
        models.starfruit.RocketMilesRoomAwareMatchingLevelTypes.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val RoomAwarePublishPriceSchema: Schema[models.starfruit.RoomAwarePublishPrice] =
    Schema.derived

  implicit lazy val RocketMilesPublishedPricingSchema: Schema[models.starfruit.RocketMilesPublishedPricing] =
    Schema.derived

  implicit lazy val PaymentMethodSchema: Schema[payment.PaymentMethod] = Schema.derived

  implicit lazy val PaymentCategorySchema: Schema[payment.PaymentCategory] = Schema.derived

  implicit lazy val DisplayPaymentTypeSchema: Schema[payment.DisplayPaymentType] = Schema.derived

  implicit lazy val CheapestStayPackageRatePlansSchema: Schema[models.CheapestStayPackageRatePlans] =
    Schema.derived

  implicit lazy val propertySchema: Schema[transformers.Property] = Schema.derived

  implicit lazy val debugSchema: Schema[transformers.Debug] = Schema.derived

  implicit lazy val enrichedCheapestRoomSchema: Schema[transformers.EnrichedCheapestRoom] = Schema.derived

  implicit lazy val supplierInformationSchema: Schema[transformers.SupplierInformation] = Schema.derived
  implicit lazy val supplierInformationMapSchema: Schema[Map[Long, transformers.SupplierInformation]] =
    Schema.schemaForMap[Long, transformers.SupplierInformation](_.toString)

  implicit lazy val DescriptionSchema: Schema[com.agoda.content.models.db.information.Description] = Schema.derived

  implicit lazy val UsefulInfoSchema: Schema[com.agoda.content.models.db.information.UsefulInfo] = Schema.derived

  implicit lazy val UsefulInfoGroupSchema: Schema[com.agoda.content.models.db.information.UsefulInfoGroup] =
    Schema.derived

  implicit lazy val NotesSchema: Schema[com.agoda.content.models.db.information.Notes] = Schema.derived

  implicit lazy val EntertainmentResponseSchema: Schema[com.agoda.content.models.db.information.EntertainmentResponse] =
    Schema.derived

  implicit lazy val PolicyAgeRangeSchema: Schema[com.agoda.content.models.db.policy.AgeRange] =
    Schema.derived.name(Schema.SName("com.agoda.content.models.db.policy.ContentAgeRange"))

  implicit lazy val ChildPolicySchema: Schema[com.agoda.content.models.db.policy.ChildPolicy] =
    Schema.derived.name(Schema.SName("com.agoda.content.models.db.policy.ContentChildPolicy"))

  implicit lazy val ChildrenPolicySchema: Schema[com.agoda.content.models.db.policy.ChildrenPolicy] = Schema.derived

  implicit lazy val HotelAgePolicySchema: Schema[com.agoda.content.models.db.policy.HotelAgePolicy] = Schema.derived

  implicit lazy val HotelPoliciesSchema: Schema[com.agoda.content.models.db.policy.HotelPolicies] = Schema.derived

  implicit lazy val LanguageInfoSchema: Schema[com.agoda.content.models.db.information.LanguageInfo] = Schema.derived

  implicit lazy val MessagingSchema: Schema[com.agoda.content.models.db.information.Messaging] = Schema.derived

  implicit lazy val ReceptionSchema: Schema[com.agoda.content.models.db.information.Reception] = Schema.derived

  implicit lazy val CountrySchema: Schema[com.agoda.content.models.db.information.Country] = Schema.derived

  implicit lazy val ContactSchema: Schema[com.agoda.content.models.db.information.Contact] = Schema.derived

  implicit lazy val AddressSchema: Schema[com.agoda.content.models.db.information.Address] = Schema.derived

  implicit lazy val LocalSchema: Schema[com.agoda.content.models.db.information.Local] = Schema.derived

  implicit lazy val LocaleBlockCountriesSchema: Schema[com.agoda.content.models.LocaleBlockCountries] = Schema.derived

  implicit lazy val ImportantNotesSchema: Schema[com.agoda.content.models.ImportantNotes] = Schema.derived

  implicit lazy val NotesLocalizationsSchema: Schema[com.agoda.content.models.db.information.NotesLocalizations] =
    Schema.derived

  implicit lazy val DescriptionsLocalizationsSchema
      : Schema[com.agoda.content.models.db.information.DescriptionsLocalizations] =
    Schema.derived

  implicit lazy val InformationLocalizationsSchema
      : Schema[com.agoda.content.models.db.information.InformationLocalizations] =
    Schema.derived

  implicit lazy val ContractInfoSchema: Schema[com.agoda.content.models.db.information.ContractInfo] = Schema.derived

  implicit lazy val BreakfastInfoSchema: Schema[com.agoda.content.models.db.information.BreakfastInfo] = Schema.derived

  implicit lazy val RestaurantOnSiteSchema: Schema[com.agoda.content.models.db.information.RestaurantOnSite] =
    Schema.derived

  implicit lazy val CoffeeInfoSchema: Schema[com.agoda.content.models.db.information.CoffeeInfo] = Schema.derived

  implicit lazy val TimeInformationSchema: Schema[com.agoda.content.models.db.information.TimeInformation] =
    Schema.derived

  implicit lazy val ContentCheckInInformationSchema
      : Schema[com.agoda.content.models.db.information.CheckInInformation] =
    Schema.derived.name(Schema.SName("com.agoda.content.models.db.information.ContentCheckInInformation"))

  implicit lazy val NhaInformationSchema: Schema[com.agoda.content.models.db.information.NhaInformation] =
    Schema.derived

  implicit lazy val CertificateSchema: Schema[com.agoda.content.models.db.information.Certificate] = Schema.derived

  implicit lazy val StaffVaccinationInfoSchema: Schema[com.agoda.content.models.db.information.StaffVaccinationInfo] =
    Schema.derived

  implicit lazy val SustainabilityPracticeSchema
      : Schema[com.agoda.content.models.db.information.SustainabilityPractice] =
    Schema.derived

  implicit lazy val SustainabilityCategorySchema
      : Schema[com.agoda.content.models.db.information.SustainabilityCategory] =
    Schema.derived

  implicit lazy val SustainabilityInfoSchema: Schema[com.agoda.content.models.db.information.SustainabilityInfo] =
    Schema.derived

  implicit lazy val NightStayInfoSchema: Schema[com.agoda.content.models.db.information.NightStayInfo] =
    Schema.derived

  implicit lazy val informationResponseSchema: Schema[com.agoda.content.models.db.information.InformationResponse] =
    Schema.derived

  @deprecated("companyTraceabilityInfo is replaced with companyTraceabilityData")
  implicit lazy val companyTraceabilityInfoSchema
      : Schema[com.agoda.content.models.db.information.CompanyTraceabilityInfo] = Schema.derived

  implicit lazy val companyTraceabilityDataSchema
      : Schema[com.agoda.content.models.db.information.CompanyTraceabilityData] = Schema.derived

  implicit lazy val CompanyTraceabilityAddressInfoSchema
      : Schema[com.agoda.content.models.db.information.CompanyTraceabilityAddressInfo] = Schema.derived

  implicit lazy val LocalizationResponseSchema: Schema[transformers.LocalizationResponse] = Schema.derived

  implicit lazy val ResponseStateTokenSchema: Schema[models.starfruit.PropertyPricingJsonProtocol.ResponseStateToken] =
    Schema.derived

  implicit lazy val DFMetaResultSchema: Schema[transformers.DFMetaResult] = Schema.derived

  implicit lazy val SearchStatusSchema: Schema[com.agoda.core.search.models.enumeration.SearchStatus] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.core.search.models.enumeration.SearchStatus")),
      validator = Validator.enumeration(
        com.agoda.core.search.models.enumeration.SearchStatuses.values.toList,
        s => Some(s.entryName)
      )
    )

  implicit lazy val SearchCriteriaSchema: Schema[com.agoda.core.search.models.response.SearchCriteria] =
    Schema.derived.name(Schema.SName("com.agoda.core.search.models.response.CoreSearchCriteria"))

  implicit lazy val SearchStatusAndCriteriaSchema
      : Schema[com.agoda.core.search.models.response.SearchStatusAndCriteria] = Schema.derived

  implicit lazy val PollingInfoResponseSchema: Schema[com.agoda.core.search.models.response.PollingInfoResponse] =
    Schema.derived

  implicit lazy val GrowthProgramInfoSchema: Schema[GrowthProgramInfo] = Schema.derived

  implicit lazy val propertiesSchema: Schema[transformers.Properties] = Schema.derived

  implicit lazy val NearbyRestaurantSchema: Schema[com.agoda.content.models.db.information.NearbyRestaurant] =
    Schema.derived

  implicit lazy val DsaComplianceInfoSchema: Schema[com.agoda.content.models.db.information.DsaComplianceInfo] =
    Schema.derived

  implicit lazy val ReviewTagRatingTypeSchema
      : Schema[com.agoda.content.models.propertycontent.enums.ReviewTagRatingType] =
    Schema(
      schemaType = SchemaType.SString(),
      name = Some(SName("com.agoda.content.models.propertycontent.enums.ReviewTagRatingType")),
      validator = Validator.enumeration(
        com.agoda.content.models.propertycontent.enums.ReviewTagRatingType.values.toList,
        s => Some(s.entryName)
      )
    )

}
