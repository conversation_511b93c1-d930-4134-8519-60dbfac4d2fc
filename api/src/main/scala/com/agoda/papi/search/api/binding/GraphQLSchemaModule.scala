package com.agoda.papi.search.api.binding

import akka.actor.ActorSystem
import scaldi.Module
import com.agoda.common.graphql.config.RemoteSchemaConfigs
import com.agoda.common.graphql.query.{
  Cached<PERSON>ueryParser,
  PreProcessQueryValidator,
  PreProcessQueryValidatorImpl,
  QueryValidatorSettings
}
import com.agoda.common.graphql.schema.comparator.{ SchemaComparator, SchemaTypeComparator }
import com.agoda.common.graphql.schema.loader.{ ScheduledSchemaLoader, SchemaLoader }
import com.agoda.common.graphql.schema.source.SchemaSourceBuilder
import com.agoda.common.graphql.schema.validator.{ DefaultSchemaChangeValidator, SchemaChangeValidator }
import com.agoda.common.graphql.schema.{ DefaultTypeMerger, Merger }
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.logging.metrics.MetricsReporter
import com.typesafe.config.ConfigFactory
import org.asynchttpclient.AsyncHttpClient
import sttp.client3.SttpBackend
import sttp.client3.asynchttpclient.future.AsyncHttpClientFutureBackend
import com.github.blemale.scaffeine.{ Cache, Scaffeine }

import scala.concurrent.{ ExecutionContext, Future }
import scala.concurrent.duration._
import scala.util.Try
import com.agoda.papi.search.complexsearch.configuration.ConfigExtension._
import com.agoda.papi.search.api.http.graphql.schema.StaticSchema
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.util.configuration.QueryCacheSetting
import com.agoda.papi.search.common.util.context.ExecutionContexts
import sangria.ast.Document

class GraphQLSchemaModule extends Module {

  implicit val defaultEC: ExecutionContext = ExecutionContexts.defaultContext
  private val configs                      = ConfigFactory.load()

  bind[RemoteSchemaConfigs] to RemoteSchemaConfigs.apply(configs)
  (bind[SttpBackend[Future, Any]] to AsyncHttpClientFutureBackend.usingClient(inject[AsyncHttpClient]))
    .identifiedBy('schemaSttpBackend)

  val schemaReloadTime: FiniteDuration =
    Try(configs.getFiniteDuration("graphql.schema-reload-time")).toOption.getOrElse(180.seconds)
  val timeoutForFetchingSchema: FiniteDuration =
    Try(configs.getFiniteDuration("graphql.timeout-for-fetching-schema")).toOption.getOrElse(5.seconds)

  bind[SchemaChangeValidator] to new DefaultSchemaChangeValidator
  bind[SchemaComparator[GraphQLContext, Unit]] to new SchemaTypeComparator[GraphQLContext, Unit]
  bind[Merger] to new DefaultTypeMerger()

  private lazy val schemaSources = SchemaSourceBuilder.build(
    inject[RemoteSchemaConfigs],
    inject[Map[String, ServiceMesh]]("consulServiceMeshes")
  )(defaultEC, inject[MetricsReporter], inject[SttpBackend[Future, Any]]("schemaSttpBackend"))

  bind[SchemaLoader[GraphQLContext, Unit]] to new ScheduledSchemaLoader[GraphQLContext, Unit](
    schemaSources,
    inject[SchemaChangeValidator],
    StaticSchema.schema,
    StaticSchema.builder,
    inject[SchemaComparator[GraphQLContext, Unit]],
    inject[Merger],
    // Schema reload duration in seconds
    schemaReloadTime,
    // Timeout for fetching schema in seconds
    timeoutForFetchingSchema
  )(defaultEC, inject[MetricsReporter], inject[ActorSystem])

  val queryCacheSettings = QueryCacheSetting.apply(configs)

  bind[Cache[String, Document]] to Scaffeine()
    .expireAfterAccess(queryCacheSettings.ttl)
    .maximumSize(queryCacheSettings.maxSize)
    .initialCapacity(queryCacheSettings.minSize)
    .build[String, Document]()

  bind[CachedQueryParser] to new CachedQueryParser(inject[Cache[String, Document]])
  bind[PreProcessQueryValidator] to new PreProcessQueryValidatorImpl(QueryValidatorSettings.fromConfig(configs))

}
