package com.agoda.papi.search.api.binding

import akka.actor.{ ActorSystem, Scheduler }
import akka.http.scaladsl.server.Directive0
import com.agoda.common.graphql.query.{ C<PERSON>d<PERSON><PERSON>y<PERSON><PERSON><PERSON>, PreProcessQueryValidator }
import com.agoda.common.graphql.schema.loader.SchemaLoader
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.papi.search.api.config.SsrDelayInjectConfig
import com.agoda.papi.search.api.http.graphql.GraphQLRoute
import com.agoda.papi.search.api.http.graphql.resolver.ResolverComposer
import com.agoda.papi.search.api.util.directive.{ PiiEncryptionDirective, PiiFilterDirective }
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.service.{ PiiEncryptionService, RequestSegmentMetricsGenerator, WhiteLabelService }
import com.agoda.papi.search.common.util.context.GlobalContextService
import com.agoda.papi.search.common.util.logger.{ HadoopLogger, PAPIAppLogger }
import com.agoda.papi.search.complexsearch.configuration.PiiEncryptionSettings
import com.agoda.papi.search.complexsearch.graphql.resolver.{
  PriceTrendSearchResolver,
  PropertyAllotmentSearchResolver,
  ResolverLevel1,
  ResolverLevel2,
  ResolverLevel3,
  ResolverLevel4
}
import com.agoda.papi.search.complexsearch.service.{
  DataFetcher,
  PriceTrendSummaryDataFetcher,
  PropertyAllotmentSummaryDataFetcher
}
import com.agoda.papi.search.internalmodel.configuration.{ PAPIMessageSettings, TopBookedCitiesSetting }
import com.agoda.papi.search.property.graphql.resolver.{ PropertyDetailResolverLevel1, PropertySummaryResolverLevel1 }
import com.agoda.papi.search.property.service.datafetcher.{ PropertyDetailsDataFetcher, PropertySummaryDataFetcher }
import com.typesafe.config.{ Config, ConfigFactory }
import io.opentracing.Tracer
import scaldi.Module

import scala.concurrent.ExecutionContext
import scala.util.Try

class GraphQLApiModule extends Module with PAPIAppLogger {

  private val configs = ConfigFactory.load()

  bind[Scheduler] to inject[ActorSystem].scheduler
  bind[ResolverLevel1] to injected[ResolverLevel1]
  bind[ResolverLevel2] to injected[ResolverLevel2]
  bind[ResolverLevel3] to injected[ResolverLevel3]
  bind[ResolverLevel4] to injected[ResolverLevel4]
  bind[PropertyDetailResolverLevel1] to injected[PropertyDetailResolverLevel1]
  bind[PropertySummaryResolverLevel1] to injected[PropertySummaryResolverLevel1]
  bind[PropertyAllotmentSearchResolver] to injected[PropertyAllotmentSearchResolver]
  bind[PriceTrendSearchResolver] to injected[PriceTrendSearchResolver]
  bind[ResolverComposer] to injected[ResolverComposer]

  bind[GraphQLRoute] to new GraphQLRoute {
    override val ec: ExecutionContext = inject[ExecutionContext]

    override val schemaLoader: SchemaLoader[GraphQLContext, Unit] =
      try
        inject[SchemaLoader[GraphQLContext, Unit]]
      catch {
        case t: Throwable =>
          errorLog(Components.PAPI, "!!!! shut down server - can't run schemaLoader !!!", Some(t))
          sys.exit(1)
      }

    override val resolverComposer: ResolverComposer                = inject[ResolverComposer]
    override val dataFetcher                                       = inject[DataFetcher]
    override val propertyDetailsDataFetcher                        = inject[PropertyDetailsDataFetcher]
    override val hotelPriceDataFetcher: PropertySummaryDataFetcher = inject[PropertySummaryDataFetcher]
    override val metricsReporter: MetricsReporter                  = inject[MetricsReporter]
    override val tracer: Tracer                                    = inject[Tracer]
    override val hadoopLogger                                      = inject[HadoopLogger]
    override val globalContextService: GlobalContextService        = inject[GlobalContextService]
    override val searchExperimentManagerService: SearchExperimentManagerService =
      inject[SearchExperimentManagerService]
    override val cachedQueryParser: CachedQueryParser               = inject[CachedQueryParser]
    override val preProcessQueryValidator: PreProcessQueryValidator = inject[PreProcessQueryValidator]
    override val webGateApiKey: String =
      Try(configs.getString("webgateApiKey")).getOrElse("63ee62a3-69a1-4c5f-9921-ab599bfbb19b")
    override val maxQueryDepth: Int = Try(configs.getInt("graphql.max-query-depth")).getOrElse(200)
    override val topBookedCitiesSetting: TopBookedCitiesSetting             = inject[TopBookedCitiesSetting]
    override val papiMessageSettings: PAPIMessageSettings                   = inject[PAPIMessageSettings]
    override val priceTrendSummaryDataFetcher: PriceTrendSummaryDataFetcher = inject[PriceTrendSummaryDataFetcher]
    override val propertyAllotmentSummaryDataFetcher: PropertyAllotmentSummaryDataFetcher =
      inject[PropertyAllotmentSummaryDataFetcher]

    override val handlePiiHeaders: Directive0 = inject[PiiEncryptionSettings].key match {
      case Some(x) => new PiiEncryptionDirective(PiiEncryptionService(x))
      case _       => new PiiFilterDirective()
    }

    override val piiEncryptionService: Option[PiiEncryptionService]             = inject[Option[PiiEncryptionService]]
    override val requestSegmentMetricsGenerator: RequestSegmentMetricsGenerator = inject[RequestSegmentMetricsGenerator]
    override val whiteLabelService: WhiteLabelService                           = inject[WhiteLabelService]
    override val scheduler: Scheduler                                           = inject[Scheduler]
    override val injectSsrDelayConfig: SsrDelayInjectConfig =
      SsrDelayInjectConfig.fromConsul(inject[State[Config]])
  }

}
