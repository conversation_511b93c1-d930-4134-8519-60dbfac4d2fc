package com.agoda.papi.search.api.http.graphql

import akka.http.scaladsl.model.{ AttributeKey => AkkaAttribute<PERSON>ey, _ }
import akka.http.scaladsl.model.headers.Accept
import akka.http.scaladsl.server.Directives._
import akka.http.scaladsl.server.{ Directive0, Route }
import com.agoda.common.graphql.exception.GQLArgumentException
import com.agoda.common.graphql.model.GQLSerializer._
import com.agoda.common.graphql.model.Query
import com.agoda.common.graphql.query.{ CachedQueryParser, PreProcessQueryValidator, QueryUtil }
import com.agoda.common.graphql.util.{ Constants, ErrorFormatter }
import com.agoda.commons.http.server.akka.directives.ServiceDirectives
import com.agoda.commons.http.server.circe.Implicits._
import com.agoda.commons.http.server.measurements.MetricsContext
import com.agoda.commons.http.server.models.{ ClientRequestContext, RequestContext }
import com.agoda.commons.tracing.core.TracerOps._
import com.agoda.papi.search.api.http.graphql.GraphQLRoute._
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.util.{ MetricsContextHelper, PackageMetricHelper }
import com.agoda.papi.search.common.util.circuitbreaker.CircuitBreakerWrapper
import com.agoda.papi.search.common.util.context.{
  ExecutionContexts,
  ExperimentContext,
  GlobalContextService,
  PiiContext,
  WithCorrelationId
}
import com.agoda.papi.search.common.util.exception.{
  BasePAPIException,
  DataFetcherException,
  ExceptionSanitizer,
  GQLException,
  InvalidRequestException
}
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementName, MeasurementTag, MetricsHelper }
import com.agoda.platform.service.context.GlobalContext
import com.agoda.platform.service.context.akka.GlobalContextDirectives
import com.agoda.papi.search.complexsearch.queryexample.QueryExamples
import QueryExamples._
import akka.actor.Scheduler
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.papi.search.api.util.directive.UnencryptedPiiHeaders
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.service.RequestSegmentMetricsGenerator
import de.heikoseeberger.akkahttpcirce.FailFastCirceSupport._
import io.circe.{ Json, Printer }
import io.opentelemetry.api.common.AttributeKey
import sangria.ast.Document
import sangria.execution._
import sangria.marshalling.circe._
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.request.SearchContext
import com.agoda.papi.search.api.config.SsrDelayInjectConfig
import com.agoda.papi.search.api.http.graphql.DelayInjector.isSsrSearchType
import com.agoda.papi.search.common.externaldependency.logger.message.PAPISearchLatencyMessage

import scala.collection.immutable
import scala.concurrent.Future
import scala.concurrent.duration.FiniteDuration
//TODO: To be replaced by ag-httpserver
import ch.megard.akka.http.cors.scaladsl.CorsDirectives._

object GraphQLRoute {

  object GQLResponse {
    def ok(json: Json): GQLResponse = GQLResponse(json, success = true, status = None)
    def error(json: Json, status: String, statusCode: Int, error: Option[Throwable] = None): GQLResponse =
      GQLResponse(json, success = false, status = Some(status), statusCode, error)
  }

  case class GQLResponse(
      json: Json,
      success: Boolean,
      status: Option[String],
      statusCode: Int = 200,
      error: Option[Throwable] = None
  )

}

/** <AUTHOR>   mkhan
  */

abstract class GraphQLRoute
    extends GraphQLExecutor with ServiceDirectives with GlobalContextDirectives with MetricsHelper with PAPIAppLogger
      with DelayInjector {

  val webGateApiKey: String
  val maxQueryDepth: Int
  val globalContextService: GlobalContextService
  val cachedQueryParser: CachedQueryParser
  val preProcessQueryValidator: PreProcessQueryValidator
  val scheduler: Scheduler
  val injectSsrDelayConfig: SsrDelayInjectConfig

  def isExternalRequest(globalContext: GlobalContext): Option[Boolean] =
    for {
      callerAPIKey <- globalContext.getCallerAPIKey
      origin       <- globalContext.getOrigin
    } yield (callerAPIKey == webGateApiKey && origin != "A1")

  def fetchQueryReducer(globalContext: GlobalContext): List[QueryReducer[GraphQLContext, GraphQLContext]] =
    isExternalRequest(globalContext) match {
      case Some(true) =>
        List(
          QueryReducer.rejectMaxDepth[GraphQLContext](maxQueryDepth),
          QueryReducer.rejectIntrospection[GraphQLContext]()
        )
      case _ => QueryReducer.rejectMaxDepth[GraphQLContext](maxQueryDepth) :: Nil
    }

  def executeGraphQL(
      ast: Document,
      query: Query,
      globalContext: GlobalContext,
      piiContext: PiiContext,
      httpRequest: HttpRequest,
      firstTopLevelQueryField: String
  )(implicit requestContext: RequestContext, clientRequestContext: ClientRequestContext): Future[GQLResponse] = {
    implicit val metricsContext = requestContext.metrics

    val gqlResult =
      measureFuture(MeasurementName.ExecuteQuery) {
        execute(
          ast,
          query,
          globalContext,
          piiContext,
          queryReducers = fetchQueryReducer(globalContext),
          degradeMode = clientRequestContext.degradeMode,
          httpRequest,
          firstTopLevelQueryField
        )
      }

    gqlResult
      .map(GQLResponse.ok)
      .recover {
        case error: QueryAnalysisError ⇒ GQLResponse.error(error.resolveError, "", 400, Some(error))
        case error: ErrorWithResolver  ⇒ GQLResponse.error(error.resolveError, "", 500, Some(error))
        case error: Throwable         => fetchGQLResponse(error)
      }
  }

  def fetchGQLResponse(e: Throwable): GQLResponse =
    e match {
      case e: DataFetcherException =>
        e.getInnerCause match {
          case ie: InvalidRequestException =>
            GQLResponse.error(ErrorFormatter.formatError(ie), "query-handle", 400, Some(ie))
          case ie: Throwable => GQLResponse.error(ErrorFormatter.formatError(ie), "query-handle", 500, Some(ie))
        }
      case e: GQLArgumentException => GQLResponse.error(ErrorFormatter.formatError(e), "query-handle", 400, Some(e))
      case e: Throwable            => GQLResponse.error(ErrorFormatter.formatError(e), "query-handle", 500, Some(e))
    }

  def badRequestResponse(json: Json, globalContext: GlobalContext): Route =
    if (globalContext.isDebug.getOrElse(false)) {
      complete(
        HttpResponse(
          StatusCodes.BadRequest,
          entity = HttpEntity(ContentTypes.`application/json`, Printer.noSpaces.print(json).getBytes)
        )
      )
    } else {
      complete(StatusCodes.BadRequest -> "Missing required headers")
    }

  def gqlRoute(implicit requestContext: RequestContext): Route = {
    implicit val clientRequestContext = requestContext.client
    extractRequest { httpRequest =>
      pathPrefix(Constants.GQL_ROUTE) {
        cors() {
          post {
            UnencryptedPiiHeaders { unencryptedHeaders =>
              handlePiiHeaders {
                withRequest[Query](toDeserializer[Query], requestContext) { query: Query =>
                  activateTrace {
                    withGlobalContext { globalContext =>
                      MetricsContextHelper.setMetricsContext(globalContext, requestContext)
                      val contextValidation =
                        if (query.operationName.getOrElse("").toLowerCase == "introspectionquery") {
                          Right(globalContext)
                        } else {
                          validateContext(globalContext)
                        }
                      contextValidation match {
                        case Left(errorJson) =>
                          MetricsContextHelper.setMetricsContext(globalContext, requestContext)
                          badRequestResponse(errorJson, globalContext)
                        case Right(ctx) =>
                          import cats.instances.future._
                          val updatedContext = globalContextService.prepareContext(ctx)
                          MetricsContextHelper.setMetricsContext(updatedContext, requestContext)
                          val queryParse = measure(MeasurementName.ParseQuery) {
                            cachedQueryParser.parse(query.query)
                          }
                          queryParse match {
                            case Right(ast) =>
                              preProcessQueryValidator.validate(ast) match {
                                case None =>
                                  tracer.withSpan("execute gql") {
                                    gqlComplete(
                                      {
                                        val firstTopLevelQueryField =
                                          QueryUtil.getTopLevelFields(ast).headOption.getOrElse("")
                                        requestContext.metrics.setTag(
                                          MeasurementTag.QueryField,
                                          firstTopLevelQueryField
                                        )
                                        val piiContext =
                                          PiiContext(updatedContext.getRawAgHeaders ++ unencryptedHeaders)
                                        val resultF = executeGraphQL(
                                          ast,
                                          query,
                                          updatedContext,
                                          piiContext,
                                          httpRequest,
                                          firstTopLevelQueryField
                                        )

                                        logGQLFailureRequest(resultF, firstTopLevelQueryField)(
                                          requestContext.metrics,
                                          updatedContext,
                                          query,
                                          ast
                                        )

                                        resultF
                                      },
                                      unencryptedHeaders
                                    )(requestContext, updatedContext, query, ast)
                                  }
                                case Some(errorMsg) =>
                                  badRequestResponse(ErrorFormatter.formatError(errorMsg.message), updatedContext)
                              }
                            case Left(error) => badRequestResponse(ErrorFormatter.formatError(error), updatedContext)
                          }
                      }
                    }
                  }
                }
              }
            }
          } ~ get {
            withGlobalContext { globalContext =>
              complete(
                executeGraphQL(
                  sangria.introspection.introspectionQuery,
                  Query(query = "introspection", operationName = None, variables = Json.obj()),
                  globalContext,
                  PiiContext(),
                  httpRequest,
                  "introspection"
                ).map(_.json)
              )
            }
          }

        }
      }
    }
  }

  private def logGQLFailureRequest(
      resultF: => Future[GQLResponse],
      firstTopLevelQueryField: String
  )(implicit mc: MetricsContext, globalContext: GlobalContext, query: Query, doc: Document): Unit =
    resultF.map { result =>
      result.error match {
        case Some(err) =>
          val (innerException, exceptionComponent) = if (err.isInstanceOf[BasePAPIException]) {
            val papiException = err.asInstanceOf[BasePAPIException]
            (papiException.getInnerCause(), papiException.getComponent())
          } else {
            (err, Components.PAPI)
          }

          error(
            exceptionComponent,
            s"PAPI returned Http ${result.statusCode}",
            Some(innerException),
            globalContext.getRawAgHeaders ++ Map(
              "SEARCH_TYPE" -> s"gql/$firstTopLevelQueryField",
              "SERVICE"     -> exceptionComponent.toString,
              "STATUS_CODE" -> result.statusCode.toString
            )
          )(new WithCorrelationId {
            override def getCorrelationId: String = globalContext.getCorrelationId.getOrElse(DEFAULT_PAPI_SEARCH_ID)
          })
        case _ =>
          ErrorFormatter.getErrors(result.json).foreach { errorMsg =>
            error(
              Components.PAPI,
              s"PAPI returned Http ${result.statusCode}",
              Some(new GQLException(errorMsg)),
              globalContext.getRawAgHeaders ++ Map(
                "SEARCH_TYPE"    -> s"gql/$firstTopLevelQueryField",
                "query"          -> query.query,
                "variable"       -> query.variables.noSpaces,
                "toplevelfields" -> firstTopLevelQueryField
              )
            )(new WithCorrelationId {
              override def getCorrelationId: String = globalContext.getCorrelationId.getOrElse(DEFAULT_PAPI_SEARCH_ID)
            })
          }
      }
    }(ExecutionContexts.defaultContext)

  private def handleInternalServerError(result: GQLResponse, mc: MetricsContext): Unit =
    result.error.foreach { err =>
      mc.setAttribute(AttributeKey.stringKey(MeasurementTag.ErrorType), ExceptionSanitizer.getErrorType(err))
    }

  private def gqlComplete(
      resultF: => Future[GQLResponse],
      unencryptedHeaders: Map[String, String]
  )(implicit
      requestContext: RequestContext,
      globalContext: GlobalContext,
      query: Query,
      doc: Document
  ): Route = {
    implicit val mc: MetricsContext = requestContext.metrics
    onSuccess(resultF) { result =>
      result.statusCode match {
        case 500 =>
          handleInternalServerError(result, mc)
          complete(
            HttpResponse(
              StatusCodes.InternalServerError,
              entity = HttpEntity(ContentTypes.`application/json`, Printer.noSpaces.print(result.json).getBytes)
            )
          )

        case 400 =>
          complete(
            HttpResponse(
              StatusCodes.BadRequest,
              entity = HttpEntity(ContentTypes.`application/json`, Printer.noSpaces.print(result.json).getBytes)
            )
          )

        case 200 if injectSsrDelayConfig.enable.get().get() && DelayInjector.isSsrSearchType(query.query) =>
          val resultWithDelayF =
            injectDelayWithMeasurement(result, unencryptedHeaders, requestContext, globalContext, mc)
          complete(resultWithDelayF)
        case _ => complete(result.json)
      }
    }
  }

  private def validateContext(globalContext: GlobalContext): Either[Json, GlobalContext] = {
    import cats.syntax.either._
    globalContextService.validateContext(globalContext).leftMap { list =>
      Json.obj(
        "errors" -> Json.arr(
          Json.obj(
            "message" -> Json.fromString("Missing fields in header"),
            "fields"  -> Json.arr(list.map(Json.fromString).toList: _*)
          )
        )
      )
    }
  }

  def explicitlyAccepts(mediaType: MediaType): Directive0 =
    headerValuePF {
      case Accept(ranges) if ranges.exists(range ⇒ !range.isWildcard && range.matches(mediaType)) ⇒ ranges
    }.flatMap(_ ⇒ pass)

  val gqlDocRoute: Route =
    get {
      pathPrefix(Constants.GQL_ROUTE_DOC) {
        (pathEndOrSingleSlash & redirectToTrailingSlashIfMissing(StatusCodes.TemporaryRedirect)) {
          getFromResource("graphql/index.html")
        } ~ {
          getFromResourceDirectory("graphql")
        }
      } ~
        pathPrefix("queryExamples") {
          complete(QueryExamples.queryExamples)
        }
    } ~
      (post & pathPrefix(Constants.GQL_ROUTE_DOC)) {
        // Added to redirect post request at /graphiql route to graphql (playground sends request on the same url)
        redirect("/" + Constants.GQL_ROUTE, StatusCodes.PermanentRedirect)
      }

}
