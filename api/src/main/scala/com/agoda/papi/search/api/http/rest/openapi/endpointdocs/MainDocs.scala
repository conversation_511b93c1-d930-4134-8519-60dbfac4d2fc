package com.agoda.papi.search.api.http.rest.openapi.endpointdocs

import akka.http.scaladsl.server.Route
import sttp.apispec.openapi.OpenAPI
import sttp.apispec.openapi.circe.yaml.RichOpenAPI
import sttp.tapir.docs.openapi.{ OpenAPIDocsInterpreter, OpenAPIDocsOptions }
import sttp.tapir.server.akkahttp.AkkaHttpServerInterpreter
import sttp.tapir.swagger.SwaggerUI

import scala.collection.mutable
import scala.concurrent.Future

class MainDocs(
    cancellationPolicyDoc: CancellationPolicyDoc,
    propertyDocs: PropertyDoc,
    bookingDocs: BookingDoc
)(implicit ec: scala.concurrent.ExecutionContext) {

  private val usedNames = mutable.Set[String]()

  private val openAPIDocsOptionsStrictUniqueName = OpenAPIDocsOptions.default.copy(
    schemaName = schemaName => {
      val managedNameWithTypeParameter = schemaName.fullName
        .split('.')
        .lastOption
        .map { name =>
          if (schemaName.typeParameterShortNames.isEmpty) {
            name
          } else {
            name + "_" + schemaName.typeParameterShortNames.mkString("_")
          }
        }
      managedNameWithTypeParameter match {
        case Some(name) =>
          if (usedNames.contains(name)) {
            throw new IllegalArgumentException(s"Duplicate schema name detected: $name")
          } else {
            usedNames.add(name)
            name
          }
        case None => throw new IllegalArgumentException(s"No name found for schema: ${schemaName.fullName}")
      }
    }
  )

  val docs: OpenAPI = OpenAPIDocsInterpreter(openAPIDocsOptionsStrictUniqueName)
    .toOpenAPI(
      List(
        cancellationPolicyDoc.cancellationPolicyEndpointDoc.tag("Property"),
        propertyDocs.propertyEndpointDoc.tag("Property"),
        bookingDocs.bookingEndpointDoc.tag("Property")
      ),
      "PAPI Search OpenAPI",
      "1.0"
    )
    .openapi("3.0.3")

  val openAPIdocRoute: Route = AkkaHttpServerInterpreter().toRoute(SwaggerUI[Future](docs.toYaml3_0_3))
}
