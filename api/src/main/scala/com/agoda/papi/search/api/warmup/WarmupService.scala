package com.agoda.papi.search.api.warmup

import com.agoda.commons.http.client.v2.settings.HttpClientSettingsBuilder
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.papi.search.api.warmup.PropertyRequestGenerator._
import com.agoda.property.client.PAPISearchAgHttpClient
import com.typesafe.config.{ Config, ConfigFactory }
import com.typesafe.scalalogging.LazyLogging
import org.slf4j.helpers.NOPLoggerFactory
import request.PropertyRequest
import sttp.client3._
import sttp.client3.asynchttpclient.future.AsyncHttpClientFutureBackend
import com.agoda.papi.search.common.util.concurrent.FutureUtil._

import scala.concurrent.{ ExecutionContext, Future }
import scala.util.Random

class WarmupService(settings: WarmupSettings) extends LazyLogging {

  private object BatchResult {
    def combine(a: BatchResult, b: BatchR<PERSON>ult): BatchResult =
      BatchResult(a.successful + b.successful, a.sent + b.sent, 0L)
  }

  private case class BatchResult(successful: Int, sent: Int, batchDuration: Long) {
    lazy val successRate: Int = (100 * successful) / sent
  }

  private case class WarmupState(total: BatchResult, batches: Vector[BatchResult]) {
    def lastN(n: Int): BatchResult = batches.takeRight(n).foldLeft(BatchResult(0, 0, 0))(BatchResult.combine)
  }

  private[api] val appConfig: Config = ConfigFactory.load()

  def doWarmUp(implicit ec: ExecutionContext): Future[WarmupResult] =
    if (settings.enabled) {
      val startTime         = System.currentTimeMillis()
      val (client, backend) = createPAPIClient
      logger.info(
        s"Warming up PAPI: sending increasing parallel request for at least ${settings.minimumIterations} attempts up to ${settings.timeouts}"
      )

      val resultWithBatchSuccessRatesF = warmupWhile(
        startTime,
        client,
        state =>
          state.batches.size >= settings.minimumIterations && state
            .lastN(settings.minimumIterations)
            .successRate >= settings.targetSuccessRate
      )(WarmupState(BatchResult(0, 0, 0), Vector.empty))

      val resultWithSuccessRatesF = resultWithBatchSuccessRatesF.map { state =>
        val overallSuccessRate   = state.total.successRate
        val lastBatchSuccessRate = state.batches.last.successRate

        val warmupDuration = System.currentTimeMillis() - startTime
        logger.info(
          s"Warming up completed in $warmupDuration ms; " +
            s"last batch success rate: $lastBatchSuccessRate%, overall success rate: $overallSuccessRate%"
        )

        WarmupResult(overallSuccessRate, lastBatchSuccessRate, warmupDuration)
      }

      resultWithSuccessRatesF.onComplete { _ =>
        backend.close()
      }
      resultWithSuccessRatesF
    } else {
      logger.info(s"Warming up is skipped")
      Future.successful(WarmupResult(100, 100, 0))
    }

  private def warmupWhile(startTime: Long, client: PAPISearchAgHttpClient[Future], done: WarmupState => Boolean)(
      state: WarmupState
  )(implicit ec: ExecutionContext): Future[WarmupState] = {
    val batchStart = System.currentTimeMillis()
    val iteration  = state.batches.size + 1
    val batchF     = sendWarmupBatch(client, iteration)
    batchF.allSuccessful.flatMap { result =>
      val batchResult = BatchResult(result.size, batchF.size, System.currentTimeMillis() - batchStart)
      val newState    = WarmupState(BatchResult.combine(batchResult, state.total), state.batches :+ batchResult)
      logger.info(
        s"$iteration) " +
          s"${batchResult.successRate}% of ${batchResult.sent} requests in ${batchResult.batchDuration}ms " +
          s"(Last ${settings.minimumIterations} batches: ${newState.lastN(settings.minimumIterations).successRate}%, Overall: ${newState.total.successRate}%)"
      )

      val exceedsTimeout = (System.currentTimeMillis() - startTime) >= settings.timeouts.toMillis

      if (done(newState) || exceedsTimeout) {
        Future.successful(newState)
      } else {
        warmupWhile(startTime, client, done)(newState)
      }
    }
  }

  private def sendWarmupBatch(client: PAPISearchAgHttpClient[Future], i: Int) = {
    val batchResultFs =
      1.to(settings.iterationConcurrency.getOrElse(i, settings.iterationConcurrencyDefault)).toList.map { _ =>
        sendWarmupRequest(client)
      }
    batchResultFs
  }

  private def sendWarmupRequest(client: PAPISearchAgHttpClient[Future]) =
    // in future we'll add other types of requests here and and the random is uniformly distributed, it'll guarantee that all types of requests are sent
    Random.nextInt(1) match {
      case 0 =>
        val propertyRequest = randomRequest
        client.getPropertyResponse(propertyRequest)
    }

  private def randomRequest: PropertyRequest = {
    val hotelId         = settings.hotels(Random.nextInt(settings.hotels.size))
    val propertyRequest = generatePropertyRequest
    // need to change the checkin date
    propertyRequest.copy(context = propertyRequest.context.copy(propertyIds = List(hotelId)))
  }

  private def createPAPIClient(implicit
      ec: ExecutionContext
  ): (PAPISearchAgHttpClient[Future], SttpBackend[Future, Any]) = {
    import cats.instances.future._
    val localInstanceMesh = ServiceMesh(
      ServiceMeshSettingsBuilder(
        "local-papi",
        appConfig
      ).build()
    )
    val clientSettings = HttpClientSettingsBuilder("local-papi").withLoggerFactory(new NOPLoggerFactory()).build()
    implicit val httpBackend: SttpBackend[Future, Any] = AsyncHttpClientFutureBackend()
    (new PAPISearchAgHttpClient[Future](localInstanceMesh, clientSettings), httpBackend)
  }

}
