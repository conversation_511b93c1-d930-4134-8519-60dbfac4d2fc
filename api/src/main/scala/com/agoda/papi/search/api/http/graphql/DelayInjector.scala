package com.agoda.papi.search.api.http.graphql

import akka.actor.Scheduler
import akka.http.scaladsl.model.{ ContentTypes, HttpEntity, HttpResponse, StatusCodes }
import akka.util.ByteString
import io.opentelemetry.api.common.AttributeKey
import com.agoda.commons.http.server.measurements.MetricsContext
import com.agoda.commons.http.server.models.RequestContext
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.request.SearchContext
import com.agoda.papi.search.api.config.SsrDelayInjectConfig
import com.agoda.papi.search.api.http.graphql.DelayInjector.{ toHttpResponse, BufferMs, DefaultTtlMs }
import com.agoda.papi.search.api.http.graphql.GraphQLRoute.GQLResponse
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.util.context.{ ExperimentContext, GlobalContextService, PiiContext }
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag, MetricsHelper }
import com.agoda.platform.service.context.GlobalContext

import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ ExecutionContext, Future }

trait DelayInjector extends MetricsHelper {

  implicit val ec: ExecutionContext
  val injectSsrDelayConfig: SsrDelayInjectConfig
  val globalContextService: GlobalContextService
  val searchExperimentManagerService: SearchExperimentManagerService
  val scheduler: Scheduler
  val metricsReporter: MetricsReporter

  def injectDelayWithMeasurement(
      result: GQLResponse,
      unencryptedHeaders: Map[String, String],
      requestContext: RequestContext,
      globalContext: GlobalContext,
      mc: MetricsContext
  ): Future[HttpResponse] = {
    val piiContext  = PiiContext(globalContext.getRawAgHeaders ++ unencryptedHeaders)
    val expContextF = createExperimentContext(globalContext, piiContext, requestContext)

    val resultWithDelayF = expContextF.flatMap { expContext =>
      val response = measure(MeasurementKey.ssrSearchResultBytestring)(
        toHttpResponse(result)
      )

      val currentLatencyMs = requestContext.expiration.getProcessingTimeMicros / 1000L
      val limitNoBuffer    = requestContext.expiration.ttl.map(_.toMillis).getOrElse(DefaultTtlMs)
      val limitWithBuffer  = limitNoBuffer - BufferMs

      DelayInjector.setMetricAttributeForInjectDelayMeasurement(
        mc,
        expContext
      )

      val (delayToInject, unboundedDelay, delayPercent) =
        determineDelay(currentLatencyMs.toInt, expContext, limitWithBuffer.toInt)

      val exceedMaximumSsrDelay = unboundedDelay > delayToInject

      val allTags = DelayInjector.generateTagsForInjectDelayMeasurement(expContext, delayPercent, exceedMaximumSsrDelay)

      metricsReporter.report(
        MeasurementKey.ssrSearchResultLatencyBeforeDelayInjected,
        currentLatencyMs,
        tags = allTags
      )
      metricsReporter.report(
        MeasurementKey.ssrSearchResultLatencyDelayToInject,
        delayToInject,
        tags = allTags
      )
      metricsReporter.report(
        MeasurementKey.ssrSearchResultLatencyDelayToInjectUnbounded,
        unboundedDelay,
        tags = allTags
      )
      metricsReporter.report(
        MeasurementKey.ssrSearchResultLatencyLimit,
        limitWithBuffer,
        tags = allTags
      )

      val resultWithInjection = measureFuture(
        MeasurementKey.ssrSearchResultLatencyActualDelayInjected,
        tags = allTags
      ) {
        injectDelay(Future.successful(response), delayToInject)
      }

      searchExperimentManagerService.completeExperiment(expContext)

      resultWithInjection
    }

    resultWithDelayF
  }

  def createExperimentContext(
      globalContext: GlobalContext,
      piiContext: PiiContext,
      requestContext: RequestContext
  ): Future[ExperimentContext] = {
    val searchContext = SearchContext(
      userId = globalContext.getUserId,
      memberId = globalContext.getMemberId.getOrElse(0),
      locale = globalContext.getLanguageLocale.getOrElse(""),
      cid = globalContext.getCid.getOrElse(-1),
      origin = globalContext.getOrigin.getOrElse(""),
      platform = globalContext.getPlatformId.getOrElse(0),
      deviceTypeId = globalContext.getDeviceTypeId,
      whiteLabelKey = globalContext.getWhiteLabelKey
    )
    val newContext = globalContextService.updateSearchContext(searchContext, globalContext, piiContext)
    val experimentCtx =
      searchExperimentManagerService.createExperimentContext(newContext, globalContext, requestContext.experiments)
    experimentCtx
  }

  def injectDelay(httpResponse: Future[HttpResponse], delayInMS: Int): Future[HttpResponse] =
    if (delayInMS > 0) {
      akka.pattern.after(
        FiniteDuration(delayInMS, "millis"),
        scheduler
      )(httpResponse)
    } else {
      httpResponse
    }

  def determineDelayPercentage(expContext: ExperimentContext): Double =
    if (expContext.isBVariant(expContext.allocationContext.injectSsrDelay1)) {
      injectSsrDelayConfig.experiment1Percentage.get().get()
    } else if (expContext.isBVariant(expContext.allocationContext.injectSsrDelay2)) {
      injectSsrDelayConfig.experiment2Percentage.get().get()
    } else if (expContext.isBVariant(expContext.allocationContext.injectSsrDelay3)) {
      injectSsrDelayConfig.experiment3Percentage.get().get()
    } else {
      0d
    }

  def determineDelay(currentLatency: Int, expContext: ExperimentContext, limit: Int): (Int, Int, Double) = {
    val maxDelay = limit - currentLatency

    val delayPercentage = determineDelayPercentage(expContext)
    val unboundedDelay  = (currentLatency.toDouble * delayPercentage / 100.0d).toInt
    val delayToInject   = Math.min(unboundedDelay, maxDelay)

    (delayToInject, unboundedDelay, delayPercentage)
  }

}

object DelayInjector {

  val BufferMs     = 200L
  val DefaultTtlMs = 3000L

  def generateTagsForInjectDelayMeasurement(
      expContext: ExperimentContext,
      delayPercent: Double,
      isExceedMaximumSsrDelay: Boolean
  ): Map[String, String] =
    Map(
      MeasurementTag.ssrLatencyInjectedPercentage -> delayPercent.toString,
      MeasurementTag.injectSsrDelayExperiment1    -> expContext.allocationContext.injectSsrDelay1.toString,
      MeasurementTag.injectSsrDelayExperiment2    -> expContext.allocationContext.injectSsrDelay2.toString,
      MeasurementTag.injectSsrDelayExperiment3    -> expContext.allocationContext.injectSsrDelay3.toString,
      MeasurementTag.isExceedMaximumSsrDelay      -> isExceedMaximumSsrDelay.toString
    )

  def setMetricAttributeForInjectDelayMeasurement(
      metricsContext: MetricsContext,
      expContext: ExperimentContext
  ): Unit = {
    metricsContext.setAttribute(
      AttributeKey.stringKey(MeasurementTag.injectSsrDelayExperiment1),
      expContext.allocationContext.injectSsrDelay1.toString
    )

    metricsContext.setAttribute(
      AttributeKey.stringKey(MeasurementTag.injectSsrDelayExperiment2),
      expContext.allocationContext.injectSsrDelay2.toString
    )

    metricsContext.setAttribute(
      AttributeKey.stringKey(MeasurementTag.injectSsrDelayExperiment3),
      expContext.allocationContext.injectSsrDelay3.toString
    )
  }

  def isSsrSearchType(query: String): Boolean = {
    val listOfSsrSearchTypes = SearchTypes.ssrSearchTypes.map(_.toString.toLowerCase)

    val lowerQuery = query.toLowerCase

    listOfSsrSearchTypes.exists(sType => lowerQuery.contains(sType))
  }

  def toHttpResponse(result: GQLResponse) =
    HttpResponse(
      status = StatusCodes.OK,
      entity = HttpEntity(ContentTypes.`application/json`, ByteString(result.json.noSpaces.getBytes))
    )

}
