package com.agoda.papi.search.api.http.graphql.resolver

import com.agoda.common.graphql.model.Wrapper
import com.agoda.common.graphql.schema.arguments.ArgumentProvider
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.content.models.circe.Serialization._
import com.agoda.content.models.propertycontent.ContentSummary
import com.agoda.core.search.models.request.CitySearchRequest
import com.agoda.papi.search.internalmodel.request.{ PriceTrendSearchDetails, PropertyAllotmentSearchDetails }
import com.agoda.core.search.models.response.{
  PollingInfoResponse,
  PostPagingAggregationResponse,
  PropertyLandmarkInfo
}
import com.agoda.mockito.helper.MockitoHelper
import com.agoda.papi.search.api.binding.GqlTestBindingWithSchema
import com.agoda.papi.search.common.externaldependency.experiment.{
  NoOpSearchExperimentManagerService,
  SearchExperimentManagerService
}
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.service.RequestSegmentMetricsGenerator
import com.agoda.papi.search.common.util.{
  ContentFeaturesDataExample,
  ContentHighlightsDataExample,
  ContentImageResponseDataExample,
  ContentInformationSummaryResponseDataExample,
  ContentQnaDataExample,
  ContentReviewScoreResponseDataExample,
  ContentReviewSummariesResponseDataExample,
  ContentTopicsDataExample
}
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.common.util.logger.HadoopLogger
import com.agoda.papi.search.internalmodel.serializer.PAPIHotelPricingSerializer._
import com.agoda.papi.search.complexsearch.service.{
  DataFetcher,
  PriceTrendSummaryDataFetcher,
  PropertyAllotmentSummaryDataFetcher
}
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultTypes
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.papi.search.internalmodel.response.PropertySmartSearchResponse
import com.agoda.papi.search.common.util.CoreDataExamples._
import com.agoda.papi.search.common.util.DFDataExample.{
  aValidPollingInfoResponse,
  aValidPriceByCheckIn,
  aValidPriceTrendSearchRequest
}
import com.agoda.papi.search.utils.PAPIContentSummaryHelper._
import com.agoda.papi.search.internalmodel.{
  ComplexSearchDeferred,
  ContentFeaturesDeferred,
  ContentHighlightsDeferred,
  ContentImagesDeferred,
  ContentInformationSummaryDeferred,
  ContentQnaDeferred,
  ContentReviewScoreDeferred,
  ContentReviewSummariesDeferred,
  ContentTopicsDeferred,
  PropertySponsoredDetail,
  PropertySummaryEnvelope
}
import com.agoda.papi.search.common.util.ResolverDataExamples.{ propertySummaryEnvelopeA, propertySummaryEnvelopeB, _ }
import com.agoda.papi.search.property.service.datafetcher.{ PropertyDetailsDataFetcher, PropertySummaryDataFetcher }
import com.agoda.property.search.utils._
import io.circe.Json
import io.circe.syntax.EncoderOps
import org.mockito.ArgumentMatchers.{ any, eq => eqTo }
import org.mockito.Mockito.{ times, verify }
import org.mockito.{ ArgumentMatchers => AM, Mockito => MM }
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import java.util.concurrent.TimeUnit
import scala.concurrent.duration._
import scala.concurrent.{ Await, ExecutionContext, Future }

class ResolverComposerSpec extends AnyWordSpec with MockitoHelper with Matchers with GqlTestBindingWithSchema {

  implicit val hadoopLogger                                                   = mock[HadoopLogger]
  implicit val reporter                                                       = mock[MetricsReporter]
  implicit val searchExperimentManagerService: SearchExperimentManagerService = new NoOpSearchExperimentManagerService
  implicit val ec: ExecutionContext                                           = ExecutionContexts.defaultContext

  "SearchSummaryResolver" should {
    "Return Valid PropertySummary Response" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val mockGraphQLContext = GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          reportingDataBuilder,
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
        MockitoHelper
          .doReturn(Future.successful(cumulativePropertyResponse))
          .when(dataFetcher)
          .fetchProperty(AM.eq(propertySearchRequestWrapper), any())(any(), any(), any())

        val searchSummaryResolver = resolverComposer.resolve(propertyRequestDefered, mockGraphQLContext, AnyRef)
        val results               = Await.result(searchSummaryResolver.head, FiniteDuration(10, TimeUnit.SECONDS))

        results shouldBe List(propertySummaryEnvelopeA, propertySummaryEnvelopeB)
      }
    }

    "Return Valid AggregationResponse Data" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val mockGraphQLContext = GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          reportingDataBuilder,
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
        MockitoHelper
          .doReturn(Future.successful(cumulativePropertyResponse))
          .when(dataFetcher)
          .fetchProperty(AM.eq(propertySearchRequestWrapper), any())(any(), any(), any())
        MockitoHelper
          .doReturn(Future.successful(aValidAggregationResponse))
          .when(dataFetcher)
          .fetchAggregatedSearchResult(any(), any(), any())(any(), any())
        MockitoHelper
          .doReturn(
            Future.successful(
              PostPagingAggregationResponse(
                Some(
                  List(
                    PropertyLandmarkInfo(1001, Some(10.5), Some("Airport"), List.empty, None)
                  )
                )
              )
            )
          )
          .when(dataFetcher)
          .fetchPostPagingAggregatedSearchResult(any(), any(), any())(any(), any())

        val searchSummaryResolver =
          resolverComposer.resolve(propertyRequestDefered ++ aggregationDeferred, mockGraphQLContext, AnyRef)
        val propertySummaries  = Await.result(searchSummaryResolver.head, FiniteDuration(10, TimeUnit.SECONDS))
        val aggregationReponse = Await.result(searchSummaryResolver.tail.head, FiniteDuration(10, TimeUnit.SECONDS))
        propertySummaries shouldBe List(propertySummaryEnvelopeA, propertySummaryEnvelopeB)
        aggregationReponse shouldBe aValidAggregationResponse
      }
    }

    "Return None in case of Empty Input" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val mockGraphQLContext = GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          reportingDataBuilder,
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
        val searchSummaryResolver = resolverComposer.resolve(Vector.empty, mockGraphQLContext, AnyRef)

        searchSummaryResolver shouldBe Vector.empty
      }
    }

    "Return Valid ComplexSearch data" in {
      import com.agoda.papi.search.common.util.CoreDataExamples._

      val dataFetcher = mock[DataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val mockGraphQLContext = GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          reportingDataBuilder,
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
        val citySearchRequest = CitySearchRequest(42, aValidSearchRequest)
        val wrapper           = Wrapper(citySearchRequest, Json.Null)
        MM.doReturn(Future.successful(aValidSearchResult), Nil: _*)
          .when(dataFetcher)
          .fetchSearchResponse(any(), any())(any(), any())
        val searchSummaryResolver =
          resolverComposer.resolve(Vector(ComplexSearchDeferred("city", wrapper)), mockGraphQLContext, AnyRef)

        val result = Await.result(searchSummaryResolver.head, 10.seconds).asInstanceOf[PropertySmartSearchResponse]

        verify(dataFetcher, times(1)).fetchSearchResponse(eqTo("city"), any())(any(), any())
        result.searchResult shouldBe aValidSearchResult
        result.searchRequest.asInstanceOf[CitySearchRequest].cityId shouldBe 42
      }
    }

    "Return Valid PropertyDetails data" in {
      import com.agoda.papi.search.common.util.CoreDataExamples._
      val propertyDetailsDataFetcher = mock[PropertyDetailsDataFetcher]
      val dataFetcher                = mock[DataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
        bind[PropertyDetailsDataFetcher] to propertyDetailsDataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val mockGraphQLContext = GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          reportingDataBuilder,
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
        MM.doReturn(Future.successful(ContentImageResponseDataExample.contentImagesResponseWrapperMap), Nil: _*)
          .when(propertyDetailsDataFetcher)
          .fetchContentImagesResponse(any(), any(), any())(any(), any())
        MM.doReturn(
          Future.successful(ContentReviewScoreResponseDataExample.contentReviewScoreResponseWrapperMap),
          Nil: _*
        ).when(propertyDetailsDataFetcher)
          .fetchContentReviewScoreResponse(any(), any(), any())(any(), any())
        MM.doReturn(
          Future.successful(ContentReviewSummariesResponseDataExample.contentReviewSummariesResponseWrapperMap),
          Nil: _*
        ).when(propertyDetailsDataFetcher)
          .fetchContentReviewSummariesResponse(any(), any(), any())(any(), any())
        MM.doReturn(
          Future.successful(ContentInformationSummaryResponseDataExample.contentInformationSummaryResponseWrapperMap),
          Nil: _*
        ).when(propertyDetailsDataFetcher)
          .fetchContentInformationSummaryResponse(any(), any(), any())(any(), any())
        MM.doReturn(Future.successful(ContentFeaturesDataExample.contentFeaturesResponseWrapperMap), Nil: _*)
          .when(propertyDetailsDataFetcher)
          .fetchContentFeaturesResponse(any(), any(), any())(any(), any())
        MM.doReturn(Future.successful(ContentHighlightsDataExample.contentHighlightsResponseWrapperMap), Nil: _*)
          .when(propertyDetailsDataFetcher)
          .fetchContentHighlightsResponse(any(), any(), any())(any(), any())
        MM.doReturn(Future.successful(ContentQnaDataExample.contentQnaResponseWrapperMap), Nil: _*)
          .when(propertyDetailsDataFetcher)
          .fetchContentQnaResponse(any(), any(), any())(any(), any())
        MM.doReturn(Future.successful(ContentTopicsDataExample.contentTopicsResponseWrapperMap), Nil: _*)
          .when(propertyDetailsDataFetcher)
          .fetchContentTopicsResponse(any(), any(), any())(any(), any())

        val searchSummaryResolver = resolverComposer.resolve(
          Vector(
            ContentImagesDeferred(1, ContentImageResponseDataExample.contentImagesRequestWrapper),
            ContentReviewScoreDeferred(1, ContentReviewScoreResponseDataExample.contentReviewScoreRequestWrapper),
            ContentReviewSummariesDeferred(
              1,
              ContentReviewSummariesResponseDataExample.contentReviewSummariesRequestWrapper
            ),
            ContentInformationSummaryDeferred(
              1,
              ContentInformationSummaryResponseDataExample.contentInformationSummaryRequestWrapper
            ),
            ContentFeaturesDeferred(1, ContentFeaturesDataExample.contentFeaturesRequestWrapper),
            ContentHighlightsDeferred(1, ContentHighlightsDataExample.contentHighlightsRequestWrapper),
            ContentQnaDeferred(1, ContentQnaDataExample.contentQnaRequestWrapper),
            ContentTopicsDeferred(1, ContentTopicsDataExample.contentTopicsRequestWrapper)
          ),
          mockGraphQLContext,
          AnyRef
        )

        val searchSummaryF = Future.sequence(searchSummaryResolver)
        val searchSummary  = Await.result(searchSummaryF, 10.seconds)

        val imageResponse = searchSummary(0).asInstanceOf[Option[Json]].get
        imageResponse shouldBe ContentImageResponseDataExample.jsonContentImagesResponse

        val reviewScoreResponse = searchSummary(1).asInstanceOf[Option[Json]].get
        reviewScoreResponse shouldBe ContentReviewScoreResponseDataExample.jsonContentReviewScoreResponse

        val reviewSummariesResponse = searchSummary(2).asInstanceOf[Option[Json]].get
        reviewSummariesResponse shouldBe ContentReviewSummariesResponseDataExample.jsonContentReviewSummariesResponse

        val informationSummaryResponse = searchSummary(3).asInstanceOf[Option[Json]].get
        informationSummaryResponse shouldBe ContentInformationSummaryResponseDataExample.jsonContentInformationSummaryResponse

        val featuresResponse = searchSummary(4).asInstanceOf[Option[Json]].get
        featuresResponse shouldBe ContentFeaturesDataExample.jsonContentFeaturesResponse

        val highlightsResponse = searchSummary(5).asInstanceOf[Option[Json]].get
        highlightsResponse shouldBe ContentHighlightsDataExample.jsonContentHighlightsResponse

        val qnaResponse = searchSummary(6).asInstanceOf[Option[Json]].get
        qnaResponse shouldBe ContentQnaDataExample.jsonContentQnaResponse

        val topicsIndex    = 7
        val topicsResponse = searchSummary(topicsIndex).asInstanceOf[Option[Json]].get
        topicsResponse shouldBe ContentTopicsDataExample.jsonContentTopicsResponse

        verify(propertyDetailsDataFetcher, times(1)).fetchContentImagesResponse(any(), any(), any())(
          any(),
          any()
        )
        verify(propertyDetailsDataFetcher, times(1)).fetchContentReviewScoreResponse(any(), any(), any())(
          any(),
          any()
        )
        verify(propertyDetailsDataFetcher, times(1)).fetchContentInformationSummaryResponse(any(), any(), any())(
          any(),
          any()
        )
        verify(propertyDetailsDataFetcher, times(1)).fetchContentFeaturesResponse(any(), any(), any())(
          any(),
          any()
        )
        verify(propertyDetailsDataFetcher, times(1)).fetchContentHighlightsResponse(any(), any(), any())(
          any(),
          any()
        )
        verify(propertyDetailsDataFetcher, times(1)).fetchContentQnaResponse(any(), any(), any())(any(), any())
        verify(propertyDetailsDataFetcher, times(1)).fetchContentTopicsResponse(any(), any(), any())(any(), any())
      }
    }

    "Return Properties Pricing data" in {
      val dataFetcher                = mock[DataFetcher]
      val propertySummaryDataFetcher = mock[PropertySummaryDataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
        bind[PropertySummaryDataFetcher] to propertySummaryDataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        import com.agoda.papi.search.common.util.CoreDataExamples._
        val contentSummary =
          ContentSummary(aValidPAPIHotelPricing.hotelId, None, None, None, None, Vector.empty, None, None, None, None)
        val expectedPropertySummaryEnvelope = List(
          PropertySummaryEnvelope(
            aValidPAPIHotelPricing.hotelId,
            Wrapper(contentSummary, contentSummary.asJson),
            Some(Wrapper(aValidPAPIHotelPricing, aValidPAPIHotelPricing.asJson)),
            None,
            None,
            None,
            PropertySponsoredDetail(),
            PropertyResultTypes.NormalProperty
          )
        )
        val mockGraphQLContext = GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          reportingDataBuilder,
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
        MockitoHelper
          .doReturn(Future.successful(expectedPropertySummaryEnvelope))
          .when(propertySummaryDataFetcher)
          .fetchPropertiesSummary(
            AM.eq(propertyIds.toList),
            AM.eq(propertySummarySearchDeferred.pricingRequestParameters),
            any(),
            any(),
            any()
          )(any(), any())

        val searchSummaryResolver =
          resolverComposer.resolve(Vector(propertySummarySearchDeferred), mockGraphQLContext, AnyRef)

        val searchSummary =
          Await.result(searchSummaryResolver.head, 10.seconds).asInstanceOf[List[PropertySummaryEnvelope]]

        searchSummary shouldBe expectedPropertySummaryEnvelope

        verify(propertySummaryDataFetcher, times(1)).fetchPropertiesSummary(any(), any(), any(), any(), any())(
          any(),
          any()
        )
      }
    }

    "Return valid PropertySummarySearchResponse" in {
      val dataFetcher                = mock[DataFetcher]
      val propertySummaryDataFetcher = mock[PropertySummaryDataFetcher]
      injectionTest(new GqlMockModule {
        bind[DataFetcher] to dataFetcher
        bind[PropertySummaryDataFetcher] to propertySummaryDataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val contentSummary =
          ContentSummary(aValidPAPIHotelPricing.hotelId, None, None, None, None, Vector.empty, None, None, None, None)
        val propertySummaryEnvelope = List(
          PropertySummaryEnvelope(
            aValidPAPIHotelPricing.hotelId,
            Wrapper(contentSummary, contentSummary.asJson),
            Some(Wrapper(aValidPAPIHotelPricing, aValidPAPIHotelPricing.asJson)),
            None,
            None,
            None,
            PropertySponsoredDetail(),
            PropertyResultTypes.NormalProperty
          )
        )
        val expectedPollingInfoResponse = Some(aValidPollingInfoResponse)

        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            reportingDataBuilder,
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future(propertySummaryEnvelope))
          .when(propertySummaryDataFetcher)
          .fetchPropertiesSummary(
            AM.eq(propertyIds.toList),
            AM.eq(propertySummarySearchDeferred.pricingRequestParameters),
            any(),
            any(),
            any()
          )(any(), any())

        val searchSummaryResolver = resolverComposer.resolve(
          Vector(propertySummarySearchDeferred, pollingInfoDeferred),
          mockGraphQLContext,
          AnyRef
        )

        val propertySummaries =
          Await.result(searchSummaryResolver(0), 10.seconds).asInstanceOf[List[PropertySummaryEnvelope]]
        propertySummaries shouldBe propertySummaryEnvelope
        verify(propertySummaryDataFetcher, times(1)).fetchPropertiesSummary(any(), any(), any(), any(), any())(
          any(),
          any()
        )

        val pollingInfoResponse =
          Await.result(searchSummaryResolver(1), 10.seconds).asInstanceOf[Some[PollingInfoResponse]]

        pollingInfoResponse shouldBe expectedPollingInfoResponse
      }
    }

    "Return valid price trend search response" in {
      val priceTrendSummaryDataFetcher = mock[PriceTrendSummaryDataFetcher]
      injectionTest(new GqlMockModule {
        bind[PriceTrendSummaryDataFetcher] to priceTrendSummaryDataFetcher
      }) { implicit injector =>
        val resolverComposer = inject[ResolverComposer]
        val expectedPriceTrendSearchDetails =
          PriceTrendSearchDetails(cityId, Some(propertyId1), Seq(aValidPriceByCheckIn))
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            reportingDataBuilder,
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            None,
            None,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(expectedPriceTrendSearchDetails))
          .when(priceTrendSummaryDataFetcher)
          .getPriceTrendSummary(AM.eq(aValidPriceTrendSearchRequest), any(), any())(any())

        val searchSummaryResolver =
          resolverComposer.resolve(Vector(priceTrendSearchDetailsDeferred), mockGraphQLContext, AnyRef)

        val priceTrendSearchResponse =
          Await.result(searchSummaryResolver(0), 10.seconds).asInstanceOf[PriceTrendSearchDetails]
        priceTrendSearchResponse shouldBe expectedPriceTrendSearchDetails
      }
    }

    "Return property allotment search response" in {
      val propertyAllotmentSummaryDataFetcher = mock[PropertyAllotmentSummaryDataFetcher]
      injectionTest(new GqlMockModule {
        bind[PropertyAllotmentSummaryDataFetcher] to propertyAllotmentSummaryDataFetcher
      }) { implicit injector =>
        val resolverComposer                       = inject[ResolverComposer]
        val expectedPropertyAllotmentSearchDetails = aValidPropertyAllotmentSearchDetails
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            reportingDataBuilder,
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            None,
            None,
            None,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(expectedPropertyAllotmentSearchDetails))
          .when(propertyAllotmentSummaryDataFetcher)
          .getPropertyAllotmentSummary(AM.eq(aValidPropertyAllotmentSearchRequest), any())(any())

        val searchSummaryResolver =
          resolverComposer.resolve(Vector(propertyAllotmentSearchDetailsDeferred), mockGraphQLContext, AnyRef)

        val propertyAllotmentSearchResponse =
          Await.result(searchSummaryResolver(0), 10.seconds).asInstanceOf[PropertyAllotmentSearchDetails]
        propertyAllotmentSearchResponse shouldBe expectedPropertyAllotmentSearchDetails
      }
    }
  }

  "SearchSummaryResolver : Failure cases" should {
    val dataFetcher = mock[DataFetcher]
    injectionTest(new GqlMockModule {
      bind[DataFetcher] to dataFetcher
    }) { implicit injector =>
      val resolverComposer = inject[ResolverComposer]
      val mockGraphQLContext = new GraphQLContext(
        mock[ArgumentProvider],
        globalContext,
        reportingDataBuilder,
        defaultRequestContext,
        Some(aValidPAPISearchResultTracker),
        piiContext,
        NoOpTracer,
        requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
      )

      "Return a Partial Response" in {
        MockitoHelper
          .doReturn(Future.successful(cumulativePropertyResponse))
          .when(dataFetcher)
          .fetchProperty(AM.eq(propertySearchRequestWrapper), any())(any(), any(), any())

        val searchSummaryResolver =
          resolverComposer.resolve(Vector(propertyRequestDeferred), mockGraphQLContext, AnyRef)
        val results = Await.result(searchSummaryResolver.head, FiniteDuration(10, TimeUnit.SECONDS))
        results shouldBe List(propertySummaryEnvelopeA, propertySummaryEnvelopeB)
      }
    }
  }

}
