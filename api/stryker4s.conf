stryker4s {
  base-dir: "api"
  reporters: ["console", "html", "json"]
  scala-dialect: "2.12"
  mutate: [
    "!target/**",
    "src/main/**/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/http/graphql/schema/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/util/directive/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/http/graphql/schema/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/http/rest/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/http/graphql/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/http/rest/openapi/schema/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/http/rest/openapi/endpointdocs/*.scala",
    "!src/main/scala/com/agoda/papi/search/api/binding/*.scala"
  ]
}