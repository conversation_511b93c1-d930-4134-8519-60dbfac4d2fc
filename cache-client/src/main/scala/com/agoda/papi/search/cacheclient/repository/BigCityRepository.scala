package com.agoda.papi.search.cacheclient.repository

import cats.effect.IO
import com.agoda.commons.sql.{ Query, Sql, SqlConnection }
import com.agoda.papi.search.cacheclient.model.{ PropertyInfo, PropertyInfoList }
import com.agoda.papi.search.cacheclient.repository.BigCityRepositorySource.BIG_CITY
import com.agoda.papi.search.cacheclient.{ fetchBigCityIds, CityIdType, PropertyInfoCacheKeyBuilder }
import com.typesafe.scalalogging.LazyLogging

trait BigCityRepositorySource {
  def bigCityIdsCache: Set[CityIdType]
}

class BigCityRepositorySourceImpl(ioSql: IO[Sql[IO, SqlConnection[IO]]])
    extends BigCityRepositorySource with LazyLogging {

  private val baseQuery: String = fetchBigCityIds
  private val fullQuery         = Query.unsafe(baseQuery)

  def execute: IO[List[Long]] =
    for {
      sql     <- ioSql
      cityIds <- sql.transact(fullQuery)(sql.resultSet[Long])
    } yield cityIds

  private def bigCityIds: List[Long] = execute.unsafeRunSync()
  // Evaluated and cached in memory during app load
  val bigCityIdsCache: Set[CityIdType] = bigCityIds.toSet ++ BIG_CITY
}

object BigCityRepositorySource {
  val NUM_CHUNKS: Int = 5

  // scalastyle:off
  val BIG_CITY: Set[Long] = Set(10757, 233, 17193, 16594, 721611, 647, 16534, 16716, 15470, 11350, 16580, 9590, 14524,
    5085, 13170, 7395, 18007, 16056, 17379, 11304, 9395, 8584, 1063, 16364, 14552, 1622, 10647, 16571, 16585, 8691,
    12772, 16573, 5531, 14370, 9442, 14932, 1569, 3987, 15977, 18008, 6325, 2758, 2994, 7401, 16256, 9294, 8734, 2002,
    86197, 17164, 10112, 10372, 4214, 14690, 79692, 1784, 318, 16572, 8373, 18225, 16587, 14503, 3935, 19020, 15790,
    79831, 12333, 6882, 4729, 17198, 2045, 23554, 18670, 106100, 12521, 72200, 4839, 4923, 17019, 21748, 5277, 18924,
    16440, 11825, 10703, 16657, 105104, 8738, 19311, 21683, 16087, 5835, 13747, 4001, 23037, 13621, 16850, 21583, 7082,
    11675, 70976, 3243, 21688, 1692, 17190, 13868, 1808, 18720, 16638, 16145, 23541, 18943, 3750, 10838, 16582, 7361,
    10461, 14810, 17589, 86962, 14018, 16577, 15947, 717899, 15121, 15932, 14892, 2679, 11807, 15543, 15983, 12399,
    19226, 4951, 11579, 19272, 21337, 5590, 2333, 12411, 14877, 1623, 7625, 11617, 6658, 16901, 4495, 4926, 200539,
    102916, 2976, 18316, 19416, 16051, 19812, 18315, 17269, 11158, 8944, 7835, 5663, 5954, 79596, 4257, 113282, 19699,
    102679, 13484, 16611, 8845, 10084, 16970, 6861, 7678, 99394, 13115, 2366, 3068, 18321, 16842, 4191, 6343, 19017,
    21579, 9028, 3789, 16084, 6764, 5609, 101030, 19708, 23127, 2086, 5093, 12722, 9933, 23360, 105353, 15289, 17374,
    1223, 79756, 5596, 10965, 85950, 19450, 1002, 101027, 23684, 70023, 12153, 6241, 70771, 5070, 16395, 105273, 18314,
    19930, 16187, 10921, 19039, 9348, 10309, 3435, 17182, 14186, 1131, 4617, 1862, 5772, 5216, 16404, 8581, 1327,
    104747, 19336, 13801, 16854, 21235, 8801, 2487, 16808, 78339, 16917, 5283, 11361, 79527, 79834, 79832, 23114, 14544,
    7219, 8863, 14865, 87928, 513272, 88773, 17273, 13899, 23641, 512944, 11980, 23677, 18371, 512866, 2112, 70836,
    86603, 23093, 2776, 10501, 12838, 11670, 18013, 8711, 7875, 18020, 5818, 6346, 1030, 7493, 79714, 102718, 19741,
    16527, 19999, 18319, 88151, 9170, 5414, 512790, 6960, 3253, 102488, 1178, 11882, 9323, 61, 10008, 12080, 4197,
    10136, 14382, 3167, 12690, 79830, 19453, 15904, 13259, 88100, 3636, 17172, 19813, 5624, 18325, 2138, 69228, 8683,
    79693, 197716, 20711, 2703, 76773, 16639, 21561, 9909, 99322, 773, 10095, 12619, 17072, 19216, 5349, 6488, 72724,
    3687, 10366, 5872, 102702, 1460, 19794, 10189, 114283, 10196, 18347, 10779, 16857, 16480, 262, 15755, 70953, 18701,
    18926, 16185, 17196, 6692, 17290, 85956, 8558, 20794, 18218, 182702, 67685, 4542, 3667, 9188, 16552, 9466, 5762,
    12349, 3656, 7100, 75688, 544751, 18036, 99398, 11531, 3527, 701914, 102583, 4816, 75540, 70639, 4064, 78500, 5320,
    10188, 23681, 79496, 21724, 19791, 21075, 12557, 76448, 12354, 512976, 517, 19577, 21568, 3332, 19313, 4740, 7273,
    12706, 10022, 2993, 5077, 99248, 18343, 512695, 4086, 9777, 16619, 98957, 9254, 79620, 73046, 79837, 14562, 16660,
    12565, 72660, 99354, 19703, 3339, 5445, 6700, 2021, 16636, 1987, 17402, 9927, 3727, 11240, 6036, 19206, 15523,
    70699, 18230, 17742, 102741, 15591, 16448, 196305, 2566, 9739, 544698, 9023, 73696, 19768, 5064, 1071, 79709, 7081,
    7636, 11692, 19373, 177626, 11610, 11613, 12453, 14819, 11524, 9846, 4175, 103380, 9206, 670, 18324, 23675, 23036,
    6613, 21680, 75926, 386225, 5435, 105281, 13550, 19675, 686350, 15464, 21778, 79607, 664, 2772, 14779, 11154, 16928,
    700999, 160, 3609, 102750, 68567, 16429, 14215, 21002, 20470, 196945, 9111, 3676, 17831, 18322, 17914, 756, 15032,
    512948, 19041, 1579, 8248, 3257, 21351, 23023, 19259, 102742, 17005, 2189, 16479, 106082, 4229, 14313, 2199, 102691,
    15806, 107999, 15643, 17759, 18874, 2612, 4094, 6167, 76265, 6355, 2295, 13740, 713017, 14574, 21348, 8338, 19623,
    6845, 8415, 4682, 5733, 6263, 11175, 9342, 243553, 12575, 76746, 6576, 15190, 16086, 14414, 217, 11107, 79336, 3905,
    23732, 30340, 1618, 19438, 19391, 12911, 13796, 17815, 6031, 15780, 107782, 108332, 8366, 79789, 71487, 6214, 17253,
    102492, 5120, 4234, 18878, 19477, 9212, 3049, 8430, 5906, 10358, 13596, 8913, 6851, 11320, 8881, 12077, 4980, 16630,
    16164, 1196, 11171, 16933, 19553, 5056, 17843, 11863, 73749, 16398, 88349, 17026, 11977, 7390, 79914, 13313, 102767,
    1760, 105657, 513003, 17234, 10863, 9128, 2217, 3612, 15426, 9360, 19184, 460, 16919, 16003, 6240, 11981, 18058,
    13183, 18094, 6599, 16584, 5323, 7436, 1931, 1568, 16223, 18215, 16114, 10665, 57, 15193, 17136, 9768, 11740, 5682,
    15878, 3322, 4329, 10871, 16045, 1109, 19359, 3453, 15150, 669661, 17161, 11902, 4556, 7413, 79639, 11876, 88776,
    78901, 17139, 19678, 10932, 7760, 9589, 702243, 78570, 7321, 3899, 7254, 108228, 4216, 23111, 8927, 21342, 18809,
    17389, 14712, 17787, 6139, 17331, 15642, 254308, 78591, 15697, 4557, 17242, 1915, 13200, 9164, 3054, 21191, 5334,
    12673, 10345, 16592, 1900, 13002, 11768, 161657, 19534, 6528, 12737, 18732, 5455, 16264, 78510, 4091, 407, 7692,
    2396, 23053, 8623, 19635, 17245, 4280, 11570, 106001, 18340, 8145, 6427, 17870, 6445, 5534, 23744, 78569, 15368,
    11948, 20637, 14100, 9868, 13853, 72063, 9641, 10580, 7923, 19433, 12645, 21123, 12863, 11356, 7142, 11488, 21258,
    5270, 17232, 69455, 15847, 615, 10731, 15578, 12546, 18017, 14147, 99459, 17042, 105938, 17550, 6958, 23055, 471,
    3143, 8105, 4276, 3922, 8864, 14566, 14809, 193, 16568, 9957, 12746, 17254, 8053, 6573, 10823, 11442, 102464, 20424,
    10163, 15897, 76956, 5905, 5063, 7609, 1249, 21284, 1297, 12393, 10787, 7138, 7169, 17096, 10401, 23118, 17729,
    9620, 18934, 108443, 2862, 14757, 17793, 22797, 17823, 18749, 4689, 3005, 78197, 3147, 79849, 13714, 16115, 8422,
    8559, 16644, 12329, 1286, 106058, 9104, 16169, 6931, 105662, 17222, 85868, 2423, 662, 9517, 17235, 16120, 6524,
    15446, 13122, 88749, 16593, 2253, 70802, 19815, 7010, 6084, 2570, 13864, 9853, 3738, 71511, 7165, 6546, 4359, 9974,
    5598, 9617, 17160, 18064, 14523, 11323, 79761, 17083, 7233, 5923, 13281, 13563, 16278, 13561, 4590, 21035, 1124,
    3223, 18916, 8152, 79371, 7719, 144, 19479, 17077, 7094, 17236, 7800, 15936, 6638, 390172, 15244, 2557, 804, 13638,
    2425, 5997, 85860, 7394, 105656, 7741, 4716, 15237, 406, 17892, 7519, 6942, 88743, 21664, 4498, 219, 9312, 2084,
    7077, 9047, 17179, 2299, 17137, 7626, 11306, 9713, 21280, 4635, 86228, 4569, 106065, 2111, 388364, 7033, 18189,
    108342, 17579, 248097, 4684, 15378, 983, 8052, 16670, 13630, 16599, 2051, 16062, 107284, 247905, 102497, 3241,
    18871, 9698, 79553, 17191, 15063, 23028, 1102, 181896, 6476, 12784, 3881, 14983, 14493, 4426, 77583, 250191, 12021,
    15038, 15965, 78905, 21227, 10249, 17487, 17080, 17704, 21711, 19789, 342, 14385, 12043, 21740, 18686, 17212, 14934,
    12289, 247771, 2977, 67761, 17276, 8163, 17055, 739, 16079, 14158, 73061, 9473, 21584, 212458, 175541, 5235, 79546,
    3818, 3114, 14130, 18346, 13791, 5152, 13602, 2599, 3564, 17790, 14389, 17023, 19788, 1404, 11181, 17099, 7331,
    3829, 5736, 15612, 3356, 17188, 21397, 17262, 211364, 106977, 571, 3256, 19480, 11931, 9732, 7720, 10680, 16675,
    14496, 16443, 71623, 17904, 14403, 249498, 9119, 19392, 4278, 9060, 13406, 4587, 9527, 17127, 17259, 77366, 75986,
    88744, 15903, 104, 1390, 2919, 2071, 17095, 79631, 11267, 18115, 13919, 12552, 1062, 8292, 18875, 5368, 14457, 3561,
    10182, 79606, 12113, 20663, 18217, 12050, 78379, 11013, 75989, 79383, 11918, 2394, 213193, 11287, 17753, 1689,
    11780, 58, 7266, 19317, 18881, 21298, 14094, 11658, 11815, 21472, 16667, 13239, 684, 12711, 2036, 549, 9787, 8969,
    20612, 17233, 5051, 79729, 2374, 103192, 212469, 4472, 3860, 11680, 12374, 3892, 16507, 7242, 11437, 4841, 9728,
    107, 2201, 1816, 3177, 72820, 10009, 4427, 2704, 513079, 9299, 79797, 9752, 2961, 11067, 6889, 4442, 9795, 23733,
    19625, 16254, 77785, 18929, 12395, 6771, 16462, 12611, 10554, 16190, 2467, 79788, 3965, 17062, 106750, 8453, 18826,
    4955, 7694, 17145, 13689, 1399, 13723, 7103, 7900, 5877, 21319, 105990, 75626, 513550, 16918, 16203, 2018, 8179,
    12331, 19193, 21372, 15305, 6632, 9286, 106682, 19100, 10475, 16017, 160898, 79764, 70807, 70679, 6425, 118039,
    178971, 79769, 15134, 718879, 79774, 79777, 79778, 68773, 21560, 79800, 79291, 9537, 5058, 23746, 15939, 71250,
    79706, 79838, 19937, 161893, 126694, 103148, 161132, 71278, 79743)
  // scalastyle:on

  def isBigCity(cityId: Long): Boolean = BIG_CITY.contains(cityId)

  def generateCacheKeysForBigCity(
      cityId: Long,
      numChunks: Int,
      keyBuilder: PropertyInfoCacheKeyBuilder
  ): Vector[String] =
    generateCacheKeypartsForBigCity(cityId, numChunks)
      .map(keypart => keyBuilder.build(keypart))

  def generateCacheKeypartsForBigCity(cityId: Long, numChunks: Int): Vector[String] =
    (1 to numChunks).map(partitionNumber => buildKeyPartForBigCity(cityId, partitionNumber, numChunks)).toVector

  def generateChunks(
      propertyInfoList: PropertyInfoList,
      keyparts: Vector[String]
  ): Iterator[(String, PropertyInfoList)] = {
    val totalChunks = keyparts.size
    propertyInfoList.propertyInfoList.zipWithIndex
      .groupBy { case (_, index) =>
        index % totalChunks
      }
      .map { case (chunkIndex: Int, chunk: Seq[(PropertyInfo, Int)]) =>
        val key              = keyparts(chunkIndex)
        val propertyInfoList = PropertyInfoList(chunk.map(_._1))
        (key, propertyInfoList)
      }
      .iterator
  }

  private def buildKeyPartForBigCity(cityId: Long, partitionNumber: Int, totalPartitions: Int): String =
    s"${cityId}_${"%02d".format(partitionNumber)}_${"%02d".format(totalPartitions)}"
}
