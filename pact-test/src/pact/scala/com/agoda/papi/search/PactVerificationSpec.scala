package com.agoda.papi.search

import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers
import pact4s.provider.PactSource.PactBrokerWithSelectors
import pact4s.provider.{ConsumerVersionSelectors, ProviderInfoBuilder}
import pact4s.scalatest.PactVerifier
import scala.concurrent.duration._

class PactVerificationSpec extends AnyFlatSpec with Matchers with PactVerifier {
  val selectors: ConsumerVersionSelectors = ConsumerVersionSelectors().latestTag(System.getenv("CONTRACT_TAG"))

  val provider: ProviderInfoBuilder = ProviderInfoBuilder(
    name = "propertyapisearchdocker",
    pactSource = PactBrokerWithSelectors(System.getenv("BROKER_URL")).withConsumerVersionSelectors(selectors)
  ).withProtocol("https")
    .withHost(System.getenv("DEVSTACK_URL"))
    .withPort(443)

  "Pact verification" should "verify the interactions in the Pact file" in {
    verifyPacts(
      verificationTimeout = Some(System.getenv("VERIFICATION_TIMEOUT").toInt.seconds),
    )
  }

}