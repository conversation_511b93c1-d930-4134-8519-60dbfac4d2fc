import sbt.Keys.{mappings, publishMavenStyle, resolvers, *}
import sbt.{addCompilerPlugin, *}

import java.io.File
import com.typesafe.sbt.packager.universal.ZipHelper

import scala.util.Try

Global / onChangedBuildSource := ReloadOnSourceChanges

addCommandAlias("rebuild", ";clean; compile; package")
addCommandAlias("package", "test:package")

//////////////////////////////////////////////////////////////////////////////
// PROJECTS
//////////////////////////////////////////////////////////////////////////////
//lazy val scalafmtSettings      = Seq(scalafmtOnCompile := true)
lazy val propertyapi = project
  .in(file("."))
  .settings(commonSettings)
  .dependsOn(property % "test->test;test->compile", complexsearch, common, api, client)
  .aggregate(
    property,
    models,
    internalModel,
    database,
    client,
    it,
    complexsearch,
    spTests,
    complexsearchFlowtest,
    common,
    integrationTest,
    cacheClient,
    cachePopulator,
    api,
    functionalTest,
    jarvisClient
  )

lazy val fakeApi = project
  .in(file("fake-api"))
  .settings(commonSettings ++ Seq(
    mainClass := Some("com.agoda.papi.search.fakeapi.Boot")
  ))
  .dependsOn(
    property              % "test->test;compile->test",
    complexsearch         % "test->test;compile->test",
    complexsearchFlowtest % "test->test;compile->test",
    common                % "test->test;compile->test",
    api                   % "test->test;compile->test",
    client                % "test->test;compile->test",
    functionalTest        % "test->test;compile->test"
  )

lazy val functionalTest = project
  .in(file("functional-test"))
  .settings(commonSettings)
  .dependsOn(
    property              % "test->test;compile->compile",
    complexsearch         % "test->test;compile->compile",
    complexsearchFlowtest % "test->test;compile->compile",
    common                % "test->test;compile->compile",
    api                   % "test->test;compile->compile",
    client                % "test->test;compile->compile"
  )

lazy val property = project
  .in(file("property"))
  .settings(commonSettings)
  .dependsOn(
    models        % "test->test;compile->compile",
    internalModel % "test->test;compile->compile",
    common        % "test->test;compile->compile",
    jarvisClient  % "compile->compile"
  )

lazy val models = project
  .in(file("models"))
  .settings(compactSettings ++ publishSettings)

lazy val database = project
  .in(file("database"))
  .configs(EndToEndTest)
  .settings(compactSettings ++ e2eSettings)
  .dependsOn(complexsearch % "test->test;compile->compile", property % "test->test;compile->compile")

lazy val client = project
  .in(file("client"))
  .configs(IntegrationTest)
  .settings(compactSettings ++ publishSettings, Defaults.itSettings)
  .dependsOn(models % "compile->compile;test->test")

lazy val it = project
  .in(file("it"))
  .settings(commonSettings ++ skipPublishSettings)
  .dependsOn(
    client,
    models        % "compile->compile;test->test",
    internalModel % "compile->compile;test->test",
    common        % "compile->compile;test->test"
  )

lazy val spTests = project
  .in(file("sp-tests"))
  .configs(SpTest)
  .settings(compactSettings ++ spTestSettings ++ skipPublishSettings)
  .dependsOn(
    models        % "test->test;compile->compile;sptest->test",
    internalModel % "test->test;compile->compile;sptest->test",
    common        % "test->test;compile->compile;sptest->test",
    complexsearch % "test->test;compile->compile;sptest->test",
    database      % "test->test;compile->compile;sptest->e2e"
  )

lazy val internalModel = project
  .in(file("internal-model"))
  .settings(commonSettings)
  .dependsOn(models % "test->test;compile->compile")

lazy val complexsearch = project
  .in(file("complexsearch"))
  .settings(commonSettings)
  .dependsOn(
    internalModel % "test->test;compile->compile",
    common        % "test->test;compile->compile",
    cacheClient   % "compile->compile",
    jarvisClient  % "compile->compile"
  )

lazy val complexsearchFlowtest = project
  .in(file("complexsearch-flowtest"))
  .settings(commonSettings ++ skipPublishSettings)
  .dependsOn(
    common        % "compile->compile;test->test",
    complexsearch % "compile->compile;test->test",
    api           % "compile->compile;test->test"
  )

lazy val benchmarks = project
  .in(file("benchmarks"))
  .settings(commonSettings ++ skipPublishSettings)
  .dependsOn(propertyapi % "compile->compile;test->test")

lazy val common = project
  .in(file("common"))
  .settings(commonSettings ++ skipPublishSettings)
  .dependsOn(
    internalModel % "compile->compile;test->test",
    models        % "compile->compile;test->test"
  )

lazy val api = project
  .in(file("api"))
  .settings(commonSettings ++ skipPublishSettings)
  .dependsOn(
    common        % "test->test;compile->compile",
    property      % "test->test;compile->compile",
    complexsearch % "test->test;compile->compile",
    internalModel % "compile->compile;test->test",
    client
  )

lazy val openapispecGenerator = project
  .in(file("openapispec-generator"))
  .settings(commonSettings ++ skipPublishSettings)
  .dependsOn(
    api % "test->test;compile->compile"
  )

lazy val integrationTest = project
  .in(file("integration-test"))
  .configs(UnitIntegrationTest)
  .settings(commonSettings ++ skipPublishSettings ++ unitIntegrationSettings)
  .dependsOn(
    common        % "it->test;compile->compile",
    complexsearch % "it->test;compile->compile",
    property      % "it->test;compile->compile"
  )

lazy val pactTest = project
  .in(file("pact-test"))
  .configs(PactTest)
  .settings(commonSettings ++ pactSettings)

lazy val cacheClient = project
  .in(file("cache-client"))
  .settings(
    commonSettings ++ skipPublishSettings,
    buildInfoKeys    := Seq[BuildInfoKey](name, version),
    buildInfoPackage := "cacheClientSettings"
  )
  .enablePlugins(sbtbuildinfo.BuildInfoPlugin)

lazy val cachePopulator = project
  .in(file("cache-populator"))
  .dependsOn(
    cacheClient % "compile->compile;test->compile" // for serialization and compression
  )

lazy val jarvisClient = Project("jarvis-client", base = file("jarvis-client"))
  .enablePlugins(AgCodegenSbtPlugin)
  .disablePlugins(ScalafmtPlugin)
  .settings(compactSettings ++ skipPublishSettings)
  .settings(
    inputSpec       := "https://apicurio-registry.agodadev.io/apis/registry/v2/groups/jarvis/artifacts/jarvis_v2",
    lang            := "ag-http-client-v2",
    groupId         := "com.agoda.jarvis.interfacing",
    artifactId      := "jarvis-client",
    artifactVersion := "2.0.0",
    additionalProperties := Map[String, String](
      "isSbtModule"         -> "true",
      "moduleName"          -> "jarvis-client",
      "agHttpClientVersion" -> Versions.Agoda.agHttpClient
    ),
    // Comment this out to fix the compile issue where the compiler can't fetch Jarvis OAS from apicurio
    // due to missing credential
//    Compile / sourceGenerators += Def.task {
//      codegen.toTask("").value
//      val managedSrcDir = (Compile / sourceManaged).value
//      val scalaFiles    = (managedSrcDir ** "*.scala").get
//      scalaFiles
//    }.taskValue
  )

//////////////////////////////////////////////////////////////////////////////
// PROJECT INFO
//////////////////////////////////////////////////////////////////////////////
val Organization  = "com.agoda.propertyapi"
val PROJECT_NAME  = "propertyapi"
val SCALA_VERSION = Versions.Scala.SCALA_2_12

//////////////////////////////////////////////////////////////////////////////
// SHARED SETTINGS
//////////////////////////////////////////////////////////////////////////////

val integrate = taskKey[Unit]("Runs package then test")

lazy val IntegrationTest = config("it").extend(Test)

val EndToEndTest = config("e2e").extend(Test)

val SpTest = config("sptest").extend(Test)

val PactTest = config("pact").extend(Test)

val UnitIntegrationTest = config("it").extend(Test)

lazy val e2eSettings = inConfig(EndToEndTest)(Defaults.testSettings) ++ Seq(
  EndToEndTest / fork              := false,
  EndToEndTest / parallelExecution := false,
  EndToEndTest / scalaSource       := baseDirectory.value / "src/e2e/scala"
)

lazy val spTestSettings = inConfig(SpTest)(Defaults.testSettings) ++ Seq(
  SpTest / fork              := false,
  SpTest / parallelExecution := false,
  SpTest / scalaSource       := baseDirectory.value / "src/sptest/scala"
)

val unitIntegrationSettings = inConfig(UnitIntegrationTest)(Defaults.testSettings) ++ Seq(
  SpTest / scalaSource := baseDirectory.value / "src/it/scala"
)

lazy val pactSettings = inConfig(PactTest)(Defaults.testSettings) ++ Seq(
  PactTest / fork              := false,
  PactTest / parallelExecution := true,
  PactTest / scalaSource       := baseDirectory.value / "src/pact/scala"
)

lazy val compactSettings = Defaults.coreDefaultSettings ++ compactBasicSettings

lazy val compactBasicSettings = Seq(
  organization := Organization,
  scalaVersion := SCALA_VERSION,
  exportJars   := true,

  // disable publishing the jar produced by `Test/package`
  Test / packageBin / publishArtifact := false,

  // disable publishing the test API jar
  Test / packageDoc / publishArtifact := false,

  // disable publishing the test sources jar
  Test / packageSrc / publishArtifact := false,

  // disable publishing the jar produced by `package`
  Compile / packageBin / publishArtifact := false,

  // disable publishing the main API jar
  Compile / packageDoc / publishArtifact := false,

  // disable publishing the main sources jar
  Compile / packageSrc / publishArtifact := false,
  dependencyOverrides ++= Seq(
    "ch.epfl.scala" %% "shapeless" % "2.3.3-lower-priority-coproduct"
  ),
  Compile / scalacOptions ++= {
    CrossVersion.partialVersion(scalaVersion.value) match {
      case Some((2, n)) if n < 12 => Nil
      case _ =>
        List(
          "-Ybackend-parallelism",
          "8"
        )
    }
  },

  // For profile compilation time
  /*
  Compile / scalacOptions ++= Seq(
    "-Ystatistics",
    "-Ycache-plugin-class-loader:last-modified",
    "-Xplugin:~/Library/Caches/Coursier/v1/https/repo1.maven.org/maven2/ch/epfl/scala/scalac-profiling_2.12/1.0.0/scalac-profiling_2.12-1.0.0.jar",
    "-P:scalac-profiling:no-profiledb",
    "-P:scalac-profiling:show-profiles",
    "-P:scalac-profiling:sourceroot:~/joatworkspace/propertyapi",
    "-P:scalac-profiling:generate-macro-flamegraph",
    "-Xlog-implicits"
  ),
   */
  
  updateOptions := updateOptions.value.withCachedResolution(true),
  addCompilerPlugin("com.olegpy" %% "better-monadic-for" % Versions.monadicForPlugin),
  libraryDependencies ~= {
    _.map(moduleID => Versions.excludeAgProtobufs(moduleID))
  },
  dependencyOverrides ++= Seq(
    "io.grpc"       % "grpc-core"         % Versions.grpc,
    "io.grpc"       % "grpc-netty-shaded" % Versions.grpc,
    "org.ow2.asm"   % "asm"               % Versions.asm,
    "org.ow2.asm"   % "asm-commons"       % Versions.asm,
    "org.ow2.asm"   % "asm-tree"          % Versions.asm,
    "org.ow2.asm"   % "asm-util"          % Versions.asm,
    "com.beachape" %% "enumeratum"        % Versions.enumeratum
  ),
  libraryDependencySchemes ++= Seq(
    "io.circe"               %% "circe-core"         % VersionScheme.Always,
    "io.circe"               %% "circe-parser"       % VersionScheme.Always,
    "io.circe"               %% "circe-jawn"         % VersionScheme.Always,
    "org.scala-lang.modules" %% "scala-java8-compat" % VersionScheme.Always,
    "com.thesamet.scalapb"   %% "scalapb-runtime"    % VersionScheme.Always
  )
)

lazy val publishSettings =
  // impt: realmName has to match between credentials and publishTo settings
  Seq(
    credentials += Credentials("Artifactory Realm", "repo-hkg.agodadev.io", "platform_deploy", "Wer56*!"),
    publishMavenStyle     := true,
    ThisBuild / publishTo := Some("Artifactory Agoda".at(s"https://repo-hkg.agodadev.io//agoda-maven-platform"))
  )

lazy val skipPublishSettings = Seq(
  publish / skip := true
)

lazy val configResource: Seq[String] =
  Try(Option(System.getProperty("config.resource"))).toOption.flatten
    .withFilter(_.nonEmpty)
    .map(conf => s"-Dconfig.resource=$conf")
    .toSeq

lazy val commonSettings = compactSettings ++ basicSettings //++ scalafmtSettings

lazy val basicSettings = Seq(
  libraryDependencies ++= Seq(
    "org.scalatest"     %% "scalatest"    % Versions.scalaTest            % "test",
    "org.scalatestplus" %% "mockito-3-4"  % Versions.scalaTestPlusMockito % "test",
    "org.specs2"        %% "specs2-core"  % Versions.specs2               % "test",
    "org.specs2"        %% "specs2-junit" % Versions.specs2               % "test",
    "org.mockito"        % "mockito-core" % Versions.mockito              % "test"
  ),
  libraryDependencies ~= {
    _.map(moduleID => Versions.commonExclude(moduleID))
  },
  Compile / scalacOptions ++= Seq(
    "-unchecked",
    "-deprecation",
    "-feature",
    "-Xlint"
  ),
//  javaOptions     := Seq("-Xdebug", "-Xrunjdwp:transport=dt_socket,server=y,suspend=n,address=5006") ++ configResource,
  scalaModuleInfo := scalaModuleInfo.value.map(_.withOverrideScalaVersion(true)),
  run / fork      := true,
  Test / fork     := false,
  Test / parallelExecution := true,

  // To fix serailise/deserailise java object in the unit test to fork JVM for unit test PackPropertyRouteSpec
  Test / testGrouping := {
    lazy val javaOp = javaOptions.value
    (Test / definedTests).value.filter(_.name.contains("PackPropertyRouteSpec")).map { test =>
      Tests.Group(
        name = test.name,
        tests = Seq(test),
        runPolicy = Tests.SubProcess(
          ForkOptions(
            javaHome.value,
            outputStrategy.value,
            Vector.empty[java.io.File],
            Some(baseDirectory.value),
            javaOp.toVector,
            connectInput.value,
            Map.empty[String, String]
          )
        )
      )
    }
  },
  Test / testGrouping += Tests.Group(
    name = "Standard Group",
    tests = (Test / definedTests).value.filter(!_.name.contains("PackPropertyRouteSpec")),
    runPolicy = Tests.SubProcess(ForkOptions())
  ),
  coverageOutputTeamCity := true,
  scalacOptions += "-target:jvm-1.8",
  dependencyOverrides ++= Seq(
    "com.agoda.commons"            %% "metrics"              % Versions.Agoda.commons,
    "com.agoda.commons"            %% "ag-grpc-client"       % Versions.Agoda.agGrpcV0,
    "com.agoda.everest"            %% "grpc"                 % Versions.slService,
    "com.fasterxml.jackson.module" %% "jackson-module-scala" % Versions.jacksonModule,
    "io.grpc"                       % "grpc-core"            % Versions.grpc,
    "io.grpc"                       % "grpc-netty-shaded"    % Versions.grpc,
    "com.thesamet.scalapb"         %% "scalapb-runtime"      % scalapb.compiler.Version.scalapbVersion,
    "com.thesamet.scalapb"         %% "scalapb-runtime-grpc" % scalapb.compiler.Version.scalapbVersion,
    // Fix for CVE-2023-28867: Upgrade GraphQL Java to safe version
    "com.graphql-java" % "graphql-java"    % Versions.graphqlJava,
    "ch.qos.logback"   % "logback-classic" % Versions.logback,
    "org.yaml"         % "snakeyaml"       % Versions.snakeYaml
  ),

  // to run some commands at sbt start-up https://www.scala-sbt.org/1.x/docs/Howto-Startup.html
  onLoad in Global := {
    val old = (onLoad in Global).value
    // compose the new transition on top of the existing one
    // in case your plugins are using this hook.
    startupTransition.compose(old)
  }
)

//  lazy val testScalastyle = taskKey[Unit]("testScalastyle")
//  testScalastyle := org.scalastyle.sbt.ScalastylePlugin.scalastyle.in(Test).toTask("").value
//  (Test / test) <<= (Test / test) dependsOn testScalastyle

// to run some commands at sbt start-up https://www.scala-sbt.org/1.x/docs/Howto-Startup.html
lazy val startupTransition: State => State = { s: State =>
  "setupGitHookTask" :: s
}

lazy val setupGitHookTask = TaskKey[Unit]("setupGitHookTask", "copy all githook in .githook to .git/hook")

ThisBuild / setupGitHookTask := {
  import sbt.io.IO
  IO.copyFile(baseDirectory.value / ".githooks" / "commit-msg", baseDirectory.value / ".git" / "hooks" / "commit-msg")
  IO.copyFile(baseDirectory.value / ".githooks" / "pre-commit", baseDirectory.value / ".git" / "hooks" / "pre-commit")
}

Compile / compile := ((Compile / compile).dependsOn(setupGitHookTask)).value

Global / excludeLintKeys += buildInfoKeys // exclude buildInfoKeys from linter

//////////////////////////////////////////////////////////////////////////////
// BUILD PACKAGE
//////////////////////////////////////////////////////////////////////////////

import com.typesafe.sbt.packager.universal.ZipHelper

update / aggregate := false
updateOptions      := updateOptions.value.withCachedResolution(true)

buildInfoKeys := Seq[BuildInfoKey](
  name,
  version,
  scalaVersion,
  sbtVersion,
  "builtAt" -> {
    val dtf = new java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss")
    dtf.setTimeZone(java.util.TimeZone.getTimeZone("UTC"))
    dtf.format(new java.util.Date())
  },
  "builtAtMillis" -> System.currentTimeMillis()
)

enablePlugins(JavaServerAppPackaging)

Universal / mappings ++= {
  ((Compile / sourceDirectory).value / "resources" * "*").get.map { f =>
    f -> s"conf/${f.name}"
  }
}

Universal / mappings ++= {
  ((Compile / sourceDirectory).value / "resources/shard" * "*").get.map { f =>
    f -> s"conf/shard/${f.name}"
  }
}

/** New CSpider * */
Universal / mappings ++= {
  ((Compile / sourceDirectory).value / "resources/puppets/manifests" * "*").get.map { f =>
    f -> s"puppet/modules/puppets/manifests/${f.name}"
  }
}

// Consul templates
Universal / mappings ++= {
  ((Compile / sourceDirectory).value / "resources/consul-templates" * "*").get.map { f =>
    f -> s"consul-templates/${f.name}"
  }
}

// Vault Consul templates
Universal / mappings ++= {
  ((Compile / sourceDirectory).value / "resources/vault-consul-templates" * "*").get.map { f =>
    f -> s"vault-consul-templates/${f.name}"
  }
}

/** For Sync DB deployment
  */
Universal / mappings ++= {
  val sqlDir = baseDirectory.value / "database" / "SQL"
  sqlDir.allPaths.get.withFilter(_.isFile).map { f =>
    val targetPath = f.getPath.replaceFirst(sqlDir.getPath, "SQL")
    f -> s"$targetPath"
  }
}

/** For gatekeeper configuration
  */
Universal / mappings ++= {
  val sqlDir = baseDirectory.value / "gatekeeper"
  sqlDir.allPaths.get.withFilter(_.isFile).map { f =>
    val targetPath = f.getPath.replaceFirst(sqlDir.getPath, "gatekeeper")
    f -> s"$targetPath"
  }
}

/** Core shard
  */
Universal / mappings ++= {
  (baseDirectory.value / "core" / "src" / "main" / "resources" * "*").get.map { f =>
    f -> s"conf/core/${f.name}"
  }
}

/** Core shard
  */
Universal / mappings ++= {
  (baseDirectory.value / "core" / "src" / "main" / "resources" / "shard" * "*").get.map { f =>
    f -> s"conf/core/shard/${f.name}"
  }
}

Compile / mainClass := Some("Boot")

Universal / packageBin := {
  val originalFileName = (Universal / packageBin).value
  val (base, ext)      = originalFileName.baseAndExt
  val newFileName      = file(originalFileName.getParent) / (base + "_dist." + ext)
  val extractedFiles   = IO.unzip(originalFileName, file(originalFileName.getParent))
  val mappings: Set[(File, String)] =
    extractedFiles.map(f => (f, f.getAbsolutePath.substring(originalFileName.getParent.length + base.length + 2)))
  val binFiles = mappings.filter { case (_, path) => path.startsWith("bin/") }
  for (f <- binFiles) f._1.setExecutable(true)
  ZipHelper.zip(mappings, newFileName)
  IO.move(newFileName, originalFileName)
  IO.delete(file(originalFileName.getParent + "/" + originalFileName.base))
  originalFileName
}

ThisBuild / libraryDependencySchemes ++= Seq(
  "org.scala-lang.modules" %% "scala-xml" % VersionScheme.Always
)

addCommandAlias(
  "onlyUnitTests",
  """; set ThisBuild/Test/testOptions += Tests.Filter(t => !t.startsWith("com.agoda.papi.search.functionaltest.testsuite")); coverage;test;coverageAggregate""".stripMargin
)

addCommandAlias(
  "onlyFunctionalTests",
  """; set ThisBuild/Test/testOptions += Tests.Filter(t => t.startsWith("com.agoda.papi.search.functionaltest.testsuite")); coverage;test;coverageAggregate""".stripMargin
)