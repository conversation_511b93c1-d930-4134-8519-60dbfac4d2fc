name := "database"

libraryDependencies ++= Seq(
  ("com.agoda.commons" %% "sql"          % Versions.Agoda.commons).withSources().changing().force(),
  ("com.agoda.commons" %% "ag-sql-scala" % Versions.Agoda.agSql)
    .withSources()
    .changing()
    .force()
    .excludeAll(
      ExclusionRule(organization = "org.specs2")
    ),
  "com.agoda.commons"   % "ag-dynamic-state-consul" % Versions.Agoda.agConsul,
  ("com.agoda.commons" %% "serialization"           % Versions.Agoda.commons).changing(),
  "org.scalatest"      %% "scalatest"               % Versions.scalaTest % "test",
  "org.mockito"         % "mockito-core"            % Versions.mockito   % "test",
  "com.vividsolutions"  % "jts-core"                % Versions.jts,
  "com.vividsolutions"  % "jts-io"                  % Versions.jts,
  "com.agoda.platform" %% "ag-cache-api"            % Versions.Agoda.agCache,
  "com.agoda.platform" %% "ag-cache-couchbase"      % Versions.Agoda.agCache,
  "com.agoda.platform" %% "ag-cache-caffeine"       % Versions.Agoda.agCache,
  "com.github.cb372"   %% "scalacache-caffeine"     % Versions.scalacache,
  "org.scaldi"         %% "scaldi"                  % Versions.scaldi,
  "com.agoda.commons"  %% "ag-couchbase"            % Versions.Agoda.agCouchbase,
  ("com.agoda.commons"  % "ag-vault"                % Versions.Agoda.agVault).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule(organization = "com.fasterxml.jackson.databind"),
    ExclusionRule(organization = "com.typesafe.akka")
  ),
  // Need the upgraded version of opentelemetry-jdbc to solve https://gitlab.agodadev.io/common-libs/ag-sql/-/issues/129
  "io.opentelemetry.instrumentation" % "opentelemetry-jdbc" % Versions.openTelemetryAlpha,
  "com.microsoft.sqlserver"          % "mssql-jdbc"         % Versions.msJdbc,
  "com.agoda.commons"                % "ag-sql-hikari"      % Versions.Agoda.agSql,
  ("com.agoda.commons"              %% "ag-sql-scala"       % Versions.Agoda.agSql)
    .withSources()
    .changing()
    .force()
    .excludeAll(ExclusionRule(organization = "org.specs2")),
  ("com.agoda.commons" % "ag-jdbc-cache-driver" % Versions.Agoda.agJdbcCache).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule(organization = "com.fasterxml.jackson.databind"),
    ExclusionRule(organization = "com.typesafe.akka")
  ),
  "org.yaml" % "snakeyaml" % Versions.snakeYaml
)
