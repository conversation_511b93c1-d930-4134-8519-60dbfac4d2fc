package com.agoda.papi.search.complexsearch.logging;
import com.agoda.adp.messaging.message.Message;

import java.util.List;

public class PAPIPostFilterMessage extends Message {
    public String correlationId;
    public int postFilterCount;
    public List<PostFilterResult> filterResult;

    public PAPIPostFilterMessage(String correlationId, int postFilterCount, List<PostFilterResult> filterResult){
        this.correlationId = correlationId;
        this.postFilterCount = postFilterCount;
        this.filterResult = filterResult;
    }
}
