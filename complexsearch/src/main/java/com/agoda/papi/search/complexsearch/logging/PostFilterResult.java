package com.agoda.papi.search.complexsearch.logging;

import java.util.List;

public class PostFilterResult {
    public String filterName;
    public int filterId;
    public List<Long> failedHotelIds;
    public int expectedResultSize;
    public int actualResultSize;

    public PostFilterResult(String filterName, int filterId, List<Long> failedHotelIds, int expectedResultSize, int actualResultSize) {
        this.filterName = filterName;
        this.filterId = filterId;
        this.failedHotelIds = failedHotelIds;
        this.expectedResultSize = expectedResultSize;
        this.actualResultSize = actualResultSize;
    }
}
