package com.agoda.papi.search.complexsearch.common.service.client

import cats.MonadError
import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.http.client.v2.{ HttpClient, RequestSettings => ClientRequestSettings }
import com.agoda.commons.http.mesh.v2.{ RequestSettings, ServiceMesh }
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.papi.content.graphql.ContentGroupTopicsRequestEnvelope
import com.agoda.papi.content.graphql.Serializer.ContentGroupTopicsRequestEnvelopeEncoder
import com.agoda.papi.search.common.externaldependency.chunkable.client.ChunkableClientFuture
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.util.circuitbreaker.CircuitBreakerWrapper
import com.agoda.papi.search.common.util.configuration.{ EndpointSettings, SplitSettings }
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext }
import com.agoda.platform.service.context.GlobalContext
import com.typesafe.scalalogging.LazyLogging
import io.opentracing.Tracer
import sttp.model.Uri
import sttp.model.Uri._
import io.circe.syntax._

import scala.concurrent.Future

trait ContentTopicsChunkableClient
    extends ChunkableClientFuture[ContentGroupTopicsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]

class ContentTopicsChunkableClientImpl(
    contentServiceMesh: ServiceMesh,
    client: HttpClient[Future],
    tracer: Tracer,
    circuitBreaker: CircuitBreakerWrapper
) extends ContentTopicsChunkableClient with LazyLogging {
  private val endpoint: Uri = uri"http://localhost/api/v2/property/topics"

  override def split(
      request: ContentGroupTopicsRequestEnvelope,
      settings: SplitSettings,
      experimentContext: ExperimentContext
  ): List[ContentGroupTopicsRequestEnvelope] =
    List(request)

  override def serviceCall(
      chunkRequest: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      endpointSettings: EndpointSettings
  )(implicit
      ME: MonadError[Future, Throwable],
      contextHolder: ContextHolder
  ): Future[List[Wrapper[ContentWithPropertyId]]] = {
    import com.agoda.common.graphql.model.WrapperSerializer.toListDeserializer
    import com.agoda.papi.search.common.model.content.ContentWithPropertyIdSerializer._

    if (globalContext.isDebug.contains(true)) {
      logger.info(s"Content topics request ==> ${chunkRequest.asJson.noSpaces}")
    }

    contentServiceMesh.request(
      rpc = { (host, meshBuilder) =>
        val requestBuilder = getMeshBuilder(meshBuilder, globalContext)
        client.post[ContentGroupTopicsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
          endpoint.host(host.host).port(host.port),
          chunkRequest,
          requestBuilder,
          ClientRequestSettings.default.copy(extraMetricTags = contextHolder.commonContextTags)
        )(circeSerializer[ContentGroupTopicsRequestEnvelope], toListDeserializer[ContentWithPropertyId])
      },
      requestSettings = RequestSettings(endpointSettings.maxNumberOfAttempts)
    )
  }

  override def aggregate(responses: List[List[Wrapper[ContentWithPropertyId]]]): List[Wrapper[ContentWithPropertyId]] =
    responses.flatten

  override val circuitBreakerWrapper: CircuitBreakerWrapper = circuitBreaker
}
