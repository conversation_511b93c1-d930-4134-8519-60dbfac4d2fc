package com.agoda.papi.search.complexsearch.graphql.resolver

import api.request.FeatureFlag
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.tracing.api.WithTracer
import com.agoda.core.search.models.request.RegionSearchRequest
import com.agoda.core.search.models.response.SearchResult.toGqlSearchResult
import com.agoda.papi.search.common.util.measurement.{ Measurement<PERSON>ey, MeasurementName, MeasurementTag }
import com.agoda.papi.search.common.graphql.resolver.ResolverUtil._
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.service.RequestSegmentMetricsGenerator
import com.agoda.papi.search.common.util.logger.HadoopLogger
import com.agoda.papi.search.common.util.measurement
import com.agoda.papi.search.internalmodel.{
  AggregationR<PERSON><PERSON><PERSON><PERSON>er<PERSON>,
  CumulativePropertiesResponse,
  ExtraAgodaHomesRequestD<PERSON><PERSON>red,
  FeaturedAgodaHomesRequestDeferred,
  FeaturedLuxuryHotelsRequestDeferred,
  FeaturedPulsePropertiesRequestDeferred,
  GeoPlacesDeferred,
  HighlyRatedAgodaHomesRequestDeferred,
  PropertyRequestDeferred,
  SearchBannerCampaignInfoDeferred,
  SearchEnrichmentDeferred,
  SearchEnrichmentResponse,
  SearchResultDeferred
}
import com.agoda.papi.search.complexsearch.search.MatrixFlowHelper
import com.agoda.papi.search.complexsearch.service.DataFetcher
import com.agoda.styx.botprofile.{ BotProfileParser, BotTypes, TrafficType }
import com.agoda.whitelabel.client.settings.Constants
import com.agoda.whitelabel.client.settings.Constants.WhiteLabelId
import io.opentracing.Tracer
import sangria.execution.deferred.Deferred

import scala.concurrent.{ ExecutionContext, Future }

class ResolverLevel2(
    val tracer: Tracer,
    hadoopLogger: HadoopLogger,
    dataFetcher: DataFetcher,
    metricsReporter: MetricsReporter,
    requestSegmentMetricsGenerator: RequestSegmentMetricsGenerator
) extends WithTracer {
  val PARALLEL_AGGREGATION_THRESHOLD = 100

  // scalastyle:off
  def resolveLevel2(deferred: Vector[Deferred[Any]], ctx: GraphQLContext, queryState: Any)(implicit
      ec: ExecutionContext
  ): PartialFunction[Deferred[Any], Future[Any]] = {
    // Second call will be for this deferred
    val propertySearchRequestEnvelop: Option[PropertyRequestDeferred] = deferred.collectFirst {
      case PropertyRequestDeferred(request, searchResult) => PropertyRequestDeferred(request, searchResult)
    }

    val aggregationRequestEnvelope = deferred.collectFirst { case AggregationRequestDeferred(request, searchResult) =>
      AggregationRequestDeferred(request, searchResult)
    }

    val featuredAgodaHomesRequestEnvelop = deferred.collectFirst {
      case FeaturedAgodaHomesRequestDeferred(request, searchResult) =>
        FeaturedAgodaHomesRequestDeferred(request, searchResult)
    }

    val highlyRatedAgodaHomesRequestEnvelop = deferred.collectFirst {
      case HighlyRatedAgodaHomesRequestDeferred(request, searchResult) =>
        HighlyRatedAgodaHomesRequestDeferred(request, searchResult)
    }

    val extraAgodaHomesRequestEnvelop = deferred.collectFirst {
      case ExtraAgodaHomesRequestDeferred(request, searchResult) =>
        ExtraAgodaHomesRequestDeferred(request, searchResult)
    }

    val featuredLuxuryHotelsRequestEnvelop = deferred.collectFirst {
      case FeaturedLuxuryHotelsRequestDeferred(request, searchResult) =>
        FeaturedLuxuryHotelsRequestDeferred(request, searchResult)
    }

    val featuredPulsePropertiesRequestEnvelop = deferred.collectFirst {
      case FeaturedPulsePropertiesRequestDeferred(request, searchResult) =>
        FeaturedPulsePropertiesRequestDeferred(request, searchResult)
    }

    val searchEnrichmentDeferred = deferred.collectFirst { case SearchEnrichmentDeferred(request, searchResult) =>
      SearchEnrichmentDeferred(request, searchResult)
    }

    val searchBannerCampaignInfoDeferred = deferred.collectFirst {
      case SearchBannerCampaignInfoDeferred(request, bannerCampaignRequest) =>
        SearchBannerCampaignInfoDeferred(request, bannerCampaignRequest)
    }

    val geoPlacesDeferred: Option[GeoPlacesDeferred] = deferred.collectFirst { case deferred: GeoPlacesDeferred =>
      deferred
    }

    /** * Process **
      */
    val propertiesEnvelopF = optionToFuture(propertySearchRequestEnvelop.map { propertySearchRequest =>
      ctx.reportingDataBuilder.update(propertySearchRequest.propertySearchRequestWrapper.contentRequestWrapper.t)
      propertySearchRequest.propertySearchRequestWrapper.pricingRequestWrapper.map(wrapper =>
        ctx.reportingDataBuilder.update(wrapper.t)
      )
      val result = dataFetcher.fetchProperty(
        propertySearchRequest.propertySearchRequestWrapper,
        propertySearchRequest.searchResult
      )(ec, ctx, hadoopLogger)
      result.map(props => ctx.reportingDataBuilder.update(props.propertySummaryWrapper))
      result
    })

    val aggregationEnvelopeF =
      optionToFuture(aggregationRequestEnvelope.map { propertySearchRequest =>
        if (propertySearchRequest.searchResult.properties.properties.size > PARALLEL_AGGREGATION_THRESHOLD) {
          ctx.requestContext.metrics.setTag(MeasurementTag.AggregationMode, "parallel")
          val aggregationResponseF = dataFetcher.fetchAggregatedSearchResult(
            propertySearchRequest.searchRequest,
            propertySearchRequest.searchResult,
            None
          )(ec, ctx.getSearchContext)
          val postPagingAggregationResponseF = propertiesEnvelopF.flatMap { propertiesO =>
            optionToFuture(propertiesO.map { properties =>
              dataFetcher.fetchPostPagingAggregatedSearchResult(
                propertySearchRequest.searchRequest,
                propertySearchRequest.searchResult,
                properties
              )(ec, ctx.getSearchContext)
            })
          }
          val res = for {
            aggregationResponse            <- aggregationResponseF
            postPagingAggregationResponseO <- postPagingAggregationResponseF
          } yield postPagingAggregationResponseO
            .map(postPagingAggregationResponse =>
              MatrixFlowHelper.mergeAggregationResponse(aggregationResponse, postPagingAggregationResponse)
            )
            .orElse(Some(aggregationResponse))
          res
        } else {
          ctx.requestContext.metrics.setTag(MeasurementTag.AggregationMode, "sequential")
          propertiesEnvelopF.flatMap { propertiesO =>
            val aggregationResponseOptF = propertiesO
              .map { properties =>
                val aggregationResponseF = dataFetcher.fetchAggregatedSearchResult(
                  propertySearchRequest.searchRequest,
                  propertySearchRequest.searchResult,
                  Some(properties)
                )(ec, ctx.getSearchContext)
                val postPagingAggregationResponseF = dataFetcher.fetchPostPagingAggregatedSearchResult(
                  propertySearchRequest.searchRequest,
                  propertySearchRequest.searchResult,
                  properties
                )(ec, ctx.getSearchContext)
                for {
                  aggregationResponse           <- aggregationResponseF
                  postPagingAggregationResponse <- postPagingAggregationResponseF
                } yield Some(
                  MatrixFlowHelper.mergeAggregationResponse(aggregationResponse, postPagingAggregationResponse)
                )
              }
              .getOrElse {
                ctx.getSearchContext.experimentContext.runABExperiment(
                  ctx.getSearchContext.allocationContext.doAggregationOnEmptyProperties,
                  Future.successful(None), {
                    val aggregationResponseF = dataFetcher.fetchAggregatedSearchResult(
                      propertySearchRequest.searchRequest,
                      propertySearchRequest.searchResult,
                      None
                    )(ec, ctx.getSearchContext)
                    val postPagingAggregationResponseF = dataFetcher.fetchPostPagingAggregatedSearchResult(
                      propertySearchRequest.searchRequest,
                      propertySearchRequest.searchResult,
                      CumulativePropertiesResponse(List.empty, pagingFlowMetadata = None)
                    )(ec, ctx.getSearchContext)
                    for {
                      aggregationResponse           <- aggregationResponseF
                      postPagingAggregationResponse <- postPagingAggregationResponseF
                    } yield Some(
                      MatrixFlowHelper.mergeAggregationResponse(aggregationResponse, postPagingAggregationResponse)
                    )
                  }
                )
              }
            aggregationResponseOptF
          }
        }
      }).map(_.flatten)

    val featuredAgodaHomesF = optionToFuture(
      featuredAgodaHomesRequestEnvelop.map(propertySearchRequest =>
        dataFetcher.fetchFeaturedAgodaHomes(
          propertySearchRequest.propertySearchRequestWrapper,
          propertySearchRequest.searchResult
        )(ec, ctx.getSearchContext)
      )
    )

    val highlyRatedAgodaHomesF = optionToFuture(
      highlyRatedAgodaHomesRequestEnvelop.map(propertySearchRequest =>
        dataFetcher.fetchHighlyRatedAgodaHomes(
          propertySearchRequest.propertySearchRequestWrapper,
          propertySearchRequest.searchResult
        )(ec, ctx.getSearchContext)
      )
    )

    val extraAgodaHomesF = for {
      propertiesEnvelop <- propertiesEnvelopF
      extraAgodaHome <- optionToFuture {
        extraAgodaHomesRequestEnvelop.flatMap(propertySearchRequest =>
          propertiesEnvelop.map { properties =>
            dataFetcher.fetchExtraAgodaHomes(
              propertySearchRequest.propertySearchRequestWrapper,
              propertySearchRequest.searchResult,
              properties
            )(ec, ctx.getSearchContext)
          }
        )
      }
    } yield (extraAgodaHome)

    val featuredLuxuryHotelsF = optionToFuture(
      featuredLuxuryHotelsRequestEnvelop.map(propertySearchRequest =>
        dataFetcher.fetchLuxuryHotels(
          propertySearchRequest.propertySearchRequestWrapper,
          propertySearchRequest.searchResult
        )(ec, ctx.getSearchContext)
      )
    )

    val featuredPulsePropertiesF = optionToFuture(
      featuredPulsePropertiesRequestEnvelop.map(propertySearchRequest =>
        dataFetcher.fetchFeaturedPulseProperties(
          propertySearchRequest.propertySearchRequestWrapper,
          propertySearchRequest.searchResult
        )(ec, ctx.getSearchContext)
      )
    )

    val searchEnrichmentF = for {
      propertiesEnvelop <- propertiesEnvelopF
      searchEnrichment = searchEnrichmentDeferred.flatMap(searchEnrichmentRequest =>
        propertiesEnvelop.map { properties =>
          SearchEnrichmentResponse(searchEnrichmentRequest.propertySearchRequestWrapper, properties)
        }
      )
    } yield (searchEnrichment)

    val bannerCampaignResponseF = optionToFuture(searchBannerCampaignInfoDeferred.map { searchBannerCampaignRequest =>
      searchBannerCampaignRequest.searchRequest match {
        case regionSearchRequest: RegionSearchRequest =>
          dataFetcher.fetchRegionBannerCampaignResponse(
            regionSearchRequest.regionId,
            searchBannerCampaignRequest.bannerCampaignRequest
          )(ec, ctx.getSearchContext)
        case _ => Future.successful(List.empty)
      }
    })

    val geoPlacesResponseF = optionToFuture(geoPlacesDeferred.map { geoPlacesRequest =>
      dataFetcher
        .fetchGeoPlaces(geoPlacesRequest.objectInfo, geoPlacesRequest.searchContext)(ec, ctx.getSearchContext)
    })

    val featureFlags = propertySearchRequestEnvelop
      .flatMap { propertySearchRequest =>
        propertySearchRequest.propertySearchRequestWrapper.pricingRequestWrapper.map(_.t.pricing.featureFlag)
      }
      .getOrElse(List.empty)
    val enablePageToken = propertySearchRequestEnvelop
      .flatMap(
        _.propertySearchRequestWrapper.searchRequest.searchCriteria.featureFlagRequest.flatMap(_.enablePageToken)
      )
      .contains(true)
    val isReturnHotelNotReadyIfPullNotReady = featureFlags.contains(FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
    val shouldIgnoreReturnHotelNotReadyIfPullNotReady = ctx.getSearchContext.experimentContext.isBVariant(
      ctx.getSearchContext.allocationContext.ignoreReturnHotelNotReadyIfPullNotReady
    )
    val effectiveIsReturnHotelNotReadyIfPullNotReady =
      isReturnHotelNotReadyIfPullNotReady && !shouldIgnoreReturnHotelNotReadyIfPullNotReady
    val shouldUpdateIsComplete = enablePageToken || effectiveIsReturnHotelNotReadyIfPullNotReady

    val partialFunctionResult: PartialFunction[Deferred[Any], Future[Any]] = {
      /** SECOND LEVEL */
      // This case will match only once
      case SearchResultDeferred(searchResult) =>
        for {
          propertyResult <- propertiesEnvelopF
        } yield {
          // This is for recalculate totalFilteredHotel
          // removed after proper way to calculate
          val newResult = updateSearchResult(searchResult, propertyResult, ctx.getSearchContext)
          val newIsComplete = if (effectiveIsReturnHotelNotReadyIfPullNotReady) {
            newResult.searchInfo.isComplete && computeHotelNotReadyIfPullNotReadyIsComplete(propertyResult)
          } else {
            newResult.searchInfo.isComplete
          }
          val newResultWithUpdatedIsComplete = updateIsComplete(newResult, shouldUpdateIsComplete, newIsComplete)

          ctx.reportingDataBuilder.update(newResultWithUpdatedIsComplete)
          // TODO Should be removed in the future once Client can consume information from headers
          toGqlSearchResult(overrideCid(newResultWithUpdatedIsComplete, ctx))
        }
      case PropertyRequestDeferred(propertySearchRequestWrapper, _) =>
        for {
          propertyResult <- propertiesEnvelopF
          properties <- propertiesEnvelopF.map(
            _.map(propMap => propMap.propertySummaryWrapper.map(propertyEnvelop => propertyEnvelop))
              .getOrElse(List.empty)
          )
        } yield {
          propertyResult.foreach(res =>
            requestSegmentMetricsGenerator.reportAfterPagingFlow(
              propertySearchRequestWrapper.searchRequest,
              res
            )
          )
          val newIsComplete = if (effectiveIsReturnHotelNotReadyIfPullNotReady) {
            computeHotelNotReadyIfPullNotReadyIsComplete(propertyResult)
          } else {
            true
          }
          val updatedResult = updatePropertyResult(properties, shouldUpdateIsComplete, newIsComplete)
          reportPercentageOfNotReadyOrPropCount(
            Option(
              CumulativePropertiesResponse(
                updatedResult,
                pagingFlowMetadata = propertyResult.flatMap(_.pagingFlowMetadata)
              )
            ),
            propertySearchRequestEnvelop,
            enablePageToken,
            effectiveIsReturnHotelNotReadyIfPullNotReady
          )
          updatedResult
        }

      case AggregationRequestDeferred(_, _) =>
        aggregationEnvelopeF.map(_.map(propMap => propMap).getOrElse(emptyAggregationResponse))
      case FeaturedAgodaHomesRequestDeferred(_, _) =>
        featuredAgodaHomesF.map(_.map(propMap => propMap.map(propertyEnvelop => propertyEnvelop)).getOrElse(List.empty))
      case HighlyRatedAgodaHomesRequestDeferred(_, _) =>
        highlyRatedAgodaHomesF.map(
          _.map(propMap => propMap.map(propertyEnvelop => propertyEnvelop)).getOrElse(List.empty)
        )
      case ExtraAgodaHomesRequestDeferred(_, _) =>
        extraAgodaHomesF.map(_.map(propMap => propMap.map(propertyEnvelop => propertyEnvelop)).getOrElse(List.empty))
      case FeaturedLuxuryHotelsRequestDeferred(_, _) =>
        featuredLuxuryHotelsF.map(
          _.map(propMap => propMap.map(propertyEnvelop => propertyEnvelop)).getOrElse(List.empty)
        )
      case FeaturedPulsePropertiesRequestDeferred(_, _) =>
        featuredPulsePropertiesF.map(_.getOrElse(List.empty))
      case SearchEnrichmentDeferred(_, _) =>
        searchEnrichmentF
      case SearchBannerCampaignInfoDeferred(_, _) =>
        bannerCampaignResponseF.map(_.getOrElse(List.empty))
      case GeoPlacesDeferred(_, _) =>
        geoPlacesResponseF
    }
    partialFunctionResult
  }

  // scalastyle:on
  private def reportPercentageOfNotReadyOrPropCount(
      propertyResult: Option[CumulativePropertiesResponse],
      propertySearchRequestEnvelop: Option[PropertyRequestDeferred],
      enablePageToken: Boolean,
      isReturnHotelNotReadyIfPullNotReady: Boolean
  ) = {
    val totalSize = propertyResult
      .map(_.propertySummaryWrapper.size)
      .getOrElse(0)
    val multiplyFactor = if (totalSize > 0) 100.0 / totalSize else 0
    val notReadyPropertiesCount = propertyResult
      .map(_.propertySummaryWrapper.filterNot(_.pricing.map(_.t.isReady).getOrElse(false)))
      .getOrElse(List.empty)
      .size
    metricsReporter.report(
      MeasurementKey.NotReadyProperties,
      (notReadyPropertiesCount * multiplyFactor).toLong,
      Map(
        MeasurementTag.PlatformId -> propertySearchRequestEnvelop
          .flatMap(_.propertySearchRequestWrapper.globalContext.getPlatformId)
          .getOrElse(-1)
          .toString,
        MeasurementTag.SearchType -> propertySearchRequestEnvelop
          .map(_.propertySearchRequestWrapper.searchRequest.searchTypeString)
          .getOrElse(None)
          .toString,
        MeasurementTag.IsPageTokenEnabled                  -> enablePageToken.toString,
        MeasurementTag.IsReturnHotelNotReadyIfPullNotReady -> isReturnHotelNotReadyIfPullNotReady.toString,
        MeasurementTag.NotReadyPercentage -> (Math.floor(
          (notReadyPropertiesCount * multiplyFactor) / 10
        ) * 10).toString,
        MeasurementTag.NotReadyPropertiesCount -> roundNumber(notReadyPropertiesCount).toString
      )
    )
    metricsReporter.report(
      MeasurementName.SearchResultCount,
      totalSize,
      Map(
        MeasurementTag.IsEmptyResult -> (totalSize == 0).toString,
        MeasurementTag.PlatformId -> propertySearchRequestEnvelop
          .flatMap(_.propertySearchRequestWrapper.globalContext.getPlatformId)
          .getOrElse(-1)
          .toString,
        MeasurementTag.NewTrafficType -> propertySearchRequestEnvelop
          .map(
            _.propertySearchRequestWrapper.globalContext.getBotInfo
              .map(
                BotProfileParser(Seq(BotTypes.VOLUME, BotTypes.VOLUME_HIDDEN)).parse(_).trafficType
              )
              .getOrElse(TrafficType.None)
          )
          .getOrElse("None")
          .toString,
        MeasurementTag.OperationTags -> propertySearchRequestEnvelop
          .map(_.propertySearchRequestWrapper.searchRequest.searchTypeString)
          .getOrElse(None)
          .toString,
        MeasurementTag.WhiteLabelId -> propertySearchRequestEnvelop
          .flatMap(_.propertySearchRequestWrapper.globalContext.getWhiteLabelId)
          .getOrElse(WhiteLabelId.AGODA)
          .toString,
        MeasurementTag.PageNumber -> propertySearchRequestEnvelop
          .flatMap(_.propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageNumber))
          .getOrElse(1)
          .toString
      )
    )
    if (propertySearchRequestEnvelop.map(_.searchResult.isDFSQLCircuitBreakerOpen).getOrElse(false))
      metricsReporter.report(
        MeasurementName.AvailabilityFallbackResponse,
        totalSize,
        Map(
          MeasurementTag.IsEmptyResult -> (totalSize == 0).toString,
          MeasurementTag.PlatformId -> propertySearchRequestEnvelop
            .map(_.propertySearchRequestWrapper.globalContext.getPlatformId.getOrElse(0))
            .getOrElse(-1)
            .toString,
          MeasurementTag.NewTrafficType -> propertySearchRequestEnvelop
            .map(
              _.propertySearchRequestWrapper.globalContext.getBotInfo
                .map(
                  BotProfileParser(Seq(BotTypes.VOLUME, BotTypes.VOLUME_HIDDEN)).parse(_).trafficType
                )
                .getOrElse(TrafficType.None)
            )
            .getOrElse("None")
            .toString,
          MeasurementTag.OperationTags -> propertySearchRequestEnvelop
            .map(_.propertySearchRequestWrapper.searchRequest.searchTypeString)
            .getOrElse(None)
            .toString,
          MeasurementTag.PageNumber -> propertySearchRequestEnvelop
            .flatMap(_.propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageNumber))
            .getOrElse(1)
            .toString,
          MeasurementTag.PageSize -> propertySearchRequestEnvelop
            .flatMap(_.propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageSize))
            .getOrElse(10)
            .toString
        )
      )
  }

  def roundNumber(value: Int): Int =
    if (value >= 0 && value <= 5) value
    else if (value > 5 && value < 10) 6
    else (Math.floor(value / 10.0) * 10).toInt

}
