package com.agoda.papi.search.complexsearch.binding

import akka.actor.{ ActorSystem, Scheduler }
import akka.pattern.CircuitBreaker
import akka.stream.{ ActorMaterializer, Materializer }
import cats.MonadError
import cats.effect.IO
import cats.instances.future.catsStdInstancesForFuture
import com.agoda.bundler.packaging.{ BundleService, BundleServiceImpl }
import com.agoda.cassandra.data.Reader
import com.agoda.commons.cache.keybuilders.CacheKeyBuilder
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.http.client.v2.settings.HttpClientSettingsBuilder
import com.agoda.commons.http.client.v2.{ ClientSettings, RequestSettings }
import com.agoda.commons.http.mesh.v2.rr.WeightedRoundRobinStrategy
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.commons.http.mesh.v2.{ RequestSettings => MeshRequestSettings, ServiceMesh }
import com.agoda.commons.logging.metrics.{ AdpMetricsReporter, MetricsReporter }
import com.agoda.commons.sql.hikari.load.balancing.{ DataSourcePoolLoadBalancer, HikariDataSourceResource }
import com.agoda.commons.sql.{ Sql, SqlConnection }
import com.agoda.core.search.models.response.RoomSizeRange
import com.agoda.cql.CQL
import com.agoda.everest.client.{ EverestClient, EverestClientImpl, EverestClientSettings }
import com.agoda.metrics.InstrumentedScheduledMetrics
import com.agoda.npc.client.{ NpcHttpClient, NpcHttpClientSettings }
import com.agoda.papi.search.cacheclient.{ LZ4HCacheCompressor, Protobuf3WithCompressionPropertyInfoListCodec }
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.externaldependency.cache.{
  AgCacheProvider,
  AgCacheSettings,
  CacheConfig,
  ProductHotelSettings
}
import com.agoda.papi.search.common.externaldependency.customer.{ CAPIService, CAPIServiceImpl }
import com.agoda.papi.search.common.externaldependency.repository._
import com.agoda.papi.search.common.externaldependency.vault.VaultAuthenticationSettings
import com.agoda.papi.search.common.handler.datafetcher.pricestream.{
  PriceStreamFetcherService,
  PriceStreamFetcherServiceImpl
}
import com.agoda.papi.search.common.model.KillSwitchConfig
import com.agoda.papi.search.common.service.cancellationpolicy.{
  BNPLService,
  BNPLServiceImpl,
  CancellationPolicyService,
  CancellationPolicyServiceDefault
}
import com.agoda.papi.search.common.service.{
  CalculateChannelService,
  CalculateChannelServiceDefault,
  CMSService,
  ExchangeDataService,
  ExchangeDataServiceImpl,
  LocaleService,
  LocaleServiceImpl,
  PiiEncryptionService,
  PropertyMetaService,
  PropertyMetaServiceImpl,
  RegulatoryBlockingService,
  RequestSegmentMetricsGenerator,
  StatefulCmsServiceDefault,
  SupplierPricingPluginHelper,
  WhiteLabelService,
  WhiteLabelServiceImpl
}
import com.agoda.papi.search.common.util.{ SqlMetricsNewImpl, SqlMetricsV2 }
import com.agoda.papi.search.common.util.circuitbreaker.{
  CircuitBreakerWrapper,
  CircuitBreakerWrapperBuilder,
  ExternalServiceCircuitBreakerSettings,
  ShardLevelCircuitBreaker,
  ShardLevelCircuitBreakerWrapper
}
import com.agoda.papi.search.common.util.constant.Constants
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.common.util.hotelshutdown.{
  HotelShutdownSetting,
  HotelShutdownUtils,
  HotelShutdownUtilsImpl
}
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.common.util.measurement.{
  AsyncHttpClientMeasurementKeys,
  ContextScheduledMetrics,
  MeasurementName,
  MeasurementTag
}
import com.agoda.papi.search.complexsearch.availability.service.helper.{
  CityAvailReadRepositoryWrapper,
  CityAvailReadRepositoryWrapperImpl,
  SupplierDataClientWrapper,
  SupplierDataClientWrapperImpl
}
import com.agoda.papi.search.complexsearch.availability.service.pullcas.{
  PullAvailabilityService,
  PullShardAvailabilityService
}
import com.agoda.papi.search.complexsearch.availability.service.pushcas.{
  PushAvailabilityService,
  PushShardAvailabilityService
}
import com.agoda.papi.search.complexsearch.availability.util.NoOpCQL
import com.agoda.papi.search.complexsearch.configuration.{
  AugmentSearchSetting,
  AugmentSearchSettings,
  CityAugmentSetting,
  CityAugmentSettings,
  ElasticFilterResultSizeSettings,
  EnvironmentalSettings,
  FireDrillServiceSettings,
  JarvisSettings,
  ManualSetting,
  ManualSettings,
  NearestLandmarkSettings,
  NoCCMatrixOptionSetting,
  NoCCMatrixOptionSettings,
  PageSearchSettings,
  PiiEncryptionSettings,
  SecretDealSettings,
  SegmentFilterSetting,
  SegmentFilterSettings,
  SLServiceSettings
}
import com.agoda.papi.search.complexsearch.detail.histogram.HistogramGenerator
import com.agoda.papi.search.complexsearch.externaldependency.elasticsearch.ElasticSearchClient
import com.agoda.papi.search.complexsearch.externaldependency.propertyinfolistcache.{
  PropertyInfoListCacheReader,
  PropertyInfoListCacheReaderImpl,
  PropertyInfoListSettings
}
import com.agoda.papi.search.complexsearch.externaldependency.repository.{
  AccommodationNameRepositoryAgsql,
  AccommodationNameRepositoryAgsqlImpl,
  AgodaHomesRepositoryAgsql,
  AgodaHomesRepositoryAgsqlImpl,
  AreaEnrichmentDetails,
  AreaInfoRepositoryAgsql,
  AreaInfoRepositoryAgsqlImpl,
  AreaNameRepository,
  AreaNameRepositoryAgsqlImpl,
  AreaPopularityByOriginRepository,
  AreaPopularityByOriginRepositoryImpl,
  AreaTopAtmosphereRepositoryAgsql,
  AreaTopAtmosphereRepositoryAgsqlImpl,
  AtmosphereOrderRepositoryAgsql,
  AtmosphereOrderRepositoryAgsqlImpl,
  AveragePriceRepositoryAgsql,
  AveragePriceRepositoryAgsqlImpl,
  BenefitData,
  BenefitRepositoryAgsql,
  BenefitRepositoryAgsqlImpl,
  BigCityRepositorySource,
  BigCityRepositorySourceImpl,
  CampaignRepositoryAgsql,
  CampaignRepositoryAgsqlImpl,
  CityAndCountryNameAgSqlImpl,
  CityAndCountryNameRepository,
  CityCenterInfoRepository,
  CityCenterInfoRepositoryAgsqlImpl,
  CityInfoRepositoryAgsql,
  CityInfoRepositoryAgsqlImpl,
  CityNameRepositoryAgsql,
  CityNameRepositoryAgsqlImpl,
  CitySortMatrixSourceIO,
  CitySortMatrixSourceIOImpl,
  DefaultMaxMinPriceRepository,
  DefaultMaxMinPriceRepositoryAgsqlImpl,
  DMCWithSellabilityEnableRepository,
  DMCWithSellabilityEnableRepositoryAgsqlImpl,
  FacilityClassNameRepositoryAgsql,
  FacilityClassNameRepositoryAgsqlImpl,
  FacilityNameRepositoryAgsql,
  FacilityNameRepositoryAgsqlImpl,
  FilterRankingRepositoryAgsql,
  FilterRankingRepositoryAgsqlImpl,
  HotelChainNameRepository,
  HotelChainNameRepositoryAgsqlImpl,
  HotelChainOrderAgsqlRepositoryImpl,
  HotelChainOrderRepository,
  HotelRoomBedConfigurationAgsqlRepositoryImpl,
  HotelRoomBedConfigurationRepository,
  JTBHotelMappingDataRepository,
  JTBHotelMappingDataRepositoryImpl,
  LandmarkDataRepository,
  LandmarkDataRepositoryAgsqlImpl,
  LandmarkInfoRepository,
  LandmarkInfoRepositoryAgsqlImpl,
  LandmarkNameRepository,
  LandmarkNameRepositoryAgsqlImpl,
  LandmarkPrioritizedRepositoryAgsql,
  LandmarkPrioritizedRepositoryAgsqlImpl,
  MatrixAccommodationName,
  MatrixFacilityClassName,
  MatrixFacilityName,
  NumberOfReturnCustomerRepository,
  NumberOfReturnCustomerRepositoryImpl,
  PopularFilterAndOrderingRepository,
  PopularFilterAndOrderingRepositoryAgsqlImpl,
  RegionInfoRepository,
  RegionInfoRepositoryAgsqlImpl,
  RoomSizeRangeRepositoryAgsql,
  RoomSizeRangeRepositoryAgsqlImpl,
  StateInfoRepository,
  StateInfoRepositoryAgsqlImpl,
  TopAreaInfoRepository,
  TopAreaInfoRepositoryAgsqlImpl
}
import com.agoda.papi.search.complexsearch.filter.elastic.config.{ ElasticSetting, ElasticSettings }
import com.agoda.papi.search.complexsearch.filter.elastic.{
  FilterMatrixSortSetting,
  FilterMatrixSortSettings,
  QueryBuilder
}
import com.agoda.papi.search.complexsearch.filter.pre.{ PropertySelectorForFilter, PropertySelectorForFilterImpl }
import com.agoda.papi.search.complexsearch.filter.{ FilteringService, FilteringServiceImpl }
import com.agoda.papi.search.complexsearch.flow.{
  AvailabilityComposer,
  AvailabilityComposerImpl,
  CBManualStatusCache,
  FilterFlow,
  FilterFlowImpl,
  ManualFlow,
  ManualFlowDefault,
  PagingFlow,
  PagingFlowImpl,
  PropertiesFetcherFlow,
  SortingFlow,
  SortingFlowImpl
}
import com.agoda.papi.search.complexsearch.helper.{
  ElasticMatrixHelper,
  ElasticMatrixHelperImpl,
  PersonalizedPropertiesHelper,
  PersonalizedPropertiesHelperImpl,
  ReviewLocationScoreCmsHelper,
  ReviewLocationScoreCmsHelperImpl,
  ReviewScoreCmsHelper,
  ReviewScoreCmsHelperImpl
}
import com.agoda.papi.search.complexsearch.logging.CoreHadoopLogger
import com.agoda.papi.search.complexsearch.matrix.derived.{
  PopularFilterService,
  PopularFilterServiceImpl,
  RecommendedFiltersService,
  RecommendedFiltersServiceImpl,
  SegmentFilterService,
  SegmentFilterServiceImpl
}
import com.agoda.papi.search.complexsearch.matrix.enrich.{ MatrixNameService, MatrixNameServiceImpl }
import com.agoda.papi.search.complexsearch.matrix.sort.{
  FamilyFiltersService,
  FamilyFiltersServiceImpl,
  FilterMatrixFromElasticSortingService,
  FilterMatrixFromElasticSortingServiceImpl,
  FilterMatrixManipulatingService,
  FilterMatrixManipulatingServiceImpl,
  FilterRankingService,
  FilterRankingServiceImp,
  GroupTravellerFilterService,
  GroupTravellerFilterServiceImpl
}
import com.agoda.papi.search.complexsearch.matrix.{
  CustomMatrixService,
  ElasticDistanceService,
  ElasticDistanceServiceImpl,
  ElasticMatrixService,
  ElasticMatrixServiceImpl
}
import com.agoda.papi.search.common.service.fs.{ CacheFeatureStoreService, FeatureStoreService }
import com.agoda.papi.search.complexsearch.paging.{
  AgodaHomesPaging,
  AgodaHomesPagingDefault,
  ExtraHotelPaging,
  ExtraHotelPagingDefault,
  LuxuryHotelsPaging,
  LuxuryHotelsPagingDefault,
  PulsePropertiesPaging,
  PulsePropertiesPagingDefault,
  SoldOutPaging,
  SoldOutPagingImpl
}
import com.agoda.papi.search.complexsearch.search.{
  MatrixFlow,
  MatrixFlowImpl,
  PostFilterFlow,
  PostFilterFlowImpl,
  SearchFlow,
  SearchFlowImpl
}
import com.agoda.papi.search.complexsearch.service.paging.{ PageTokenService, PageTokenServiceImpl }
import com.agoda.papi.search.complexsearch.service.{
  AggregationService,
  AggregationServiceImpl,
  ASQService,
  ASQServiceImpl,
  ChannelService,
  ChannelServiceDefault,
  CityAvailSPCallReportingService,
  CMSHelperService,
  CMSHelperServiceImpl,
  CoreSearchAgodaHomesService,
  CoreSearchAgodaHomesServiceImpl,
  CoreSearchLuxuryHotelsService,
  CoreSearchLuxuryHotelsServiceImpl,
  CoreSearchPulsePropertiesService,
  CoreSearchPulsePropertiesServiceImpl,
  HeuristicAccuracyReporter,
  HotelSortAggregationService,
  HotelSortAggregationServiceImpl,
  NearestLandmarkService,
  NearestLandmarkServiceDefault,
  ObjectInfoService,
  ObjectInfoServiceImpl,
  PackagingService,
  PackagingServiceImpl,
  PostFilterService,
  PostFilterServiceImpl,
  PropertyMetadataService,
  RoomSizeHistogramService,
  RoomSizeHistogramServiceImpl,
  SellabilityService,
  SellabilityServiceImpl,
  SLService,
  SLServiceImpl,
  SoldOutPropsPriceService,
  SoldOutPropsPriceServiceImpl,
  TealiumService,
  TealiumServiceImpl
}
import com.agoda.papi.search.complexsearch.sorting.jarvis.{
  JarvisClient,
  JarvisClientDefault,
  JarvisService,
  JarvisServiceDefault
}
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.RocketmilesRankingClient
import com.agoda.papi.search.complexsearch.sorting.{
  HotelDataPreparationFlow,
  SortingService,
  SortingServiceImpl,
  SortMatrixService,
  SortMatrixServiceImpl
}
import com.agoda.papi.search.complexsearch.util.ElasticFallbackFilterUtil
import com.agoda.papi.search.internalmodel.configuration.{
  HeuristicAccuracyCheckerSettings,
  PAPIMessageSettings,
  TopBookedCitiesSetting,
  TopBookedCitiesSettings
}
import com.agoda.papi.search.internalmodel.database.{
  AFMSetting,
  AreaInfo,
  AreaPopularity,
  AugmentedResult,
  AverageRoomPrice,
  CancellationPolicyInfo,
  CategoryPriceValue,
  CidInfo,
  CityAndCountryName,
  CityCenterInfo,
  CityInfo,
  CitySortMatrix,
  DefaultMatrixName,
  ExchangeData,
  FilterScore,
  LandmarkData,
  LandmarkInfoBySubTypeCategory,
  LandmarkPrioritizeData,
  MaxMinPriceWithPrecision,
  NumberOfReturnCustomer,
  ObjectExtraInfoDB,
  PopularFilter,
  PriceConfig,
  ProductHotels,
  RegionInfo,
  RoomBedConfigurations,
  StateInfo,
  TopAreaInfo
}
import com.agoda.papi.search.types._
import com.agoda.papi.cancellation.CxlPolicyService
import com.agoda.pricestream.client.{ PriceStreamClientSettings, PriceStreamHttpClient }
import com.agoda.scapi.sqlserver.CityAvailReadRepository
import com.agoda.scapi.sqlserver.settings.{ SQLServerSettings => SupplyCacheSQLServerSettings }
import com.agoda.scapi.sqlserver.util.CityAvailRepositoryBuilder
import com.agoda.scapi.sqlserver.util.measurement.{ MeasurementContext => ScapiMeasurementContext }
import com.agoda.supplier.client.{ SupplierClient, SupplierClientImpl, SupplierCQLDataClient }
import com.agoda.supplier.common.services.{ SupplierMetadataService, SupplierMetadataServiceImpl }
import com.agoda.supplier.core._
import com.agoda.supplier.services.SupplierExperimentServiceImpl
import com.agoda.universal.cms.client.context.{ SQLContext => CmsSqlContext }
import com.agoda.universal.cms.client.service.{ UniversalCmsService, UniversalCmsServiceDefault }
import com.agoda.whitelabel.client.{ ApiInvoker, WhiteLabelClientMultiEnv }
import com.agoda.zenith.client.ZenithHttpClient
import com.datastax.driver.core.Session
import com.sksamuel.elastic4s.ElasticClient
import com.typesafe.config.{ Config, ConfigFactory }
import io.opentelemetry.api.OpenTelemetry
import io.opentracing.Tracer
import org.asynchttpclient.{ AsyncHttpClient, DefaultAsyncHttpClient, DefaultAsyncHttpClientConfig }
import org.asynchttpclient.config.AsyncHttpClientConfigDefaults
import scalacache.{ AbstractCache, CacheConfig => ScalacacheCacheconfig }
import scaldi.Module
import sttp.client3.SttpBackend
import sttp.client3.asynchttpclient.future.AsyncHttpClientFutureBackend

import java.sql.Connection
import java.util.concurrent.TimeUnit
import scala.concurrent.duration._
import scala.concurrent.{ ExecutionContext, ExecutionContextExecutor, Future }
import scala.util.Try
import com.agoda.papi.search.cacheclient.repository.{
  BigCityRepositorySource => CachePopulatorBigCityRepositorySource,
  BigCityRepositorySourceImpl => CachePopulatorBigCityRepositorySourceImpl,
  CityIdsFetcherRepositoryAgsql => CachePopulatorCityIdsFetcherRepositoryAgsql,
  CityIdsFetcherRepositoryAgsqlImpl => CachePopulatorCityIdsFetcherRepositoryAgsqlImpl,
  PropertyInfoRepositoryAgsql => CachePopulatorPropertyInfoRepositoryAgsql,
  PropertyInfoRepositoryAgsqlImpl => CachePopulatorPropertyInfoRepositoryAgsqlImpl
}
import com.agoda.papi.search.cacheclient.transformer.PropertyInfoListTransformer
import com.agoda.papi.search.cacheclient.{ CDBService, CDBServiceImpl }
import com.agoda.papi.search.complexsearch.externaldependency.npc.NpcClient
import com.agoda.papi.search.property.externaldependency.whitelabel.WhiteLabelSetting
import com.agoda.whitelabel.client.api.FeatureConfigurationApi
import com.sun.jersey.api.client.Client
import com.agoda.commons.couchbase.ReadStrategy.{
  ReadWithCustomTimeoutAndFallbackToReplica,
  ReadWithTimeoutAndFallbackToReplica
}
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.util.RocketmilesRankingExpUtils

object Bindings {

  val config = ConfigFactory.load()

  object AkkaSystem {
    val hostname = Constants.HostName
    implicit val actorSystem: ActorSystem =
      ActorSystem("akka-actor", None, None, Some(ExecutionContexts.defaultContext))
    implicit val actorMaterializer: ActorMaterializer = ActorMaterializer()
  }

  // TODO: Temporary have been moved from API to keep only one actor system at play
  class AkkaModule extends Module {
    (binding to AkkaSystem.actorSystem).destroyWith(_.terminate())
    bind[ActorMaterializer] to AkkaSystem.actorMaterializer
  }

  trait Ec {
    implicit val ec: ExecutionContext = ExecutionContexts.defaultContext
  }

  val defaultEC = ExecutionContexts.defaultContext

  trait BaseReportingModule extends ContextScheduledMetrics {
    //  ToDO: use proper injection
    override val reportingService: MetricsReporter = AdpMetricsReporter
  }

  private val vaultCredentialsOpt =
    if (config.getBoolean("ag-vault.enable")) Some(VaultAuthenticationSettings(config)) else None

  class AvailabilitiesModule extends Module with PAPIAppLogger {

    def buildCqlContext(cql: CQL): CQLContext =
      // scalastyle:off
      new CQLContext {
        override def CQLWrapper[T](metricTag: String)(block: Reader => Future[T]): Future[T] =
          block(cql.cassandraSwitcher.currentCassandra.reader)

        override def CQL[T](metricTag: String)(block: Session => T): T =
          block(cql.cassandraSwitcher.currentCassandra.session)

        override def cqlEnabled: Boolean = false
      }
    // scalastyle:on

    // SQL
    // scalastyle:off
    val supplierCommonSqlContext = new com.agoda.supplier.common.core.SQLContext {

      override def SQL[T](block: (Connection) => T) =
        inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](connection => block(connection))

    }

    val sqlContext = new BaseReportingModule with SQLContext {

      override def SQL[T](metricTag: String)(block: Connection => T): T =
        inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](connection =>
          timerWithoutSampling(
            MeasurementName.SqlContext,
            Map(
              "step"                    -> metricTag,
              MeasurementTag.ClientName -> "supplier-client"
            )
          )(block(connection))
        )

    }
    // scalastyle:on

    val supplierClient: SupplierClient = new SupplierClientImpl(config, sqlContext)
    bind[SupplierClient] to supplierClient

    val supplierExperimentService = new SupplierExperimentServiceImpl()

    bind[MeasurementContext] to new MeasurementContext {
      private val reportingService = inject[MetricsReporter]
      override def sendMetric(metric: String, value: Long, tags: Map[String, String]): Unit =
        reportingService.report(metric, value, tags)
    }

    bind[AggregateContext] to new AggregateContext {
      private val reportingService = inject[MetricsReporter]

      override def aggregate(
          metricKey: String,
          count: Long = 1L,
          tags: Map[String, String] = Map.empty[String, String]
      ): Unit =
        reportingService.report(metricKey, count, tags)

      override def count(metricKey: String, count: Long, tags: Map[String, String]): Unit =
        aggregate(metricKey, count, tags)
    }

    val supplierMetadataService: SupplierMetadataService = new SupplierMetadataServiceImpl(supplierCommonSqlContext)
    bind[SupplierMetadataService] to supplierMetadataService

    // SQL
    val cql: CQL = NoOpCQL
    bind[CQL] to cql
    val cqlContext = buildCqlContext(cql)

    bind[CQLContext] to cqlContext

    val shardSetting = vaultCredentialsOpt
      .map(credentialProvider =>
        SupplyCacheSQLServerSettings(
          config,
          credentialProvider.getShardSQLAuthenticationSettings,
          "shard-availabilities-settings"
        )
      )
      .getOrElse(SupplyCacheSQLServerSettings.fromConfig(config, "shard-availabilities-settings"))
      .get

    bind[Option[VaultAuthenticationSettings]] to vaultCredentialsOpt

    val shardAuthenticationState: State[com.agoda.scapi.sqlserver.settings.AuthenticationSettings] = vaultCredentialsOpt
      .map(_.getShardSQLAuthenticationSettings)
      .getOrElse(
        State.of(
          com.agoda.scapi.sqlserver.settings
            .AuthenticationSettings(shardSetting.username.getOrElse(""), shardSetting.password.getOrElse(""))
        )
      )

    bind[CityAvailReadRepository] to CityAvailRepositoryBuilder.defaultInstance
      .withSQLServerSettings(shardSetting)
      .withMeasurementContext(new ScapiMeasurementContext {
        private val reportingService = inject[MetricsReporter]

        override def prefix: String = "scapi"

        override protected def report(name: String, value: Long, tags: Map[String, String]): Unit =
          reportingService.report(name, value, tags)
      })
      .withExecutionContext(defaultEC)
      .withTracer(inject[Tracer])
      .withAuthenticationSettings(shardAuthenticationState)
      .buildHighAvailableCityAvailRepository()
      .get

    bind[SupplierDataClientWrapper] to new SupplierDataClientWrapperImpl {
      override val cqlContext: CQLContext                       = inject[CQLContext]
      override val supplierCQLDataClient: SupplierCQLDataClient = inject[SupplierCQLDataClient]
      override val schedulerContext: SchedulerContext =
        SupplierPricingPluginHelper.createSchedulerContext(inject[ActorSystem])
      override val supplierClient: SupplierClient = inject[SupplierClient]
      override val circuitBreaker: ShardLevelCircuitBreakerWrapper =
        inject[ShardLevelCircuitBreakerWrapper]
    }

    bind[CityAvailSPCallReportingService] to injected[CityAvailSPCallReportingService]

    bind[PushAvailabilityService] to new PushShardAvailabilityService with ContextScheduledMetrics {
      override val reportingService: MetricsReporter          = inject[MetricsReporter]
      override val supplierWrapper: SupplierDataClientWrapper = inject[SupplierDataClientWrapper]
      override val tracer: Tracer                             = inject[Tracer]
      override val cityAvailSPCallReportingService: CityAvailSPCallReportingService =
        inject[CityAvailSPCallReportingService]
      override val heuristicAccuracyCheckerSettings: HeuristicAccuracyCheckerSettings =
        inject[HeuristicAccuracyCheckerSettings]

      override val cityAvailReadRepositoryWrapper: CityAvailReadRepositoryWrapper =
        inject[CityAvailReadRepositoryWrapper]
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
    }

    bind[CityAvailReadRepositoryWrapper] to injected[CityAvailReadRepositoryWrapperImpl]

    // pull
    bind[SupplierCQLDataClient] to new SupplierCQLDataClient(
      ConfigFactory.load(),
      supplierClient,
      supplierExperimentService,
      None,
      Some(inject[CityAvailReadRepository]),
      None,
      inject[Tracer],
      allowNoPullExpiry = false
    )(inject[ActorSystem], defaultEC, inject[MeasurementContext], inject[AggregateContext])

    bind[PullAvailabilityService] to new PullShardAvailabilityService with ContextScheduledMetrics {
      override val reportingService: MetricsReporter                = inject[MetricsReporter]
      override val languageRepository: LanguageRepository           = inject[LanguageRepository]
      override val tracer: Tracer                                   = inject[Tracer]
      override val supplierClientWrapper: SupplierDataClientWrapper = inject[SupplierDataClientWrapper]
      override val cityAvailSPCallReportingService: CityAvailSPCallReportingService =
        inject[CityAvailSPCallReportingService]
      override val heuristicAccuracyCheckerSettings: HeuristicAccuracyCheckerSettings =
        inject[HeuristicAccuracyCheckerSettings]
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
    }

    bind[ShardLevelCircuitBreakerWrapper] to ShardLevelCircuitBreaker(
      ExecutionContexts.defaultContext,
      inject[ActorSystem].scheduler,
      inject[ExternalServiceCircuitBreakerSettings].shardCircuitBreakerSettings,
      inject[MetricsReporter]
    )

    bind[AvailabilityComposer] to new BaseReportingModule with AvailabilityComposerImpl {
      override val pullAvailabilityService: PullAvailabilityService = inject[PullAvailabilityService]
      override val pushAvailabilityService: PushAvailabilityService = inject[PushAvailabilityService]
      override val supplierClient: SupplierClient                   = inject[SupplierClient]
      override val cqlContext: CQLContext                           = inject[CQLContext]
      override val tracer: Tracer                                   = inject[Tracer]
    }

  }

  val agCacheProvider =
    new AgCacheProvider(
      AdpMetricsReporter,
      config,
      vaultCredentialsOpt.map(_.getCouchbaseAuthenticationSettings),
      vaultCredentialsOpt.flatMap(_.getCouchbaseCBDATAAuthenticationSettigs)
    )

  class RepositoryModule extends Module {

    bind[ExecutionContext] to ExecutionContexts.defaultContext
    bind[InstrumentedScheduledMetrics] to InstrumentedScheduledMetrics.apply(AdpMetricsReporter)

    val agCacheProvider =
      new AgCacheProvider(
        AdpMetricsReporter,
        config,
        vaultCredentialsOpt.map(_.getCouchbaseAuthenticationSettings),
        vaultCredentialsOpt.flatMap(_.getCouchbaseCBDATAAuthenticationSettigs)
      )

    private val agCacheConfig = AgCacheSettings()

    bind[BigCityRepositorySource] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with BigCityRepositorySourceImpl
    bind[LanguageRepositorySourceImpl] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with LanguageRepositorySourceImpl

    bind[LanguageRepository] to new LanguageRepositoryImpl {
      override val languageRepositorySource = inject[LanguageRepositorySourceImpl]
    }

    bind[JTBHotelMappingDataRepository].toNonLazy(
      new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]]) with JTBHotelMappingDataRepositoryImpl
    )

    bind[ExchangeDataRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with ExchangeDataRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Option[ExchangeData]] =
        agCacheProvider.getLocalCache(
          cacheName = "ExchangeDataRepositoryAgsql",
          localCacheCapacity = Some(5000),
          cacheConfig = CacheConfig.configForMethod
        )

    }

    bind[IO[Sql[IO, SqlConnection[IO]]]] to inject[IO[Sql[IO, SqlConnection[IO]]]]
    bind[SqlMetricsV2] to new SqlMetricsNewImpl(inject[MetricsReporter], defaultEC)

    bind[AbstractCache[Option[LandmarkData]]] to agCacheProvider.getL1L2Cache[Option[LandmarkData]](
      "LandmarkDataRepositoryAgsql"
    )

    bind[LandmarkDataRepository] to injected[LandmarkDataRepositoryAgsqlImpl]

    bind[TopAreaInfoRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with TopAreaInfoRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[TopAreaInfo]] =
        agCacheProvider.getL1L2Cache("TopAreaInfoRepositoryAgsql")
    }

    bind[RegionInfoRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with RegionInfoRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[RegionInfo]] =
        agCacheProvider.getL1L2Cache("RegionInfoRepositoryAgsql")
    }

    bind[MapDisplayInfoRepository] to new MapDisplayInfoRepositoryImpl {}

    bind[LandmarkInfoRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with LandmarkInfoRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[LandmarkInfoBySubTypeCategory] =
        agCacheProvider.getL1L2Cache("LandmarkInfoRepositoryAgsql")
    }

    bind[LandmarkNameRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with LandmarkNameRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[DefaultMatrixName] =
        agCacheProvider.getL1L2Cache("LandmarkNameRepositoryAgsql")
    }

    bind[HotelChainOrderRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with HotelChainOrderAgsqlRepositoryImpl {
      implicit override protected val agCache: AbstractCache[Vector[HotelChainIdType]] =
        agCacheProvider.getL1L2Cache("HotelChainOrderAgsqlRepository")
    }

    bind[AreaPopularityByOriginRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AreaPopularityByOriginRepositoryImpl {

      implicit override val agCache: AbstractCache[AreaPopularity] =
        agCacheProvider.getL1L2Cache("AreaPopularityByOriginRepository")
    }

    bind[AreaInfoRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AreaInfoRepositoryAgsqlImpl {

      implicit override val agCache: AbstractCache[Option[AreaInfo]] =
        agCacheProvider.getL1L2Cache("AreaInfoRepositoryAgsql")
    }

    bind[StateInfoRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with StateInfoRepositoryAgsqlImpl {

      implicit override val agCache: AbstractCache[Option[StateInfo]] =
        agCacheProvider.getL1L2Cache("StateInfoRepository")
    }

    bind[FilterRankingRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with FilterRankingRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Vector[FilterScore]] =
        agCacheProvider.getL1L2Cache("FilterRankingRepositoryAgsql")
    }

    bind[AveragePriceRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AveragePriceRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Vector[(String, CategoryPriceValue)]] =
        agCacheProvider.getL1L2Cache("AveragePriceRepositoryAgsql")
    }

    bind[CityInfoRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CityInfoRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Option[CityInfo]] =
        agCacheProvider.getL1L2Cache("CityInfoRepositoryAgsql")
    }

    bind[LandmarkPrioritizedRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with LandmarkPrioritizedRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[LandmarkPrioritizeData] =
        agCacheProvider.getL1L2Cache("LandmarkPrioritizedRepositoryAgsql")
    }

    bind[ObjectInfoRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with ObjectInfoRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[ObjectExtraInfoDB] =
        agCacheProvider.getL1L2Cache("ObjectInfoRepositoryAgsql")
    }

    bind[PriceConfigRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with PriceConfigRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[PriceConfig] =
        agCacheProvider.getL1L2Cache("PriceConfigRepositoryAgsql")
    }

    bind[ProductHotelSettings] to ProductHotelSettings(config)

    bind[ProductHotelRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with ProductHotelRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[ProductHotels] = agCacheProvider
        .getL1L2Cache(
          "ProductHotelRepositoryAgsql",
          localCacheCapacity = Some(agCacheConfig.productHotelsLocalCacheCapacity),
          overrideReadStrategy =
            Some(ReadWithCustomTimeoutAndFallbackToReplica(inject[ProductHotelSettings].couchbaseReadTimeout))
        )

    }

    bind[CachePopulatorBigCityRepositorySource] to new CachePopulatorBigCityRepositorySourceImpl(
      inject[IO[Sql[IO, SqlConnection[IO]]]]
    )

    bind[CachePopulatorPropertyInfoRepositoryAgsql] to new CachePopulatorPropertyInfoRepositoryAgsqlImpl(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[CachePopulatorBigCityRepositorySource]
    )

    bind[CachePopulatorCityIdsFetcherRepositoryAgsql] to new CachePopulatorCityIdsFetcherRepositoryAgsqlImpl(
      inject[IO[Sql[IO, SqlConnection[IO]]]]
    )

    bind[PopularFilterAndOrderingRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with PopularFilterAndOrderingRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[(Vector[PopularFilter], Vector[PopularFilter])] =
        agCacheProvider.getL1L2Cache("PopularFilterAndOrderingRepositoryAgsql")
    }

    bind[SoldOutPropsPriceRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with SoldOutPropsPriceRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[(Int, AverageRoomPrice)]] =
        agCacheProvider.getL1L2Cache("SoldOutPropsPriceRepositoryAgsql")
    }

    bind[HotelRoomBedConfigurationRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with HotelRoomBedConfigurationAgsqlRepositoryImpl {
      implicit override protected val agCache: AbstractCache[RoomBedConfigurations] =
        agCacheProvider.getL1L2Cache("HotelRoomBedConfigurationAgsqlRepository")
    }

    bind[AFMSettingRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AFMSettingRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Vector[AFMSetting]] =
        agCacheProvider.getLocalCache(
          cacheName = "AFMSettingRepositoryAgsql",
          localCacheCapacity = Some(10),
          cacheConfig = CacheConfig.configForMethod
        )

    }

    bind[AreaNameRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AreaNameRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[DefaultMatrixName] =
        agCacheProvider.getL1L2Cache("AreaNameRepositoryAgsql")
    }

    bind[BNPLHotelRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with BNPLHotelRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[Boolean]] =
        agCacheProvider.getL1L2Cache("BNPLRepositoryAgsql", localCacheCapacity = Some(100000))
    }

    bind[CancellationPolicyRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CancellationPolicyRepositoryImpl {
      implicit override protected val agCache: AbstractCache[Vector[CancellationPolicyInfo]] =
        agCacheProvider.getL1L2Cache("CancellationPolicyRepositoryAgsql")
    }

    bind[CityCenterInfoRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CityCenterInfoRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[CityCenterInfo]] =
        agCacheProvider.getL1L2Cache("CityCenterInfoRepositoryAgsql")
    }

    bind[DefaultMaxMinPriceRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with DefaultMaxMinPriceRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[MaxMinPriceWithPrecision]] =
        agCacheProvider.getL1L2Cache("DefaultMaxMinPriceRepositoryAgsql")
    }

    (bind[DMCWithSellabilityEnableRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with DMCWithSellabilityEnableRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Vector[ProviderIdType]] = agCacheProvider.getLocalCache(
        cacheName = "DMCWithSellabilityEnableRepositoryAgSql",
        localCacheCapacity = Some(10),
        cacheConfig = CacheConfig.configForMethod
      )

    }).identifiedBy('dmcWithSellabilityEnableRepositoryAgsql)

    bind[HotelChainNameRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with HotelChainNameRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[DefaultMatrixName] =
        agCacheProvider.getL1L2Cache("HotelChainNameRepositoryAgsql")
    }

    bind[SupplierBNPLEligibilityRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with SupplierBNPLEligibilityRepositoryAgsqlImpl {

      implicit override protected val agCache: AbstractCache[Vector[(HotelChainIdType, Boolean)]] =
        agCacheProvider.getLocalCache(
          cacheName = "SupplierBNPLEligibilityRepositoryAgsql",
          localCacheCapacity = Some(10),
          cacheConfig = CacheConfig.configForMethod
        )

    }

    bind[CitySortMatrixSourceIO] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]], defaultEC)
      with CitySortMatrixSourceIOImpl {
      implicit override protected val agCache: AbstractCache[Vector[CitySortMatrix]] =
        agCacheProvider.getL1L2Cache("CitySortMatrixSourceIO")
    }

    bind[AbstractCache[MatrixFacilityClassName]] to agCacheProvider.getL1L2Cache[MatrixFacilityClassName](
      "FacilityClassNameRepositoryAgsql"
    )

    bind[FacilityClassNameRepositoryAgsql] to injected[FacilityClassNameRepositoryAgsqlImpl]

    bind[AtmosphereOrderRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AtmosphereOrderRepositoryAgsqlImpl {
      implicit override val agCache: AbstractCache[Vector[(AtmosphereIdType, Int)]] =
        agCacheProvider.getL1L2Cache("AtmosphereOrderRepositoryAgsql")
    }

    bind[AreaTopAtmosphereRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AreaTopAtmosphereRepositoryAgsqlImpl {
      implicit override val agCache: AbstractCache[Vector[(AreaIdType, AreaEnrichmentDetails)]] =
        agCacheProvider.getL1L2Cache("AreaTopAtmosphereRepositoryAgsql")
    }

    bind[AbstractCache[MatrixFacilityName]] to agCacheProvider.getL1L2Cache[MatrixFacilityName](
      "FacilityNameRepositoryAgsql"
    )

    bind[FacilityNameRepositoryAgsql] to injected[FacilityNameRepositoryAgsqlImpl]

    bind[AbstractCache[MatrixAccommodationName]] to agCacheProvider.getL1L2Cache[MatrixAccommodationName](
      "AccommodationNameRepositoryAgsql"
    )

    bind[AccommodationNameRepositoryAgsql] to injected[AccommodationNameRepositoryAgsqlImpl]

    bind[CityNameRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CityNameRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[DefaultMatrixName] =
        agCacheProvider.getL1L2Cache("CityNameRepositoryAgsql")
    }

    bind[AgodaHomesRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with AgodaHomesRepositoryAgsqlImpl {
      implicit override val agCache: AbstractCache[Vector[HotelIdType]] =
        agCacheProvider.getL1L2Cache("AgodaHomesRepositoryAgsql")
    }

    bind[CampaignRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CampaignRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[HotelIdType]] =
        agCacheProvider.getL1L2Cache("CampaignRepositoryAgsql")
    }

    bind[CityAndCountryNameRepository] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CityAndCountryNameAgSqlImpl {
      implicit override protected val agCache: AbstractCache[Option[CityAndCountryName]] =
        agCacheProvider.getL1L2Cache("CityAndCountryNameRepository")
    }

    bind[RoomSizeRangeRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with RoomSizeRangeRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[RoomSizeRange] =
        agCacheProvider.getL1L2Cache("RoomSizeMaxMinRepositoryAgsql")
    }

    bind[CidMappingRepositoryAgsql] to new BaseRepositoryIOModule(inject[IO[Sql[IO, SqlConnection[IO]]]])
      with CidMappingRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[CidInfo]] =
        agCacheProvider.getL1L2Cache("CidMappingRepositoryAgsql")
    }

    bind[BenefitRepositoryAgsql] to new BaseRepositoryIOModule(
      inject[IO[Sql[IO, SqlConnection[IO]]]]
    ) with BenefitRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[BenefitData]] =
        agCacheProvider.getL1L2Cache("BenefitRepositoryAgsql")
    }

    bind[NumberOfReturnCustomerRepository] to new BaseRepositoryIOModule(
      inject[IO[Sql[IO, SqlConnection[IO]]]]
    ) with NumberOfReturnCustomerRepositoryImpl {
      implicit override protected val agCache: AbstractCache[Option[NumberOfReturnCustomer]] =
        agCacheProvider.getL1L2Cache("NumberOfReturnCustomerRepository", localCacheCapacity = Some(50000))
    }

    class BaseRepositoryIOModule(
        val ioSql: IO[Sql[IO, SqlConnection[IO]]],
        val ec: ExecutionContext = ExecutionContexts.defaultContext,
        val dbEc: ExecutionContext = ExecutionContexts.defaultContext
    ) extends BaseReportingModule

  }

  class ServiceModule extends Module {

    bind[Config] to config

    bind[HeuristicAccuracyCheckerSettings] to HeuristicAccuracyCheckerSettings.fromConfig(inject[Config])

    val environmentalConfigs = EnvironmentalSettings(config)
    bind[EnvironmentalSettings] to environmentalConfigs

    bind[MetricsReporter] to AdpMetricsReporter

    // Whitelabel client settings
    lazy val whiteLabelSetting = WhiteLabelSetting(config)

    // this is the workaround to override whitelabel client timeout sine the library do not allow us to do via config
    val apiInvoker = new ApiInvoker {

      override def newClient(
          host: String,
          isAsync: Boolean = false,
          connectionTimeOutInMs: Int = whiteLabelSetting.connectionTimeoutMs.toInt,
          requestTimeoutInMs: Int = whiteLabelSetting.requestTimeoutMs.toInt
      ): Client =
        super.newClient(host, connectionTimeOutInMs = connectionTimeOutInMs, requestTimeoutInMs = requestTimeoutInMs)

    }

    val featureConfigurationApi = new FeatureConfigurationApi(defApiInvoker = apiInvoker)

    bind[WhiteLabelClientMultiEnv] to WhiteLabelClientMultiEnv(
      whiteLabelSetting.host,
      whiteLabelSetting.appName,
      whiteLabelSetting.appName,
      maxAttempts = whiteLabelSetting.maxAttempts,
      featureConfigurationAPI = featureConfigurationApi
    )

    bind[WhiteLabelService] to new WhiteLabelServiceImpl {
      override val client: WhiteLabelClientMultiEnv = inject[WhiteLabelClientMultiEnv]
    }

    // scalastyle:off
    bind[CMSService] to new BaseReportingModule with StatefulCmsServiceDefault {
      private val AppName = "PAPI"

      private val cmsSqlContext: CmsSqlContext = new CmsSqlContext {

        override def SQL[T](metricTag: String)(block: Connection => T): T =
          inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](connection =>
            timerWithoutSampling(
              MeasurementName.SqlContext,
              Map(
                "step"                    -> metricTag,
                MeasurementTag.ClientName -> "cms-client"
              )
            )(block(connection))
          )

      }

      override val cmsService: UniversalCmsService = new UniversalCmsServiceDefault(cmsSqlContext, AppName)
    }

    // scalastyle:on

    class BaseServiceModuleCached(val ec: ExecutionContext = ExecutionContexts.defaultContext)
        extends BaseReportingModule

    bind[PageSearchSettings] to PageSearchSettings()

    bind[TopBookedCitiesSetting] to TopBookedCitiesSettings()

    bind[PAPIMessageSettings] to PAPIMessageSettings()

    bind[PageTokenService] to new PageTokenServiceImpl {}

    bind[CircuitBreakerWrapper]
      .identifiedBy('CAPICircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].capiCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[CAPIService] to new BaseServiceModuleCached(ExecutionContexts.defaultContext) with CAPIServiceImpl {
      implicit override val actorSystem: ActorSystem   = inject[ActorSystem]
      implicit override val materializer: Materializer = inject[ActorMaterializer]
      implicit override val capiEC: ExecutionContext   = ExecutionContexts.defaultContext
      override val tracer: Tracer                      = inject[Tracer]
      override val circuitBreaker: CircuitBreakerWrapper =
        inject[CircuitBreakerWrapper](identified by 'CAPICircuitBreaker)
    }

    bind[ExchangeDataService] to new ExchangeDataServiceImpl {
      override val exchangeDataRepository: ExchangeDataRepository = inject[ExchangeDataRepository]
    }

    bind[LocaleService] to injected[LocaleServiceImpl]

    bind[ElasticFilterResultSizeSettings] to ElasticFilterResultSizeSettings()

    bind[QueryBuilder] to new QueryBuilder {
      override val settings: ElasticSetting = inject[ElasticSetting]
      override val elasticFilterResultSizeSettings: ElasticFilterResultSizeSettings =
        inject[ElasticFilterResultSizeSettings]
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
    }

    bind[SellabilityService] to new SellabilityServiceImpl {
      override val dmcWithSellabilityRepositoryAgsql: DMCWithSellabilityEnableRepository =
        inject[DMCWithSellabilityEnableRepository]('dmcWithSellabilityEnableRepositoryAgsql)
      implicit override val ec: ExecutionContext = defaultEC
    }

    bind[ElasticFallbackFilterUtil] to new ElasticFallbackFilterUtil

    bind[CircuitBreakerWrapper].identifiedBy('MandatoryElasticSearchCircuitBreaker) to ElasticSearchClient
      .createESCircuitBreakerWrapper(
        inject[ExternalServiceCircuitBreakerSettings].mandatoryESCircuitBreakerSettings,
        inject[MetricsReporter]
      )(inject[ActorSystem].scheduler, ExecutionContexts.defaultContext)

    bind[CircuitBreakerWrapper].identifiedBy('OptionalElasticSearchCircuitBreaker) to ElasticSearchClient
      .createESCircuitBreakerWrapper(
        inject[ExternalServiceCircuitBreakerSettings].optionalESCircuitBreakerSettings,
        inject[MetricsReporter]
      )(inject[ActorSystem].scheduler, ExecutionContexts.defaultContext)

    (bind[ElasticClient].identifiedBy('OptionalElasticClient) to ElasticSearchClient.createElasticClient(
      inject[ElasticSetting],
      true
    )).destroyWith(_.close())

    (bind[ElasticClient].identifiedBy('MandatoryElasticClient) to ElasticSearchClient.createElasticClient(
      inject[ElasticSetting],
      false
    )).destroyWith(_.close())

    bind[ElasticSearchClient].identifiedBy('OptionalElasticSearchClient) to new ElasticSearchClient(
      inject[ElasticSetting],
      inject[ElasticClient](identified by 'OptionalElasticClient),
      inject[MetricsReporter],
      inject[CircuitBreakerWrapper](identified by 'OptionalElasticSearchCircuitBreaker)
    )(ExecutionContexts.defaultContext)

    bind[ElasticSearchClient].identifiedBy('MandatoryElasticSearchClient) to new ElasticSearchClient(
      inject[ElasticSetting],
      inject[ElasticClient](identified by 'MandatoryElasticClient),
      inject[MetricsReporter],
      inject[CircuitBreakerWrapper](identified by 'MandatoryElasticSearchCircuitBreaker)
    )(ExecutionContexts.defaultContext)

    bind[AggregationService] to new AggregationServiceImpl with BaseReportingModule {
      implicit val ec                                                   = ExecutionContexts.defaultContext
      override val hadoopLogger: CoreHadoopLogger                       = inject[CoreHadoopLogger]
      override val builder: QueryBuilder                                = inject[QueryBuilder]
      override val settings: ElasticSetting                             = inject[ElasticSetting]
      override val metricReporter: MetricsReporter                      = inject[MetricsReporter]
      override val elasticFallbackFilterUtil: ElasticFallbackFilterUtil = inject[ElasticFallbackFilterUtil]
      override val tracer: Tracer                                       = inject[Tracer]
      override val elasticSearchClientOptionalSearchType: ElasticSearchClient =
        inject[ElasticSearchClient](identified by 'OptionalElasticSearchClient)
      override val elasticSearchClientMandatorySearchType: ElasticSearchClient =
        inject[ElasticSearchClient](identified by 'MandatoryElasticSearchClient)
    }

    bind[HotelSortAggregationService] to new HotelSortAggregationServiceImpl with BaseReportingModule {
      implicit val ec                       = ExecutionContexts.defaultContext
      implicit val scheduler: Scheduler     = inject[ActorSystem].scheduler
      override val builder: QueryBuilder    = inject[QueryBuilder]
      override val settings: ElasticSetting = inject[ElasticSetting]
      override val tracer: Tracer           = inject[Tracer]
      override val elasticSearchClientMandatorySearchType: ElasticSearchClient =
        inject[ElasticSearchClient](identified by 'MandatoryElasticSearchClient)
    }

    bind[CityAugmentSetting] to CityAugmentSettings()
    bind[SegmentFilterSetting] to SegmentFilterSettings()

    bind[CalculateChannelService] to new CalculateChannelServiceDefault {
      implicit override val ec: ExecutionContext                          = defaultEC
      override val priceConfigRepositoryAgsql: PriceConfigRepositoryAgsql = inject[PriceConfigRepositoryAgsql]
    }

    bind[ElasticSetting] to ElasticSettings()
    bind[FilterMatrixSortSetting] to FilterMatrixSortSettings()

    // NPC client settings
    private val npcHttpClientSettings = NpcHttpClientSettings(config)

    private val npcHttpClientServiceMesh = ServiceMesh(
      ServiceMeshSettingsBuilder(npcHttpClientSettings.serviceName, config)
        .withRoundRobinStrategy(WeightedRoundRobinStrategy.getWeightedRoundRobinStrategy())
        .withRequestSettings(MeshRequestSettings(npcHttpClientSettings.maxAttempts))
        .build()
    )

    bind[NpcHttpClient[Future]] to {
      val httpClientSettings = HttpClientSettingsBuilder(npcHttpClientSettings.serviceName, config)
        .withOpenTelemetry(inject[OpenTelemetry])
        .withClientSettings(ClientSettings(npcHttpClientSettings.requestTimeout))
        .build()
      new NpcHttpClient(npcHttpClientServiceMesh, httpClientSettings)(
        inject[SttpBackend[Future, Any]]("sttpBackendNPC")
      )
    }

    bind[FireDrillServiceSettings] to FireDrillServiceSettings()

    bind[ASQService] to new ASQServiceImpl {}
    bind[HotelShutdownSetting] to HotelShutdownSetting()
    bind[HotelShutdownUtils] to injected[HotelShutdownUtilsImpl]

    bind[PropertyInfoListSettings] to PropertyInfoListSettings(config)

    bind[AbstractCache[PropertyInfoList]] to agCacheProvider.getL1L2CacheWithCustomCodec(
      "propertyinfolistcachereader",
      customLocalCacheExpiry = Some(FiniteDuration(120, TimeUnit.MINUTES)),
      customCodec = new Protobuf3WithCompressionPropertyInfoListCodec(new LZ4HCacheCompressor()),
      cacheConfig = new ScalacacheCacheconfig(cacheKeyBuilder = CacheKeyBuilder.default),
      overrideReadStrategy0 = Some(ReadWithTimeoutAndFallbackToReplica)
    )

    bind[PropertyInfoListTransformer] to new PropertyInfoListTransformer

    bind[CDBService] to new CDBServiceImpl(
      inject[CachePopulatorCityIdsFetcherRepositoryAgsql],
      inject[CachePopulatorPropertyInfoRepositoryAgsql],
      inject[PropertyInfoListTransformer]
    )

    bind[PropertyInfoListCacheReader] to injected[PropertyInfoListCacheReaderImpl]

    bind[PropertyMetaService] to new PropertyMetaServiceImpl(
      inject[ProductHotelRepositoryAgsql],
      inject[RegulatoryBlockingService]
    )

    bind[PropertyMetadataService] to injected[PropertyMetadataService]

    bind[PropertiesFetcherFlow] to new BaseServiceModuleCached with PropertiesFetcherFlow {
      override val augmentedSetting: CityAugmentSetting             = inject[CityAugmentSetting]
      override val localeService: LocaleService                     = inject[LocaleService]
      override val calculateChannelService: CalculateChannelService = inject[CalculateChannelService]
      override val npcApiClient                                     = inject[NpcClient]
      override val aggregationService                               = inject[AggregationService]
      implicit val languageRepository: LanguageRepository           = inject[LanguageRepository]
      override val cityInfoRepositoryAgsql: CityInfoRepositoryAgsql = inject[CityInfoRepositoryAgsql]
      override val landmarkDataRepository: LandmarkDataRepository   = inject[LandmarkDataRepository]

      override val areaInfoRepositoryAgsql: AreaInfoRepositoryAgsql   = inject[AreaInfoRepositoryAgsql]
      override val cityCenterInfoRepository: CityCenterInfoRepository = inject[CityCenterInfoRepository]
      override val regionInfoRepository: RegionInfoRepository         = inject[RegionInfoRepository]
      override val topAreaInfoRepository: TopAreaInfoRepository       = inject[TopAreaInfoRepository]
      override val whiteLabelService: WhiteLabelService               = inject[WhiteLabelService]
      override val sellabilityService: SellabilityService             = inject[SellabilityService]
      override val npcSetting: NpcHttpClientSettings                  = NpcHttpClientSettings(config)
      override val augmentSearchSetting: AugmentSearchSetting         = AugmentSearchSettings()
      override val fireDrillSettings: FireDrillServiceSettings        = inject[FireDrillServiceSettings]
      override val campaignRepositoryAgsql: CampaignRepositoryAgsql   = inject[CampaignRepositoryAgsql]
      override val stateInfoRepository: StateInfoRepository           = inject[StateInfoRepository]
      override val hotelShutdownSetting: HotelShutdownSetting         = inject[HotelShutdownSetting]
      override val hotelShutdownUtils: HotelShutdownUtils             = injected[HotelShutdownUtilsImpl]
      implicit override val cache: scalacache.Cache[AugmentedResult] =
        agCacheProvider.getL1L2Cache("AugmentedResultsCache", cacheConfig = CacheConfig.configForService)
      override val asqService: ASQService                           = inject[ASQService]
      override val tracer: Tracer                                   = inject[Tracer]
      override val propertyMetadataService: PropertyMetadataService = inject[PropertyMetadataService]
      override val bigCityRepositorySource: BigCityRepositorySource = inject[BigCityRepositorySource]

      override val enableAugmentedCityFeatureForBigCities: State[Boolean] =
        inject[State[KillSwitchConfig]]
          .map(_.enableAugmentedCityFeatureForBigCities)

      override val blockDSANonCompliantHotelRegisteredBeforeFeb2024: State[Boolean] =
        inject[State[KillSwitchConfig]]
          .map(_.blockDSANonCompliantHotelRegisteredBeforeFeb2024)

    }

    bind[NearestLandmarkSettings] to NearestLandmarkSettings()

    bind[NearestLandmarkService] to new NearestLandmarkServiceDefault {
      override val landmarkNameRepository: LandmarkNameRepository  = inject[LandmarkNameRepository]
      override val landmarkInfoRepository: LandmarkInfoRepository  = inject[LandmarkInfoRepository]
      override val settings: NearestLandmarkSettings               = inject[NearestLandmarkSettings]
      implicit override val cmsService: CMSService                 = inject[CMSService]
      implicit override val ec: ExecutionContext                   = defaultEC
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
    }

    bind[ElasticMatrixHelper] to new BaseServiceModuleCached with ElasticMatrixHelperImpl

    bind[ReviewLocationScoreCmsHelper] to new BaseServiceModuleCached with ReviewLocationScoreCmsHelperImpl {
      override val cmsService = inject[CMSService]
    }

    bind[FilterMatrixFromElasticSortingService] to new FilterMatrixFromElasticSortingServiceImpl {
      override val filterMatrixSortSetting: FilterMatrixSortSetting = inject[FilterMatrixSortSetting]
    }

    bind[CxlPolicyService] to new CxlPolicyService {}

    bind[CancellationPolicyService] to new BaseReportingModule with CancellationPolicyServiceDefault {
      implicit val ec                                     = defaultEC
      implicit val languageRepository: LanguageRepository = inject[LanguageRepository]
      implicit val coreCmsService: CMSService             = inject[CMSService]

      implicit val localeService: LocaleService = inject[LocaleService]
      implicit override val cancellationPolicyRepository: CancellationPolicyRepository =
        inject[CancellationPolicyRepository]
      implicit override val productHotelRepositoryAgsql: ProductHotelRepositoryAgsql =
        inject[ProductHotelRepositoryAgsql]
      implicit val cxlPolicyService: CxlPolicyService = inject[CxlPolicyService]
    }

    bind[SortMatrixService] to new SortMatrixServiceImpl {
      override val citySortMatrixSourceIO: CitySortMatrixSourceIO = inject[CitySortMatrixSourceIO]
      implicit override val ec: ExecutionContext                  = defaultEC
      override val cmsService                                     = inject[CMSService]
    }

    bind[BNPLService] to new BNPLServiceImpl {
      override val bnplHotelRepository: BNPLHotelRepository   = inject[BNPLHotelRepository]
      override val afmSettingRepository: AFMSettingRepository = inject[AFMSettingRepository]
      override val supplierBNPLEligibilityRepository: SupplierBNPLEligibilityRepository =
        inject[SupplierBNPLEligibilityRepository]
    }

    bind[PopularFilterService] to new BaseServiceModuleCached with PopularFilterServiceImpl {
      override val popularFilterAndOrderingRepository: PopularFilterAndOrderingRepository =
        inject[PopularFilterAndOrderingRepository]
      override val cmsService                           = inject[CMSService]
      override val localeService                        = inject[LocaleService]
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
    }

    lazy val zenithClientSettings = HttpClientSettingsBuilder("zenith", config)
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    bind[MonadError[Future, Throwable]] to catsStdInstancesForFuture(ExecutionContexts.defaultContext)
    lazy val zenithServiceMeshSettings = ServiceMeshSettingsBuilder("zenith", config).build()

    bind[ZenithHttpClient[Future]] to new ZenithHttpClient[Future](
      ServiceMesh(zenithServiceMeshSettings),
      zenithClientSettings
    )(inject[SttpBackend[Future, Any]]("sttpBackendZenith"), inject[MonadError[Future, Throwable]])

    bind[CircuitBreakerWrapper]
      .identifiedBy('FsCircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].fsCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[FeatureStoreService] to CacheFeatureStoreService(
      "papi-feature-Store",
      10000,
      inject[MetricsReporter],
      inject[Tracer],
      ExecutionContext.global,
      inject[CircuitBreakerWrapper](identified by 'FsCircuitBreaker)
    )

    bind[RecommendedFiltersService] to new RecommendedFiltersServiceImpl(
      inject[ZenithHttpClient[Future]],
      RequestSettings.default,
      inject[FeatureStoreService]
    )

    bind[SegmentFilterService] to new SegmentFilterServiceImpl {
      override val segmentFilterSetting: SegmentFilterSetting = inject[SegmentFilterSetting]
      override val cmsService                                 = inject[CMSService]
    }

    bind[FamilyFiltersService] to new FamilyFiltersServiceImpl {
      override val cmsService: CMSService = inject[CMSService]
    }

    bind[GroupTravellerFilterService] to new BaseServiceModuleCached with GroupTravellerFilterServiceImpl

    bind[FilterMatrixManipulatingService] to new BaseServiceModuleCached with FilterMatrixManipulatingServiceImpl {
      override val familyFiltersService        = inject[FamilyFiltersService]
      override val groupTravellerFilterService = inject[GroupTravellerFilterService]
    }

    bind[ReviewLocationScoreCmsHelper] to new ReviewLocationScoreCmsHelperImpl {
      override val cmsService: CMSService = inject[CMSService]
    }

    bind[ReviewScoreCmsHelper] to new ReviewScoreCmsHelperImpl {
      override val cmsService: CMSService = inject[CMSService]
    }

    import cats.instances.future.catsStdInstancesForFuture

    bind[JarvisSettings] to JarvisSettings()

    lazy val jarvisServiceMeshSettings = ServiceMeshSettingsBuilder(
      inject[JarvisSettings].service,
      config
    ).build()

    lazy val jarvisHttpClientSettings = HttpClientSettingsBuilder(inject[JarvisSettings].service, config)
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    (bind[RequestSettings] to RequestSettings(jarvisHttpClientSettings.clientSettings, Map.empty))
      .identifiedBy('jarvisRequestSettings)

    bind[PiiEncryptionSettings] to PiiEncryptionSettings()

    bind[Option[PiiEncryptionService]] to inject[PiiEncryptionSettings].key.map(PiiEncryptionService(_))

    bind[CircuitBreakerWrapper]
      .identifiedBy('JarvisCircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].jarvisCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[JarvisClient] to new JarvisClientDefault(
      ServiceMesh(jarvisServiceMeshSettings),
      jarvisHttpClientSettings,
      inject[CircuitBreakerWrapper](identified by 'JarvisCircuitBreaker)
    )(
      ExecutionContexts.defaultContext,
      inject[SttpBackend[Future, Any]]("sttpBackendJarvis"),
      catsStdInstancesForFuture(ExecutionContexts.defaultContext)
    )

    bind[JarvisService] to new BaseReportingModule with JarvisServiceDefault {
      implicit val ec                                              = ExecutionContexts.defaultContext
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
      override val jarvisClient: JarvisClient                      = inject[JarvisClient]
      override val requestSettings: RequestSettings                = inject[RequestSettings]("jarvisRequestSettings")
      override val tracer: Tracer                                  = inject[Tracer]
    }

    lazy val priceStreamHttpClientSettings =
      HttpClientSettingsBuilder(inject[PriceStreamClientSettings].service, config)
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

    lazy val priceStreamServiceMeshSettings = ServiceMeshSettingsBuilder(
      inject[PriceStreamClientSettings].service,
      config
    ).build()

    bind[PriceStreamHttpClient[Future]] to new PriceStreamHttpClient[Future](
      ServiceMesh(priceStreamServiceMeshSettings),
      priceStreamHttpClientSettings
    )(
      inject[SttpBackend[Future, Any]]("sttpBackendPricestream"),
      catsStdInstancesForFuture(ExecutionContexts.defaultContext)
    )

    bind[PriceStreamFetcherService] to new PriceStreamFetcherServiceImpl {
      override val client: PriceStreamHttpClient[Future] = inject[PriceStreamHttpClient[Future]]
      override val requestSettings: RequestSettings = inject[RequestSettings](identified by 'PriceStreamRequestSetting)
    }

    bind[RoomSizeHistogramService] to new RoomSizeHistogramServiceImpl {
      override val roomSizeRangeRepository: RoomSizeRangeRepositoryAgsql = inject[RoomSizeRangeRepositoryAgsql]
    }

    bind[FilteringService] to new BaseReportingModule with FilteringServiceImpl {
      override val exchangeDataService: ExchangeDataService             = inject[ExchangeDataService]
      override val aggregationService: AggregationService               = inject[AggregationService]
      override val cancellationPolicyService: CancellationPolicyService = inject[CancellationPolicyService]
      override val bnplService: BNPLService                             = inject[BNPLService]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
      implicit override val ec: ExecutionContext                        = defaultEC
      override val hotelRoomBedConfigRepository: HotelRoomBedConfigurationRepository =
        inject[HotelRoomBedConfigurationRepository]
      override val tracer: Tracer = inject[Tracer]
      override val heuristicAccuracyCheckerSettings: HeuristicAccuracyCheckerSettings =
        inject[HeuristicAccuracyCheckerSettings]
    }

    bind[MatrixNameService] to new BaseServiceModuleCached with MatrixNameServiceImpl with ReviewScoreCmsHelperImpl {
      override val areaNameRepository                   = inject[AreaNameRepository]
      override val cityNameRepositoryAgsql              = inject[CityNameRepositoryAgsql]
      override val hotelChainNameRepository             = inject[HotelChainNameRepository]
      override val landmarkNameRepository               = inject[LandmarkNameRepository]
      override val reviewLocationScore                  = inject[ReviewLocationScoreCmsHelper]
      override val elasticMatrixHelper                  = inject[ElasticMatrixHelper]
      override val cmsService                           = inject[CMSService]
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
      override val facilityClassNameRepository: FacilityClassNameRepositoryAgsql =
        inject[FacilityClassNameRepositoryAgsql]
      override val facilityNameRepositoryAgsql: FacilityNameRepositoryAgsql = inject[FacilityNameRepositoryAgsql]
      override val accommodationNameRepositoryAgsql: AccommodationNameRepositoryAgsql =
        inject[AccommodationNameRepositoryAgsql]
      override val filterMatrixSortSetting: FilterMatrixSortSetting = inject[FilterMatrixSortSetting]
    }

    bind[ElasticMatrixService] to new BaseReportingModule with ElasticMatrixServiceImpl {
      override val elasticService                             = inject[AggregationService]
      override val cmsService                                 = inject[CMSService]
      implicit override val languageRepository                = inject[LanguageRepository]
      override val environmentalConfig: EnvironmentalSettings = inject[EnvironmentalSettings]
      override val hotelChainOrderFromHadoopRepository        = inject[HotelChainOrderRepository]
      override val matrixNameService: MatrixNameService       = inject[MatrixNameService]
      override val areaPopularityByOriginRepository: AreaPopularityByOriginRepository =
        inject[AreaPopularityByOriginRepository]
      override val atmosphereOrderRepositoryAgsql: AtmosphereOrderRepositoryAgsql =
        inject[AtmosphereOrderRepositoryAgsql]
      override val exchangeDataService: ExchangeDataService                 = inject[ExchangeDataService]
      override val averagePriceRepositoryAgsql: AveragePriceRepositoryAgsql = inject[AveragePriceRepositoryAgsql]
      override val filterMatrixSortSetting: FilterMatrixSortSetting         = inject[FilterMatrixSortSetting]
      override val filterMatrixFromElasticSortingService: FilterMatrixFromElasticSortingService =
        inject[FilterMatrixFromElasticSortingService]
      override val elasticMatrixHelper: ElasticMatrixHelper       = inject[ElasticMatrixHelper]
      override val elasticDistanceService: ElasticDistanceService = inject[ElasticDistanceService]

      override val localeService: LocaleService = inject[LocaleService]

      override val areaTopAtmosphereRepositoryAgsql: AreaTopAtmosphereRepositoryAgsql =
        inject[AreaTopAtmosphereRepositoryAgsql]
      override val landmarkPrioritizedRepositoryAgsql: LandmarkPrioritizedRepositoryAgsql =
        inject[LandmarkPrioritizedRepositoryAgsql]
      override val tracer: Tracer = inject[Tracer]
    }

    bind[SecretDealSettings] to SecretDealSettings()

    bind[PostFilterService] to new PostFilterServiceImpl with BaseReportingModule {
      implicit override val ec: ExecutionContext = ExecutionContexts.defaultContext
      override val hotelRoomBedConfigRepository: HotelRoomBedConfigurationRepository =
        inject[HotelRoomBedConfigurationRepository]
      override val hadoopLogger: CoreHadoopLogger       = inject[CoreHadoopLogger]
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
      override val tracer: Tracer                       = inject[Tracer]
    }

    bind[PostFilterFlow] to new BaseReportingModule with PostFilterFlowImpl {
      override val postFilterService: PostFilterService = inject[PostFilterService]
      override val tracer: Tracer                       = inject[Tracer]
    }

    bind[EverestClient] to {
      implicit val ec: ExecutionContextExecutor = ExecutionContexts.defaultContext

      new EverestClientImpl(
        everestClientSettings = EverestClientSettings.apply
      )(ec)
    }

    bind[SLService] to {
      val config = SLServiceSettings.apply

      new BaseReportingModule with SLServiceImpl {
        override val settings = config

        override val everestClient                = inject[EverestClient]
        implicit val ec                           = ExecutionContexts.defaultContext
        override val localeService: LocaleService = inject[LocaleService]

        override val breaker = new CircuitBreaker(
          inject[ActorSystem].scheduler,
          config.failureCount,
          config.totalTimeout,
          config.disableDuration
        )
        override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
        override val hadoopLogger: CoreHadoopLogger       = inject[CoreHadoopLogger]
      }
    }

    bind[CircuitBreakerWrapper]
      .identifiedBy('RocketmilesCircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].rocketmilesRankingCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[RocketmilesRankingClient] to RocketmilesRankingClient(
      config,
      inject[OpenTelemetry],
      inject[CircuitBreakerWrapper](identified by 'RocketmilesCircuitBreaker)
    )(ExecutionContexts.defaultContext)

    bind[HotelDataPreparationFlow] to new BaseReportingModule with HotelDataPreparationFlow {
      implicit override val ec: ExecutionContext                          = ExecutionContexts.defaultContext
      override val jarvisService: JarvisService                           = inject[JarvisService]
      override val rocketmilesRankingClient: RocketmilesRankingClient     = inject[RocketmilesRankingClient]
      override val slService: SLService                                   = inject[SLService]
      override val environmentalSetting: EnvironmentalSettings            = environmentalConfigs
      override val elasticSearchService: HotelSortAggregationService      = inject[HotelSortAggregationService]
      override val tracer: Tracer                                         = inject[Tracer]
      override val whitelabelService: WhiteLabelService                   = inject[WhiteLabelService]
      override val rocketmilesRankingExpUtils: RocketmilesRankingExpUtils = inject[RocketmilesRankingExpUtils]
    }

    bind[CoreHadoopLogger] to new CoreHadoopLogger

    bind[SoldOutPropsPriceService] to new SoldOutPropsPriceServiceImpl {}

    bind[ChannelService] to new BaseReportingModule with ChannelServiceDefault with Ec {
      override val localeService: LocaleService                     = inject[LocaleService]
      override val calculateChannelService: CalculateChannelService = inject[CalculateChannelService]
    }

    bind[CoreSearchAgodaHomesService] to new CoreSearchAgodaHomesServiceImpl {
      implicit val ec: ExecutionContext = ExecutionContexts.defaultContext
      implicit override protected val agodaHomesRepositoryAgsql: AgodaHomesRepositoryAgsql =
        inject[AgodaHomesRepositoryAgsql]
    }

    bind[SoldOutPaging] to new BaseReportingModule with SoldOutPagingImpl {
      override val tracer: Tracer = inject[Tracer]
    }

    bind[ExtraHotelPaging] to new BaseReportingModule with ExtraHotelPagingDefault

    bind[AgodaHomesPaging] to new BaseReportingModule with AgodaHomesPagingDefault {
      override val agodaHomesService: CoreSearchAgodaHomesService = inject[CoreSearchAgodaHomesService]
    }

    bind[CoreSearchLuxuryHotelsService] to new CoreSearchLuxuryHotelsServiceImpl {
      override protected def aggregationService: AggregationService = inject[AggregationService]

      implicit override protected val ec: ExecutionContext = ExecutionContexts.defaultContext
    }

    bind[LuxuryHotelsPaging] to new BaseReportingModule with LuxuryHotelsPagingDefault {
      override val luxuryHotelsService: CoreSearchLuxuryHotelsService = inject[CoreSearchLuxuryHotelsService]
    }

    bind[CoreSearchPulsePropertiesService] to injected[CoreSearchPulsePropertiesServiceImpl]

    bind[PulsePropertiesPaging] to injected[PulsePropertiesPagingDefault]

    bind[PagingFlow] to new PagingFlowImpl {
      override val pageConfig: PageSearchSettings     = PageSearchSettings()
      override val pageTokenService: PageTokenService = inject[PageTokenService]
      override val heuristicAccuracyCheckerSettings: HeuristicAccuracyCheckerSettings =
        inject[HeuristicAccuracyCheckerSettings]
    }

    bind[ManualFlow] to new CBManualStatusCache with BaseReportingModule with ManualFlowDefault {
      override val agCache: AbstractCache[Option[Int]]              = agCacheProvider.getRemoteCache("ManualCalcCache")
      override val hadoopLogger: CoreHadoopLogger                   = inject[CoreHadoopLogger]
      override val manualSetting: ManualSetting                     = ManualSettings()
      override val supplierDataClient: SupplierCQLDataClient        = inject[SupplierCQLDataClient]
      override val supplierMetadataService: SupplierMetadataService = inject[SupplierMetadataService]
      implicit override val manualEc: ExecutionContext              = ExecutionContexts.defaultContext
      override val tracer: Tracer                                   = inject[Tracer]
    }

    bind[BundleService] to new BundleServiceImpl()

    bind[PackagingService] to new PackagingServiceImpl {
      override val bundleService                     = inject[BundleService]
      override val reportingService: MetricsReporter = inject[MetricsReporter]
    }

    bind[SearchFlow] to new BaseReportingModule with SearchFlowImpl with Ec {
      override val availabilityComposer: AvailabilityComposer               = inject[AvailabilityComposer]
      override val filterFlow: FilterFlow                                   = inject[FilterFlow]
      override val sortingFlow: SortingFlow                                 = inject[SortingFlow]
      override val soldOutPaging: SoldOutPaging                             = inject[SoldOutPaging]
      override val pagingFlow: PagingFlow                                   = inject[PagingFlow]
      override val manualFlow: ManualFlow                                   = inject[ManualFlow]
      override val agodaHomesPaging: AgodaHomesPaging                       = inject[AgodaHomesPaging]
      override val luxuryHotelsPaging: LuxuryHotelsPaging                   = inject[LuxuryHotelsPaging]
      override val extraHotelPaging: ExtraHotelPaging                       = inject[ExtraHotelPaging]
      override val histogramGenerator: HistogramGenerator                   = inject[HistogramGenerator]
      override val soldOutPropsPriceRepository: SoldOutPropsPriceRepository = inject[SoldOutPropsPriceRepository]
      override val soldOutPropsPriceService: SoldOutPropsPriceService       = inject[SoldOutPropsPriceService]
      override val sortMatrixService: SortMatrixService                     = inject[SortMatrixService]
      override val localeService: LocaleService                             = inject[LocaleService]
      override val channelService: ChannelService                           = inject[ChannelService]
      override val exchangeDataService: ExchangeDataService                 = inject[ExchangeDataService]
      override val slService: SLService                                     = inject[SLService]
      override val objectInfoService: ObjectInfoService                     = inject[ObjectInfoService]
      override val propertyFetcher: PropertiesFetcherFlow                   = inject[PropertiesFetcherFlow]
      override val capiService: CAPIService                                 = inject[CAPIService]
      override val packagingService: PackagingService                       = inject[PackagingService]
      override val whiteLabelService: WhiteLabelService                     = inject[WhiteLabelService]
      override val secretDealSettings: SecretDealSettings                   = inject[SecretDealSettings]
      override val hotelDataPreparationFlow: HotelDataPreparationFlow       = inject[HotelDataPreparationFlow]
      override val priceStreamFetcherService: PriceStreamFetcherService     = inject[PriceStreamFetcherService]
      override val roomSizeHistogramService: RoomSizeHistogramService       = inject[RoomSizeHistogramService]
      override val pulsePropertiesPaging: PulsePropertiesPaging             = inject[PulsePropertiesPaging]
      override val tracer: Tracer                                           = inject[Tracer]
      override val hotelShutdownSetting: HotelShutdownSetting               = inject[HotelShutdownSetting]

      override val skipCallingPullSupplierForUnknownBot: State[Boolean] =
        inject[State[KillSwitchConfig]]
          .map(_.skipCallingPullSupplierForUnknownBot)

      override val skipCallingPullSupplier: State[Boolean] =
        inject[State[KillSwitchConfig]]
          .map(_.skipCallingPullSupplier)

    }

    bind[SortingService] to new BaseReportingModule with SortingServiceImpl {
      implicit override val ec: ExecutionContext = ExecutionContexts.defaultContext
      override val tracer: Tracer                = inject[Tracer]
    }

    bind[SortingFlow] to new BaseReportingModule with SortingFlowImpl {
      implicit override val ec: ExecutionContext                               = ExecutionContexts.defaultContext
      implicit override val hotelDataPreparationFlow: HotelDataPreparationFlow = inject[HotelDataPreparationFlow]
      implicit override val sortingService: SortingService                     = inject[SortingService]
      override val tracer: Tracer                                              = inject[Tracer]
    }

    bind[PersonalizedPropertiesHelper] to new PersonalizedPropertiesHelperImpl {}

    bind[PropertySelectorForFilter] to new BaseServiceModuleCached() with PropertySelectorForFilterImpl {
      override val pageConfig                                         = inject[PageSearchSettings]
      override val jarvisService: JarvisService                       = inject[JarvisService]
      override val filteringService: FilteringService                 = inject[FilteringService]
      override val hotelDataPreparationFlow: HotelDataPreparationFlow = inject[HotelDataPreparationFlow]
      override val tracer: Tracer                                     = inject[Tracer]
    }

    bind[FilterFlow] to new BaseReportingModule with FilterFlowImpl {
      val filteringService: FilteringService                         = inject[FilteringService]
      val propertySelectorForFilter: PropertySelectorForFilter       = inject[PropertySelectorForFilter]
      val personalizedPropertiesHelper: PersonalizedPropertiesHelper = inject[PersonalizedPropertiesHelper]
      val environmentalSetting: EnvironmentalSettings                = inject[EnvironmentalSettings]
      val extraHotelPaging: ExtraHotelPaging                         = inject[ExtraHotelPaging]
      override val tracer: Tracer                                    = inject[Tracer]
    }

    bind[GroupTravellerFilterService] to new GroupTravellerFilterServiceImpl {}
    bind[NoCCMatrixOptionSetting] to NoCCMatrixOptionSettings()

    bind[FilterRankingService] to new FilterRankingServiceImp {
      override val filterRankingRepositoryAgsql: FilterRankingRepositoryAgsql = inject[FilterRankingRepositoryAgsql]
    }

    bind[CustomMatrixService] to new CustomMatrixService with BaseReportingModule {
      override val cancellationPolicyService: CancellationPolicyService = inject[CancellationPolicyService]
      override val bnplService: BNPLService                             = inject[BNPLService]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
      override val cmsService: CMSService                               = inject[CMSService]
      override val noCCMatrixOptionSetting: NoCCMatrixOptionSetting     = inject[NoCCMatrixOptionSetting]
      override val filterMatrixSortSetting: FilterMatrixSortSetting     = inject[FilterMatrixSortSetting]
      override val localeService: LocaleService                         = inject[LocaleService]
      implicit override val languageRepository: LanguageRepository      = inject[LanguageRepository]
      override val tracer: Tracer                                       = inject[Tracer]
    }

    bind[TealiumService] to new TealiumServiceImpl {
      override protected val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
    }

    bind[FilterMatrixManipulatingService] to new FilterMatrixManipulatingServiceImpl {
      override val familyFiltersService: FamilyFiltersService               = inject[FamilyFiltersService]
      override val groupTravellerFilterService: GroupTravellerFilterService = inject[GroupTravellerFilterService]

    }

    bind[ElasticDistanceService] to new BaseReportingModule with ElasticDistanceServiceImpl {
      override val elasticService: AggregationService             = inject[AggregationService]
      override val nearestLandmarkService: NearestLandmarkService = inject[NearestLandmarkService]
      override val tracer: Tracer                                 = inject[Tracer]
    }

    bind[MatrixFlow] to new BaseReportingModule with MatrixFlowImpl {
      override protected val filterMatrixManipulatingService: FilterMatrixManipulatingService =
        inject[FilterMatrixManipulatingService]
      override protected val propertySelectorForFilter: PropertySelectorForFilter = inject[PropertySelectorForFilter]
      override protected val filterRankingService: FilterRankingService           = inject[FilterRankingService]
      override protected val customMatrixService: CustomMatrixService             = inject[CustomMatrixService]
      override protected val filteringService: FilteringService                   = inject[FilteringService]
      override protected val environmentalSetting: EnvironmentalSettings          = inject[EnvironmentalSettings]
      override protected val elasticMatrixService: ElasticMatrixService           = inject[ElasticMatrixService]
      override protected val localeService: LocaleService                         = inject[LocaleService]
      override protected val segmentFilterService: SegmentFilterService           = inject[SegmentFilterService]
      override protected val popularFilterService: PopularFilterService           = inject[PopularFilterService]
      override protected val elasticDistanceService: ElasticDistanceService       = inject[ElasticDistanceService]
      override protected val recommendedFiltersService: RecommendedFiltersService = inject[RecommendedFiltersService]
      override val tracer: Tracer                                                 = inject[Tracer]
    }

    // Details
    bind[HistogramGenerator] to new HistogramGenerator {
      override protected val defaultMaxMinPriceRepository: DefaultMaxMinPriceRepository =
        inject[DefaultMaxMinPriceRepository]
    }

    // ObjectInfoService
    bind[CMSHelperService] to new CMSHelperServiceImpl {
      override val cmsService: CMSService = inject[CMSService]
    }

    bind[ObjectInfoService] to new ObjectInfoServiceImpl with BaseReportingModule {
      override val aggregationService: AggregationService             = inject[AggregationService]
      override val cmsHelperService: CMSHelperService                 = inject[CMSHelperService]
      override val mapDisplayInfoRepository: MapDisplayInfoRepository = inject[MapDisplayInfoRepository]
      override val localeService: LocaleService                       = inject[LocaleService]

      override val objectInfoRepositoryAgsql: ObjectInfoRepositoryAgsql       = inject[ObjectInfoRepositoryAgsql]
      override val cityAndCountryNameRepository: CityAndCountryNameRepository = inject[CityAndCountryNameRepository]
      override val tealiumService: TealiumService                             = inject[TealiumService]
    }

    bind[RequestSegmentMetricsGenerator] to injected[RequestSegmentMetricsGenerator]

    bind[HeuristicAccuracyReporter] to injected[HeuristicAccuracyReporter]
  }

  class SttpModule extends Module with PAPIAppLogger {
    val disableMaxConnectionsPerHost: Boolean = Try(config.getBoolean("disableMaxConnectionsPerHost")).getOrElse(false)
    val connectTimeout                        = Try(config.getInt("defaultConnectionTimeout")).getOrElse(100)
    val maxConnectionsPerHostToUse =
      if (disableMaxConnectionsPerHost) AsyncHttpClientConfigDefaults.defaultMaxConnectionsPerHost() else 40

    lazy val asyncHttpClientFutureBackEnd: AsyncHttpClient = new DefaultAsyncHttpClient(
      new DefaultAsyncHttpClientConfig.Builder()
        .setMaxConnections(1800)
        .setMaxConnectionsPerHost(maxConnectionsPerHostToUse)
        .setConnectTimeout(connectTimeout)
        .setReadTimeout(100)
        .setThreadPoolName("async-http-client")
        .setIoThreadsCount(1)
        .build()
    )

    private def logAsyncHttpClientStats(): Unit = {
      val clientStats = asyncHttpClientFutureBackEnd.getClientStats
      AdpMetricsReporter.report(
        AsyncHttpClientMeasurementKeys.totalConnection,
        clientStats.getTotalConnectionCount,
        Map.empty
      )
      AdpMetricsReporter.report(
        AsyncHttpClientMeasurementKeys.totalActiveConnection,
        clientStats.getTotalActiveConnectionCount,
        Map.empty
      )
      AdpMetricsReporter.report(
        AsyncHttpClientMeasurementKeys.totalIdleConnection,
        clientStats.getTotalIdleConnectionCount,
        Map.empty
      )
    }

    import AkkaSystem.actorSystem.dispatcher

    import scala.concurrent.duration._

    AkkaSystem.actorSystem.scheduler.schedule(1 minute, 1 minute) {
      logAsyncHttpClientStats()
    }

    bind[AsyncHttpClient] to asyncHttpClientFutureBackEnd

    lazy val sttpBackendDF: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)
    lazy val sttpBackendContent: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)
    lazy val sttpBackendPricestream: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)
    lazy val sttpBackendZenith: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)
    lazy val sttpBackendJarvis: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)
    lazy val sttpBackendPromo: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)
    lazy val sttpBackendNPC: SttpBackend[Future, Any] =
      AsyncHttpClientFutureBackend.usingClient(asyncHttpClientFutureBackEnd)(ExecutionContexts.defaultContext)

    (bind[SttpBackend[Future, Any]] to sttpBackendDF).identifiedBy('sttpBackedDF)
    (bind[SttpBackend[Future, Any]] to sttpBackendContent).identifiedBy('sttpBackedContent)
    (bind[SttpBackend[Future, Any]] to sttpBackendPricestream).identifiedBy('sttpBackendPricestream)
    (bind[SttpBackend[Future, Any]] to sttpBackendZenith).identifiedBy('sttpBackendZenith)
    (bind[SttpBackend[Future, Any]] to sttpBackendJarvis).identifiedBy('sttpBackendJarvis)
    (bind[SttpBackend[Future, Any]] to sttpBackendPromo).identifiedBy('sttpBackendPromo)
    (bind[SttpBackend[Future, Any]] to sttpBackendNPC).identifiedBy('sttpBackendNPC)
  }

}
