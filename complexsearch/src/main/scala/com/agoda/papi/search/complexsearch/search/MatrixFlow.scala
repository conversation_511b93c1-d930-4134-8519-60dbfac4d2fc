package com.agoda.papi.search.complexsearch.search

import com.agoda.core.search.models.request._
import com.agoda.core.search.models.response.{
  AggregationResponse,
  MatrixResult,
  MatrixResultElement,
  PostPagingAggregationResponse
}
import com.agoda.core.search.models.{ response, CMSMapping, Fields, PropertyData }
import com.agoda.papi.search.common.externaldependency.logger.message.PAPISearchLatencyMessage
import com.agoda.papi.search.common.service.LocaleService
import com.agoda.papi.search.common.util.concurrent.FZip
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.common.util.measurement.{ APIFlowMetric, MeasurementKey, PageMeasurementStep }
import com.agoda.papi.search.complexsearch.configuration.EnvironmentalSettings
import com.agoda.papi.search.complexsearch.constant.FeatureConstants
import com.agoda.papi.search.complexsearch.filter.FilteringService
import com.agoda.papi.search.complexsearch.filter.pre.PropertySelectorForFilter
import com.agoda.papi.search.complexsearch.matrix.MatrixTransformer.transformMatrix
import com.agoda.papi.search.complexsearch.matrix.derived.{
  PopularFilterService,
  PopularForFamilyMatrixService,
  RecommendedFiltersService,
  SegmentFilterService
}
import com.agoda.papi.search.complexsearch.matrix.sort.{ FilterMatrixManipulatingService, FilterRankingService }
import com.agoda.papi.search.complexsearch.matrix.{
  CustomMatrixService,
  ElasticDistanceService,
  ElasticMatrixService,
  MatrixTransformer
}
import com.agoda.papi.search.internalmodel.DegradeFeatures.FilterAggregation
import com.agoda.papi.search.internalmodel.JMapBuilder._
import com.agoda.papi.search.types.{ HotelIdType, PropertyDataCollection, WithHotelIdMap }

import scala.collection.Set
import scala.concurrent.{ ExecutionContext, Future }

trait MatrixFlow {

  def specializeMatrixForSearchType(request: BaseRequest[ComplexSearchRequest]): BaseRequest[ComplexSearchRequest]

  /** * Can call parallel with content and DF
    */
  def getAggregation(implicit
      request: AggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[AggregationResponse]

  /** * Can call after call content and DF
    */
  def getPostPagingAggregation(implicit
      postPagingAggregationRequest: PostPagingAggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[PostPagingAggregationResponse]

}

object MatrixFlowHelper {

  def mergeAggregationResponse(
      aggregationResponse: AggregationResponse,
      postPagingAggregation: PostPagingAggregationResponse
  ): AggregationResponse =
    aggregationResponse.copy(propertyLandmarkInfo = postPagingAggregation.propertyLandmarkInfo)

}

trait MatrixFlowImpl extends MatrixFlow with APIFlowMetric {

  protected val elasticMatrixService: ElasticMatrixService
  protected val customMatrixService: CustomMatrixService
  protected val filteringService: FilteringService
  protected val propertySelectorForFilter: PropertySelectorForFilter
  protected val environmentalSetting: EnvironmentalSettings
  protected val filterRankingService: FilterRankingService
  protected val filterMatrixManipulatingService: FilterMatrixManipulatingService
  protected val localeService: LocaleService
  protected val segmentFilterService: SegmentFilterService
  protected val popularFilterService: PopularFilterService
  protected val recommendedFiltersService: RecommendedFiltersService
  protected val elasticDistanceService: ElasticDistanceService

  def getPostPagingAggregation(implicit
      postPagingAggregationRequest: PostPagingAggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[PostPagingAggregationResponse] = {
    val languageId = localeService.localeToLanguageUse(postPagingAggregationRequest.searchContext.locale)
    for {
      lanmarkInfo <- elasticDistanceService.getHotelDistance(postPagingAggregationRequest, languageId)(contextHolder)
    } yield PostPagingAggregationResponse(
      lanmarkInfo
    )
  }

  def specializeMatrixForSearchType(request: BaseRequest[ComplexSearchRequest]): BaseRequest[ComplexSearchRequest] =
    request match {
      case _: RegionSearchRequest =>
        request.toOverrideSearchRequest(
          request.searchRequest.copy(matrix = transformMatrix(request.matrixRequest))
        )
      case areaSearch: AreaSearchRequest =>
        request
          .toOverrideSearchRequest(
            request.searchRequest.copy(
              matrix = transformMatrix(
                request.matrixRequest,
                remove = Set(Fields.CITYID, Fields.HOTELAREA, Fields.NEIGHBORHOOD)
              )
            )
          )
          .toAppendFilters(List(TermFilterDefinition(Fields.HOTELAREA, areaSearch.areaId)))
      case _ =>
        request.toOverrideSearchRequest(
          request.searchRequest.copy(matrix = transformMatrix(request.matrixRequest, remove = Set(Fields.CITYID)))
        )
    }

  /** Both elastic and custom filter matrices are calculated in parallel and combined result is returned
    */
  def getAggregation(implicit
      request: AggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[AggregationResponse] = {
    val enablePropertyFilterSyncId = contextHolder.experimentContext.isBVariant(
      contextHolder.allocationContext.enablePropertyFilterSyncId
    )
    if (contextHolder.isFeatureDegraded(FilterAggregation)) {
      calculateDefaultAggregation(enablePropertyFilterSyncId)
    } else {
      stepTiming(MeasurementKey.coreSearch, MeasurementKey.Aggregation, PageMeasurementStep.TotalStep) {
        calculateAggregation(enablePropertyFilterSyncId)
      }
    }
  }

  private def calculateDefaultAggregation(enablePropertyFilterSyncId: Boolean)(implicit
      request: AggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[AggregationResponse] =
    elasticMatrixService.defaultFilterMatrix(request).map { defaultElasticMatrix =>
      val defaultCustomMatrix = customMatrixService.buildDefaultCustomFilterMatrix
      val matrix              = Some(MatrixResult(defaultCustomMatrix.result ++ defaultElasticMatrix.result))
      AggregationResponse(
        None,
        matrix,
        Seq.empty,
        Seq.empty,
        Seq.empty,
        MatrixTransformer.transformToClayMatrixResult(
          matrix,
          Seq.empty,
          Seq.empty,
          None,
          enablePropertyFilterSyncId
        )
      )
    }

  private def calculateAggregation(enablePropertyFilterSyncId: Boolean)(implicit
      request: AggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[AggregationResponse] = {
    val languageId =
      localeService.localeToLanguageId(request.searchContext.locale)
    val andTermsResults: PropertyDataCollection = request.matrixHotelInput.exceptOrFilter.map { id =>
      request.allProperties(id)
    }(collection.breakOut)
    val matrixResultF = for {
      (elasticHotelIds, filteredHotelIds) <- findAggregationPropertyIds(
        request.allProperties,
        andTermsResults
      )
      ((elasticMatrixResult, propertyDistanceInfo), customMatrixResult) <- FZip(
        elasticMatrixService.getFilterMatrixMultiCitiesEnriched(
          request.copy(
            matrixHotelInput = request.matrixHotelInput.copy(exceptElasticIds = elasticHotelIds)
          )
        ),
        customMatrixService.buildCustomFilterMatrix(
          filteredHotelIds,
          andTermsResults
        )(request, ec, contextHolder)
      )

      _ = PAPISearchLatencyMessage.updateCountFieldsForHadoopMessage(
        contextHolder.searchLatencyMessage,
        PAPISearchLatencyMessage.AndTermsResultsHotelCount(andTermsResults.size),
        PAPISearchLatencyMessage.ElasticHotelCount(elasticHotelIds.size),
        PAPISearchLatencyMessage.FilteredHotelCount(filteredHotelIds.size)
      )

      matrixResult = combineMatrixResult(
        elasticMatrixResult,
        customMatrixResult
      )
      sortedMatrixResult <- filterRankingService.getSortedMatrixResult(
        matrixResult,
        request.searchCriteria.travellerType,
        request.baseMatrixRequest.requestedCityIds
      )
    } yield {
      filterMatrixManipulatingService.injectCustomSegmentFilter(
        sortedMatrixResult,
        localeService.localeToLanguageId(request.searchContext.locale)
      )
      val filteredMatrix = filterMatrixResult(
        sortedMatrixResult,
        request.baseMatrixRequest.hasInsiderDeal
      )
      val matrix = if (filteredMatrix.isEmpty && request.matrixRequest.isDefined) {
        request.matrixRequest
          .map(
            _.map(_.col -> Seq.empty).toJMap[String, Seq[MatrixResultElement]]()
          )
          .map(emp => response.MatrixResult(emp))
      } else {
        filteredMatrix
      }
      (propertyDistanceInfo, matrix)
    }
    for {
      (propertyLandmarkInfo, matrixResult) <- matrixResultF
      popularFilterResult <- popularFilterService.getPopularFilters(
        request.baseMatrixRequest.cityIdPopularFilter,
        request.matrixRequest,
        matrixResult,
        languageId
      )
      segmentFilterResult = segmentFilterService.getSegmentFilters(
        matrixResult,
        languageId
      )
      recommendedFilters <- recommendedFiltersService.addRecommendedFilters(
        request.baseMatrixRequest.cityIdPopularFilter,
        matrixResult
      )
      popularFilterWithName = popularFilterService.enrichPopularFilter(popularFilterResult)
      segmentFilterWithName = popularFilterService.enrichPopularFilter(segmentFilterResult)
      popularForFamilyMatrix = PopularForFamilyMatrixService.getPopularForFamilyMatrix(
        request.matrixRequest,
        matrixResult
      )
      matrixGroup = MatrixTransformer.transformToClayMatrixResult(
        matrixResult,
        popularFilterWithName,
        segmentFilterWithName,
        popularForFamilyMatrix,
        enablePropertyFilterSyncId
      )
    } yield {
      PAPISearchLatencyMessage.updateCountFieldsForHadoopMessage(
        contextHolder.searchLatencyMessage,
        PAPISearchLatencyMessage.MatrixResultCount(matrixResult.map(_.result.size).getOrElse(-1))
      )
      AggregationResponse(
        propertyLandmarkInfo,
        matrixResult,
        popularFilterWithName,
        Seq.empty,
        segmentFilterWithName,
        matrixGroup
      )
    }
  }

  private def combineMatrixResult(
      elasticMatrixResult: Option[MatrixResult],
      customMatrixResult: Option[MatrixResult]
  ): Option[MatrixResult] =
    (elasticMatrixResult, customMatrixResult) match {
      case (Some(elastic), Some(custom)) =>
        Some(MatrixResult(elastic.result ++ custom.result))
      case (Some(_), None) =>
        elasticMatrixResult
      case (None, Some(_)) =>
        customMatrixResult
      case (None, None) =>
        None
    }

  private def filterMatrixResult(matrixResult: Option[MatrixResult], hasInsiderDealFlg: Boolean)(implicit
      aggregationRequest: AggregationRequest,
      contextHolder: ContextHolder
  ): Option[MatrixResult] = {

    val matrixResultAfterInsider = if (!hasInsiderDealFlg) {
      matrixResult.map { m =>
        MatrixResult(m.result.map { m =>
          m._1 match {
            case Fields.DEALS =>
              m._1 -> m._2.filterNot(_.id == CMSMapping.InsiderDealMatrix)
            case _ => m
          }
        })
      }
    } else matrixResult

    removeDynamicMatrixResult(
      matrixResultAfterInsider.map(matrix => MatrixResult(matrix.result.filterNot(_._1 == Fields.TOPRATEDAREA)))
    )
  }

  private def removeDynamicMatrixResult(matrixResult: Option[MatrixResult])(implicit
      aggregationRequest: AggregationRequest,
      contextHolder: ContextHolder
  ): Option[MatrixResult] = {
    val matrixRequest = aggregationRequest.matrixRequest
    if (matrixRequest.isDefined) {
      val matrixCols = matrixRequest.get.map(_.col)
      matrixResult match {
        case None =>
          None
        case Some(mResult) =>
          Some(
            MatrixResult(
              mResult.result
                .clone()
                .retain((k, _) =>
                  matrixCols.contains(k) || Fields.nonRequestMatrixFields
                    .contains(k)
                )
            )
          )
      }
    } else None
  }

  private def findAggregationPropertyIds(
      availability: WithHotelIdMap[PropertyData],
      andTermProperties: PropertyDataCollection
  )(implicit
      request: AggregationRequest,
      contextHolder: ContextHolder,
      ec: ExecutionContext
  ): Future[(Set[HotelIdType], PropertyDataCollection)] = {
    val isBestSellerFilterApplied = if (request.searchCriteria.isAPORequest) {
      false
    } else {
      request.elasticFilters.exists {
        case r: RangeFilterDefinition =>
          r.col.equalsIgnoreCase(FeatureConstants.overAllBooking)
        case _ => false
      }
    }

    if (isBestSellerFilterApplied) {
      val BEST_SELLER_PROPERTY_WITH_BUFFER = 100
      val bestsellerHotelId = request.matrixHotelInput.exceptElasticIds.take(BEST_SELLER_PROPERTY_WITH_BUFFER)
      Future.successful(
        bestsellerHotelId -> bestsellerHotelId.map(id => availability(id))(collection.breakOut)
      )
    } else {
      for {
        filteredHotelIds <- filteringService.prePageCustomOrTermsFilter(andTermProperties)
      } yield (request.matrixHotelInput.exceptElasticIds, filteredHotelIds)
    }
  }

}
