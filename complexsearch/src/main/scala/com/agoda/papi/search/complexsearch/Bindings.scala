package com.agoda.papi.search.complexsearch

import akka.actor.ActorSystem
import cats.instances.future._
import com.agoda.common.graphql.model.Wrapper
import com.agoda.common.graphql.query.CachedQueryParser
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.http.client.v2.settings.HttpClientSettingsBuilder
import com.agoda.commons.http.client.v2.{ ClientSettings, HttpClient, RequestSettings }
import com.agoda.commons.http.mesh.v2.rr.WeightedRoundRobinStrategy
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.commons.http.mesh.v2.{ RequestSettings => MeshRequestSettings, ServiceMesh }
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.sql.hikari.load.balancing.{ DataSourcePoolLoadBalancer, HikariDataSourceResource }
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.information.{ InformationResponse, LocalInformation }
import com.agoda.content.models.propertycontent.{
  ContentImagesResponse,
  ContentInformationSummary,
  ContentReviewScoreResponse,
  ContentReviewSummariesResponse
}
import com.agoda.papi.search.complexsearch.binding.Bindings.{ config, BaseReportingModule }
import com.agoda.gandalf.client.PromoAgHttpClientSettings
import com.agoda.npc.client.NpcHttpClient
import com.agoda.papi.content.graphql._
import com.agoda.papi.search.common.externaldependency.chunkable.client.{
  ContentChunkableClient,
  DFChannelEnrichService,
  DFChunkableClient,
  DFChunkableService,
  HttpContentChunkableClient,
  HttpDFChunkableClient
}
import com.agoda.papi.search.common.externaldependency.chunkable.{ ChunkableService, ChunkableServiceFuture }
import com.agoda.papi.search.common.externaldependency.cms.{ GQLCMSService, GQLStatefulCmsServiceDefault }
import com.agoda.papi.search.common.externaldependency.content.{
  EngagementService,
  EngagementServiceImpl,
  ExperiencesService,
  ExperiencesServiceImpl,
  FeaturesService,
  FeaturesServiceImpl,
  HighlightsService,
  HighlightsServiceImpl,
  ImageService,
  ImageServiceImpl,
  InformationService,
  InformationServiceImpl,
  InformationSummaryService,
  InformationSummaryServiceImpl,
  LocalInformationService,
  LocalInformationServiceImpl,
  QnaService,
  QnaServiceImpl,
  RateCategoryService,
  RateCategoryServiceImpl,
  ReviewScoreService,
  ReviewScoreServiceImpl,
  ReviewSummariesService,
  ReviewSummariesServiceImpl,
  TopicsService,
  TopicsServiceImpl
}
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.externaldependency.experiment.{
  SearchExperimentManagerService,
  SearchExperimentManagerServiceImpl
}
import com.agoda.papi.search.common.externaldependency.promocode.{
  GandalfService,
  GandalfServiceImpl,
  PromoAgHttpClient
}
import com.agoda.papi.search.common.externaldependency.regulatoryblocking.RegulatoryBlockingSetting
import com.agoda.papi.search.common.externaldependency.repository.ExchangeDataRepository
import com.agoda.papi.search.common.externaldependency.repository.{
  BookingHistoryByPropertyRepositoryAgsql,
  LanguageRepository,
  ProductHotelRepositoryAgsql,
  SoldOutPropsPriceRepository
}
import com.agoda.papi.search.common.handler.datafetcher.pricestream.{
  PriceStreamFetcherService,
  PriceStreamFetcherServiceImpl
}
import com.agoda.papi.search.common.model.{ Components, KillSwitchConfig }
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.service.{
  CalculateChannelService,
  ExchangeDataService,
  LocaleService,
  PropertyMetaService,
  PropertyMetaServiceImpl,
  RegulatoryBlockingService,
  RegulatoryBlockingServiceImpl,
  RocketmilesRankingExperimentService,
  WhiteLabelService
}
import com.agoda.papi.search.common.util.circuitbreaker.{
  CircuitBreakerWrapper,
  CircuitBreakerWrapperBuilder,
  ExternalServiceCircuitBreakerSettings
}
import com.agoda.papi.search.common.util.configuration.{ ChunkableWrapperSettings, OverrideExperimentSettings }
import com.agoda.papi.search.common.util.context.{
  DefaultGlobalContextService,
  ExecutionContexts,
  GlobalContextService
}
import com.agoda.papi.search.common.util.logger.HadoopLogger
import com.agoda.papi.search.common.util.measurement.{ MeasurementName, MeasurementTag }
import com.agoda.papi.search.complexsearch.common.service.client._
import com.agoda.papi.search.complexsearch.common.service.LanguageService
import com.agoda.papi.search.complexsearch.configuration.{ PageSearchSettings, ZenithSettings }
import com.agoda.papi.search.complexsearch.externaldependency.npc.NpcClient
import com.agoda.papi.search.complexsearch.externaldependency.repository.{
  BenefitRepositoryAgsql,
  NumberOfReturnCustomerRepository
}
import com.agoda.papi.search.complexsearch.search.{ MatrixFlow, PostFilterFlow, SearchFlow }
import com.agoda.papi.search.complexsearch.service.agodahome._
import com.agoda.papi.search.complexsearch.service.enrichment.{ PopularEnrichService, PopularEnrichServiceImpl }
import com.agoda.papi.search.complexsearch.service.packaging.{
  PackageDealsSortingService,
  PackageDealsSortingServiceImpl
}
import com.agoda.papi.search.complexsearch.service.paging.PageTokenService
import com.agoda.papi.search.complexsearch.service.pricingbadge.{ PricingBadgesService, PricingBadgesServiceImpl }
import com.agoda.papi.search.complexsearch.service.rocketmiles.RocketmilesRankingService
import com.agoda.papi.search.complexsearch.service.searchenrichment._
import com.agoda.papi.search.complexsearch.service._
import com.agoda.papi.search.complexsearch.service.citi.{ CitiRankingService, CitiRankingServiceImpl }
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.RocketmilesRankingClient
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.util.RocketmilesRankingExpUtils
import com.agoda.papi.search.complexsearch.util.GraphQLRequestForwardingHelper
import com.agoda.papi.search.internalmodel.content.PAPIContentSummary
import com.agoda.papi.search.internalmodel.pricing.PAPIHotelPricing
import com.agoda.papi.search.internalmodel.request.{ MetaLabPropertiesRequest, PricingPropertiesRequestWrapper }
import com.agoda.pricestream.client.{ PriceStreamClientSettings, PriceStreamHttpClient }
import com.agoda.pricestream.models.response.PropertyAttributesResponse
import com.agoda.regulatoryblocking.client.RegulatoryBlockingClient
import com.agoda.regulatoryblocking.client.config.RegulatoryBlockingInitConfig
import com.agoda.supplier.common.services.{ SupplierMetadataService, SupplierMetadataServiceImpl }
import com.agoda.universal.cms.client.context.{ SQLContext => CmsSqlContext }
import com.agoda.universal.cms.client.service.{ UniversalCmsService, UniversalCmsServiceDefault }
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import com.typesafe.config.ConfigFactory
import com.typesafe.scalalogging.LazyLogging
import io.opentelemetry.api.OpenTelemetry
import io.opentracing.Tracer
import scaldi.Module
import sttp.client3.SttpBackend

import java.sql.Connection
import scala.concurrent.{ ExecutionContext, Future }

/** <AUTHOR>   mkhan
  */
object Bindings extends LazyLogging {

  implicit val defaultEC: ExecutionContext = ExecutionContexts.defaultContext
  private val configs                      = ConfigFactory.load()

  class BaseModule extends Module {
    bind[HadoopLogger] to new HadoopLogger()
  }

  class CoreModule extends Module {

    // scalastyle:off
    val pricingChunkableWrapperSettings = ChunkableWrapperSettings.apply(configs, "pricing-chunkable-wrapper-settings")
    val contentChunkableWrapperSettings = ChunkableWrapperSettings.apply(configs, "content-chunkable-wrapper-settings")
    val metaLabChunkableWrapperSettings = ChunkableWrapperSettings.apply(configs, "metalab-chunkable-wrapper-settings")

    val cmsService = new BaseReportingModule with GQLStatefulCmsServiceDefault {
      private val AppName = "PAPI"

      private val cmsSqlContext: CmsSqlContext = new BaseReportingModule with CmsSqlContext {

        override def SQL[T](metricTag: String)(block: Connection => T): T =
          inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](connection =>
            timerWithoutSampling(
              MeasurementName.SqlContext,
              Map(
                "step"                    -> metricTag,
                MeasurementTag.ClientName -> "cms-client"
              )
            )(block(connection))
          )

      }

      override val cmsService: UniversalCmsService = new UniversalCmsServiceDefault(cmsSqlContext, AppName)
    }

    // scalastyle:on
    bind[GQLCMSService] to cmsService

    lazy val serviceMeshes = inject[Map[String, ServiceMesh]]("consulServiceMeshes")

    // Dynamic Schema configuration
    bind[SearchExperimentManagerService] to injected[SearchExperimentManagerServiceImpl]
    bind[GlobalContextService] to new DefaultGlobalContextService(inject[WhiteLabelClientMultiEnv])

    lazy val contentHttpClientSettings = HttpClientSettingsBuilder("content", configs)
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    bind[ContentChunkableClient] to new HttpContentChunkableClient(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/properties/summary")
      )
    )

    lazy val dfClientSettings = HttpClientSettingsBuilder("dragonfruit", configs)
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    bind[DFChunkableClient] to new HttpDFChunkableClient(
      serviceMeshes("dragonfruit"),
      new HttpClient(dfClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedDF")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].dragonfruitCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "Api/v2/properties")
      )
    )

    bind[ChunkableServiceFuture[ContentPropertiesSummaryRequestEnvelope, List[Wrapper[PAPIContentSummary]]]] to
      new ChunkableServiceFuture[ContentPropertiesSummaryRequestEnvelope, List[Wrapper[PAPIContentSummary]]](
        inject[ContentChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[DFChannelEnrichService] to new DFChannelEnrichService(
      inject[CalculateChannelService]
    )

    bind[ChunkableServiceFuture[PricingPropertiesRequestWrapper, List[Wrapper[PAPIHotelPricing]]]] to
      new DFChunkableService(
        inject[DFChunkableClient],
        pricingChunkableWrapperSettings,
        inject[DFChannelEnrichService],
        inject[CAPIService],
        inject[PropertyMetaService],
        inject[Tracer],
        Components.Dragonfruit
      )

    bind[PriceStreamClientSettings] to PriceStreamClientSettings()

    lazy val priceStreamHttpClientSettings =
      HttpClientSettingsBuilder(inject[PriceStreamClientSettings].service, configs)
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

    lazy val priceStreamServiceMeshSettings = ServiceMeshSettingsBuilder(
      inject[PriceStreamClientSettings].service,
      configs
    ).build()

    bind[PriceStreamHttpClient[Future]] to new PriceStreamHttpClient[Future](
      ServiceMesh(priceStreamServiceMeshSettings),
      priceStreamHttpClientSettings
    )(
      inject[SttpBackend[Future, Any]]("sttpBackendPricestream"),
      catsStdInstancesForFuture
    )

    bind[MetaLabChunkableClient] to new HttpMetaLabChunkableClientImpl(
      ServiceMesh(priceStreamServiceMeshSettings),
      new HttpClient(priceStreamHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackendPricestream")),
      inject[Tracer]
    )

    bind[ChunkableService[Future, MetaLabPropertiesRequest, PropertyAttributesResponse]] to
      new ChunkableService[Future, MetaLabPropertiesRequest, PropertyAttributesResponse](
        inject[MetaLabChunkableClient],
        metaLabChunkableWrapperSettings,
        inject[Tracer],
        Components.PriceStream
      )

    private val zenithSettings = ZenithSettings()
    bind[ZenithSettings] to zenithSettings

    lazy val zenithClientSettings = HttpClientSettingsBuilder(zenithSettings.service, configs)
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    lazy val zenithServiceMeshSettings = ServiceMeshSettingsBuilder(zenithSettings.service, configs).build()

    private val promoAgHttpClientSettings = PromoAgHttpClientSettings(configs)

    lazy val promoHttpClientSettings = HttpClientSettingsBuilder(promoAgHttpClientSettings.serviceName, configs)
      .withOpenTelemetry(inject[OpenTelemetry])
      .withClientSettings(ClientSettings(promoAgHttpClientSettings.requestTimeout))
      .build()

    lazy val promoServiceMeshSettings = ServiceMeshSettingsBuilder(promoAgHttpClientSettings.serviceName, config)
      .withRoundRobinStrategy(WeightedRoundRobinStrategy.getWeightedRoundRobinStrategy())
      .withRequestSettings(MeshRequestSettings(promoAgHttpClientSettings.maxAttempts))
      .build()

    bind[PromoAgHttpClient[Future]] to new PromoAgHttpClient(
      ServiceMesh(promoServiceMeshSettings),
      promoHttpClientSettings
    )(inject[SttpBackend[Future, Any]]("sttpBackendPromo"))

    bind[OverrideExperimentSettings] to OverrideExperimentSettings.apply()

    bind[PropertySearchServiceLogicHelper] to injected[PropertySearchServiceLogicHelper]

    binding.identifiedBy('PropertySearchService) to new PropertySearchServiceImpl(
      inject[ChunkableService[Future, ContentPropertiesSummaryRequestEnvelope, List[Wrapper[PAPIContentSummary]]]],
      inject[ChunkableService[Future, PricingPropertiesRequestWrapper, List[Wrapper[PAPIHotelPricing]]]],
      inject[ChunkableService[Future, MetaLabPropertiesRequest, PropertyAttributesResponse]],
      inject[Tracer],
      inject[OverrideExperimentSettings],
      inject[MetricsReporter],
      inject[PropertySearchServiceLogicHelper]
    )

    bind[SoldOutPropertyService] to injected[SoldOutPropertyServiceImpl](
      'propertyService -> inject[PropertySearchService](identified by 'PropertySearchService)
    )

    bind[RocketmilesRankingService] to new RocketmilesRankingService {
      override val rocketmilesRankingClient: RocketmilesRankingClient = inject[RocketmilesRankingClient]
      override val ec: ExecutionContext                               = ExecutionContexts.defaultContext
      override val whitelabelService: WhiteLabelService               = inject[WhiteLabelService]
    }

    bind[RocketmilesRankingExpUtils] to injected[RocketmilesRankingExpUtils]

    bind[PackageDealsSortingService] to new PackageDealsSortingServiceImpl {}

    bind[SearchService] to new SearchServiceImpl(
      inject[SearchFlow],
      inject[PostFilterFlow],
      inject[MatrixFlow],
      inject[PropertyMetaService]
    )

    bind[CitiRankingService] to new CitiRankingServiceImpl

    binding.identifiedBy('PagedPropertyService) to new PagingServiceImpl
      with SortingServiceImpl with BaseReportingModule {
      override val propertySearchService: PropertySearchService =
        inject[PropertySearchService](identified by 'PropertySearchService)
      override val priceStreamHttpClient: PriceStreamHttpClient[Future] = inject[PriceStreamHttpClient[Future]]
      override val pageConfig: PageSearchSettings                       = inject[PageSearchSettings]
      override val searchService: SearchService                         = inject[SearchService]
      override val rocketmilesRankingService: RocketmilesRankingService = inject[RocketmilesRankingService]
      override val priceStreamRequestSettings: RequestSettings =
        inject[RequestSettings](identified by 'PriceStreamRequestSetting)
      override val packageDealsSortingService: PackageDealsSortingService = inject[PackageDealsSortingService]
      override val pageTokenService: PageTokenService                     = inject[PageTokenService]
      override val metricsReporter: MetricsReporter                       = reportingService
      override val tracer: Tracer                                         = inject[Tracer]
      override val whitelabelService: WhiteLabelService                   = inject[WhiteLabelService]
      override val rocketmilesRankingExperimentService: RocketmilesRankingExperimentService =
        inject[RocketmilesRankingExperimentService]
      override val rocketmilesRankingExpUtils: RocketmilesRankingExpUtils = inject[RocketmilesRankingExpUtils]
      override val citiRankingService                                     = inject[CitiRankingService]
    }

    binding.identifiedBy('DatelessPropertySearchService) to new DatelessPropertySearchServiceImpl(
      inject[GlobalContextService],
      inject[ChunkableServiceFuture[ContentPropertiesSummaryRequestEnvelope, List[Wrapper[PAPIContentSummary]]]],
      inject[SoldOutPropsPriceRepository],
      inject[ExchangeDataService],
      inject[Tracer]
    )

    binding.identifiedBy('PagedDatelessPropertyService) to new PagedDatelessPropertyServiceImpl(
      inject[DatelessPropertySearchService](identified by 'DatelessPropertySearchService),
      inject[PriceStreamHttpClient[Future]],
      inject[PageSearchSettings],
      inject[SearchService],
      inject[RequestSettings](identified by 'PriceStreamRequestSetting),
      inject[RocketmilesRankingExperimentService]
    ) with SortingServiceImpl with BaseReportingModule {
      override val rocketmilesRankingService: RocketmilesRankingService   = inject[RocketmilesRankingService]
      override val packageDealsSortingService: PackageDealsSortingService = inject[PackageDealsSortingService]
      override val pageTokenService: PageTokenService                     = inject[PageTokenService]
      override val metricsReporter: MetricsReporter                       = reportingService
      override val tracer: Tracer                                         = inject[Tracer]
      override val whitelabelService: WhiteLabelService                   = inject[WhiteLabelService]
      override val rocketmilesRankingExpUtils: RocketmilesRankingExpUtils = inject[RocketmilesRankingExpUtils]
      override val citiRankingService                                     = inject[CitiRankingService]
    }

    // scalastyle:off
    val supplierCommonSqlContext = new com.agoda.supplier.common.core.SQLContext {

      override def SQL[T](block: (Connection) => T) =
        inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](connection => block(connection))

    }

    bind[ContentImagesChunkableClient] to new ContentImagesChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/images")
      )
    )

    bind[ChunkableServiceFuture[ContentGroupImagesRequestEnvelope, List[Wrapper[ContentImagesResponse]]]] to
      new ChunkableServiceFuture[ContentGroupImagesRequestEnvelope, List[Wrapper[ContentImagesResponse]]](
        inject[ContentImagesChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ContentReviewScoreChunkableClient] to new ContentReviewScoreChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/reviews/score")
      )
    )

    bind[ChunkableServiceFuture[ContentGroupReviewScoreRequestEnvelope, List[Wrapper[ContentReviewScoreResponse]]]] to
      new ChunkableServiceFuture[ContentGroupReviewScoreRequestEnvelope, List[Wrapper[ContentReviewScoreResponse]]](
        inject[ContentReviewScoreChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ContentReviewSummariesChunkableClient] to new ContentReviewSummariesChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/reviews/summaries")
      )
    )

    bind[ContentInformationSummaryChunkableClient] to new ContentInformationSummaryChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/summary")
      )
    )

    bind[ContentLocalInformationChunkableClient] to new ContentLocalInformationChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/localInformation")
      )
    )

    bind[ContentExperienceChunkableClient] to new ContentExperienceChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/experiences")
      )
    )

    bind[ContentFeaturesChunkableClient] to new ContentFeaturesChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/features")
      )
    )

    bind[ContentHighlightsChunkableClient] to new ContentHighlightsChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/highlights")
      )
    )

    bind[ContentQnaChunkableClient] to new ContentQnaChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/qna")
      )
    )

    bind[ContentTopicsChunkableClient] to new ContentTopicsChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/topics")
      )
    )

    bind[ContentRateCategoryChunkableClient] to new ContentRateCategoryChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/rateCategory")
      )
    )

    bind[ContentRoomSummaryChunkableClient] to new ContentRoomSummaryChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/roomSummary")
      )
    )

    bind[ChunkableServiceFuture[ContentGroupRoomSummaryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupRoomSummaryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentRoomSummaryChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[
      ChunkableServiceFuture[ContentGroupReviewSummariesRequestEnvelope, List[Wrapper[ContentReviewSummariesResponse]]]
    ] to
      new ChunkableServiceFuture[ContentGroupReviewSummariesRequestEnvelope, List[
        Wrapper[ContentReviewSummariesResponse]
      ]](
        inject[ContentReviewSummariesChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[
      ChunkableServiceFuture[ContentInformationSummaryGroupRequestEnvelope, List[Wrapper[ContentInformationSummary]]]
    ] to
      new ChunkableServiceFuture[ContentInformationSummaryGroupRequestEnvelope, List[
        Wrapper[ContentInformationSummary]
      ]](
        inject[ContentInformationSummaryChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ContentInformationChunkableClient] to new ContentInformationChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/information")
      )
    )

    bind[ChunkableServiceFuture[ContentInformationGroupRequestEnvelope, List[Wrapper[InformationResponse]]]] to
      new ChunkableServiceFuture[ContentInformationGroupRequestEnvelope, List[Wrapper[InformationResponse]]](
        inject[ContentInformationChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupLocalInformationRequestEnvelope, List[Wrapper[LocalInformation]]]] to
      new ChunkableServiceFuture[ContentGroupLocalInformationRequestEnvelope, List[Wrapper[LocalInformation]]](
        inject[ContentLocalInformationChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ContentEngagementChunkableClient] to new ContentEngagementChunkableClientImpl(
      serviceMeshes("content"),
      new HttpClient(contentHttpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent")),
      inject[Tracer],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/v2/property/engagement")
      )
    )

    bind[ChunkableServiceFuture[ContentGroupEngagementRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupEngagementRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentEngagementChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupExperienceRequestEnvelope, List[Wrapper[Experiences]]]] to
      new ChunkableServiceFuture[ContentGroupExperienceRequestEnvelope, List[Wrapper[Experiences]]](
        inject[ContentExperienceChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupFeaturesRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupFeaturesRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentFeaturesChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupHighlightsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupHighlightsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentHighlightsChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupQnaRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupQnaRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentQnaChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupTopicsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupTopicsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentTopicsChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    bind[ChunkableServiceFuture[ContentGroupRateCategoryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]] to
      new ChunkableServiceFuture[ContentGroupRateCategoryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]](
        inject[ContentRateCategoryChunkableClient],
        contentChunkableWrapperSettings,
        inject[Tracer],
        Components.Content
      )

    (bind[RequestSettings] to RequestSettings(priceStreamHttpClientSettings.clientSettings, Map.empty))
      .identifiedBy('PriceStreamRequestSetting)

    bind[PriceStreamFetcherService] to new PriceStreamFetcherServiceImpl {
      override val client: PriceStreamHttpClient[Future] = inject[PriceStreamHttpClient[Future]]
      override val requestSettings: RequestSettings = inject[RequestSettings](identified by 'PriceStreamRequestSetting)
    }

    bind[PricingBadgesService] to PricingBadgesServiceImpl
    bind[APOService] to new APOServiceImpl()

    bind[RegulatoryBlockingClient] to {
      RegulatoryBlockingClient(RegulatoryBlockingSetting(config), inject[OpenTelemetry])(
        ExecutionContexts.defaultContext
      )
    }

    bind[RegulatoryBlockingService] to injected[RegulatoryBlockingServiceImpl]

    bind[PropertyMetaService] to new PropertyMetaServiceImpl(
      inject[ProductHotelRepositoryAgsql],
      inject[RegulatoryBlockingService]
    )

    bind[ExtraHotelServices] to new ExtraHotelServicesImpl(
      inject[PropertySearchService](identified by 'PropertySearchService),
      inject[SoldOutPropsPriceRepository],
      inject[ExchangeDataRepository],
      inject[MetricsReporter],
      inject[Tracer]
    )

    bind[FeaturedAgodaHomesService] to new FeaturedAgodaHomesServiceImpl(
      inject[PropertySearchService](identified by 'PropertySearchService),
      inject[SearchService],
      inject[MetricsReporter],
      inject[Tracer]
    )

    bind[LuxuryHotelsService] to new LuxuryHotelsServiceImpl(
      inject[PropertySearchService](identified by 'PropertySearchService),
      inject[SearchService],
      inject[MetricsReporter],
      inject[Tracer]
    )

    bind[TopSellingPointService] to new TopSellingPointServiceImpl(inject[GlobalContextService])
    bind[UniqueSellingPointService] to injected[UniqueSellingPointServiceImpl]
    bind[BookingHistoryService] to new BookingHistoryServiceImpl(inject[BookingHistoryByPropertyRepositoryAgsql])

    bind[ReviewSnippetService] to new ReviewSnippetServiceImpl {
      override val localeService: LocaleService = inject[LocaleService]
    }

    bind[DistanceEnrichService] to new DistanceEnrichServiceImpl

    bind[EnrichmentService] to new EnrichmentServiceImpl(
      inject[TopSellingPointService],
      inject[UniqueSellingPointService],
      inject[BookingHistoryService],
      inject[PricingBadgesService],
      inject[ReviewSnippetService],
      inject[DistanceEnrichService]
    )

    bind[HighlyRatedAgodaHomesService] to injected[HighlyRatedAgodaHomesServiceImpl](
      'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
    )

    bind[ExtraAgodaHomesService] to injected[ExtraAgodaHomesServiceImpl](
      'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
    )

    bind[PulsePropertiesService] to injected[PulsePropertiesServiceImpl](
      'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
    )

    bind[PropertyMergerService] to injected[PropertyMergerServiceImpl]

    bind[CircuitBreakerWrapper]
      .identifiedBy('NpcCircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].npcCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[NpcClient] to new NpcClient(
      inject[NpcHttpClient[Future]],
      inject[CircuitBreakerWrapper](identified by 'NpcCircuitBreaker)
    )

    bind[GeoService] to new GeoServiceImpl(
      inject[NpcClient],
      new LanguageService(inject[LanguageRepository])
    )

    bind[CampaignInformationService] to new CampaignInformationServiceImpl(inject[NpcClient])
    bind[SupplierMetadataService] to new SupplierMetadataServiceImpl(supplierCommonSqlContext)
    bind[SupplierInformationService] to new SupplierInformationServiceImpl(inject[SupplierMetadataService])

    bind[BenefitInformationService] to new BenefitInformationServiceImpl(
      inject[GQLCMSService],
      inject[BenefitRepositoryAgsql]
    )

    bind[ImageService] to new ImageServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupImagesRequestEnvelope, List[Wrapper[ContentImagesResponse]]]]
    )

    bind[ReviewScoreService] to new ReviewScoreServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupReviewScoreRequestEnvelope, List[Wrapper[ContentReviewScoreResponse]]]]
    )

    bind[ReviewSummariesService] to new ReviewSummariesServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupReviewSummariesRequestEnvelope, List[
        Wrapper[ContentReviewSummariesResponse]
      ]]]
    )

    bind[InformationSummaryService] to new InformationSummaryServiceImpl(
      inject[
        ChunkableServiceFuture[ContentInformationSummaryGroupRequestEnvelope, List[Wrapper[ContentInformationSummary]]]
      ]
    )

    bind[InformationService] to new InformationServiceImpl(
      inject[ChunkableServiceFuture[ContentInformationGroupRequestEnvelope, List[Wrapper[InformationResponse]]]]
    )

    bind[LocalInformationService] to new LocalInformationServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupLocalInformationRequestEnvelope, List[Wrapper[LocalInformation]]]]
    )

    bind[EngagementService] to new EngagementServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupEngagementRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[ExperiencesService] to new ExperiencesServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupExperienceRequestEnvelope, List[Wrapper[Experiences]]]]
    )

    bind[FeaturesService] to new FeaturesServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupFeaturesRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[HighlightsService] to new HighlightsServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupHighlightsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[QnaService] to new QnaServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupQnaRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[TopicsService] to new TopicsServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupTopicsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[RoomSummaryService] to new RoomSummaryServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupRoomSummaryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[RateCategoryService] to new RateCategoryServiceImpl(
      inject[ChunkableServiceFuture[ContentGroupRateCategoryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]]
    )

    bind[GandalfService] to new GandalfServiceImpl(
      inject[PromoAgHttpClient[Future]],
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].gandalfCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        Map("endpoint" -> "api/promocode")
      ),
      inject[GQLCMSService]
    )

    bind[PopularEnrichService] to new PopularEnrichServiceImpl {
      override protected val returnCustomerRepository: NumberOfReturnCustomerRepository =
        inject[NumberOfReturnCustomerRepository]
    }

    bind[DataFetcher] to injected[DefaultDataFetcher](
      'pagingService   -> inject[PagedPropertyService](identified by 'PagedPropertyService),
      'datelessService -> inject[PagedDatelessPropertyService](identified by 'PagedDatelessPropertyService)
    )

    bind[PriceStreamDataFetcher] to injected[DefaultPriceStreamDataFetcher]
    bind[PriceTrendSummaryDataFetcher] to injected[DefaultPriceTrendSummaryDataFetcher]
    bind[PropertyAllotmentSummaryDataFetcher] to injected[DefaultPropertyAllotmentSummaryDataFetcher]

  }

  class ServerModule extends Module {

    bind[GraphQLRequestForwardingHelper] to new GraphQLRequestForwardingHelper(
      cachedQueryParser = inject[CachedQueryParser]
    )

  }

}
