package com.agoda.papi.search.complexsearch.service

import api.request.FeatureFlag
import cats.instances.future._
import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.FilterRequest
import com.agoda.core.search.models.enumeration.SortFields
import com.agoda.core.search.models.request._
import com.agoda.core.search.models.response.{
  AggregationResponse,
  ObjectExtraInfo,
  PostPagingAggregationResponse,
  SearchResult
}
import com.agoda.papi.content.graphql.ContentGroupRoomSummaryRequestEnvelope
import com.agoda.papi.search.common.externaldependency.promocode.GandalfService
import com.agoda.papi.search.common.externaldependency.repository.PointsMaxDescriptionRepositoryAgsql
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.service.LocaleService
import com.agoda.papi.search.common.util.ModelHelper
import com.agoda.papi.search.common.util.constant.LanguageID
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext, WithCorrelationId }
import com.agoda.papi.search.common.util.debug.ResultTrackerSteps.{
  SelectedProperty,
  SelectedPropertyRequest,
  SoldOutProperty
}
import com.agoda.papi.search.common.util.exception.{ BaseException, DataFetcherException, InvalidRequestException }
import com.agoda.papi.search.common.util.logger.{ HadoopLogger, PAPIAppLogger }
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementName, MeasurementTag, MetricsHelper }
import com.agoda.papi.search.complexsearch.PropertySummaryEnvelopeList
import com.agoda.papi.search.complexsearch.requesthelper.SearchRequestHelper
import com.agoda.papi.search.complexsearch.service.PropertyMergerService.{
  getPrevPagePropSize,
  APOInjectionIntervalGroup,
  SoldOutsInjectionInterval
}
import com.agoda.papi.search.complexsearch.service.agodahome.{
  ExtraAgodaHomesService,
  FeaturedAgodaHomesService,
  HighlyRatedAgodaHomesService
}
import com.agoda.papi.search.complexsearch.service.debug.GqlResultTracking.{
  propertySummaryEnvelopeCheapestRoomReturnListResponse,
  propertySummaryEnvelopeListResponse
}
import com.agoda.papi.search.complexsearch.service.enrichment.{ KoddiEnrichService, PopularEnrichService }
import com.agoda.papi.search.complexsearch.service.paging.PageTokenService
import com.agoda.papi.search.complexsearch.service.searchenrichment.{
  BenefitInformationService,
  CampaignInformationService,
  SupplierInformationService
}
import com.agoda.papi.search.complexsearch.util.ResultTracking.vectorToList
import com.agoda.papi.search.internalmodel._
import com.agoda.papi.search.internalmodel.content.PAPIContentSummary
import com.agoda.papi.search.internalmodel.enumeration.TopSellingPointTypes.{
  DealOfTheDay,
  FiveStarDealOfTheDay,
  HomesDealOfTheDay
}
import com.agoda.papi.search.internalmodel.enumeration.UniqueSellingPointProperties.{
  Hotel3Stars,
  Hotel4Stars,
  Hotel5Stars
}
import com.agoda.papi.search.internalmodel.enumeration.UniqueSellingPointTypes.BestGuestRate
import com.agoda.papi.search.internalmodel.enumeration.{
  PropertyResultTypes,
  TopSellingPointType,
  UniqueSellingPointProperty
}
import com.agoda.papi.search.internalmodel.promocode.{ BannerCampaignInfo, BannerCampaignRequest }
import com.agoda.papi.search.internalmodel.response._
import com.agoda.papi.search.types.{ HotelIdType, PropertyId, RegionIdType }
import com.agoda.platform.service.context.GlobalContext
import com.agoda.propertyapi.search.page.PageToken
import io.opentracing.Tracer
import models.pricing.LoyaltyItem.{ VipGoldChannel, VipPlatinumBasicChannel, VipPlatinumChannel }
import scala.concurrent.{ ExecutionContext, Future }

/** <AUTHOR>   mkhan
  *
  * Root Interface for fetching all the data from the services
  */
trait DataFetcher {

  /** @param searchRequestWrapper
    * @param executionContext
    * @return
    *   Future[SearchResult]
    */
  def fetchSearchResponse[T <: BaseRequest[ComplexSearchRequest]](
      searchType: String,
      searchRequestWrapper: Wrapper[T]
  )(implicit executionContext: ExecutionContext, searchContextHolder: ContextHolder): Future[SearchResult]

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(implicit
      executionContext: ExecutionContext,
      ctx: GraphQLContext,
      hadoopLogger: HadoopLogger
  ): Future[CumulativePropertiesResponse]

  /** @param searchRequest
    * @param searchResult
    * @param propertySummaryModel
    * @param searchContextHolder
    * @return
    */
  def fetchEnrichment(
      searchRequest: BaseRequest[ComplexSearchRequest],
      searchResult: SearchResult,
      propertySummaryModel: Vector[PropertySummaryModel]
  )(implicit searchContextHolder: ContextHolder): Map[HotelIdType, PropertySummaryEnrichment]

  /** @param searchRequest
    * @param propertyIds
    * @param contentList
    * @param executionContext
    * @return
    */
  def fetchBookingHistoryForEnrichment(
      searchRequest: BaseRequest[ComplexSearchRequest],
      propertyIds: Seq[HotelIdType],
      contentList: Seq[PAPIContentSummary]
  )(implicit executionContext: ExecutionContext): Future[Map[HotelIdType, BookingHistoryInfo]]

  /** @param searchRequest
    * @param searchResult
    * @param cumulativePropertiesResponse
    * @param executionContext
    * @param searchContextHolder
    * @return
    */
  def fetchAggregatedSearchResult(
      searchRequest: BaseRequest[ComplexSearchRequest],
      searchResult: SearchResult,
      cumulativePropertiesResponseO: Option[CumulativePropertiesResponse]
  )(implicit executionContext: ExecutionContext, searchContextHolder: ContextHolder): Future[AggregationResponse]

  /** @param searchRequest
    * @param searchResult
    * @param cumulativePropertiesResponse
    * @param executionContext
    * @param searchContextHolder
    * @return
    */
  def fetchPostPagingAggregatedSearchResult(
      searchRequest: BaseRequest[ComplexSearchRequest],
      searchResult: SearchResult,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PostPagingAggregationResponse]

  /** @param searchContext
    * @param objectInfo
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchGeoPlaces(objectInfo: ObjectExtraInfo, searchContext: SearchContext)(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Seq[GeoPlace]]

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchFeaturedAgodaHomes(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList]

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchHighlyRatedAgodaHomes(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList]

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchExtraAgodaHomes(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList]

  def fetchLuxuryHotels(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList]

  def fetchCreditCardCampaignInfo(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Seq[CreditCardCampaignInfo]]

  def fetchIsConnectedApplicable(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  ): Option[Boolean]

  def fetchPointMaxDescription(propertySearchRequestWrapper: PropertySearchRequestWrapper)(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Option[PointMaxInformation]]

  def fetchBenefitInformation(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[List[BenefitInformation]]

  def fetchSuppliersInformation(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit executionContext: ExecutionContext, searchContextHolder: ContextHolder): List[SupplierInformation]

  def fetchIsPopular(
      propertyIds: List[HotelIdType]
  )(implicit ec: ExecutionContext, withCorrelationId: WithCorrelationId): Future[Map[HotelIdType, Boolean]]

  def fetchPackagingDetails(searchRequest: BaseRequest[ComplexSearchRequest]): (Option[FilterRequest], Option[Double])

  def fetchHotelRoomInformation(
      propertySummaryModel: Vector[PropertySummaryModel]
  ): Map[HotelIdType, HotelRoomInformation]

  def fetchRegionBannerCampaignResponse(regionIdType: RegionIdType, bannerCampaignRequest: BannerCampaignRequest)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[List[BannerCampaignInfo]]

  def fetchRoomSummaryResponse(
      contentGroupRoomSummaryRequestEnvelope: ContentGroupRoomSummaryRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchFeaturedPulseProperties(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList]

}

class DefaultDataFetcher(
    searchService: SearchService,
    pagingService: PagedPropertyService,
    enrichmentService: EnrichmentService,
    extraHotelServices: ExtraHotelServices,
    soldOutService: SoldOutPropertyService,
    datelessService: PagedDatelessPropertyService,
    propertyMergerService: PropertyMergerService,
    geoService: GeoService,
    featuredAgodaHomesService: FeaturedAgodaHomesService,
    highlyRatedAgodaHomesService: HighlyRatedAgodaHomesService,
    extraAgodaHomesService: ExtraAgodaHomesService,
    featuredLuxuryHotelsService: LuxuryHotelsService,
    featuredPulsePropertiesService: PulsePropertiesService,
    supplierInformationService: SupplierInformationService,
    benefitInformationService: BenefitInformationService,
    pointsMaxDescriptionRepositoryAgsql: PointsMaxDescriptionRepositoryAgsql,
    popularEnrichService: PopularEnrichService,
    apoService: APOService,
    reporter: MetricsReporter,
    packagingService: PackagingService,
    tracing: Tracer,
    localeService: LocaleService,
    campaignInformationService: CampaignInformationService,
    gandalfService: GandalfService,
    pageTokenService: PageTokenService,
    topSellingPointService: TopSellingPointService,
    uniqueSellingPointService: UniqueSellingPointService,
    roomSummaryService: RoomSummaryService,
    preFilterAccuracyReporter: HeuristicAccuracyReporter
) extends DataFetcher with MetricsHelper with PAPIAppLogger {
  val PROGRAM_NAME_TYPE_ID                                  = 7
  val MAX_PAGES_IN_PAGE_TOKEN                               = 10
  val eligibleTspTypeForJacketing: Set[TopSellingPointType] = Set(FiveStarDealOfTheDay, DealOfTheDay, HomesDealOfTheDay)
  val eligibleUspPropertyTypeForJacketing: Set[Option[UniqueSellingPointProperty]] =
    Set(Option(Hotel3Stars), Option(Hotel4Stars), Option(Hotel5Stars))
  val vipJacketList = Set(VipPlatinumBasicChannel.toString, VipPlatinumChannel.toString, VipGoldChannel.toString)

  override val metricsReporter = reporter
  override val tracer          = tracing

  private def raiseDataException(
      message: String,
      exception: Throwable,
      globalContext: GlobalContext,
      failRequest: Boolean = false
  ) = {
    val originalException = exception match {
      case e: BaseException => if (e.getInnerCause() == null) e else e.getInnerCause()
      case _                => exception
    }
    throw new DataFetcherException(
      s"Error fetching $message :${originalException.getMessage}",
      originalException,
      failRequest
    )
  }

  /** @param searchRequestWrapper
    * @param executionContext
    * @return
    *   Future[SearchResult]
    */
  override def fetchSearchResponse[T <: BaseRequest[ComplexSearchRequest]](
      searchType: String,
      searchRequestWrapper: Wrapper[T]
  )(implicit executionContext: ExecutionContext, searchContextHolder: ContextHolder): Future[SearchResult] =
    measureFuture(
      MeasurementName.DataFetch.CoreSearchPreFilter,
      Map(MeasurementName.SearchType.SearchTypeTag -> searchType)
    ) {
      searchService.complexSearch(searchRequestWrapper.t)
    }.recover { case cause => raiseDataException("search response", cause, searchContextHolder.globalContext, true) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  override def fetchProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      ctx: GraphQLContext,
      hadoopLogger: HadoopLogger
  ): Future[CumulativePropertiesResponse] =
    measureFuture(
      MeasurementName.DataFetch.MergeSearch,
      searchLatencyMessage = Some(ctx.searchLatencyMessage)
    ) {
      fetchMergedList(propertySearchRequestWrapper, searchResult)
    }

  /*
    sortedProperties may contain unfilteredProperties which are shown as Recommendations
    so we need to reorder the sortedProperties i.e., first filteredSortedProperties followed by recommendedSortedProperties.
   */
  def reorderPostPageSortedProperties(
      sortedProperties: CumulativePropertiesResponse,
      recommendedProperties: scala.collection.Set[HotelIdType],
      startIndex: Int,
      endIndex: Int
  ): CumulativePropertiesResponse = {
    val (recommendedSortedProperties, regularProperties) =
      sortedProperties.propertySummaryWrapper.partition(p => recommendedProperties.contains(p.propertyId))
    val propertiesBeforeSlicing = regularProperties ++ recommendedSortedProperties
    val propertiesAfterSlicing  = propertiesBeforeSlicing.slice(startIndex, endIndex)
    sortedProperties.copy(propertySummaryWrapper = propertiesAfterSlicing)
  }

  private[service] def checkPulseCampaignMetadata(propertySummaryEnvelop: PropertySummaryEnvelope): Boolean =
    propertySummaryEnvelop.pricing.exists(_.t.pulseCampaignMetadata.isDefined)

  private[service] def checkVIPDeals(propertySummaryEnvelop: PropertySummaryEnvelope): Boolean =
    propertySummaryEnvelop.pricing.exists(
      _.t.offers.exists(_.roomOffers.exists(_.room.loyaltyDisplay.exists(_.items.exists(vipJacketList.contains))))
    )

  private def getDealsOfTheDay(
      normalProperties: List[PropertySummaryEnvelope],
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit searchContextHolder: ContextHolder): PropertiesWithEnrichment = {
    val propertySummaryModel = normalProperties
      .map(property =>
        PropertySummaryModel(
          property.propertyId,
          Some(property.content.t),
          property.pricing.map(_.t),
          property.propertyResultType,
          property.overrideCheckInOutDate,
          None,
          None
        )
      )
      .toVector
    val propertiesSummaryForEnrichment = enrichmentService.getPropertyInformationForEnrichment(
      propertySearchRequestWrapper.searchRequest,
      searchResult,
      propertySummaryModel
    )
    val topSellingPointMap = topSellingPointService.markTopSellingPoints(propertiesSummaryForEnrichment)(
      propertySearchRequestWrapper.searchRequest,
      searchContextHolder
    )
    val topSellingPointList = topSellingPointMap
      .filter(_._2.exists(tspType => eligibleTspTypeForJacketing.contains(tspType.tspType)))
      .keys
      .toList
    val uniqueSellingPointMap = uniqueSellingPointService.markUniqueSellingPoints(propertiesSummaryForEnrichment)(
      propertySearchRequestWrapper.searchRequest,
      searchContextHolder
    )
    val uniqueSellingPointList = uniqueSellingPointMap
      .filter(
        _._2.exists(uspInfo =>
          uspInfo.uspType == BestGuestRate &&
            eligibleUspPropertyTypeForJacketing.contains(uspInfo.uspPropertyType)
        )
      )
      .keys
      .toList
    val eligibleTspAndUspList = topSellingPointList ++ uniqueSellingPointList

    PropertiesWithEnrichment(
      normalProperties.filter(property =>
        eligibleTspAndUspList.contains(property.propertyId) || checkPulseCampaignMetadata(property) || checkVIPDeals(
          property
        )
      ),
      topSellingPointMap,
      uniqueSellingPointMap
    )
  }

  def fetchMergedList(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(implicit
      executionContext: ExecutionContext,
      ctx: GraphQLContext,
      hadoopLogger: HadoopLogger
  ): Future[CumulativePropertiesResponse] = {
    implicit val searchContextHolder: ContextHolder = ctx.getSearchContext

    val newRequestWrapper = ModelHelper.updatePropertySearchRequestWrapper(
      propertySearchRequestWrapper,
      searchResult,
      ctx.getSearchContext.experimentContext
        .isBVariant(ctx.getSearchContext.allocationContext.enableOverrideRequiredBasis)
    )
    val enableDealsOfTheDayFilter = propertySearchRequestWrapper.searchRequest.searchCriteria.featureFlagRequest
      .flatMap(_.enableDealsOfTheDayFilter)
      .getOrElse(false)
    val hasSecretDealSort = propertySearchRequestWrapper.searchRequest.searchCriteria.sorting.exists(s =>
      s.sortField.equals(SortFields.SecretDeal)
    )

    val normalPropertiesWithRemainingF =
      if (propertySearchRequestWrapper.searchRequest.searchCriteria.isDatelessSearch.getOrElse(false)) {
        fetchDatelessProperty(newRequestWrapper, searchResult)
      } else fetchPagedProperty(newRequestWrapper, searchResult)

    val apoPropertyF       = fetchApoPagedProperty(newRequestWrapper, searchResult)
    val extraPropertiesF   = fetchExtraProperty(newRequestWrapper, searchResult)
    val soldOutPropertiesF = fetchSoldOutProperty(newRequestWrapper, searchResult)

    for {
      normalPropertiesWithRemaining <- normalPropertiesWithRemainingF
      apoProperty                   <- apoPropertyF
      extraProperties               <- extraPropertiesF
      soldOutProperties             <- soldOutPropertiesF
    } yield {

      val eligibleAPO = apoProperty.map(a => apoService.getNormalPageOfApoIndex(a)(propertySearchRequestWrapper))

      val (
        normalPropertiesWithRemainingProperties,
        sLtoBeInCurrentPage,
        shiftedApoOffsetIndex,
        expectedSizeOnPrevPages,
        isFilterApply,
        hasSecretDealSortOrFilter
      ) = ctx.getSearchContext.experimentContext.runABExperiment(
        ctx.getSearchContext.allocationContext.recalculateSponsoredPositionWithSeperationLogic,
        (
          normalPropertiesWithRemaining.propertySummaryWrapper,
          List.empty,
          apoService.getShiftedApoOffsetIndex(
            propertySearchRequestWrapper,
            searchResult,
            apoProperty,
            searchResult.properties.fetchInformation.startIndex,
            searchResult.properties.fetchInformation.endIndex
          ),
          0,
          PropertyMergerService.isFilterApply(propertySearchRequestWrapper),
          PropertyMergerService.hasSecretDealSortOrFilter(propertySearchRequestWrapper)
        ),
        seperateSLfromNormalPropertiesLogic(
          normalPropertiesWithRemaining,
          propertySearchRequestWrapper,
          searchResult,
          apoProperty,
          enableDealsOfTheDayFilter
        )
      )

      val slicedAvailableProperties = reorderPostPageSortedProperties(
        normalPropertiesWithRemaining.copy(propertySummaryWrapper = normalPropertiesWithRemainingProperties),
        Set.empty,
        shiftedApoOffsetIndex.startIndex,
        shiftedApoOffsetIndex.endIndex
      )

      val (remainingProperties, normalProperties) = slicedAvailableProperties.propertySummaryWrapper.partition(
        _.propertyResultType == PropertyResultTypes.RemainingProperty
      )

      preFilterAccuracyReporter.reportPreFilterAccuracy(
        normalProperties ++ sLtoBeInCurrentPage,
        searchResult,
        propertySearchRequestWrapper.searchRequest,
        searchContextHolder,
        propertySearchRequestWrapper
      )
      preFilterAccuracyReporter.reportAvailabilityAccuracy(
        normalProperties ++ sLtoBeInCurrentPage,
        searchResult,
        propertySearchRequestWrapper.searchRequest
      )

      reportNumberOfNormalProperties(propertySearchRequestWrapper, normalProperties ++ sLtoBeInCurrentPage)
      def enrichProperties(properties: List[PropertySummaryEnvelope]): PropertiesWithEnrichment =
        if (hasSecretDealSort && enableDealsOfTheDayFilter) {

          getDealsOfTheDay(properties, propertySearchRequestWrapper, searchResult)
        } else {
          PropertiesWithEnrichment(
            properties,
            Map.empty[HotelIdType, Seq[TopSellingPointInfo]],
            Map.empty[HotelIdType, Seq[UniqueSellingPointInfo]]
          )
        }

      val secretDealProperties       = eligibleAPO.map(_.propertySummaryWrapper).getOrElse(List.empty)
      val normalPropertiesWithTspUsp = enrichProperties(normalProperties)
      val mergedResult = propertyMergerService.merge(
        propertySearchRequestWrapper,
        normalPropertiesWithTspUsp.normalProperties,
        secretDealProperties,
        extraProperties,
        soldOutProperties,
        remainingProperties,
        enableDealsOfTheDayFilter,
        isFilterApply,
        hasSecretDealSortOrFilter,
        enrichProperties(sLtoBeInCurrentPage).normalProperties,
        searchResult.sponsoredPropertyIdToPositionMap,
        expectedSizeOnPrevPages
      )

      val mergedResultWithTspAnsUsp = mergedResult.map(mr =>
        mr.copy(
          topSellingPointInfo = normalPropertiesWithTspUsp.topSellingPointInfoMap.get(mr.propertyId),
          uniqueSellingPointInfo = normalPropertiesWithTspUsp.uniqueSellingPointInfoMap.get(mr.propertyId)
        )
      )

      // no ranking changed, only add additional tracking data
      val enrichedMergedResult = KoddiEnrichService.enrichRank(searchResult, mergedResultWithTspAnsUsp)

      val pageToken = generatePageToken(
        normalPropertiesWithRemaining,
        normalProperties ++ sLtoBeInCurrentPage,
        propertySearchRequestWrapper,
        mergedResult.size
      )(
        searchContextHolder
      )
      pageToken.foreach { token =>
        reporter.report(
          MeasurementName.PageTokenSize,
          token.length().toLong,
          Map(
            MeasurementTag.Platform -> propertySearchRequestWrapper.searchRequest.searchContext.platform.toString(),
            MeasurementTag.SortField -> propertySearchRequestWrapper.searchRequest.searchCriteria.sorting
              .map(_.sortField.entryName)
              .getOrElse(SortFields.Ranking.entryName)
          )
        )
      }

      val mergedPagingFlowMetadata = PagingFlowMetadata(
        normalPropertiesWithRemaining.pagingFlowMetadata
          .map(_.iterationCountByPropertyResultType)
          .getOrElse(Map.empty) ++
          apoProperty
            .flatMap(_.pagingFlowMetadata.map(_.iterationCountByPropertyResultType))
            .getOrElse(Map.empty)
      )

      CumulativePropertiesResponse(
        enrichedMergedResult,
        normalPropertiesWithRemaining.inValidHotels,
        normalPropertiesWithRemaining.calledProperties,
        eligibleAPO.map(_.inValidApoHotels).getOrElse(Vector.empty),
        pageToken,
        Some(mergedPagingFlowMetadata)
      )
    }
  }

  private def reportNumberOfNormalProperties(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      normalProperties: List[PropertySummaryEnvelope]
  )(implicit contextHolder: ContextHolder): Unit = {
    val pageSize = propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageSize).getOrElse(30)
    metricsReporter.report(
      MeasurementKey.NormalProperties,
      normalProperties.size,
      Map(
        MeasurementTag.PageSize        -> pageSize.toString,
        MeasurementTag.IsPageFulfilled -> (normalProperties.size == pageSize).toString
      )
    )
  }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchPagedProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[CumulativePropertiesResponse] =
    measureFuture(
      MeasurementName.DataFetch.SearchAvailable,
      searchLatencyMessage = searchContextHolder.searchLatencyMessage
    ) {
      pagingService.getPagedProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause => raiseDataException("properties", cause, searchContextHolder.globalContext, true) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchExtraProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchExtra) {
      searchContextHolder.papiSearchResultTracker.step(searchResult.properties.selectedProperties)(
        SelectedPropertyRequest,
        vectorToList,
        () => SearchRequestHelper.isRequestedExtraProperty(propertySearchRequestWrapper)
      )
      searchContextHolder.papiSearchResultTracker.stepF(
        extraHotelServices.getProperties(propertySearchRequestWrapper, searchResult)
      )(
        SelectedProperty,
        propertySummaryEnvelopeCheapestRoomReturnListResponse,
        () => SearchRequestHelper.isRequestedExtraProperty(propertySearchRequestWrapper)
      )
    }.recover { case cause => raiseDataException("extra properties", cause, searchContextHolder.globalContext, true) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    */

  def fetchSoldOutProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchSoldOut) {
      searchContextHolder.papiSearchResultTracker.stepF(
        soldOutService.getProperties(propertySearchRequestWrapper, searchResult)
      )(SoldOutProperty, propertySummaryEnvelopeListResponse, () => searchResult.properties.soldOutProperties.nonEmpty)
    }.recover { case cause => raiseDataException("soldout properties", cause, searchContextHolder.globalContext) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @param searchContextHolder
    * @return
    */
  def fetchDatelessProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[CumulativePropertiesResponse] =
    measureFuture(
      MeasurementName.DataFetch.SearchDateless,
      searchLatencyMessage = searchContextHolder.searchLatencyMessage
    ) {
      datelessService.getPagedProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause => raiseDataException("dateless properties", cause, searchContextHolder.globalContext) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  def fetchApoPagedProperty(propertySearchRequestWrapper: PropertySearchRequestWrapper, searchResult: SearchResult)(
      implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Option[CumulativePropertiesResponse]] =
    measureFuture(
      MeasurementName.DataFetch.SearchApo,
      searchLatencyMessage = searchContextHolder.searchLatencyMessage
    ) {
      pagingService.getAPOPagedProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause => raiseDataException("apo  properties", cause, searchContextHolder.globalContext) }

  /** @param searchRequest
    * @param searchResult
    * @param propertySummaryModel
    * @param searchContextHolder
    * @return
    *   PropertySummaryEnrichment
    */
  override def fetchEnrichment(
      searchRequest: BaseRequest[ComplexSearchRequest],
      searchResult: SearchResult,
      propertySummaryModel: Vector[PropertySummaryModel]
  )(implicit searchContextHolder: ContextHolder): Map[HotelIdType, PropertySummaryEnrichment] =
    measure(MeasurementName.DataFetch.Enrichment) {
      enrichmentService.enrichProperties(searchRequest, searchResult, propertySummaryModel)
    }

  /** @param searchRequest
    * @param propertyIds
    * @param contentList
    * @param executionContext
    * @return
    */
  override def fetchBookingHistoryForEnrichment(
      searchRequest: BaseRequest[ComplexSearchRequest],
      propertyIds: Seq[HotelIdType],
      contentList: Seq[PAPIContentSummary]
  )(implicit executionContext: ExecutionContext): Future[Map[HotelIdType, BookingHistoryInfo]] =
    measure(MeasurementName.DataFetch.BookingHistory) {
      enrichmentService.fetchBookingHistoryInfo(searchRequest, propertyIds, contentList)
    }

  override def fetchGeoPlaces(objectInfo: ObjectExtraInfo, searchContext: SearchContext)(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Seq[GeoPlace]] =
    measureFuture(MeasurementName.DataFetch.GeoPlace) {
      geoService.searchLandmarksWithinRadius(
        objectInfo,
        searchContext
      )
    }

  /** @param searchRequest
    * @param searchResult
    * @param cumulativePropertiesResponse
    * @param executionContext
    * @param searchContextHolder
    * @return
    */
  override def fetchAggregatedSearchResult(
      searchRequest: BaseRequest[ComplexSearchRequest],
      searchResult: SearchResult,
      cumulativePropertiesResponseO: Option[CumulativePropertiesResponse]
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[AggregationResponse] =
    measureFuture(MeasurementName.DataFetch.SearchAggregation) {

      val invalidPropertyIds: Set[HotelIdType] = cumulativePropertiesResponseO match {
        case Some(cumulativePropertiesResponse) => cumulativePropertiesResponse.inValidHotels.toSet
        case _                                  => Set.empty
      }

      val aggregationResponseF =
        searchService.aggregate(searchRequest, searchResult.aggregationInput, invalidPropertyIds)
      val fullAggregationResponse =
        if (searchRequest.searchCriteria.featureFlagRequest.flatMap(_.zeroCountFiltersInMatrix).getOrElse(false)) {
          val filterlessRequest = searchRequest.withoutFilters()
          val allAvailableProperties: Set[HotelIdType] = searchResult.aggregationInput.allProperties
            .withFilter(_._2.availability.exists(_.isAvailable))
            .map(_._1)(collection.breakOut)
          val aggregationInputFilterless = searchResult.aggregationInput.copy(
            exceptOrFilter = allAvailableProperties,
            exceptElasticIds = allAvailableProperties,
            // We cannot actually know for sure if there will be an insider deal without doing the actual search.
            hasInsiderDeal = true
          )
          Some(searchService.aggregate(filterlessRequest, aggregationInputFilterless, Set.empty))
        } else {
          None
        }
      AggregationMatrixMerger.merge(aggregationResponseF, fullAggregationResponse)
    }.recover { case cause => raiseDataException("aggregation result", cause, searchContextHolder.globalContext, true) }

  override def fetchPostPagingAggregatedSearchResult(
      searchRequest: BaseRequest[ComplexSearchRequest],
      searchResult: SearchResult,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PostPagingAggregationResponse] =
    measureFuture(MeasurementName.DataFetch.SearchPostPagingAggregation) {
      val resultPropertyIds: Set[HotelIdType] =
        cumulativePropertiesResponse.propertySummaryWrapper.map(_.propertyId)(collection.breakOut): Set[HotelIdType]
      val invalidPropertyIds: Set[HotelIdType] = cumulativePropertiesResponse.inValidHotels.toSet
      val aggregationResponseF = searchService.postPagingAggregate(
        searchRequest,
        resultPropertyIds,
        searchResult.aggregationInput,
        invalidPropertyIds
      )
      aggregationResponseF
    }.recover { case cause => raiseDataException("aggregation result", cause, searchContextHolder.globalContext, true) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @param searchContextHolder
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  override def fetchFeaturedAgodaHomes(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchFeaturedAgodaHomes) {
      featuredAgodaHomesService.getProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause => raiseDataException("featured agoda homes", cause, searchContextHolder.globalContext) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param executionContext
    * @param searchContextHolder
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  override def fetchHighlyRatedAgodaHomes(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchHighlyRatedAgodaHomes) {
      highlyRatedAgodaHomesService.getProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause => raiseDataException("highly rated agoda homes", cause, searchContextHolder.globalContext) }

  /** @param propertySearchRequestWrapper
    * @param searchResult
    * @param cumulativePropertiesResponse
    * @param executionContext
    * @param searchContextHolder
    * @return
    *   Future[Map[PropertyId,PropertySummaryWrapper]]
    */
  override def fetchExtraAgodaHomes(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchExtraAgodaHomes) {
      extraAgodaHomesService.getProperties(propertySearchRequestWrapper, searchResult, cumulativePropertiesResponse)
    }.recover { case cause => raiseDataException("extra agoda homes", cause, searchContextHolder.globalContext) }

  override def fetchLuxuryHotels(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchFeaturedLuxuryHotels) {
      featuredLuxuryHotelsService.getProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause => raiseDataException("featured luxury hotels", cause, searchContextHolder.globalContext) }

  override def fetchCreditCardCampaignInfo(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Seq[CreditCardCampaignInfo]] =
    spanFuture(MeasurementName.DataFetch.CreditCardCampaignInfo) {
      campaignInformationService.getCreditCardCampaignInfo(
        propertySearchRequestWrapper,
        cumulativePropertiesResponse.propertySummaryWrapper
      )
    }

  override def fetchIsConnectedApplicable(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  ): Option[Boolean] =
    span(MeasurementName.DataFetch.IsConnectedTripApplicable) {
      val connectedTripFlow = propertySearchRequestWrapper.pricingRequestWrapper
        .exists(_.t.pricing.featureFlag.contains(FeatureFlag.ConnectedTrip))
      if (!connectedTripFlow) None
      else {
        Some(
          cumulativePropertiesResponse.propertySummaryWrapper.exists(propertySummary =>
            {
              for {
                propertyPricing <- propertySummary.pricing.headOption
                roomOffers      <- propertyPricing.t.offers.headOption
                roomBundleOffer <- roomOffers.roomOffers.headOption
                roomPricing     <- roomBundleOffer.room.pricing.headOption
              } yield roomPricing.packagePriceAndSaving.isDefined
            }.getOrElse(false)
          )
        )
      }
    }

  def fetchPointMaxDescription(propertySearchRequestWrapper: PropertySearchRequestWrapper)(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Option[PointMaxInformation]] =
    spanFuture(MeasurementName.DataFetch.PointMaxDescription) {
      propertySearchRequestWrapper.pricingRequestWrapper
        .map { request =>
          val langId = propertySearchRequestWrapper.globalContext.getLanguageLocale
            .map(localeService.localeToLanguageUse)
            .getOrElse(LanguageID.ENGLISH)
          for {
            pointMaxName <- pointsMaxDescriptionRepositoryAgsql.getPointsMaxData(
              langId,
              request.t.pricing.features.priusId,
              PROGRAM_NAME_TYPE_ID
            )
          } yield pointMaxName.headOption.map(_.translatedCms).map { pointMaxName =>
            PointMaxInformation(pointMaxName)
          }
        }
        .getOrElse(Future.successful(None))
    }

  def fetchBenefitInformation(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[List[BenefitInformation]] =
    spanFuture(MeasurementName.DataFetch.BenefitInformation) {
      val langId = propertySearchRequestWrapper.globalContext.getLanguageLocale
        .map(localeService.localeToLanguageUse)
        .getOrElse(LanguageID.ENGLISH)
      benefitInformationService.getBenefitInformation(langId, cumulativePropertiesResponse.propertySummaryWrapper)
    }

  def fetchSuppliersInformation(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      cumulativePropertiesResponse: CumulativePropertiesResponse
  )(implicit executionContext: ExecutionContext, searchContextHolder: ContextHolder): List[SupplierInformation] =
    span(MeasurementName.DataFetch.SuppliersInformation) {
      supplierInformationService.getSupplierInformation(
        propertySearchRequestWrapper,
        cumulativePropertiesResponse.propertySummaryWrapper
      )
    }

  def fetchIsPopular(
      propertyIds: List[HotelIdType]
  )(implicit ec: ExecutionContext, withCorrelationId: WithCorrelationId): Future[Map[HotelIdType, Boolean]] =
    span(MeasurementName.DataFetch.IsPopular) {
      popularEnrichService.enrichIsPopular(propertyIds)
    }

  override def fetchPackagingDetails(
      searchRequest: BaseRequest[ComplexSearchRequest]
  ): (Option[FilterRequest], Option[Double]) =
    span(MeasurementName.DataFetch.PackagingDetails) {
      packagingService.extract(searchRequest)
    }

  override def fetchHotelRoomInformation(
      propertySummaryModel: Vector[PropertySummaryModel]
  ): Map[HotelIdType, HotelRoomInformation] =
    span(MeasurementName.DataFetch.HotelRoomInformation) {
      enrichmentService.getRoomInformation(propertySummaryModel)
    }

  override def fetchRegionBannerCampaignResponse(
      regionIdType: RegionIdType,
      bannerCampaignRequest: BannerCampaignRequest
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[List[BannerCampaignInfo]] =
    gandalfService.getBannerCampaignForRegion(regionIdType, bannerCampaignRequest.checkIn)

  private[service] def generatePageToken(
      normalPropertiesWithRemaining: CumulativePropertiesResponse,
      normalProperties: List[PropertySummaryEnvelope],
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      mergedPropertyListSize: Int
  )(implicit searchContextHolder: ContextHolder): Option[String] = {
    val notDatelessSearch = !propertySearchRequestWrapper.searchRequest.searchCriteria.isDatelessSearch.getOrElse(false)
    val isAddPageToken: Boolean = propertySearchRequestWrapper.searchRequest.searchCriteria.featureFlagRequest
      .flatMap(_.enablePageToken)
      .contains(true) && {
      propertySearchRequestWrapper.searchRequest.searchRequest.page match {
        case Some(page) if page.pageNumber > 1 =>
          // We only return page-token when the user is traversing each page 1-by-1.
          // If there is any page jump in the forward direction (ex from 1 to 3), we won't return any page-token back.
          // However, For pageNumber > 10, we still return the page-token back even if it doesn't contain every previous pages (but must at least have 1-10 pages).
          val maxPreviousPageNumber = if (page.pageNumber <= 10) page.pageNumber - 1 else 10
          pageTokenService
            .pageTokenFromPageRequest(page)
            .map(pageTokenService.containEveryPagesTil(_, maxPreviousPageNumber))
            .getOrElse(false)
        case _ => true
      }
    }

    if (notDatelessSearch && isAddPageToken) {
      val pageNumber = propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageNumber).getOrElse(1)
      val currentPageToken = propertySearchRequestWrapper.searchRequest.searchRequest.page
        .flatMap(pageTokenService.pageTokenFromPageRequest(_))
        .getOrElse(PageToken())
      // We allow only 10 pages in page-token, so its size won't be too big.
      // For pageNumber > 10, we will just
      if (pageNumber <= MAX_PAGES_IN_PAGE_TOKEN) {
        val pageUsedProperties: Seq[Int] =
          (normalPropertiesWithRemaining.inValidHotels ++ normalProperties.map(
            _.propertyId
          )).map(_.toInt)
        Some(
          pageTokenService.pageTokenToString(
            PageToken(
              pageNumberToPropertySize =
                currentPageToken.pageNumberToPropertySize + (pageNumber -> mergedPropertyListSize),
              pageNumberToUsedPropertyIds =
                currentPageToken.pageNumberToUsedPropertyIds + (pageNumber -> PageToken.UsedPropertyIds(
                  pageUsedProperties
                ))
            )
          )
        )
      } else {
        Some(
          pageTokenService.pageTokenToString(
            PageToken(
              pageNumberToPropertySize =
                currentPageToken.pageNumberToPropertySize + (pageNumber -> mergedPropertyListSize),
              pageNumberToUsedPropertyIds = currentPageToken.pageNumberToUsedPropertyIds
            )
          )
        )
      }
    } else {
      None
    }

  }

  override def fetchRoomSummaryResponse(
      contentGroupRoomSummaryRequestEnvelope: ContentGroupRoomSummaryRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.RoomSummary) {
      val roomSummaryF =
        roomSummaryService.getRoomSummary(contentGroupRoomSummaryRequestEnvelope, globalContext, experimentContext)
      for {
        contentRoomSummaryResponse <- roomSummaryF
      } yield contentRoomSummaryResponse.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchFeaturedPulseProperties(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  )(implicit
      executionContext: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[PropertySummaryEnvelopeList] =
    measureFuture(MeasurementName.DataFetch.SearchFeaturedPulseProperties) {
      featuredPulsePropertiesService.getProperties(propertySearchRequestWrapper, searchResult)
    }.recover { case cause =>
      raiseDataException("featured pulse properties", cause, searchContextHolder.globalContext)
    }

  def expectedPropSizeWithInjection(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      soldOutInterval: Int,
      hasSecretDealSortOrFilter: Boolean,
      enableDealsOfTheDayFilter: Boolean,
      isFilterApply: Boolean
  ): Int = {

    val pageSize =
      propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageSize).getOrElse(BaseRequest.PageSize)

    val apoPropertySize = pageSize / APOInjectionIntervalGroup
    val soldoutPropertiesSize =
      PropertyMergerService.calculateSoldoutPropertiesSize(pageSize, soldOutInterval, propertySearchRequestWrapper)

    if (hasSecretDealSortOrFilter && enableDealsOfTheDayFilter) {
      pageSize + apoPropertySize
    } else if (isFilterApply && !hasSecretDealSortOrFilter) {
      pageSize + apoPropertySize + soldoutPropertiesSize
    } else {
      pageSize + soldoutPropertiesSize
    }

  }

  def seperateSLfromNormalPropertiesLogic(
      normalPropertiesWithRemaining: CumulativePropertiesResponse,
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult,
      apoProperty: Option[CumulativePropertiesResponse],
      enableDealsOfTheDayFilter: Boolean
  )(implicit
      searchContextHolder: ContextHolder,
      ctx: GraphQLContext
  ): (List[PropertySummaryEnvelope], List[PropertySummaryEnvelope], OffsetIndex, Int, Boolean, Boolean) = {
    val (sponsoredPropertiesList, normalPropertiesWithoutSLWithRemaining) =
      normalPropertiesWithRemaining.propertySummaryWrapper
        .partition(property => searchResult.sponsoredPropertyIdToPositionMap.contains(property.propertyId))

    val isFilterApply: Boolean = PropertyMergerService.isFilterApply(propertySearchRequestWrapper)
    val hasSecretDealSortOrFilter: Boolean =
      PropertyMergerService.hasSecretDealSortOrFilter(propertySearchRequestWrapper)

    val currPageNumber = propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageNumber).getOrElse(1)
    val currPageSize =
      propertySearchRequestWrapper.searchRequest.searchRequest.page.map(_.pageSize).getOrElse(BaseRequest.PageSize)
    val expectedCurrPageSizeAfterInjection = expectedPropSizeWithInjection(
      propertySearchRequestWrapper,
      SoldOutsInjectionInterval,
      hasSecretDealSortOrFilter,
      enableDealsOfTheDayFilter,
      isFilterApply
    )

    val expectedSizeOnPrevPages = getPrevPagePropSize(
      propertySearchRequestWrapper,
      isFilterApply,
      hasSecretDealSortOrFilter,
      None,
      enableDealsOfTheDayFilter,
      pageTokenService.numberOfPropertiesOnPreviousPages(propertySearchRequestWrapper.searchRequest),
      pageTokenService.isPageTokenValidForPrevPageProp(propertySearchRequestWrapper.searchRequest)
    )
    val sLinjectedOnPrevPage = sponsoredPropertiesList.filter(property =>
      searchResult.sponsoredPropertyIdToPositionMap(property.propertyId) - expectedSizeOnPrevPages < 0
    )
    val normalPropOnPrevPage =
      if (currPageNumber == 1) 0 else currPageSize * (currPageNumber - 1) - sLinjectedOnPrevPage.size

    val sLtoBeInCurrentPage = sponsoredPropertiesList.filter(property =>
      searchResult.sponsoredPropertyIdToPositionMap(property.propertyId) - expectedSizeOnPrevPages >= 0 &&
        searchResult.sponsoredPropertyIdToPositionMap(
          property.propertyId
        ) - expectedSizeOnPrevPages - expectedCurrPageSizeAfterInjection < 0
    )
    val isPageTokenEnabled = propertySearchRequestWrapper.searchRequest.searchRequest.searchCriteria.featureFlagRequest
      .flatMap(_.enablePageToken)
      .contains(true)

    // properties that were excluded  from the prev pages were sLinjectedOnPrevPage.size
    // so we can reduce the original startIndex i.e. 45 for web by sLinjectedOnPrevPage.size
    // update endIndex = startIndex + normalToBeInCurrentPage
    val startIndex = searchResult.properties.fetchInformation.startIndex - sLinjectedOnPrevPage.size

    val normalToBeInCurrentPage = currPageSize - sLtoBeInCurrentPage.size
    val shiftedApoOffsetIndex =
      apoService.getShiftedApoOffsetIndex(
        propertySearchRequestWrapper,
        searchResult,
        apoProperty,
        startIndex,
        startIndex + normalToBeInCurrentPage
      )

    (
      normalPropertiesWithoutSLWithRemaining,
      sLtoBeInCurrentPage,
      shiftedApoOffsetIndex,
      expectedSizeOnPrevPages,
      isFilterApply,
      hasSecretDealSortOrFilter
    )
  }

}
