extend type PropertySummary{
    content:ContentSummary @loadContent
    pricing:HotelPricing @loadPricing
    roomSummary(ContentExtraRoomSummaryRequest: ContentExtraRoomSummaryRequest!):RoomSummaryResponse @loadContentRoomSummary
}
extend type PropertySearchResponse{
 properties(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters!, PriceStreamMetaLabRequest: PriceStreamMetaLabRequest):[PropertySummary!]! @loadProperty
  featuredAgodaHome(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters!):[PropertySummary!] @featuredAgodaHome
  highlyRatedAgodaHomes(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters!):[PropertySummary!] @highlyRatedAgodaHomes
  extraAgodaHomes(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters!):[PropertySummary!] @extraAgodaHomes
  featuredPulseProperties(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters!):[PropertySummary!] @featuredPulseProperties
}
extend type AlternateDatePricing{
    pricing:HotelPricing @loadAlternatePricing
}

extend type PropertySummarySearchResponse {
    properties(PricingSummaryRequest: PricingRequestParameters, ContentSummaryRequest: ContentSummaryRequest): [PropertySummary!] @loadPropertySummarySearch
}

extend type ContentDetail {
    contentReviewScore(ContentReviewScoreRequest: ContentReviewScoreRequest): ContentReviewScoreResponse @loadContentReviewScore
    contentExperiences(ContentExperienceRequest: ContentExperienceRequest): ContentExperiences @loadContentExperiences
    contentFeatures(ContentFeaturesRequest: ContentFeaturesRequest): ContentFeaturesResponse @loadContentFeatures
    contentHighlights(ContentHighlightsRequest: ContentHighlightsRequest) : ContentHighlightsResponse @loadContentHighlights
    contentSummary(ContentInformationSummaryRequest: ContentInformationSummaryRequest): ContentInformationSummary @loadContentInformationSummary
    contentEngagement: ContentPropertyEngagementResponse @loadContentEngagement
    contentImages(ContentImagesRequest: ContentImagesRequest!): ContentImagesResponse @loadContentImages
    contentInformation(ContentInformationRequest: ContentInformationRequest): ContentInformationResponse @loadContentInformation
    contentLocalInformation(ContentLocalInformationRequest: ContentLocalInformationRequest): ContentLocalInformationResponse @loadContentLocalInformation
    contentRateCategory(ContentRateCategoryRequest: ContentRateCategoryRequest): ContentRateCategoryResponse @loadContentRateCategory
    contentQna(ContentQnaRequest: ContentQnaRequest): ContentQnaResponse @loadContentQna
    contentTopics(ContentTopicsRequest: TopicsRequest): ContentTopicsResponse @loadContentTopics
}

extend type ContentNonHotelAccommodation{
    masterRooms:[ContentMasterRoom] @masterRooms
}