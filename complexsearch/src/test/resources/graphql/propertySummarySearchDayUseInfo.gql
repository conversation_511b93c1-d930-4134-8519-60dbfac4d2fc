query propertySummarySearch($PricingSummaryRequest: PricingRequestParameters, $PropertySummarySearchRequest: PropertySummarySearchRequest!){
    propertySummarySearch(PropertySummarySearchRequest: $PropertySummarySearchRequest) {
        properties(PricingSummaryRequest: $PricingSummaryRequest) {
            propertyId
            pricing {
                hotelId
                dayuseInfo {
                  dayuseCheckInTimes {
                    hourOfDay
                    minuteOfHour
                 }
                }
            }
        }
    }
}
