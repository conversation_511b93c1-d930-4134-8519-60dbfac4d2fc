package com.agoda.core.search.framework.binding

import com.agoda.bundler.packaging.{ BundleService, BundleServiceImpl }
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.core.search.framework.bindings.QueryBuilderTest
import com.agoda.core.search.framework.testservices.{
  CMSHelperServiceTest,
  PropertiesFetcherFlowTest,
  SellabilityServiceTest
}
import com.agoda.core.search.mocks._
import com.agoda.core.search.mocks.availabilities._
import com.agoda.core.search.mocks.client.SLServiceTest
import com.agoda.core.search.mocks.search.{ MatrixFlowTest, PostFilterFlowTest, SearchFlowTest }
import com.agoda.papi.search.common.framework.mock._
import com.agoda.papi.search.common.handler.datafetcher.pricestream.PriceStreamFetcherService
import com.agoda.papi.search.common.service._
import com.agoda.papi.search.common.service.cancellationpolicy.{ BNPLService, CancellationPolicyService }
import com.agoda.papi.search.common.util.circuitbreaker.{ NoOpShardCircuitBreaker, ShardLevelCircuitBreakerWrapper }
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import com.agoda.papi.search.complexsearch.availability.service.helper.{
  CityAvailReadRepositoryWrapper,
  CityAvailReadRepositoryWrapperImpl
}
import com.agoda.papi.search.complexsearch.availability.service.pullcas.PullAvailabilityService
import com.agoda.papi.search.complexsearch.availability.service.pushcas.PushAvailabilityService
import com.agoda.papi.search.complexsearch.configuration.FireDrillServiceSettings
import com.agoda.papi.search.complexsearch.detail.histogram.HistogramGenerator
import com.agoda.papi.search.complexsearch.externaldependency.propertyinfolistcache.PropertyInfoListCacheReader
import com.agoda.papi.search.complexsearch.filter.FilteringService
import com.agoda.papi.search.complexsearch.filter.elastic.QueryBuilder
import com.agoda.papi.search.complexsearch.filter.pre.PropertySelectorForFilter
import com.agoda.papi.search.complexsearch.flow._
import com.agoda.papi.search.complexsearch.helper._
import com.agoda.papi.search.complexsearch.matrix.{
  CustomMatrixService,
  ElasticDistanceService,
  ElasticMatrixService,
  FilterMatrixFromElasticSortingServiceTest
}
import com.agoda.papi.search.complexsearch.matrix.derived.{
  PopularFilterService,
  RecommendedFiltersService,
  SegmentFilterService
}
import com.agoda.papi.search.complexsearch.matrix.enrich.MatrixNameService
import com.agoda.papi.search.complexsearch.matrix.sort.{
  FamilyFiltersService,
  FilterMatrixFromElasticSortingService,
  FilterMatrixManipulatingService,
  FilterRankingService
}
import com.agoda.papi.search.complexsearch.paging._
import com.agoda.papi.search.complexsearch.search.{ MatrixFlow, PostFilterFlow, SearchFlow }
import com.agoda.papi.search.complexsearch.service._
import com.agoda.papi.search.complexsearch.service.paging.PageTokenService
import com.agoda.papi.search.complexsearch.sorting.{ HotelDataPreparationFlow, SortingService, SortMatrixService }
import com.agoda.papi.search.complexsearch.util.ElasticFallbackFilterUtil
import com.agoda.papi.search.types.ProviderIdType
import com.agoda.papi.cancellation.CxlPolicyService
import com.agoda.papi.search.common.externaldependency.repository.ProductHotelRepositoryAgsql
import com.agoda.papi.search.complexsearch.PropertySearchServiceLogicHelper
import com.agoda.papi.search.common.service.fs.{ FeatureStoreService, FeatureStoreServiceTest }
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.util.RocketmilesRankingExpUtils
import com.agoda.supplier.common.services.SupplierMetadataService
import com.agoda.supplier.services.{
  PreAllocationExperimentService,
  PushPullStrategyService,
  SupplierExperimentService
}
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import org.mockito.Mockito
import scalacache.caffeine.CaffeineCache
import scaldi.Module

import scala.concurrent.ExecutionContext

private[binding] trait BaseServicesModule extends Module {
  bind[PageTokenService].toProvider(new PageTokenServiceDefaultTest)
  bind[CMSService].toProvider(new CMSServiceTest)
  bind[LocaleService].toProvider(injected[LocaleServiceImpl])
  bind[SLService].toProvider(new SLServiceTest)
  bind[AggregationService].toProvider(new AggregationServiceTest)
  bind[HotelSortAggregationService].toProvider(new HotelSortAggregationServiceTest)
  bind[FilterFlow].toProvider(new FilterFlowTest)
  bind[PropertySelectorForFilter].toProvider(new PropertySelectorForFilterTest)
  bind[PersonalizedPropertiesHelper].toProvider(new PersonalizedPropertiesHelperTest)
  bind[FilteringService].toProvider(new FilteringServiceTest)
  bind[ExchangeDataService].toProvider(new ExchangeDataServiceTest)
  bind[CxlPolicyService] to new CxlPolicyService {}
  bind[CancellationPolicyService].toProvider(new CancellationPolicyServiceTest)
  bind[BNPLService].toProvider(new BNPLServiceTest)
  bind[ElasticMatrixHelper].toProvider(new ElasticMatrixHelperImpl {})
  bind[FilterMatrixFromElasticSortingService].toProvider(new FilterMatrixFromElasticSortingServiceTest())
  bind[MatrixNameService].toProvider(new MatrixNameServiceTest())
  bind[FilterRankingService].toProvider(injected[FilterRankingServiceTest])
  bind[ReviewLocationScoreCmsHelper].toProvider(injected[ReviewLocationScoreCmsHelperTest])
  bind[ReviewScoreCmsHelper].toProvider(injected[ReviewScoreCmsHelperTest])

  bind[FireDrillServiceSettings].toProvider(new FireDrillServiceSettings(true))
  bind[HotelMetaComposer].toProvider(new HotelMetaComposerTest())
  bind[ASQService] to new ASQServiceImpl {}
  bind[MatrixFlow].toProvider(new MatrixFlowTest())
  bind[CustomMatrixService].toProvider(new CustomMatrixServiceTest())
  bind[WhiteLabelClientMultiEnv].toProvider(Mockito.mock(classOf[WhiteLabelClientMultiEnv]))
  bind[WhiteLabelService].toProvider(new WhiteLabelServiceTest())
  bind[NearestLandmarkService].toProvider(new NearestLandmarkServiceTest())
  bind[SegmentFilterService].toProvider(new SegmentFilterServiceTest)
  bind[HotelDataPreparationFlow].toProvider(new HotelDataPreparationTest())
  bind[SortingService].toProvider(new SortingServiceTest())
  bind[SortingFlow].toProvider(new SortingFlowTest())
  bind[SortMatrixService].toProvider(new SortMatrixServiceTest)
  bind[SoldOutPropsPriceService].toProvider(new SoldOutPropsPriceServiceTest)
  bind[ChannelService].toProvider(new ChannelServiceTest)
  bind[CalculateChannelService].toProvider(new CalculateChannelServiceTest)
  bind[SellabilityService].toProvider(new SellabilityServiceTest)
  bind[CoreSearchAgodaHomesService].toProvider(new CoreSearchAgodaHomesServiceTest)
  bind[SoldOutPaging].toProvider(new SoldOutPagingTest)
  bind[ExtraHotelPaging].toProvider(new ExtraHotelPagingTest)
  bind[AgodaHomesPaging].toProvider(new AgodaHomesPagingTest)
  bind[CoreSearchLuxuryHotelsService].toProvider(new CoreSearchLuxuryHotelsServiceTest)
  bind[LuxuryHotelsPaging].toProvider(new LuxuryHotelsPagingTest)
  bind[CoreSearchPulsePropertiesService].toProvider(new CoreSearchPulsePropertiesServiceTest)
  bind[PulsePropertiesPaging].toProvider(injected[PulsePropertiesPagingTest])
  bind[PostFilterService].toProvider(new PostFilterServiceTest)
  bind[SearchFlow].toProvider(new SearchFlowTest())
  bind[PostFilterFlow].toProvider(new PostFilterFlowTest())
  bind[PagingFlow].toProvider(new PagingFlowTest)
  bind[ManualFlow].toProvider(new ManualFlowTest)
  // Details
  bind[HistogramGenerator].toProvider(new HistogramGeneratorTest)
  // Availability
  bind[CityAvailReadRepositoryWrapper].toProvider(injected[CityAvailReadRepositoryWrapperImpl])
  bind[ShardLevelCircuitBreakerWrapper].toProvider(NoOpShardCircuitBreaker)
  bind[PushAvailabilityService].toProvider(new PushShardAvailabilityServiceTest)
  bind[PullAvailabilityService].toProvider(new PullShardAvailabilityServiceTest)
  bind[PushPullStrategyService].toProvider(new PushPullStrategyServiceTest)
  bind[SupplierExperimentService].toProvider(new SupplierExperimentServiceTest)
  bind[PreAllocationExperimentService].toProvider(injected[PreAllocationExperimentServiceTest])
  bind[AvailabilityComposer].toProvider(new AvailabilityComposerTest)
  // Elastic search
  bind[QueryBuilder].toProvider(new QueryBuilderTest)
  bind[ElasticMatrixService].toProvider(new ElasticMatrixServiceTest())
  bind[ElasticFallbackFilterUtil] to new ElasticFallbackFilterUtil
  bind[ElasticDistanceService].toProvider(new ElasticDistanceServiceTest())
  bind[FilterMatrixManipulatingService].toProvider(new FilterMatrixManipulatingServiceTest)

  bind[FamilyFiltersService].toProvider(new FamilyFiltersServiceTest)
  bind[PopularFilterService].toProvider(injected[PopularFilterServiceTest])
  bind[ObjectInfoService].toProvider(new ObjectInfoServiceTest)
  bind[CMSHelperService].toProvider(new CMSHelperServiceTest)
  bind[PropertiesFetcherFlow].toProvider(new PropertiesFetcherFlowTest)
  bind[TealiumService] to TealiumServiceMock
  bind[MetricsReporter] to NoOpMetricsReporter
  bind[BundleService] to new BundleServiceImpl

  bind[PackagingService] to new PackagingServiceImpl {
    override val bundleService: BundleService      = inject[BundleService]
    override val reportingService: MetricsReporter = inject[MetricsReporter]
  }

  bind[PriceStreamFetcherService] to new PriceStreamFetcherServiceDefaultTest
  bind[SupplierMetadataService] to new SupplierMetadataServiceTest
  bind[RecommendedFiltersService] to new RecommendedFiltersServiceTest

  bind[RoomSizeHistogramService] to new RoomSizeHistogramServiceTest

  bind[Option[PiiEncryptionService]].toProvider(None)
  bind[RegulatoryBlockingService].toProvider(new RegulatoryBlockingServiceTest)

  bind[PropertyMetaService] to new PropertyMetaServiceImpl(
    inject[ProductHotelRepositoryAgsql],
    inject[RegulatoryBlockingService]
  )

  bind[PropertyMetadataService] to injected[PropertyMetadataService]

  bind[CityAvailSPCallReportingService] to injected[CityAvailSPCallReportingService]

  bind[RequestSegmentMetricsGenerator] to injected[RequestSegmentMetricsGenerator]

  bind[HeuristicAccuracyReporter] to injected[HeuristicAccuracyReporter]

  bind[RocketmilesRankingExperimentService] to injected[RocketmilesRankingExperimentService]

  bind[PropertySearchServiceLogicHelper] to injected[PropertySearchServiceLogicHelper]

  bind[RocketmilesRankingExpUtils] to injected[RocketmilesRankingExpUtils]

  bind[FeatureStoreService] to new FeatureStoreServiceTest()
}
