package com.agoda.core.search.framework.builders.examples

import com.agoda.content.models.propertycontent.{
  ContentCombinedReviewScore,
  ContentReview,
  ContentReviewResponse,
  ContentReviewScoreResponse,
  ContentReviewTotal
}
import com.agoda.core.search.models._
import com.agoda.core.search.framework.dexsync.{ ElasticHotelInfo, Review }
import Fields._
import agoda.search.models.protobuf.{ Availability, ChannelInfo }
import com.agoda.content.models.OccupancyRequest
import com.agoda.content.models.request.sf.{
  ContentExperienceRequest,
  ContentFeaturesRequest,
  ContentHighlightsRequest,
  ContentImagesRequest,
  ContentInformationRequest,
  ContentInformationSummaryRequest,
  ContentLocalInformationRequest,
  ContentQnaRequest,
  ContentReviewScoreRequest,
  ContentReviewSummariesRequest
}
import com.agoda.core.search.models
import com.agoda.core.search.models.builders.BaseDataParameter
import com.agoda.core.search.models.enumeration.{ AffordableCategories, AgencyBookingTypes }
import com.agoda.papi.search.internalmodel.response._
import com.agoda.core.search.models.request.{ GeoPoint, PolygonCoordinates, SearchHistory }
import com.agoda.core.search.models.response.{
  AvailabilityData,
  BookingDates,
  MatrixResult,
  MatrixResultElement,
  PropertyMetadata,
  ReviewInfo,
  ReviewScoreGrade,
  SupplierInfo
}
import com.agoda.papi.search.common.util.constant.Constants.ExperimentVariant
import com.agoda.papi.search.cacheclient.model.{ AgencyBookingType, AgencyInfo }
import com.agoda.papi.search.types._
import com.agoda.everest.model.SponsoredDetail
import com.agoda.ml.ranking.jarvis.HotelResponse
import com.agoda.papi.enums.hotel.PaymentModel.Merchant
import com.agoda.papi.search.cacheclient.model.PropertyInfo
import com.agoda.papi.search.common.service.CalculateChannelResult
import com.agoda.papi.search.common.service.cancellationpolicy.BNPLInfo
import com.agoda.papi.search.complexsearch.constant.BeachAccessType
import com.agoda.papi.search.complexsearch.model.{ ESPropertyMetaData, PropertySummary }
import com.agoda.papi.search.internalmodel.database.{
  AreaInfo,
  CalculateChannel,
  CdbPropertyInfo,
  ChannelConfig,
  CityCenterInfo,
  CityInfo,
  LandmarkData,
  ProductHotels,
  RegionInfo,
  StateInfo,
  TopAreaInfo
}
import com.agoda.scapi.sqlserver.domain.{ PullCityAvailResponse, PullReadAvailableResult, PushCityAvailResponse }
import com.agoda.whitelabel.client.settings.Constants.WhiteLabelId
import org.joda.time.DateTime
import com.agoda.papi.search.cacheclient.model.FireDrillCommission
import com.agoda.papi.search.internalmodel.growthprogram.{ GrowthProgramInfoRequest, PAPIGrowthProgramInfo }
import com.agoda.papi.search.internalmodel.promocode.BannerCampaignRequest
import com.agoda.papi.search.internalmodel.request.{ PriceStreamMetaLabRequest, PropertyDetailsRequest }

import scala.collection.mutable

//non-request defaults goes here

private[builders] trait DefaultDataExample extends BaseDataParameter {

  import com.agoda.core.search.framework.dexsync.HotelDataTypes.BedroomConfigurations

  // public
  lazy val aValidCalculateChannelResult: CalculateChannelResult = CalculateChannelResult(List(1))
  lazy val aValidAvailabilityData: AvailabilityData             = AvailabilityData(aValidHotelId, isAvailable = true)

  lazy val aValidDefaultRankingScores: Map[HotelIdType, Double] = Map(
    10001L -> 3046000,
    10006L -> 3008000,
    10011L -> 3006000,
    10024L -> 3034000,
    10034L -> 3033000,
    10050L -> 3023000,
    10056L -> 3035000,
    10346L -> 3039000,
    20013L -> 3043000,
    20068L -> 3024000
  )

  lazy val aValidJarvisHotelResponse = HotelResponse(10001L, 0L)

  lazy val aValidDefaultPropertyMetadata = response.PropertyMetadata(
    10001L,
    0.0,
    aValidCityId,
    106,
    "TH",
    List.empty,
    5.0,
    aValidDefaultRankingScores(10001L),
    commission = 2.0,
    providerIdList = Set(aValidSupplierId),
    commissionEquivalent = Some(21.0f)
  )

  lazy val aValidDefaultPropertyMetadataList = WithHotelIdMap[PropertyMetadata](
    (
      10001L,
      response.PropertyMetadata(
        10001L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10001L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10006L,
      response.PropertyMetadata(
        10006L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10006L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10011L,
      response.PropertyMetadata(
        10011L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10011L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10034L,
      response.PropertyMetadata(
        10034L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10034L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10024L,
      response.PropertyMetadata(
        10024L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10024L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      20068L,
      response.PropertyMetadata(
        20068L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(20068L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10050L,
      response.PropertyMetadata(
        10050L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10050L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      20013L,
      response.PropertyMetadata(
        20013L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(20013L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10056L,
      PropertyMetadata(
        10056L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10056L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    ),
    (
      10346L,
      response.PropertyMetadata(
        10346L,
        0.0,
        aValidCityId,
        106,
        "TH",
        List.empty,
        5.0,
        aValidDefaultRankingScores(10346L),
        commission = 1.0,
        providerIdList = Set(332),
        commissionEquivalent = Some(21.0f)
      )
    )
  )

  lazy val aValidAgencySupplier             = 3038
  lazy val aValidMerchantSupplier           = 332
  lazy val aValidBNPLDisabledSupplier       = 27904
  lazy val aValidMerchantCommissionSupplier = 333

  lazy val bookNowPayLaterChargeBuffer  = "BNPLCB"
  lazy val bookNowPayLaterDisplayBuffer = "BNPLDB"
  lazy val bookNowPayLaterSettleDate    = "BNPLAD"

  lazy val aValidSupplierInfo = SupplierInfo(
    supplierId = 332,
    benefitIDs = List(1),
    paymentModel = Merchant,
    cancellationCodes = List.empty,
    fromChannelInfo = true
  )

  lazy val aValidCityInfo: CityInfo = CityInfo(
    cityId = aValidCityId,
    latLon = (13.737397, 100.560793),
    hotelIds = List(aValidHotelId),
    radius = Some(50),
    stateId = 1,
    countryId = 106
  )

  lazy val aValidAreaInfo: AreaInfo = AreaInfo(aValidAreaId, aValidCityId, 13.724711, 100.577087, 0, 0, List.empty)

  lazy val aValidTopAreaInfo: TopAreaInfo = TopAreaInfo(aValidTopAreaId, GeoPoint(13.724711, 100.577087), List.empty)

  lazy val aValidCityCenterInfo: CityCenterInfo =
    CityCenterInfo(aValidCityCenterId, GeoPoint(13.724711, 100.577087), List.empty)

  lazy val aValidJapanStateInfo: StateInfo =
    StateInfo(aValidJapanStateId, GeoPoint(13.724711, 100.577087), aValidJapanCountryId, List(aValidCityId), List.empty)
  lazy val aValidStateInfo: StateInfo =
    StateInfo(aValidStateId, GeoPoint(13.724711, 100.577087), aValidCountryId, List(aValidCityId), List.empty)

  lazy val aValidBNPLInfo: BNPLInfo = BNPLInfo(true, aValidCheckIn, aValidCheckIn, false)

  lazy val aValidCdbPropertyInfo: CdbPropertyInfo = CdbPropertyInfo(
    hotelId = aValidHotelId,
    starRating = 3,
    areaId = 5,
    cityId = aValidCityId,
    countryId = 10,
    countryCode = "TH",
    accommodationTypes = List.empty,
    rankingScore = 1000,
    gmtOffset = 7
  )

  lazy val aValidCachePropertyInfo: PropertyInfo = PropertyInfo(
    hotelId = aValidHotelId,
    starRating = 3,
    areaId = 5,
    cityId = aValidCityId,
    countryId = 10,
    countryCode = "TH",
    accommodationTypes = Seq.empty,
    rankingScore = 1000,
    gmtOffset = 7,
    contentServable = true,
    isGenericCreditCardRequired = true,
    isDomesticCreditCardRequired = true
  )

  val aValidfireDrillCommission: FireDrillCommission = FireDrillCommission(
    hotelId = 111L,
    contractId = 0,
    effectiveDate = "2017-01-01",
    periodStart = "2017-04-01",
    periodEnd = DateTime.now.plusDays(2).toString("yyyy-MM-dd"),
    commission = 35,
    isInvoice = false,
    invoiceAmount = None,
    commissionEquivalent = None
  )

  lazy val aValidLandmarkData: LandmarkData = LandmarkData(1, cityId = 1, lat = 0d, lon = 0d, areaId = 1)

  lazy val aValidRegionInfoData: RegionInfo = RegionInfo(
    regionId = aValidRegionId,
    GeoPoint(0d, 0d),
    boundaryCoordinates =
      PolygonCoordinates(List(GeoPoint(1d, 1d), GeoPoint(1d, 0d), GeoPoint(0d, 1d), GeoPoint(0d, 0d)))
  )

  lazy val aValidIdPromotionEligible: Boolean = true

  lazy val aValidPropertySummary: PropertySummary =
    PropertySummary(hotelId = aValidHotelId, ranking = 3016000, cheapestPrice = Some(100), distance = Some(3.04))

  val aValidESMetadata = ESMetaData(10)

  val aValidPropertyData = models.PropertyData(
    aValidHotelId,
    aValidDefaultPropertyMetadata,
    Some(aValidAvailabilityData),
    Some(aValidESMetadata),
    Some(SponsoredDetail(3016000))
  )

  lazy val aValidESPropertyMetaData: ESPropertyMetaData = ESPropertyMetaData(aValidHotelId, 10, -1, Some(aValidCityId))

  lazy val aValidCalculateChannel: CalculateChannel = CalculateChannel(None, None)

  lazy val aValidElasticReview = Review(0, 332, 8, 8, 10)

  // A variant documents
  lazy val aValidElasticHotelInfo = new ElasticHotelInfo(
    id = aValidHotelId,
    hotelId = aValidHotelId,
    experimentVariant = ExperimentVariant.A_VARIANT,
    whiteLabelId = WhiteLabelId.AGODA,
    hotelName = "Test Hotel",
    starRating = 5,
    opsStarRating = 4,
    opsStarRatingWithLuxury = List(4),
    opsStarRatingWithLuxuryNewDef = List(4),
    starRatingWithLuxury = List(5),
    accommodationType = List(34),
    productType = List(1),
    defaultDmcId = aValidSupplierId,
    hotelCityId = aValidCityId,
    hotelCountryId = aValidCountryId,
    hotelChainId = 0,
    hotelBrandId = 0,
    hotelAreaId = aValidAreaId,
    gmtOffset = 7,
    rankingScoresA = 2625286d,
    rankingScoresB = 2625286d,
    location = "13.750324249267578,100.54206848144531",
    review = List(aValidElasticReview),
    hotelFacilities = List.empty,
    airportLandmarkIds = List.empty,
    airportLandmarkDistances = List.empty,
    beachAccessCategoryIds = List(2000),
    beachAccessTypeIds = List.empty,
    numberOfBedrooms = Vector(1),
    landmarkIds = List.empty,
    landmarkTypeIds = List.empty,
    landmarkSubTypeIds = List.empty,
    landmarkSubTypeCategoryIds = List.empty,
    landmarkDistances = List.empty,
    transportLandmarkIds = List.empty,
    bookOnRequest = true,
    isLocatedInTopRatedArea = 1,
    roomAmenities = Set.empty,
    freeChildAges = List.empty,
    affordableCategory = 1,
    nearestDistanceToCityCenterPolygon = 10,
    affordableCategoryB = 1,
    atmosphereIds = List.empty,
    allGuestCleanlinessRating = 5.0,
    allGuestFacilitiesRating = 5.0,
    allGuestComfortRating = 5.0,
    allGuestStaffRating = 5.0,
    allGuestValueRating = 5.0,
    allGuestLocationRating = 5.0,
    isLocatedInCityCenter = 0,
    isChildrenStayFree = 0,
    newlyBuiltOrRenovated = -1,
    metroSubwayStationLandmarkIds = List.empty,
    busStationLandmarkIds = List.empty,
    trainStationLandmarkIds = List.empty,
    facilityClassPositiveMentions = List.empty,
    maxRoomSizeSqm = 50.0,
    beachAccessCategoryIds_b = Nil,
    travellerChoiceAward = 0,
    hostLevel = 0,
    characteristics = List.empty,
    familyFacilities = List.empty,
    themeIds = List.empty,
    noOfBedrooms = Vector.empty,
    tripPurpose = List.empty,
    luxuryPropertyTypes = Seq.empty,
    storeIds = List.empty
  )

  lazy val aValidFriendsBookingDate =
    BookingDates(new DateTime("2018-03-16T00:00:00.000+07:00"), new DateTime("2018-03-17T00:00:00.000+07:00"))
  lazy val aValidreviewScoreGrade = ReviewScoreGrade("10011", "Mock review", 7.0, Some(8.0), "SomeScore", Some(4))

  lazy val aValidFriendReviewInfo = ReviewInfo(
    "",
    new DateTime("2017-02-12T00:00:00.0000+07:00"),
    new DateTime("2017-02-12T00:00:00.0000+07:00"),
    List(aValidreviewScoreGrade)
  )

  lazy val aValidSearchHistory = SearchHistory(
    searchDate = Some("2018-09-11"),
    searchType = None,
    numberOfAdults = Some(1),
    numberOfChildren = Some(2),
    numberOfRooms = Some(3)
  )

  lazy val aValidRatingMatrixResult1 = Seq(
    MatrixResultElement("", 3, Option(315), Some(HOTELRATING)),
    MatrixResultElement("", 4, Option(274), Some(HOTELRATING)),
    MatrixResultElement("", 5, Option(141), Some(HOTELRATING)),
    MatrixResultElement("", 2, Option(136), Some(HOTELRATING)),
    MatrixResultElement("", 1, Option(86), Some(HOTELRATING)),
    MatrixResultElement("", 0, Option(2), Some(HOTELRATING)),
    MatrixResultElement("", 3.5, Option(2), Some(HOTELRATING)),
    MatrixResultElement("", 4.5, Option(2), Some(HOTELRATING))
  )

  lazy val aValidRatingMatrixResult2 = Seq(
    MatrixResultElement("", 3, Option(690), Some(HOTELRATING)),
    MatrixResultElement("", 2, Option(358), Some(HOTELRATING)),
    MatrixResultElement("", 4, Option(284), Some(HOTELRATING)),
    MatrixResultElement("", 0, Option(257), Some(HOTELRATING)),
    MatrixResultElement("", 2.5, Option(221), Some(HOTELRATING)),
    MatrixResultElement("", 3.5, Option(158), Some(HOTELRATING)),
    MatrixResultElement("", 5, Option(103), Some(HOTELRATING)),
    MatrixResultElement("", 1.5, Option(50), Some(HOTELRATING)),
    MatrixResultElement("", 1, Option(39), Some(HOTELRATING)),
    MatrixResultElement("", 4.5, Option(32), Some(HOTELRATING))
  )

  lazy val aValidFacilityMatrixResult = Seq(
    MatrixResultElement("Swimming Pool", 93, Option(642), Some(HOTELFACILITIES)),
    MatrixResultElement("Internet", 90, Option(987), Some(HOTELFACILITIES)),
    MatrixResultElement("Gym/Fitness", 92, Option(607), Some(HOTELFACILITIES)),
    MatrixResultElement("Car Park", 80, Option(1418), Some(HOTELFACILITIES)),
    MatrixResultElement("Spa/Sauna", 91, Option(342), Some(HOTELFACILITIES)),
    MatrixResultElement("Business Facilities", 88, Option(792), Some(HOTELFACILITIES)),
    MatrixResultElement("Family/Child Friendly", 89, Option(889), Some(HOTELFACILITIES)),
    MatrixResultElement("Restaurant", 11, Option(1176), Some(HOTELFACILITIES)),
    MatrixResultElement("Non Smoking Rooms", 15, Option(765), Some(HOTELFACILITIES)),
    MatrixResultElement("Smoking Area", 83, Option(1354), Some(HOTELFACILITIES)),
    MatrixResultElement("Airport Transfer", 17, Option(701), Some(HOTELFACILITIES)),
    MatrixResultElement("Pets Allowed", 24, Option(60), Some(HOTELFACILITIES)),
    MatrixResultElement("Facilities For Disabled Guests", 16, Option(256), Some(HOTELFACILITIES)),
    MatrixResultElement("Golf Course (on Site)", 56, Option(7), Some(HOTELFACILITIES)),
    MatrixResultElement("Nightclub", 10, Option(83), Some(HOTELFACILITIES))
  )

  lazy val aValidReviewLocationScoreMatrixResult = Seq(
    MatrixResultElement("Exceptional", 9, Option(279), Some(REVIEWLOCATIONSCORE)),
    MatrixResultElement("Excellent", 8, Option(1137), Some(REVIEWLOCATIONSCORE)),
    MatrixResultElement("Very good", 7, Option(1942), Some(REVIEWLOCATIONSCORE))
  )

  lazy val aValidBangkokAreaMatrixResult = Seq(
    MatrixResultElement("Sukhumvit", 27713, Option(532), Some(HOTELAREA)),
    MatrixResultElement("Siam", 378468, Option(175), Some(HOTELAREA)),
    MatrixResultElement("Ratchadaphisek", 26023, Option(174), Some(HOTELAREA)),
    MatrixResultElement("Don Mueang International Airport", 20552, Option(168), Some(HOTELAREA)),
    MatrixResultElement("Khaosan", 25983, Option(159), Some(HOTELAREA)),
    MatrixResultElement("Pratunam", 26026, Option(135), Some(HOTELAREA)),
    MatrixResultElement("Suvarnabhumi Airport", 27717, Option(123), Some(HOTELAREA)),
    MatrixResultElement("Silom", 27715, Option(121), Some(HOTELAREA)),
    MatrixResultElement("Sathorn", 514610, Option(109), Some(HOTELAREA)),
    MatrixResultElement("Chatuchak", 27718, Option(98), Some(HOTELAREA)),
    MatrixResultElement("Bang Kapi", 514611, Option(90), Some(HOTELAREA)),
    MatrixResultElement("Bang Na", 514609, Option(77), Some(HOTELAREA)),
    MatrixResultElement("Bangkok Riverside", 31432, Option(75), Some(HOTELAREA)),
    MatrixResultElement("Thonburi", 34383, Option(70), Some(HOTELAREA)),
    MatrixResultElement("Wireless", 27714, Option(34), Some(HOTELAREA)),
    MatrixResultElement("China Town", 36662, Option(25), Some(HOTELAREA)),
    MatrixResultElement("South Thonburi", 481624, Option(23), Some(HOTELAREA)),
    MatrixResultElement("Nonthaburi City Center", 47669, Option(3), Some(HOTELAREA)),
    MatrixResultElement("Bangyai", 27732, Option(1), Some(HOTELAREA))
  )

  lazy val aValidBaliAreaMatrixResult = Seq(
    MatrixResultElement("Ubud", 26638, Option(193), Some(HOTELAREA)),
    MatrixResultElement("Seminyak", 27988, Option(178), Some(HOTELAREA)),
    MatrixResultElement("Kuta", 34423, Option(92), Some(HOTELAREA)),
    MatrixResultElement("Sanur", 26634, Option(81), Some(HOTELAREA)),
    MatrixResultElement("Legian", 26631, Option(59), Some(HOTELAREA)),
    MatrixResultElement("Nusa Dua", 26633, Option(36), Some(HOTELAREA)),
    MatrixResultElement("Lovina Beach", 31064, Option(36), Some(HOTELAREA)),
    MatrixResultElement("Uluwatu", 31977, Option(34), Some(HOTELAREA)),
    MatrixResultElement("Canggu", 57750, Option(29), Some(HOTELAREA)),
    MatrixResultElement("Nusa Lembongan", 31063, Option(28), Some(HOTELAREA)),
    MatrixResultElement("Jimbaran", 34110, Option(27), Some(HOTELAREA)),
    MatrixResultElement("Candidasa", 26629, Option(26), Some(HOTELAREA)),
    MatrixResultElement("Denpasar", 31530, Option(26), Some(HOTELAREA)),
    MatrixResultElement("Amed", 31483, Option(19), Some(HOTELAREA)),
    MatrixResultElement("Pemuteran", 34411, Option(13), Some(HOTELAREA)),
    MatrixResultElement("Tabanan", 26636, Option(12), Some(HOTELAREA)),
    MatrixResultElement("Kintamani", 34412, Option(11), Some(HOTELAREA)),
    MatrixResultElement("Tulamben", 34413, Option(11), Some(HOTELAREA)),
    MatrixResultElement("Munduk", 483006, Option(10), Some(HOTELAREA)),
    MatrixResultElement("Keramas", 494181, Option(8), Some(HOTELAREA)),
    MatrixResultElement("Singaraja", 69962, Option(7), Some(HOTELAREA)),
    MatrixResultElement("Tanah Lot", 93996, Option(6), Some(HOTELAREA)),
    MatrixResultElement("Seririt", 34414, Option(5), Some(HOTELAREA)),
    MatrixResultElement("Pekutatan", 94044, Option(4), Some(HOTELAREA)),
    MatrixResultElement("Bedugul", 94662, Option(3), Some(HOTELAREA)),
    MatrixResultElement("Negara", 34410, Option(2), Some(HOTELAREA)),
    MatrixResultElement("Nusa Penida", 493873, Option(1), Some(HOTELAREA)),
    MatrixResultElement("Nusa Ceningan", 514895, Option(1), Some(HOTELAREA))
  )

  lazy val aValidAccommodationMatrixResult = Seq(
    MatrixResultElement("Hotel", 34, Option(1183), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Serviced Apartment", 120, Option(335), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Hostel", 33, Option(275), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Guest House / Bed & Breakfast", 32, Option(217), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Apartment", 29, Option(129), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Resort", 37, Option(27), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Motel", 35, Option(13), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Villa", 30, Option(5), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Love Hotel", 113, Option(3), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("House", 131, Option(3), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Ryokan", 38, Option(1), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Capsule Hotel", 105, Option(1), Some(ACCOMMODATIONTYPE))
  )

  lazy val aValidAccommodationTypeMatrixResult = Seq(
    MatrixResultElement("Resort", 37, Option(548), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Hotel", 34, Option(173), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Guest House / Bed & Breakfast", 32, Option(101), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Villa", 30, Option(97), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Bungalow", 103, Option(32), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Holiday Park / Caravan Park", 110, Option(3), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Serviced Apartment", 120, Option(3), Some(ACCOMMODATIONTYPE)),
    MatrixResultElement("Apartment", 29, Option(1), Some(ACCOMMODATIONTYPE))
  )

  lazy val aValidRoomAmenityMatrixResult = Seq(
    MatrixResultElement("", 342, Some(1), Some(ROOMAMENITIES)),
    MatrixResultElement("Coffee/ Tea maker", 47, Some(1), Some(ROOMAMENITIES)),
    MatrixResultElement("Heating", 143, Some(1), Some(ROOMAMENITIES)),
    MatrixResultElement("Bathtub", 37, Some(1), Some(ROOMAMENITIES))
  )

  lazy val aValidBeachAccessTypeMatrixResult = Seq(
    MatrixResultElement("Public Beach", BeachAccessType.PublicBeach2Km, Option(10), Some(BEACHACCESSTYPE)),
    MatrixResultElement("Private Beach", BeachAccessType.PrivateBeach, Option(1), Some(BEACHACCESSTYPE))
  )

  lazy val aValidLandmarkSubTypeMatrixResult = Seq(
    MatrixResultElement("Metro/Subway station", 4, Option(20), Some(LANDMARKINFOSUBTYPECATEGORY)),
    MatrixResultElement("BTS Station", 5, Option(1), Some(LANDMARKINFOSUBTYPECATEGORY))
  )

  lazy val aValidAffordableCategoryMatrixResult = Seq(
    MatrixResultElement("Budget", AffordableCategories.Budget.i, Option(22), Some(AFFORDABLECATEGORY)),
    MatrixResultElement("Luxury", AffordableCategories.Luxury.i, Option(50), Some(AFFORDABLECATEGORY))
  )

  lazy val aValidLandMarkIdMatrixResult = Seq(
    MatrixResultElement(s"LandmarkName 1", 1, Some(6), Some(LANDMARKID)),
    MatrixResultElement(s"LandmarkName 2", 2, Some(14), Some(LANDMARKID))
  )

  lazy val aValidPaymentModeMatrixResult = Seq(
    MatrixResultElement("Pay at the hotel", CMSMapping.PayAtHotelMatrix, Option(21), Some(PAYMENTMODE))
  )

  lazy val aValidRoomBenefitMatrixResult = Seq(
    MatrixResultElement("Breakfast included", CMSMapping.BreakfastIncludedMatrix, Option(10), Some(ROOM_BENEFITS))
  )

  lazy val aValidStarRatingMatrixResult = Seq(
    MatrixResultElement("4-star and 4.5-star hotels", 4, Option(1), Some(STARRATING))
  )

  lazy val aValidStarRatingWithLuxuryMatrixResult = Seq(
    MatrixResultElement("", 4, Option(1), Some(STARRATINGWITHLUXURY)),
    MatrixResultElement("Best in luxury", 101, Option(1), Some(STARRATINGWITHLUXURY))
  )

  lazy val aValidReviewScoreMatrixResult = Seq(
    MatrixResultElement("Excellent", 9, Option(0), Some(REVIEWSCORE)),
    MatrixResultElement("Exceptional", 8, Option(1), Some(REVIEWSCORE)),
    MatrixResultElement("Very Good", 7, Option(1), Some(REVIEWSCORE)),
    MatrixResultElement("Good", 6, Option(0), Some(REVIEWSCORE))
  )

  lazy val aValidGuestReviewBreakdownMatrixResult = Seq(
    MatrixResultElement("Cleanliness", 8, Option(10), Some(ALLGUESTCLEANLINESSRATING)),
    MatrixResultElement("Facilities", 8, Option(20), Some(ALLGUESTFACILITIESRATING)),
    MatrixResultElement("Comfort", 8, Option(30), Some(ALLGUESTCOMFORTRATING)),
    MatrixResultElement("Staff", 8, Option(40), Some(ALLGUESTSTAFFRATING)),
    MatrixResultElement("Value", 8, Option(50), Some(ALLGUESTVALUERATING)),
    MatrixResultElement("Guest location", 8, Option(60), Some(ALLGUESTLOCATIONRATING))
  )

  lazy val aValidMatrixResult = Some(
    MatrixResult(
      mutable.Map(
        HOTELFACILITIES             -> aValidFacilityMatrixResult,
        HOTELAREA                   -> aValidBaliAreaMatrixResult,
        ACCOMMODATIONTYPE           -> aValidAccommodationTypeMatrixResult,
        BEACHACCESSTYPE             -> aValidBeachAccessTypeMatrixResult,
        LANDMARKINFOSUBTYPECATEGORY -> aValidLandmarkSubTypeMatrixResult,
        HOTELFACILITIES             -> aValidFacilityMatrixResult,
        AFFORDABLECATEGORY          -> aValidAffordableCategoryMatrixResult,
        ROOMAMENITIES               -> aValidRoomAmenityMatrixResult,
        LANDMARKID                  -> aValidLandMarkIdMatrixResult,
        PAYMENTMODE                 -> aValidPaymentModeMatrixResult,
        ROOM_BENEFITS               -> aValidRoomBenefitMatrixResult,
        STARRATING                  -> aValidStarRatingMatrixResult,
        REVIEWSCORE                 -> aValidReviewScoreMatrixResult,
        REVIEWLOCATIONSCORE         -> aValidReviewLocationScoreMatrixResult
      )
    )
  )

  lazy val anEmptyMatrixResult = Some(
    response.MatrixResult(
      mutable.Map(
        HOTELFACILITIES             -> Seq.empty,
        HOTELAREA                   -> Seq.empty,
        ACCOMMODATIONTYPE           -> Seq.empty,
        BEACHACCESSTYPE             -> Seq.empty,
        LANDMARKINFOSUBTYPECATEGORY -> Seq.empty,
        HOTELFACILITIES             -> Seq.empty,
        AFFORDABLECATEGORY          -> Seq.empty,
        ROOMAMENITIES               -> Seq.empty,
        LANDMARKID                  -> Seq.empty,
        PAYMENTMODE                 -> Seq.empty,
        ROOM_BENEFITS               -> Seq.empty
      )
    )
  )

  lazy val aValidProductHotels = ProductHotels(
    hotelId = aValidHotelId,
    countryId = aValidCountryId,
    cityId = aValidCityId,
    areaId = aValidAreaId,
    gmtOffset = aValidGmtOffset,
    cityGmtOffset = aValidGmtOffset,
    cityGmtOffsetMinutes = aValidGmtOffsetMinutes,
    activeHotels = None
  )

  lazy val aValidChannelConfig =
    ChannelConfig(aValidPlatformId, aValidCountryId.toInt, aValidLanguageId, aValidOrigin, "1", 123)

  lazy val aValidChannelInfoProto = new ChannelInfo(1)
    .withRegularCutoff(1)
    .withCheapestPrice(2000)
    .withRegularAllotments(1)
    .withRemainingRooms(10)
    .withMaxOccupancy(10)
    .withMaxPersons(10)
    .clearMaxAdvancePurchase
    .withBenefits(mutable.Seq(1, 4, 5))
    .withCancellationCodes(Seq("5D1N", "1D100P"))
    .withGuaranteeAllotments(1)
    .clearGuaranteeCutoff

  lazy val aValidAvailabilityProto = new Availability()
    .withID(aValidHotelId.toInt)
    .withSupplierID(aValidSupplierId)
    .withChannelInfo(Seq(aValidChannelInfoProto))

  lazy val aValidPushCityAvailResponse = PushCityAvailResponse(
    aValidHotelId.toInt,
    aValidSupplierId,
    Some(1),
    2000,
    Some(1),
    Some(10),
    Some(10),
    Some(10),
    None,
    Some("1,4,5"),
    Some("5D1N,1D100P"),
    Some(1),
    None,
    None,
    Some(1),
    Some("1"),
    false,
    availData = Some(aValidAvailabilityProto.toByteArray)
  )

  lazy val aValidPullCityAvailResponse = PullCityAvailResponse(
    aValidHotelId.toInt,
    aValidPullSupplierId,
    "priceversion",
    Some(1),
    2000,
    Some(1),
    Some(10),
    Some(10),
    Some(10),
    None,
    Some("1,4,5"),
    Some("5D1N,1D100P"),
    Some(1),
    None,
    None,
    Some(1),
    Some("1"),
    false,
    DateTime.now().plusYears(1),
    DateTime.now(),
    availData = Some(aValidAvailabilityProto.toByteArray)
  )

  lazy val aValidChannelConfigUS = ChannelConfig(0, 10, aValidLanguageId, "US", "1", 123)

  val aValidContentReviewTotal = ContentReviewTotal(reviewCount = 600, score = 9, maxScore = 10)
  val aValidContentReview      = ContentReview(ProviderIds.Ycs, true, None, None, Some(aValidContentReviewTotal), None)
  val aValidContentReviewResponse = ContentReviewResponse(List(aValidContentReview), None, None)

  val aValidAgencyInfo = AgencyInfo(support = false, eligible = false, bookingType = AgencyBookingType.DomesticBooking)

  val aValidPropertyDetailsRequest    = PropertyDetailsRequest(List(1L))
  val aValidContentReviewScoreRequest = ContentReviewScoreRequest(None, None)
  val aValidContentImageRequest       = ContentImagesRequest(None, None, Some(1L))
  val aValidContentReviewSummariesRequest =
    ContentReviewSummariesRequest(Some(List(3038, 332)), None, None, None, None, None, None)
  val aValidContentInformationSummaryRequest = ContentInformationSummaryRequest()
  val aValidContentInformationRequest        = ContentInformationRequest(None, None, None, None, None, None)
  val aValidContentLocalInformationRequest   = ContentLocalInformationRequest()
  val aValidContentExperienceRequest         = ContentExperienceRequest()
  val aValidContentFeaturesRequest =
    ContentFeaturesRequest(includeMissing = Some(false), occupancyRequest = Some(OccupancyRequest(2, 1)))
  val aValidContentHighlightsRequest =
    ContentHighlightsRequest(None, None, None, None, includeAtfPropertyHighlights = Some(true))
  val aValidContentQnaRequest         = ContentQnaRequest(Vector(1), None)
  val aValidPriceStreamMetaLabRequest = PriceStreamMetaLabRequest()
  val aValidBannerCampaignRequest     = BannerCampaignRequest(DateTime.now())

  val aValidContentReviewScoreResponse = ContentReviewScoreResponse(1L, None, None, None)
  val aValidContentCombinedReviewScore = ContentCombinedReviewScore(None, None, None)

  val aValidGrowthProgramInfoRequest = GrowthProgramInfoRequest(
    bookingDate = new DateTime("2024-08-07T08:12:13.222+07:00"),
    checkInDate = new DateTime("2024-12-10T09:00:45.666+07:00"),
    checkOutDate = new DateTime("2024-12-11T09:00:45.666+07:00"),
    lengthOfStay = 1,
    rooms = 1,
    adults = 2,
    children = 0
  )

  val aValidGrowthProgramInfoNoBadges = PAPIGrowthProgramInfo(
    badges = List()
  )

  val aValidGrowthProgramInfoWithBadges = PAPIGrowthProgramInfo(
    badges = List("AGODA_PREFERRED")
  )

}
