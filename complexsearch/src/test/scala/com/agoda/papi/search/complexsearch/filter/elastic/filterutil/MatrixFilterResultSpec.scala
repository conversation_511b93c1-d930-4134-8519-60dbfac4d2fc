package com.agoda.papi.search.complexsearch.filter.elastic.filterutil

import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.builders.RequestBuilder
import com.agoda.core.search.models.{ request, Fields }
import com.agoda.papi.search.internalmodel.enumeration.FeatureFlagKey
import com.agoda.core.search.models.request._
import com.sksamuel.elastic4s.testkit.{ ClientProvider, DockerTests, ElasticSugar }
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.time.{ Seconds, Span }
import org.scalatest.BeforeAndAfterAll

import scala.collection.mutable
import com.agoda.core.search.models.Fields._
import com.agoda.core.search.models.request.{ CitySearchRequest, FeatureFlagRequest, MatrixField }
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.complexsearch.filter.elastic.{ FilterMatrixSortSettings, TestElasticService }
import com.agoda.papi.search.complexsearch.util.elasticsearch.EmbeddedElasticService
import com.agoda.papi.search.complexsearch.util.elasticsearch.EmbeddedElasticService.getClient
import com.sksamuel.elastic4s.fields.{ DoubleField, GeoPointField, LongField, NestedField }
import org.scalatest.funsuite.AnyFunSuite
import org.scalatest.matchers.should.Matchers
import org.scalatestplus.mockito.MockitoSugar

class MatrixFilterResultSpec
    extends AnyFunSuite with EmbeddedElasticService with BeforeAndAfterAll with ElasticSugar with ClientProvider
      with ScalaFutures with MockitoSugar with Matchers {

  override lazy val client = elasticsearchClient

  implicit val defaultPatience = PatienceConfig(timeout = Span(2, Seconds))

  val testIndex   = "hotels"
  val testIndexB  = "bhotels"
  val testtype    = "hotelstype"
  val cityId      = 9395
  val otherCityId = 8691
  val hotelIds    = Array[Long](461790, 10637, 10809, 286830, 293442, 13579)
  lazy val elasticService =
    new TestElasticService(testIndex, testIndexB, testtype, getClient)
  implicit val contextHolder  = RequestBuilder.testContextHolder
  val landmarkOrder           = Seq.empty
  val hotelChainOrder         = Seq.empty
  val fieldOrder              = FilterFieldOrder(landmarkIds = landmarkOrder, chainIds = hotelChainOrder)
  val filterMatrixSortSetting = FilterMatrixSortSettings.apply()

  // scalastyle:off
  override def beforeAll(): Unit = {
    setup()
    getClient.execute {
      createIndex(testIndex).mapping(
        properties(
          GeoPointField("location"),
          NestedField("review", enabled = Some(true)),
          DoubleField(CITYCENTERDISTANCE).stored(true),
          LongField(Fields.HOTELID, store = Some(true))
        )
      )
    }.await

    getClient.execute {
      bulk(
        indexInto(testIndex).fields(
          "id"                -> hotelIds(0),
          "hotelId"           -> hotelIds(0),
          "experimentVariant" -> 1,
          "whiteLabelId"      -> 1,
          "hotelName"         -> "The Berkeley Hotel Pratunam",
          "starRating"        -> 4.5,
          "hotelCityId"       -> cityId,
          "hotelCountryId"    -> 106,
          "hotelChainId"      -> 0,
          "hotelBrandId"      -> 0,
          "hotelAreaId"       -> 26026,
          "gmtOffset"         -> 7,
          "rankingScoresA"    -> 2625286,
          "rankingScoresB"    -> 2625286,
          "location"          -> "13.750324249267578,100.54206848144531",
          "review"            -> Array(Map("providerId" -> 332, "score" -> 5.889)),
          "hotelFacilities"   -> Array(88, 64, 281, 18, 16, 143, 36, 323),
          "bookOnRequest"     -> false
        ),
        indexInto(testIndex).fields(
          "id"                -> hotelIds(1),
          "hotelId"           -> hotelIds(1),
          "experimentVariant" -> 1,
          "whiteLabelId"      -> 1,
          "hotelName"         -> "Baiyoke Sky Hotel",
          "starRating"        -> 5.0,
          "hotelCityId"       -> cityId,
          "hotelCountryId"    -> 106,
          "hotelChainId"      -> 0,
          "hotelBrandId"      -> 0,
          "hotelAreaId"       -> 26026,
          "gmtOffset"         -> 7,
          "rankingScoresA"    -> 1048728,
          "rankingScoresB"    -> 1048728,
          "location"          -> "13.7545978763281,100.540308952332",
          "review" -> Array(Map("providerId" -> 128, "score" -> 4.174), Map("providerId" -> 128, "score" -> 4.48)),
          "hotelFacilities" -> Array(323, 121),
          "bookOnRequest"   -> false
        ),
        indexInto(testIndex).fields(
          "id"                -> hotelIds(2),
          "hotelId"           -> hotelIds(2),
          "experimentVariant" -> 1,
          "whiteLabelId"      -> 1,
          "hotelName"         -> "XYZ Hotel",
          "starRating"        -> 3.0,
          "hotelCityId"       -> cityId,
          "hotelCountryId"    -> 106,
          "hotelChainId"      -> 0,
          "hotelBrandId"      -> 0,
          "hotelAreaId"       -> 26026,
          "gmtOffset"         -> 7,
          "rankingScoresA"    -> 90834,
          "rankingScoresB"    -> 90834,
          "location"          -> "13.9445978763281,100.830308952332",
          "review" -> Array(Map("providerId" -> 332, "score" -> 5.174), Map("providerId" -> 332, "score" -> 9.48)),
          "hotelFacilities" -> Array(323, 88),
          "bookOnRequest"   -> false
        ),
        indexInto(testIndex).fields(
          "id"                -> hotelIds(3),
          "hotelId"           -> hotelIds(3),
          "experimentVariant" -> 1,
          "whiteLabelId"      -> 1,
          "hotelName"         -> "Grande Centre Point Hotel Terminal 21",
          "starRating"        -> 5.0,
          "hotelCityId"       -> cityId,
          "hotelCountryId"    -> 106,
          "hotelChainId"      -> 453,
          "hotelBrandId"      -> 782,
          "hotelAreaId"       -> 27713,
          "gmtOffset"         -> 7,
          "rankingScoresA"    -> 980452,
          "rankingScoresB"    -> 980452,
          "location"          -> "13.737397,100.560793",
          "review" -> Array(Map("providerId" -> 128, "score" -> 5.777), Map("providerId" -> 332, "score" -> 8.66)),
          "hotelFacilities" -> Array(80, 121),
          "bookOnRequest"   -> false
        ),
        indexInto(testIndex).fields(
          "id"                -> hotelIds(4),
          "hotelId"           -> hotelIds(4),
          "experimentVariant" -> 1,
          "whiteLabelId"      -> 1,
          "hotelName"         -> "CheQinn - Bangkok Chic Hostel",
          "starRating"        -> 2.0,
          "hotelCityId"       -> 9395,
          "hotelCountryId"    -> 106,
          "hotelChainId"      -> 0,
          "hotelBrandId"      -> 0,
          "hotelAreaId"       -> 27713,
          "gmtOffset"         -> 7,
          "rankingScoresA"    -> 9795,
          "rankingScoresB"    -> 9795,
          "location"          -> "13.7400885596195,100.553553700447",
          "review" -> Array(Map("providerId" -> 332, "score" -> 8.564), Map("providerId" -> 128, "score" -> 4.804)),
          "hotelFacilities" -> Array(248, 91, 56, 121),
          "bookOnRequest"   -> false
        ),
        indexInto(testIndex).fields(
          "id"                -> hotelIds(5),
          "hotelId"           -> hotelIds(5),
          "experimentVariant" -> 1,
          "whiteLabelId"      -> 1,
          "hotelName"         -> "HolidayInn - Jakarta",
          "starRating"        -> 5.0,
          "hotelCityId"       -> 8691,
          "hotelCountryId"    -> 192,
          "hotelChainId"      -> 0,
          "hotelBrandId"      -> 0,
          "hotelAreaId"       -> 70094,
          "gmtOffset"         -> 7,
          "rankingScoresA"    -> 100123,
          "rankingScoresB"    -> 100123,
          "location"          -> "13.7400885596195,100.553553700447",
          "review" -> Array(Map("providerId" -> 332, "score" -> 8.564), Map("providerId" -> 128, "score" -> 4.804)),
          "hotelFacilities" -> Array(323, 121),
          "bookOnRequest"   -> false
        )
      )
    }.await

    refresh(testIndex)
    blockUntilCount(6, testIndex)
  }

  override def afterAll(): Unit =
    try
      deleteIndex(testIndex)
    catch {
      case _ => // ignore
    }

  private def createRequest(
      filters: List[FilterDefinition],
      matrix: List[MatrixField],
      featureFlagRequest: Option[FeatureFlagRequest] = None
  ): CitySearchRequest =
    CitySearchRequest(
      cityId,
      aValidSearchRequest
        .withSearchCriteria(aValidSearchCriteria.withFeatureFlagRequest(featureFlagRequest))
        .withFilters(filters)
        .withMatrix(matrix)
    )

  // scalastyle:on
  test("Aggregation query response is converted to expected result form") {

    val agg1                             = "starRating"
    val filter1: ElasticFilterDefinition = RangeFilterDefinition("starRating", Some(Map("gte" -> 4)))
    val agg2                             = "hotelAreaId"
    val filter2: ElasticFilterDefinition = TermFilterDefinition("hotelAreaId", 26026)

    val matrix: List[MatrixField] = List(
      MatrixField(agg1, Some(10)),
      MatrixField(agg2)
    )

    val cols: mutable.Map[String, Int] = mutable.Map(agg1 -> 10, agg2 -> 10)

    val aValidFeatureFlagRequest = Some(FeatureFlagRequest(flagMap = Some(Map(FeatureFlagKey.FamilyMode -> true))))

    val aValidCitySearchRequest: CitySearchRequest = request.CitySearchRequest(
      cityId = 9395,
      searchRequest = aValidSearchRequest
        .withSearchCriteria(aValidSearchCriteria.withFeatureFlagRequest(aValidFeatureFlagRequest))
        .build()
    )

    implicit val contextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      List(filter1, filter2),
      List(MatrixField(agg1), MatrixField(agg2)),
      fieldOrder,
      false,
      contextHolder = RequestBuilder.testContextHolder
    )
    val response = getClient.execute {
      elasticService.builder.getAggregationQueryMultiCities(
        Array(cityId, otherCityId),
        filterAggregator,
        Set[Long](461790, 10637, 10809),
        false
      )
    }.await

    val matrixResult = elasticService.responseToMatrixResult(Array(9395), cols, response.result)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 2
      (aggMap should contain).key(agg1)
      (aggMap should contain).key(agg2)

      val starRatingMap = aggMap(agg1)
      val hotelAreaMap  = aggMap(agg2)

      hotelAreaMap should have size 1
      hotelAreaMap.map(x => x.id -> x.value).toMap should contain(26026 -> Option(2))
      starRatingMap should have size 3
      (starRatingMap.map(x => x.id -> x.value).toMap should contain)
        .allOf(3 -> Option(1), 4.5 -> Option(1), 5 -> Option(1))
    }
  }

  test("End to End MatrixFilterResult test") {

    val agg1 = "starRating"
    val agg2 = "hotelAreaId"

    val filters: List[FilterDefinition] = List(
      RangeFilterDefinition(agg1, Some(Map("gte" -> 4))),
      TermFilterDefinition(agg2, 26026)
    )

    val matrix: List[MatrixField] = List(
      MatrixField(agg1, Some(10)),
      MatrixField(agg2)
    )

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(hotelIds.toSet, Array(cityId), filterAggregator, false)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 2
      (aggMap should contain).key(agg1)
      (aggMap should contain).key(agg2)

      val starRatingMap = aggMap(agg1)
      val hotelAreaMap  = aggMap(agg2)

      hotelAreaMap should have size 2
      (hotelAreaMap.map(x => x.id -> x.value).toMap should contain).allOf(26026 -> Option(2), 27713 -> Option(1))
      starRatingMap should have size 3
      (starRatingMap.map(x => x.id -> x.value).toMap should contain)
        .allOf(3 -> Option(1), 4.5 -> Option(1), 5 -> Option(1))
    }
  }

  test("Matrix result returns aggregation for only those fields that are in the matrix input") {

    val agg1 = "starRating"
    val agg2 = "hotelAreaId"

    val filters: List[FilterDefinition] = List(
      RangeFilterDefinition(agg1, Some(Map("gte" -> 4))),
      TermFilterDefinition(agg2, 26026)
    )

    val matrix: List[MatrixField] = List(
      MatrixField(agg2)
    )

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(hotelIds.toSet, Array(cityId), filterAggregator, false)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 1
      (aggMap should contain).key(agg2)

      val hotelAreaMap = aggMap(agg2)

      hotelAreaMap should have size 2
      (hotelAreaMap.map(x => x.id -> x.value).toMap should contain).allOf(26026 -> Option(2), 27713 -> Option(1))
    }
  }

  test("End to End MatrixFilterResult complex filters test") {

    val agg1 = "starRating"
    val agg2 = "hotelAreaId"
    val agg3 = "hotelFacilities"

    val filters: List[FilterDefinition] = List(
      // common filter
      RangeFilterDefinition(agg1, Some(Map("gte" -> 4))),
      TermsFilterDefinition(agg3, 323, 121),
      TermFilterDefinition(agg2, 26026)
    )

    val matrix: List[MatrixField] = List(
      MatrixField(agg2),
      MatrixField(agg3)
    )

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(hotelIds.toSet, Array(cityId), filterAggregator, false)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 2
      (aggMap should contain).key(agg2)
      (aggMap should contain).key(agg3)

      val hotelAreaMap       = aggMap(agg2)
      val hotelfacilitiesMap = aggMap(agg3)

      hotelAreaMap should have size 2
      (hotelAreaMap.map(x => x.id -> x.value).toMap should contain).allOf(26026 -> Option(2), 27713 -> Option(1))
      hotelfacilitiesMap should have size 9
      (hotelfacilitiesMap.map(x => x.id -> x.value).toMap should contain).allOf(
        88  -> Option(1),
        121 -> Option(1),
        64  -> Option(1),
        281 -> Option(1),
        323 -> Option(2),
        18  -> Option(1),
        16  -> Option(1),
        143 -> Option(1),
        36  -> Option(1)
      )
    }
  }

  test("End to End MatrixFilterResult nested filters test") {

    val agg1 = "starRating"
    val agg2 = "hotelAreaId"
    val agg3 = "hotelFacilities"

    val filters: List[FilterDefinition] = List(
      // common filter
      RangeFilterDefinition(agg1, Some(Map("gte" -> 2))),
      TermsFilterDefinition(agg3, 323, 121),
      NestedFilterDefinition("review", TermFilterDefinition("review.providerId", 128))
    )

    val matrix: List[MatrixField] = List(
      MatrixField(agg2),
      MatrixField(agg3)
    )

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(hotelIds.toSet, Array(cityId), filterAggregator, false)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 2
      (aggMap should contain).key(agg2)
      (aggMap should contain).key(agg3)

      val hotelAreaMap       = aggMap(agg2)
      val hotelfacilitiesMap = aggMap(agg3)

      hotelAreaMap should have size 2
      (hotelAreaMap.map(x => x.id -> x.value).toMap should contain).allOf(26026 -> Option(3), 27713 -> Option(2))
      hotelfacilitiesMap should have size 10
      (hotelfacilitiesMap.map(x => x.id -> x.value).toMap should contain).allOf(
        88  -> Option(2),
        56  -> Option(1),
        121 -> Option(3),
        64  -> Option(1),
        323 -> Option(3),
        91  -> Option(1),
        80  -> Option(1),
        18  -> Option(1),
        16  -> Option(1),
        36  -> Option(1)
      )
    }
  }

  test("Empty Matrix request with filters returns successfully") {

    val agg1 = "starRating"
    val agg3 = "hotelFacilities"

    val filters: List[FilterDefinition] = List(
      // common filter
      RangeFilterDefinition(agg1, Some(Map("gte" -> 2))),
      TermsFilterDefinition(agg3, 323, 121),
      NestedFilterDefinition("review", TermFilterDefinition("review.providerId", 128))
    )

    // Send empty matrix field
    val matrix: List[MatrixField] = List()

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(hotelIds.toSet, Array(cityId), filterAggregator, false)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 0
    }
  }

  test("End to End MatrixFilterResult terms filters test") {

    val agg2 = "hotelAreaId"
    val agg3 = "hotelFacilities"

    val filters: List[FilterDefinition] = List(
      TermsFilterDefinition(agg3, 323, 121),
      TermFilterDefinition(agg2, 26026)
    )

    val matrix: List[MatrixField] = List(
      MatrixField(agg2),
      MatrixField(agg3)
    )

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(hotelIds.toSet, Array(cityId), filterAggregator, false)

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 2
      (aggMap should contain).key(agg2)
      (aggMap should contain).key(agg3)

      val hotelAreaMap       = aggMap(agg2)
      val hotelfacilitiesMap = aggMap(agg3)

      hotelAreaMap should have size 2
      (hotelAreaMap.map(x => x.id -> x.value).toMap should contain).allOf(26026 -> Option(3), 27713 -> Option(2))
      hotelfacilitiesMap should have size 9
      (hotelfacilitiesMap.map(x => x.id -> x.value).toMap should contain).allOf(
        88  -> Option(2),
        121 -> Option(1),
        64  -> Option(1),
        281 -> Option(1),
        323 -> Option(3),
        18  -> Option(1),
        16  -> Option(1),
        143 -> Option(1),
        36  -> Option(1)
      )
    }
  }

  test("End to End MatrixFilterResult multiple cities") {

    val agg3 = "hotelFacilities"

    val filters: List[FilterDefinition] = List(
      TermsFilterDefinition(agg3, 323, 121)
    )

    val matrix: List[MatrixField] = List(
      MatrixField(agg3)
    )

    val request                               = createRequest(filters, matrix)
    implicit val contextHolder: ContextHolder = RequestBuilder.testContextHolder

    val filterAggregator = new FilterAggregator(
      filterMatrixSortSetting,
      request.elasticFilters,
      matrix,
      fieldOrder,
      false,
      contextHolder = contextHolder
    )
    val matrixResult =
      elasticService.getFilterMatrixMultiCities(
        hotelIds.toSet,
        Array(cityId, otherCityId),
        filterAggregator,
        true
      )

    whenReady(matrixResult) { matrixResult =>
      val result = matrixResult._2
      result should not be None
      val aggMap = result

      aggMap should have size 1
      (aggMap should contain).key(agg3)

      val hotelfacilitiesMap = aggMap(agg3)

      hotelfacilitiesMap should have size 10
      (hotelfacilitiesMap.map(x => x.id -> x.value).toMap should contain).allOf(
        88  -> Option(2),
        56  -> Option(1),
        121 -> Option(4),
        64  -> Option(1),
        323 -> Option(4),
        91  -> Option(1),
        80  -> Option(1),
        18  -> Option(1),
        16  -> Option(1),
        36  -> Option(1)
      )
    }
  }

}
