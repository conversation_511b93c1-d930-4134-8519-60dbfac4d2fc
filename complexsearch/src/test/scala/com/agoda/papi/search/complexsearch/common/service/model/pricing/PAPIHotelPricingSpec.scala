package com.agoda.papi.search.complexsearch.common.service.model.pricing

import com.agoda.papi.search.common.util.DFDataExample.{ aValidConsolidatedAppliedDiscount, aValidPAPIRoomOffer }
import com.agoda.papi.search.internalmodel.pricing.{ PAPIDFBundleOffer, PAPIHotelPricing, PAPIRoomOffer }
import constant.ConsolidatedAppliedDiscountType.{ downliftDiscount, promoDiscount }
import models.DFSuggestPriceType
import models.pricing.enums.{ ApplyTypes, RequestedPrice }
import org.scalatest.flatspec.AnyFlatSpec
import org.scalatest.matchers.should.Matchers

class PAPIHotelPricingSpec extends AnyFlatSpec with Matchers {

  private def aValidPAPIHotelPricing: PAPIHotelPricing = PAPIHotelPricing(
    hotelId = 1001L,
    isReady = true,
    isAvailable = true,
    suppliersSummary = None,
    supplierInfo = None,
    suggestedRoomQuantity = 1,
    suggestPriceType = DFSuggestPriceType(RequestedPrice.Exclusive, ApplyTypes.PRPN)
  )

  "PAPIHotelPricing.cheapestBundle" should "return None is isCheapestOnly is false" in {
    val papiHotelPricing = aValidPAPIHotelPricing.copy(
      isCheapestOnly = false,
      offers = Seq(PAPIDFBundleOffer(bundleType = None, roomOffers = Seq.empty))
    )
    papiHotelPricing.cheapestBundle shouldBe None
  }

  "PAPIHotelPricing.cheapestBundle" should "return the first offer if isCheapestOnly is true and there are some offers" in {
    val firstOffer  = PAPIDFBundleOffer(bundleType = None, roomOffers = Seq.empty)
    val secondOffer = PAPIDFBundleOffer(bundleType = None, roomOffers = Seq.empty)
    val papiHotelPricing = aValidPAPIHotelPricing.copy(
      isCheapestOnly = true,
      offers = Seq(firstOffer, secondOffer)
    )
    (papiHotelPricing.cheapestBundle.get should be).theSameInstanceAs(firstOffer)
  }

  "PAPIRoomOffer.isConsolidatedAppliedEligible" should "return false if there is no consolidated applied discount" in {
    val roomOffer: PAPIRoomOffer = aValidPAPIRoomOffer(uid = 123456)
    roomOffer.isConsolidatedAppliedEligible shouldBe false
  }

  "PAPIRoomOffer.isConsolidatedAppliedEligible" should "return true if there is a downlift discount" in {
    val roomOffer: PAPIRoomOffer = aValidPAPIRoomOffer(
      uid = 123456
    ).copy(consolidatedAppliedDiscount = Some(aValidConsolidatedAppliedDiscount(downliftDiscount)))
    roomOffer.isConsolidatedAppliedEligible shouldBe true
  }

  "PAPIRoomOffer.isConsolidatedAppliedEligible" should "return true if there is auto applied promo discount" in {
    val roomOffer: PAPIRoomOffer = aValidPAPIRoomOffer(
      uid = 123456
    ).copy(consolidatedAppliedDiscount = Some(aValidConsolidatedAppliedDiscount(promoDiscount)))
    roomOffer.isConsolidatedAppliedEligible shouldBe true
  }

}
