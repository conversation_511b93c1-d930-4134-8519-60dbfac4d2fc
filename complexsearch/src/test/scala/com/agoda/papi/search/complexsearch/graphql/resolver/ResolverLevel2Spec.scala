package com.agoda.papi.search.complexsearch.graphql.resolver

import akka.http.scaladsl.model.HttpRequest
import api.request.FeatureFlag
import com.agoda.common.graphql.model.Wrapper
import com.agoda.common.graphql.schema.arguments.ArgumentProvider
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.core.search.framework.{ FutureTestSet, MockModule }
import com.agoda.core.search.models.request.{ CitySearchRequest, SearchCriteria }
import com.agoda.core.search.models.response.GqlSearchResult
import com.agoda.papi.search.common.externaldependency.experiment.{
  NoOpSearchExperimentManagerService,
  SearchExperimentManagerService
}
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.model.ReportingDataBuilder
import com.agoda.papi.search.common.service.{ RequestSegmentMetricsGenerator, WhiteLabelService }
import com.agoda.papi.search.common.util.{ ContentDataExample, DFDataExample }
import com.agoda.papi.search.common.util.context.ContextGenerator
import com.agoda.papi.search.common.util.logger.HadoopLogger
import com.agoda.papi.search.complexsearch.service.DataFetcher
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultTypes
import com.agoda.platform.service.context.{ GlobalContext, ServiceHeaders }
import com.agoda.papi.search.internalmodel.{
  ComplexSearchDeferred,
  CumulativePropertiesResponse,
  PropertyRequestDeferred,
  PropertySearchRequestWrapper,
  PropertySponsoredDetail,
  PropertySummaryEnvelope,
  SearchResultDeferred
}
import com.agoda.papi.search.internalmodel.configuration.{ PAPIMessageSettings, TopBookedCitiesSettings }
import com.agoda.papi.search.internalmodel.pricing.{ PAPIDFBundleOffer, PAPIHotelPricing }
import com.agoda.property.search.framework.binding.GqlTestBinding
import com.agoda.property.search.models._
import com.agoda.papi.search.common.util.CoreDataExamples._
import com.agoda.papi.search.common.util.DFDataExample.aPricingRequestParameters
import com.agoda.papi.search.common.util.ResolverDataExamples.{ propertySummaryEnvelopeA, propertySummaryEnvelopeB, _ }
import com.agoda.papi.search.common.util.circuitbreaker.BannerCampaignInfoDataExample
import com.agoda.papi.search.types.ContentSummaryWrapper
import com.agoda.property.search.utils.MockitoHelper
import io.circe.Json
import models.DFSupplierSummary
import models.starfruit.PricingRequestParameters
import org.mockito.{ ArgumentMatchers => AM, Mockito => MM }
import org.specs2.mock.Mockito
import org.specs2.mutable.Specification

import scala.concurrent.{ ExecutionContext, Future }

class ResolverLevel2Spec(implicit ec: ExecutionContext)
    extends Specification with FutureTestSet with Mockito with GqlTestBinding with ContextGenerator {
  implicit val hadoopLogger                                                   = mock[HadoopLogger]
  implicit val reporter                                                       = mock[MetricsReporter]
  implicit val searchExperimentManagerService: SearchExperimentManagerService = new NoOpSearchExperimentManagerService

  private def bValidHotel(uid: Long, roomBundleOffer: Seq[PAPIDFBundleOffer] = Nil): PAPIHotelPricing =
    DFDataExample
      .aValidPAPIHotelPricing(uid, roomBundleOffer, false)
      .copy(
        suppliersSummaries = List(
          DFSupplierSummary(27901, false),
          DFSupplierSummary(27933, false),
          DFSupplierSummary(27922, false),
          DFSupplierSummary(27801, true)
        )
      )

  private def getPropertySummaryEnvelope(uid: Int, content: ContentSummaryWrapper): PropertySummaryEnvelope =
    PropertySummaryEnvelope(
      1005,
      content,
      Some(Wrapper(bValidHotel(uid, List(DFDataExample.mockOfferD)), DFDataExample.JsonD)),
      None,
      None,
      None,
      PropertySponsoredDetail(),
      PropertyResultTypes.NormalProperty
    )

  private def getCumulativeResponse(): CumulativePropertiesResponse =
    CumulativePropertiesResponse(
      List(
        getPropertySummaryEnvelope(1003, ContentDataExample.propertyResponseWrapperC),
        getPropertySummaryEnvelope(1004, ContentDataExample.propertyResponseWrapperD),
        getPropertySummaryEnvelope(1005, ContentDataExample.propertyResponseWrapperD),
        getPropertySummaryEnvelope(1006, ContentDataExample.propertyResponseWrapperC),
        getPropertySummaryEnvelope(1007, ContentDataExample.propertyResponseWrapperA),
        getPropertySummaryEnvelope(1008, ContentDataExample.propertyResponseWrapperC)
      ),
      pagingFlowMetadata = None
    )

  private def getPropertyRequest(): (PropertySearchRequestWrapper, PropertyRequestDeferred) = {
    val bValidSearchCriteria = SearchCriteria(
      ratePlans = None,
      isUserLoggedIn = false,
      currency = aValidCurrency,
      itineraryCriteria = Some(aValidItineraryCriteria)
    )
    val bValidSearchRequest     = aValidSearchRequest.copy(searchCriteria = bValidSearchCriteria)
    val bValidCitySearchRequest = aValidCitySearchRequest.copy(searchRequest = bValidSearchRequest)
    val bPricingRequest =
      DFDataExample.aPricingRequest.copy(featureFlag = List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady))
    val bPricingRequestParameters = PricingRequestParameters(
      context = DFDataExample.aContextRequest,
      pricing = bPricingRequest,
      suggestedPrice = "",
      isSSR = None,
      booking = None
    )
    val pricingParametersWrapperB = Wrapper(bPricingRequestParameters, jsonDFRequest)
    val propertySearchRequestWrapperB = propertySearchRequestWrapper.copy(
      searchRequest = bValidCitySearchRequest,
      pricingRequestWrapper = Some(pricingParametersWrapperB)
    )
    val propertyRequestDeferredB =
      propertyRequestDeferred.copy(propertySearchRequestWrapper = propertySearchRequestWrapperB)

    (propertySearchRequestWrapperB, propertyRequestDeferredB)
  }

  private def getReportingDataBuilder(globalContext: GlobalContext) =
    new ReportingDataBuilder(
      globalContext,
      TopBookedCitiesSettings(),
      PAPIMessageSettings(),
      query,
      HttpRequest(uri = "/graphql/search"),
      mock[WhiteLabelService]
    )

  "Level 2 Deferred" should {
    "Return Valid PropertySummary Response" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            reportingDataBuilder,
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(cumulativePropertyResponse))
          .when(dataFetcher)
          .fetchProperty(AM.eq(propertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)
        val resolverResult          = resolverLevel2.resolveLevel2(propertyRequestDefered, mockGraphQLContext, AnyRef)
        val resolverResultCollected = propertyRequestDefered.collect(resolverResult)

        resolverResultCollected.size should_== 1
        whenReady(resolverResultCollected.head) { res =>
          res should_== (List(propertySummaryEnvelopeA, propertySummaryEnvelopeB))
        }
      }
    }

    "Return CID (from GlobalContext)" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val context        = globalContext.addHeaders(ServiceHeaders.AgCid.toString -> "2")
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            context,
            getReportingDataBuilder(globalContext),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult))
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 1
        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[GqlSearchResult].cid should_== Some(2)
        }
      }
    }

    "IsComplete is changed to false if feature flag ReturnHotelNotReadyIfPullNotReady is true and conditions are met ( >3 hotel with empty cheapest room offer and at least 1 notready supplier" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2                                                 = inject[ResolverLevel2]
        val (bValidPropertySearchRequestWrapper, propertyRequestDeferredB) = getPropertyRequest()
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            getReportingDataBuilder(globalContext),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(getCumulativeResponse()))
          .when(dataFetcher)
          .fetchProperty(AM.eq(bValidPropertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)
        val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult), propertyRequestDeferredB)
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 2
        whenReady(resolverResultCollected(0)) { res =>
          res.asInstanceOf[GqlSearchResult].searchInfo.isComplete should_== false
          mockGraphQLContext.reportingDataBuilder.getSearchResult.map(_.searchInfo.isComplete) should_== Some(false)
        }
        whenReady(resolverResultCollected(1)) { res =>
          res.asInstanceOf[List[PropertySummaryEnvelope]] should_== List.empty
        }
      }
    }

    "IsComplete remain as true if feature flag ReturnHotelNotReadyIfPullNotReady is MISSING and conditions are met ( >3 hotel with empty cheapest room offer and at least 1 notready supplier" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            getReportingDataBuilder(globalContext),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(getCumulativeResponse()))
          .when(dataFetcher)
          .fetchProperty(AM.eq(propertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)

        val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult), propertyRequestDeferred)
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 2
        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[GqlSearchResult].searchInfo.isComplete should_== true
          mockGraphQLContext.reportingDataBuilder.getSearchResult.map(_.searchInfo.isComplete) should_== Some(true)
        }
        whenReady(resolverResultCollected(1)) { res =>
          res.asInstanceOf[List[PropertySummaryEnvelope]] should_!= List.empty
        }
      }
    }

    "IsComplete remain as true if feature flag ReturnHotelNotReadyIfPullNotReady is true and conditions are NOT met ( <=3 hotel with empty cheapest room offer and at least 1 notready supplier" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2                                                 = inject[ResolverLevel2]
        val (bValidPropertySearchRequestWrapper, propertyRequestDeferredB) = getPropertyRequest()
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            getReportingDataBuilder(globalContext),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(cumulativePropertyResponse))
          .when(dataFetcher)
          .fetchProperty(AM.eq(bValidPropertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)
        val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult), propertyRequestDeferredB)
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 2
        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[GqlSearchResult].searchInfo.isComplete should_== true
          mockGraphQLContext.reportingDataBuilder.getSearchResult.map(_.searchInfo.isComplete) should_== Some(true)
        }
        whenReady(resolverResultCollected(1)) { res =>
          res.asInstanceOf[List[PropertySummaryEnvelope]] should_!= List.empty
        }
      }
    }

    "IsComplete is false when searchResults.searchInfo.isComplete is false and feature ReturnHotelNotReadyIfPullNotReady is true and conditions for it are NOT met" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2                                                 = inject[ResolverLevel2]
        val (bValidPropertySearchRequestWrapper, propertyRequestDeferredB) = getPropertyRequest()
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            getReportingDataBuilder(globalContext),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(cumulativePropertyResponse))
          .when(dataFetcher)
          .fetchProperty(AM.eq(bValidPropertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)
        val inputDefer = Vector(
          SearchResultDeferred(aValidSearchResult.copy(searchInfo = aValidSearchInfo.copy(isComplete = false))),
          propertyRequestDeferredB
        )
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 2
        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[GqlSearchResult].searchInfo.isComplete should_== false
          mockGraphQLContext.reportingDataBuilder.getSearchResult.map(_.searchInfo.isComplete) should_== Some(false)
        }
        whenReady(resolverResultCollected(1)) { res =>
          res.asInstanceOf[List[PropertySummaryEnvelope]] should_!= List.empty
        }
      }
    }

    "Return CID (from Pricing Request)" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val mockGlobalContext =
          new GlobalContext(Map(ServiceHeaders.AgOrigin.toString -> "US", ServiceHeaders.AgCid.toString -> "0"))
        val reportingBuilder = getReportingDataBuilder(mockGlobalContext)
        reportingBuilder.update(aPricingRequestParameters)
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            mockGlobalContext,
            reportingBuilder,
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult))
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 1
        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[GqlSearchResult].cid should_== Some(0)
        }
      }
    }

    "Return CID (No data)" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            getReportingDataBuilder(globalContext),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult))
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected.size should_== 1
        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[GqlSearchResult].cid should_== None
        }
      }
    }

    "Return Valid BannerCampaignInfo" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            reportingDataBuilder.update(aValidRegionSearchRequest),
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        MockitoHelper
          .doReturn(Future.successful(BannerCampaignInfoDataExample.searchBannerCampaignInfoResponse))
          .when(dataFetcher)
          .fetchRegionBannerCampaignResponse(anyObject, anyObject)(anyObject, anyObject)
        val inputDefer              = Vector(searchBannerCampaignInfoDeferred)
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)

        resolverResultCollected.size should_== 1
        whenReady(resolverResultCollected.head) { res =>
          res should_== (BannerCampaignInfoDataExample.searchBannerCampaignInfoResponse)
        }
      }
    }
  }

  "Not Level 2 Deferred" should {
    "Not Return Valid ComplexSearch data" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]
        val mockGraphQLContext =
          GraphQLContext(
            mock[ArgumentProvider],
            globalContext,
            reportingDataBuilder,
            defaultRequestContext,
            Some(aValidPAPISearchResultTracker),
            piiContext,
            NoOpTracer,
            requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
          )
        val citySearchRequest = CitySearchRequest(42, aValidSearchRequest)
        val wrapper           = Wrapper(citySearchRequest, Json.Null)
        MM.doReturn(Future.successful(aValidSearchResult), Nil: _*)
          .when(dataFetcher)
          .fetchSearchResponse(anyObject, anyObject)(anyObject, anyObject)
        val inputDefer              = Vector(ComplexSearchDeferred("city", wrapper))
        val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
        val resolverResultCollected = inputDefer.collect(resolverResult)
        resolverResultCollected should_== Vector.empty
      }
    }
  }

  "Round Number should" should {
    "Return rounded number" in {
      val dataFetcher = mock[DataFetcher]
      injectionTest(new MockModule {
        bind[DataFetcher] to dataFetcher
      }) { implicit injector =>
        val resolverLevel2 = inject[ResolverLevel2]

        resolverLevel2.roundNumber(0) should_== 0
        resolverLevel2.roundNumber(1) should_== 1
        resolverLevel2.roundNumber(5) should_== 5
        resolverLevel2.roundNumber(7) should_== 6
        resolverLevel2.roundNumber(10) should_== 10
        resolverLevel2.roundNumber(15) should_== 10

      }
    }
  }

  "FUSION-6248: ignore ReturnHotelNotReadyIfPullNotReady flag on B side" in {
    val dataFetcher = mock[DataFetcher]
    injectionTest(new MockModule {
      bind[DataFetcher] to dataFetcher
    }) { implicit injector =>
      val resolverLevel2                                                 = inject[ResolverLevel2]
      val (bValidPropertySearchRequestWrapper, propertyRequestDeferredB) = getPropertyRequest()
      val mockSearchContext = createContextHolder(forceExperimentNames = Some(Seq("FUSION-6248")))
      val mockGraphQLContext =
        GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          getReportingDataBuilder(globalContext),
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          searchContextHolderO = Some(mockSearchContext),
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
      MockitoHelper
        .doReturn(Future.successful(getCumulativeResponse()))
        .when(dataFetcher)
        .fetchProperty(AM.eq(bValidPropertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)
      val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult), propertyRequestDeferredB)
      val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
      val resolverResultCollected = inputDefer.collect(resolverResult)
      resolverResultCollected.size should_== 2
      whenReady(resolverResultCollected(0)) { res =>
        // On B side, experiment ignores the flag so isComplete should remain true even with not ready hotels
        res.asInstanceOf[GqlSearchResult].searchInfo.isComplete should_== true
        mockGraphQLContext.reportingDataBuilder.getSearchResult.map(_.searchInfo.isComplete) should_== Some(true)
      }
      whenReady(resolverResultCollected(1)) { res =>
        // On B side, properties should be returned
        res.asInstanceOf[List[PropertySummaryEnvelope]] should_!= List.empty
      }
    }
  }

  "FUSION-6248: keep ReturnHotelNotReadyIfPullNotReady flag on A side" in {
    val dataFetcher = mock[DataFetcher]
    injectionTest(new MockModule {
      bind[DataFetcher] to dataFetcher
    }) { implicit injector =>
      val resolverLevel2                                                 = inject[ResolverLevel2]
      val (bValidPropertySearchRequestWrapper, propertyRequestDeferredB) = getPropertyRequest()
      val mockSearchContext = createContextHolder(forceExperimentNames = None)
      val mockGraphQLContext =
        GraphQLContext(
          mock[ArgumentProvider],
          globalContext,
          getReportingDataBuilder(globalContext),
          defaultRequestContext,
          Some(aValidPAPISearchResultTracker),
          piiContext,
          NoOpTracer,
          searchContextHolderO = Some(mockSearchContext),
          requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
        )
      MockitoHelper
        .doReturn(Future.successful(getCumulativeResponse()))
        .when(dataFetcher)
        .fetchProperty(AM.eq(bValidPropertySearchRequestWrapper), anyObject)(anyObject, anyObject, anyObject)
      val inputDefer              = Vector(SearchResultDeferred(aValidSearchResult), propertyRequestDeferredB)
      val resolverResult          = resolverLevel2.resolveLevel2(inputDefer, mockGraphQLContext, AnyRef)
      val resolverResultCollected = inputDefer.collect(resolverResult)
      resolverResultCollected.size should_== 2
      whenReady(resolverResultCollected(0)) { res =>
        // On A side, flag behavior should be preserved - isComplete changes to false when conditions are met
        res.asInstanceOf[GqlSearchResult].searchInfo.isComplete should_== false
        mockGraphQLContext.reportingDataBuilder.getSearchResult.map(_.searchInfo.isComplete) should_== Some(false)
      }
      whenReady(resolverResultCollected(1)) { res =>
        // On A side, no properties should be returned when isComplete is false
        res.asInstanceOf[List[PropertySummaryEnvelope]] should_== List.empty
      }
    }
  }

}
