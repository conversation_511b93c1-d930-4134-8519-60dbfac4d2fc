package com.agoda.papi.search.complexsearch.util.elasticsearch

import scala.concurrent.duration._

object EmbedElasticConstants {

  val index       = "hotels"
  val indexB      = "bhotels"
  val indexType   = "hotelstype"
  val clusterName = "testCluster"
  // connectionString is now dynamically determined by TestContainers
  def connectionString            = EmbeddedElasticService.getConnectionString
  val cityResultSize              = 50
  val tcpTimeOut                  = 1000
  val asyncTimeout                = 1050
  val polygonResultSize           = 50
  val requestTimeOut              = 2000 milliseconds
  val hotelIdsPerReviewFetching   = 1000
  val hotelIdsPerDistanceFetching = 1000
}
