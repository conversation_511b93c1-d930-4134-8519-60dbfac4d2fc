package com.agoda.papi.search.complexsearch.util.elasticsearch

import com.agoda.core.search.framework.dexsync.ElasticHotelInfo
import com.agoda.core.search.models.Fields.CITYCENTERDISTANCE
import com.agoda.papi.search.types.CityIdType
import com.sksamuel.elastic4s.Indexable
import com.sksamuel.elastic4s.fields.{ DoubleField, GeoPointField, IntegerField, LongField, NestedField }
import com.sksamuel.elastic4s.http.JavaClientExceptionWrapper
import com.sksamuel.elastic4s.requests.bulk.BulkCompatibleRequest
import com.sksamuel.elastic4s.testkit.{ ClientProvider, ElasticSugar }
import org.scalatest.{ BeforeAndAfterAll, TestSuite }

import scala.util.{ Failure, Success, Try }

trait MockElasticService
    extends TestSuite with EmbeddedElasticService with BeforeAndAfterAll with ElasticSugar with ClientProvider {
  // scalastyle:off

  override lazy val client = elasticsearchClient

  val testIndex            = EmbedElasticConstants.index
  val testType             = EmbedElasticConstants.indexType
  val memberId: CityIdType = 10001

  // override this if use builders
  val docs: List[ElasticHotelInfo] = List.empty

  // override this for custom queries
  lazy val documents: List[BulkCompatibleRequest] =
    docs.map { hotel =>
      indexInto(testIndex).id(hotel.id.toString).source(hotel)
    }

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  def mockEsBeforeAll(): Unit = {
    setup()
    val result = Try {
      deleteIndex(testIndex)
      client.execute {
        createIndex(testIndex)
          .replicas(1)
          .shards(2)
          .refreshInterval("-1")
          .indexSetting("merge.scheduler.max_thread_count", "1")
          .mapping(
            properties(
              LongField("landmarkTypeIds", store = Some(false)),
              LongField("landmarkSubTypeIds", store = Some(false)),
              LongField("landmarkSubTypeCategoryIds", store = Some(false)),
              DoubleField(CITYCENTERDISTANCE, store = Some(false)),
              LongField("id", store = Some(true)),
              LongField("hotelId", store = Some(true)),
              LongField("hotelCityId", store = Some(true)),
              DoubleField("landmarkDistances", store = Some(true)),
              DoubleField("airportLandmarkDistances", store = Some(true)),
              GeoPointField("location", store = Some(false)),
              NestedField("review", enabled = Some(true)),
              DoubleField("combinedReviewScore", store = Some(true)),
              DoubleField("combinedReviewScoreBusinessTraveller", store = Some(true)),
              DoubleField("combinedReviewScoreCouples", store = Some(true)),
              DoubleField("combinedReviewScoreSoloTravellers", store = Some(true)),
              DoubleField("combinedReviewScoreFamiliesWithYoung", store = Some(true)),
              DoubleField("combinedReviewScoreFamiliesWithTeen", store = Some(true)),
              DoubleField("combinedReviewScoreGroupsTravellers", store = Some(true)),
              IntegerField("combinedReviewCount", store = Some(true))
            )
          )
      }.await

      client.execute {
        bulk(documents)
      }.await

      refresh(testIndex)
      val count = docs.size
      blockUntilCount(count, testIndex)
    }
    result match {
      case Success(_) =>
      case Failure(exception) if exception.isInstanceOf[JavaClientExceptionWrapper] =>
        fail("Failed to initialize Elasticsearch for testing", exception)
    }
  }

  implicit def JacksonJsonIndexable[T]: Indexable[T] = new Indexable[T] {
    override def json(t: T): String = ElasticJacksonMapper.mapper.writeValueAsString(t)
  }

}
