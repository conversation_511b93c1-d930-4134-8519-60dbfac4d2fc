package com.agoda.papi.search.complexsearch.service

import cats.instances.future._
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.papi.pricing.graphql.PricingPropertiesRequestEnvelope
import com.agoda.papi.search.common.externaldependency.chunkable.client.{ DFChannelEnrichService, DFChunkableService }
import com.agoda.papi.search.common.framework.mock.CAPIServiceTest
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.service._
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.configuration.{
  ChunkableWrapperSettings,
  EndpointSettings,
  MergeSettings,
  SplitSettings
}
import com.agoda.papi.search.common.util.context.ContextGenerator.createContextHolder
import com.agoda.papi.search.common.util.context._
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.internalmodel.request.PricingPropertiesRequestWrapper
import com.agoda.papi.search.types.{ CountryIdType, HotelIdType }
import com.agoda.papi.ypl.models.AreaId
import com.agoda.platform.service.context.GlobalContext
import com.netflix.config.scala.{ DynamicBooleanProperty, DynamicIntProperty }
import models.db.HotelId
import org.mockito.Mockito.verify
import org.mockito.{ ArgumentMatchers => AM, Mockito => MM }
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AsyncWordSpec

import java.util.concurrent.{ TimeoutException, TimeUnit }
import scala.concurrent.duration._
import scala.concurrent.{ ExecutionContext, Future }

class DFChunkableServiceSpec extends AsyncWordSpec with Matchers {
  implicit val searchContextHolder: ContextHolder = createContextHolder()

  import DFDataExample._
  import com.agoda.papi.search.common.util.CoreDataExamples._
  implicit val ec: ExecutionContext = ExecutionContexts.defaultContext

  class MockPropertyMetaService(mockProductHotels: Seq[ProductHotels]) extends PropertyMetaService {

    override def getProductHotelsByHotel(
        hotels: Seq[HotelId]
    )(implicit
        withCorrelationId: WithCorrelationId,
        contextHolder: ContextHolder
    ): Future[Map[HotelId, ProductHotels]] = {
      val existedHotel = mockProductHotels.map(_.hotelId).intersect(hotels)
      val result       = mockProductHotels.filter(p => existedHotel.contains(p.hotelId)).map(p => p.hotelId -> p).toMap
      Future.successful(result)
    }

    override def isPropertyCompliant(isCompliant: Boolean, propertyCountryId: HotelId, areaId: AreaId)(implicit
        contextHolder: ContextHolder
    ): Boolean = true

    override def isPropertyDsaCompliant(
        isDSAComplianceEnabled: Boolean,
        dsaComplianceStatus: Boolean,
        isHotelCreatedBeforeFeb2024: Boolean,
        blockDSANonCompliantHotelRegisteredBeforeFeb2024: Boolean
    )(implicit contextHolder: ContextHolder): Boolean = true

    override def isPropertyDsaCompliantV2(
        hotelIds: List[HotelIdType],
        userOrigin: Option[String],
        requestId: Option[String]
    )(implicit ec: ExecutionContext): Future[HotelIdType => Boolean] = Future.successful(_ => true)

  }

  class MockCalculateChannelService(resultMap: Map[CountryIdType, (List[Int], Boolean)])
      extends CalculateChannelService {

    override def calculateChannel(
        calculateChannelParams: CalculateChannelParams
    )(implicit contextHolder: ContextHolder): Future[CalculateChannelResult] = {
      val res = resultMap.getOrElse(calculateChannelParams.countryId, (List.empty, false))
      Future.successful(CalculateChannelResult(res._1))
    }

  }

  "DF Chunkable Service" should {

    val mockEndpointSettings = EndpointSettings(
      DynamicIntProperty("fake-path", 0),
      FiniteDuration(0, TimeUnit.SECONDS),
      DynamicBooleanProperty("fake-path", false)
    )
    val defaultSettings =
      ChunkableWrapperSettings(SplitSettings(chunkSize = 3), MergeSettings(1.0), mockEndpointSettings)
    val ableToFailSettings =
      ChunkableWrapperSettings(SplitSettings(chunkSize = 3), MergeSettings(0.0), mockEndpointSettings)
    val capiService = new CAPIServiceTest

    "set Email To Discount Request correctly" in {
      DFChunkableService.getOverrideEmail("@agoda.com", Some(aValidDiscountRequest)) shouldEqual Some("@agoda.com")
      DFChunkableService.getOverrideEmail("@agoda.com", None) shouldEqual Some("@agoda.com")
      DFChunkableService.getOverrideEmail(
        "@agoda.com",
        Some(aValidDiscountRequest.copy(email = Some("@yahoo.com")))
      ) shouldEqual None
      DFChunkableService.getOverrideEmail("", Some(aValidDiscountRequest)) shouldEqual None
      DFChunkableService.getOverrideEmail("", None) shouldEqual None
    }

    "get Override DF request correctly" in {
      val mockUserCtx   = Some("some Context")
      val mockEmail     = "@agoda.com"
      val mockRatePlans = List(1, 2, 8)
      val res = DFChunkableService.getOverrideDFRequest(
        aValidPricingPropertiesRequestEnvelope,
        capiRequestContext = mockUserCtx,
        emailDomain = mockEmail,
        channels = Some(CalculateChannelResult(channelIds = mockRatePlans)),
        hasHourlyRates = true
      )
      res.userContext shouldEqual mockUserCtx
      res.emailDomain shouldEqual Some(mockEmail)
      res.ratePlans shouldEqual Some(mockRatePlans)
    }

    "pass pricing flag correctly" in {
      val productHotel        = ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None)
      val anotherProductHotel = ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None)
      val productHotelMapping = Map(
        1L -> productHotel,
        2L -> anotherProductHotel
      )
      val productHotels = Seq(
        productHotel,
        anotherProductHotel
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true)
      )
      val mockMeta             = new MockPropertyMetaService(productHotels)
      val mockChannel          = MM.spy(new MockCalculateChannelService(channelMap))
      val channelEnrichService = MM.spy(new DFChannelEnrichService(mockChannel))

      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 2L).toEnvelope,
          Some(true),
          Some(true),
          true,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(channelEnrichService, org.mockito.Mockito.times(1)).enrichedChannelRequest(
          request = AM.any(),
          globalContext = AM.any(),
          enableOpaqueChannel = AM.eq(Some(true)),
          isEnabledPartnerChannelSelection = AM.eq(Some(true)),
          isPackageDeal = AM.eq(true),
          productHotelMapping = AM.eq(productHotelMapping),
          experimentContext = AM.eq(NopExperimentContext)
        )(executionContext = AM.eq(ec))
        succeed
      }
    }

    "one city" in {
      val productHotel        = ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None)
      val anotherProductHotel = ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None)

      val productHotels = Seq(
        productHotel,
        anotherProductHotel
      )

      val productHotelMapping = Map(
        1L -> productHotel,
        2L -> anotherProductHotel
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true)
      )
      val mockMeta    = new MockPropertyMetaService(productHotels)
      val mockChannel = MM.spy(new MockCalculateChannelService(channelMap))

      val channelEnrichService = MM.spy(new DFChannelEnrichService(mockChannel))

      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 2L).toEnvelope,
          Some(true),
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(1))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        val expCalculateChannelParams = CalculateChannelParams(List(), 0, 1, "US", 0, 0, false, true, 1, false, false)
        verify(mockChannel, org.mockito.Mockito.times(2)).calculateChannel(AM.eq(expCalculateChannelParams))(AM.any())
        verify(channelEnrichService, org.mockito.Mockito.times(1)).enrichedChannelRequest(
          request = AM.any(),
          globalContext = AM.any(),
          enableOpaqueChannel = AM.eq(Some(true)),
          isEnabledPartnerChannelSelection = AM.eq(None),
          isPackageDeal = AM.eq(false),
          productHotelMapping = AM.eq(productHotelMapping),
          experimentContext = AM.eq(NopExperimentContext)
        )(executionContext = AM.eq(ec))
        succeed
      }
    }

    val hasHourlyRatesTestCases = Seq(
      ("any one property has hourly rates", true, true),
      ("no property has hourly rates", false, false)
    )

    "get Override DF request" should {
      hasHourlyRatesTestCases.foreach { case (testName, hasHourlyRate, expectedHasHourlyRates) =>
        s"pass hasHourlyRates correctly when $testName" in {
          val productHotel        = ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None, hasHourlyRate = hasHourlyRate)
          val anotherProductHotel = ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None)

          val productHotels = Seq(
            productHotel,
            anotherProductHotel
          )
          val channelMap = Map[CountryIdType, (List[Int], Boolean)](
            1L -> (List(1, 2, 7), true)
          )
          val mockMeta    = new MockPropertyMetaService(productHotels)
          val mockChannel = MM.spy(new MockCalculateChannelService(channelMap))

          val channelEnrichService = MM.spy(new DFChannelEnrichService(mockChannel))

          val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
          val dfChunkableService = new DFChunkableService(
            mockDFChunkableClient,
            defaultSettings,
            channelEnrichService,
            capiService,
            mockMeta,
            NoOpTracer,
            Components.PAPI
          )
          val resultF = dfChunkableService.process(
            PricingPropertiesRequestWrapper(
              aValidPricingPropertiesRequest.withPropertyIds(1L to 2L).toEnvelope,
              Some(true),
              capiEmailDomain = ""
            ),
            globalContext,
            NopExperimentContext
          )

          resultF.map { _ =>
            verify(mockDFChunkableClient, org.mockito.Mockito.times(1))
              .serviceCall(
                AM.argThat[PricingPropertiesRequestWrapper](pricingPropertiesRequestWrapper =>
                  pricingPropertiesRequestWrapper.pricingRequest.overridePricingRequest.get.hasHourlyRates
                    .contains(expectedHasHourlyRates)
                ),
                AM.any(),
                AM.any()
              )(AM.any(), AM.any())
            succeed
          }
        }
      }
    }

    "one city 6 hotels" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(3L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(4L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(5L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(6L, 1L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true)
      )
      val mockMeta              = new MockPropertyMetaService(productHotels)
      val mockChannel           = new MockCalculateChannelService(channelMap)
      val channelEnrichService  = new DFChannelEnrichService(mockChannel)
      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 6L).toEnvelope,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(2))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        succeed
      }
    }

    "two city" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(3L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(4L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(5L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(6L, 2L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true),
        2L -> (List(1, 2, 7, 8, 9), true)
      )
      val mockMeta              = new MockPropertyMetaService(productHotels)
      val mockChannel           = MM.spy(new MockCalculateChannelService(channelMap))
      val channelEnrichService  = new DFChannelEnrichService(mockChannel)
      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 6L).toEnvelope,
          Some(true),
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(2))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        val expCalculateChannelParams = CalculateChannelParams(List(), 0, 1, "US", 0, 0, false, true, 1, false, false)
        verify(mockChannel, org.mockito.Mockito.times(3)).calculateChannel(AM.eq(expCalculateChannelParams))(AM.any())
        succeed
      }
    }

    "two city 6 hotels each" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(3L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(4L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(5L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(6L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(7L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(8L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(9L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(10L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(11L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(12L, 1L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true),
        2L -> (List(1, 2, 7, 8, 9), true)
      )
      val mockMeta              = new MockPropertyMetaService(productHotels)
      val mockChannel           = new MockCalculateChannelService(channelMap)
      val channelEnrichService  = new DFChannelEnrichService(mockChannel)
      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 12L).toEnvelope,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(4))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        succeed
      }
    }

    "if SP fail DF call able to fallback 1" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(3L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(4L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(5L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(6L, 2L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true),
        2L -> (List(1, 2, 7, 8, 9), true)
      )
      val mockMeta = new MockPropertyMetaService(productHotels)
      val mockChannel = new CalculateChannelService {
        override def calculateChannel(calculateChannelParams: CalculateChannelParams)(implicit
            contextHolder: ContextHolder
        ): Future[CalculateChannelResult] =
          Future.failed(new TimeoutException)
      }
      val channelEnrichService  = new DFChannelEnrichService(mockChannel)
      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 6L).toEnvelope,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(2))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        succeed
      }
    }

    "if SP fail DF call able to fallback 2" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(3L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(4L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(5L, 2L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(6L, 2L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true),
        2L -> (List(1, 2, 7, 8, 9), true)
      )
      val mockMeta = new PropertyMetaService {
        override def getProductHotelsByHotel(hotels: Seq[HotelId])(implicit
            withCorrelationId: WithCorrelationId,
            contextHolder: ContextHolder
        ): Future[Map[HotelId, ProductHotels]] = Future.failed(new NullPointerException)

        override def isPropertyCompliant(isCompliant: Boolean, propertyCountryId: HotelId, areaId: AreaId)(implicit
            contextHolder: ContextHolder
        ): Boolean = true

        override def isPropertyDsaCompliant(
            isDSAComplianceEnabled: Boolean,
            dsaComplianceStatus: Boolean,
            isHotelCreatedBeforeFeb2024: Boolean,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024: Boolean
        )(implicit contextHolder: ContextHolder): Boolean = true

        override def isPropertyDsaCompliantV2(
            hotelIds: List[HotelIdType],
            userOrigin: Option[String],
            requestId: Option[String]
        )(implicit ec: ExecutionContext): Future[HotelIdType => Boolean] = Future.successful(_ => true)
      }
      val mockChannel           = new MockCalculateChannelService(channelMap)
      val channelEnrichService  = new DFChannelEnrichService(mockChannel)
      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 6L).toEnvelope,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(2))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        succeed
      }
    }

    "if service call fail it should not retry on whole process" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true)
      )
      val mockMeta              = new MockPropertyMetaService(productHotels)
      val mockChannel           = new MockCalculateChannelService(channelMap)
      val channelEnrichService  = new DFChannelEnrichService(mockChannel)
      val mockDFChunkableClient = MM.spy(new MockFailedDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        ableToFailSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )
      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 2L).toEnvelope,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(1))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        succeed
      }
    }

    "if channelEnrichService call throw exception - it should not fail the whole process" in {
      val productHotels = Seq(
        ProductHotels(1L, 1L, 1L, 1L, 1, 1, 0, None),
        ProductHotels(2L, 1L, 1L, 1L, 1, 1, 0, None)
      )
      val channelMap = Map[CountryIdType, (List[Int], Boolean)](
        1L -> (List(1, 2, 7), true)
      )
      val mockMeta    = new MockPropertyMetaService(productHotels)
      val mockChannel = new MockCalculateChannelService(channelMap)
      val channelEnrichService = new DFChannelEnrichService(mockChannel) {
        override def getChannelMapping(
            request: PricingPropertiesRequestEnvelope,
            globalContext: GlobalContext,
            enableOpaqueChannel: Option[Boolean],
            hotelCountryMapping: Map[HotelIdType, CountryIdType],
            isEnabledPartnerChannelSelection: Option[Boolean],
            isPackageDeal: Boolean,
            experimentContext: ExperimentContext
        )(implicit ec: ExecutionContext): Future[Map[CalculateChannelResult, Seq[Long]]] =
          Future.failed(new RuntimeException("channelEnrichService failed"))
      }
      val mockDFChunkableClient = MM.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        mockDFChunkableClient,
        defaultSettings,
        channelEnrichService,
        capiService,
        mockMeta,
        NoOpTracer,
        Components.PAPI
      )

      val resultF = dfChunkableService.process(
        PricingPropertiesRequestWrapper(
          aValidPricingPropertiesRequest.withPropertyIds(1L to 2L).toEnvelope,
          capiEmailDomain = ""
        ),
        globalContext,
        NopExperimentContext
      )

      resultF.map { _ =>
        verify(mockDFChunkableClient, org.mockito.Mockito.times(1))
          .serviceCall(AM.any(), AM.any(), AM.any())(AM.any(), AM.any())
        succeed
      }
    }
  }

}
