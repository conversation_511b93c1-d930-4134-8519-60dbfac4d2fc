package com.agoda.papi.search.complexsearch.util.elasticsearch

import com.github.dockerjava.api.command.CreateContainerCmd
import com.github.dockerjava.api.model.{ ExposedPort, PortBinding, Ports }
import com.sksamuel.elastic4s.{ ElasticClient, ElasticProperties }
import com.sksamuel.elastic4s.http.JavaClient
import org.scalatest.Suite
import org.testcontainers.elasticsearch.ElasticsearchContainer
import org.testcontainers.utility.DockerImageName

import java.util.concurrent.atomic.AtomicBoolean
import scala.util.{ Failure, Success, Try }

object EmbeddedElasticService {
  private val isInitialized: AtomicBoolean = new AtomicBoolean(false)

  // Use the same version as your current elasticsearch version
  private val elasticsearchDockerImage = DockerImageName.parse("docker.elastic.co/elasticsearch/elasticsearch:7.17.4")

  // Start Container earlies as possible
  startContainer()

  private lazy val container: ElasticsearchContainer = {
    val c = new ElasticsearchContainer(elasticsearchDockerImage)
      .with<PERSON><PERSON><PERSON>(true)
    c.addEnv("discovery.type", "single-node")
    c.addEnv("bootstrap.memory_lock", "true")
    c.addEnv("ES_JAVA_OPTS", "-Xms512m -Xmx512m")
    c.addEnv("http.cors.enabled", "true")
    c.addEnv("http.cors.allow-origin", "*")
    c.addEnv("xpack.security.enabled", "false")
    c
  }

  private lazy val client: ElasticClient = {
    val props = ElasticProperties(s"http://${container.getHttpHostAddress}")
    ElasticClient(JavaClient(props))
  }

  def getClient: ElasticClient = client

  def getConnectionString: String = container.getHttpHostAddress

  private def startContainer(): Unit =
    if (!isInitialized.get()) {
      Try {
        if (!container.isRunning) {
          container.start()
        }
        // ElasticsearchContainer has built-in wait strategies, no need for explicit waiting
      } match {
        case Success(_) =>
          isInitialized.set(true)
        case Failure(exception) =>
          throw new RuntimeException("Failed to start Elasticsearch container", exception)
      }
    }

  def ensureStarted(): Unit = startContainer()

  def shutdown(): Unit = {
    if (container.isRunning) {
      container.stop()
    }
    isInitialized.set(false)
  }

}

trait EmbeddedElasticService {
  this: Suite =>

  def setup(): Unit =
    EmbeddedElasticService.ensureStarted()

  def elasticsearchClient: ElasticClient = EmbeddedElasticService.getClient

  def elasticsearchConnectionString: String = EmbeddedElasticService.getConnectionString
}
