package com.agoda.papi.search.complexsearch.model

import api.request.FeatureFlag
import com.agoda.common.graphql.model.Wrapper
import com.agoda.content.models.request.sf.OverrideContentRequest
import com.agoda.papi.enums.request.RequestedPrice
import com.agoda.platform.service.context.{ GlobalContext, ServiceHeaders }
import com.agoda.papi.search.common.util.CoreDataExamples._
import com.agoda.papi.search.common.util.DFDataExample._
import models.{ CheapestStayPackageRatePlans, DFCapacity, DFSuggestPriceType, HotelPricing }
import models.pricing.enums.{ ApplyTypes, PaymentModels }
import models.starfruit.{ ConsolidatedAppliedDiscount, Experiment => PricingExperiment, OverridePricingRequest }
import com.agoda.core.search.models
import com.agoda.core.search.models.builders.TestModelsBuilders.toCitySearchRequest
import com.agoda.core.search.models.enumeration.SearchStatuses
import com.agoda.core.search.models.request._
import models.{ response, CancellationTypes }
import com.agoda.papi.enums.hotel.StayPackageTypes
import com.agoda.papi.enums.room.ApplyType
import com.agoda.papi.search.common.util.{ CoreDataExamples, ModelHelper }
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextGenerator, ContextHolder }
import com.agoda.pricing.models.circe.Encoders.PricingSummaryRequestEncoder
import io.circe.syntax._
import com.agoda.papi.search.internalmodel.{ PropertySearchRequestWrapper, PropertySummaryEnvelope }
import com.agoda.papi.search.internalmodel.pricing.{ PAPIHotelPricing, PAPIPayLaterData, PAPIPulseCampaignMetadata }
import constant.ConsolidatedAppliedDiscountType.{ downliftDiscount, promoDiscount }
import io.circe.Json
import org.joda.time.{ DateTime, Days }
import org.scalatestplus.mockito.MockitoSugar
import org.specs2.mutable.Specification

class ModelHelperSpec extends Specification with MockitoSugar with ContextGenerator {

  implicit val searchContextHolder: ContextHolder = createContextHolder()

  "ModelHelper" should {
    "do not override check-in if search status is normal" in {
      val searchResult = CoreDataExamples.aValidSearchResult
      val request      = CoreDataExamples.aValidPropertySearchRequestWrapper

      val result = ModelHelper.overrideYesterdaySearchAndUserContext(request, searchResult)
      result.overridePricingRequest.flatMap(_.checkInDate) should_== None
      result.overridePricingRequest.flatMap(_.checkOutDate) should_== None
    }
    "override checkin checkout if yesterday search is not possible (search status is check in changed)" in {
      val newCheckIn = DateTime.now().plusDays(1)
      val newSearchStatus =
        response.SearchStatusAndCriteria(SearchStatuses.CheckInChanged, response.SearchCriteria(newCheckIn))
      val newSearchInfo = CoreDataExamples.aValidSearchResult.searchInfo.copy(searchStatus = Some(newSearchStatus))
      val searchResult  = CoreDataExamples.aValidSearchResult.copy(searchInfo = newSearchInfo)
      val request       = CoreDataExamples.aValidPropertySearchRequestWrapper

      val pricing = request.pricingRequestWrapper.map(_.t.pricing)

      val los         = pricing.map(req => Days.daysBetween(req.checkIn, req.checkout).getDays).getOrElse(0)
      val newCheckout = newCheckIn.plusDays(los)

      val result = ModelHelper.overrideYesterdaySearchAndUserContext(request, searchResult)
      result.overridePricingRequest.flatMap(_.checkInDate) should_== Some(newCheckIn)
      result.overridePricingRequest.flatMap(_.checkOutDate) should_== Some(newCheckout)
    }

    "Override ThaiGov CID correctly" in {
      ModelHelper.overrideCidForThaiGovCampaign(None, GlobalContext(Seq.empty), false, false) should_== None
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"))),
        false,
        false
      ) should_== Some(1605717)
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "A1"))),
        true,
        false
      ) should_== Some(1605717)
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgOrigin.toString, "TH"))),
        true,
        false
      ) should_== None
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "TH"))),
        true,
        false
      ) should_== Some(1881563)
      ModelHelper.overrideCidForThaiGovCampaign(
        Some(1881563),
        GlobalContext(Seq((ServiceHeaders.AgOrigin.toString, "TH"))),
        true,
        false
      ) should_== Some(1881563)
    }

    "isPricingForceBVariant" in {
      ModelHelper.isPricingForceBVariant(ActiveABTests().isThaiGovCampaign, Nil) should_== false
      ModelHelper.isPricingForceBVariant(
        ActiveABTests().isThaiGovCampaign,
        List(PricingExperiment("THAI-GOV", 'A'))
      ) should_== false
      ModelHelper.isPricingForceBVariant(
        ActiveABTests().isThaiGovCampaign,
        List(PricingExperiment("THAI-GOV", 'B'))
      ) should_== true
    }
    "isPAPIForceBVariant" in {
      ModelHelper.isPAPIForceBVariant(ActiveABTests().isThaiGovCampaign, None) should_== false
      ModelHelper.isPAPIForceBVariant(
        ActiveABTests().isThaiGovCampaign,
        Some(Vector(ForcedExperiment("THAI-GOV", "A")))
      ) should_== false
      ModelHelper.isPAPIForceBVariant(
        ActiveABTests().isThaiGovCampaign,
        Some(Vector(ForcedExperiment("THAI-GOV", "B")))
      ) should_== true
    }

    "Override ThaiGov CID correctly for Packages" in {
      ModelHelper.overrideCidForThaiGovCampaign(None, GlobalContext(Seq.empty), false, true) should_== None
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"))),
        false,
        true
      ) should_== Some(1605717)
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "A1"))),
        true,
        true
      ) should_== Some(1605717)
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgOrigin.toString, "TH"))),
        true,
        true
      ) should_== None
      ModelHelper.overrideCidForThaiGovCampaign(
        None,
        GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "TH"))),
        true,
        true
      ) should_== Some(1605717)
      ModelHelper.overrideCidForThaiGovCampaign(
        Some(1881563),
        GlobalContext(Seq((ServiceHeaders.AgOrigin.toString, "TH"))),
        true,
        true
      ) should_== Some(1605717)
    }
  }

  "ModelHelper.setIsApoRequest(...)" in {

    "return the same overrided requests with APO flag enabled" in {
      val propertySearchRequest: PropertySearchRequestWrapper = aValidPropertySearchRequestWrapper.copy(
        overridePricingRequest = Some(OverridePricingRequest(filterAPO = None, cid = Some(1))),
        overrideContentRequest = Some(OverrideContentRequest(apo = None))
      )
      val result = ModelHelper.setIsApoRequest(propertySearchRequest)

      result.overridePricingRequest should_== Some(OverridePricingRequest(filterAPO = Some(true), cid = Some(1)))
      result.overrideContentRequest should_== Some(OverrideContentRequest(apo = Some(true)))
    }
  }

  "ModelHelper.toPropertyInformation(...)" in {

    def expectedPropertyInformation: PropertyInformation = models.request.PropertyInformation(
      id = 1001L,
      isMSE = false,
      isReady = true,
      enableAPS = false,
      isRecommendedProperty = false,
      suggestedPrice = Some(RequestedPrice.Exclusive),
      availableChannels = Set.empty,
      availableBenefits = Seq.empty,
      payment = PaymentInfoSummary(
        taxReceipt = Some(false),
        payAtHotel = Some(false),
        payAtCheckin = None,
        payLater = Some(false),
        noCC = Some(false),
        cancellationType = Some(CancellationTypes.None),
        payNowEnable = Some(false)
      ),
      contentMasterRoomIds = Set.empty,
      contentCumulativeReviewScore = None,
      availableMasterRoomIds = Set.empty,
      cheapestRoomId = Some("1001"),
      childRooms = Seq(models.request.ChildRoomInfoSummary(12, "1001", 1, None, Some(Capacity()))),
      suggestedRooms = Seq.empty,
      isAPO = None,
      multiTypeRoomSuggestionSummaries = Seq.empty,
      hasASORate = None
    )

    "correctly construct simple PropertyInformation" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA
      val result                 = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result should_== expectedPropertyInformation
    }

    "return suggestedRooms instead of childRooms if DF's isSuggested is true" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              offers = pricing.t.offers.map(offer =>
                offer.copy(
                  roomOffers = offer.roomOffers.map(roomOffer =>
                    roomOffer.copy(
                      room = roomOffer.room.copy(isSuggested = true)
                    )
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.childRooms should_== Seq.empty[ChildRoomInfoSummary]
      result.suggestedRooms should_== Seq(models.request.ChildRoomInfoSummary(12, "1001", 1, None, Some(Capacity())))
    }

    "return payNowEnable as true if payment model of some rooms is Merchant" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              roomLevelPayLaterData = Some(
                Seq(
                  PAPIPayLaterData(
                    paymentModel = PaymentModels.Merchant
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.payment.payNowEnable should_== Some(true)
    }

    "return payNowEnable as true if payment model of some rooms is MerchantCommission" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              roomLevelPayLaterData = Some(
                Seq(
                  PAPIPayLaterData(
                    paymentModel = PaymentModels.MerchantCommission
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.payment.payNowEnable should_== Some(true)
    }

    "return hasASORate as true if there is any Escapes package on DF response" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              cheapestStayPackageRatePlans = Some(
                Seq(
                  CheapestStayPackageRatePlans(
                    stayPackageType = StayPackageTypes.Escapes.i,
                    ratePlanId = 0L
                  ),
                  CheapestStayPackageRatePlans(
                    stayPackageType = StayPackageTypes.NormalOffer.i,
                    ratePlanId = 0L
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.hasASORate should_== Some(true)
    }

    "return hasASORate as false if there is no Escapes package on DF response" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              cheapestStayPackageRatePlans = Some(
                Seq(
                  CheapestStayPackageRatePlans(
                    stayPackageType = StayPackageTypes.NormalOffer.i,
                    ratePlanId = 0L
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.hasASORate should_== Some(false)
    }

    "return isMSE as true if there is a cheapest room and its isMSEProperty is true" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              offers = pricing.t.offers.map(offer =>
                offer.copy(
                  roomOffers = offer.roomOffers.map(roomOffer =>
                    roomOffer.copy(
                      room = roomOffer.room.copy(isMseProperty = true)
                    )
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.isMSE should_== true
    }

    "return isMSE as false there is no cheapest room at all" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              offers = pricing.t.offers.map(offer =>
                offer.copy(
                  roomOffers = Seq.empty
                )
              )
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.isMSE should_== false
    }

    "return isReady as false if there is DF response is None" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(pricing = None)
      val result                 = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.isReady should_== false
    }

    "return hasPulsePromo as false if DF response is None" in {
      val propertySummaryEnvelope = propertySummaryEnvelopeA.copy(pricing = None)
      val result                  = ModelHelper.toPropertyInformation(propertySummaryEnvelope, None, "THB", false)
      result.hasPulsePromotion should_== false
    }

    "return hasPulsePromo as true if DF response has pulseCampaignMetadata" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              pulseCampaignMetadata = Some(PAPIPulseCampaignMetadata())
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.hasPulsePromotion should_== true
    }

    "return hasPulsePromo as false if DF response does not have pulseCampaignMetadata" in {
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              pulseCampaignMetadata = None
            ),
            pricing.json
          )
        )
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.hasPulsePromotion should_== false
    }

    "return perOfferFreeChildren and perOfferMaxFreeChildren correctly" in {
      val perOfferMaxFreeChildren = 2
      val perOfferFreeChildren    = 1
      val capacity = DFCapacity(
        extraBedsAvailable = false,
        perOfferMaxFreeChildren = Some(perOfferMaxFreeChildren),
        perOfferFreeChildren = Some(perOfferFreeChildren)
      )
      val offers = Seq(aValidPAPIDFBundleOffer(List(aValidPAPIRoomOffer(1234).copy(capacity = capacity))))
      val propertySummaryEnvelop = propertySummaryEnvelopeA.copy(
        pricing =
          propertySummaryEnvelopeA.pricing.map(pricing => Wrapper(pricing.t.copy(offers = offers), pricing.json))
      )
      val result = ModelHelper.toPropertyInformation(propertySummaryEnvelop, None, "THB", false)
      result.childRooms.head.capacity.get.perOfferMaxFreeChildren should_== perOfferMaxFreeChildren
      result.childRooms.head.capacity.get.perOfferFreeChildren should_== perOfferFreeChildren
    }

    "cheapestRoomHasAutoApplyWalletPromo" in {
      def getPropertySummaryEnvelopWithConsolidatedAppliedDiscount(
          consolidatedAppliedDiscount: Option[ConsolidatedAppliedDiscount] = None
      ): PropertySummaryEnvelope = propertySummaryEnvelopeA.copy(
        pricing = propertySummaryEnvelopeA.pricing.map(pricing =>
          Wrapper(
            pricing.t.copy(
              offers = pricing.t.offers.map(offer =>
                offer.copy(
                  roomOffers = offer.roomOffers.map(roomOffer =>
                    roomOffer.copy(
                      room = roomOffer.room.copy(
                        consolidatedAppliedDiscount = consolidatedAppliedDiscount
                      )
                    )
                  )
                )
              )
            ),
            pricing.json
          )
        )
      )

      "return as false when DF response doesn't have cheapest room with consolidated applied discount" in {
        val result = ModelHelper.toPropertyInformation(
          getPropertySummaryEnvelopWithConsolidatedAppliedDiscount(),
          None,
          "THB",
          isRecommended = false
        )
        result.cheapestRoomHasAutoApplyWalletPromo should_== false
      }

      "return as true when DF response have cheapest room with DL" in {
        val result = ModelHelper.toPropertyInformation(
          getPropertySummaryEnvelopWithConsolidatedAppliedDiscount(
            Some(aValidConsolidatedAppliedDiscount(downliftDiscount))
          ),
          None,
          "THB",
          isRecommended = false
        )
        result.cheapestRoomHasAutoApplyWalletPromo should_== true
      }

      "return as true when DF response has cheapest room with auto-applied promo" in {
        val result = ModelHelper.toPropertyInformation(
          getPropertySummaryEnvelopWithConsolidatedAppliedDiscount(
            Some(aValidConsolidatedAppliedDiscount(promoDiscount))
          ),
          None,
          "THB",
          isRecommended = false
        )
        result.cheapestRoomHasAutoApplyWalletPromo should_== true
      }
    }
  }

  "ModelHelper.toPricingPropertiesRequestEnvelope(...)" in {

    "when BLT cid is ThaiGovCampagin" in {

      "return overridePricingRequest as original overridePricingRequest if request is a valid ThaiGovCampaign" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(
            aPricingRequestParameters.copy(
              context = aPricingRequestParameters.context.copy(
                packaging = None,
                experiment = List(PricingExperiment(ActiveABTests().isThaiGovCampaign, 'B'))
              )
            ),
            Json.Null
          ),
          overridePricingRequest = Some(OverridePricingRequest(cid = Some(1), filterAPO = Some(true))),
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "TH"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(None).build()
        )
        result.overridePricingRequest should_== Some(OverridePricingRequest(cid = Some(1), filterAPO = Some(true)))
      }

      "return overridePricingRequest with Cid = MAPI if request is not a valid ThaiGovCampaign request (not in TH)" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(
            aPricingRequestParameters.copy(
              context = aPricingRequestParameters.context.copy(
                packaging = None,
                experiment = List(PricingExperiment(ActiveABTests().isThaiGovCampaign, 'B'))
              )
            ),
            Json.Null
          ),
          overridePricingRequest = Some(OverridePricingRequest(cid = Some(1881563), filterAPO = Some(true))),
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "NOT-TH"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(None).build()
        )
        result.overridePricingRequest.map(_.cid) should_== Some(Some(1605717))
        result.overridePricingRequest.map(_.filterAPO) should_== Some(Some(true))
      }

      "return overridePricingRequest with Cid = MAPI if request is a valid ThaiGovCampaign request but ThaiGovCampagin is not active" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(aPricingRequestParameters, Json.Null),
          overridePricingRequest = Some(OverridePricingRequest(cid = Some(1881563), filterAPO = Some(true))),
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "TH"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(Some(Packaging(None))).build()
        )
        result.overridePricingRequest.map(_.cid) should_== Some(Some(1605717))
        result.overridePricingRequest.map(_.filterAPO) should_== Some(Some(true))
      }

      "return overridePricingRequest with Cid = MAPI if request is not a valid ThaiGovCampaign request (request with packaging)" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(
            aPricingRequestParameters.copy(
              context = aPricingRequestParameters.context.copy(
                experiment = List(PricingExperiment(ActiveABTests().isThaiGovCampaign, 'B'))
              )
            ),
            Json.Null
          ),
          overridePricingRequest = Some(OverridePricingRequest(cid = Some(1881563), filterAPO = Some(true))),
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "TH"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(Some(Packaging(None))).build()
        )
        result.overridePricingRequest.map(_.cid) should_== Some(Some(1605717))
        result.overridePricingRequest.map(_.filterAPO) should_== Some(Some(true))
      }

      "return new overridePricingRequest with Cid = MAPI and filterAPO = None if original overridePricingRequest is None and request is not a valid ThaiGovCampaign request" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(
            aPricingRequestParameters.copy(
              context = aPricingRequestParameters.context.copy(
                experiment = List(PricingExperiment(ActiveABTests().isThaiGovCampaign, 'B'))
              )
            ),
            Json.Null
          ),
          overridePricingRequest = None,
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1881563"), (ServiceHeaders.AgOrigin.toString, "TH"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(Some(Packaging(None))).build()
        )
        result.overridePricingRequest.map(_.cid) should_== Some(Some(1605717))
        result.overridePricingRequest.map(_.filterAPO) should_== Some(None)
      }

    }

    "when BLT cid is not ThaiGovCampagin" in {

      "return overridePricingRequest as None" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(aPricingRequestParameters, Json.Null),
          overridePricingRequest = None,
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(None).build()
        )
        result.overridePricingRequest should_== None
      }

      "return original overridePricingRequest" in {
        val result = ModelHelper.toPricingPropertiesRequestEnvelope(
          propertyIds = List(1001L, 1002L, 1003L, 1004L),
          pricingRequestWrapper = Wrapper(aPricingRequestParameters, Json.Null),
          overridePricingRequest = Some(OverridePricingRequest(cid = Some(1), filterAPO = Some(true))),
          GlobalContext(Seq((ServiceHeaders.AgCid.toString, "1"))),
          toCitySearchRequest(aValidCitySearchRequest).withPackaging(None).build()
        )
        result.overridePricingRequest should_== Some(OverridePricingRequest(cid = Some(1), filterAPO = Some(true)))
      }

    }
  }

  "ModelHelper.toNotAvailableHotelPricingWrapper(...)" in {

    "create a correct not-available hotel pricing (DF Response)" in {
      import com.agoda.pricing.models.circe.ResponseEncoder
      import io.circe.syntax._
      val encodingObj                  = new ResponseEncoder {}
      implicit val hotelPricingDecoder = encodingObj.HotelPricingEncoder

      val result = ModelHelper.toNotAvailableHotelPricingWrapper(1001L)
      val expectedHotelPricing = HotelPricing(
        hotelId = 1001L,
        isReady = true,
        isAvailable = false,
        pointmax = None,
        cheapestRoomOffer = None,
        roomBundle = None,
        suppliersSummary = None,
        suppliersSummaries = Nil,
        supplierInfo = None,
        suggestedRoomQuantity = 0,
        suggestPriceType = DFSuggestPriceType(_root_.models.pricing.enums.RequestedPrice.AllInclusive, ApplyTypes.PB),
        isSuggested = false,
        isMultiHotelEligible = false,
        isPackageEligible = false,
        isCartEligible = false
      )
      result.t should_== PAPIHotelPricing(
        hotelId = 1001L,
        isReady = true,
        isAvailable = false,
        cheapestRoomOffer = None,
        suppliersSummary = None,
        suppliersSummaries = Nil,
        supplierInfo = None,
        suggestedRoomQuantity = 0,
        suggestPriceType = DFSuggestPriceType(_root_.models.pricing.enums.RequestedPrice.AllInclusive, ApplyTypes.PB)
      )
      result.json should_== expectedHotelPricing.asJson
    }

  }

  "ModelHelper.isPAPIForceBVariant(...)" in {
    "return false if there is no forcedExperiments at all" in {
      ModelHelper.isPAPIForceBVariant("TEST-EXP", Some(Vector.empty)) should_== false
    }
    "return true if the input experiment is forced" in {
      val notForcedExp = ForcedExperiment(id = "TEST-EXP-1", variant = "A")
      val forcedExp    = ForcedExperiment(id = "TEST-EXP-2", variant = "B")
      ModelHelper.isPAPIForceBVariant("TEST-EXP-2", Some(Vector(notForcedExp, forcedExp))) should_== true
    }
  }

  "ModelHelper.overrideDfRequestForFlexibleRoomSearch(...)" in {
    "Add additional feature flag to DF request if there is feature flag request for flexible multi room search " +
      "and override df request is null" in {

        val request = aValidPropertySearchRequestWrapper.copy(
          searchRequest = aValidCitySearchRequest.copy(
            searchRequest = ComplexSearchRequest(
              aValidSearchCriteria.copy(
                featureFlagRequest = Some(FeatureFlagRequest(isFlexibleMultiRoomSearch = Some(true)))
              ),
              aValidSearchContext,
              None,
              aValidFilterRequest,
              None,
              None,
              aValidPage
            )
          )
        )

        val result = ModelHelper.overrideDfRequestForFlexibleRoomSearch(request)
        result.overridePricingRequest.flatMap(_.additionalFeatureFlags) should_== Some(
          List(FeatureFlag.FlexibleMultiRoom)
        )
      }

    "Add additional feature flag to DF request if there is feature flag request for flexible multi room search" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              featureFlagRequest = Some(FeatureFlagRequest(isFlexibleMultiRoomSearch = Some(true)))
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.overrideDfRequestForFlexibleRoomSearch(request)
      result.overridePricingRequest.flatMap(_.additionalFeatureFlags) should_== Some(
        List(FeatureFlag.FlexibleMultiRoom, FeatureFlag.EnableCashback)
      )
    }
  }

  "ModelHelper.overrideDfRequestForRequiredBasis(...)" in {

    "Add requiredBasis to DF request if enableOverrideRequiredBasis = true and no requiredBasis in PricingRequest" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              requiredBasis = Some(ApplyType.PB)
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.overrideDfRequestForRequiredBasis(
        request,
        enableOverrideRequiredBasis = true
      )
      result.overridePricingRequest should_== Some(
        OverridePricingRequest(
          filterAPO = None,
          additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)),
          requiredBasis = Some(ApplyTypes.PB)
        )
      )
    }

    "Create new override request and add requiredBasis to DF request if enableOverrideRequiredBasis = true and no requiredBasis in PricingRequest" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              requiredBasis = Some(ApplyType.PB)
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = None
      )
      val result = ModelHelper.overrideDfRequestForRequiredBasis(
        request,
        enableOverrideRequiredBasis = true
      )
      result.overridePricingRequest should_== Some(
        OverridePricingRequest(
          filterAPO = None,
          requiredBasis = Some(ApplyTypes.PB)
        )
      )
    }

    "Do not add requiredBasis to DF request if has requiredBasis in PricingRequest" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              requiredBasis = Some(ApplyType.PB)
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        pricingRequestWrapper = Some(
          pricingRequestParametersWrapper.copy(
            t = aPricingRequestParameters.copy(
              pricing = aPricingRequest.copy(requiredBasis = Some(ApplyTypes.PRPN))
            )
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.overrideDfRequestForRequiredBasis(
        request,
        enableOverrideRequiredBasis = true
      )
      result.overridePricingRequest should_== Some(
        OverridePricingRequest(
          filterAPO = None,
          additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback))
        )
      )
    }

    "Do not add requiredBasis to DF request if enableOverrideRequiredBasis = false" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              requiredBasis = Some(ApplyType.PB)
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.overrideDfRequestForRequiredBasis(
        request,
        enableOverrideRequiredBasis = false
      )
      result.overridePricingRequest should_== Some(
        OverridePricingRequest(
          filterAPO = None,
          additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback))
        )
      )
    }
  }

  "ModelHelper.updatePropertySearchRequestWrapper(...)" in {

    "Add additional feature flag to DF request if isFlexibleMultiRoomSearch = true" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              featureFlagRequest = Some(FeatureFlagRequest(isFlexibleMultiRoomSearch = Some(true)))
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.updatePropertySearchRequestWrapper(
        request,
        aValidSearchResult,
        enableOverrideRequiredBasis = false
      )
      result.overridePricingRequest.flatMap(_.additionalFeatureFlags) should_== Some(
        List(FeatureFlag.FlexibleMultiRoom, FeatureFlag.EnableCashback)
      )
    }

    "Do not add additional feature flag to DF request if isFlexibleMultiRoomSearch = false " in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              featureFlagRequest = Some(FeatureFlagRequest(isFlexibleMultiRoomSearch = Some(false)))
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.updatePropertySearchRequestWrapper(
        request,
        aValidSearchResult,
        enableOverrideRequiredBasis = false
      )
      result.overridePricingRequest.flatMap(_.additionalFeatureFlags) should_== Some(
        List(FeatureFlag.EnableCashback)
      )
    }

    "Add requiredBasis to DF request if enableOverrideRequiredBasis = true" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              requiredBasis = Some(ApplyType.PB)
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.updatePropertySearchRequestWrapper(
        request,
        aValidSearchResult,
        enableOverrideRequiredBasis = true
      )
      result.overridePricingRequest should_== Some(
        OverridePricingRequest(
          filterAPO = None,
          additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)),
          requiredBasis = Some(ApplyTypes.PB)
        )
      )
    }

    "Do not add requiredBasis to DF request if enableOverrideRequiredBasis = false" in {

      val request = aValidPropertySearchRequestWrapper.copy(
        searchRequest = aValidCitySearchRequest.copy(
          searchRequest = ComplexSearchRequest(
            aValidSearchCriteria.copy(
              requiredBasis = Some(ApplyType.PB)
            ),
            aValidSearchContext,
            None,
            aValidFilterRequest,
            None,
            None,
            aValidPage
          )
        ),
        overridePricingRequest = Some(
          OverridePricingRequest(additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback)), filterAPO = None)
        )
      )
      val result = ModelHelper.updatePropertySearchRequestWrapper(
        request,
        aValidSearchResult,
        enableOverrideRequiredBasis = false
      )
      result.overridePricingRequest should_== Some(
        OverridePricingRequest(
          filterAPO = None,
          additionalFeatureFlags = Some(List(FeatureFlag.EnableCashback))
        )
      )
    }
  }

  "ModelHelper.filterReturnHotelNotReadyIfPullNotReadyFlag" in {

    "Filter out ReturnHotelNotReadyIfPullNotReady flag from main pricing request on B side" in {
      val pricingRequest = aPricingRequest.copy(featureFlag =
        List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)
      )
      val pricingRequestParameters = aPricingRequestParameters.copy(pricing = pricingRequest)
      val pricingWrapper           = Wrapper(pricingRequestParameters, pricingRequestParameters.asJson)

      val request = aValidPropertySearchRequestWrapper.copy(
        pricingRequestWrapper = Some(pricingWrapper)
      )

      val contextHolderB = createContextHolder(forceExperimentNames = Some(Seq("FUSION-6248")))
      val result         = ModelHelper.filterReturnHotelNotReadyIfPullNotReadyFlag(request)(contextHolderB)

      result.pricingRequestWrapper.get.t.pricing.featureFlag should not contain FeatureFlag.ReturnHotelNotReadyIfPullNotReady
      result.pricingRequestWrapper.get.t.pricing.featureFlag should contain(FeatureFlag.EnableCashback)
    }

    "Filter out ReturnHotelNotReadyIfPullNotReady flag from additionalFeatureFlags in overridePricingRequest on B side" in {
      val request = aValidPropertySearchRequestWrapper.copy(
        overridePricingRequest = Some(
          OverridePricingRequest(
            additionalFeatureFlags =
              Some(List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)),
            filterAPO = None
          )
        )
      )

      val contextHolderB = createContextHolder(forceExperimentNames = Some(Seq("FUSION-6248")))
      val result         = ModelHelper.filterReturnHotelNotReadyIfPullNotReadyFlag(request)(contextHolderB)

      result.overridePricingRequest.flatMap(_.additionalFeatureFlags) should_== Some(List(FeatureFlag.EnableCashback))
    }

    "Keep ReturnHotelNotReadyIfPullNotReady flag on A side" in {
      val pricingRequest = aPricingRequest.copy(featureFlag =
        List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)
      )
      val pricingRequestParameters = aPricingRequestParameters.copy(pricing = pricingRequest)
      val pricingWrapper           = Wrapper(pricingRequestParameters, pricingRequestParameters.asJson)

      val request = aValidPropertySearchRequestWrapper.copy(
        pricingRequestWrapper = Some(pricingWrapper),
        overridePricingRequest = Some(
          OverridePricingRequest(
            additionalFeatureFlags =
              Some(List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)),
            filterAPO = None
          )
        )
      )

      val contextHolderA = createContextHolder(forceExperimentNames = None)
      val result         = ModelHelper.filterReturnHotelNotReadyIfPullNotReadyFlag(request)(contextHolderA)

      result.pricingRequestWrapper.get.t.pricing.featureFlag should_==
        List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)

      result.pricingRequestWrapper.get.t.pricing.featureFlag should contain(FeatureFlag.EnableCashback)
      result.overridePricingRequest.flatMap(_.additionalFeatureFlags) should_== Some(
        List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)
      )
    }
  }

}
