package com.agoda.property.search.framework.binding.module

import com.agoda.commons.http.client.v2.RequestSettings
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.core.search.mocks.{ PageTokenServiceDefaultTest, PriceStreamFetcherServiceDefaultTest }
import com.agoda.npc.client.NpcHttpClient
import com.agoda.papi.search.common.externaldependency.cms.GQLCMSService
import com.agoda.papi.search.common.externaldependency.content.{
  EngagementExternalService,
  EngagementService,
  EngagementServiceImpl,
  ExperiencesExternalService,
  ExperiencesService,
  ExperiencesServiceImpl,
  FeaturesExternalService,
  FeaturesService,
  FeaturesServiceImpl,
  HighlightsExternalService,
  HighlightsService,
  HighlightsServiceImpl,
  ImageExternalService,
  ImageService,
  ImageServiceImpl,
  InformationExternalService,
  InformationService,
  InformationServiceImpl,
  InformationSummaryExternalService,
  InformationSummaryService,
  InformationSummaryServiceImpl,
  LocalInformationExternalService,
  LocalInformationService,
  LocalInformationServiceImpl,
  QnaExternalService,
  QnaService,
  QnaServiceImpl,
  RateCategoryExternalService,
  RateCategoryService,
  RateCategoryServiceImpl,
  ReviewScoreExternalService,
  ReviewScoreService,
  ReviewScoreServiceImpl,
  ReviewSummariesExternalService,
  ReviewSummariesService,
  ReviewSummariesServiceImpl,
  TopicsExternalService,
  TopicsService,
  TopicsServiceImpl
}
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.externaldependency.repository.{
  ExchangeDataRepository,
  LanguageRepository,
  SoldOutPropsPriceRepository
}
import com.agoda.papi.search.common.framework.mock.{
  CAPIServiceTest,
  ContentServiceDefaultTest,
  DFServiceDefaultTest,
  GrowthProgramClientTest
}
import com.agoda.papi.search.common.handler.datafetcher.pricestream.PriceStreamFetcherService
import com.agoda.papi.search.common.externaldependency.growthprogram.{ GrowthProgramService, GrowthProgramServiceImpl }
import com.agoda.papi.search.common.externaldependency.promocode.{
  GandalfService,
  GandalfServiceImpl,
  PromoAgHttpClient
}
import com.agoda.papi.search.common.service.{
  ExchangeDataServiceImpl,
  LocaleService,
  PropertyMetaService,
  PropertyMetaServiceImpl,
  RegulatoryBlockingService,
  RocketmilesRankingExperimentService,
  WhiteLabelService
}
import com.agoda.papi.search.common.util.circuitbreaker.{ CircuitBreakerWrapper, NoOpCircuitBreakerWrapper }
import com.agoda.papi.search.common.util.context.{
  DefaultGlobalContextService,
  ExecutionContexts,
  GlobalContextService
}
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.agoda.property.search.framework.defaultmock.corelib.services.SupplierMetadataServiceDefaultTest
import com.agoda.property.search.framework.defaultmock.services._
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import com.agoda.papi.search.complexsearch.common.service.LanguageService
import com.agoda.papi.search.complexsearch.configuration.PageSearchSettings
import com.agoda.papi.search.complexsearch.externaldependency.repository.NumberOfReturnCustomerRepository
import com.agoda.papi.search.complexsearch.graphql.resolver.{
  PriceTrendSearchResolver,
  PropertyAllotmentSearchResolver,
  ResolverLevel1,
  ResolverLevel2,
  ResolverLevel3,
  ResolverLevel4
}
import com.agoda.papi.search.complexsearch.service.agodahome.{
  ExtraAgodaHomesService,
  ExtraAgodaHomesServiceImpl,
  FeaturedAgodaHomesService,
  FeaturedAgodaHomesServiceImpl,
  HighlyRatedAgodaHomesService,
  HighlyRatedAgodaHomesServiceImpl
}
import com.agoda.papi.search.complexsearch.service.enrichment.{ PopularEnrichService, PopularEnrichServiceImpl }
import com.agoda.papi.search.complexsearch.service.packaging.{
  PackageDealsSortingService,
  PackageDealsSortingServiceImpl
}
import com.agoda.papi.search.complexsearch.service.paging.PageTokenService
import com.agoda.papi.search.complexsearch.service.pricingbadge.{ PricingBadgesService, PricingBadgesServiceImpl }
import com.agoda.papi.search.complexsearch.service.rocketmiles.RocketmilesRankingService
import com.agoda.papi.search.complexsearch.service.searchenrichment.{
  BenefitInformationService,
  BenefitInformationServiceImpl,
  CampaignInformationService,
  CampaignInformationServiceImpl,
  SupplierInformationService,
  SupplierInformationServiceImpl
}
import com.agoda.papi.search.complexsearch.service.{
  APOService,
  APOServiceImpl,
  BookingHistoryService,
  BookingHistoryServiceImpl,
  DataFetcher,
  DatelessPropertySearchService,
  DatelessPropertySearchServiceImpl,
  DefaultDataFetcher,
  DefaultPriceStreamDataFetcher,
  DefaultPriceTrendSummaryDataFetcher,
  DefaultPropertyAllotmentSummaryDataFetcher,
  DistanceEnrichService,
  DistanceEnrichServiceImpl,
  EnrichmentService,
  EnrichmentServiceImpl,
  ExtraHotelServices,
  ExtraHotelServicesImpl,
  GeoService,
  GeoServiceImpl,
  LuxuryHotelsService,
  LuxuryHotelsServiceImpl,
  PagedDatelessPropertyService,
  PagedDatelessPropertyServiceImpl,
  PagedPropertyService,
  PagingServiceImpl,
  PriceStreamDataFetcher,
  PriceTrendSummaryDataFetcher,
  PropertyAllotmentSummaryDataFetcher,
  PropertyMergerService,
  PropertyMergerServiceImpl,
  PropertySearchService,
  PropertySearchServiceImpl,
  PulsePropertiesService,
  PulsePropertiesServiceImpl,
  ReviewSnippetService,
  ReviewSnippetServiceImpl,
  RoomSummaryService,
  RoomSummaryServiceImpl,
  SearchService,
  SearchServiceImpl,
  SoldOutPropertyService,
  SoldOutPropertyServiceImpl,
  SortingServiceImpl,
  TopSellingPointService,
  TopSellingPointServiceImpl,
  UniqueSellingPointService,
  UniqueSellingPointServiceImpl
}
import com.agoda.papi.search.internalmodel.enumeration.{ PropertyResultType, PropertyResultTypes }
import com.agoda.papi.search.common.util.DFDataExample.mock
import com.agoda.papi.search.complexsearch.{ ContentService, DFService, MetaLabService, RoomSummaryExternalService }
import com.agoda.papi.search.complexsearch.externaldependency.npc.NpcClient
import com.agoda.papi.search.complexsearch.service.citi.{ CitiRankingService, CitiRankingServiceImpl }
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.util.RocketmilesRankingExpUtils
import com.agoda.supplier.common.services.SupplierMetadataService
import com.agoda.supply.growth.program.client.GrowthProgramClient
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import io.opentracing.Tracer
import org.specs2.mock.Mockito
import org.specs2.mock.mockito.MockitoMatchers
import scaldi.Module

import scala.concurrent.Future

private[binding] trait GqlServiceTestModule extends Module {
  implicit val defaultEC = ExecutionContexts.defaultContext

  /** External Service Call */
  bind[ContentService] to new ContentServiceDefaultTest
  bind[DFService] to new DFServiceDefaultTest
  bind[MetaLabService] to new MetaLabServiceDefaultTest
  bind[ImageExternalService] to new ImageExternalServiceDefaultTest
  bind[ReviewScoreExternalService] to new ReviewScoreExternalServiceDefaultTest
  bind[ReviewSummariesExternalService] to new ReviewSummariesExternalServiceDefaultTest
  bind[InformationSummaryExternalService] to new InformationSummaryExternalServiceDefaultTest
  bind[InformationExternalService] to new InformationExternalServiceDefaultTest
  bind[LocalInformationExternalService] to new LocalInformationExternalServiceDefaultTest
  bind[EngagementExternalService] to new EngagementExternalServiceDefaultTest
  bind[ExperiencesExternalService] to new ExperiencesExternalServiceDefaultTest
  bind[FeaturesExternalService] to new FeaturesExternalServiceDefaultTest
  bind[HighlightsExternalService] to new HighlightsExternalServiceDefaultTest
  bind[QnaExternalService] to new QnaExternalServiceDefaultTest
  bind[TopicsExternalService] to new TopicsExternalServiceDefaultTest
  bind[RoomSummaryExternalService] to new RoomSummaryExternalServiceDefaultTest
  bind[RateCategoryExternalService] to new RateCategoryExternalServiceDefaultTest
  bind[CAPIService] to new CAPIServiceTest
  bind[GrowthProgramClient] to new GrowthProgramClientTest

  /** Service Implement Mock
    */
  bind[APOService] to new APOServiceDefaultTest
  bind[GQLCMSService] to new GQLCMSServiceDefaultTest
  bind[RocketmilesRankingService] to new RocketmilesRankingServiceNoOp
  bind[PackageDealsSortingService] to new PackageDealsSortingServiceImpl {}
  bind[PageTokenService] to new PageTokenServiceDefaultTest
  bind[WhiteLabelClientMultiEnv] to Mockito.mock[WhiteLabelClientMultiEnv]
  bind[GlobalContextService] to injected[DefaultGlobalContextService]
  bind[RegulatoryBlockingService] to Mockito.mock[RegulatoryBlockingService]

  bind[FeaturedAgodaHomesService] to injected[FeaturedAgodaHomesServiceImpl](
    'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
  )

  bind[HighlyRatedAgodaHomesService] to injected[HighlyRatedAgodaHomesServiceImpl](
    'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
  )

  bind[ExtraAgodaHomesService] to injected[ExtraAgodaHomesServiceImpl](
    'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
  )

  bind[LuxuryHotelsService] to injected[LuxuryHotelsServiceImpl](
    'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
  )

  bind[PulsePropertiesService] to injected[PulsePropertiesServiceImpl](
    'propertySearchService -> inject[PropertySearchService](identified by 'PropertySearchService)
  )

  bind[SearchService] to injected[SearchServiceImpl]
  binding.identifiedBy('PropertySearchService) to injected[PropertySearchServiceImpl]

  bind[CitiRankingService] to new CitiRankingServiceImpl

  binding.identifiedBy('PagedPropertyService) to new PagingServiceImpl with SortingServiceImpl with NopContextMetrics {
    override val propertySearchService: PropertySearchService =
      inject[PropertySearchService](identified by 'PropertySearchService)
    override val rocketmilesRankingService: RocketmilesRankingService = inject[RocketmilesRankingService]
    override val priceStreamHttpClient: PriceStreamHttpClient[Future] = inject[PriceStreamHttpClient[Future]]
    override val pageConfig: PageSearchSettings                       = inject[PageSearchSettings]
    override val searchService: SearchService                         = inject[SearchService]
    override val priceStreamRequestSettings: RequestSettings =
      inject[RequestSettings](identified by 'PriceStreamRequestSetting)
    override val packageDealsSortingService: PackageDealsSortingService = inject[PackageDealsSortingService]
    override val pageTokenService: PageTokenService                     = inject[PageTokenService]

    override def getPropertyResultType: PropertyResultType = PropertyResultTypes.NormalProperty

    override val metricsReporter: MetricsReporter     = inject[MetricsReporter]
    override val reportingService: MetricsReporter    = inject[MetricsReporter]
    override val whitelabelService: WhiteLabelService = inject[WhiteLabelService]
    override val rocketmilesRankingExperimentService: RocketmilesRankingExperimentService =
      inject[RocketmilesRankingExperimentService]
    override val rocketmilesRankingExpUtils: RocketmilesRankingExpUtils = inject[RocketmilesRankingExpUtils]
    override val citiRankingService: CitiRankingService                 = inject[CitiRankingService]
  }

  binding.identifiedBy('DatelessPropertySearchService) to new DatelessPropertySearchServiceImpl(
    inject[GlobalContextService],
    inject[ContentService],
    inject[SoldOutPropsPriceRepository],
    new ExchangeDataServiceImpl {
      override val exchangeDataRepository: ExchangeDataRepository = inject[ExchangeDataRepository]
    },
    inject[Tracer]
  )

  binding.identifiedBy('PagedDatelessPropertyService) to new PagedDatelessPropertyServiceImpl(
    inject[DatelessPropertySearchService](identified by 'DatelessPropertySearchService),
    inject[PriceStreamHttpClient[Future]],
    inject[PageSearchSettings],
    inject[SearchService],
    inject[RequestSettings](identified by 'PriceStreamRequestSetting),
    inject[RocketmilesRankingExperimentService]
  ) with SortingServiceImpl with NopContextMetrics {
    override val packageDealsSortingService: PackageDealsSortingService = inject[PackageDealsSortingService]
    override val rocketmilesRankingService: RocketmilesRankingService   = inject[RocketmilesRankingService]
    override val pageTokenService: PageTokenService                     = inject[PageTokenService]
    override val metricsReporter: MetricsReporter                       = NoOpMetricsReporter
    override val whitelabelService: WhiteLabelService                   = inject[WhiteLabelService]
    override val rocketmilesRankingExpUtils: RocketmilesRankingExpUtils = inject[RocketmilesRankingExpUtils]
    override val citiRankingService: CitiRankingService                 = inject[CitiRankingService]
  }

  bind[PropertyMetaService] to injected[PropertyMetaServiceImpl]
  bind[PropertyMergerService] to injected[PropertyMergerServiceImpl]
  bind[TopSellingPointService] to injected[TopSellingPointServiceImpl]

  bind[ReviewSnippetService] to new ReviewSnippetServiceImpl {
    override val localeService: LocaleService = Mockito.mock[LocaleService]
  }

  bind[UniqueSellingPointService] to injected[UniqueSellingPointServiceImpl]
  bind[BookingHistoryService] to injected[BookingHistoryServiceImpl]
  bind[EnrichmentService] to injected[EnrichmentServiceImpl]
  bind[SupplierInformationService] to injected[SupplierInformationServiceImpl]
  bind[BenefitInformationService] to injected[BenefitInformationServiceImpl]
  bind[APOService] to injected[APOServiceImpl]
  bind[DistanceEnrichService] to injected[DistanceEnrichServiceImpl]

  // TODO: Refactor Code below
  bind[PriceStreamHttpClient[Future]] to Mockito.mock[PriceStreamHttpClient[Future]]
  bind[GeoService] to new GeoServiceImpl(mock[NpcClient], new LanguageService(mock[LanguageRepository]))
  bind[CampaignInformationService] to new CampaignInformationServiceImpl(mock[NpcClient])

  bind[ExtraHotelServices] to new ExtraHotelServicesImpl(
    inject[PropertySearchService](identified by 'PropertySearchService),
    inject[SoldOutPropsPriceRepository],
    inject[ExchangeDataRepository],
    NoOpMetricsReporter,
    NoOpTracer
  )

  bind[SoldOutPropertyService] to injected[SoldOutPropertyServiceImpl](
    'propertyService -> inject[PropertySearchService](identified by 'PropertySearchService)
  )

  bind[ImageService] to injected[ImageServiceImpl]
  bind[ReviewScoreService] to injected[ReviewScoreServiceImpl]
  bind[LocalInformationService] to injected[LocalInformationServiceImpl]
  bind[ExperiencesService] to injected[ExperiencesServiceImpl]
  bind[ReviewSummariesService] to injected[ReviewSummariesServiceImpl]
  bind[InformationSummaryService] to injected[InformationSummaryServiceImpl]
  bind[InformationService] to injected[InformationServiceImpl]
  bind[EngagementService] to injected[EngagementServiceImpl]
  bind[FeaturesService] to injected[FeaturesServiceImpl]
  bind[HighlightsService] to injected[HighlightsServiceImpl]
  bind[QnaService] to injected[QnaServiceImpl]
  bind[TopicsService] to injected[TopicsServiceImpl]
  bind[RoomSummaryService] to injected[RoomSummaryServiceImpl]
  bind[RateCategoryService] to injected[RateCategoryServiceImpl]

  bind[GandalfService] to new GandalfServiceImpl(
    mock[PromoAgHttpClient[Future]],
    mock[CircuitBreakerWrapper],
    mock[GQLCMSService]
  )

  bind[GrowthProgramService] to new GrowthProgramServiceImpl {
    override val gpClient: GrowthProgramClient         = inject[GrowthProgramClient]
    override val circuitBreaker: CircuitBreakerWrapper = NoOpCircuitBreakerWrapper
    override def reportingService: MetricsReporter     = NoOpMetricsReporter
    override def tracer: Tracer                        = NoOpTracer
  }

  bind[PopularEnrichService] to new PopularEnrichServiceImpl {

    override protected val returnCustomerRepository: NumberOfReturnCustomerRepository = {
      val repository = Mockito.mock[NumberOfReturnCustomerRepository]
      Mockito
        .doReturn(Future.successful(Map.empty))
        .when(repository)
        .getNumberOfReturnCustomer(MockitoMatchers.any())(MockitoMatchers.any())
      repository
    }

  }

  /** Data Fetchers */
  bind[DataFetcher] to injected[DefaultDataFetcher](
    'pagingService   -> inject[PagedPropertyService](identified by 'PagedPropertyService),
    'datelessService -> inject[PagedDatelessPropertyService](identified by 'PagedDatelessPropertyService)
  )

  bind[PriceStreamDataFetcher] to injected[DefaultPriceStreamDataFetcher]

  bind[PriceTrendSummaryDataFetcher] to injected[DefaultPriceTrendSummaryDataFetcher]
  bind[PropertyAllotmentSummaryDataFetcher] to injected[DefaultPropertyAllotmentSummaryDataFetcher]

  /** * Service from Property-Core lib
    */
  bind[PriceStreamFetcherService] to new PriceStreamFetcherServiceDefaultTest

  /** Service from Supplier common lib
    */
  bind[SupplierMetadataService] to new SupplierMetadataServiceDefaultTest

  /** Pricing Badges
    */
  bind[PricingBadgesService] to PricingBadgesServiceImpl

  /** Resolvers */
  bind[ResolverLevel1] to injected[ResolverLevel1]
  bind[ResolverLevel2] to injected[ResolverLevel2]
  bind[ResolverLevel3] to injected[ResolverLevel3]
  bind[ResolverLevel4] to injected[ResolverLevel4]
  bind[PropertyAllotmentSearchResolver] to injected[PropertyAllotmentSearchResolver]
  bind[PriceTrendSearchResolver] to injected[PriceTrendSearchResolver]
}
