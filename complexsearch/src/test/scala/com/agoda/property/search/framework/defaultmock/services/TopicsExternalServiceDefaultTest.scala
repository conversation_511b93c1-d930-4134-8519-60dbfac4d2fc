package com.agoda.property.search.framework.defaultmock.services

import com.agoda.common.graphql.model.Wrapper
import com.agoda.papi.content.graphql.ContentGroupTopicsRequestEnvelope
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext }
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.externaldependency.content.TopicsExternalService
import com.agoda.papi.search.common.util.ContentTopicsDataExample
import com.agoda.platform.service.context.GlobalContext

import scala.concurrent.Future

class TopicsExternalServiceDefaultTest extends TopicsExternalService {

  override def process(
      request: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
    Future.successful(
      List(
        Wrapper(
          ContentWithPropertyId(ContentTopicsDataExample.aValidContentTopicsResponse.propertyId),
          ContentTopicsDataExample.jsonContentTopicsResponse
        )
      )
    )

}
