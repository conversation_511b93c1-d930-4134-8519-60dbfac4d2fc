package com.agoda.property.search.utils

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.circe.Serialization.{ ContentInformationRequestEncoder, InformationResponseEncoder }
import com.agoda.content.models.db.information.{ InformationResponse, Notes }
import com.agoda.content.models.db.policy.HotelPolicies
import com.agoda.content.models.request.sf.ContentInformationRequest
import com.agoda.papi.search.complexsearch.PropertyId
import io.circe.parser.parse

import scala.collection.immutable.Vector

object ContentInformationResponseDataExample {

  private val information = InformationResponse(
    propertyId = 1,
    description = None,
    usefulInfoGroups = Option(Vector()),
    notes = Option(Notes(Option(Vector()), Option(Vector()), Option(Vector()), Option(Vector()))),
    entertainment = Option(Vector()),
    numberOfRoom = None,
    policies =
      Option(HotelPolicies(None, Option(Vector()), Option(Vector()), Option(Vector()), Option(Vector()), None)),
    spokenLanguages = Option(Vector()),
    messaging = None,
    reception = None,
    blockedNationalities = Option(Vector()),
    contact = None,
    local = None,
    chainId = None,
    childrenStayFreeTypeId = Option(0),
    formerlyName = None,
    localizations = None,
    isAgodaVerified = None,
    certificate = None,
    staffVaccinationInfo = None,
    characteristicTopics = None,
    sustainabilityInfo = None,
    nightStayInfo = None,
    companyTraceabilityInfo = None,
    companyTraceabilityData = None,
    dsaComplianceInfo = None
  )

  val jsonContentInformationResponse = parse(
    circeSerializer(InformationResponseEncoder).serialize(information)
  ).right.get

  val contentInformationWrapperList = List(Wrapper(information, jsonContentInformationResponse))

  val contentInformationResponseWrapperMap: Map[PropertyId, Wrapper[InformationResponse]] = Map(
    1L -> Wrapper(information, jsonContentInformationResponse)
  )

  private val contentInformationRequest = ContentInformationRequest(None, None, None, None, None, None, None)

  private val jsonContentInformationRequest = parse(
    circeSerializer(ContentInformationRequestEncoder).serialize(contentInformationRequest)
  ).right.get

  val contentInformationRequestWrapper: Option[Wrapper[ContentInformationRequest]] = Option(
    Wrapper(contentInformationRequest, jsonContentInformationRequest)
  )

}
