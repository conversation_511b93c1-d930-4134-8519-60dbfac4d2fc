package com.agoda.property.search.framework

import cats.MonadError
import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.content.models.circe.Serialization._
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.information.{ InformationResponse, LocalInformation }
import com.agoda.content.models.db.roomsummary.RoomSummaryResponse
import com.agoda.content.models.propertycontent.{
  ContentImagesResponse,
  ContentInformationSummary,
  ContentReviewScoreResponse,
  ContentSummary
}
import com.agoda.content.models.{ CityIdType, PropertyId }
import com.agoda.core.search.framework.MockModule
import com.agoda.core.search.mocks.PriceStreamFetcherServiceDefaultTest
import com.agoda.core.search.mocks.client.NpcHttpClientTest
import com.agoda.npc.client.NpcHttpClient
import com.agoda.npc.models.explore.request.RadiusGeoSearchRequest
import com.agoda.npc.models.explore.response.GeoPlaceSearchResult
import com.agoda.npc.models.text.property.request.PropertyIdSearchRequest
import com.agoda.npc.models.text.property.response.PropertyIdsSearchResponse
import com.agoda.papi.content.graphql._
import com.agoda.papi.search.common.externaldependency.content._
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.externaldependency.growthprogram.{ GrowthProgramService, GrowthProgramServiceImpl }
import com.agoda.papi.search.common.externaldependency.promocode.GandalfService
import com.agoda.papi.search.common.externaldependency.repository.SoldOutPropsPriceRepository
import com.agoda.papi.search.common.framework.CommonMockModule
import com.agoda.papi.search.common.framework.mock.{ CAPIServiceTest, GrowthProgramClientTest }
import com.agoda.papi.search.common.handler.datafetcher.pricestream.PriceStreamFetcherService
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.service.fs.{ FeatureStoreService, FeatureStoreServiceTest }
import com.agoda.papi.search.common.util.circuitbreaker.{ CircuitBreakerWrapper, NoOpCircuitBreakerWrapper }
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext, WithCorrelationId }
import com.agoda.papi.search.common.util.hotelshutdown.{ HotelShutdownSetting, HotelShutdownUtils }
import com.agoda.papi.search.common.util.logger.HadoopLogger
import com.agoda.papi.search.complexsearch.{ ContentService, DFService, RoomSummaryExternalService }
import com.agoda.papi.search.internalmodel.ExchangeRate
import com.agoda.papi.search.internalmodel.content.PAPIContentSummary
import com.agoda.papi.search.internalmodel.database.AverageRoomPrice
import com.agoda.papi.search.internalmodel.pricing.PAPIHotelPricing
import com.agoda.papi.search.internalmodel.promocode.BannerCampaignInfo
import com.agoda.papi.search.internalmodel.request.{
  MetaLabGroupRequest,
  PricingPropertiesRequestWrapper,
  PropertyMetaInformationGroupRequest
}
import com.agoda.papi.search.internalmodel.serializer.PAPIHotelPricingSerializer._
import com.agoda.papi.search.types.{ HotelIdType, RegionIdType }
import com.agoda.papi.search.utils.PAPIContentSummaryHelper._
import com.agoda.platform.service.context.GlobalContext
import com.agoda.pricestream.models.response.{ PropertyAttributesResponse, PropertyMetaInfoResponse }
import com.agoda.property.search.framework.defaultmock.services._
import com.agoda.supply.growth.program.api.model.{ GetCommissionsRequest, GetCommissionsResponse }
import com.agoda.supply.growth.program.client.GrowthProgramClient
import com.agoda.winterfell.output.TrustedHostInfo
import io.circe.parser.parse
import io.opentracing.Tracer
import org.joda.time.DateTime
import scaldi.BoundHelper

import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ ExecutionContext, Future }

trait GqlMockModule extends MockModule with CommonMockModule {

  def mockDfService(
      resultMap: Map[HotelIdType, PAPIHotelPricing],
      markNonMockAsUnavailable: Boolean = false
  ): BoundHelper[DFService] = {
    val resultWrapperMap = resultMap.mapValues(toHotelPricingWrapper(_))
    val mockDfService = new DFService {
      override def process(
          request: PricingPropertiesRequestWrapper,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[PAPIHotelPricing]]] =
        if (markNonMockAsUnavailable) {
          Future.successful(request.pricingRequest.propertyIds.flatMap(resultWrapperMap.get(_)).toList)
        } else {
          Future.successful(request.pricingRequest.propertyIds.map(resultWrapperMap(_)).toList)
        }
    }
    bind[DFService] to mockDfService
  }

  def mockDfServiceWithWrapper(
      resultWrapperMap: Map[HotelIdType, Wrapper[PAPIHotelPricing]],
      markNonMockAsUnavailable: Boolean = false
  ): BoundHelper[DFService] = {
    val mockDfService = new DFService {
      override def process(
          request: PricingPropertiesRequestWrapper,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[PAPIHotelPricing]]] =
        if (markNonMockAsUnavailable) {
          Future.successful(request.pricingRequest.propertyIds.flatMap(resultWrapperMap.get(_)).toList)
        } else {
          Future.successful(request.pricingRequest.propertyIds.map(resultWrapperMap(_)).toList)
        }
    }
    bind[DFService] to mockDfService
  }

  def mockContentService(resultMap: Map[HotelIdType, ContentSummary]): BoundHelper[ContentService] = {
    val resultWrapperMap = resultMap.mapValues(toContentSummaryWrapper)
    val mockContentService = new ContentService {
      override def process(
          request: ContentPropertiesSummaryRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[PAPIContentSummary]]] =
        Future.successful(request.propertyIds.map(resultWrapperMap(_)))
    }
    bind[ContentService] to mockContentService
  }

  def mockCapiService(resultResponse: Future[TrustedHostInfo]): BoundHelper[CAPIService] = {
    val mockCAPIService = new CAPIServiceTest {
      override def getTrustedHostInfo(
          propertyId: PropertyId
      )(implicit ctx: ContextHolder): Future[TrustedHostInfo] =
        resultResponse

      override def getHostPropertiesInfo(
          propertyId: PropertyId
      )(implicit ctx: ContextHolder): Future[TrustedHostInfo] =
        resultResponse
    }
    bind[CAPIService] to mockCAPIService
  }

  def mockInformationSummaryExternalService(
      resultMap: Map[HotelIdType, ContentInformationSummary]
  ): BoundHelper[InformationSummaryExternalService] = {
    val mockInformationSummaryExternalService = new InformationSummaryExternalServiceDefaultTest {
      override def process(
          request: ContentInformationSummaryGroupRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentInformationSummary]]] = {
        val resultWrapperMap = resultMap.mapValues(toContentInformationSummaryWrapper)
        Future.successful(request.propertyIds.map(resultWrapperMap(_)))
      }
    }
    bind[InformationSummaryExternalService] to mockInformationSummaryExternalService
  }

  def mockEngagementExternalService(
      resultMap: Map[HotelIdType, Wrapper[ContentWithPropertyId]]
  ): BoundHelper[EngagementExternalService] = {
    val mockEngagementExternalService = new EngagementExternalServiceDefaultTest {
      override def process(
          request: ContentGroupEngagementRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(request.propertyIds.map(resultMap(_)))
    }
    bind[EngagementExternalService] to mockEngagementExternalService
  }

  def mockExperiencesExternalService(
      resultMap: Map[HotelIdType, Experiences]
  ): BoundHelper[ExperiencesExternalService] = {
    val mockExperiencesExternalService = new ExperiencesExternalService {
      override def process(
          request: ContentGroupExperienceRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[Experiences]]] = {
        val resultWrapperMap = resultMap.mapValues(toContentExperiencesWrapper)
        Future.successful(
          request.propertyIds.map(resultWrapperMap(_))
        )
      }
    }
    bind[ExperiencesExternalService] to mockExperiencesExternalService
  }

  def mockFeaturesExternalService(
      resultMap: Map[HotelIdType, Wrapper[ContentWithPropertyId]]
  ): BoundHelper[FeaturesExternalService] = {
    val mockFeaturesExternalService = new FeaturesExternalServiceDefaultTest {
      override def process(
          request: ContentGroupFeaturesRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(request.propertyIds.map(resultMap(_)))
    }
    bind[FeaturesExternalService] to mockFeaturesExternalService
  }

  def mockHighlightsExternalService(
      resultMap: Map[HotelIdType, Wrapper[ContentWithPropertyId]]
  ): BoundHelper[HighlightsExternalService] = {
    val mockHighlightsExternalService = new HighlightsExternalServiceDefaultTest {
      override def process(
          request: ContentGroupHighlightsRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(
          request.propertyIds.map(resultMap(_))
        )
    }
    bind[HighlightsExternalService] to mockHighlightsExternalService
  }

  def mockRateCategoryService(
      resultMap: Map[HotelIdType, Wrapper[ContentWithPropertyId]]
  ): BoundHelper[RateCategoryExternalService] = {
    val mockRateCategoryExternalService = new RateCategoryExternalServiceDefaultTest {
      override def process(
          request: ContentGroupRateCategoryRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(request.propertyIds.map(resultMap(_)))
    }
    bind[RateCategoryExternalService] to mockRateCategoryExternalService
  }

  def mockPriceStreamFetcherServiceMetaLab(
      propertyAttributesResponse: PropertyAttributesResponse
  ): BoundHelper[PriceStreamFetcherService] = {
    val mockPriceStreamFetcherService = new PriceStreamFetcherServiceDefaultTest {
      override def getMetaLabResponse(request: MetaLabGroupRequest, globalContext: GlobalContext)(implicit
          ec: ExecutionContext
      ): Future[PropertyAttributesResponse] =
        Future.successful(propertyAttributesResponse)
    }
    bind[PriceStreamFetcherService] to mockPriceStreamFetcherService
  }

  def mockQnaService(resultMap: Map[HotelIdType, Wrapper[ContentWithPropertyId]]): BoundHelper[QnaExternalService] = {
    val mockQnaExternalService = new QnaExternalServiceDefaultTest {
      override def process(
          request: ContentGroupQnaRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(request.propertyIds.map(resultMap(_)))
    }
    bind[QnaExternalService] to mockQnaExternalService
  }

  def mockTopicsService(
      resultMap: Map[HotelIdType, Wrapper[ContentWithPropertyId]]
  ): BoundHelper[TopicsExternalService] = {
    val mockTopicsExternalService = new TopicsExternalServiceDefaultTest {
      override def process(
          request: ContentGroupTopicsRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(request.propertyIds.map(resultMap(_)))
    }
    bind[TopicsExternalService] to mockTopicsExternalService
  }

  def mockRoomSummaryService(
      resultMap: Map[HotelIdType, RoomSummaryResponse]
  ): BoundHelper[RoomSummaryExternalService] = {
    val resultWrapperMap = resultMap.mapValues(toRoomSummaryWrapper)
    val mockRoomSummaryService = new RoomSummaryExternalService {
      override def process(
          request: ContentGroupRoomSummaryRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
        Future.successful(request.contentRoomSummaryRequest.map(_.propertyId).map(resultWrapperMap(_)))
    }
    bind[RoomSummaryExternalService] to mockRoomSummaryService
  }

  def mockHotelShutdownUtils(): BoundHelper[HotelShutdownUtils] = {
    val mockHotelShutdownUtils: HotelShutdownUtils = new HotelShutdownUtils {

      override def getBlockedIsraelKAProperties(): Set[HotelIdType] = Set.empty

      override def getBlockedIsraelKAPropertiesCityMap(): Map[HotelIdType, Set[HotelIdType]] = Map.empty
    }

    bind[HotelShutdownUtils] to mockHotelShutdownUtils
  }

  def mockReviewScoreService(
      resultMap: Map[HotelIdType, ContentReviewScoreResponse]
  ): BoundHelper[ReviewScoreExternalService] = {
    val mockReviewScoreService = new ReviewScoreExternalServiceDefaultTest {

      override def process(
          request: ContentGroupReviewScoreRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentReviewScoreResponse]]] = {

        val resultWrapperMap = resultMap.mapValues(toContentReviewScoreWrapper)

        Future.successful(
          request.propertyIds.flatMap(propertyId => resultWrapperMap.get(propertyId))
        )
      }

    }
    bind[ReviewScoreExternalService] to mockReviewScoreService
  }

  def mockImageService(
      resultMap: Map[HotelIdType, ContentImagesResponse]
  ): BoundHelper[ImageExternalService] = {
    val mockImageService = new ImageExternalServiceDefaultTest {
      override def process(
          request: ContentGroupImagesRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentImagesResponse]]] = {
        val resultWrapperMap = resultMap.mapValues(toContentImagesWrapper)

        Future.successful(
          request.propertyIds.flatMap(propertyId => resultWrapperMap.get(propertyId))
        )
      }
    }
    bind[ImageExternalService] to mockImageService
  }

  def mockInformationService(
      resultMap: Map[HotelIdType, InformationResponse]
  ): BoundHelper[InformationExternalService] = {
    val mockInformationService = new InformationExternalServiceDefaultTest {
      override def process(
          request: ContentInformationGroupRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[InformationResponse]]] = {
        val resultWrapperMap = resultMap.mapValues(toInformationWrapper)

        Future.successful(
          request.propertyIds.flatMap(propertyId => resultWrapperMap.get(propertyId))
        )
      }

    }

    bind[InformationExternalService] to mockInformationService
  }

  def mockLocalInformationService(
      resultMap: Map[HotelIdType, LocalInformation]
  ): BoundHelper[LocalInformationExternalService] = {
    val mocklocalInformationService = new LocalInformationExternalServiceDefaultTest {
      override def process(
          request: ContentGroupLocalInformationRequestEnvelope,
          globalContext: GlobalContext,
          experimentContext: ExperimentContext
      )(implicit contextHolder: ContextHolder): Future[List[Wrapper[LocalInformation]]] = {
        val resultWrapperMap = resultMap.mapValues(toLocalInformationWrapper)

        Future.successful(
          request.propertyIds.flatMap(propertyId => resultWrapperMap.get(propertyId))
        )
      }
    }

    bind[LocalInformationExternalService] to mocklocalInformationService
  }

  def mockGrowthProgramClient(
      mockResponse: GetCommissionsResponse,
      isSuccess: Boolean = true
  ): BoundHelper[GrowthProgramClient] =
    bind[GrowthProgramClient] to new GrowthProgramClientTest {

      override def getCommissions(request: GetCommissionsRequest): Future[GetCommissionsResponse] =
        if (isSuccess) Future.successful(mockResponse)
        else Future.failed(new Exception("[mockGrowthProgramClient] throw on purpose"))

    }

  def mockGrowthProgramService(): BoundHelper[GrowthProgramService] =
    bind[GrowthProgramService] to new GrowthProgramServiceImpl {
      override val gpClient: GrowthProgramClient         = inject[GrowthProgramClient]
      override val circuitBreaker: CircuitBreakerWrapper = NoOpCircuitBreakerWrapper

      override def reportingService: MetricsReporter = NoOpMetricsReporter

      override def tracer: Tracer = NoOpTracer
    }

  def mockFeatureStoreService(): BoundHelper[FeatureStoreService] = {
    val mockFSService = new FeatureStoreServiceTest()
    bind[FeatureStoreService] to mockFSService
  }

  def mockGandalfService(
      bannerCampaignRegionData: List[BannerCampaignInfo],
      bannerCampaignPropertyData: List[BannerCampaignInfo]
  ): BoundHelper[GandalfService] = {
    val mockGandalfService = new GandalfService() {
      override def getBannerCampaignForRegion(regionId: RegionIdType, checkIn: DateTime)(implicit
          executionContext: ExecutionContext,
          searchContextHolder: ContextHolder
      ): Future[List[BannerCampaignInfo]] =
        Future.successful(bannerCampaignRegionData)

      override def getBannerCampaignForProperty(
          propertyId: PropertyId,
          checkIn: DateTime,
          globalContext: GlobalContext
      )(implicit
          executionContext: ExecutionContext,
          searchContextHolder: ContextHolder
      ): Future[List[BannerCampaignInfo]] =
        Future.successful(bannerCampaignPropertyData)
    }
    bind[GandalfService] to mockGandalfService
  }

  def mockNPCServiceForPropertyIdSearch(
      propertyIdsSearchResponse: PropertyIdsSearchResponse
  ): BoundHelper[NpcHttpClient[Future]] = {
    val mockNPCService = new NpcHttpClientTest {

      override def getPropertyIdSearch(request: PropertyIdSearchRequest, requestTimeout: Option[FiniteDuration])(
          implicit ME: MonadError[Future, Throwable]
      ): Future[PropertyIdsSearchResponse] =
        Future.successful(propertyIdsSearchResponse)
    }
    bind[NpcHttpClient[Future]] to mockNPCService
  }

  def mockNPCServiceForRadiusGeoSearch(
      geoPlaceSearchResult: GeoPlaceSearchResult
  ): BoundHelper[NpcHttpClient[Future]] = {
    val mockNPCService = new NpcHttpClientTest {
      override def getRadiusGeoSearch(request: RadiusGeoSearchRequest, requestTimeout: Option[FiniteDuration] = None)(
          implicit ME: MonadError[Future, Throwable]
      ): Future[GeoPlaceSearchResult] =
        Future.successful(geoPlaceSearchResult)
    }
    bind[NpcHttpClient[Future]] to mockNPCService
  }

  def mockPriceStreamFetcherService(
      propertyMetaInfoResponse: PropertyMetaInfoResponse
  ): Unit = {
    val mockPriceStreamFetcherService = new PriceStreamFetcherServiceDefaultTest() {
      override def getPropertyMetaInfo(request: PropertyMetaInformationGroupRequest, globalContext: GlobalContext)(
          implicit ec: ExecutionContext
      ): Future[PropertyMetaInfoResponse] =
        Future.successful(
          propertyMetaInfoResponse
        )

    }
    bind[PriceStreamFetcherService] to mockPriceStreamFetcherService
  }

  def mockHadoopLoggerTest(mockHadoopLogger: HadoopLogger): BoundHelper[HadoopLogger] =
    bind[HadoopLogger] to mockHadoopLogger

  def mockSoldOutPropsPriceRepository(
      soldoutAvgRoomPriceMap: Map[Int, AverageRoomPrice]
  ): BoundHelper[SoldOutPropsPriceRepository] = {
    val mockSoldOutPropsPriceRepository = new SoldOutPropsPriceRepository {

      override def getSoldOutPropertiesPricesForAllWhiteLabel(
          cityIds: collection.Set[CityIdType],
          exchangeRate: Option[ExchangeRate],
          whitelabelId: Int
      )(implicit contextHolder: ContextHolder): Future[Map[Int, AverageRoomPrice]] =
        Future.successful(soldoutAvgRoomPriceMap)
    }

    bind[SoldOutPropsPriceRepository] to mockSoldOutPropsPriceRepository
  }

  private def toHotelPricingWrapper(hotelPricing: PAPIHotelPricing): Wrapper[PAPIHotelPricing] = {
    val json = parse(circeSerializer[PAPIHotelPricing].serialize(hotelPricing)).right.get
    Wrapper(hotelPricing, json)
  }

  private def toContentSummaryWrapper(contentSummary: ContentSummary): Wrapper[PAPIContentSummary] = {
    val json = parse(circeSerializer[ContentSummary].serialize(contentSummary)).right.get
    Wrapper(contentSummary, json)
  }

  private def toContentInformationSummaryWrapper(
      contentInformationSummary: ContentInformationSummary
  ): Wrapper[ContentInformationSummary] = {
    val json = parse(circeSerializer[ContentInformationSummary].serialize(contentInformationSummary)).right.get
    Wrapper(contentInformationSummary, json)
  }

  private def toContentExperiencesWrapper(
      experiences: Experiences
  ): Wrapper[Experiences] = {
    val json = parse(circeSerializer[Experiences].serialize(experiences)).right.get
    Wrapper(experiences, json)
  }

  private def toRoomSummaryWrapper(roomSummaryResponse: RoomSummaryResponse): Wrapper[ContentWithPropertyId] = {
    val json = parse(circeSerializer[RoomSummaryResponse].serialize(roomSummaryResponse)).right.get
    Wrapper(ContentWithPropertyId(roomSummaryResponse.propertyId), json)
  }

  private def toContentReviewScoreWrapper(
      contentReviewScoreResponse: ContentReviewScoreResponse
  ): Wrapper[ContentReviewScoreResponse] = {
    val json = parse(circeSerializer[ContentReviewScoreResponse].serialize(contentReviewScoreResponse)).right.get
    Wrapper(contentReviewScoreResponse, json)
  }

  private def toContentImagesWrapper(
      contentImagesResponse: ContentImagesResponse
  ): Wrapper[ContentImagesResponse] = {
    val json = parse(circeSerializer[ContentImagesResponse].serialize(contentImagesResponse)).right.get
    Wrapper(contentImagesResponse, json)
  }

  private def toInformationWrapper(
      informationResponse: InformationResponse
  ): Wrapper[InformationResponse] = {
    val json = parse(circeSerializer[InformationResponse].serialize(informationResponse)).right.get
    Wrapper(informationResponse, json)
  }

  private def toLocalInformationWrapper(
      localInformation: LocalInformation
  ): Wrapper[LocalInformation] = {
    val json = parse(circeSerializer[LocalInformation].serialize(localInformation)).right.get
    Wrapper(localInformation, json)
  }

  def mockHotelShutdownSettings(
      shutdownIsraelKAHotelsByCity: Map[Long, Set[Long]] = Map.empty
  ): BoundHelper[HotelShutdownSetting] = {
    val mockHotelShutdownSettings =
      new HotelShutdownSetting(
        shutdownIsraelKAHotelsByCity
      )
    bind[HotelShutdownSetting] to mockHotelShutdownSettings
  }

}
