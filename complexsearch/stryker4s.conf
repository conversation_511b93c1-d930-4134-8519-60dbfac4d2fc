stryker4s {
    base-dir: "complexsearch"
    reporters: ["console", "html", "json"]
    excluded-mutations: ["StringLiteral"]
    scala-dialect: "2.12"
    mutate: [
        "!src/main/scala/com/agoda/property/common/services/models/content/serializer/PAPIContentSummarySerializer.scala",
        "!src/main/scala/com/agoda/property/search/services/debug/GqlResultTracking.scala",
        "!src/main/scala/com/agoda/property/search/models/Models.scala",
        "!src/main/java/com/agoda/property/search/models/PAPIRawRequestMessage.java",
        "!src/main/scala/com/agoda/property/search/graphql/schema/EnumSchema.scala",
        "!src/main/scala/com/agoda/property/search/graphql/Codecs.scala",
        "!src/main/scala/com/agoda/property/search/models/ReportingDataBuilder.scala",
        "!src/main/scala/com/agoda/property/search/services/APOService.scala",
        "!src/main/scala/com/agoda/property/search/models/SearchMessage.scala",
        "!src/main/scala/com/agoda/property/search/models/SecretDealMessage.scala",
        "!src/main/scala/com/agoda/property/search/utils/RequestGenerator.scala",
        "!src/main/scala/com/agoda/property/search/models/SearchPollPricing.scala",
        "!src/main/scala/com/agoda/property/search/PropertySearchServiceLogicHelper.scala",
        "!src/main/scala/com/agoda/property/search/models/enum/SearchCategoryTypes.scala",
        "!src/main/scala/com/agoda/property/search/blockrequest/blockretry/BlockIsRetryRequestStrategy.scala",
        "!src/main/scala/com/agoda/property/search/routes/helper/FallbackSpanHelper.scala",
        "!src/main/scala/com/agoda/core/search/filters/elastic/QueryBuilder.scala",
        "!src/main/scala/com/agoda/core/search/logging/**",
        "!src/main/scala/com/agoda/papi/search/complexsearch/binding/Bindings.scala",
        "!src/main/scala/com/agoda/papi/search/complexsearch/configuration/*.scala",
        "!src/main/scala/com/agoda/papi/search/complexsearch/detail/UrgencyScoreGenerator.scala",
        "!src/main/scala/com/agoda/papi/search/complexsearch/filter/elastic/QueryBuilder.scala",
        "!src/main/scala/com/agoda/papi/search/complexsearch/search/PostFilterFlow.scala",
        "!src/main/scala/com/agoda/papi/search/complexsearch/filter/elastic/filterutil/TermsFilterParser.scala",
        "!src/main/java/com/agoda/papi/search/complexsearch/logging/**",
        "src/main/scala/**"
    ]
    test-filter: [
        "!com.agoda.papi.search.complexsearch.sorting.HotelDataPreparationFlowSpec",
        "!com.agoda.papi.search.complexsearch.filter.elastic.QueryBuilderSpec",
        "!com.agoda.papi.search.complexsearch.filter.pre.BookOnRequestFilterSpec",
        "!com.agoda.core.search.filters.elastic.filterutils.MatrixFilterResultSpec",
        "!com.agoda.papi.search.complexsearch.sorting.SortByReviewScoreSpec",
        "!com.agoda.papi.search.complexsearch.matrix.derived.RecommendedFiltersServiceSpec",
        "!com.agoda.papi.search.complexsearch.availability.service.pushcas.PushShardAvailabilityServiceSpec",
        "!com.agoda.property.search.test.it.schema.GQLSchemaSpec"
    ]
}
