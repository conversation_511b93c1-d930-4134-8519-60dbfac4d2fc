name := "complexsearch"

val dependency = Seq(
  /** JAVA LIB * */
  "org.mockito"      % "mockito-core"    % Versions.mockito % Test,
  ("commons-logging" % "commons-logging" % Versions.commonsLogging).force(),
  "io.netty"         % "netty"           % "3.10.6.Final",
  /** Require by ElasticSugar * */
  "org.apache.commons" % "commons-lang3"                % Versions.apache_common_lang,
  "org.apache.commons" % "commons-math3"                % Versions.apache_common_math,
  "org.specs2"        %% "specs2-core"                  % Versions.specs2 % Test,
  "org.specs2"        %% "specs2-junit"                 % Versions.specs2 % Test,
  "org.specs2"        %% "specs2-mock"                  % Versions.specs2 % Test,
  "com.agoda"         %% "supplier-common"              % Versions.Agoda.supplierCommon,
  ("com.agoda.ml"     %% "zenith_client_scalapb-0-11-x" % Versions.zenithClient).excludeAll(
    ExclusionRule("com.agoda.ml", "zenith_model_scalapb-0-11-x_2.12"),
    ExclusionRule("com.agoda.commons", "ag-http-client-v2_2.12"),
    ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2_2.12")
  ),
  "com.agoda.ml"  %% "zenith_model_scalapb-0-11-x" % Versions.zenith_model,
  ("com.agoda.ml" %% "jarvis_model"                % Versions.jarvis)
    .excludeAll(ExclusionRule(organization = "io.netty"), ExclusionRule(organization = "io.grpc")),
  "org.apache.logging.log4j" % "log4j-api"                 % "2.17.0", // depend by elasticsearch
  "org.apache.logging.log4j" % "log4j-core"                % "2.17.2", // depend by elasticsearch
  "com.sksamuel.elastic4s"  %% "elastic4s-core"            % Versions.elastic4s,
  "com.sksamuel.elastic4s"  %% "elastic4s-client-esjava"   % Versions.elastic4s,
  "org.elasticsearch.client" % "elasticsearch-rest-client" % Versions.elasticsearch,
  "com.sksamuel.elastic4s"  %% "elastic4s-testkit"         % Versions.elastic4s % Test,
  "org.testcontainers"       % "elasticsearch"             % Versions.esTestcontainers % Test,
  "org.testcontainers"       % "testcontainers"            % Versions.esTestcontainers % Test,
  "org.ow2.asm"              % "asm"                       % Versions.asm % Test,
  "org.ow2.asm"              % "asm-commons"               % Versions.asm % Test,
  "org.ow2.asm"              % "asm-tree"                  % Versions.asm % Test,
  "org.ow2.asm"              % "asm-util"                  % Versions.asm % Test,
  "com.agoda.everest"       %% "sl-client"                 % Versions.slService,
  "com.agoda.everest"       %% "grpc"                      % Versions.slService,
  ("com.agoda.bundler"      %% "packaging"                 % Versions.Agoda.bundler)
    .excludeAll(ExclusionRule(organization = "com.fasterxml.jackson.module"))
)

libraryDependencies ++= dependency.map(moduleID => Versions.commonExclude(moduleID))

libraryDependencies ++= Seq(
  "com.agoda.commons"  %% "ag-http-server-api" % Versions.Agoda.agHttpServer,
  ("com.agoda.commons" %% "ag-graphql-schema"  % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
  "org.sangria-graphql"     %% "sangria"                    % Versions.Agoda.sangria,
  "com.agoda.commons"       %% "utils"                      % Versions.Agoda.commons,
  "com.agoda.adp.messaging" %% "adp-messaging-client-scala" % Versions.adpMessaging,
  "com.netflix.archaius"     % "archaius-scala"             % Versions.archaiusScala,
  "io.circe"                %% "circe-optics"               % Versions.circeOptics,
  "org.scalatest"           %% "scalatest"                  % Versions.scalaTest            % Test,
  "org.scalatestplus"       %% "mockito-3-4"                % Versions.scalaTestPlusMockito % Test,
  "com.github.tomakehurst"   % "wiremock-jre8"              % Versions.wiremock             % Test,
  ("com.agoda.commons"      %% "ag-http-server-circe"       % Versions.Agoda.agHttpServer)
    .excludeAll(ExclusionRule("com.thesamet.scalapb")),
  "com.typesafe.scala-logging" %% "scala-logging"             % Versions.scalalog,
  "com.agoda.commons"           % "ag-dynamic-state-consul"   % Versions.Agoda.agConsul,
  "ch.megard"                  %% "akka-http-cors"            % Versions.akkaHttpCors,
  "io.netty"                    % "netty-all"                 % Versions.netty,
  "de.heikoseeberger"          %% "akka-http-circe"           % Versions.akkaHttpCirce,
  "org.scaldi"                 %% "scaldi-akka"               % Versions.scaldi,
  "org.scaldi"                 %% "scaldi"                    % Versions.scaldi,
  "com.typesafe.akka"          %% "akka-actor"                % Versions.akka,
  "com.typesafe.akka"          %% "akka-stream"               % Versions.akka, // require by capi
  "com.typesafe.akka"          %% "akka-slf4j"                % Versions.akka % Test,
  "com.typesafe.akka"          %% "akka-http"                 % Versions.akkaHttp,
  "com.agoda.platform"         %% "service-context-akka-http" % Versions.Agoda.serviceContext,
  "com.typesafe.akka"          %% "akka-http-jackson"         % Versions.akkaHttp,
  "com.typesafe.akka"          %% "akka-http-spray-json"      % Versions.akkaHttp,
  "com.typesafe.akka"          %% "akka-testkit"              % Versions.akka % Test,
  "com.typesafe.akka"          %% "akka-stream-testkit"       % Versions.akka % Test,
  "com.typesafe.akka"          %% "akka-http-testkit"         % Versions.akkaHttp % Test,
  "com.agoda.commons"          %% "metrics"                   % Versions.Agoda.commons,
  "com.agoda.commons"          %% "localization"              % Versions.Agoda.commons,
  "com.agoda.commons"          %% "ag-logging"                % Versions.Agoda.logging,
  "nl.grons"                   %% "sentries"                  % Versions.sentries,
  "com.agoda.commons"          %% "cache-remote"              % Versions.Agoda.cacheRemote,
  "com.agoda.commons"          %% "ag-http-client-v2"         % Versions.Agoda.agHttpClient,
  "com.agoda.commons"          %% "ag-http-client-mesh-v2"    % Versions.Agoda.agHttpClient,
  "com.agoda.commons"          %% "utils"                     % Versions.Agoda.commons,
  ("com.agoda.commons" %% "data"    % Versions.Agoda.commons).exclude("de.ruedigermoeller", "fst"),
  "com.agoda.commons"  %% "cql"     % Versions.Agoda.commons,
  "com.agoda.commons"  %% "metrics" % Versions.Agoda.commons,
  "com.agoda.commons" %% "ag-protobuf-scalapb-0-11-x-df"         % Versions.Agoda.agProtobuf,
  "com.agoda.commons" %% "ag-protobuf-scalapb-0-11-x-spapi"      % Versions.Agoda.agProtobuf,
  "com.agoda.commons" %% "ag-protobuf-scalapb-0-11-x-ota"        % Versions.Agoda.agProtobuf,
  "com.agoda.commons" %% "ag-protobuf-scalapb-0-11-x-mix-n-save" % Versions.Agoda.agProtobuf,
  ("com.agoda.dfapi"  %% "ypl-models"                            % Versions.Agoda.dfapi)
    .excludeAll(
      ExclusionRule(organization = "com.thesamet.scalapb"),
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}")
    ),
  ("com.agoda.dfapi" %% "dfapi_price-calculation" % Versions.Agoda.dfapi)
    .excludeAll(
      ExclusionRule(organization = "com.thesamet.scalapb"),
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}")
    ),
  ("com.agoda.dfapi" %% "ypl-pricing" % Versions.Agoda.dfapi)
    .exclude("com.agoda", s"ypl_cancellation_${scalaBinaryVersion.value}")
    .excludeAll(
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}")
    ),
  ("com.agoda" %% "supplier-pricing" % Versions.supplierPricing)
    .exclude("com.typesafe.akka", s"akka-cluster_${scalaBinaryVersion.value}")
    .exclude("com.typesafe.akka", s"akka-cluster-tools_${scalaBinaryVersion.value}")
    .exclude("com.agoda.commons", s"ag-grpc-client_${scalaBinaryVersion.value}")
    .exclude("com.agoda.connectivity", s"supply-cache-sqlserver_${scalaBinaryVersion.value}")
    .excludeAll(ExclusionRule(organization = "com.thesamet.scalapb")),
  ("com.agoda.connectivity" %% "supply-cache-sqlserver" % Versions.Agoda.supplyCacheSql)
    .excludeAll(
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module"),
      ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
      ExclusionRule(organization = "com.fasterxml.jackson.databind"),
      ExclusionRule(organization = "com.typesafe.akka")
    ),

  // Everest
  ("com.agoda.everest" %% "model-playjson" % Versions.slService)
    .excludeAll(ExclusionRule(organization = "com.fasterxml.jackson")),
  "de.heikoseeberger" %% "akka-http-play-json" % Versions.akkaHttpPlayJson,

  //  NPC API Client
  ("com.agoda.npc" %% "client" % Versions.Agoda.nonPropertyContent).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule("com.agoda.commons", s"ag-http-api-v1_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"ag-http-client-circe-v1_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"ag-http-client-jackson-v1_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"ag-http-client-mesh-v1_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"ag-http-client-v1_${scalaBinaryVersion.value}")
  ),
  ("com.agoda.whitelabel.client" %% "wl-client-scala" % Versions.Agoda.whiteLabelClient)
    .excludeAll(
      ExclusionRule(organization = "com.agoda.dcs"),
      ExclusionRule(organization = "commons-logging"),
      ExclusionRule(organization = "org.slf4j"),
      ExclusionRule(organization = "io.netty"),
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module")
    )
    .exclude("com.agoda.adp.messaging", "adp-messaging-client")
    .exclude("com.agoda.adp.messaging", "adp-messaging-log4j12"),
  ("com.agoda.papi" %% "cancellation" % Versions.Agoda.cancellation)
    .excludeAll(
      ExclusionRule("com.beachape", s"enumeratum_${scalaBinaryVersion.value}")
    ),
  "com.agoda.loyalty"     %% "usercontext-client-scala"        % Versions.userContext,
  "com.agoda.commons"     %% "configuration"                   % Versions.Agoda.agConfiguration,
  "com.agoda.commons"     %% "ag-protobuf-scalapb-0-11-x-papi" % Versions.Agoda.agProtobufPapi,
  "com.github.marklister" %% "product-collections"             % Versions.productCollection % Test,
  "org.scalatest"         %% "scalatest"                       % Versions.scalaTest         % Test,
  ("com.agoda.upi"        %% "upi-core-gql"                    % Versions.Agoda.upi)
    .excludeAll(
      ExclusionRule("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.commons", s"ag-http-client-v2_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.commons", s"ag-graphql-schema_${scalaBinaryVersion.value}")
    ),
  "io.opentelemetry" % "opentelemetry-sdk-testing" % Versions.openTelemetry % Test,
  "com.agoda"       %% "supplier-exposure-control" % Versions.supplierExposureControl
)

libraryDependencies += ("com.agoda.commons" %% "ag-couchbase" % Versions.Agoda.agCouchbase).excludeAll(
  ExclusionRule(organization = "com.fasterxml.jackson.core"),
  ExclusionRule(organization = "com.fasterxml.jackson.module"),
  ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
  ExclusionRule(organization = "com.fasterxml.jackson.dataformat")
)

libraryDependencies ~= { _.map(moduleID => Versions.commonExclude(moduleID)) }
