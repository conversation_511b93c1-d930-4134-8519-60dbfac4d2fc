version: '2.3'
x-deployment:
  parameters:
    CSP_CLUSTER:
      enum: ["A", "B", "RA", "RB", "GA", "X", "Y", "Z", "V", "P", "VC", "CA", "ACM"]
      default: "A"
    CSP_CONSUL_TAG:
      enum: ["fe", "fe-graphql-search", "fe-rest", "affiliate", "bot", "internal", "prelive", "caching", "acmtest"]
      default: "fe"
x-privatecloud:
  nameSuffix: "${CSP_CONSUL_TAG}"
  ingress:
    - name: http
      protocol: http
      port: 80
      consul:
        name: papi-search
        tags:
          - "${CSP_CLUSTER}"
          - "${CSP_CONSUL_TAG}"
      hosts:
        - papi-${CSP_CLUSTER}.${HOSTNAME}.agoda.local
  vault:
    enabled: true
  services:
    propertyapisearchdocker:
      env:
        - name: DEPLOYMENT
          value: kubernetes
      resources:
        limits:
          memory: 26Gi
        requests:
          cpu: "8"
          memory: 22Gi
      startupProbe:
        httpGet:
          path: /ag-http-server/status
          port: 80
        initialDelaySeconds: 60
        periodSeconds: 5
        failureThreshold: 40
      readinessProbe:
        httpGet:
          path: /ag-http-server/status
          port: 80
        initialDelaySeconds: 5
        periodSeconds: 5
        failureThreshold: 3
      livenessProbe:
        httpGet:
          path: /ag-http-server/status
          port: 80
        initialDelaySeconds: 5
        periodSeconds: 5
        failureThreshold: 3
services:
  propertyapisearchdocker:
    image: reg-${DC}.agodadev.io/papi/search/propertyapi/release:3.44.758
    network_mode: host
    hostname: ${HOSTNAME}
    volumes:
      - /var/log/agoda/:/var/log/agoda/
      - /var/log/adp-messaging:/var/log/adp-messaging
      - /opt/propertyapi/sflogs:/opt/propertyapi/sflogs
      - /var/propertyapi/consul-cache:/var/propertyapi/consul-cache
      - /var/run/nscd:/var/run/nscd
      - /etc/consul.d/template/:/etc/consul.d/template/
      - /var/agoda/dump/:/var/agoda/dump/
      - /var/tmp/vault:/var/tmp/vault
    environment:
      TZ: Asia/Bangkok
      DC: ${DC}
      HOSTNAME: "${HOSTNAME}"
      CSP_CLUSTER: "${CSP_CLUSTER}"
      CSP_CONSUL_TAG: "${CSP_CONSUL_TAG}"
      VAULT_TOKEN: "${VAULT_TOKEN}"
      VAULT_ADDR: "${VAULT_ADDR}"
    restart: always
    healthcheck:
      #Health Check for PAPI service
      test:
        - CMD-SHELL
        - sh ./papi_healthcheck.sh
      start_period: 30s
      interval: 30s
      timeout: 30s
      retries: 20
    labels:
      syncdb: true
