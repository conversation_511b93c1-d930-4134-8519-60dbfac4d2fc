name := "functional-test"

libraryDependencies ++= Seq(
  "org.testcontainers"  % "elasticsearch"     % Versions.esTestcontainers % Test,
  "org.testcontainers"  % "testcontainers"    % Versions.esTestcontainers % Test,
  "org.mockito"        %% "mockito-scala"     % Versions.mockitoScala,
  ("com.agoda.commons" %% "ag-graphql-schema" % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
  ("com.agoda.commons" %% "ag-graphql-testkit" % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}")
)
