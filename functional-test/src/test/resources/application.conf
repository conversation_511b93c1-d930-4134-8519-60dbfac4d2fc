remote {
  schema :[
    {
      service: "content"
      connectionTimeout : 8000
      pathPrefix: "graphql"
      retry : 1
    },
    {
      service: "dragonfruit"
      connectionTimeout : 8000
      pathPrefix: "graphql"
      retry : 1
    }
  ]
}

ag-http-client.mesh.services {
  dragonfruit {
    discovery {
      method = "static"
      static {
        hosts = ["propertyapipricingdocker-fe.privatecloud.hk.agoda.is:80"]
      }
    }
  }
  content {
    discovery {
      method = "static"
      static {
        hosts = ["propertyapicontentdocker-fe.privatecloud.hk.agoda.is:80"]
      }
    }
  }
}

schema-test {
  papi-prod-url = "http://propertyapisearchdocker-papisearchregression.privatecloud.hk.agoda.is:80/graphql"
}