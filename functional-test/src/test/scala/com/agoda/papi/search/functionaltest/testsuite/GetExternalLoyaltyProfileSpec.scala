package com.agoda.papi.search.functionaltest.testsuite

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.common.util.context.ContextGenerator.ForceBExperimentContext
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.property.service.ClusterService.DFPropertyWithCacheTokenServiceWrapper
import com.agoda.platform.service.context.{ ServiceHeaders, StandardHTTPHeaders }
import com.agoda.winterfell.output.DiscountType.DiscountType
import framework.MockModule
import framework.testservice.DFPropertyWithCacheTokenServiceWrapperTest
import models.starfruit.PropertySearchRequest
import util.builders.TestDataBuilders._
import org.mockito.MockitoSugar
import org.mockito.Mockito
import org.mockito.Mockito.reset
import org.mockito.ArgumentMatchers.any
import org.specs2.mock.mockito.ArgumentCapture
import org.mockito.ArgumentMatchers.{ any => mockitoAny }

import scala.concurrent.Future
import scala.jdk.CollectionConverters.asScalaIterator
import com.agoda.winterfell.output.{
  Campaign,
  DiscountType,
  ExternalLoyaltyUserProfileResponse,
  ProductType,
  SubLoyaltyPrograms
}
import request.SearchTypes

class GetExternalLoyaltyProfileSpec extends PAPISearchFunctionalTest {

  val activeAbTests = ActiveABTests()

  "Get ExternalLoyaltyProfile" should {

    def CAPIServiceMockResponse(promocode: String, discountType: DiscountType) =
      Future.successful(
        Some(
          ExternalLoyaltyUserProfileResponse(
            subLoyaltyPrograms = Seq(
              SubLoyaltyPrograms(
                pointsBalance = Some(100.0),
                programId = Some("pid"),
                loyaltyAccountId = Some("accid"),
                programName = Some("prog"),
                externalPartnerProgramCode = Some("code"),
                campaigns = Some(
                  Seq(
                    Campaign(
                      promocode = promocode,
                      discountType = discountType,
                      value = 10.0,
                      currency = Some("USD"),
                      promoRedeemedCount = None,
                      promoLeftCount = None,
                      promoEndDate = None,
                      nextPromoAvailDate = None,
                      productType = ProductType.HOTELS
                    )
                  )
                )
              )
            ),
            externalUserContext = Some("externalVipProfile")
          )
        )
      )

    def mockAllocationContext(allocationCharMap: (String, Char)*): AllocationContext = {
      val m = mock[AllocationContext]
      allocationCharMap.foreach {
        case ("enablePromocodeInExternalLoyaltyProfile", value) =>
          when(m.enablePromocodeInExternalLoyaltyProfile).thenReturn(value)
        case ("enableExternalVipProgram", value) =>
          when(m.enableExternalVipProgram).thenReturn(value)
        case (field, _) =>
          throw new IllegalArgumentException(s"Unknown allocation context field: $field")
      }
      m
    }

    def buildModule(
        promocode: String,
        discountType: DiscountType,
        dfServiceRecorderSetter: DFPropertyWithCacheTokenServiceWrapper => Unit
    ): MockModule =
      new MockModule {
        mockCapiService(CAPIServiceMockResponse(promocode, discountType))
        bind[DFPropertyWithCacheTokenServiceWrapper].to {
          lazy val wrapper = new DFPropertyWithCacheTokenServiceWrapperTest
          val spyMock      = Mockito.spy(wrapper)
          dfServiceRecorderSetter(spyMock)
          spyMock
        }
      }

    def runPromocodeTest(
        promocode: String,
        discountType: DiscountType,
        enablePromocodeInExternalLoyaltyProfile: Char,
        enableExternalVipProgram: Char,
        expectedPromocode: Option[String]
    ): Unit = {
      var dfServiceRecorder: DFPropertyWithCacheTokenServiceWrapper = null
      val module = buildModule(promocode, discountType, spy => dfServiceRecorder = spy)

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withAdditionalExperimentForce(
          activeAbTests.enablePromocodeInExternalLoyaltyProfile,
          enablePromocodeInExternalLoyaltyProfile.toString
        )
        .withAdditionalExperimentForce(activeAbTests.enableExternalVipProgram, enableExternalVipProgram.toString)
        .build()

      val respF = executePropertySearch(
        request,
        module,
        headers = aValidSSRHttpHeaders ++ Seq(
          RawHeader(ServiceHeaders.AgCid.toString, "1"),
          RawHeader(StandardHTTPHeaders.Authorization.toString, "Bearer 123456"),
          RawHeader(ServiceHeaders.AgPartnerClaim.toString, "partner-claim-token")
        )
      )
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val dfRequestCaptor = new ArgumentCapture[PropertySearchRequest]
        verify(dfServiceRecorder, times(1)).serviceCallWithRetries(dfRequestCaptor.capture, any)(any)
        val capturedDFRequests = asScalaIterator(dfRequestCaptor.values.iterator()).toList
        val profiles           = capturedDFRequests.map(r => r.pricing.externalLoyaltyProfile)
        profiles.flatMap(_.flatMap(_.promocode)).headOption shouldBe expectedPromocode
      }
    }

    "set promocode in ExternalLoyaltyProfile when RTAP-808 is B and campaign discountType matches" in {
      runPromocodeTest(
        promocode = "PROMO123",
        discountType = DiscountType.CustomerPromotion,
        enablePromocodeInExternalLoyaltyProfile = 'B',
        enableExternalVipProgram = 'B',
        expectedPromocode = Some("PROMO123")
      )
    }

    "not set promocode in ExternalLoyaltyProfile when RTAP-808 is A" in {
      runPromocodeTest(
        promocode = "PROMO123",
        discountType = DiscountType.CustomerPromotion,
        enablePromocodeInExternalLoyaltyProfile = 'A',
        enableExternalVipProgram = 'B',
        expectedPromocode = None
      )
    }

    "not set promocode in ExternalLoyaltyProfile when RTAP-808 is B but campaign discount type is mismatch" in {
      runPromocodeTest(
        promocode = "PROMO123",
        discountType = DiscountType.Unknown,
        enablePromocodeInExternalLoyaltyProfile = 'B',
        enableExternalVipProgram = 'B',
        expectedPromocode = None
      )
    }
  }

}
