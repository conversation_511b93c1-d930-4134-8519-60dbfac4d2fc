package com.agoda.papi.search.functionaltest.testsuite.booking

import akka.http.scaladsl.model.StatusCodes
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.constant.mappings.CountryIds
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import framework.MockModule
import org.scalatest.prop.TableDrivenPropertyChecks
import request._
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class BookingEndpointHkHatSpec extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {

  val activeAbTests = ActiveABTests()

  "Booking-endpoint /api/booking" should {
    "return valid property back with HK HAT tax and fee in included" in {
      Table(
        ("charges", "expected"),
        // Non-HK
        (
          Seq(aValidDisplayTaxAndFee, aValidDisplaySurcharge),
          "CMSId:63344: CMSId:60863 USD 60.0<br>CMSId:63345: Some description USD 60.0"
        ), // tax and fee are included, surcharge is excluded
        (
          Seq(aValidDisplayTaxAndFee, aValidDisplayTaxAndFee),
          "CMSId:63344: CMSId:60863 USD 120.0"
        ), // tax and fees just sum up, all included
        // HK
        // Amount not shown here since the text is supposed to be enriched from the Dragonfruit
        // However in this case we use directly the value from CMS service mock, that just responds with the experiment id
        (
          Seq(aValidDisplayTaxAndFee, aValidHkHatTaxAndFeeCharge),
          "CMSId:63344: CMSId:60863 USD 60.0, CMSId:366389"
        ), // HK tax and fee shown separately
        (
          Seq(aValidDisplayTaxAndFee, aValidHkHatSurcharge),
          "CMSId:63344: CMSId:60863 USD 60.0<br>CMSId:63345: Some description USD 10.0"
        ) // HK surcharge shown separately
      )
        .forEvery { (charges, expected) =>
          val dfHotel = aValidDFHotel
            .withHotelId(1001)
            .withCountryId(CountryIds.HongKong)
            .withRooms(
              Seq(
                aValidDFRoom
                  .withRoomTypeId(1L)
                  .withRoomIdentifier("a")
                  .withPricing(
                    Map("USD" -> aValidDFPricing.withDisplayCharge(charges))
                  )
                  .build()
              )
            )
            .withBookingSummary(Some(aValidDFBookingSummary))
            .build()
          val contentMasterRooms = aValidRoomInformationResponse
            .withHotelId(1001)
            .withMasterRoomResponse(
              Vector(
                aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
              )
            )
            .build()
          val module = new MockModule {
            mockDFResponse(Map(1001L -> dfHotel))
            mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
          }
          val request = aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(1001L))
            .withPricing(aValidPricingRequest)
            .withAdditionalExperimentForce(activeAbTests.hongKongHatTax, "B")
            .build()

          val respF = executeBookingEndpoint(request = request, module = module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            val roomIdentifiers =
              properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
            (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))
            properties.property.head.booking.get.rooms.head.taxSurchargeInfo shouldBe expected
          }
        }
    }
  }

}
