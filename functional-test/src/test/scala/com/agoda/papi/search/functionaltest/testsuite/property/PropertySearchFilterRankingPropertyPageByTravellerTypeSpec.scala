package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.information._
import com.agoda.core.search.models.enumeration.{ TravellerType, TravellerTypes }
import com.agoda.papi.enums.room.Benefits._
import com.agoda.papi.enums.room.CancellationGroup
import com.agoda.papi.search.common.service.cancellationpolicy.CancellationPolicyService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.property.service.ClusterService.propertyfilter.PropertyFilterCount
import constant.{ FilterGroupID, FilterID, Symbols }
import framework.MockModule
import mappings.CMSMapping._
import models.starfruit.{ Benefit, PayAtHotel, PayLater }
import org.joda.time.DateTime
import org.scalatest.prop.TableDrivenPropertyChecks
import request.{ PropertyRequest, SearchTypes }
import transformers.FilterTag
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchFilterRankingPropertyPageByTravellerTypeSpec
    extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {

  private val hotelId = aValidHotelId

  private val breakfastTag =
    FilterTag(
      FilterID.BreakfastInclude,
      s"CMSId:$BreakfastRoomFilterTile",
      Symbols.BreakfastIncludePropertySymbol,
      None,
      Some(FilterGroupID.Meals)
    )

  private val freeCancellationTag =
    FilterTag(
      FilterID.FreeCancellation,
      s"CMSId:$FreeCancellationRoomFilterTile",
      Symbols.FreeCancellation,
      None,
      Some(FilterGroupID.CancellationPolicies)
    )

  private val bookNowPayLaterTag = FilterTag(
    FilterID.PayLater,
    s"CMSId:$PayLaterRoomFilterTile",
    Symbols.PayLater,
    None,
    Some(FilterGroupID.PaymentOptions)
  )

  private val nonSmokingTag = FilterTag(
    FilterID.NonSmoking,
    s"CMSId:$nonSmokingFilter",
    Symbols.nonSmokingFilter,
    None,
    Some(FilterGroupID.RoomFeatures)
  )

  private val kitchenAvailableTag =
    FilterTag(
      FilterID.KitchenAvailable,
      s"CMSId:$kitchenAvailableFilter",
      Symbols.kitchenAvailableIcon,
      None,
      Some(FilterGroupID.RoomFeatures)
    )

  private val twinBedTag = FilterTag(
    FilterID.TwinBed,
    s"CMSId:$twinBedFilter",
    Symbols.twinBedIcon,
    None,
    Some(FilterGroupID.BeddingConfiguration)
  )

  private val kingBedTag = FilterTag(
    FilterID.KingBed,
    s"CMSId:$kingBedFilter",
    Symbols.kingBedIcon,
    None,
    Some(FilterGroupID.BeddingConfiguration)
  )

  private val moreThanOrEqual20SqmTag =
    FilterTag(
      FilterID.MoreThanOrEqual20Sqm,
      s"CMSId:$MoreThanOrEqual20SqmFilter",
      Symbols.roomSize,
      None,
      Some(FilterGroupID.RoomSize)
    )

  private val moreThanOrEqual40SqmTag =
    FilterTag(
      FilterID.MoreThanOrEqual40Sqm,
      s"CMSId:$MoreThanOrEqual40SqmFilter",
      Symbols.roomSize,
      None,
      Some(FilterGroupID.RoomSize)
    )

  private val moreThanOrEqual60SqmTag =
    FilterTag(
      FilterID.MoreThanOrEqual60Sqm,
      s"CMSId:$MoreThanOrEqual60SqmFilter",
      Symbols.roomSize,
      None,
      Some(FilterGroupID.RoomSize)
    )

  private val dinnerIncludedTag =
    FilterTag(
      FilterID.DinnerIncluded,
      s"CMSId:$DinnerIncludedMatrix",
      Symbols.DinnerIcon,
      None,
      Some(FilterGroupID.Meals)
    )

  private val earlyCheckInTag = FilterTag(
    FilterID.EarlyCheckIn,
    s"CMSId:$EarlyCheckInMatrix",
    Symbols.CheckInIcon,
    None,
    Some(FilterGroupID.Others)
  )

  private val lateCheckOutTag = FilterTag(
    FilterID.LateCheckOut,
    s"CMSId:$LateCheckoutMatrix",
    Symbols.CheckOutIcon,
    None,
    Some(FilterGroupID.Others)
  )

  private val airportTransferTag =
    FilterTag(
      FilterID.AirportTransfer,
      s"CMSId:$AirportTransfer",
      Symbols.AirportTransferIcon,
      None,
      Some(FilterGroupID.Others)
    )

  "PropertySearch /api/property" should {
    "return difference property filters order when Search with each Traveller Type" in {
      Table(
        ("Traveller Type", "Expected Filter Tags"),
        (
          None,
          Seq(
            breakfastTag,
            freeCancellationTag,
            bookNowPayLaterTag,
            nonSmokingTag,
            kitchenAvailableTag,
            twinBedTag,
            kingBedTag
          )
        ),
        (
          Some(TravellerTypes.Family),
          Seq(
            breakfastTag,
            freeCancellationTag,
            nonSmokingTag,
            bookNowPayLaterTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        ),
        (
          Some(TravellerTypes.Solo),
          Seq(
            nonSmokingTag,
            bookNowPayLaterTag,
            freeCancellationTag,
            breakfastTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        ),
        (
          Some(TravellerTypes.Couple),
          Seq(
            nonSmokingTag,
            breakfastTag,
            bookNowPayLaterTag,
            freeCancellationTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        ),
        (
          Some(TravellerTypes.Group),
          Seq(
            freeCancellationTag,
            breakfastTag,
            bookNowPayLaterTag,
            nonSmokingTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        )
      )
        .forEvery { case (travellerType, expectedFilterTags) =>
          val request = createRequest(travellerType = travellerType)
            .withAdditionalExperimentForce("ROOMIE-1140", "A")
            .withAdditionalExperimentForce("ROOMIE-1197", "A")
          val respF = executePropertySearch(
            request = request,
            module = createMockModule()
          )
          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            properties.property should have size 1
            properties.property.headOption.map(_.propertyId) shouldBe Some(hotelId)

            val propertyFilters = properties.property.headOption.flatMap(_.availableFilters)
            propertyFilters shouldBe defined
            propertyFilters.get.tags should have size expectedFilterTags.size
            (propertyFilters.get.tags should contain)
              .theSameElementsInOrderAs(expectedFilterTags)
          }
        }
    }

    "[ROOMIE-1140] return correct filter order when searching with each Traveller Type and ROOMIE-1140 is B" in {
      Table(
        ("Traveller Type", "Expected Filter Tags"),
        (
          None,
          Seq(
            breakfastTag,
            freeCancellationTag,
            moreThanOrEqual20SqmTag,
            moreThanOrEqual40SqmTag,
            moreThanOrEqual60SqmTag,
            bookNowPayLaterTag,
            nonSmokingTag,
            kitchenAvailableTag,
            twinBedTag,
            kingBedTag
          )
        ),
        (
          Some(TravellerTypes.Family),
          Seq(
            breakfastTag,
            freeCancellationTag,
            moreThanOrEqual20SqmTag,
            moreThanOrEqual40SqmTag,
            moreThanOrEqual60SqmTag,
            nonSmokingTag,
            bookNowPayLaterTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        ),
        (
          Some(TravellerTypes.Solo),
          Seq(
            nonSmokingTag,
            bookNowPayLaterTag,
            freeCancellationTag,
            moreThanOrEqual20SqmTag,
            moreThanOrEqual40SqmTag,
            moreThanOrEqual60SqmTag,
            breakfastTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        ),
        (
          Some(TravellerTypes.Couple),
          Seq(
            nonSmokingTag,
            breakfastTag,
            moreThanOrEqual20SqmTag,
            moreThanOrEqual40SqmTag,
            moreThanOrEqual60SqmTag,
            bookNowPayLaterTag,
            freeCancellationTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        ),
        (
          Some(TravellerTypes.Group),
          Seq(
            freeCancellationTag,
            breakfastTag,
            moreThanOrEqual20SqmTag,
            moreThanOrEqual40SqmTag,
            moreThanOrEqual60SqmTag,
            bookNowPayLaterTag,
            nonSmokingTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag
          )
        )
      ).forEvery { case (travellerType, expectedFilterTags) =>
        val request = createRequest(travellerType = travellerType)
          .withAdditionalExperimentForce("ROOMIE-1140", "B")
          .withAdditionalExperimentForce("ROOMIE-1197", "A")
        val respF = executePropertySearch(request = request, module = createMockModule())

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties = responseToProperties(resp)
          properties.property should have size 1
          properties.property.headOption.map(_.propertyId) shouldBe Some(hotelId)

          val propertyFilters = properties.property.headOption.flatMap(_.availableFilters)
          propertyFilters shouldBe defined
          propertyFilters.get.tags should have size expectedFilterTags.size
          (propertyFilters.get.tags should contain)
            .theSameElementsInOrderAs(expectedFilterTags)
        }
      }
    }

    "return difference property filters order when Search with each Traveller Type when ROOMIE-1197 is B" in {
      Table(
        ("Traveller Type", "Expected Filter Tags"),
        (
          None,
          Seq(
            breakfastTag,
            freeCancellationTag,
            bookNowPayLaterTag,
            nonSmokingTag,
            kitchenAvailableTag,
            twinBedTag,
            kingBedTag,
            dinnerIncludedTag,
            earlyCheckInTag,
            lateCheckOutTag,
            airportTransferTag
          )
        ),
        (
          Some(TravellerTypes.Family),
          Seq(
            breakfastTag,
            freeCancellationTag,
            nonSmokingTag,
            bookNowPayLaterTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag,
            dinnerIncludedTag,
            earlyCheckInTag,
            lateCheckOutTag,
            airportTransferTag
          )
        ),
        (
          Some(TravellerTypes.Solo),
          Seq(
            nonSmokingTag,
            bookNowPayLaterTag,
            freeCancellationTag,
            breakfastTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag,
            dinnerIncludedTag,
            earlyCheckInTag,
            lateCheckOutTag,
            airportTransferTag
          )
        ),
        (
          Some(TravellerTypes.Couple),
          Seq(
            nonSmokingTag,
            breakfastTag,
            bookNowPayLaterTag,
            freeCancellationTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag,
            dinnerIncludedTag,
            earlyCheckInTag,
            lateCheckOutTag,
            airportTransferTag
          )
        ),
        (
          Some(TravellerTypes.Group),
          Seq(
            freeCancellationTag,
            breakfastTag,
            bookNowPayLaterTag,
            nonSmokingTag,
            kitchenAvailableTag,
            kingBedTag,
            twinBedTag,
            dinnerIncludedTag,
            earlyCheckInTag,
            lateCheckOutTag,
            airportTransferTag
          )
        )
      )
        .forEvery { case (travellerType, expectedFilterTags) =>
          val request = createRequest(travellerType = travellerType)
            .withAdditionalExperimentForce("ROOMIE-1140", "A")
            .withAdditionalExperimentForce("ROOMIE-1197", "B")
          val respF = executePropertySearch(request = request, module = createMockModule())

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            properties.property should have size 1
            properties.property.headOption.map(_.propertyId) shouldBe Some(hotelId)

            val propertyFilters = properties.property.headOption.flatMap(_.availableFilters)
            propertyFilters shouldBe defined
            propertyFilters.get.tags should have size expectedFilterTags.size
            (propertyFilters.get.tags should contain)
              .theSameElementsInOrderAs(expectedFilterTags)
          }
        }
    }

  }

  /* helper */
  private def createRequest(travellerType: Option[TravellerType]): PropertyRequest = {
    val pricingRequest = aValidPricingRequest
      .withTravellerType(travellerType)
      .withBookingDate(DateTime.now)
      .withCheckIn(DateTime.now.plusDays(7))
      .build()

    val request = aValidPropertyRequest
      .withContext(aValidContext.withWhiteLabelKey(Some("AGODA")))
      .withSearchType(SearchTypes.Property)
      .withHotelIds(List(hotelId))
      .withPricing(pricingRequest)
      .build()

    request
  }

  private def prepareRoomAndContentResponse(
      roomsInformation: Vector[MasterRoomResponse]
  ): (RoomInformationResponse, Seq[PropertyResponse]) = {
    val roomInformationResponse =
      aValidRoomInformationResponse.withHotelId(aValidHotelId).withMasterRoomResponse(roomsInformation).build()
    val contentResult = Seq(
      aValidContentPropertyResponse.withHotelId(aValidHotelId).withRooms(Some(roomInformationResponse)).build()
    )

    (roomInformationResponse, contentResult)
  }

  private def createMasterRoomResponse(
      roomName: String,
      roomId: Long,
      feature: Vector[RoomFeatureResponse] = Vector.empty,
      bedConfiguration: Option[BedConfigurationResponse] = None
  ): MasterRoomResponse = {
    val masterRoomMeta = aValidMasterRoomMeta
      .withRoomName(Some(roomName))
      .withRoomId(roomId)
      .withMaxOccupants(Some(2))
      .withRoomSizeSqm(Some(60.0))
      .build()

    aValidMasterRoomResponse
      .withMasterRoomMetadata(masterRoomMeta)
      .withFeatures(feature)
      .withBedConfiguration(bedConfiguration)
      .build()
  }

  private def createMockModule(): MockModule = {
    // DF
    val cancellation = aValidCancellation // free cancellation tag
      .withCxlCode(CancellationPolicyService.FreeCancellationCodeZeroNight)
      .withCmsMappedCancellationGroup(CancellationGroup.FreeCancellation)
      .build()
    val payment = aValidDFPayment
      .withCancellation(cancellation)
      .withPayAtHotel(PayAtHotel(isEligible = false)) // make sure no pay-at-the-place tag
      .withPayLater(PayLater(payLater = true))        // pay later tag
    val room1 = aValidDFRoom
      .withRoomTypeId(1L)
      .withRoomIdentifier("a1")
      .withBenefits(
        Seq(
          Benefit(Breakfast.i),
          Benefit(Dinner.i),
          Benefit(EarlyCheckIn.i),
          Benefit(LateCheckout.i),
          Benefit(AirportTransferOneWay.i)
        )
      ) // breakfast included tag and additional tags
      .withPayment(payment)
      .build()
    val rooms = Seq(room1)
    val dfHotel = aValidDFHotel
      .withHotelId(hotelId)
      .withRooms(rooms)
      .build()

    // Content
    val roomFeatures = Vector(
      aValidFeatureResponse.withName(Symbols.nonSmokingFilter).build(),    // non-smoking tag
      aValidFeatureResponse.withName(Symbols.kitchenAvailableIcon).build() // kitchen available tag
    )

    val kingBedConfigurationResponse = { // king bed tag
      val kingBedResponse   = BedResponse("King", "King Bed", Some("king-bed"), 1, None)
      val bedOptionResponse = BedOptionResponse(Vector(kingBedResponse))
      BedroomConfigurationResponse("King space", Vector(bedOptionResponse))
    }
    val twinBedConfigurationResponse = { // twin bed tag
      val twinBedResponse   = BedResponse("Single", "Single Bed", Some("single-bed"), 2, None)
      val bedOptionResponse = BedOptionResponse(Vector(twinBedResponse))
      BedroomConfigurationResponse("Single space", Vector(bedOptionResponse))
    }

    val bedDetailResponse = BedDetailResponse(
      "Sleeping arrangements",
      "bed-detail",
      Vector(kingBedConfigurationResponse, twinBedConfigurationResponse)
    )

    val bedConfigurationResponse = aValidBedConfigurationResponse
      .withConfiguration(Some(bedDetailResponse))
      .build()

    val masterRoomResponse1 = createMasterRoomResponse("Room1", 1L, roomFeatures, Some(bedConfigurationResponse))
    val roomsInformation    = Vector(masterRoomResponse1)
    val (roomInformationResponse, contentResult) = prepareRoomAndContentResponse(roomsInformation)

    val mockModule = new MockModule {
      mockDFResponse(Map(hotelId -> dfHotel))
      mockContentResponse(Future.successful(contentResult))
      mockRoomInformationService(Future.successful(Seq(roomInformationResponse)))
      mockPropertyFilterSortingService(PropertyFilterCount.filterCountMap)
    }

    mockModule
  }

}
