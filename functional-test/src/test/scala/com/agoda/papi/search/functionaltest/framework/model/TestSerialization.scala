package com.agoda.papi.search.functionaltest.framework.model

import io.circe.Decoder
import io.circe.derivation.deriveDecoder
import com.agoda.papi.search.internalmodel.serializer.decoder.CustomDecoders._

object TestSerialization {
  implicit val testCitySearchResponseDecoder: Decoder[TestCitySearchResponse] = deriveDecoder[TestCitySearchResponse]
  implicit val dataCitySearchResponseDecoder: Decoder[DataCitySearchResponse] = deriveDecoder[DataCitySearchResponse]
  implicit val testRectangleSearchResponseDecoder: Decoder[TestRectangleSearchResponse] =
    deriveDecoder[TestRectangleSearchResponse]
  implicit val dataRectangleSearchResponseDecoder: Decoder[DataRectangleSearchResponse] =
    deriveDecoder[DataRectangleSearchResponse]
  implicit val testComplexSearchResponseDecoder: Decoder[TestComplexSearchResponse] =
    deriveDecoder[TestComplexSearchResponse]
  implicit val testAggregationResponseDecoder: Decoder[TestAggregationResponse] = deriveDecoder[TestAggregationResponse]
  implicit val testMatrixGroupResultDecoder: Decoder[TestMatrixGroupResult]     = deriveDecoder[TestMatrixGroupResult]
  implicit val testMatrixItemResultDecoder: Decoder[TestMatrixItemResult]       = deriveDecoder[TestMatrixItemResult]
  implicit val testSearchResultDecoder: Decoder[TestSearchResult]               = deriveDecoder[TestSearchResult]
  implicit val testGeoPointResponse: Decoder[TestGeoPointResponse]              = deriveDecoder[TestGeoPointResponse]
  implicit val testPolygonCoordinatesResponse: Decoder[TestPolygonCoordinatesResponse] =
    deriveDecoder[TestPolygonCoordinatesResponse]
  implicit val testMapDisplayInfo: Decoder[TestMapDisplayInfo] = deriveDecoder[TestMapDisplayInfo]
  implicit val testSearchInfoDecoder: Decoder[TestSearchInfo]  = deriveDecoder[TestSearchInfo]
  implicit val testTestPollingInfoResponseDecoder: Decoder[TestPollingInfoResponse] =
    deriveDecoder[TestPollingInfoResponse]
  implicit val testSearchStatusAndCriteriaDecoder: Decoder[TestSearchStatusAndCriteria] =
    deriveDecoder[TestSearchStatusAndCriteria]
  implicit val testSearchCriteriaDecoder: Decoder[TestSearchCriteria] = deriveDecoder[TestSearchCriteria]
  implicit val testExternalLoyaltyDisplayItemDecoder: Decoder[TestExternalLoyaltyDisplayItem] =
    deriveDecoder[TestExternalLoyaltyDisplayItem]
  implicit val testObjectExtraInfoDecoder: Decoder[TestObjectExtraInfo] =
    deriveDecoder[TestObjectExtraInfo]
  implicit val testExternalLoyaltyDisplayDecoder: Decoder[TestExternalLoyaltyDisplay] =
    deriveDecoder[TestExternalLoyaltyDisplay]
  implicit val testContentReviewTotalDecoder: Decoder[TestContentReviewTotal] = deriveDecoder[TestContentReviewTotal]
  implicit val testContentReviewDecoder: Decoder[TestContentReview]           = deriveDecoder[TestContentReview]
  implicit val testContentReviewResponseDecoder: Decoder[TestContentReviewResponse] =
    deriveDecoder[TestContentReviewResponse]
  implicit val testContentMasterRoomDecoder: Decoder[TestContentMasterRoom] = deriveDecoder[TestContentMasterRoom]
  implicit val testContentNonHotelAccommodationDecoder: Decoder[TestContentNonHotelAccommodation] =
    deriveDecoder[TestContentNonHotelAccommodation]
  implicit val testContentAggregatedFacilityDecoder: Decoder[TestContentAggregatedFacility] =
    deriveDecoder[TestContentAggregatedFacility]
  implicit val testContentSynopsisDecoder: Decoder[TestContentSynopsis]   = deriveDecoder[TestContentSynopsis]
  implicit val testContentHighlightDecoder: Decoder[TestContentHighlight] = deriveDecoder[TestContentHighlight]
  implicit val testContentFavoriteFeaturesDecoder: Decoder[TestContentFavoriteFeatures] =
    deriveDecoder[TestContentFavoriteFeatures]
  implicit val testCityContentHighlightsDecoder: Decoder[TestCityContentHighlights] =
    deriveDecoder[TestCityContentHighlights]
  implicit val testContentSummaryDecoder: Decoder[TestContentSummary] = deriveDecoder[TestContentSummary]
  implicit val testContentLocalInformationDecoder: Decoder[TestContentLocalInformation] =
    deriveDecoder[TestContentLocalInformation]
  implicit val testContentTransportationDecoder: Decoder[TestContentTransportation] =
    deriveDecoder[TestContentTransportation]
  implicit val testContentAirportDecoder: Decoder[TestContentAirport] = deriveDecoder[TestContentAirport]
  implicit val testContentTransportationMode: Decoder[TestContentTransportationMode] =
    deriveDecoder[TestContentTransportationMode]
  implicit val testPricingBadgesDecoder: Decoder[TestPricingBadges] = deriveDecoder[TestPricingBadges]
  implicit val testPropertySummaryEnrichmentDecoder: Decoder[TestPropertySummaryEnrichment] =
    deriveDecoder[TestPropertySummaryEnrichment]
  implicit val testDistanceInfoDecoder: Decoder[TestDistanceInfo] =
    deriveDecoder[TestDistanceInfo]
  implicit val testDistanceEnrichInfoDecoder: Decoder[TestDistanceEnrichInfo] =
    deriveDecoder[TestDistanceEnrichInfo]
  implicit val testTopSellingPointInfoDecoder: Decoder[TestTopSellingPointInfo] =
    deriveDecoder[TestTopSellingPointInfo]
  implicit val testUniqueSellingPointInfoDecoder: Decoder[TestUniqueSellingPointInfo] =
    deriveDecoder[TestUniqueSellingPointInfo]

  implicit val testDFPricesDecoder: Decoder[TestDFPrices]                   = deriveDecoder[TestDFPrices]
  implicit val testPriceTypeDecoder: Decoder[TestPriceType]                 = deriveDecoder[TestPriceType]
  implicit val testDFRoomPricingDecoder: Decoder[TestDFRoomPricing]         = deriveDecoder[TestDFRoomPricing]
  implicit val testDFCurrencyPriceDecoder: Decoder[TestDFCurrencyPrice]     = deriveDecoder[TestDFCurrencyPrice]
  implicit val testCheapestRoomOfferDecoder: Decoder[TestCheapestRoomOffer] = deriveDecoder[TestCheapestRoomOffer]
  implicit val testDayUseInfoDecoder: Decoder[TestDayUseInfo]               = deriveDecoder[TestDayUseInfo]
  implicit val testDayUseCheckInTimeDecoder: Decoder[TestDayUseCheckInTime] = deriveDecoder[TestDayUseCheckInTime]
  implicit val testHotelPricingDecoder: Decoder[TestHotelPricing]           = deriveDecoder[TestHotelPricing]
  implicit val testPropertySummaryDecoder: Decoder[TestPropertySummary]     = deriveDecoder[TestPropertySummary]
  implicit val testDatelessSummaryDecoder: Decoder[TestDatelessSummary]     = deriveDecoder[TestDatelessSummary]
  implicit val testSoldOutSummaryDecoder: Decoder[TestSoldOutSummary]       = deriveDecoder[TestSoldOutSummary]
  implicit val testSponsoredDetailDecoder: Decoder[TestSponsoredDetail]     = deriveDecoder[TestSponsoredDetail]
  implicit val testPropertyAttributesResponseDecoder: Decoder[TestPropertyAttributesResponse] =
    deriveDecoder[TestPropertyAttributesResponse]
  implicit val testSoldOutPriceDecoder: Decoder[TestSoldOutPrice] = deriveDecoder[TestSoldOutPrice]
  implicit val testPropertyDetailsSearchResponseDecoder: Decoder[TestPropertyDetailsSearchResponse] =
    deriveDecoder[TestPropertyDetailsSearchResponse]
  implicit val dataPropertyDetailsSearchResponseDecoder: Decoder[DataPropertyDetailsSearchResponse] =
    deriveDecoder[DataPropertyDetailsSearchResponse]
  implicit val testPropertyDetailSearchDecoder: Decoder[TestPropertyDetailSearch] =
    deriveDecoder[TestPropertyDetailSearch]
  implicit val testPropertyDetailDecoder: Decoder[TestPropertyDetail] = deriveDecoder[TestPropertyDetail]
  implicit val testContentDetailDecoder: Decoder[TestContentDetail]   = deriveDecoder[TestContentDetail]
  implicit val testContentReviewScoreResponseDecoder: Decoder[TestContentReviewScoreResponse] =
    deriveDecoder[TestContentReviewScoreResponse]
  implicit val testContentImageResponseDecoder: Decoder[TestContentImagesResponse] =
    deriveDecoder[TestContentImagesResponse]
  implicit val testContentLocations: Decoder[TestContentLocations]  = deriveDecoder[TestContentLocations]
  implicit val testImageResponseDecoder: Decoder[TestImageResponse] = deriveDecoder[TestImageResponse]

  implicit val testContentInformationResponseDecoder: Decoder[TestContentInformationResponse] =
    deriveDecoder[TestContentInformationResponse]
  implicit val testContentDescriptionDecoder: Decoder[TestContentDescription] = deriveDecoder[TestContentDescription]
  implicit val testContentPolicyDecoder: Decoder[TestHotelPolicies]           = deriveDecoder[TestHotelPolicies]
  implicit val testNotes: Decoder[TestNotes]                                  = deriveDecoder[TestNotes]
  implicit val testContentLocalInformationResponseDecoder: Decoder[TestContentLocalInformationResponse] =
    deriveDecoder[TestContentLocalInformationResponse]
  implicit val testContentNearbyPropertyDecoder: Decoder[TestContentNearbyProperty] =
    deriveDecoder[TestContentNearbyProperty]
  implicit val testContentPlaceDecoder: Decoder[TestContentPlace] = deriveDecoder[TestContentPlace]

  implicit val testCombinedReviewScoreDecoder: Decoder[TestCombinedReviewScore] = deriveDecoder[TestCombinedReviewScore]

  implicit val testContentGeoInfoDecoder: Decoder[TestContentGeoInfo] =
    deriveDecoder[TestContentGeoInfo]

  implicit val testContentInformationSummaryDecoder: Decoder[TestContentInformationSummary] =
    deriveDecoder[TestContentInformationSummary]

  implicit val testContentAddressSummaryDecoder: Decoder[TestContentAddress] =
    deriveDecoder[TestContentAddress]

  implicit val testContentIdNameDecoder: Decoder[TestContentIdName] =
    deriveDecoder[TestContentIdName]

  implicit val testContentPropertyEngagementResponseDecoder: Decoder[TestContentPropertyEngagementResponse] =
    deriveDecoder[TestContentPropertyEngagementResponse]

  implicit val testContentExperiencesDecoder: Decoder[TestContentExperiences] = deriveDecoder[TestContentExperiences]

  implicit val testContentExperienceDecoder: Decoder[TestContentExperience] = deriveDecoder[TestContentExperience]

  implicit val testContentFeaturesDecoder: Decoder[TestContentFeatures] = deriveDecoder[TestContentFeatures]

  implicit val testContentFeatureGroupDecoder: Decoder[TestContentFeatureGroup] = deriveDecoder[TestContentFeatureGroup]

  implicit val testContentHighlightsDecoder: Decoder[TestContentHighlights] = deriveDecoder[TestContentHighlights]

  implicit val testContentAtfPropertyHighlightsDecoder: Decoder[TestContentAtfPropertyHighlights] =
    deriveDecoder[TestContentAtfPropertyHighlights]

  implicit val testContentPropertyHighlightDecoder: Decoder[TestContentPropertyHighlight] =
    deriveDecoder[TestContentPropertyHighlight]

  implicit val testContentHighlightInformationDecoder: Decoder[TestContentHighlightInformation] =
    deriveDecoder[TestContentHighlightInformation]

  implicit val testCompanyTraceabilityAddressInfoDecoder: Decoder[TestCompanyTraceabilityAddressInfo] =
    deriveDecoder[TestCompanyTraceabilityAddressInfo]

  implicit val testCompanyTraceabilityInfoDecoder: Decoder[TestCompanyTraceabilityInfo] =
    deriveDecoder[TestCompanyTraceabilityInfo]

  implicit val testCompanyTraceabilityDataDecoder: Decoder[TestCompanyTraceabilityData] =
    deriveDecoder[TestCompanyTraceabilityData]

  implicit val testGrowthProgramInfoResponseDecoder: Decoder[TestGrowthProgramInfoResponse] =
    deriveDecoder[TestGrowthProgramInfoResponse]

  implicit val testBannerCampaignInfoDecoder: Decoder[TestBannerCampaignInfo] = deriveDecoder[TestBannerCampaignInfo]

  implicit val testPriceStreamPropertyMetaInfoResponseDecoder: Decoder[TestPriceStreamPropertyMetaInfoResponse] =
    deriveDecoder[TestPriceStreamPropertyMetaInfoResponse]
  implicit val testHostProfileDecoder: Decoder[TestHostProfile]   = deriveDecoder[TestHostProfile]
  implicit val testHostPropertyDecoder: Decoder[TestHostProperty] = deriveDecoder[TestHostProperty]
  implicit val testPriceStreamPropertyMetaRankingDecoder: Decoder[TestPriceStreamPropertyMetaRanking] =
    deriveDecoder[TestPriceStreamPropertyMetaRanking]
  implicit val testPriceStreamRankMetricDecoder: Decoder[TestPriceStreamRankMetric] =
    deriveDecoder[TestPriceStreamRankMetric]

  implicit val testPropertySummarySearchResponseDecoder: Decoder[TestPropertySummarySearchResponse] =
    deriveDecoder[TestPropertySummarySearchResponse]
  implicit val dataPropertySummarySearchResponseDecoder: Decoder[DataPropertySummarySearchResponse] =
    deriveDecoder[DataPropertySummarySearchResponse]
  implicit val testPropertySummarySearchDecoder: Decoder[TestPropertySummarySearch] =
    deriveDecoder[TestPropertySummarySearch]

  implicit val testContentRateCategoryDecoder: Decoder[TestContentRateCategory] = deriveDecoder[TestContentRateCategory]

  implicit val testRateCategoryDecoder: Decoder[TestRateCategory] = deriveDecoder[TestRateCategory]

  implicit val testPriceStreamPropertyAttributesResponseDecoder: Decoder[TestPriceStreamPropertyAttributesResponse] =
    deriveDecoder[TestPriceStreamPropertyAttributesResponse]
  implicit val testPropertiesAttributesResponseDecoder: Decoder[TestPropertiesAttributesResponse] =
    deriveDecoder[TestPropertiesAttributesResponse]
  implicit val testPropertyAttributesDecoder: Decoder[TestPropertyAttributes] =
    deriveDecoder[TestPropertyAttributes]
  implicit val testLandmarkInfoDecoder: Decoder[TestLandmarkInfo] = deriveDecoder[TestLandmarkInfo]
  implicit val testPropertyLandmarkInfoDecoder: Decoder[TestPropertyLandmarkInfo] =
    deriveDecoder[TestPropertyLandmarkInfo]
  implicit val testGeoPlaceDecoder: Decoder[TestGeoPlace] = deriveDecoder[TestGeoPlace]

  implicit val testPropertyAttributeDecoder: Decoder[TestPropertyAttribute] = deriveDecoder[TestPropertyAttribute]

  implicit val testContentQnaResponseDecoder: Decoder[TestContentQnaResponse] = deriveDecoder[TestContentQnaResponse]

  implicit val testQnaDecoder: Decoder[TestQna] = deriveDecoder[TestQna]

  implicit val testContentTopicsResponseDecoder: Decoder[TestContentTopicsResponse] =
    deriveDecoder[TestContentTopicsResponse]

  implicit val testContentTopicDecoder: Decoder[TestContentTopic] = deriveDecoder[TestContentTopic]

  implicit val testContentTopicSentimentScoreDecoder: Decoder[TestContentTopicSentimentScore] =
    deriveDecoder[TestContentTopicSentimentScore]

  implicit val testContentTopicImageDecoder: Decoder[TestContentTopicImage] = deriveDecoder[TestContentTopicImage]

  implicit val testDataAreaSearchResponseDecoder = deriveDecoder[DataAreaSearchResponse]

  implicit val testAreaSearchResponseDecoder = deriveDecoder[TestAreaSearchResponse]

  implicit val testDataLandmarkSearchResponseDecoder = deriveDecoder[DataLandmarkSearchResponse]

  implicit val testLandmarkSearchResponseDecoder = deriveDecoder[TestLandmarkSearchResponse]

  implicit val testDataRadiusSearchResponseDecoder = deriveDecoder[DataRadiusSearchResponse]

  implicit val testRadiusSearchResponseDecoder = deriveDecoder[TestRadiusSearchResponse]

  implicit val testDataRegionSearchResponseDecoder = deriveDecoder[DataRegionSearchResponse]

  implicit val testRegionSearchResponseDecoder = deriveDecoder[TestRegionSearchResponse]

  implicit val testDataTopAreaSearchResponseDecoder = deriveDecoder[DataTopAreaSearchResponse]

  implicit val testTopAreaSearchResponseDecoder = deriveDecoder[TestTopAreaSearchResponse]

  implicit val testDataCityCenterSearchResponseDecoder = deriveDecoder[DataCityCenterSearchResponse]

  implicit val testCityCenterSearchResponseDecoder = deriveDecoder[TestCityCenterSearchResponse]

  implicit val testDataStateSearchResponseDecoder = deriveDecoder[DataStateSearchResponse]

  implicit val testStateSearchResponseDecoder = deriveDecoder[TestStateSearchResponse]

  implicit val testCheapestPriceByCheckInDecoder = deriveDecoder[TestCheapestPriceByCheckIn]

  implicit val testPriceTrendResponseDecoder = deriveDecoder[TestPriceTrendResponse]

  implicit val testPropertyAllotmentSearchDecoder = deriveDecoder[TestPropertyAllotmentSearch]

  implicit val propertyAllotmentSearchWithDetailsDecoder = deriveDecoder[TestPropertyAllotmentSearchWithDetails]

  implicit val dataPropertyAllotmentSearchResponseDecoder = deriveDecoder[DataPropertyAllotmentSearchResponse]

  implicit val testPropertyAllotmentSearchResponseDecoder = deriveDecoder[TestPropertyAllotmentSearchResponse]
}
