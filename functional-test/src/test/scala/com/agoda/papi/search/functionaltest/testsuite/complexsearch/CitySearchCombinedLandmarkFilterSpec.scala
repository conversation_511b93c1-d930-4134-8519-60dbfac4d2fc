package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestAreas._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.enumeration.matrix.FilterKeys
import com.agoda.core.search.models.{ FilterRequest, IdsFilterRequest }
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchCombinedLandmarkFilterSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  val landmark1 = 32423L
  val landmark2 = 32425L
  val landmark3 = 32448L
  val landmark4 = 32452L

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark1, landmark2, landmark3))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withHotelAreaId(area2)
      .withLandmarkIds(List(landmark1, landmark2))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark4))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .withHotelAreaId(area2)
      .withLandmarkIds(List(landmark1))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .withHotelAreaId(area3)
      .withLandmarkIds(List(landmark1))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel6)
      .withHotelAreaId(area1)
      .withLandmarkIds(List.empty)
      .build()
  )

  val propertyIds = List(hotel1, hotel2, hotel3, hotel4, hotel5, hotel6)

  private def executeTestCase(
      propertyIds: List[Long]
  ): Future[HttpResponse] = {
    val searchContext = aValidSearchContext.copy(endpointSearchType = Some(SearchTypes.CitySearch))
    val citySearchRequest = aValidCitySearchRequest
      .withSearchContext(searchContext)
      .withSearchCriteria(aValidSearchCriteria.build())
      .withFilterRequest(
        FilterRequest(
          idsFilters = Some(
            List(
              IdsFilterRequest(
                FilterKeys.HotelAreaId,
                ids = List(area1, area2)
              ),
              IdsFilterRequest(
                FilterKeys.LandmarkIds,
                ids = List(landmark1, landmark2)
              )
            )
          )
        )
      )
      .build()

    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val avail            = generateDefaultAvail(propertyIds)
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)

    val mockModule = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockESMatrixService(client)
    }

    executeSSRGraphQL(
      query,
      mockModule
    )
  }

  // migrate from CitySearch-CombinedLandmarkFilter.feature : TC1
  "City search" should {
    "return properties for 2 landmarkIds filters and 2 area id filters" in {
      val respF = executeTestCase(propertyIds)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels shouldBe 3
        citySearchResponse.data.citySearch.properties.get.map(_.propertyId) should equal(List(hotel1, hotel2, hotel4))
      }
    }
  }

}
