package com.agoda.papi.search.functionaltest.testsuite.pricestream

import akka.http.scaladsl.model.StatusCodes
import com.agoda.commons.http.client.v2.RequestSettings
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.agoda.pricestream.models.request.CityPriceTrendRequest
import com.agoda.pricestream.models.response.{
  CityPriceByCheckIn => PriceStreamCityPriceByCheckIn,
  CityPriceTrendResponse
}
import framework.MockModule
import framework.testservice.PriceStreamHttpClientTest
import metaapi.PriceTrendByCityRequest
import models.starfruit.SuggestedPrice
import org.joda.time.format.ISODateTimeFormat
import util.builders.TestDataBuilders._

import scala.concurrent.Future

// Migrating cityPriceTrend.feature
class CityPriceTrendSpec extends PAPISearchFunctionalTest {

  val activeABTest = new ActiveABTests()

  // Common test parameters from the Background section
  private val los            = 1
  private val isUserLoggedIn = false
  private val cityId         = 16056
  private val startDate      = ISODateTimeFormat.dateTimeParser().parseDateTime("2019-03-01T00:00:00.0000+07:00")
  private val endDate        = ISODateTimeFormat.dateTimeParser().parseDateTime("2019-03-09T00:00:00.0000+07:00")
  private val adults         = 2
  private val ratePlanId     = 1
  private val currency       = "EUR"

  // Creating a custom PriceStreamHttpClientTest implementation
  class TestPriceStreamHttpClient extends PriceStreamHttpClientTest {

    override def getCityPriceTrend(
        request: CityPriceTrendRequest,
        settings: RequestSettings
    ): Future[CityPriceTrendResponse] =
      // Return an empty CityPriceTrendResponse for successful test
      Future.successful(
        CityPriceTrendResponse(
          cityId = request.cityId,
          prices = Seq(PriceStreamCityPriceByCheckIn(checkIn = startDate, price = 100.0)),
          cheapestPrices = None
        )
      )

  }

  private def executeTestCase(
      suggestPrice: Option[Int] = None,
      requiredPrice: Option[String] = None,
      experimentVariant: Option[Char] = None
  ) = {
    val supportedChannel = List(ratePlanId)

    // Map the required price string to SuggestedPrice enum
    val mappedRequiredPrice = requiredPrice.map {
      case "Exclusive"    => SuggestedPrice.Exclusive
      case "AllInclusive" => SuggestedPrice.AllInclusive
      case _              => SuggestedPrice.Exclusive
    }

    val forceVariant = experimentVariant.getOrElse('A').toString

    val ctx = aValidContext
      .withAdditionalExperiments(activeABTest.pricestreamCityPriceTrendNegativeExp, forceVariant)
      .build()

    val request = PriceTrendByCityRequest(
      context = Some(ctx), // Add context to ensure experiment is activated
      cityId = cityId,
      los = los,
      occupancy = adults,
      startDate = startDate,
      endDate = endDate,
      supportedChannel = supportedChannel,
      isUserLoggedIn = isUserLoggedIn,
      suggestedPrice = suggestPrice.getOrElse(0),
      currency = currency,
      requiredPrice = mappedRequiredPrice,
      hotelStarRating = None,
      cheapest = None
    )

    val priceStreamHttpClientMock = new TestPriceStreamHttpClient()

    // Create a single MockModule for all cases
    val module = new MockModule {
      bind[PriceStreamHttpClient[Future]] to priceStreamHttpClientMock
    }

    executeCityPriceTrend(
      request,
      module
    )
  }

  "City Price Trend Search" should {
    "return successful results with suggestPrice parameter" in {
      // Test case 1 with suggestPrice
      val suggestPrice = Some(1)
      val respF        = executeTestCase(suggestPrice = suggestPrice)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val cityTrend = responseToCityPriceTrend(resp)
        cityTrend.cityId shouldBe cityId
        cityTrend.prices should not be empty
      }
    }

    "return empty results with suggestPrice parameter when FUSION-5238 experiment is on B side" in {
      // Test case 1 with suggestPrice - B side
      val suggestPrice = Some(1)
      val respF        = executeTestCase(suggestPrice = suggestPrice, experimentVariant = Some('B'))

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val cityTrend = responseToCityPriceTrend(resp)
        cityTrend.cityId shouldBe cityId
        cityTrend.prices shouldBe empty  // B side should return empty price list
        cityTrend.cheapest shouldBe None // B side should return None for cheapest
      }
    }

    "return successful results with RequiredPrice Exclusive" in {
      // Test case 2 with RequiredPrice Exclusive
      val requiredPrice = Some("Exclusive")
      val respF         = executeTestCase(requiredPrice = requiredPrice)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val cityTrend = responseToCityPriceTrend(resp)
        cityTrend.cityId shouldBe cityId
        cityTrend.prices should not be empty
      }
    }

    "return empty results with RequiredPrice Exclusive when FUSION-5238 experiment is on B side" in {
      // Test case 2 with RequiredPrice Exclusive - B side
      val requiredPrice = Some("Exclusive")
      val respF         = executeTestCase(requiredPrice = requiredPrice, experimentVariant = Some('B'))

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val cityTrend = responseToCityPriceTrend(resp)
        cityTrend.cityId shouldBe cityId
        cityTrend.prices shouldBe empty  // B side should return empty price list
        cityTrend.cheapest shouldBe None // B side should return None for cheapest
      }
    }

    "return successful results with RequiredPrice AllInclusive" in {
      // Test case 3 with RequiredPrice AllInclusive
      val requiredPrice = Some("AllInclusive")
      val respF         = executeTestCase(requiredPrice = requiredPrice)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val cityTrend = responseToCityPriceTrend(resp)
        cityTrend.cityId shouldBe cityId
        cityTrend.prices should not be empty
      }
    }

    "return empty results with RequiredPrice AllInclusive when FUSION-5238 experiment is on B side" in {
      // Test case 3 with RequiredPrice AllInclusive - B side
      val requiredPrice = Some("AllInclusive")
      val respF         = executeTestCase(requiredPrice = requiredPrice, experimentVariant = Some('B'))

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val cityTrend = responseToCityPriceTrend(resp)
        cityTrend.cityId shouldBe cityId
        cityTrend.prices shouldBe empty  // B side should return empty price list
        cityTrend.cheapest shouldBe None // B side should return None for cheapest
      }
    }
  }

}
