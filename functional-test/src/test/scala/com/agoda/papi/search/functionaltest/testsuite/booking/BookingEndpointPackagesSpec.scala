package com.agoda.papi.search.functionaltest.testsuite.booking

import akka.http.scaladsl.model.StatusCodes
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.request.Packaging
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{ ArrangementEntries, ProductEntries }
import com.agoda.upi.models.request.{ CartBaseRequest, CartMetadata }
import framework.MockModule
import org.scalatest.prop.TableDrivenPropertyChecks
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class BookingEndpointPackagesSpec extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {

  "BookingEndpoint /api/booking" should {

    val defaultHotel = aValidDFHotel.withHotelId(aValidHotelId)

    val defaultRoom =
      aValidDFRoom
        .withRoomTypeId(1L)
        .withRoomIdentifier("a")
        .build()

    "report package request count when packaging is present" in {
      val roomWithPackaging = defaultRoom.withPackaging(Some(aValidDFPackaging)).build()
      val dfHotel           = defaultHotel.withRooms(Seq(roomWithPackaging)).build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelId)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val mockReportingService = mock[MetricsReporter]

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = Seq(dfHotel))))

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest.withPackaging(Some(Packaging(None))))
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)))
        .build()

      val respF = executeBookingEndpoint(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/booking",
        MeasurementTag.PackagingFlowType -> "Package"
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        childRoom.packaging.nonEmpty shouldBe true

        verify(mockReportingService, times(1)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }

    "report package request count when cart request with Package product entry is present" in {
      val dfHotel = defaultHotel.withRooms(Seq(defaultRoom)).build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelId)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val mockReportingService = mock[MetricsReporter]

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = Seq(dfHotel))))

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val cartMeta = CartMetadata(productEntry = Some(ProductEntries.PackageProduct), itemMetadata = List.empty)
      val cartBaseRequest = CartBaseRequest(
        token = None,
        arrangement = Some(
          Arrangement(`type` = ArrangementEntries.Bundle, refId = None)
        ),
        sourceId = None,
        srcId = None,
        meta = Some(cartMeta)
      )

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)).withCartRequest(Some(cartBaseRequest)))
        .build()

      val respF = executeBookingEndpoint(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/booking",
        MeasurementTag.PackagingFlowType -> "PackageToCartMigration"
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        verify(mockReportingService, times(1)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }

    "do not report package request count if there is a normal hotel-search" in {
      val dfHotel = defaultHotel.withRooms(Seq(defaultRoom)).build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelId)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val mockReportingService = mock[MetricsReporter]

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = Seq(dfHotel))))

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)))
        .build()

      val respF = executeBookingEndpoint(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/booking",
        MeasurementTag.PackagingFlowType -> "NonPackage"
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        verify(mockReportingService, times(0)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }
  }

}
