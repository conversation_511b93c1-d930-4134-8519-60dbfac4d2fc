import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.db.information.RoomInformationResponse
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import framework.MockModule
import models.starfruit.{ DisplayBasis, DisplayPrice, Pricing, Room, SuggestedPrice }
import request.SearchTypes
import transformers.{ EnrichedMasterRoom, RecommendedUpgrade }
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchRecommendedUpgradeSpec extends PAPISearchFunctionalTest {

  "PropertySearch /api/property" should {

    case class TestRoomData(
        typeId: Long,
        size: Double,
        price: Double,
        roomIdentifier: String
    )

    def createTestRooms(data: Seq[TestRoomData]): (Seq[EnrichedMasterRoom], Seq[Room]) = {
      val enrichedMasterRooms = data.map { d =>
        aValidEnrichedMasterRoom
          .withTypeId(d.typeId)
          .withFeatures(Vector(aValidFeatureResponse.copy(name = "room-size-sqft", text = d.size.toString)))
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom
                .withPricing(
                  Map(
                    "USD" -> aValidEnrichedPricing
                      .withDisplay(
                        aValidDisplayBasis
                          .withPerBook(aValidDisplayPrice.withAllInclusive(d.price).withExclusive(d.price))
                      )
                      .build()
                  )
                )
                .build()
            )
          )
          .build()
      }

      val dfRooms = data.map { d =>
        val displayPrice = DisplayPrice(d.price, d.price)
        val displayBasis = DisplayBasis(displayPrice, displayPrice, displayPrice)
        val pricing = Pricing(
          displayBasis,
          displayBasis,
          Seq(),
          Seq(),
          models.pricing.enums.CorTypeFlags.NoCOR,
          aValidDisplaySummary
        )
        aValidDFRoom
          .withRoomTypeId(d.typeId)
          .withRoomIdentifier(d.roomIdentifier)
          .withMasterTypeId(d.typeId)
          .withPricing(Map("USD" -> pricing))
          .build()
      }

      (enrichedMasterRooms, dfRooms)
    }

    def createDFHotel(hotelId: Long, dfRooms: Seq[Room]): models.starfruit.Hotel =
      aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(dfRooms)
        .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
        .build()

    def createContentMasterRooms(hotelId: Long, masterRooms: Seq[EnrichedMasterRoom]): RoomInformationResponse =
      aValidRoomInformationResponse
        .withHotelId(hotelId)
        .withMasterRoomResponse(
          masterRooms.map { mr =>
            val roomSizeFeatureOpt = mr.features.find(_.name == "room-size-sqft")
            val featureValue       = roomSizeFeatureOpt.map(_.text).getOrElse("0.0")
            aValidMasterRoomResponse
              .withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(mr.typeId).build())
              .withFeatures(Vector(aValidFeatureResponse.copy(name = "room-size-sqft", text = featureValue)))
              .build()
          }.toVector
        )
        .build()

    "return recommendedUpgrade for the first eligible master room and stop further processing" in {
      val hotelId = 2004L
      val testRooms = Seq(
        TestRoomData(1L, 100, 100, "room1"), // base
        TestRoomData(2L, 109, 104, "room2"), // 9% size, 4% price (not eligible)
        TestRoomData(3L, 115, 110, "room3"), // 15% size, 10% price (eligible)
        TestRoomData(4L, 130, 112, "room4")  // would also be eligible, but should not be checked
      )
      val (masterRooms, dfRooms) = createTestRooms(testRooms)
      val dfHotel                = createDFHotel(hotelId, dfRooms)
      val contentMasterRooms     = createContentMasterRooms(hotelId, masterRooms)

      val module = new MockModule {
        mockDFResponse(Map(hotelId -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withFeatureFlag(aValidFeatureFlagRequest.copy(enableRecommendedUpgrade = Some(true)))
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties  = responseToProperties(resp)
        val masterRooms = properties.property.head.masterRooms
        masterRooms.lift(1).flatMap(_.recommendedUpgrade) shouldBe None
        masterRooms.lift(2).flatMap(_.recommendedUpgrade) shouldBe Some(
          RecommendedUpgrade(
            priceDiff = 10.0,
            roomSizeDiffPercent = Some(15.0)
          )
        )
        masterRooms.lift(3).flatMap(_.recommendedUpgrade) shouldBe None // not checked
      }
    }

    "stop processing further master rooms if price diff > 15% is encountered" in {
      val hotelId = 2005L
      val testRooms = Seq(
        TestRoomData(1L, 100, 100, "room1"), // base
        TestRoomData(2L, 109, 104, "room2"), // 9% size, 4% price (not eligible)
        TestRoomData(3L, 120, 116, "room3"), // 20% size, 16% price (not eligible, price diff > 15%)
        TestRoomData(4L, 130, 120, "room4")  // would be eligible, but should not be checked
      )
      val (masterRooms, dfRooms) = createTestRooms(testRooms)
      val dfHotel                = createDFHotel(hotelId, dfRooms)
      val contentMasterRooms     = createContentMasterRooms(hotelId, masterRooms)

      val module = new MockModule {
        mockDFResponse(Map(hotelId -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withFeatureFlag(aValidFeatureFlagRequest.copy(enableRecommendedUpgrade = Some(true)))
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties  = responseToProperties(resp)
        val masterRooms = properties.property.head.masterRooms
        masterRooms.lift(1).flatMap(_.recommendedUpgrade) shouldBe None
        masterRooms.lift(2).flatMap(_.recommendedUpgrade) shouldBe None // price diff > 15%
        masterRooms.lift(3).flatMap(_.recommendedUpgrade) shouldBe None // not checked
      }
    }

    "not return recommendedUpgrade if criteria are not met" in {
      val hotelId = 2002L
      val testRooms = Seq(
        TestRoomData(1L, 100, 100, "room1"),
        TestRoomData(2L, 105, 120, "room2") // 5% bigger, 20% more expensive
      )
      val (masterRooms, dfRooms) = createTestRooms(testRooms)
      val dfHotel                = createDFHotel(hotelId, dfRooms)
      val contentMasterRooms     = createContentMasterRooms(hotelId, masterRooms)

      val module = new MockModule {
        mockDFResponse(Map(hotelId -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withFeatureFlag(aValidFeatureFlagRequest.copy(enableRecommendedUpgrade = Some(true)))
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties         = responseToProperties(resp)
        val recommendedUpgrade = properties.property.head.masterRooms.lift(1).flatMap(_.recommendedUpgrade)
        recommendedUpgrade shouldBe None
      }
    }

    "not return recommendedUpgrade if feature flag is disabled" in {
      val hotelId = 2003L
      val testRooms = Seq(
        TestRoomData(1L, 100, 100, "room1"),
        TestRoomData(2L, 130, 109, "room2") // Would qualify if flag enabled
      )
      val (masterRooms, dfRooms) = createTestRooms(testRooms)
      val dfHotel                = createDFHotel(hotelId, dfRooms)
      val contentMasterRooms     = createContentMasterRooms(hotelId, masterRooms)

      val module = new MockModule {
        mockDFResponse(Map(hotelId -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withFeatureFlag(aValidFeatureFlagRequest.copy(enableRecommendedUpgrade = Some(false)))
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties         = responseToProperties(resp)
        val recommendedUpgrade = properties.property.head.masterRooms.lift(1).flatMap(_.recommendedUpgrade)
        recommendedUpgrade shouldBe None
      }
    }
  }

}
