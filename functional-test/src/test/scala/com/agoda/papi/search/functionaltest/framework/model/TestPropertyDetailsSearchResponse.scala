package com.agoda.papi.search.functionaltest.framework.model

import com.agoda.content.models.db.information.CompanyTraceabilityAddressInfo

import java.util.UUID

case class TestPropertyDetailsSearchResponse(data: DataPropertyDetailsSearchResponse)

case class DataPropertyDetailsSearchResponse(
    propertyDetailsSearch: TestPropertyDetailSearch
)

case class TestPropertyDetailSearch(
    propertyDetails: List[TestPropertyDetail]
)

case class TestPropertyDetail(
    propertyId: Long,
    contentDetail: Option[TestContentDetail],
    metaLab: Option[TestPriceStreamPropertyAttributesResponse],
    growthProgramInfo: Option[TestGrowthProgramInfoResponse],
    bannerCampaignInfo: Option[Vector[TestBannerCampaignInfo]],
    propertyMetaInfo: Option[TestPriceStreamPropertyMetaInfoResponse]
)

case class TestHostProfile(
    userId: UUID,
    displayName: Option[String] = None,
    properties: Option[List[TestHostProperty]] = None
)

case class TestHostProperty(
    id: Long,
    bookings: Option[Int],
    reviewCount: Option[Int],
    reviewAvg: Option[Double]
)

case class TestPriceStreamPropertyMetaInfoResponse(
    propertyId: Long,
    propertyMetaRanking: Option[TestPriceStreamPropertyMetaRanking]
)

case class TestPriceStreamPropertyMetaRanking(
    numberOfProperty: Option[Int],
    metrics: Option[Vector[TestPriceStreamRankMetric]]
)

case class TestPriceStreamRankMetric(
    metricName: Option[String],
    rank: Option[Int],
    absoluteValue: Option[Double]
)

case class TestBannerCampaignInfo(
    campaignId: Int,
    campaignName: String,
    ranking: Int
)

case class TestContentDetail(
    contentReviewScore: Option[TestContentReviewScoreResponse],
    contentSummary: Option[TestContentInformationSummary],
    contentEngagement: Option[TestContentPropertyEngagementResponse],
    contentExperiences: Option[TestContentExperiences],
    contentFeatures: Option[TestContentFeatures],
    contentRateCategory: Option[TestContentRateCategory],
    contentQna: Option[TestContentQnaResponse],
    contentHighlights: Option[TestContentHighlights],
    contentImages: Option[TestContentImagesResponse],
    contentLocalInformation: Option[TestContentLocalInformationResponse],
    contentInformation: Option[TestContentInformationResponse],
    contentTopics: Option[TestContentTopicsResponse],
    hostProfile: Option[TestHostProfile]
)

case class TestContentImagesResponse(
    mainImage: Option[TestImageResponse],
    hotelImages: Vector[TestImageResponse]
)

case class TestImageResponse(
    id: Option[Long],
    caption: Option[String],
    urls: Option[Vector[TestContentLocations]]
)

case class TestContentInformationResponse(
    propertyId: Long,
    description: Option[TestContentDescription],
    policies: Option[TestHotelPolicies],
    notes: Option[TestNotes],
    companyTraceabilityData: Option[TestCompanyTraceabilityData]
)

case class TestContentDescription(
    long: Option[String],
    short: Option[String]
)

case class TestHotelPolicies(
    extraBed: Option[Vector[String]]
)

case class TestNotes(
    publicNotes: Option[Vector[String]]
)

case class TestContentLocalInformationResponse(
    nearbyProperties: Option[Vector[TestContentNearbyProperty]],
    nearbyPlaces: Option[Vector[TestContentPlace]],
    topPlaces: Option[Vector[TestContentPlace]],
    popularLandmarkNumber: Option[Int]
)

case class TestContentNearbyProperty(
    id: Option[String]
)

case class TestContentPlace(
    name: Option[String]
)

case class TestContentReviewScoreResponse(
    combinedReviewScore: Option[TestCombinedReviewScore]
)

case class TestCombinedReviewScore(
    providerIds: Option[Vector[Int]]
)

case class TestContentInformationSummary(
    localeName: Option[String],
    rating: Option[Double],
    displayName: Option[String],
    address: Option[TestContentAddress],
    propertyType: Option[String],
    hasHostExperience: Option[Boolean],
    accommodationType: Option[Int],
    geoInfo: Option[TestContentGeoInfo]
)

case class TestContentAddress(
    country: Option[TestContentIdName],
    city: Option[TestContentIdName],
    area: Option[TestContentIdName]
)

case class TestContentIdName(id: Option[Long], name: Option[String])

case class TestContentPropertyEngagementResponse(
    propertyId: Option[Long],
    peopleLooking: Option[String]
)

case class TestContentExperiences(experience: Option[Vector[TestContentExperience]])

case class TestContentExperience(name: Option[String], symbol: Option[String])

case class TestContentFeatures(featureGroups: Option[Vector[TestContentFeatureGroup]])

case class TestContentFeatureGroup(id: Option[Int], name: Option[String])

case class TestGrowthProgramInfoResponse(
    badges: List[String]
)

case class TestContentHighlights(
    favoriteFeatures: Option[Vector[TestContentPropertyHighlight]],
    locationHighlightMessage: Option[TestContentHighlightInformation],
    atfPropertyHighlights: Option[Vector[TestContentAtfPropertyHighlights]]
)

case class TestContentAtfPropertyHighlights(name: Option[String], topicName: Option[String])

case class TestContentPropertyHighlight(name: Option[String])

case class TestContentHighlightInformation(title: Option[String])

case class TestContentRateCategory(rateCategories: Option[Vector[TestRateCategory]])

case class TestRateCategory(rateCategoryId: Option[Long], localizedRateCategoryName: Option[String])

case class TestPropertiesAttributesResponse(properties: List[TestPropertyAttributes])
case class TestPropertyAttributes(propertyId: Long, attributes: List[TestPropertyAttribute])

case class TestPriceStreamPropertyAttributesResponse(propertyAttributes: Option[Vector[TestPropertyAttribute]])

case class TestPropertyAttribute(
    attributeId: Option[Long],
    dataType: Option[String],
    version: Option[String],
    value: Option[String]
)

case class TestContentQnaResponse(
    qnas: Option[Vector[TestQna]]
)

case class TestQna(
    id: Option[Long],
    question: Option[String],
    answer: Option[String]
)

case class TestContentLocations(
    key: Option[String],
    value: Option[String]
)

case class TestContentGeoInfo(
    latitude: Option[Float],
    longitude: Option[Float],
    obfuscatedLat: Option[Float],
    obfuscatedLong: Option[Float]
)

case class TestContentTopicsResponse(topics: Option[Vector[TestContentTopic]])

case class TestContentTopic(
    topic: Option[String],
    sentimentScore: Option[TestContentTopicSentimentScore],
    reviewSnippets: Option[Vector[String]],
    images: Option[Vector[TestContentTopicImage]],
    summary: Option[String]
)

case class TestContentTopicSentimentScore(
    positive: Option[Double],
    negative: Option[Double],
    neutral: Option[Double]
)

case class TestContentTopicImage(
    url: Option[String],
    imageId: Option[Long],
    imageSource: Option[String]
)

case class TestCompanyTraceabilityAddressInfo(
    addressUnit: Option[String],
    addressFloor: Option[String],
    addressBuilding: Option[String],
    addressStreet1: Option[String],
    addressStreet2: Option[String],
    city: Option[String],
    stateId: Option[Int],
    stateName: Option[String],
    countryId: Option[Int],
    countryName: Option[String],
    postalCode: Option[String]
)

case class TestCompanyTraceabilityInfo(
    tradingName: Option[String],
    address: Option[TestCompanyTraceabilityAddressInfo],
    email: Option[String],
    phoneNumber: Option[String],
    registrationNo: Option[String],
    chainSpecificComplianceText: Option[String],
    representativeName: Option[String]
)

case class TestCompanyTraceabilityData(
    requiredLayoutType: Option[String],
    traderInformationList: Option[List[TestCompanyTraceabilityInfo]]
)
