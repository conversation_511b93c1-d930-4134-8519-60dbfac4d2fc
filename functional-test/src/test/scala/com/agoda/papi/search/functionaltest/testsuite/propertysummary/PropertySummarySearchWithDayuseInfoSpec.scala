package com.agoda.papi.search.functionaltest.testsuite.propertysummary

import akka.http.scaladsl.model.StatusCodes
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.papi.search.common.util.ContentDataExample.aValidContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.DFDataExample.aPricingRequestParameters
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.{ TestDayUseCheckInTime, TestDayUseInfo }
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.pricing.{ PAPIDayuseCheckInTime, PAPIDayuseInfo }
import com.agoda.papi.search.internalmodel.request.PropertySummarySearchRequest
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.property.search.utils.builders.GqlTestDataBuilders.toDFHotelPricingBuilder

class PropertySummarySearchWithDayuseInfoSpec extends PAPISearchFunctionalTest {

  "PropertySummarySearch" should {
    "return dayuseInfo from from DF" in {
      val query = QueryBuilder.buildPropertySummarySearchQuery(
        """
          |query propertySummarySearch(
          |  $PropertySummarySearchRequest: PropertySummarySearchRequest!,
          |  $PricingSummaryRequest: PricingRequestParameters!,
          |  $ContentSummaryRequest: ContentSummaryRequest!
          |) {
          |  propertySummarySearch(PropertySummarySearchRequest: $PropertySummarySearchRequest) {
          |    properties(
          |      PricingSummaryRequest: $PricingSummaryRequest,
          |      ContentSummaryRequest: $ContentSummaryRequest
          |    ) {
          |      propertyId
          |      propertyResultType
          |      pricing {
          |        dayuseInfo {
          |          dayuseCheckInTimes {
          |            hourOfDay
          |            minuteOfHour
          |         }
          |        }
          |      }
          |    }
          |  }
          |}
          |""".stripMargin,
        PropertySummarySearchRequest(List(hotel1)),
        Some(aPricingRequestParameters),
        Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t),
        None,
        None
      )

      val dayuseCheckInTimes = Seq(PAPIDayuseCheckInTime(10), PAPIDayuseCheckInTime(14))
      val papiDayuseInfo     = PAPIDayuseInfo(dayuseCheckInTimes)
      val hotelPricing = DFDataExample
        .aValidPAPIHotelPricing(
          1L,
          dayuseInfo = Some(papiDayuseInfo)
        )
        .withHotelId(hotel1)
        .build()

      val module = new GqlMockModule {
        mockDfService(Map(hotel1 -> hotelPricing))
      }

      val respF = executePropertySummarySearchGraphQL(query = query, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val propertySummarySearchResponse = responseToPropertySummarySearchResponse(resp)
        val dayuseInfo = propertySummarySearchResponse.data.propertySummarySearch.properties.headOption
          .flatMap(_.pricing)
          .flatMap(_.dayuseInfo)

        dayuseInfo shouldBe Some(
          TestDayUseInfo(Some(List(TestDayUseCheckInTime(Some(10), Some(0)), TestDayUseCheckInTime(Some(14), Some(0)))))
        )
      }
    }
  }

}
