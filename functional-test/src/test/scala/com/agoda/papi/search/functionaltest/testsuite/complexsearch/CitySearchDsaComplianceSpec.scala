package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.commons.dynamic.state.State
import com.agoda.complexsearch.flowtest.gql.helper.GqlTestHelpers
import com.agoda.complexsearch.flowtest.gql.search.BaseGQLIntegrationTest
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.util.ContentDataExample._
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.matchers.should.Matchers

import scala.concurrent.Future

class CitySearchDsaComplianceSpec
    extends PAPISearchFunctionalTest with Matchers with BaseGQLIntegrationTest with GqlTestHelpers {

  private def executeTestCase(
      dsaComplianceStatus: Boolean,
      origin: String,
      isHotelCreatedBeforeFeb2024: Boolean,
      blockDSANonCompliantHotelRegisteredBeforeFeb2024: Boolean = false
  ): Future[HttpResponse] = {

    val query = QueryBuilder.build(
      aValidSSRQuery,
      aValidCitySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val propertyMetadata = PropertyInfoList(
      Seq(
        aValidCachePropertyInfo
          .withHotelId(10001L)
          .withDsaComplianceStatus(dsaComplianceStatus)
          .withIsHotelCreatedBeforeFeb2024(isHotelCreatedBeforeFeb2024)
      )
    )

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      if (blockDSANonCompliantHotelRegisteredBeforeFeb2024) {
        (bind[State[Boolean]] to State.of(true)).identifiedBy('BlockDSANonCompliantHotelRegisteredBeforeFeb2024)
      }
    }

    val headers = aValidSSRHttpHeaders ++ Seq(
      RawHeader("AG-ORIGIN", origin)
    )

    executeSSRGraphQL(
      query,
      module,
      headers
    )
  }

  private def assertResponseSize(
      resp: HttpResponse,
      expectedSize: Int
  ): Unit = {
    resp.status shouldBe StatusCodes.OK
    val citySearchResponse = responseToCitySearchResponse(resp)
    val propertyIds        = citySearchResponse.data.citySearch.properties.get.map(_.propertyId)
    propertyIds.size shouldBe expectedSize
  }

  private def runTest(
      dsaComplianceStatus: Boolean,
      origin: String,
      isHotelCreatedBeforeFeb2024: Boolean,
      blockDSANonCompliantHotelRegisteredBeforeFeb2024: Boolean = false,
      expectedSize: Int
  ): Unit = {
    val respF = executeTestCase(
      dsaComplianceStatus,
      origin,
      isHotelCreatedBeforeFeb2024,
      blockDSANonCompliantHotelRegisteredBeforeFeb2024
    )
    whenReady(respF) { resp =>
      assertResponseSize(resp, expectedSize)
    }
  }

  "Only compliant properties should be returned" should {
    "Always return property for non-EU origin user regardless of compliance status" should {
      "Compliant property" in {
        runTest(
          true,
          "TH",
          true,
          expectedSize = 1
        )
      }

      "Non-compliant property" in {
        runTest(
          false,
          "TH",
          true,
          expectedSize = 1
        )
      }
      "Compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
        runTest(
          true,
          "TH",
          true,
          blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
          expectedSize = 1
        )
      }
      "Non-compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
        runTest(
          false,
          "TH",
          true,
          blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
          expectedSize = 1
        )
      }
    }

    "Return only compliant property for EU origin user" should {
      "Property created after Feb 2024" should {
        "Compliant property" in {
          runTest(
            true,
            "FR",
            false,
            expectedSize = 1
          )
        }
        "Non-compliant property" in {
          runTest(
            false,
            "FR",
            false,
            expectedSize = 0
          )
        }
        "Compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
          runTest(
            true,
            "FR",
            false,
            expectedSize = 1,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true
          )
        }
        "Non-compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
          runTest(
            false,
            "FR",
            false,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
            expectedSize = 0
          )
        }
      }

      "Property created before Feb 2024 and blockDSANonCompliantHotelRegisteredBeforeFeb2024 OFF" should {
        "Compliant property" in {
          runTest(
            true,
            "FR",
            true,
            expectedSize = 1
          )
        }

        "Non-compliant property" in {
          runTest(
            false,
            "FR",
            true,
            expectedSize = 1
          )
        }
      }

      "Property created before Feb 2024 and blockDSANonCompliantHotelRegisteredBeforeFeb2024 ON" should {
        "Compliant property" in {
          runTest(
            true,
            "FR",
            true,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
            expectedSize = 1
          )
        }

        "Non-compliant property" in {
          runTest(
            false,
            "FR",
            true,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
            expectedSize = 0
          )
        }
      }
    }
  }

}
