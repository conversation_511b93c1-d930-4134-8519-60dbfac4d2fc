package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.http.server.models.{ ExperimentContext => HttpServerExperimentContext }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.models.request.{ Page, SearchContext }
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.util.ContentDataExample._
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext, ExperimentContext }
import com.agoda.papi.search.functionaltest.framework.defaultmock.SearchExperimentManagerServiceDefaultMock
import com.agoda.papi.search.functionaltest.framework.{ PAPISearchFunctionalTest, SearchExperimentManagerServiceHelper }
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.GlobalContext
import com.agoda.property.search.framework.GqlMockModule
import com.typesafe.config.{ Config, ConfigFactory }
import org.scalatest.prop.TableDrivenPropertyChecks

import scala.concurrent.{ ExecutionContext, Future }

class CitySearchDelayInjectedExperimentSpec extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {

  val abTest = ActiveABTests()
  val exp1   = abTest.injectSsrDelay1
  val exp2   = abTest.injectSsrDelay2
  val exp3   = abTest.injectSsrDelay3

  private def createMockExperiment(experimentToForceB: Option[String] = None) = {
    val allocationContext = mock[AllocationContext]
    experimentToForceB match {
      case Some(e) if e == exp1 => when(allocationContext.injectSsrDelay1).thenReturn('B')
      case Some(e) if e == exp2 => when(allocationContext.injectSsrDelay2).thenReturn('B')
      case Some(e) if e == exp3 => when(allocationContext.injectSsrDelay3).thenReturn('B')
      case _                    =>
    }

    createSpySearchExperimentManagerServiceMock(allocationContext)
  }

  private def executeTestCase(
      propertyIds: Seq[Long],
      experimentToForceB: Option[String],
      configMock: State[Config]
  ): (Future[HttpResponse], SearchExperimentManagerService) = {

    val avail            = generateDefaultAvail(propertyIds.toList)
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds.toList)
    val query = QueryBuilder.build(
      aValidSSRQuery,
      aValidCitySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val expManagerMock = createMockExperiment(experimentToForceB)

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      bind[SearchExperimentManagerService] to expManagerMock
      bind[State[Config]] to configMock
    }

    val resF = executeSSRGraphQL(
      query,
      module
    )

    (resF, expManagerMock)
  }

  private def assertResponse(
      resp: HttpResponse,
      expectedPropertyIdsReturned: Seq[Long],
      expectedTotalFilteredHotels: Int,
      isEnable: Boolean,
      managerMock: SearchExperimentManagerService
  ) = {
    resp.status shouldBe StatusCodes.OK
    val citySearchResponse      = responseToCitySearchResponse(resp)
    val propertyIds: List[Long] = citySearchResponse.data.citySearch.properties.get.map(_.propertyId)
    (propertyIds should contain).theSameElementsInOrderAs(expectedPropertyIdsReturned)
    val totalFilteredHotels = citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels
    totalFilteredHotels shouldBe expectedTotalFilteredHotels

    if (isEnable) {
      // one time at GraphQLContext.postQuery and another time at DelayInjector.injectDelayWithMeasurement
      verify(managerMock, times(2)).completeExperiment(any)(any)
    } else {
      // one time at GraphQLContext.postQuery
      verify(managerMock, times(1)).completeExperiment(any)(any)
    }
  }

  private def createSpySearchExperimentManagerServiceMock(
      inputAllocationContext: AllocationContext
  ): SearchExperimentManagerService = {
    val searchExperimentManagerService = spy(new SearchExperimentManagerServiceMock(inputAllocationContext))

    searchExperimentManagerService
  }

  "Simple City Search" should {
    Table(
      ("enable", "experimentToForceB"),
      (true, None),
      (true, Some(exp1)),
      (true, Some(exp2)),
      (true, Some(exp3)),
      (false, None),
      (false, Some(exp1)),
      (false, Some(exp2)),
      (false, Some(exp3))
    )
      .forEvery { case (isEnable, expNameToForce) =>
        val testName = expNameToForce match {
          case Some(exp) => s"[enable=$isEnable]return available properties as usual with $exp=B"
          case None => s"[enable=$isEnable]return available properties without any latency injected experiment runs"
        }

        val delayInjectConfig = ConfigFactory.parseString(
          s"""
            |inject-delay{
            |  experiment-1 = 50.0
            |  experiment-2 = 75.0
            |  experiment-3 = 100.0
            |  enable = $isEnable
            |}
          """.stripMargin
        )

        val stateOfConfig = State.of(delayInjectConfig)

        testName in {
          val (respF, expManagerMock) = executeTestCase(
            propertyIds = 1L to 100L,
            expNameToForce,
            stateOfConfig
          )

          whenReady(respF) { resp =>
            assertResponse(
              resp,
              expectedPropertyIdsReturned = 1L to 10L,
              expectedTotalFilteredHotels = 100,
              isEnable = isEnable,
              managerMock = expManagerMock
            )
          }
        }

      }
  }

}

class SearchExperimentManagerServiceMock(inputAllocationContext: AllocationContext)
    extends SearchExperimentManagerServiceDefaultMock {

  override def createExperimentContext(
      searchContext: SearchContext,
      globalContext: GlobalContext,
      httpServerExperimentContext: HttpServerExperimentContext
  )(implicit ec: ExecutionContext): Future[ExperimentContext] = Future.successful(new ExperimentContext {
    override val allocationContext: AllocationContext = inputAllocationContext
  })

}
