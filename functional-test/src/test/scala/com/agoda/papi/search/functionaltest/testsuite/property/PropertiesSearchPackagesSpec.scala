package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.request.Packaging
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.enums.{ ArrangementEntries, ProductEntries }
import com.agoda.upi.models.request.{ CartBaseRequest, CartMetadata }
import framework.MockModule
import org.mockito.MockitoSugar
import org.scalatest.BeforeAndAfterEach
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertiesSearchPackagesSpec extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach {

  "PropertySearch /api/properties" should {
    val defaultHotel = aValidDFHotel.withHotelId(aValidHotelId)
    val defaultRoom =
      aValidDFRoom
        .withRoomTypeId(1L)
        .withRoomIdentifier("a")
        .build()

    val dfHotel = defaultHotel.withRooms(Seq(defaultRoom)).build()
    val contentMasterRooms = aValidRoomInformationResponse
      .withHotelId(aValidHotelId)
      .withMasterRoomResponse(
        Vector(
          aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
        )
      )
      .build()

    val hotelResult = Seq(dfHotel)

    val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

    "report package request count when packaging is present" in {
      val cartMeta = CartMetadata(productEntry = Some(ProductEntries.PackageProduct), itemMetadata = List.empty)
      val cartBaseRequest = CartBaseRequest(
        token = None,
        arrangement = Some(
          Arrangement(`type` = ArrangementEntries.Bundle, refId = None)
        ),
        sourceId = None,
        srcId = None,
        meta = Some(cartMeta)
      )

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)).withCartRequest(Some(cartBaseRequest)))
        .build()

      val mockReportingService = mock[MetricsReporter]

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val respF = executeProperties(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/properties",
        MeasurementTag.PackagingFlowType -> "PackageToCartMigration"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        verify(mockReportingService, times(1)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }

    "report package request count when cart request with Package product entry is present" in {
      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest.withPackaging(Some(Packaging(None))))
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)))
        .build()

      val mockReportingService = mock[MetricsReporter]

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val respF = executeProperties(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/properties",
        MeasurementTag.PackagingFlowType -> "Package"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        verify(mockReportingService, times(1)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }

    "do not report package request count if there is a normal hotel-search" in {
      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .build()

      val mockReportingService = mock[MetricsReporter]

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val respF = executeProperties(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/properties",
        MeasurementTag.PackagingFlowType -> "NonPackage"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        verify(mockReportingService, times(0)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }
  }

}
