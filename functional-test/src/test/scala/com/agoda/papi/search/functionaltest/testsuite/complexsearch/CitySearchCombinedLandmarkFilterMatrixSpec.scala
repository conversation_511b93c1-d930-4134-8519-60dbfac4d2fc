package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestAreas._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.enumeration.matrix.{ FilterKeys, MatrixGroupTypes }
import com.agoda.core.search.models.request.MatrixGroupRequest
import com.agoda.core.search.models.{ FilterRequest, IdsFilterRequest }
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchCombinedLandmarkFilterMatrixSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  val landmark1 = 32423L
  val landmark2 = 32425L
  val landmark3 = 32448L

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark1, landmark2, landmark3))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withLandmarkIds(List(landmark1, landmark2))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark1))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .withHotelAreaId(area1)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .build()
  )

  val propertyIds = List(hotel1, hotel2, hotel3, hotel4, hotel5)

  private def executeTestCase(
      propertyIds: List[Long],
      matrixGroupRequests: List[MatrixGroupRequest]
  ): Future[HttpResponse] = {
    val searchContext = aValidSearchContext.copy(endpointSearchType = Some(SearchTypes.AreaSearch))
    val citySearchRequest = aValidCitySearchRequest
      .withSearchContext(searchContext)
      .withSearchCriteria(aValidSearchCriteria.build())
      .withMatrixGroup(Some(matrixGroupRequests))
      .withFilterRequest(
        FilterRequest(
          idsFilters = Some(
            List(
              IdsFilterRequest(
                FilterKeys.HotelAreaId,
                ids = List(area1)
              )
            )
          )
        )
      )
      .build()

    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val avail            = generateDefaultAvail(propertyIds)
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)

    val mockModule = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockESMatrixService(client)
    }

    executeSSRGraphQL(
      query,
      mockModule
    )
  }

  // migrate from CitySearch-CombinedLandmarkFilterMatrix.feature : TC1
  "City search" should {
    "return matrix group result for landmarkIds for the selected area" in {
      val matrixGroupRequests = List(
        MatrixGroupRequest(MatrixGroupTypes.LandmarkIds, Some(10))
      )

      val respF = executeTestCase(propertyIds, matrixGroupRequests)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        val matrixGroupResults = citySearchResponse.data.citySearch.aggregation.map(_.matrixGroupResults)
        val landmarkIdMatrixItemResults =
          matrixGroupResults
            .flatMap(_.find(_.matrixGroup == MatrixGroupTypes.LandmarkIds.entryName))
            .map(_.matrixItemResults)

        landmarkIdMatrixItemResults shouldBe Some(
          List(
            TestMatrixItemResult(landmark1, s"mockname_$landmark1", count = 2),
            TestMatrixItemResult(landmark2, s"mockname_$landmark2", count = 1),
            TestMatrixItemResult(landmark3, s"mockname_$landmark3", count = 1)
          )
        )
      }
    }
  }

}
