package com.agoda.papi.search.functionaltest.framework.defaultmock

import com.agoda.commons.http.server.models.{ ExperimentContext => ExpContext }
import com.agoda.experiments.RequestContext
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.externaldependency.repository.{ CidMappingRepositoryAgsql, LanguageRepository }
import com.agoda.papi.search.common.util.context.ContextGenerator.TestExperimentManager
import com.agoda.papi.search.common.util.context.ExperimentContext
import request.PropertyContext

import scala.concurrent.{ ExecutionContext, Future }

class PAPIExperimentManagerLogicDefaultMock(
    languageRepository: LanguageRepository,
    cidMappingRepositoryAgsql: CidMappingRepositoryAgsql
) extends PAPIExperimentManagerLogic {

  override def createExperimentContext(implicit
      context: PropertyContext,
      ec: ExecutionContext,
      experimentContext: ExpContext
  ): Future[ExperimentContext] = {
    val reqContext: Future[RequestContext] = PAPIExperimentManagerLogic
      .requestContext(context, experimentContext)(languageRepository, cidMappingRepositoryAgsql, ec)

    reqContext
      .map(requestContext => new ExperimentContext(Some(new TestExperimentManager()), Some(requestContext)))
  }

  override def completeExperiment[A](
      in: Future[A]
  )(implicit experimentContext: ExperimentContext, ec: ExecutionContext): Future[A] = in

}
