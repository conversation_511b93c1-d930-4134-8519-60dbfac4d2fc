package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.information.InformationSummaryResponse
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import framework.MockModule
import request.SearchTypes
import util.builders.TestDataBuilders._
import util.builders.requestbuilder.PropertyRequestBuilder

import scala.concurrent.Future

class PropertySearchSimilarPropertiesRecommendSpec extends PAPISearchFunctionalTest {

  val activeABTest = new ActiveABTests()

  "PropertySearch /api/property" should {

    val masterRoomId1   = 111L
    val masterRoomId2   = 112L
    val masterRoomId3   = 113L
    val masterRoomMeta1 = aValidMasterRoomMeta.withRoomId(masterRoomId1).withMaxOccupants(Some(2))
    val masterRoomMeta2 = aValidMasterRoomMeta.withRoomId(masterRoomId2).withMaxOccupants(Some(3))
    val masterRoomMeta3 = aValidMasterRoomMeta.withRoomId(masterRoomId3).withMaxOccupants(Some(4))

    val roomsInformation1 = aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMeta1)
    val roomsInformation2 = aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMeta2)
    val roomsInformation3 = aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMeta3)

    val roomInformationResponse = aValidRoomInformationResponse
      .withHotelId(aValidHotelId)
      .withMasterRoomResponse(Vector(roomsInformation1, roomsInformation2, roomsInformation3))
      .build()

    val hotelId201 = 201L
    val hotelId202 = 202L

    val informationSummaryResponse202: InformationSummaryResponse = aValidInformationSummaryResponse.copy(
      displayName = "Welcome 202 Apartment",
      isNonHotelAccommodationType = true,
      hasHostExperience = true,
      accommodationType = "Apartment (41657)",
      singleRoomNonHotelAccommodation = false,
      propertyId = hotelId202
    )

    val contentResult: Seq[PropertyResponse] = Seq(
      aValidContentPropertyResponse
        .withHotelId(aValidHotelId),
      aValidContentPropertyResponse
        .withHotelId(hotelId201),
      aValidContentPropertyResponse
        .withHotelId(hotelId202)
        .withSummary(Some(informationSummaryResponse202))
    )

    val returnedRecommendationPropertyRequest: PropertyRequestBuilder#self#self#self#self#self = aValidPropertyRequest
      .withHotelIds(List(hotelId201, hotelId202))
      .withSearchType(SearchTypes.Property)
      .withPricing(aValidPricingRequest)
      .withFeatureFlag(aValidFeatureFlagRequest.copy(bookingHistory = Some(true)))
      .withHilight()

    "return recommends from zenith" in {

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(aValidHotelId))
        .withRecommendation(1)
        .build()

      val module = new MockModule {
        mockContentResponse(Future.successful(contentResult))
        mockZenithService(returnedRecommendationPropertyRequest)
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties      = responseToProperties(resp)
        val recommendations = properties.property.flatMap(_.recommendations)
        recommendations.size should be(1)
        val actualRecommendation = recommendations(0)
        actualRecommendation.propertyId.get should be(hotelId202)
        actualRecommendation.isNonHotelAccommodationType.get should be(
          informationSummaryResponse202.isNonHotelAccommodationType
        )
        actualRecommendation.singleRoomNonHotelAccommodation.get should be(
          informationSummaryResponse202.singleRoomNonHotelAccommodation
        )
      }
    }

    "return empty recommends if zenith response is empty" in {

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(10003))
        .withRecommendation(1)
        .build()

      val module = new MockModule {
        mockContentResponse(
          Future.successful(
            contentResult.map(
              _.copy(propertyId = 10003, rooms = Some(roomInformationResponse.copy(propertyId = 10003)))
            )
          )
        )
        mockZenithService(aValidPropertyRequest)
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties      = responseToProperties(resp)
        val recommendations = properties.property.flatMap(_.recommendations)
        recommendations should be(empty)
      }
    }

    "return recommends when PERSO-3497 is B" in {

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(aValidHotelId))
        .withRecommendation(1)
        .withAdditionalExperimentForce(activeABTest.migrateSimilarPropertiesToJarvis, "B")
        .build()

      val module = new MockModule {
        mockContentResponse(Future.successful(contentResult))
        mockJarvisService(returnedRecommendationPropertyRequest)
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties      = responseToProperties(resp)
        val recommendations = properties.property.flatMap(_.recommendations)
        recommendations.size should be(1)
        val actualRecommendation = recommendations(0)
        actualRecommendation.propertyId.get should be(hotelId202)
        actualRecommendation.isNonHotelAccommodationType.get should be(
          informationSummaryResponse202.isNonHotelAccommodationType
        )
        actualRecommendation.singleRoomNonHotelAccommodation.get should be(
          informationSummaryResponse202.singleRoomNonHotelAccommodation
        )
      }
    }

    "return empty recommended if response is empty when PERSO-3497 is B" in {

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(aValidHotelId))
        .withRecommendation(1)
        .withAdditionalExperimentForce(activeABTest.migrateSimilarPropertiesToJarvis, "B")
        .build()

      val module = new MockModule {
        mockContentResponse(Future.successful(contentResult))
        mockJarvisService(
          returnedRecommendationPropertyRequest.copy(context = aValidContext.copy(propertyIds = List()))
        )
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }

  }

}
