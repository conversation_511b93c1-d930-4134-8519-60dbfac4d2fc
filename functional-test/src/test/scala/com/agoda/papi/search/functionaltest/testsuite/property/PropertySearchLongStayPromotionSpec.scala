package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.commons.http.client.v2.RequestSettings
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.agoda.pricestream.models.request.PropertyLongStayPromotionRequest
import com.agoda.pricestream.models.response.{ PropertyLongStayPromotion, PropertyLongStayPromotionResponse }
import framework.MockModule
import framework.testservice.PriceStreamHttpClientTest
import org.scalatest.prop.TableDrivenPropertyChecks
import request.{ FeatureFlagRequest, SearchTypes }
import types.PropertyId
import util.builders.TestDataBuilders.{ aValidPricingRequest, aValidPropertyRequest }
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchLongStayPromotionSpec extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {

  val activeABTest = new ActiveABTests()
  val expName      = activeABTest.pricestreamPropertyLongStayPromotionNegativeExp
  val hotelId      = 1001L

  "PropertySearch /api/property" should {
    Table(
      (s"$expName variant", "showLongStayPromotionGrid flag", "expected longStayPromotions in response"),
      ('A', Some(true), true),
      ('A', Some(false), false),
      ('A', None, false),
      ('B', Some(true), false),
      ('B', Some(false), false),
      ('B', None, false)
    ).forEvery { case (expVariant, showLongStayPromotionGrid, expectedLongStayPromotions) =>
      val returnOrNotReturn = if (expectedLongStayPromotions) "return" else "not return"

      s"[$expName=$expVariant] $returnOrNotReturn long stay promotion response when feature flag showLongStayPromotionGrid=$showLongStayPromotionGrid" in {
        val longStayResponse = PropertyLongStayPromotionResponse(
          Vector(
            PropertyLongStayPromotion(7, 35),
            PropertyLongStayPromotion(14, 40),
            PropertyLongStayPromotion(28, 45)
          )
        )

        val propertyToLongStayPromoMap = Map(
          hotelId -> longStayResponse
        )

        val module = new MockModule {
          mockPriceStreamPropertyLongStayPromotion(propertyToLongStayPromoMap)
        }
        val request = aValidPropertyRequest
          .withSearchType(SearchTypes.Property)
          .withHotelIds(List(hotelId))
          .withFeatureFlag(
            FeatureFlagRequest(showLongStayPromotionGrid = showLongStayPromotionGrid)
          )
          .withAdditionalExperimentForce(expName, expVariant.toString)
          .build()

        val respF = executePropertySearch(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties = responseToProperties(resp)
          val propertyIdToLongStayPromotionsMap =
            properties.property.map(p => p.propertyId -> p.longStayPromotions).toMap

          propertyIdToLongStayPromotionsMap should not be empty
          propertyIdToLongStayPromotionsMap should have size 1
          val promotions = propertyIdToLongStayPromotionsMap.get(hotelId)
          promotions shouldBe defined

          if (expectedLongStayPromotions) {
            promotions.get should contain theSameElementsAs longStayResponse.longStayPromotions
          } else {
            promotions.get should be(empty)
          }
        }
      }
    }

  }

}
