package com.agoda.papi.search.functionaltest.testsuite.booking

import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.testsuite.common.CommonSearchContextForRestEndpointsTestSuit
import request._
import util.builders.TestDataBuilders._

class BookingEndpointSearchContextBugFixSpec
    extends PAPISearchFunctionalTest with CommonSearchContextForRestEndpointsTestSuit {

  val request: PropertyRequest = aValidPropertyRequest
    .withSearchType(SearchTypes.Booking)
    .withHotelIds(List(1001L))
    .withPricing(aValidPricingRequest)
    .build()

  "Booking-endpoint /api/booking" should {
    executeFxiTestCases(request, executeBookingEndpoint(_, _))
    executeSuggestPriceTestCases(request, executeBookingEndpoint(_, _))
  }

}
