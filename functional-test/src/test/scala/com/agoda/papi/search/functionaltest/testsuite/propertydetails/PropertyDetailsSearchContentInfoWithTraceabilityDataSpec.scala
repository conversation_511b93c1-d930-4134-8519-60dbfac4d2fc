package com.agoda.papi.search.functionaltest.testsuite.propertydetails

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.db.information.InformationResponse
import com.agoda.core.search.framework.builders.TestDataBuilders.{
  aValidContentInformationRequest,
  aValidPropertyDetailsRequest
}
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model._
import com.agoda.property.search.framework.GqlMockModule
import util.builders.TestDataBuilders.aValidCompanyTraceabilityData

class PropertyDetailsSearchContentInfoWithTraceabilityDataSpec extends PAPISearchFunctionalTest {

  "Property Details Search - companyTraceabilityData" should {

    "return companyTraceabilityData of the hotel" in {
      val query = aValidPropertyDetailsSearchRequest
        .withPropertyDetailsRequest(aValidPropertyDetailsRequest.copy(propertyIds = List(1002L)))
        .withContentInformation(aValidContentInformationRequest)
        .build()
      val aValidInformationResponse = InformationResponse(
        propertyId = 1002L,
        description = None,
        usefulInfoGroups = Option(Vector()),
        notes = None,
        entertainment = Option(Vector()),
        numberOfRoom = None,
        policies = None,
        spokenLanguages = Option(Vector()),
        messaging = None,
        reception = None,
        blockedNationalities = Option(Vector()),
        contact = None,
        local = None,
        chainId = None,
        childrenStayFreeTypeId = Option(0),
        formerlyName = None,
        localizations = None,
        isAgodaVerified = None,
        certificate = None,
        staffVaccinationInfo = None,
        characteristicTopics = None,
        sustainabilityInfo = None,
        nightStayInfo = None,
        companyTraceabilityInfo = None,
        companyTraceabilityData = Some(aValidCompanyTraceabilityData),
        dsaComplianceInfo = None
      )
      val module = new GqlMockModule {
        mockInformationService(
          Map(
            1002L -> aValidInformationResponse
          )
        )
      }

      val respF = executePropertyDetailsSearchGraphQL(query, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val propertyDetailsSearchResponse = responseToPropertyDetailsSearchResponse(resp)
        val propertyIds = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails.map(_.propertyId)
        val contentInformation = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails
          .flatMap(_.contentDetail.flatMap(_.contentInformation))
        val (
          expectedCompanyTraceabilityData,
          expectedCompanyTraceabilityInfoItem,
          expectedCompanyTraceabilityInfoAddress
        ) = (for {
          data     <- aValidInformationResponse.companyTraceabilityData
          infoItem <- data.traderInformationList.flatMap(_.headOption)
          address  <- infoItem.address
        } yield (data, infoItem, address)).getOrElse(fail("Test data is missing expected company traceability data."))
        propertyIds should contain theSameElementsAs List(1002L)
        contentInformation shouldBe List(
          TestContentInformationResponse(
            propertyId = 1002L,
            description = None,
            policies = None,
            notes = None,
            companyTraceabilityData = Some(
              TestCompanyTraceabilityData(
                requiredLayoutType = expectedCompanyTraceabilityData.requiredLayoutType,
                traderInformationList = Some(
                  List(
                    TestCompanyTraceabilityInfo(
                      tradingName = expectedCompanyTraceabilityInfoItem.tradingName,
                      address = Some(
                        TestCompanyTraceabilityAddressInfo(
                          addressUnit = expectedCompanyTraceabilityInfoAddress.addressUnit,
                          addressFloor = expectedCompanyTraceabilityInfoAddress.addressFloor,
                          addressBuilding = expectedCompanyTraceabilityInfoAddress.addressBuilding,
                          addressStreet1 = expectedCompanyTraceabilityInfoAddress.addressStreet1,
                          addressStreet2 = expectedCompanyTraceabilityInfoAddress.addressStreet2,
                          city = expectedCompanyTraceabilityInfoAddress.city,
                          stateId = expectedCompanyTraceabilityInfoAddress.stateId,
                          stateName = expectedCompanyTraceabilityInfoAddress.stateName,
                          countryId = expectedCompanyTraceabilityInfoAddress.countryId,
                          countryName = expectedCompanyTraceabilityInfoAddress.countryName,
                          postalCode = expectedCompanyTraceabilityInfoAddress.postalCode
                        )
                      ),
                      email = expectedCompanyTraceabilityInfoItem.email,
                      phoneNumber = expectedCompanyTraceabilityInfoItem.phoneNumber,
                      registrationNo = expectedCompanyTraceabilityInfoItem.registrationNo,
                      chainSpecificComplianceText = expectedCompanyTraceabilityInfoItem.chainSpecificComplianceText,
                      representativeName = expectedCompanyTraceabilityInfoItem.representativeName
                    )
                  )
                )
              )
            )
          )
        )
      }
    }
  }

}
