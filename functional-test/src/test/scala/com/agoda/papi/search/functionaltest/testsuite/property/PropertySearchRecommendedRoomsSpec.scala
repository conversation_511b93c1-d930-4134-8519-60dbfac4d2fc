package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.PropertyResponse
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import framework.MockModule
import models.starfruit.{ Hotel, Room }
import request.SearchTypes
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchRecommendedRoomsSpec extends PAPISearchFunctionalTest {

  val activeAbTests = ActiveABTests()

  "PropertySearch /api/property" should {

    val masterRoomId1   = 111L
    val masterRoomId2   = 112L
    val masterRoomId3   = 113L
    val masterRoomMeta1 = aValidMasterRoomMeta.withRoomId(masterRoomId1).withMaxOccupants(Some(2))
    val masterRoomMeta2 = aValidMasterRoomMeta.withRoomId(masterRoomId2).withMaxOccupants(Some(3))
    val masterRoomMeta3 = aValidMasterRoomMeta.withRoomId(masterRoomId3).withMaxOccupants(Some(4))

    val roomsInformation1 = aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMeta1)
    val roomsInformation2 = aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMeta2)
    val roomsInformation3 = aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMeta3)

    val roomInformationResponse = aValidRoomInformationResponse
      .withHotelId(aValidHotelId)
      .withMasterRoomResponse(Vector(roomsInformation1, roomsInformation2, roomsInformation3))
      .build()

    val contentResult: Seq[PropertyResponse] =
      Seq(aValidContentPropertyResponse.withHotelId(aValidHotelId).withRooms(Some(roomInformationResponse)))

    val dfRoomTypeId1: Room = aValidDFRoom.withRoomTypeId(masterRoomId1)
    val dfRoomTypeId2: Room = aValidDFRoom.withRoomTypeId(masterRoomId2)
    val dfRoomTypeId3: Room = aValidDFRoom.withRoomTypeId(masterRoomId3)
    val dfHotel: Seq[Hotel] = Seq(aValidDFHotel.withRooms(Seq(dfRoomTypeId1, dfRoomTypeId2, dfRoomTypeId3)).build())
    val dfResult            = Future.successful(Some(aValidDFProperties.copy(hotels = dfHotel)))

    "return recommended room when from zenith" in {

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .build()

      val module = new MockModule {
        mockContentResponse(Future.successful(contentResult))
        mockZenithService(aValidPropertyRequest)
        mockRoomInformationService(Future.successful(Seq(roomInformationResponse)))
        mockDFResponse(dfResult)
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should contain(true)
      }
    }

    "return empty recommended room if not intersect with response from zenith" in {

      val request =
        aValidPropertyRequest
          .withHotelIds(List(aValidHotelIdForWhichZenithRoomRecoDoesNotIntersect))
          .withSearchType(SearchTypes.Property)
          .withPricing(aValidPricingRequest.withRoom(5))
          .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
          .build()

      val module = new MockModule {
        mockContentResponse(
          Future.successful(
            contentResult.map(
              _.copy(
                propertyId = aValidHotelIdForWhichZenithRoomRecoDoesNotIntersect,
                rooms =
                  Some(roomInformationResponse.copy(propertyId = aValidHotelIdForWhichZenithRoomRecoDoesNotIntersect))
              )
            )
          )
        )
        mockZenithService(aValidPropertyRequest)
        mockRoomInformationService(
          Future.successful(
            Seq(roomInformationResponse.copy(propertyId = aValidHotelIdForWhichZenithRoomRecoDoesNotIntersect))
          )
        )
        mockDFResponse(
          Future.successful(
            Some(
              aValidDFProperties.copy(hotels =
                dfHotel.map(_.copy(hotelId = aValidHotelIdForWhichZenithRoomRecoDoesNotIntersect))
              )
            )
          )
        )
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }

    "return empty recommended room if zenith response is empty " in {

      val request = aValidPropertyRequest
        .withHotelIds(List(10003))
        .withSearchType(SearchTypes.Property)
        .withPricing(aValidPricingRequest.withRoom(5))
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .build()

      val module = new MockModule {
        mockContentResponse(
          Future.successful(
            contentResult.map(
              _.copy(propertyId = 10003, rooms = Some(roomInformationResponse.copy(propertyId = 10003)))
            )
          )
        )
        mockZenithService(aValidPropertyRequest)
        mockRoomInformationService(Future.successful(Seq(roomInformationResponse.copy(propertyId = 10003))))
        mockDFResponse(Future.successful(Some(aValidDFProperties.copy(hotels = dfHotel.map(_.copy(hotelId = 10003))))))
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }

    "return empty recommended room if exception while calling zenith" in {

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelIdForWhichZenithRoomRecoCallFailed))
        .withSearchType(SearchTypes.Property)
        .withPricing(aValidPricingRequest.withRoom(5))
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .build()

      val module = new MockModule {
        mockContentResponse(
          Future.successful(
            contentResult.map(
              _.copy(
                propertyId = aValidHotelIdForWhichZenithRoomRecoCallFailed,
                rooms = Some(roomInformationResponse.copy(propertyId = aValidHotelIdForWhichZenithRoomRecoCallFailed))
              )
            )
          )
        )
        mockZenithService(aValidPropertyRequest)
        mockRoomInformationService(
          Future.successful(
            Seq(roomInformationResponse.copy(propertyId = aValidHotelIdForWhichZenithRoomRecoCallFailed))
          )
        )
        mockDFResponse(
          Future.successful(
            Some(
              aValidDFProperties.copy(hotels =
                dfHotel.map(_.copy(hotelId = aValidHotelIdForWhichZenithRoomRecoCallFailed))
              )
            )
          )
        )
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }

    "return recommended room when PERSO-3828 is B" in {

      val dfHotel = aValidDFHotel
        .withHotelId(1001L)
        .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
        .build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(1001L)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .withAdditionalExperimentForce(activeAbTests.migrateRoomRecoFromZenithToFs, "B")
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockRecommendedRoomsService(Seq(1L))
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should contain(true)
      }
    }

    "return empty recommended room if not intersect with response when PERSO-3828 is B" in {

      val dfHotel = aValidDFHotel
        .withHotelId(1001L)
        .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
        .build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(1001L)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .withAdditionalExperimentForce(activeAbTests.migrateRoomRecoFromZenithToFs, "B")
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockRecommendedRoomsService(Seq(110L, 112L, 113L, 111L, 12345L))
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }

    "return empty recommended room if response is empty when PERSO-3828 is B" in {

      val dfHotel = aValidDFHotel
        .withHotelId(1001L)
        .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
        .build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(1001L)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .withAdditionalExperimentForce(activeAbTests.migrateRoomRecoFromZenithToFs, "B")
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockRecommendedRoomsService()
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }

    "return empty recommended room if exception while calling feature store when PERSO-3828 is B" in {

      val dfHotel = aValidDFHotel
        .withHotelId(aValidHotelIdForWhichZenithRoomRecoCallFailed)
        .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
        .build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelIdForWhichZenithRoomRecoCallFailed)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(aValidHotelIdForWhichZenithRoomRecoCallFailed))
        .withPricing(aValidPricingRequest)
        .withRoomRequest(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        .withAdditionalExperimentForce(activeAbTests.migrateRoomRecoFromZenithToFs, "B")
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockRecommendedRoomsService()
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties       = responseToProperties(resp)
        val recommendedRooms = properties.property.flatMap(_.masterRooms.flatMap(_.isRecommended))
        recommendedRooms should be(empty)
      }
    }
  }

}
