package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpHeader, StatusCodes }
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext, ContextHolder }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.property.service.StarfruitService
import framework.MockModule
import models.starfruit.Properties
import org.joda.time.DateTime
import org.mockito.Mockito
import org.scalatest.BeforeAndAfterEach
import org.specs2.mock.mockito.ArgumentCapture
import request.{ PropertyRequest, SearchTypes }
import util.builders.TestDataBuilders.{ aValidPricingRequest, _ }

import scala.concurrent.Future

class PropertySearchMsePricingTokenSpec extends PAPISearchFunctionalTest with BeforeAndAfterEach {

  val msePricingTokenHeaderUpperCase                   = "AG-MSE-PRICING-TOKEN"
  val msePricingTokenHeaderLowerCase                   = "ag-mse-pricing-token"
  val propertiesFromStarfruit: transformers.Properties = aValidProperties.build()
  val starfruitService: StarfruitService               = mock[StarfruitService]

  val activeAbTests = new ActiveABTests()

  val mockPricingRequest: PropertyRequest = aValidPropertyRequest
    .withSearchType(SearchTypes.Property)
    .withHotelIds(List(10001L))
    .withPricing(
      aValidPricingRequest
        .withBookingDate(new DateTime("2025-02-20T00:00:00.0000+07:00"))
        .withCheckIn(new DateTime("2025-02-25T00:00:00.0000+07:00"))
        .withLos(3)
    )
    .build()

  val mockPricingRequestA: PropertyRequest = mockPricingRequest
    .withAdditionalExperimentForce(activeAbTests.enableMultiProductMSEheader, "A")

  val mockPricingRequestB: PropertyRequest = mockPricingRequest
    .withAdditionalExperimentForce(activeAbTests.enableMultiProductMSEheader, "B")

  val agMsePricingTokenExpected =
    """[{"mpt":"msePricingToken","los":"3","cid":"1","hid":"10001","cin":"2025-02-25"}]"""

  val agMultiProductMsePricingTokenExpected =
    """{"property":[{"mpt":"msePricingToken","los":"3","cid":"1","hid":"10001","cin":"2025-02-25"}],"activity":[]}"""

  val agMultiProductMsePricingTokenWithActivityExpected =
    """{"property":[{"mpt":"msePricingToken","los":"3","cid":"1","hid":"10001","cin":"2025-02-25"}],"activity":[{"cid":"1","actId":"1234"}]}"""

  override def beforeEach(): Unit =
    Mockito.clearInvocations(starfruitService)

  val dfResult: Future[Some[Properties]] =
    Future.successful(Some(aValidDFProperties.copy(hotels = Seq(aValidDFHotel.build()))))

  "AgMsePricingToken header when ACTB-MSE = A" should {
    s"should be passed to DF when input header is upper case when A of ${activeAbTests.enableMultiProductMSEheader}" in {
      when(starfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(propertiesFromStarfruit))
      val agMsePricingToken =
        """[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"}]"""
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(msePricingTokenHeaderUpperCase, agMsePricingToken)
      )

      val module = new MockModule {
        mockDFResponse(Map(10001L -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestA, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header is lower case when A of ${activeAbTests.enableMultiProductMSEheader}" in {
      when(starfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(propertiesFromStarfruit))
      val agMsePricingToken =
        """[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"}]"""
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(msePricingTokenHeaderLowerCase, agMsePricingToken)
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestA, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header is upper case and is filtered based on CID/checkin/checkout when A of ${activeAbTests.enableMultiProductMSEheader}" in {

      when(starfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(propertiesFromStarfruit))
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(
          msePricingTokenHeaderUpperCase,
          """[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"},
            |{"cid":"1","cin":"2023-02-26","los":"3", "hid":"2", "mpt":"anotherToken"}]""".stripMargin
        ),
        RawHeader("AG-CID", "1")
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }
      val respF = executePropertySearch(request = mockPricingRequestA, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header is lower case and is filtered based on CID/checkin/checkout when A of ${activeAbTests.enableMultiProductMSEheader}" in {
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(
          msePricingTokenHeaderLowerCase,
          """[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"},
            |{"cid":"1","cin":"2023-02-26","los":"3", "hid":"2", "mpt":"anotherToken"}]""".stripMargin
        ),
        RawHeader("AG-CID", "1")
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestA, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMsePricingTokenExpected
      }
    }
  }

  "AgMsePricingToken header when ACTB-MSE = B" should {
    s"should be passed to DF when input header is upper case when B of ${activeAbTests.enableMultiProductMSEheader}" in {
      when(starfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(propertiesFromStarfruit))
      val agMsePricingToken =
        """{"property":[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"}],"activity":[]}"""
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(msePricingTokenHeaderUpperCase, agMsePricingToken)
      )

      val module = new MockModule {
        mockDFResponse(Map(10001L -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestB, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMultiProductMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header is lower case when B of ${activeAbTests.enableMultiProductMSEheader}" in {
      when(starfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(propertiesFromStarfruit))
      val agMsePricingToken =
        """{"property":[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"}],"activity":[]}"""
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(msePricingTokenHeaderLowerCase, agMsePricingToken)
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestB, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMultiProductMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header is upper case and is filtered based on CID/checkin/checkout when B of ${activeAbTests.enableMultiProductMSEheader}" in {

      when(starfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(propertiesFromStarfruit))
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(
          msePricingTokenHeaderUpperCase,
          """{"property":[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"},
            |{"cid":"1","cin":"2023-02-26","los":"3", "hid":"2", "mpt":"anotherToken"}],"activity":[]}""".stripMargin
        ),
        RawHeader("AG-CID", "1")
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }
      val respF = executePropertySearch(request = mockPricingRequestB, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMultiProductMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header is lower case and is filtered based on CID/checkin/checkout when B of ${activeAbTests.enableMultiProductMSEheader}" in {
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(
          msePricingTokenHeaderLowerCase,
          """{"property":[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"},
            |{"cid":"1","cin":"2023-02-26","los":"3", "hid":"2", "mpt":"anotherToken"}],"activity":[]}""".stripMargin
        ),
        RawHeader("AG-CID", "1")
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestB, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMultiProductMsePricingTokenExpected
      }
    }

    s"should be passed to DF when input header contain both property and activity value when B of ${activeAbTests.enableMultiProductMSEheader}" in {
      val agHeaders: Seq[HttpHeader] = Seq(
        RawHeader(
          msePricingTokenHeaderLowerCase,
          """{"property":[{"cid":"1","cin":"2025-02-25","los":"3", "hid":"10001", "mpt":"msePricingToken"},
            |{"cid":"1","cin":"2023-02-26","los":"3", "hid":"2", "mpt":"anotherToken"}],"activity":[{"cid":"1","actId":"1234"}]}""".stripMargin
        ),
        RawHeader("AG-CID", "1")
      )

      val module = new MockModule {
        mockDFResponse(Map(aValidHotelId -> aValidDFHotel.build()))
        bind[StarfruitService] to starfruitService
      }

      val respF = executePropertySearch(request = mockPricingRequestB, module = module, headers = agHeaders)
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val captor = new ArgumentCapture[ContextHolder]()
        verify(starfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.globalContext.getRawAgHeaders(
          "AG-MSE-PRICING-TOKEN"
        ) shouldBe agMultiProductMsePricingTokenWithActivityExpected
      }
    }
  }

}
