package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.mocks.client.MockJarvisService
import com.agoda.core.search.models.request.RocketmilesRequestV2
import com.agoda.papi.search.common.service.RocketmilesRankingExperimentService.CITI_SITE_SLUG
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.complexsearch.service.citi._
import com.agoda.papi.search.complexsearch.service.rocketmiles.RocketmilesRankingService
import com.agoda.papi.search.complexsearch.sorting.jarvis.JarvisService
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.RocketmilesRankingClient
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.model.RocketmilesRankingResponse
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.util.RocketmilesRankingExpUtils
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.property.search.framework.binding.module.RocketmilesRankingServiceNoOp
import com.agoda.property.search.utils.RocketmilesDataExample
import org.mockito.{ ArgumentCaptor, Mockito, MockitoSugar }
import org.scalatest.BeforeAndAfterEach
import org.scalatest.prop.TableDrivenPropertyChecks

import scala.concurrent.Future

class CitySearchUseJarvisForRocketmilesRankingSpec
    extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach with TableDrivenPropertyChecks {

  val properties = Seq(101L, 102L, 103L)

  val mockRocketmilesRankingResponse = RocketmilesRankingResponse(
    properties.zipWithIndex.map(x => x._1.toString -> (x._2 + 1.0f)).toMap
  )

  val abTest                   = ActiveABTests()
  val jarvisRmRankingExpName   = abTest.useJarvisForRocketmilesRanking
  val jarvisCitiRankingExpName = abTest.useJarvisForCitiRanking

  val mockRcoketmilesRankingExpUtilsEnable = mock[RocketmilesRankingExpUtils]
  when(mockRcoketmilesRankingExpUtilsEnable.isRankingExperimentEnabled(any, any)).thenReturn(true)

  val mockRcoketmilesRankingExpUtilsDisable = mock[RocketmilesRankingExpUtils]
  when(mockRcoketmilesRankingExpUtilsDisable.isRankingExperimentEnabled(any, any)).thenReturn(false)

  val citiRocketmilesRequest = RocketmilesDataExample.aRocketmilesRequestV2
    .withSiteSlug(CITI_SITE_SLUG)
    .build()

  val nonCitiRocketmilesRequest = RocketmilesDataExample.aRocketmilesRequestV2
    .withSiteSlug("rocketmile-non-citi")
    .build()

  val mockJarvisService             = spy(new MockJarvisService)
  val mockRocketMilesClient         = mock[RocketmilesRankingClient]
  val mockRocketmilesRankingService = spy(new RocketmilesRankingServiceNoOp)
  val citiRankingService            = spy(new CitiRankingServiceImpl)

  when(mockRocketMilesClient.getRocketmilesRankingResponse(any, any)(any))
    .thenReturn(Future.successful(mockRocketmilesRankingResponse))

  override def beforeEach(): Unit = {
    super.beforeEach()
    Mockito.clearInvocations(mockJarvisService)
    Mockito.clearInvocations(mockRocketMilesClient)
    Mockito.clearInvocations(mockRocketmilesRankingService)
    Mockito.clearInvocations(citiRankingService)
  }

  s"ComplexSearch with Rocketmile Request" should {
    Table(
      (
        s"$jarvisRmRankingExpName variant",
        s"$jarvisCitiRankingExpName variant",
        "RocketmileRankingExperiment Enabled",
        "isCiti",
        "expected service call",
        "expected isRocketRankingService in Jarvis call",
        "should invoke CitiRankingService or not"
      ),
      ('A', 'A', true, true, "Rocketmiles", None, false),
      ('A', 'A', true, false, "Rocketmiles", None, false),
      ('A', 'A', false, true, "Jarvis", Some(false), false),
      ('A', 'A', false, false, "Jarvis", Some(false), false),
      ('A', 'B', true, true, "Jarvis", Some(true), true),
      ('A', 'B', true, false, "Rocketmiles", None, false),
      ('A', 'B', false, true, "Jarvis", Some(false), false),
      ('A', 'B', false, false, "Jarvis", Some(false), false),
      ('B', 'A', true, true, "Rocketmiles", None, false),
      ('B', 'A', true, false, "Jarvis", Some(true), false),
      ('B', 'A', false, true, "Jarvis", Some(false), false),
      ('B', 'A', false, false, "Jarvis", Some(false), false),
      ('B', 'B', true, true, "Jarvis", Some(true), true),
      ('B', 'B', true, false, "Jarvis", Some(true), false),
      ('B', 'B', false, true, "Jarvis", Some(false), false),
      ('B', 'B', false, false, "Jarvis", Some(false), false)
    ).forEvery {
      (
          rmVariant,
          citiVariant,
          rankingExpEnabled,
          isCiti,
          callExpectation,
          expectedIsRocketRankingService,
          shouldInvokeCitiRankingService
      ) =>
        s"[$jarvisRmRankingExpName=$rmVariant, $jarvisCitiRankingExpName=$citiVariant] " +
          s"When RocketmileRankingExperiment Enabled=$rankingExpEnabled and isCiti=$isCiti, " +
          s"PAPI Search should call $callExpectation" in {
            val rocketMilesRequestToUse = if (isCiti) citiRocketmilesRequest else nonCitiRocketmilesRequest
            val mockRocketmilesRankingExpUtils =
              if (rankingExpEnabled) mockRcoketmilesRankingExpUtilsEnable else mockRcoketmilesRankingExpUtilsDisable

            val response = executeTestCase(
              propertyIds = properties,
              rocketMilesRequest = rocketMilesRequestToUse,
              rocketmilesRankingExpUtils = mockRocketmilesRankingExpUtils,
              rmVariant,
              citiVariant
            )

            whenReady(response) { res =>
              res.status shouldBe StatusCodes.OK

              if (callExpectation == "Jarvis") {
                shouldCallJarvisAssertion(expectedIsRocketRankingService.get)
              } else {
                shouldCallRocketmileRankingAssertion()
              }

              if (shouldInvokeCitiRankingService) {
                verify(citiRankingService, times(1)).injectCitiCollectionsAndSort(any, any)
              } else {
                verify(citiRankingService, never).injectCitiCollectionsAndSort(any, any)
              }
            }
          }
    }
  }

  /* helper */

  private def shouldCallRocketmileRankingAssertion() = {
    verify(mockRocketMilesClient, times(1)).getRocketmilesRankingResponse(any, any)(any)
    verify(mockRocketmilesRankingService, times(1)).rankAndSort(any, any, any)(any, any)
    verify(mockJarvisService, never).getRankingHotelResponse(any, any, any, any, any, any)(
      any,
      any,
      any
    )
  }

  private def shouldCallJarvisAssertion(isRocketRankingServiceCallExpectation: Boolean) = {
    val isRocketRankingServiceCaptor = ArgumentCaptor.forClass(classOf[Boolean])

    verify(mockRocketMilesClient, never).getRocketmilesRankingResponse(any, any)(any)
    verify(mockRocketmilesRankingService, never).rankAndSort(any, any, any)(any, any)
    verify(mockJarvisService, times(1)).getRankingHotelResponse(
      any,
      any,
      any,
      any,
      any,
      isRocketRankingServiceCaptor.capture()
    )(
      any,
      any,
      any
    )
    val flagCaptured = isRocketRankingServiceCaptor.getValue.asInstanceOf[Boolean]
    flagCaptured shouldBe isRocketRankingServiceCallExpectation
  }

  private def executeTestCase(
      propertyIds: Seq[Long],
      rocketMilesRequest: RocketmilesRequestV2,
      rocketmilesRankingExpUtils: RocketmilesRankingExpUtils,
      rmExperimentVariant: Char,
      citiExperimentVariant: Char
  ): Future[HttpResponse] = {
    val propertyInfoList     = generateDefaultPropertyInfoList(propertyIds.toList)
    val availabilityDataList = generateDefaultAvail(propertyIds.toList)

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyInfoList)
      mockPushAvailability(Future.successful(availabilityDataList, Set.empty))
      bind[RocketmilesRankingExpUtils] to rocketmilesRankingExpUtils
      bind[JarvisService] to mockJarvisService
      bind[RocketmilesRankingClient] to mockRocketMilesClient
      bind[RocketmilesRankingService] to mockRocketmilesRankingService
      bind[CitiRankingService] to citiRankingService
    }
    val citySearchRequest = aValidCitySearchRequest
      .withSearchCriteria(aValidSearchCriteria.build())
      .withRocketmilesRequestV2(rocketMilesRequest)
      .build()
    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val forceExpHeaders =
      s"$jarvisRmRankingExpName,$rmExperimentVariant,$jarvisCitiRankingExpName,$citiExperimentVariant"
    val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, forceExpHeaders)

    executeSSRGraphQL(query = query, module = module, headers = headers)
  }

}
