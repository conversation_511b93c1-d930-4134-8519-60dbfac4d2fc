package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.db.features.{ Feature, FeatureGroup }
import com.agoda.content.models.db.information.RoomFeatureResponse
import com.agoda.papi.enums.hotel.StayPackageTypes.Escapes
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import constant._
import framework.MockModule
import mappings.CMSMapping
import models.starfruit.{ Channel, NoCreditCardRequired, Room, TimeInterval }
import request.{ FeatureFlagKey, FeatureFlagRequest, SearchTypes }
import transformers.{ FilterGroup, FilterTag, PropertyFilters }
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchWithPropertyFiltersSpec extends PAPISearchFunctionalTest {

  val activeABTest = new ActiveABTests()

  "PropertySearch /api/property" should {
    val normalRoomId   = "7ec7e104-f93b-7845-db06-9cb73acbf799"
    val insiderRoomId  = "7ec7e104-f93b-7845-db06-9cb73acbf798"
    val noCCRoomId     = "7ec7e104-f93b-7845-db06-9cb73acbf797"
    val giftCardRoomId = "7ec7e104-f93b-7845-db06-9cb73acbf796"

    val normalRoom: Room = aValidDFRoom
      .withRoomTypeId(1)
      .withSupplierID(3038)
      .withUID(normalRoomId)
      .withDMCUD("a4b4ac0c-1792-bdab-d424-227f202a0f0e")
    val insiderDealRoom: Room = aValidDFRoom.withRoomTypeId(2).withChannelID(Channel(2)).withUID(insiderRoomId)
    val noCCRoom: Room = aValidDFRoom
      .withRoomTypeId(3)
      .withUID(noCCRoomId)
      .copy(
        payment = aValidDFPayment.copy(noCreditCardRequired = Some(NoCreditCardRequired(isEligible = true))),
        stayPackageType = Some(Escapes)
      )
      .withHourlyAvailableSlots(
        Some(
          Seq(
            TimeInterval(1, "18:00"),
            TimeInterval(1, "20:00")
          )
        )
      )
    val giftCardRoom: Room = aValidDFRoom.withRoomTypeId(4).withUID(giftCardRoomId).withGiftCard(aValidGiftCard)

    val dfHotel = aValidDFHotel
      .withHotelId(1001L)
      .withRooms(List(insiderDealRoom, normalRoom, noCCRoom, giftCardRoom))
      .withCheapestRoomID(normalRoomId)
      .withSuggestRoom(List(normalRoom))
      .build()

    val normalContentRoom                              = aValidChildRoomMeta.withRoomId(1)
    val insiderDealContentRoom                         = aValidChildRoomMeta.withRoomId(2)
    val giftCardContentRoom                            = aValidChildRoomMeta.withRoomId(3)
    val noCCContentRoom                                = aValidChildRoomMeta.withRoomId(4)
    val nonSmokingRoomFeature: RoomFeatureResponse     = aValidFeatureResponse.withName(Symbols.nonSmokingFilter)
    val kitchenetteRoomFeature: RoomFeatureResponse    = aValidFeatureResponse.withName(Symbols.kitchenAvailableIcon)
    val balconyTerraceRoomFeature: RoomFeatureResponse = aValidFeatureResponse.withName(Symbols.balconyTerraceIcon)
    val babyCotFeature              = aValidFeatureResponse.withName(RoomFeaturesConstants.InfantCotAvailable)
    val feature: Feature            = aValidFeature.withId(109)
    val facilityGroup: FeatureGroup = aValidFeatureGroup.withFeatures(Vector(feature)).withId(26)

    val configuration = aValidBedDetailResponse.withBedRooms(
      Vector(aValidBedroomConfigurationResponse, aValidBedroomConfigurationResponse)
    )
    val bedConfiguration = aValidBedConfigurationResponse.withConfiguration(Some(configuration))

    val familyRelatedfeature        = aValidFeature.withId(103).build()
    val familyRelatedfeatures       = Vector(familyRelatedfeature)
    val familyRelatedFacilityGroups = aValidFeatureGroup.withFeatures(familyRelatedfeatures)

    val contentMasterRooms = aValidRoomInformationResponse
      .withHotelId(1001L)
      .withMasterRoomResponse(
        Vector(
          aValidMasterRoomResponse
            .withChildrenRoomMetaData(
              Vector(normalContentRoom, insiderDealContentRoom, giftCardContentRoom, noCCContentRoom)
            )
            .withFeatures(
              Vector(nonSmokingRoomFeature, kitchenetteRoomFeature, balconyTerraceRoomFeature, babyCotFeature)
            )
            .withFacilityGroups(Vector(facilityGroup, familyRelatedFacilityGroups))
            .withFilterTags(Some(aValidFilterTags))
            .withSuitabilityType(Some(FamilyConstant.SuitabilityTypeFamily))
            .withBedConfiguration(Some(bedConfiguration))
            .withMasterRoomMetadata(normalContentRoom.copy(numberOfBedRooms = Some(2)))
        )
      )
      .build()

    val request = aValidPropertyRequest
      .withSearchType(SearchTypes.Property)
      .withHotelIds(List(1001L))
      .withPricing(aValidPricingRequest)
      .withFeatureFlag(
        aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
      )
      .build()

    "return all filter tags and filter groups" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val expectedPropertyFilters = PropertyFilters(
        "CMSId:64940",
        Seq(
          FilterTag(
            FilterID.noCCPropertyPage,
            "CMSId:80031",
            Symbols.noCCPropertyPageFilter,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.PayAtHotel,
            "CMSId:56649",
            Symbols.PayAtHotel,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.NonSmoking,
            "CMSId:65152",
            Symbols.nonSmokingFilter,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.KitchenAvailable,
            "CMSId:83947",
            Symbols.kitchenAvailableIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.RecommendedForFamilies,
            "CMSId:82163",
            Symbols.recommendedForFamiliesIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.MoreThanOneBedRooms,
            "CMSId:83540",
            Symbols.bedroomIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.TwinBed,
            "CMSId:84511",
            Symbols.twinBedIcon,
            None,
            Some(FilterGroupID.BeddingConfiguration)
          )
        ),
        Some(
          Seq(
            FilterGroup(
              FilterGroupID.PaymentOptions,
              "CMSId:387877"
            ),
            FilterGroup(
              FilterGroupID.BeddingConfiguration,
              "CMSId:388161"
            ),
            FilterGroup(
              FilterGroupID.RoomFeatures,
              "CMSId:173886"
            )
          )
        )
      )
      val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
      val respF                 = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = properties.property.head.masterRooms.head.childrenRooms
        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.noCCPropertyPage,
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(giftCardRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags
      }
    }

    "return all available filters with syncId when ROOMIE-1402 = B" in {
      val forceBRequest = request
        .withAdditionalExperimentForce(activeABTest.enablePropertyFilterSyncId, "B")
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val expectedPropertyFilters = PropertyFilters(
        "CMSId:64940",
        Seq(
          FilterTag(
            FilterID.noCCPropertyPage,
            "CMSId:80031",
            Symbols.noCCPropertyPageFilter,
            None,
            Some(FilterGroupID.PaymentOptions),
            Some(CMSMapping.NoCCMatrix.toString)
          ),
          FilterTag(
            FilterID.PayAtHotel,
            "CMSId:56649",
            Symbols.PayAtHotel,
            None,
            Some(FilterGroupID.PaymentOptions),
            Some(CMSMapping.PayAtHotelMatrix.toString)
          ),
          FilterTag(
            FilterID.NonSmoking,
            "CMSId:65152",
            Symbols.nonSmokingFilter,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.KitchenAvailable,
            "CMSId:83947",
            Symbols.kitchenAvailableIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.RecommendedForFamilies,
            "CMSId:82163",
            Symbols.recommendedForFamiliesIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.MoreThanOneBedRooms,
            "CMSId:83540",
            Symbols.bedroomIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.TwinBed,
            "CMSId:84511",
            Symbols.twinBedIcon,
            None,
            Some(FilterGroupID.BeddingConfiguration)
          )
        ),
        Some(
          Seq(
            FilterGroup(
              FilterGroupID.PaymentOptions,
              "CMSId:387877"
            ),
            FilterGroup(
              FilterGroupID.BeddingConfiguration,
              "CMSId:388161"
            ),
            FilterGroup(
              FilterGroupID.RoomFeatures,
              "CMSId:173886"
            )
          )
        )
      )
      val respF = executePropertySearch(request = forceBRequest, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
      }
    }

    "return Agoda Special Offers filter when stayPackageType is Escapes, ROOMIE-1311 is B, and whitelabel key is Agoda" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val requestWithASO = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withFeatureFlag(
          FeatureFlagRequest(enableEscapesPackage = Some(true))
        )
        .withWhiteLabelKey(Some("AGODA"))
        .withAdditionalExperimentForce(activeABTest.asoFilterMigration, "B")
        .build()

      val expectedPropertyFilters = PropertyFilters(
        "CMSId:64940",
        Seq(
          FilterTag(
            FilterID.noCCPropertyPage,
            "CMSId:80031",
            Symbols.noCCPropertyPageFilter,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.PayAtHotel,
            "CMSId:56649",
            Symbols.PayAtHotel,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.NonSmoking,
            "CMSId:65152",
            Symbols.nonSmokingFilter,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.KitchenAvailable,
            "CMSId:83947",
            Symbols.kitchenAvailableIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.TwinBed,
            "CMSId:84511",
            Symbols.twinBedIcon,
            None,
            Some(FilterGroupID.BeddingConfiguration)
          ),
          FilterTag(
            FilterID.AgodaSpecialOffers,
            "CMSId:121372",
            Symbols.ASOIcon,
            None,
            Some(FilterGroupID.SpecialOffers)
          )
        ),
        Some(
          Seq(
            FilterGroup(
              FilterGroupID.PaymentOptions,
              "CMSId:387877"
            ),
            FilterGroup(
              FilterGroupID.BeddingConfiguration,
              "CMSId:388161"
            ),
            FilterGroup(
              FilterGroupID.SpecialOffers,
              "CMSId:171401"
            ),
            FilterGroup(
              FilterGroupID.RoomFeatures,
              "CMSId:173886"
            )
          )
        )
      )

      val respF = executePropertySearch(request = requestWithASO, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = properties.property.head.masterRooms.head.childrenRooms
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.AgodaSpecialOffers
          )
      }
    }

    "not return Agoda Special Offers filter when stayPackageType is Escapes, ROOMIE-1311 is B, and whitelabel key is not Agoda" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val requestWithASO = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withFeatureFlag(
          FeatureFlagRequest(enableEscapesPackage = Some(true))
        )
        .withWhiteLabelKey(Some("CITI"))
        .withAdditionalExperimentForce(activeABTest.asoFilterMigration, "B")
        .build()

      val expectedPropertyFilters = PropertyFilters(
        "CMSId:64940",
        Seq(
          FilterTag(
            FilterID.noCCPropertyPage,
            "CMSId:80031",
            Symbols.noCCPropertyPageFilter,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.PayAtHotel,
            "CMSId:56649",
            Symbols.PayAtHotel,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.NonSmoking,
            "CMSId:65152",
            Symbols.nonSmokingFilter,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.KitchenAvailable,
            "CMSId:83947",
            Symbols.kitchenAvailableIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.TwinBed,
            "CMSId:84511",
            Symbols.twinBedIcon,
            None,
            Some(FilterGroupID.BeddingConfiguration)
          )
        ),
        Some(
          Seq(
            FilterGroup(
              FilterGroupID.PaymentOptions,
              "CMSId:387877"
            ),
            FilterGroup(
              FilterGroupID.BeddingConfiguration,
              "CMSId:388161"
            ),
            FilterGroup(
              FilterGroupID.RoomFeatures,
              "CMSId:173886"
            )
          )
        )
      )

      val respF = executePropertySearch(request = requestWithASO, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = properties.property.head.masterRooms.head.childrenRooms
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage
          )
      }
    }

    "return dayuse and overnight filter when both types of offers are available and mergeDayUseOffersWithOvernight is ON" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withFeatureFlag(
          FeatureFlagRequest(mergeDayUseOffersWithOvernight = Some(true))
        )
        .build()

      val expectedPropertyFilters = PropertyFilters(
        "CMSId:64940",
        Seq(
          FilterTag(
            FilterID.DayUse,
            "CMSId:372538",
            Symbols.DayUseIcon,
            None,
            Some(FilterGroupID.Others)
          ),
          FilterTag(
            FilterID.Overnight,
            "CMSId:372539",
            Symbols.OvernightIcon,
            None,
            Some(FilterGroupID.Others)
          ),
          FilterTag(
            FilterID.noCCPropertyPage,
            "CMSId:80031",
            Symbols.noCCPropertyPageFilter,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.PayAtHotel,
            "CMSId:56649",
            Symbols.PayAtHotel,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.NonSmoking,
            "CMSId:65152",
            Symbols.nonSmokingFilter,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.KitchenAvailable,
            "CMSId:83947",
            Symbols.kitchenAvailableIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.TwinBed,
            "CMSId:84511",
            Symbols.twinBedIcon,
            None,
            Some(FilterGroupID.BeddingConfiguration)
          )
        ),
        Some(
          Seq(
            FilterGroup(
              FilterGroupID.PaymentOptions,
              "CMSId:387877"
            ),
            FilterGroup(
              FilterGroupID.BeddingConfiguration,
              "CMSId:388161"
            ),
            FilterGroup(
              FilterGroupID.RoomFeatures,
              "CMSId:173886"
            ),
            FilterGroup(
              FilterGroupID.Others,
              "CMSId:56918"
            )
          )
        )
      )

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = properties.property.head.masterRooms.head.childrenRooms
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.DayUse,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage
          )

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.Overnight,
            FilterID.PayAtHotel
          )
      }
    }

    "return not dayuse and overnight filter when mergeDayUseOffersWithOvernight is off" in {

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .withFeatureFlag(
          FeatureFlagRequest(mergeDayUseOffersWithOvernight = Some(false))
        )
        .build()

      val expectedPropertyFilters = PropertyFilters(
        "CMSId:64940",
        Seq(
          FilterTag(
            FilterID.noCCPropertyPage,
            "CMSId:80031",
            Symbols.noCCPropertyPageFilter,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.PayAtHotel,
            "CMSId:56649",
            Symbols.PayAtHotel,
            None,
            Some(FilterGroupID.PaymentOptions)
          ),
          FilterTag(
            FilterID.NonSmoking,
            "CMSId:65152",
            Symbols.nonSmokingFilter,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.KitchenAvailable,
            "CMSId:83947",
            Symbols.kitchenAvailableIcon,
            None,
            Some(FilterGroupID.RoomFeatures)
          ),
          FilterTag(
            FilterID.TwinBed,
            "CMSId:84511",
            Symbols.twinBedIcon,
            None,
            Some(FilterGroupID.BeddingConfiguration)
          )
        ),
        Some(
          Seq(
            FilterGroup(
              FilterGroupID.PaymentOptions,
              "CMSId:387877"
            ),
            FilterGroup(
              FilterGroupID.BeddingConfiguration,
              "CMSId:388161"
            ),
            FilterGroup(
              FilterGroupID.RoomFeatures,
              "CMSId:173886"
            )
          )
        )
      )

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = properties.property.head.masterRooms.head.childrenRooms
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage
          )

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.PayAtHotel
          )
      }
    }
  }

}
