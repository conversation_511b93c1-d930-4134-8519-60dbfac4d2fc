package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestAreas._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.enumeration.matrix.MatrixGroupTypes
import com.agoda.core.search.models.request.{ AreaSearchRequest, MatrixGroupRequest }
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class AreaSearchLandmarkFilterMatrixSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  val landmark1 = 32423L
  val landmark2 = 32425L
  val landmark3 = 32448L

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark1, landmark2, landmark3))
      .withStarRating(3.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark1, landmark2))
      .withStarRating(4.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withHotelAreaId(area1)
      .withLandmarkIds(List(landmark1))
      .withStarRating(5.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .withHotelAreaId(area1)
      .withStarRating(4.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .withStarRating(3.0)
      .withHotelAreaId(area1)
      .build()
  )

  val propertyIds = List(hotel1, hotel2, hotel3, hotel4, hotel5)

  private def executeTestCase(
      propertyIds: List[Long],
      matrixGroupRequests: List[MatrixGroupRequest]
  ): Future[HttpResponse] = {
    val searchContext = aValidSearchContext.copy(endpointSearchType = Some(SearchTypes.AreaSearch))
    val areaSearchRequest =
      AreaSearchRequest(area1, aValidCityId, aValidSearchRequest.copy(searchContext = searchContext))
        .withSearchCriteria(aValidSearchCriteria.build())
        .withMatrixGroup(Some(matrixGroupRequests))
        .build()

    val query = QueryBuilder.build(
      aValidAreaSearchQuery,
      areaSearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val avail            = generateDefaultAvail(propertyIds)
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)

    val mockModule = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockAreaInfoRepositoryAgsql(aValidCityId, propertyIds)
      mockESMatrixService(client)
    }

    executeSSRGraphQL(
      query,
      mockModule
    )
  }

  // migrate from CitySearch-LandmarkFilterMatrix.feature : TC1
  "Area search" should {
    "return matrix group result for landmarkIds and another group correctly" in {
      val matrixGroupRequests = List(
        MatrixGroupRequest(MatrixGroupTypes.LandmarkIds, Some(10)),
        MatrixGroupRequest(MatrixGroupTypes.StarRating, Some(10))
      )

      val respF = executeTestCase(propertyIds, matrixGroupRequests)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val areaSearchResponse = responseToAreaSearchResponse(resp)
        val matrixGroupResults = areaSearchResponse.data.areaSearch.aggregation.map(_.matrixGroupResults)
        val landmarkIdMatrixItemResults =
          matrixGroupResults
            .flatMap(_.find(_.matrixGroup == MatrixGroupTypes.LandmarkIds.entryName))
            .map(_.matrixItemResults)
        landmarkIdMatrixItemResults shouldBe Some(
          List(
            TestMatrixItemResult(landmark1, s"mockname_$landmark1", count = 3),
            TestMatrixItemResult(landmark2, s"mockname_$landmark2", count = 2),
            TestMatrixItemResult(landmark3, s"mockname_$landmark3", count = 1)
          )
        )
        val starRatingMatrixItemResults =
          matrixGroupResults
            .flatMap(_.find(_.matrixGroup == MatrixGroupTypes.StarRating.entryName))
            .map(_.matrixItemResults)
        starRatingMatrixItemResults shouldBe Some(
          List(
            TestMatrixItemResult(3, "", count = 2),
            TestMatrixItemResult(4, "", count = 2),
            TestMatrixItemResult(5, "", count = 1)
          )
        )
      }
    }
  }

}
