package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.mockito.helper.MockitoHelper
import com.agoda.papi.search.common.framework.mock.SupplierMetadataServiceTest
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.supplier.common.services.SupplierMetadataService
import framework.MockModule
import models.pricing.enums.CorTypeFlags
import models.starfruit._
import transformers.EnrichedChildRoom
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchOfferPrioritySpec extends PAPISearchFunctionalTest with MockitoHelper {
  private val topPriority: Double = 1.0
  private val none: Double        = 0

  val mockRoom1 =
    mockRoom("room1", isBreakfastIncluded = true, price = (100, 150), displayPriceAfterCashback = Option(90, 100))

  val mockRoom2 =
    mockRoom("room2", price = (110, 120), displayPriceAfterCashback = Option(90, 100))

  val mockRoom3 =
    mockRoom("room3", price = (200, 210), displayPriceAfterCashback = Option(90, 100))

  val mockRoom4 =
    mockRoom("room4", price = (230, 250), displayPriceAfterCashback = Option(90, 100))

  val mockRoom5 =
    mockRoom("room5", price = (120, 150))

  val childRooms1 =
    List(mockRoom1, mockRoom2).map(_.copy(masterTypeId = 5001))
  val childRooms2 =
    List(mockRoom3, mockRoom4, mockRoom5).map(_.copy(masterTypeId = 5002))

  val multipleMasterRooms = childRooms2 ++ childRooms1

  "PropertySearch /api/property" should {
    "return master room with priority children room sorted" in {
      val hotelId = aValidHotelId

      val dfHotel = aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(multipleMasterRooms)
        .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
        .build()

      val hotelResult = Seq(dfHotel)

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.Exclusive)))
        .build()

      val module = new MockModule {
        mockDFResponse(dfResult)
      }
      val expectedChildRooms = List(
        ("room1", topPriority), // 100, cheapest, meaningful
        ("room2", topPriority), // 110, cheapest with pay at hotel, meaningful
        ("room5", none),        // 120, not meaningful
        ("room3", none),        // 200, not meaningful
        ("room4", none)         // 200, not meaningful
      )

      val respF = executePropertySearch(request = request, module = module)
      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1
        val ChildRooms = properties.property.head.masterRooms.head.childrenRooms
        assertChildrenRoomsWithPriority(expectedChildRooms, ChildRooms)
      }
    }
  }

  private def assertChildrenRoomsWithPriority(
      expectedChildRooms: List[(String, Double)],
      result: List[EnrichedChildRoom]
  ): Unit = {
    result should have size expectedChildRooms.size
    result.zipWithIndex.foreach { case (data, index) =>
      data.roomIdentifiers.get shouldBe expectedChildRooms(index)._1
      data.priority.get shouldBe expectedChildRooms(index)._2
    }
  }

  private def mockRoom(
      roomIdentifier: String,
      isBreakfastIncluded: Boolean = false,
      price: (Double, Double),
      displayPriceAfterCashback: Option[(Double, Double)] = None
  ): Room =
    aValidDFRoom
      .withRoomIdentifier(roomIdentifier)
      .withisBreakfastIncluded(isBreakfastIncluded)
      .withPricing(createDfPricingMap(price._1, price._2, "USD", displayPriceAfterCashback))

  private def createDfPricingMap(
      exclusive: Double,
      allInclusive: Double,
      currency: String,
      displayPriceAfterCashback: Option[(Double, Double)]
  ): Map[String, Pricing] = {
    val displayPrice = DisplayPrice(exclusive, allInclusive)
    val displayBasis = DisplayBasis(displayPrice, displayPrice, displayPrice)
    val priceSummary = aValidDisplaySummary
      .withPerRoomPerNight(
        aValidSummaryElement.copy(
          displayAfterCashback = displayPriceAfterCashback match {
            case Some(price) =>
              Some(
                aValidDisplayPrice
                  .withExclusive(price._1)
                  .withAllInclusive(price._2)
                  .build()
              )
            case _ => None
          }
        )
      )
    val dfPricing = Pricing(displayBasis, displayBasis, Seq(), Seq(), CorTypeFlags.NoCOR, priceSummary)
    Map(currency -> dfPricing)
  }

}
