package com.agoda.papi.search.functionaltest.testsuite.common

import akka.actor.ActorSystem
import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.core.search.models.request.CitySearchRequest
import com.agoda.papi.search.common.util.ContentDataExample._
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.core.search.models.request.{ Experiments, ForcedExperiment }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.typesafe.config.ConfigFactory

class HttpRequestSizeLimitsSpec extends PAPISearchFunctionalTest {

  def createCitySearchRequestWithLargeExperimentName(byteSize: Int): CitySearchRequest = {
    val largeExperimentName = "x" * byteSize
    aValidCitySearchRequest.withSearchContext(
      aValidCitySearchRequest.searchContext.copy(
        experiments = Some(
          Experiments(
            forceByVariant = None,
            forceByExperiment = Some(Vector(ForcedExperiment(largeExperimentName, "B")))
          )
        )
      )
    )
  }

  "City Search GraphQL endpoint" should {
    "return 413 Payload Too Large when request exceeds max-content-length > 1000KB" in {
      val citySearchRequest = createCitySearchRequestWithLargeExperimentName(1000001)

      val query = QueryBuilder.build(
        aValidSSRQuery,
        citySearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )

      val module = new GqlMockModule {}
      val respF  = executeSSRGraphQL(query, module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.PayloadTooLarge
      }
    }

    "successfully process requests within the size limit" in {
      val query = QueryBuilder.build(
        aValidSSRQuery,
        aValidCitySearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )

      val module = new GqlMockModule {}
      val respF  = executeSSRGraphQL(query, module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
      }
    }
  }

}
