package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.request.Packaging
import com.agoda.papi.constants.CurrencyCode
import com.agoda.papi.enums.room.CancellationGroup
import com.agoda.papi.search.common.util.DFDataExample.{ aValidCartBundleBasis, aValidCartBundleDisplay }
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.property.service.enrich.EnrichPaymentHelper
import com.agoda.upi.models.cart.Cart
import com.agoda.upi.models.cart.pricing.CartPricing
import com.agoda.upi.models.cart.product.CartProduct
import com.agoda.upi.models.common._
import com.agoda.upi.models.enums._
import com.agoda.upi.models.request.{ CartBaseRequest, CartMetadata }
import constant.StringConstants
import framework.MockModule
import models.pricing.enums.PaymentModels
import models.starfruit.{ Cancellation, PayLater }
import org.joda.time.DateTime
import org.mockito.MockitoSugar
import org.scalatest.BeforeAndAfterEach
import transformers.EnrichedPayAtCheckIn
import types.{ HotelIdType, ProviderIdType }
import util.builders.DefaultTestDataExample
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchPackagesSpec extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach {

  "PropertySearch /api/property" should {
    val hotelIden = Identifier("hotelId", aValidHotelId.toString)
    val roomIden  = Identifier("roomIdentifier", "a")
    val pricing   = CartPricing(CurrencyCode.USD, aValidCartBundleDisplay, aValidCartBundleBasis, Seq.empty, Seq.empty)
    val cartProduct = CartProduct(
      refId = 1,
      info = Info(ItemEntries.Property, OfferEntries.Normal, Seq(hotelIden, roomIden)),
      attribute = PropertyProductAttribute(isAgodaManaged = true, supplierId = 332),
      pricing = Seq(pricing),
      sourceId = None,
      srcId = None
    )
    val aValidCart = Cart(
      token = Some("token"),
      `type` = CartEntries.Bundle,
      products = Seq(cartProduct),
      quantities = Seq(Quantity(QuantityEntries.Pax, 2)),
      pricing = Seq(pricing)
    )

    val defaultHotel = aValidDFHotel.withHotelId(aValidHotelId)
    val checkinDate  = new DateTime("2017-03-01T00:00:00.000+07:00")

    val freeCxlDate = new DateTime("2024-12-06T00:00:00.000+07:00")
    val dfCancellation = Cancellation(
      "365D100P_100P",
      cmsMappedCancellationGroup = CancellationGroup.FreeCancellation,
      cmsMappedFreeCancellationDate = Some(freeCxlDate)
    )
    val payment = DefaultTestDataExample.aValidDFPayment
      .withPaymentModel(PaymentModels.Merchant)
      .withPayLater(PayLater(payLater = true))
      .withCancellation(dfCancellation)
      .build()

    val roomWithoutCart =
      aValidDFRoom
        .withRoomTypeId(1L)
        .withRoomIdentifier("a")
        .withPayment(payment)
        .build()
    val roomWithCart = roomWithoutCart.withCart(Some(aValidCart)).withIsCartEligible(true).build()

    "return cart object and disable PayAtCheckIn when cart request with Package product entry is present" in {
      val dfHotel = defaultHotel.withRooms(Seq(roomWithCart)).build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelId)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val hotelResult = Seq(dfHotel)

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val cartMeta = CartMetadata(productEntry = Some(ProductEntries.PackageProduct), itemMetadata = List.empty)
      val cartBaseRequest = CartBaseRequest(
        token = None,
        arrangement = Some(
          Arrangement(`type` = ArrangementEntries.Bundle, refId = None)
        ),
        sourceId = None,
        srcId = None,
        meta = Some(cartMeta)
      )

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)).withCartRequest(Some(cartBaseRequest)))
        .build()

      // Mock BNPL/Hotel
      val bnplHotel: Map[HotelIdType, Boolean] = Map((defaultHotel.hotelId -> true))

      // Mock BNPL/Suppiler
      val bnplSupplier: Map[ProviderIdType, Boolean] = Map((defaultHotel.supplierId.toInt -> true))

      val mockReportingService = mock[MetricsReporter]

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockBNPLHotelRepository(bnplHotel)
        mockSupplierBNPLEligibilityRepository(bnplSupplier)
        mockMetricsReporter(mockReportingService)
      }

      val respF = executePropertySearch(request = request, module = module)

      val expectedEnrichedPayAtCheckIn = EnrichPaymentHelper.defaultEnrichedPayAtCheckIn

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/property",
        MeasurementTag.PackagingFlowType -> "PackageToCartMigration"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1

        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        childRoom.cart shouldBe Some(aValidCart)
        childRoom.payment.map(_.payAtCheckIn) shouldBe Some(expectedEnrichedPayAtCheckIn)

        verify(mockReportingService, times(1)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }

    "return packaging object when packaging is present in the request" in {
      val roomWithPackaging = roomWithoutCart.withPackaging(Some(aValidDFPackaging)).build()
      val dfHotel           = defaultHotel.withRooms(Seq(roomWithPackaging)).build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelId)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val hotelResult = Seq(dfHotel)

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest.withPackaging(Some(Packaging(None))))
        .withContext(aValidContext.withPropertyIds(List(aValidHotelId)))
        .build()

      val mockReportingService = mock[MetricsReporter]

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockMetricsReporter(mockReportingService)
      }

      val respF = executePropertySearch(request = request, module = module)

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/property",
        MeasurementTag.PackagingFlowType -> "Package"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1

        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        childRoom.packaging.nonEmpty shouldBe true

        verify(mockReportingService, times(1)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }

    "return cart as None and execute PayAtCheckIn flow as normal if there is a normal hotel-search" in {
      val dfHotel = defaultHotel.withRooms(Seq(roomWithoutCart)).build()
      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(aValidHotelId)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
          )
        )
        .build()

      val hotelResult = Seq(dfHotel)

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .build()

      // Mock BNPL/Hotel
      val bnplHotel: Map[HotelIdType, Boolean] = Map((defaultHotel.hotelId -> true))

      // Mock BNPL/Suppiler
      val bnplSupplier: Map[ProviderIdType, Boolean] = Map((defaultHotel.supplierId.toInt -> true))

      val mockReportingService = mock[MetricsReporter]

      val module = new MockModule {
        mockDFResponse(dfResult)
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        mockBNPLHotelRepository(bnplHotel)
        mockSupplierBNPLEligibilityRepository(bnplSupplier)
        mockMetricsReporter(mockReportingService)
      }

      val respF = executePropertySearch(request = request, module = module)

      // PAPI always return EnrichedPayAtCheckIn.isEligible = false even if room is eligible for BNPL
      val expectedEnrichedPayAtCheckIn = EnrichedPayAtCheckIn(
        false,
        freeCxlDate.minusDays(1),
        checkinDate,
        StringConstants.EMPTY_STRING,
        StringConstants.EMPTY_STRING,
        StringConstants.EMPTY_STRING
      )

      val expectedPackageTags = Map(
        MeasurementTag.PlatformId        -> request.context.platform.toString,
        MeasurementTag.Endpoint          -> "/api/property",
        MeasurementTag.PackagingFlowType -> "NonPackage"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1

        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        childRoom.cart shouldBe None
        childRoom.payment.map(_.payAtCheckIn) shouldBe Some(expectedEnrichedPayAtCheckIn)

        verify(mockReportingService, times(0)).report(MeasurementKey.PackageNumberOfRequest, 1L, expectedPackageTags)
      }
    }
  }

}
