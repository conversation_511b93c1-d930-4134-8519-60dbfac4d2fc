package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.commons.dynamic.state.State
import com.agoda.papi.search.common.externaldependency.repository.ProductHotelRepositoryAgsql
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextHolder, WithCorrelationId }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.types.HotelIdType
import framework.MockModule
import request.SearchTypes
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchDsaComplianceSpec extends PAPISearchFunctionalTest {
  val AGODA_WL_TOKEN = "F1A5905F-9620-45E5-9D91-D251C07E0B42"
  val activeAbTests  = new ActiveABTests()

  private def executeTestCase(
      dsaCompliant: <PERSON><PERSON><PERSON>,
      origin: String,
      isHotelCreatedBeforeFeb2024: <PERSON><PERSON><PERSON>,
      whiteLabelKey: String = AGODA_WL_TOKEN,
      blockDSANonCompliantHotelRegisteredBeforeFeb2024: Boolean = false,
      useRegulatoryBlockingForDsaCompliant: Char = 'A',
      useDisableDsaLicenseBlockingVariant: Char = 'A',
      expectedSize: Int
  ): Unit = {
    val dfHotel = aValidDFHotel
      .withHotelId(aValidHotelId)
      .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
      .build()

    val contentMasterRooms = aValidRoomInformationResponse
      .withHotelId(aValidHotelId)
      .withMasterRoomResponse(
        Vector(
          aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
        )
      )
      .build()

    val module = new MockModule {
      mockDFResponse(Map(aValidHotelId -> dfHotel))
      mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      if (blockDSANonCompliantHotelRegisteredBeforeFeb2024) {
        (bind[State[Boolean]] to State.of(true)).identifiedBy('BlockDSANonCompliantHotelRegisteredBeforeFeb2024)
      }
      bind[ProductHotelRepositoryAgsql] to new ProductHotelRepositoryAgsql {
        def getProductHotels(hotels: Seq[HotelIdType])(implicit
            withCorrelationId: WithCorrelationId,
            contextHolder: ContextHolder
        ): Future[Map[HotelIdType, ProductHotels]] = Future.successful(
          Map(
            aValidHotelId -> aValidProductHotel
              .withHotelId(aValidHotelId)
              .withIsDsaCompliant(dsaCompliant)
              .withIsHotelCreatedBeforeFeb2024(isHotelCreatedBeforeFeb2024)
          )
        )
      }
      mockRegulatoryBlockingService(Future.successful(_ => dsaCompliant))
    }

    val request = aValidPropertyRequest
      .withSearchType(SearchTypes.Property)
      .withHotelIds(List(aValidHotelId))
      .withPricing(aValidPricingRequest)
      .withOrigin(origin)
      .withWhiteLabelKey(Some(whiteLabelKey))
      .withAdditionalExperimentForce(
        activeAbTests.useDisableDsaLicenseBlockingWLFeature,
        useDisableDsaLicenseBlockingVariant.toString
      )
      .withAdditionalExperimentForce(
        activeAbTests.useRegulatoryBlockingForDsaCompliant,
        useRegulatoryBlockingForDsaCompliant.toString
      )
      .build()

    val respF = executePropertySearch(request = request, module = module)

    whenReady(respF) { resp =>
      assertCompliantResponse(resp, expectedSize)
    }

  }

  private def assertCompliantResponse(
      resp: HttpResponse,
      roomsSize: Int
  ): Unit = {
    resp.status shouldBe StatusCodes.OK
    val properties = responseToProperties(resp)
    properties.property.head.masterRooms should have size roomsSize
  }

  "Only compliant properties should be returned" should {
    "PropertyDsaCompliant" should {
      "Always return property for non-EU origin user regardless of compliance status" should {
        "Compliant property" in {
          executeTestCase(
            true,
            "TH",
            false,
            expectedSize = 1
          )
        }
        "Non-compliant property" in {
          executeTestCase(
            false,
            "TH",
            true,
            expectedSize = 1
          )
        }
        "Non-compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
          executeTestCase(
            false,
            "TH",
            true,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
            expectedSize = 1
          )
        }
        "Compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
          executeTestCase(
            true,
            "TH",
            false,
            blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
            expectedSize = 1
          )
        }
      }

      "Return only compliant property for EU origin user" should {
        "Property created after Feb 2024" should {
          "Compliant property" in {
            executeTestCase(
              true,
              "FR",
              false,
              expectedSize = 1
            )
          }
          "Non-compliant property" in {
            executeTestCase(
              false,
              "FR",
              false,
              expectedSize = 0
            )
          }
          "Compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
            executeTestCase(
              true,
              "FR",
              false,
              blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
              expectedSize = 1
            )
          }
          "Non-compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 is ON" in {
            executeTestCase(
              false,
              "FR",
              false,
              blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
              expectedSize = 0
            )
          }
        }

        "Property created before Feb 2024 and blockDSANonCompliantHotelRegisteredBeforeFeb2024 OFF" should {
          "Compliant property" in {
            executeTestCase(
              true,
              "FR",
              true,
              expectedSize = 1
            )
          }

          "Non-compliant property" in {
            executeTestCase(
              false,
              "FR",
              true,
              expectedSize = 1
            )
          }
        }

        "Property created before Feb 2024 and blockDSANonCompliantHotelRegisteredBeforeFeb2024 ON" should {
          "Compliant property" in {
            executeTestCase(
              true,
              "FR",
              true,
              blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
              expectedSize = 1
            )
          }

          "Non-compliant property with blockDSANonCompliantHotelRegisteredBeforeFeb2024 flag is ON" in {
            executeTestCase(
              false,
              "FR",
              true,
              blockDSANonCompliantHotelRegisteredBeforeFeb2024 = true,
              expectedSize = 0
            )
          }
        }
      }
    }

    "PropertyDsaCompliantV2" should {
      "Return property when the property is compliant" in {
        executeTestCase(
          true,
          "DE",
          false,
          expectedSize = 1,
          useRegulatoryBlockingForDsaCompliant = 'B'
        )
      }
      "Not return property when the property is non-compliant" in {
        executeTestCase(
          false,
          "DE",
          false,
          expectedSize = 0,
          useRegulatoryBlockingForDsaCompliant = 'B'
        )
      }
    }

    "Return only compliant for users with EU origin Landing from different WLs" when {
      "Agoda WL - Disabled DisableDsaLicenseBlockingWLFeature" should {
        "Compliant property - with LT-1208 is OFF" in {
          executeTestCase(
            true,
            "FR",
            false,
            AGODA_WL_TOKEN,
            expectedSize = 1
          )
        }

        "Compliant property - with LT-1208 is ON" in {
          executeTestCase(
            true,
            "FR",
            false,
            AGODA_WL_TOKEN,
            useDisableDsaLicenseBlockingVariant = 'B',
            expectedSize = 1
          )
        }

        "Non-Compliant property - with LT-1208 is OFF" in {
          executeTestCase(
            false,
            "FR",
            false,
            AGODA_WL_TOKEN,
            expectedSize = 0
          )
        }

        "Non-Compliant property - with LT-1208 is ON" in {
          executeTestCase(
            false,
            "FR",
            false,
            AGODA_WL_TOKEN,
            useDisableDsaLicenseBlockingVariant = 'B',
            expectedSize = 0
          )
        }
      }
      "JAPANICAN WL - Disabled DisableDsaLicenseBlockingWLFeature" should {
        val JAPANICAN_WL_TOKEN = "A876A852-CA8F-4F2D-9A6B-718D3ED6C514"

        "Compliant property - with LT-1208 is OFF" in {
          executeTestCase(
            true,
            "FR",
            false,
            JAPANICAN_WL_TOKEN,
            expectedSize = 1
          )
        }

        "Compliant property - with LT-1208 is ON" in {
          executeTestCase(
            true,
            "FR",
            false,
            JAPANICAN_WL_TOKEN,
            useDisableDsaLicenseBlockingVariant = 'B',
            expectedSize = 1
          )
        }

        "Non-Compliant property - with LT-1208 is OFF" in {
          executeTestCase(
            false,
            "FR",
            false,
            JAPANICAN_WL_TOKEN,
            expectedSize = 0
          )
        }

        "Non-Compliant property - with LT-1208 is ON" in {
          executeTestCase(
            false,
            "FR",
            false,
            JAPANICAN_WL_TOKEN,
            useDisableDsaLicenseBlockingVariant = 'B',
            expectedSize = 1
          )
        }
      }
    }

  }

}
