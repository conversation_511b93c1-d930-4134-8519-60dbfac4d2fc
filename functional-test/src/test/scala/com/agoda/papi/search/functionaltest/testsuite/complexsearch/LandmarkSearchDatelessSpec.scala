package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.StatusCodes
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.model.ESPropertyMetaData
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.database.AverageRoomPrice
import com.agoda.papi.search.types.WithHotelIdMap
import com.agoda.property.search.framework.GqlMockModule
import org.joda.time.DateTime
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class LandmarkSearchDatelessSpec extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  val propertyIds = List(hotel1, hotel2, hotel3)

  val landmark1 = 324251L

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withLandmarkIds(List(landmark1))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withLandmarkIds(List(landmark1))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withLandmarkIds(List(landmark1))
      .build()
  )

  val hotel1AverageRoomPrice = AverageRoomPrice(600d, 500d)
  val hotel2AverageRoomPrice = AverageRoomPrice(941d, 499d)
  val hotel3AverageRoomPrice = AverageRoomPrice(777d, 888d)

  val soldoutAvgRoomPriceMap = Map(
    hotel1.toInt -> hotel1AverageRoomPrice,
    hotel2.toInt -> hotel2AverageRoomPrice,
    hotel3.toInt -> hotel3AverageRoomPrice
  )

  private val hotelPropertyMetaData: WithHotelIdMap[ESPropertyMetaData] = propertyIds.map { h =>
    h -> aValidESPropertyMetaData.withHotelId(h).build()
  }(collection.breakOut)

  "Landmark search" should {
    "Return DatelessProperty propertyResultType when do dateless search" in {
      val avail            = generateDefaultAvail(propertyIds)
      val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)

      val datelessSearchCriteria = aValidSearchCriteria
        .withIsDatelessSearch(Some(true))
        .build()

      val searchContext = aValidSearchContext
        .copy(endpointSearchType = Some(SearchTypes.LandmarkSearch))

      val datelessSr = aValidSearchRequest
        .withSearchContext(searchContext)
        .withSearchCriteria(datelessSearchCriteria)
        .build()

      val landmarkSearchRequest = aValidLandmarkSearchRequest
        .withSearchRequest(datelessSr)
        .withLandmarkId(landmark1)
        .withFilters(List.empty)
        .build()

      val query = QueryBuilder.build(
        aValidLandmarkSearchQuery,
        landmarkSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )

      val mockModule = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)
        mockSoldOutPropsPriceRepository(soldoutAvgRoomPriceMap)
        mockAggregationService(Future.successful(hotelPropertyMetaData))
      }

      val resF = executeSSRGraphQL(
        query,
        mockModule
      )

      whenReady(resF) { res =>
        res.status shouldBe StatusCodes.OK
        val landmarkSearchResponse = responseToLandmarkSearchResponse(res)

        val propertyResultMap =
          landmarkSearchResponse.data.landmarkSearch.properties.get.map(p => p.propertyId -> p).toMap

        propertyResultMap should have size propertyIds.size

        propertyResultMap(hotel1).propertyResultType shouldBe "DatelessProperty"
        propertyResultMap(hotel2).propertyResultType shouldBe "DatelessProperty"
        propertyResultMap(hotel3).propertyResultType shouldBe "DatelessProperty"
      }
    }

    "Still return DatelessProperty propertyResultType when do dateless search with booking date set" in {
      val avail            = generateDefaultAvail(propertyIds)
      val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)

      val datelessSearchCriteria = aValidSearchCriteria
        .withIsDatelessSearch(Some(true))
        .withBookingDate(DateTime.now())
        .build()

      val searchContext = aValidSearchContext
        .copy(endpointSearchType = Some(SearchTypes.LandmarkSearch))

      val datelessSr = aValidSearchRequest
        .withSearchContext(searchContext)
        .withSearchCriteria(datelessSearchCriteria)
        .build()

      val landmarkSearchRequest = aValidLandmarkSearchRequest
        .withSearchRequest(datelessSr)
        .withLandmarkId(landmark1)
        .withFilters(List.empty)
        .build()

      val query = QueryBuilder.build(
        aValidLandmarkSearchQuery,
        landmarkSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )

      val mockModule = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)
        mockSoldOutPropsPriceRepository(soldoutAvgRoomPriceMap)
        mockAggregationService(Future.successful(hotelPropertyMetaData))
      }

      val resF = executeSSRGraphQL(
        query,
        mockModule
      )

      whenReady(resF) { res =>
        res.status shouldBe StatusCodes.OK
        val landmarkSearchResponse = responseToLandmarkSearchResponse(res)

        val propertyResultMap =
          landmarkSearchResponse.data.landmarkSearch.properties.get.map(p => p.propertyId -> p).toMap

        propertyResultMap should have size propertyIds.size

        propertyResultMap(hotel1).propertyResultType shouldBe "DatelessProperty"
        propertyResultMap(hotel2).propertyResultType shouldBe "DatelessProperty"
        propertyResultMap(hotel3).propertyResultType shouldBe "DatelessProperty"
      }
    }
  }

}
