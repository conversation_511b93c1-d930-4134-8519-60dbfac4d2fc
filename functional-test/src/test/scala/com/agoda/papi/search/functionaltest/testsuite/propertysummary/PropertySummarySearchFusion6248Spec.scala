package com.agoda.papi.search.functionaltest.testsuite.propertysummary

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.papi.search.common.externaldependency.chunkable.DFService
import com.agoda.papi.search.common.framework.mock.DFServiceDefaultTest
import com.agoda.papi.search.common.util.ContentDataExample.aValidContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.search.common.util.DFDataExample.aPricingRequestParameters
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.request.{ PricingPropertiesRequestWrapper, PropertySummarySearchRequest }
import com.agoda.platform.service.context.ServiceHeaders
import api.request.FeatureFlag
import org.mockito.ArgumentMatchers.{ any => mockitoAny }
import org.mockito.Mockito
import org.specs2.mock.mockito.ArgumentCapture
import scaldi.Module

import scala.concurrent.Future
import scala.jdk.CollectionConverters.asScalaIterator

class PropertySummarySearchFusion6248Spec extends PAPISearchFunctionalTest {

  private def executeTestCase(
      experimentVariant: Char
  ): Future[(HttpResponse, List[PricingPropertiesRequestWrapper])] = {
    val spiedDFService = Mockito.spy(new DFServiceDefaultTest)

    val query = QueryBuilder.buildPropertySummarySearchQuery(
      """
        |query propertySummarySearch(
        |  $PropertySummarySearchRequest: PropertySummarySearchRequest!,
        |  $PricingSummaryRequest: PricingRequestParameters!,
        |  $ContentSummaryRequest: ContentSummaryRequest!
        |) {
        |  propertySummarySearch(PropertySummarySearchRequest: $PropertySummarySearchRequest) {
        |    properties(
        |      PricingSummaryRequest: $PricingSummaryRequest,
        |      ContentSummaryRequest: $ContentSummaryRequest
        |    ) {
        |      propertyId,
        |      propertyResultType
        |    }
        |  }
        |}
        |""".stripMargin,
      PropertySummarySearchRequest(List(1001L, 1002L)),
      Some(
        aPricingRequestParameters.copy(
          pricing = aPricingRequestParameters.pricing.copy(
            featureFlag = List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnablePapiEnrichment)
          )
        )
      ),
      Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t),
      None,
      None
    )

    val module = new Module {
      bind[DFService] to spiedDFService
    }

    val forceExpHeaders = s"FUSION-6248,$experimentVariant"
    val headers         = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, forceExpHeaders)

    val respF = executePropertySummarySearchGraphQL(query = query, module = module, headers = headers)

    respF.map { resp =>
      val dfRequestCaptor = new ArgumentCapture[PricingPropertiesRequestWrapper]
      Mockito
        .verify(spiedDFService, Mockito.atLeast(1))
        .process(dfRequestCaptor.capture, mockitoAny(), mockitoAny())(mockitoAny())
      val capturedDFRequests = asScalaIterator(dfRequestCaptor.values.iterator()).toList
      resp -> capturedDFRequests
    }
  }

  private def fetchFeatureFlagsFromDFRequest(
      capturedDFRequests: List[PricingPropertiesRequestWrapper]
  ): List[FeatureFlag] =
    capturedDFRequests.flatMap { request =>
      val mainFeatureFlags = request.pricingRequest.pricingRequestParameters.t.pricing.featureFlag
      val overrideFeatureFlags = request.pricingRequest.overridePricingRequest
        .flatMap(_.additionalFeatureFlags)
        .getOrElse(List.empty)
      mainFeatureFlags ++ overrideFeatureFlags
    }

  s"[FUSION-6248] PropertySummarySearch Ignore ReturnHotelNotReadyIfPullNotReady Flag Experiment" should {

    "A side should send ReturnHotelNotReadyIfPullNotReady flag to DF" in {
      val testResult = executeTestCase(experimentVariant = 'A')

      whenReady(testResult) { case (resp, capturedDFRequests) =>
        resp.status shouldBe StatusCodes.OK
        val propertySummarySearchResponse = responseToPropertySummarySearchResponse(resp)
        val propertyIds = propertySummarySearchResponse.data.propertySummarySearch.properties.map(_.propertyId)
        propertyIds should contain theSameElementsAs List(1001L, 1002L)

        // Verify that ReturnHotelNotReadyIfPullNotReady flag was sent to DF
        capturedDFRequests should not be empty
        val allFeatureFlags = fetchFeatureFlagsFromDFRequest(capturedDFRequests)
        allFeatureFlags should contain(FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
      }
    }

    "B side should filter out ReturnHotelNotReadyIfPullNotReady flag from DF request" in {
      val testResult = executeTestCase(experimentVariant = 'B')

      whenReady(testResult) { case (resp, capturedDFRequests) =>
        resp.status shouldBe StatusCodes.OK
        val propertySummarySearchResponse = responseToPropertySummarySearchResponse(resp)
        val propertyIds = propertySummarySearchResponse.data.propertySummarySearch.properties.map(_.propertyId)
        propertyIds should contain theSameElementsAs List(1001L, 1002L)

        // Verify that ReturnHotelNotReadyIfPullNotReady flag was filtered out from DF request
        capturedDFRequests should not be empty
        val allFeatureFlags = fetchFeatureFlagsFromDFRequest(capturedDFRequests)
        allFeatureFlags should not contain (FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
        allFeatureFlags should contain(FeatureFlag.EnablePapiEnrichment)
      }
    }

  }

}
