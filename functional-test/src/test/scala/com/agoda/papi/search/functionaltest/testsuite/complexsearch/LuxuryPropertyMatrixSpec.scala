package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.StatusCodes
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models
import com.agoda.core.search.models.enumeration.matrix.MatrixGroupTypes
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.enumeration.LuxuryPropertyType
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class LuxuryPropertyMatrixSpec extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withLuxuryPropertyTypes(Seq(LuxuryPropertyType.BestInLuxury.i))
      .build()
  )

  val hotelIds         = (hotel1 to hotel2).toList
  val avail            = generateDefaultAvail(hotelIds)
  val propertyMetadata = generateDefaultPropertyInfoList(hotelIds)

  val matrixGroupRequest = Option(
    List(
      models.request.MatrixGroupRequest(MatrixGroupTypes.LuxuryPropertyTypes, Some(10))
    )
  )

  "GraphQL Schema" should {
    "Return aggregation result for luxuryPropertyTypes" in {

      val query = QueryBuilder.build(
        aValidSSRQuery,
        aValidCitySearchRequest.withMatrixGroup(matrixGroupRequest),
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val mockModule = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)
      }
      val result = executeSSRGraphQL(
        query,
        mockModule,
        aValidSSRHttpHeaders
      )

      whenReady(result) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)

        val matrixGroupResults = citySearchResponse.data.citySearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 1
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs List(
          TestMatrixItemResult(1, "CMSId:350972", 1)
        )
      }

    }
  }

}
