package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ Http<PERSON><PERSON><PERSON>, HttpResponse, StatusCodes }
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.content.models.propertycontent.ContentSummary
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.framework.dexsync.ElasticHotelInfo
import com.agoda.core.search.models.Fields.STARRATING
import com.agoda.core.search.models._
import com.agoda.core.search.models.enumeration.matrix.FilterKeys
import request.{ CitySearchRequest, RangeFilterDefinition, TermsFilterDefinition, TupleStringDouble }
import com.agoda.papi.search.common.util.ContentDataExample.{ aValidContentSummary, contentSummaryRequest }
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.property.search.utils.builders.GqlTestDataBuilders._
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchReviewScoreFilterSpec extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {
  override def beforeAll(): Unit = mockEsBeforeAll()

  override val docs: List[ElasticHotelInfo] = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withReview(
        List(
          aValidElasticReview.withScore(10).withProviderId(ProviderIds.Ycs).build(),
          aValidElasticReview.withScore(5.0).withProviderId(ProviderIds.Bcom).build()
        )
      )
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withReview(List(aValidElasticReview.withScore(7.5).withProviderId(ProviderIds.Bcom).build()))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withReview(List(aValidElasticReview.withScore(9).withProviderId(ProviderIds.Ycs).build()))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .withReview(List(aValidElasticReview.withScore(8).withProviderId(ProviderIds.AirBnb).build()))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .withReview(List(aValidElasticReview.withScore(9.5).withProviderId(ProviderIds.Jtb).build()))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel6)
      .withReview(List(aValidElasticReview.withScore(9.0).withProviderId(ProviderIds.Ycs).build()))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel7)
      .withReview(List(aValidElasticReview.withScore(9.2).withProviderId(ProviderIds.Ycs).build()))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel8)
      .withReview(List(aValidElasticReview.withScore(9.3).withProviderId(ProviderIds.Ycs).build()))
      .build()
  )

  private val hotelIds = (hotel1 to hotel8).toList

  private val meta = generateDefaultPropertyInfoList(hotelIds)

  private val avail = generateDefaultAvail(hotelIds)

  private val contentSummaryMap: Map[Long, ContentSummary] = Map(
    hotel1 -> aValidContentSummary(hotel1).withCumulativeReviewScore(10.0d).build(),
    hotel2 -> aValidContentSummary(hotel2).withCumulativeReviewScore(9.0d).build(),
    hotel3 -> aValidContentSummary(hotel3).withCumulativeReviewScore(3.0d).build(),
    hotel4 -> aValidContentSummary(hotel4).withCumulativeReviewScore(3.0d).build(),
    hotel5 -> aValidContentSummary(hotel5).build(),
    hotel6 -> aValidContentSummary(hotel6).build(),
    hotel7 -> aValidContentSummary(hotel7).withCumulativeReviewScore(9.5d).build(),
    hotel8 -> aValidContentSummary(hotel8).withCumulativeReviewScore(9.5d).build()
  )

  val headersForceA: Seq[HttpHeader] =
    aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5235,A")
  val headersForceB: Seq[HttpHeader] =
    aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5235,B")

  "city search filter by review score" should {
    "return only hotel that match ReviewScore RangeFilter filters as per content's cumulative review score when FUSION-5235=B" in {
      val respF = buildAndExecuteRequest(buildRequestWithReviewScoreRangeFilter(), headersForceB)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse      = responseToCitySearchResponse(resp)
        val propertyIds: List[Long] = citySearchResponse.data.citySearch.properties.get.map(_.propertyId)
        (propertyIds should contain).theSameElementsInOrderAs(Seq(hotel1, hotel7, hotel8))
      }
    }
    "return only hotel that match ReviewScore RangeFilter filters as per ES's review score when FUSION-5235=A" in {

      val respF = buildAndExecuteRequest(buildRequestWithReviewScoreRangeFilter(), headersForceA)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse      = responseToCitySearchResponse(resp)
        val propertyIds: List[Long] = citySearchResponse.data.citySearch.properties.get.map(_.propertyId)
        (propertyIds should contain).theSameElementsInOrderAs(
          Seq(hotel1, hotel3, hotel4, hotel5, hotel6, hotel7, hotel8)
        )
      }
    }
    "return only hotel that match ReviewScore ElasticFilter filters as per content's cumulative review score when FUSION-5235=B" in {
      val respF = buildAndExecuteRequest(buildRequestWithReviewScoreElasticFilter(), headersForceB)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse      = responseToCitySearchResponse(resp)
        val propertyIds: List[Long] = citySearchResponse.data.citySearch.properties.get.map(_.propertyId)
        (propertyIds should contain).theSameElementsInOrderAs(Seq(hotel1, hotel7, hotel8))
      }
    }
    "return only hotel that match ReviewScore ElasticFilter filters as per ES's review score when FUSION-5235=A" in {

      val respF = buildAndExecuteRequest(buildRequestWithReviewScoreElasticFilter(), headersForceA)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse      = responseToCitySearchResponse(resp)
        val propertyIds: List[Long] = citySearchResponse.data.citySearch.properties.get.map(_.propertyId)
        (propertyIds should contain).theSameElementsInOrderAs(
          Seq(hotel1, hotel3, hotel4, hotel5, hotel6, hotel7, hotel8)
        )
      }
    }

  }

  private def buildRequestWithReviewScoreRangeFilter() = {
    val searchCriteria = aValidSearchCriteria
      .build()

    val filterRequest = FilterRequest(rangeFilters =
      Some(
        List(
          RangeFilterRequest(
            FilterKeys.ReviewScore,
            ranges = List(RangeFilterItem(8.0, Some(10.0)))
          )
        )
      )
    )

    val citySearchRequest = aValidCitySearchRequest
      .withSearchCriteria(searchCriteria)
      .withFilterRequest(filterRequest)
      .build()

    citySearchRequest
  }

  private def buildRequestWithReviewScoreElasticFilter() = {
    val searchCriteria = aValidSearchCriteria
      .build()

    val citySearchRequest = aValidCitySearchRequest
      .withSearchCriteria(searchCriteria)
      .withFilterRequest(
        FilterRequest(elasticFilters =
          Some(
            ElasticFilterRequest(range =
              List(
                RangeFilterDefinition(
                  Fields.REVIEWSCORE,
                  tupleList = Some(List(TupleStringDouble("gte", 8.0), TupleStringDouble("lte", 10.0)))
                )
              )
            )
          )
        )
      )
      .build()

    citySearchRequest
  }

  private def buildAndExecuteRequest(
      citySearchRequest: CitySearchRequest,
      headers: Seq[HttpHeader]
  ): Future[HttpResponse] = {

    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val module: GqlMockModule = new GqlMockModule {
      mockCachePropertyInfoList(meta)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockESMatrixService(client)
      mockContentService(
        contentSummaryMap
      )
    }

    executeSSRGraphQL(
      query,
      module,
      headers
    )
  }

}
