package com.agoda.papi.search.functionaltest.framework.testdatabuilder

import com.agoda.common.graphql.model.Query
import com.agoda.core.search.framework.builders.DefaultTestDataExample._
import com.agoda.papi.search.complexsearch.model.serializer.encoder.RequestEncoders._
import com.agoda.content.models.circe.Serialization._
import com.agoda.content.models.request.sf.{
  ContentExperienceRequest,
  ContentFeaturesRequest,
  ContentHighlightsRequest,
  ContentImagesRequest,
  ContentInformationRequest,
  ContentInformationSummaryRequest,
  ContentLocalInformationRequest,
  ContentQnaRequest,
  ContentRateCategoryRequest,
  ContentReviewScoreRequest,
  TopicsRequest
}
import com.agoda.papi.search.internalmodel.growthprogram.GrowthProgramInfoRequest
import com.agoda.papi.search.internalmodel.request.{
  HostProfileRequest,
  PriceStreamMetaLabRequest,
  PropertyDetailsRequest
}
import com.agoda.papi.search.internalmodel.promocode.BannerCampaignRequest
import io.circe.JsonObject
import io.circe.syntax._

class PropertyDetailsSearchRequestBuilder extends Builder[Query] {

  private var queryString: String = """
    |query propertyDetailsSearch($PropertyDetailsRequest: PropertyDetailsRequest!variables_placeholder) {
    |  propertyDetailsSearch(PropertyDetailsRequest: $PropertyDetailsRequest) {
    |    propertyDetails {
    |      propertyId
    |      propertyDetail_placeholder
    |    }
    |  }
    |}
    |""".stripMargin

  private var variables: JsonObject = JsonObject(
    "PropertyDetailsRequest" -> aValidPropertyDetailsRequest.asJson
  )

  private val defaultContentDetailQueryString: String =
    """
      |propertyDetail_placeholder
      |contentDetail {
      |  contentDetail_placeholder
      |}
      |""".stripMargin

  def withPropertyDetailsRequest(req: PropertyDetailsRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("PropertyDetailsRequest", req.asJson)
    this
  }

  def withContentReviewScore(req: ContentReviewScoreRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentReviewScoreRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $ContentReviewScoreRequest: ContentReviewScoreRequest!variables_placeholder")
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentReviewScore(ContentReviewScoreRequest: $ContentReviewScoreRequest) {
        |  combinedReviewScore {
        |    providerIds
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentInformationSummaryRequest(
      req: ContentInformationSummaryRequest
  ): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentInformationSummaryRequest", req.asJson)
    queryString = queryString
      .replace(
        "variables_placeholder",
        ", $ContentInformationSummaryRequest: ContentInformationSummaryRequest!variables_placeholder"
      )
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentSummary(ContentInformationSummaryRequest: $ContentInformationSummaryRequest) {
        |  localeName
        |}
        |""".stripMargin
    )
    this
  }

  // contentEngagement is only in the query. there is no request for it
  def withContentEngagement: PropertyDetailsSearchRequestBuilder = {
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentEngagement {
        |  propertyId
        |  peopleLooking
        |}
        |""".stripMargin
    )
    this
  }

  def withContentExperiences(req: ContentExperienceRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentExperienceRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $ContentExperienceRequest: ContentExperienceRequest!variables_placeholder")
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentExperiences(ContentExperienceRequest: $ContentExperienceRequest) {
        |  experience {
        |   name
        |   symbol
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentFeatures(req: ContentFeaturesRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentFeaturesRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $ContentFeaturesRequest: ContentFeaturesRequest!variables_placeholder")
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentFeatures(ContentFeaturesRequest: $ContentFeaturesRequest) {
        |  featureGroups {
        |    id
        |    name
        | }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentHighlights(req: ContentHighlightsRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentHighlightsRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $ContentHighlightsRequest: ContentHighlightsRequest!variables_placeholder")
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentHighlights(ContentHighlightsRequest: $ContentHighlightsRequest) {
        |  favoriteFeatures {
        |    name
        |  }
        |  locationHighlightMessage {
        |    title
        |  }
        |  atfPropertyHighlights {
        |    name
        |    topicName
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withGrowthProgramInfo(req: GrowthProgramInfoRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("GrowthProgramInfoRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $GrowthProgramInfoRequest: GrowthProgramInfoRequest!variables_placeholder")
    queryString = queryString.replace(
      "propertyDetail_placeholder",
      """
        |propertyDetail_placeholder
        |growthProgramInfo(GrowthProgramInfoRequest: $GrowthProgramInfoRequest) {
        |  badges
        |}
        |""".stripMargin
    )
    this
  }

  def withRateCategory(req: ContentRateCategoryRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentRateCategoryRequest", req.asJson)
    queryString = queryString
      .replace(
        "variables_placeholder",
        ", $ContentRateCategoryRequest: ContentRateCategoryRequest!variables_placeholder"
      )
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentRateCategory(ContentRateCategoryRequest: $ContentRateCategoryRequest) {
        |  rateCategories {
        |    rateCategoryId
        |    localizedRateCategoryName
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withMetaLab(req: PriceStreamMetaLabRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("PriceStreamMetaLabRequest", req.asJson)
    queryString = queryString
      .replace(
        "variables_placeholder",
        ", $PriceStreamMetaLabRequest: PriceStreamMetaLabRequest!variables_placeholder"
      )
    queryString = queryString.replace(
      "propertyDetail_placeholder",
      """
        |propertyDetail_placeholder
        |metaLab(PriceStreamMetaLabRequest: $PriceStreamMetaLabRequest) {
        |  propertyAttributes {
        |    attributeId
        |    dataType
        |    version
        |    value
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentQna(req: ContentQnaRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentQnaRequest", req.asJson)
    queryString = queryString
      .replace(
        "variables_placeholder",
        ", $ContentQnaRequest: ContentQnaRequest!variables_placeholder"
      )
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentQna(ContentQnaRequest: $ContentQnaRequest) {
        |  qnas {
        |    id
        |    question
        |    answer
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentTopics(req: TopicsRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentTopicsRequest", req.asJson)
    queryString = queryString
      .replace(
        "variables_placeholder",
        ", $ContentTopicsRequest: TopicsRequest!variables_placeholder"
      )
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentTopics(ContentTopicsRequest: $ContentTopicsRequest) {
        |  topics {
        |    topic
        |    sentimentScore {
        |      positive
        |      negative
        |      neutral
        |    }
        |    reviewSnippets
        |    images {
        |      url
        |      imageId
        |      imageSource
        |    }
        |    summary
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentImages(req: ContentImagesRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentImagesRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $ContentImagesRequest: ContentImagesRequest!variables_placeholder")
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentImages(ContentImagesRequest: $ContentImagesRequest) {
        |  mainImage {
        |    id
        |    caption
        |   }
        |   hotelImages {
        |     id
        |     caption
        |   }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentInformation(req: ContentInformationRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentInformationRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $ContentInformationRequest: ContentInformationRequest!variables_placeholder")
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentInformation(ContentInformationRequest: $ContentInformationRequest) {
        |  propertyId
        |  description{
        |    long
        |    short
        |  }
        |  notes{
        |    publicNotes
        |  }
        |  policies{
        |    extraBed
        |  }
        |  companyTraceabilityData {
        |    requiredLayoutType
        |    traderInformationList {
        |      tradingName
        |      address {
        |        addressUnit
        |        addressFloor
        |        addressBuilding
        |        addressStreet1
        |        addressStreet2
        |        city
        |        stateId
        |        stateName
        |        countryId
        |        countryName
        |        postalCode
        |      }
        |      email
        |      phoneNumber
        |      registrationNo
        |      chainSpecificComplianceText
        |      representativeName
        |    }
        |  }
        |}
        |""".stripMargin
    )
    this
  }

  def withContentLocalInformationRequest(req: ContentLocalInformationRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("ContentLocalInformationRequest", req.asJson)
    queryString = queryString
      .replace(
        "variables_placeholder",
        ", $ContentLocalInformationRequest: ContentLocalInformationRequest!variables_placeholder"
      )
    if (!queryString.contains("contentDetail_placeholder")) {
      queryString = queryString
        .replace("propertyDetail_placeholder", defaultContentDetailQueryString)
    }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      """
        |contentDetail_placeholder
        |contentLocalInformation(ContentLocalInformationRequest: $ContentLocalInformationRequest) {
        |         nearbyProperties {
        |            id
        |          }
        |          nearbyPlaces {
        |            name
        |          }
        |          topPlaces {
        |            name
        |          }
        |          popularLandmarkNumber
        |}
        |""".stripMargin
    )
    this
  }

  def withBannerCampaignRequest(req: BannerCampaignRequest): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("BannerCampaignRequest", req.asJson)
    queryString = queryString
      .replace("variables_placeholder", ", $BannerCampaignRequest: BannerCampaignRequest!variables_placeholder")
    queryString = queryString.replace(
      "propertyDetail_placeholder",
      """
        |propertyDetail_placeholder
        |bannerCampaignInfo(BannerCampaignRequest: $BannerCampaignRequest) {
        |  campaignId
        |  campaignName
        |  ranking
        |}
        |""".stripMargin
    )
    this
  }

  def withHostProfileInfo(req: Option[HostProfileRequest] = None): PropertyDetailsSearchRequestBuilder = {
    variables = variables.add("HostProfileRequest", req.asJson)
    if (!queryString.contains("$HostProfileRequest: HostProfileRequest!")) {
      queryString = queryString
        .replace("variables_placeholder", ", $HostProfileRequest: HostProfileRequest!variables_placeholder")
    }
    val hostProfileFields =
      if (req.flatMap(_.isHostPropertyEnabled).getOrElse(false)) {
        """
          |userId
          |displayName
          |properties {
          |  id
          |  bookings
          |  reviewCount
          |  reviewAvg
          |}
        """.stripMargin
      } else {
        "userId"
      }
    queryString = queryString.replace(
      "contentDetail_placeholder",
      s"""
        |contentDetail_placeholder
        |hostProfile(HostProfileRequest: $$HostProfileRequest) {
        |  $hostProfileFields
        |}
        |""".stripMargin
    )
    this
  }

  def withPropertyMetaInfo(): PropertyDetailsSearchRequestBuilder = {
    queryString = queryString
      .replace(
        "propertyDetail_placeholder",
        """
          |propertyDetail_placeholder
          |propertyMetaInfo {
          |      propertyId
          |      propertyMetaRanking {
          |        numberOfProperty
          |        metrics {
          |          metricName
          |          rank
          |          absoluteValue
          |        }
          |      }
          |    }
          |""".stripMargin
      )
    this
  }

  override def build(): Query = {
    val finalQueryString = queryString
      .replace("variables_placeholder", "")
      .replace("propertyDetail_placeholder", "")
      .replace("contentDetail_placeholder", "")
    Query(query = finalQueryString, operationName = Some("propertyDetailsSearch"), variables = variables.asJson)
  }

}
