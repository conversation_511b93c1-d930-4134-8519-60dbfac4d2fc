package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.commons.http.client.v2.RequestSettings
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.agoda.pricestream.models.request.PropertyMetaInfoRequest
import com.agoda.pricestream.models.response._
import framework.MockModule
import framework.testservice.PriceStreamHttpClientTest
import request.{ FeatureFlagRequest, SearchTypes }
import transformers.Properties
import util.builders.TestDataBuilders._
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.prop.Tables.Table

import scala.concurrent.Future

class PropertyMetaInfoSpec extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {
  // Migrate it/src/test/resources/features/papi/othersearch/PropertyMetaInfo.feature
  val activeAbTest = ActiveABTests()
  val propertyId   = 1001

  val hotelMetaRankingResponse: Option[PropertyMetaRanking] = Some(
    PropertyMetaRanking(propertyId, Vector(RankMetric("metric_name", 1, 1.0)))
  )

  val lastYearBookingCountResponse: Option[PropertyLastYearBookingCount] = Some(PropertyLastYearBookingCount(100))

  val recentBookingResponse: Option[PropertyRecentBooking] = Some(
    PropertyRecentBooking(
      latestBookings = Map(
        1001L -> BookingTimeStamp(1010, "AM")
      ),
      recentBookingWithSameOrigin = Map(
        1001L -> Some(BookingTimeStamp(7273, "AM"))
      )
    )
  )

  val priceStreamClientTest: PriceStreamHttpClientTest = new PriceStreamHttpClientTest {

    override def getPropertyMetaInfo(
        request: PropertyMetaInfoRequest,
        settings: RequestSettings
    ): Future[PropertyMetaInfoResponse] = {
      val propertyMetaInfo = PropertyMetaInfo(
        propertyId,
        hotelMetaRanking = request.featureFlags.hotelMetaRanking.flatMap(x => hotelMetaRankingResponse),
        lastYearBookingCount = request.featureFlags.lastYearBookingCount.flatMap(x => lastYearBookingCountResponse),
        recentBooking = request.featureFlags.recentBookingRoomLevel.flatMap(x => recentBookingResponse),
        propertyPriceSchema = request.featureFlags.propertyPriceSchema.flatMap(x =>
          Some(PropertyPriceSchema(PropertyPriceSchemaResponse(propertyId, 0.0)))
        )
      )

      Future.successful(PropertyMetaInfoResponse(Vector(propertyMetaInfo)))
    }

  }

  "PropertyMetaInfo" should {
    Table(
      (
        "hotelMetaRankingFF",
        "showLastYearBookingCountFF",
        "getRecentBookingWithSameOriginFF",
        "enableNewPriceSchemaFF"
      ),
      (true, false, false, false),
      (false, true, false, false),
      (false, false, true, false),
      (false, false, false, true),
      (true, true, true, true)
    ).foreach {
      case (
            hotelMetaRankingFF,
            showLastYearBookingCountFF,
            getRecentBookingWithSameOriginFF,
            enableNewPriceSchemaFF
          ) =>
        s"return requested features for hotelMetaRankingFF : $hotelMetaRankingFF,showLastYearBookingCountFF : $showLastYearBookingCountFF,getRecentBookingWithSameOriginFF : $getRecentBookingWithSameOriginFF,enableNewPriceSchemaFF : $enableNewPriceSchemaFF" in {
          val module = new MockModule {
            bind[PriceStreamHttpClient[Future]] to priceStreamClientTest
          }

          val request = aValidPropertyRequest
            .withHotelIds(List(propertyId))
            .withFeatureFlag(
              FeatureFlagRequest(
                hotelMetaRanking = Some(hotelMetaRankingFF),
                showLastYearBookingCount = Some(showLastYearBookingCountFF),
                getRecentBookingWithSameOrigin = Some(getRecentBookingWithSameOriginFF),
                enableNewPriceSchema = Some(enableNewPriceSchemaFF)
              )
            )
            .withSearchType(SearchTypes.Property)
            .build()

          val respF = executePropertySearch(request = request, module = module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            parseResponse(properties) shouldBe Some(
              List(
                hotelMetaRankingFF,
                showLastYearBookingCountFF,
                getRecentBookingWithSameOriginFF,
                enableNewPriceSchemaFF
              )
            )
          }
        }
    }
    "not return any features when No Feature Flags passed" in {
      val module = new MockModule {
        bind[PriceStreamHttpClient[Future]] to priceStreamClientTest
      }

      val request = aValidPropertyRequest
        .withHotelIds(List(propertyId))
        .withSearchType(SearchTypes.Property)
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.propertyMetaInfo shouldBe (None)

      }
    }

    "not return any features when negative experiment FUSION-4923 is B" in {
      val module = new MockModule {
        bind[PriceStreamHttpClient[Future]] to priceStreamClientTest
      }
      val request = aValidPropertyRequest
        .withHotelIds(List(propertyId))
        .withFeatureFlag(
          FeatureFlagRequest(
            hotelMetaRanking = Some(true),
            showLastYearBookingCount = Some(true),
            getRecentBookingWithSameOrigin = Some(true),
            enableNewPriceSchema = Some(true)
          )
        )
        .withSearchType(SearchTypes.Property)
        .withAdditionalExperimentForce(activeAbTest.pricestreamPropertyMetaInfoNegativeExp, "B")
        .build()

      val respF = executePropertySearch(request = request, module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.propertyMetaInfo shouldBe None
      }
    }
    "return features when negative experiment FUSION-4923 is A" in {

      val module = new MockModule {
        bind[PriceStreamHttpClient[Future]] to priceStreamClientTest
      }
      val request = aValidPropertyRequest
        .withHotelIds(List(propertyId))
        .withFeatureFlag(
          FeatureFlagRequest(
            hotelMetaRanking = Some(true),
            showLastYearBookingCount = Some(true),
            getRecentBookingWithSameOrigin = Some(true),
            enableNewPriceSchema = Some(true)
          )
        )
        .withSearchType(SearchTypes.Property)
        .withAdditionalExperimentForce(activeAbTest.pricestreamPropertyMetaInfoNegativeExp, "A")
        .build()

      val respF = executePropertySearch(request = request, module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        parseResponse(properties) shouldBe Some(
          List(
            true,
            true,
            true,
            true
          )
        )
      }
    }
  }

  def parseResponse(properties: Properties) =
    properties.property.head.propertyMetaInfo.map(x =>
      List(
        x.hotelMetaRanking.isDefined,
        x.lastYearBookingCount.isDefined,
        x.recentBooking.isDefined,
        x.propertyPriceSchema.isDefined
      )
    )

}
