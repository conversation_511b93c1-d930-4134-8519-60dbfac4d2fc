package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import api.request.FeatureFlag
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.papi.search.common.externaldependency.chunkable.DFService
import com.agoda.papi.search.common.framework.mock.DFServiceDefaultTest
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.request.PricingPropertiesRequestWrapper
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.mockito.ArgumentMatchers.{ any => mockitoAny }
import org.mockito.Mockito
import org.specs2.mock.mockito.ArgumentCapture

import scala.concurrent.Future
import scala.jdk.CollectionConverters.asScalaIterator

class CitySearchIgnoreReturnHotelNotReadyIfPullNotReadySpec extends PAPISearchFunctionalTest {

  private def executeTestCase(
      experimentVariant: Char
  ): Future[(HttpResponse, List[PricingPropertiesRequestWrapper])] = {
    val propertyIds      = Seq(1001L, 1002L, 1003L)
    val avail            = generateDefaultAvail(propertyIds.toList)
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds.toList)

    val spiedDFService = Mockito.spy(new DFServiceDefaultTest)

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      bind[DFService] to spiedDFService
    }

    val query = QueryBuilder.build(
      aValidSSRQuery,
      aValidCitySearchRequest,
      DFDataExample.aPricingRequestParameters.copy(
        pricing = DFDataExample.aPricingRequestParameters.pricing.copy(
          featureFlag = List(FeatureFlag.ReturnHotelNotReadyIfPullNotReady, FeatureFlag.EnableCashback)
        )
      ),
      contentSummaryRequest
    )

    val forceExpHeaders = s"FUSION-6248,$experimentVariant"
    val headers         = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, forceExpHeaders)

    val respF = executeSSRGraphQL(
      query,
      module,
      headers = headers
    )

    respF.map { resp =>
      val dfRequestCaptor = new ArgumentCapture[PricingPropertiesRequestWrapper]
      Mockito
        .verify(spiedDFService, Mockito.atLeast(1))
        .process(dfRequestCaptor.capture, mockitoAny(), mockitoAny())(mockitoAny())
      val capturedDFRequests = asScalaIterator(dfRequestCaptor.values.iterator()).toList
      resp -> capturedDFRequests
    }
  }

  private def fetchFeatureFlagsFromDFRequest(
      capturedDFRequests: List[PricingPropertiesRequestWrapper]
  ): List[FeatureFlag] =
    capturedDFRequests.flatMap { request =>
      val mainFeatureFlags = request.pricingRequest.pricingRequestParameters.t.pricing.featureFlag
      val overrideFeatureFlags = request.pricingRequest.overridePricingRequest
        .flatMap(_.additionalFeatureFlags)
        .getOrElse(List.empty)
      mainFeatureFlags ++ overrideFeatureFlags
    }

  s"[FUSION-6248] Ignore ReturnHotelNotReadyIfPullNotReady Flag Experiment" should {

    "A side should send ReturnHotelNotReadyIfPullNotReady flag to DF" in {
      val testResult = executeTestCase(experimentVariant = 'A')

      whenReady(testResult) { case (resp, capturedDFRequests) =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.properties.map(_.size shouldBe 3)

        // Verify that ReturnHotelNotReadyIfPullNotReady flag was sent to DF
        capturedDFRequests should not be empty
        val allFeatureFlags = fetchFeatureFlagsFromDFRequest(capturedDFRequests)
        allFeatureFlags should contain(FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
      }
    }

    "B side should filter out ReturnHotelNotReadyIfPullNotReady flag from DF request" in {
      val testResult = executeTestCase(experimentVariant = 'B')

      whenReady(testResult) { case (resp, capturedDFRequests) =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.properties.map(_.size shouldBe 3)

        // Verify that ReturnHotelNotReadyIfPullNotReady flag was filtered out from DF request
        capturedDFRequests should not be empty
        val allFeatureFlags = fetchFeatureFlagsFromDFRequest(capturedDFRequests)
        allFeatureFlags should not contain (FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
        allFeatureFlags should contain(FeatureFlag.EnableCashback)

      }
    }

  }

}
