package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.matrix.{ FilterKeys, MatrixGroupTypes }
import com.agoda.core.search.models.request.{ MatrixGroupRequest, RangeFilterDefinition, TupleStringDouble }
import com.agoda.core.search.models._
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidRectangleSearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class RectangleSearchStarRatingMatrixClaySpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {
  override def beforeAll(): Unit = mockEsBeforeAll()

  val propertyIds      = 1L to 6L
  val propertyMetadata = generateDefaultPropertyInfoList(propertyIds.toList)
  val avail            = generateDefaultAvail(propertyIds.toList)

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withStarRating(2.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withStarRating(2.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withStarRating(2.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .withStarRating(2.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .withStarRating(2.0)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel6)
      .withStarRating(2.0)
      .build()
  )

  "rectangle Search filter by star rating" should {

    "return matrix group result that contains the selected Rangefilter (FUSION-5339 = B)" in {
      val rectangleSearchRequest = buildRequestWithRangeFilter()

      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,B")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 2
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs List(
          TestMatrixItemResult(5, "", 0),
          TestMatrixItemResult(3, "", 0)
        )
      }
    }
    "miss matrix group result of the selected Rangefilter (FUSION-5339 = A)" in {
      val rectangleSearchRequest = buildRequestWithRangeFilter()

      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,A")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 0

      }
    }
    "return matrix group result that contains the selected Elasticfilter (FUSION-5339 = B)" in {
      val rectangleSearchRequest = buildRequestWithElasticFilter()

      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,B")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 2
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs List(
          TestMatrixItemResult(5, "", 0),
          TestMatrixItemResult(3, "", 0)
        )
      }
    }
    "miss matrix group result of the selected Elasticfilter (FUSION-5339 = A)" in {
      val rectangleSearchRequest = buildRequestWithElasticFilter()

      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,A")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 0

      }
    }

  }

  private def buildRequestWithRangeFilter() = {

    val filterRequest = FilterRequest(rangeFilters =
      Some(
        List(
          RangeFilterRequest(
            FilterKeys.StarRating,
            ranges = List(RangeFilterItem(5.0, Some(6.0)), RangeFilterItem(3.0, Some(4.0)))
          )
        )
      )
    )

    val rectangleSearchRequest = aValidRectangleSearchRequest
      .copy(searchRequest =
        aValidRectangleSearchRequest.searchRequest
          .withFilterRequest(filterRequest)
          .withMatrixGroup(Some(List(MatrixGroupRequest(MatrixGroupTypes.StarRating, Some(20)))))
          .build()
      )

    rectangleSearchRequest
  }

  private def buildRequestWithElasticFilter() = {

    val filterRequest = FilterRequest(elasticFilters =
      Some(
        ElasticFilterRequest(
          orFilters = Some(
            List(
              CompositeFilterRequest(
                List(
                  RangeFilterDefinition(
                    col = Fields.STARRATING,
                    tupleList = Some(List(TupleStringDouble("gte", 3.0), TupleStringDouble("lt", 4.0)))
                  ),
                  RangeFilterDefinition(
                    col = Fields.STARRATING,
                    tupleList = Some(List(TupleStringDouble("gte", 5.0), TupleStringDouble("lt", 6.0)))
                  )
                )
              )
            )
          )
        )
      )
    )

    val rectangleSearchRequest = aValidRectangleSearchRequest
      .copy(searchRequest =
        aValidRectangleSearchRequest.searchRequest
          .withFilterRequest(filterRequest)
          .withMatrixGroup(Some(List(MatrixGroupRequest(MatrixGroupTypes.StarRating, Some(20)))))
          .build()
      )

    rectangleSearchRequest
  }

}
