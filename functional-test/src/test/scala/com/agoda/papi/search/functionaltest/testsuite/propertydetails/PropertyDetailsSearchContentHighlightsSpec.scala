package com.agoda.papi.search.functionaltest.testsuite.propertydetails

import akka.http.scaladsl.model.StatusCodes
import com.agoda.common.graphql.model.Wrapper
import com.agoda.core.search.framework.builders.TestDataBuilders.{
  aValidContentHighlightsRequest,
  aValidPropertyDetailsRequest
}
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.util.ContentHighlightsDataExample
import com.agoda.papi.search.common.util.ContentHighlightsDataExample.{
  aValidHighLight,
  aValidHighlight,
  aValidHighlightInformation
}
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.{
  TestContentAtfPropertyHighlights,
  TestContentHighlightInformation,
  TestContentHighlights,
  TestContentPropertyHighlight
}
import com.agoda.property.search.framework.GqlMockModule

class PropertyDetailsSearchContentHighlightsSpec extends PAPISearchFunctionalTest {

  "Property Details Search" should {
    "return content highlights" in {
      val query = aValidPropertyDetailsSearchRequest
        .withPropertyDetailsRequest(aValidPropertyDetailsRequest.copy(propertyIds = List(1001L)))
        .withContentHighlights(aValidContentHighlightsRequest)
        .build()

      val module = new GqlMockModule {
        mockHighlightsExternalService(
          Map(
            1001L -> Wrapper(ContentWithPropertyId(1001L), ContentHighlightsDataExample.jsonContentHighlightsResponse)
          )
        )
      }

      val respF = executePropertyDetailsSearchGraphQL(query, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val propertyDetailsSearchResponse = responseToPropertyDetailsSearchResponse(resp)
        val propertyIds = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails.map(_.propertyId)
        val contentHighlights = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails
          .flatMap(_.contentDetail.flatMap(_.contentHighlights))
        propertyIds should contain theSameElementsAs List(1001L)
        contentHighlights should have size 1
        contentHighlights should contain theSameElementsAs List(
          TestContentHighlights(
            Some(Vector(TestContentPropertyHighlight(aValidHighlight.name))),
            Some(TestContentHighlightInformation(aValidHighlightInformation.title)),
            Some(Vector(TestContentAtfPropertyHighlights(aValidHighLight.name, aValidHighLight.topicName)))
          )
        )
      }
    }
  }

}
