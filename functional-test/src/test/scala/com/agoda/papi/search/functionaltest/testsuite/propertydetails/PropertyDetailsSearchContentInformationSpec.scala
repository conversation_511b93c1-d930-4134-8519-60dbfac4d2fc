package com.agoda.papi.search.functionaltest.testsuite.propertydetails

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.db.information.{ Description, InformationResponse, Notes }
import com.agoda.content.models.db.policy.HotelPolicies
import com.agoda.core.search.framework.builders.TestDataBuilders.{
  aValidContentInformationRequest,
  aValidPropertyDetailsRequest
}
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.{
  TestContentDescription,
  TestContentInformationResponse,
  TestHotelPolicies,
  TestNotes
}
import com.agoda.property.search.framework.GqlMockModule

class PropertyDetailsSearchContentInformationSpec extends PAPISearchFunctionalTest {
  // Migrate it/src/test/resources/features/property-details/IT-InformationGQL.feature

  "Property Details Search" should {

    "return ContentInformation of the hotel" in {
      val query = aValidPropertyDetailsSearchRequest
        .withPropertyDetailsRequest(aValidPropertyDetailsRequest.copy(propertyIds = List(1001L)))
        .withContentInformation(aValidContentInformationRequest)
        .build()
      val aValidInformationResponse = InformationResponse(
        propertyId = 1001L,
        description = Some(
          Description(
            long = Some("test_long_description"),
            short = Some("test_short_description")
          )
        ),
        usefulInfoGroups = Option(Vector()),
        notes = Option(
          Notes(Option(Vector("test_public_notes")), Option(Vector()), Option(Vector()), Option(Vector()))
        ),
        entertainment = Option(Vector()),
        numberOfRoom = None,
        policies = Option(
          HotelPolicies(
            None,
            Option(Vector()),
            Option(Vector()),
            Option(Vector("test_extra_bed_policy")),
            Option(Vector()),
            None
          )
        ),
        spokenLanguages = Option(Vector()),
        messaging = None,
        reception = None,
        blockedNationalities = Option(Vector()),
        contact = None,
        local = None,
        chainId = None,
        childrenStayFreeTypeId = Option(0),
        formerlyName = None,
        localizations = None,
        isAgodaVerified = None,
        certificate = None,
        staffVaccinationInfo = None,
        characteristicTopics = None,
        sustainabilityInfo = None,
        nightStayInfo = None,
        companyTraceabilityInfo = None,
        companyTraceabilityData = None,
        dsaComplianceInfo = None
      )
      val module = new GqlMockModule {
        mockInformationService(
          Map(
            1001L -> aValidInformationResponse
          )
        )
      }

      val respF = executePropertyDetailsSearchGraphQL(query, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val propertyDetailsSearchResponse = responseToPropertyDetailsSearchResponse(resp)
        val propertyIds = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails.map(_.propertyId)
        val contentInformation = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails
          .flatMap(_.contentDetail.flatMap(_.contentInformation))
        propertyIds should contain theSameElementsAs List(1001L)
        contentInformation shouldBe List(
          TestContentInformationResponse(
            propertyId = 1001L,
            description = Some( // Verify has Description
              TestContentDescription(
                long = aValidInformationResponse.description.get.long,
                short = aValidInformationResponse.description.get.short
              )
            ),
            policies = Some(
              TestHotelPolicies( // verify has Policies
                extraBed = aValidInformationResponse.policies.get.extraBed
              )
            ),
            notes = Some(
              TestNotes( // verify has Notes
                publicNotes = aValidInformationResponse.notes.get.publicNotes
              )
            ),
            companyTraceabilityData = None
          )
        )

      }
    }
  }

}
