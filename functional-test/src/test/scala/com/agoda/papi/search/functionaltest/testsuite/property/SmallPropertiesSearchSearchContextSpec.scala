package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.papi.search.common.service.RocketmilesRankingExperimentService
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.testsuite.common.CommonSearchContextForRestEndpointsTestSuit
import com.agoda.papi.search.property.service.StarfruitService
import framework.MockModule
import org.mockito.{ Mockito, MockitoSugar }
import org.scalatest.BeforeAndAfterEach
import org.specs2.mock.mockito.ArgumentCapture
import request._
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class SmallPropertiesSearchSearchContextSpec
    extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach
      with CommonSearchContextForRestEndpointsTestSuit {

  val rmService = mock[RocketmilesRankingExperimentService]
  when(rmService.isRankingExperimentEnabled(any)).thenReturn(false)

  val proeprtiesFromStarfruit = aValidProperties
    .build()

  val startfruitService = mock[StarfruitService]
  when(startfruitService.doSearch(any, any)).thenReturn(Future.successful(proeprtiesFromStarfruit))

  val dfHotel = aValidDFHotel
    .withHotelId(1001L)
    .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
    .build()

  val contentMasterRooms = aValidRoomInformationResponse
    .withHotelId(1001L)
    .withMasterRoomResponse(
      Vector(
        aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
      )
    )
    .build()

  val request = aValidPropertyRequest
    .withSearchType(SearchTypes.Property)
    .withHotelIds(List(1001L))
    .withPricing(aValidPricingRequest)
    .build()

  override def beforeEach(): Unit = {
    Mockito.clearInvocations(rmService)
    Mockito.clearInvocations(startfruitService)
  }

  "PropertySearch /api/smallProperties" should {

    s"not pass SearchContext to RocketmilesRankingExperimentService when A of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[RocketmilesRankingExperimentService] to rmService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "A")
        .build()

      val respF = executeSmallProperties(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK

        val captor = new ArgumentCapture[ContextHolder]()
        verify(rmService, times(1)).isRankingExperimentEnabled(captor.capture)
        captor.value.searchContext shouldBe empty
      }
    }

    s"pass SearchContext to RocketmilesRankingExperimentService when B of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[RocketmilesRankingExperimentService] to rmService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "B")
        .build()

      val respF = executeSmallProperties(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK

        val captor = new ArgumentCapture[ContextHolder]()
        verify(rmService, times(1)).isRankingExperimentEnabled(captor.capture)
        captor.value.searchContext shouldBe defined
      }
    }

    s"not pass SearchContext to starfruit service doSearchEnriched when A of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        bind[StarfruitService] to startfruitService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "A")
        .build()

      val respF = executeSmallProperties(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val smallProperties = responseToSmallProperties(resp)
        smallProperties.properties.map(_.hotelId) shouldBe proeprtiesFromStarfruit.property.map(_.propertyId)

        val captor = new ArgumentCapture[ContextHolder]()
        verify(startfruitService, times(1)).doSearch(any, captor.capture)
        captor.value.searchContext shouldBe empty
      }
    }

    s"pass SearchContext to starfruit service doSearchEnriched when B of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[StarfruitService] to startfruitService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "B")
        .build()

      val respF = executeSmallProperties(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val smallProperties = responseToSmallProperties(resp)
        smallProperties.properties.map(_.hotelId) shouldBe proeprtiesFromStarfruit.property.map(_.propertyId)

        val captor = new ArgumentCapture[ContextHolder]()
        verify(startfruitService, times(1)).doSearch(any, captor.capture)
        captor.value.searchContext shouldBe defined
      }
    }

    executeFxiTestCases(request, executeSmallProperties(_, _))
    executeSuggestPriceTestCases(request, executeSmallProperties(_, _))
  }

}
