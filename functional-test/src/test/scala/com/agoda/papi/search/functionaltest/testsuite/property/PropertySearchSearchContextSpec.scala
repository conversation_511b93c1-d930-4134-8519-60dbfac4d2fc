package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.papi.search.common.service.{ RocketmilesRankingExperimentService, WhiteLabelService }
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.testsuite.common.CommonSearchContextForRestEndpointsTestSuit
import com.agoda.papi.search.property.service.StarfruitService
import framework.MockModule
import org.mockito.{ Mockito, MockitoSugar }
import org.scalatest.BeforeAndAfterEach
import org.scalatest.prop.TableDrivenPropertyChecks
import org.specs2.mock.mockito.ArgumentCapture
import request._
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchSearchContextSpec
    extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach with TableDrivenPropertyChecks
      with CommonSearchContextForRestEndpointsTestSuit {

  val whitelabelService = mock[WhiteLabelService]
  when(whitelabelService.isValidWhiteLabelKey(any, any)).thenReturn(true)

  val rmService = mock[RocketmilesRankingExperimentService]
  when(rmService.isRankingExperimentEnabled(any)).thenReturn(false)

  val proeprtiesFromStarfruit = aValidProperties.build()

  val startfruitService = mock[StarfruitService]
  when(startfruitService.doSearchEnriched(any, any)).thenReturn(Future.successful(proeprtiesFromStarfruit))

  val dfHotel = aValidDFHotel
    .withHotelId(1001L)
    .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
    .build()

  val contentMasterRooms = aValidRoomInformationResponse
    .withHotelId(1001L)
    .withMasterRoomResponse(
      Vector(
        aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
      )
    )
    .build()

  val request = aValidPropertyRequest
    .withSearchType(SearchTypes.Property)
    .withHotelIds(List(1001L))
    .withPricing(aValidPricingRequest)
    .build()

  override def beforeEach(): Unit = {
    Mockito.clearInvocations(rmService)
    Mockito.clearInvocations(startfruitService)
    Mockito.clearInvocations(whitelabelService)
  }

  "PropertySearch /api/property" should {

    s"not pass SearchContext to RM service when A of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[RocketmilesRankingExperimentService] to rmService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "A")
        .build()

      val respF = executePropertySearch(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        val captor = new ArgumentCapture[ContextHolder]()
        verify(rmService, times(1)).isRankingExperimentEnabled(captor.capture)
        captor.value.searchContext shouldBe empty
      }
    }

    s"pass SearchContext to RM service when B of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[RocketmilesRankingExperimentService] to rmService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "B")
        .build()

      val respF = executePropertySearch(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        val roomIdentifiers =
          properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
        (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))

        val captor = new ArgumentCapture[ContextHolder]()
        verify(rmService, times(1)).isRankingExperimentEnabled(captor.capture)
        captor.value.searchContext shouldBe defined
      }
    }

    s"not pass SearchContext to starfruit service doSearchEnriched when A of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        bind[StarfruitService] to startfruitService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "A")
        .build()

      val respF = executePropertySearch(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties shouldBe proeprtiesFromStarfruit

        val captor = new ArgumentCapture[ContextHolder]()
        verify(startfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.searchContext shouldBe empty
      }
    }

    s"pass SearchContext to starfruit service doSearchEnriched when B of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[StarfruitService] to startfruitService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "B")
        .build()

      val respF = executePropertySearch(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties shouldBe proeprtiesFromStarfruit

        val captor = new ArgumentCapture[ContextHolder]()
        verify(startfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.searchContext shouldBe defined
      }
    }

    executeFxiTestCases(request, executePropertySearch(_, _))
    executeSuggestPriceTestCases(request, executePropertySearch(_, _))
  }

  "PropertySearch /api/v2/property" should {

    s"not pass SearchContext to starfruit service doSearchEnriched when A of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        bind[StarfruitService] to startfruitService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "A")
        .build()

      val respF = executePropertySearchV2(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties shouldBe proeprtiesFromStarfruit

        val captor = new ArgumentCapture[ContextHolder]()
        verify(startfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.searchContext shouldBe empty
      }
    }

    s"pass SearchContext to starfruit service doSearchEnriched when B of ${activeABTest.fixMissingSearchContextInRestEndpointsBug}" in {
      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        bind[StarfruitService] to startfruitService
      }

      val requestWithExperiment = request
        .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, "B")
        .build()

      val respF = executePropertySearchV2(request = requestWithExperiment, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties shouldBe proeprtiesFromStarfruit

        val captor = new ArgumentCapture[ContextHolder]()
        verify(startfruitService, times(1)).doSearchEnriched(any, captor.capture)
        captor.value.searchContext shouldBe defined
      }
    }

    executeFxiTestCases(request, executePropertySearchV2(_, _))
    executeSuggestPriceTestCases(request, executePropertySearchV2(_, _))
  }

}
