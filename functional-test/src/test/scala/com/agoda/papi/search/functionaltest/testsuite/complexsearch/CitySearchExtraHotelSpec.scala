package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.request.ExtraHotelsRequest
import com.agoda.core.search.models.response.AvailabilityData
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.DFDataExample.{
  aClientInfo,
  aContextRequest,
  aFeatureRequest,
  aValidPAPIHotelPricing,
  aValidPAPIHotelPricingMap,
  hotelWrapperA,
  mockRoomA
}
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.pricing.{ PAPIDayuseCheckInTime, PAPIDayuseInfo }
import com.agoda.papi.search.types.WithHotelIdMap
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.spapi.calculator.protobuf.cache.ota.common.PolicyChargeType.N
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchExtraHotelSpec extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()
  val hotels = List(hotel1)

  val meta: PropertyInfoList = PropertyInfoList(
    hotels.map(hotel => aValidCachePropertyInfo.withHotelId(hotel).withHourlyProviderIdList(Seq(332)))
  )

  val avail: WithHotelIdMap[AvailabilityData] = WithHotelIdMap(
    hotel1 -> aValidAvailabilityData.withHotelId(hotel1).withSupplierInfo(List(aValidSupplierInfo)).build()
  )

  "City searches" should {
    "return dayuseInfo when showCheapestHourlyRate is true" in {
      val citySearchRequest = aValidCitySearchRequest.copy(
        searchRequest = aValidCitySearchRequest.searchRequest.copy(
          extraHotels = Option(
            ExtraHotelsRequest(
              extraHotelIds = List(hotel1)
            )
          )
        )
      )
      val pricingRequestParameter = DFDataExample.aPricingRequestParameters.copy(
        context = aContextRequest.copy(clientInfo = aClientInfo.copy(cid = 1)),
        pricing = DFDataExample.aPricingRequestParameters.pricing.copy(
          features = aFeatureRequest.copy(
            showCheapestHourlyRate = Option(true)
          )
        )
      )

      val query = QueryBuilder.build(
        aValidSSRQuery,
        citySearchRequest,
        pricingRequestParameter,
        contentSummaryRequest
      )

      val dayuseCheckInTimes = Seq(PAPIDayuseCheckInTime(10), PAPIDayuseCheckInTime(14))
      val dayuseInfo         = PAPIDayuseInfo(dayuseCheckInTimes)

      val module = new GqlMockModule {
        mockCachePropertyInfoList(meta)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockDfService(
          Map(
            hotel1 -> aValidPAPIHotelPricing(hotel1).copy(
              dayuseInfo = Some(dayuseInfo)
            )
          )
        )
      }

      val respF = executeSSRGraphQL(
        query,
        module
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        val properties         = citySearchResponse.data.citySearch.properties
        properties should not be empty
        properties match {
          case Some(properties) =>
            properties should not be empty
            properties.foreach(property => property.pricing.flatMap(_.dayuseInfo) should not be empty)
        }
      }
    }

    "should not return dayuseInfo when showCheapestHourlyRate is false" in {
      val citySearchRequest = aValidCitySearchRequest.copy(
        searchRequest = aValidCitySearchRequest.searchRequest.copy(
          extraHotels = Option(
            ExtraHotelsRequest(
              extraHotelIds = List(hotel1)
            )
          )
        )
      )
      val pricingRequestParameter = DFDataExample.aPricingRequestParameters.copy(
        context = aContextRequest.copy(clientInfo = aClientInfo.copy(cid = 1)),
        pricing = DFDataExample.aPricingRequestParameters.pricing.copy(
          features = aFeatureRequest.copy(
            showCheapestHourlyRate = Option(false)
          )
        )
      )

      val query = QueryBuilder.build(
        aValidSSRQuery,
        citySearchRequest,
        pricingRequestParameter,
        contentSummaryRequest
      )

      val dayuseCheckInTimes = Seq(PAPIDayuseCheckInTime(10), PAPIDayuseCheckInTime(14))
      val dayuseInfo         = PAPIDayuseInfo(dayuseCheckInTimes)

      val module = new GqlMockModule {
        mockCachePropertyInfoList(meta)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockDfService(Map(hotel1 -> aValidPAPIHotelPricing(hotel1)))
      }

      val respF = executeSSRGraphQL(
        query,
        module
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        val properties         = citySearchResponse.data.citySearch.properties
        properties should not be empty
        properties match {
          case Some(properties) =>
            properties should not be empty
            properties.foreach(property => property.pricing.flatMap(_.dayuseInfo) shouldBe empty)
        }
      }
    }
  }

}
