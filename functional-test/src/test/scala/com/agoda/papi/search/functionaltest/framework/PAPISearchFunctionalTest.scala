package com.agoda.papi.search.functionaltest.framework

import akka.actor.ActorSystem
import akka.http.scaladsl.model._
import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.server.{ Directive0, MalformedRequestContentRejection, RejectionHandler }
import akka.http.scaladsl.server.Directives.{ complete, handleRejections, withSizeLimit }
import akka.http.scaladsl.testkit.{ RouteTestTimeout, ScalatestRouteTest }
import com.agoda.common.graphql.model.GQLSerializer._
import com.agoda.common.graphql.model.Query
import com.agoda.commons.http.server.models.{ RequestContext => AgRequestContext }
import com.agoda.core.search.framework.TestDataGenerator
import com.agoda.core.search.models.CancellationPoliciesResult
import com.agoda.papi.search.api.http.HttpRoutes
import com.agoda.papi.search.api.util.directive.RequestDecoderDirective
import com.agoda.papi.search.functionaltest.framework.model.TestSerialization._
import com.agoda.papi.search.functionaltest.framework.model._
import com.typesafe.config.ConfigFactory
import io.circe.parser._
import io.circe.syntax._
import metaapi._
import org.mockito.{ ArgumentMatchersSugar, MockitoSugar }
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.time.{ Seconds, Span }
import org.scalatest.wordspec.AnyWordSpec
import request.{ CancellationPolicyRequest, PropertyRecommendationRequest, PropertyRequest }
import sangria.marshalling.circe.CirceInputUnmarshaller
import sangria.schema.Schema
import scaldi.{ Injectable, Injector, Module }
import serializer.Serialization
import transformers.{ Properties, PropertiesRecommendationResponse, SmallProperties }

import java.io.File
import scala.concurrent.duration._
import scala.concurrent.{ Future, Promise }

trait PAPISearchFunctionalTest
    extends AnyWordSpec with Matchers with ScalatestRouteTest with TestDataGenerator with Injectable with TestData
      with MockitoSugar with ArgumentMatchersSugar with ScalaFutures {

  /** Why don't we use AsyncWordSpec? The answer is that AsyncWordSpec doesn't allow us to set a global timeout while
    * waiting for the Future to complete.
    */

  implicit val defaultPatience           = PatienceConfig(timeout = Span(10, Seconds))
  implicit val timeout: RouteTestTimeout = RouteTestTimeout(10 seconds)

  private val maxContentLength = ConfigFactory
    .parseFile(new File("src/main/resources/config/application/graphql-settings.conf"))
    .getInt("server.maxRequestSize") * 1000

  def injectionTest[T](module: Injector)(testFunc: Injector => T): T = {
    val functionalTestModule =
      module ::
        new Module {
          bind[ActorSystem] to system
        }
    val appInjector = PAPISearchFunctionalTestModule.createInjector(functionalTestModule)
    testFunc(appInjector)
  }

  def executeSSRGraphQL(
      query: Query,
      module: Module,
      headers: Seq[HttpHeader] = aValidSSRHttpHeaders,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/graphql/search")
      .withEntity(ContentTypes.`application/json`, query.asJson.toString())
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePropertyAllotmentSearchGraphQL(
      query: Query,
      module: Module,
      headers: Seq[HttpHeader] = aValidSSRHttpHeaders,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/graphql/search")
      .withEntity(ContentTypes.`application/json`, query.asJson.toString())
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePropertyDetailsSearchGraphQL(
      query: Query,
      module: Module,
      headers: Seq[HttpHeader] = aValidSSRHttpHeaders,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/graphql/property")
      .withEntity(ContentTypes.`application/json`, query.asJson.toString())
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePropertySummarySearchGraphQL(
      query: Query,
      module: Module,
      headers: Seq[HttpHeader] = aValidSSRHttpHeaders,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/graphql/propertySummary")
      .withEntity(ContentTypes.`application/json`, query.asJson.toString())
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePropertySearch(
      request: PropertyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/property")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePropertySearchV2(
      request: PropertyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val withRequiredHeaders: Seq[HttpHeader] = headers ++ Seq(
      RawHeader("AG-CALLER-APP-NAME", "test"),
      RawHeader("AG-CALLER-VERSION", "1.0.0")
    )

    val httpMessage = Post("/api/v2/property")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(withRequiredHeaders: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeProperties(
      request: PropertyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/properties")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeSmallProperties(
      request: PropertyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/smallProperties")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePriceStreamMetaLab(
      request: MetaLabRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/pricestream/metalab")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeCancellationSearch(
      request: CancellationPolicyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/cancellation")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeBookingEndpoint(
      request: PropertyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/booking")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executePriceStreamPriceTrend(
      request: PriceTrendByPropertyRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/pricestream/pricetrend")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeRecommendationSearch(
      request: PropertyRecommendationRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/recommendation")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeGraphQLSchemaEndpoint(module: Module): Future[HttpResponse] =
    executeAkkaTestKit(Get("/graphql"), module)

  def withSizeLimitHandling: Directive0 = {
    val errorMessage: String = "Request entity too large"
    val rejectionHandler = RejectionHandler.newBuilder
      .handle {
        case MalformedRequestContentRejection(msg, _) if msg.contains("exceeded size limit") =>
          complete(StatusCodes.PayloadTooLarge -> errorMessage)
      }
      .result()
    handleRejections(rejectionHandler) & RequestDecoderDirective.safeDecodeRequest(maxContentLength)
  }

  def executeAkkaTestKit(
      httpRequest: HttpRequest,
      module: Module,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val respP = Promise[HttpResponse]()
    injectionTest(module) { implicit injector =>
      val httpRoutes = inject[HttpRoutes]
      httpRequest ~> withSizeLimitHandling(httpRoutes.route(requestContext)) ~> check {
        respP.success(response)
      }
    }
    respP.future
  }

  def responseToProperties(httpResponse: HttpResponse): Properties = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val properties = Serialization.jacksonMapper.readValue[Properties](jsonString)
    properties
  }

  def responseToSmallProperties(httpResponse: HttpResponse): SmallProperties = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val properties = Serialization.jacksonMapper.readValue[SmallProperties](jsonString)
    properties
  }

  def responseToPropertyAttributes(httpResponse: HttpResponse): TestPropertiesAttributesResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val properties = Serialization.jacksonMapper.readValue[TestPropertiesAttributesResponse](jsonString)
    properties
  }

  def responseToCancellation(httpResponse: HttpResponse): CancellationPoliciesResult = {
    val jsonString                 = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val cancellationPoliciesResult = Serialization.jacksonMapper.readValue[CancellationPoliciesResult](jsonString)
    cancellationPoliciesResult
  }

  def responseToCitySearchResponse(httpResponse: HttpResponse): TestCitySearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestCitySearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToAreaSearchResponse(httpResponse: HttpResponse): TestAreaSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestAreaSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToLandmarkSearchResponse(httpResponse: HttpResponse): TestLandmarkSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestLandmarkSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToRadiusSearchResponse(httpResponse: HttpResponse): TestRadiusSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestRadiusSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToRectangleSearchResponse(httpResponse: HttpResponse): TestRectangleSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestRectangleSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToRegionSearchResponse(httpResponse: HttpResponse): TestRegionSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestRegionSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToPropertyDetailsSearchResponse(httpResponse: HttpResponse): TestPropertyDetailsSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestPropertyDetailsSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToPropertySummarySearchResponse(httpResponse: HttpResponse): TestPropertySummarySearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestPropertySummarySearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToTopAreaSearchResponse(httpResponse: HttpResponse): TestTopAreaSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestTopAreaSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToPropertyAllotmentSearchResponse(httpResponse: HttpResponse): TestPropertyAllotmentSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestPropertyAllotmentSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToCityCenterSearchResponse(httpResponse: HttpResponse): TestCityCenterSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestCityCenterSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToStateSearchResponse(httpResponse: HttpResponse): TestStateSearchResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestStateSearchResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToPriceTrendResponse(httpResponse: HttpResponse): TestPriceTrendResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = decode[TestPriceTrendResponse](jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => value
    }
  }

  def responseToSchema(httpResponse: HttpResponse): Schema[Any, Any] = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val resp       = parse(jsonString)
    resp match {
      case Left(err)    => throw err
      case Right(value) => Schema.buildFromIntrospection(value)
    }
  }

  def executeCityPriceTrend(
      request: PriceTrendByCityRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/pricestream/citytrend")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def executeHotelMetaRanking(
      request: HotelMetaRankingRequest,
      module: Module,
      headers: Seq[HttpHeader] = Seq.empty,
      requestContext: AgRequestContext = AgRequestContext.default()
  ): Future[HttpResponse] = {
    val httpMessage = Post("/api/pricestream/hotelmetaranking")
      .withEntity(ContentTypes.`application/json`, Serialization.jacksonMapper.writeValueAsString(request))
      .withHeaders(headers: _*)
    executeAkkaTestKit(httpMessage, module, requestContext)
  }

  def responseToCityPriceTrend(httpResponse: HttpResponse): CityTrend = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val priceTrend = Serialization.jacksonMapper.readValue[CityTrend](jsonString)
    priceTrend
  }

  def responseToHotelMetaRanking(httpResponse: HttpResponse): HotelMetaRankingResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val response   = Serialization.jacksonMapper.readValue[HotelMetaRankingResponse](jsonString)
    response
  }

  def responseToRecommendations(httpResponse: HttpResponse): PropertiesRecommendationResponse = {
    val jsonString = httpResponse.entity.asInstanceOf[HttpEntity.Strict].data.utf8String
    val response   = Serialization.jacksonMapper.readValue[PropertiesRecommendationResponse](jsonString)
    response
  }

}
