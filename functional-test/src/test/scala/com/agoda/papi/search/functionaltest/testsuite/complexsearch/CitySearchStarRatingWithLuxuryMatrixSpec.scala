package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models
import com.agoda.core.search.models.enumeration.matrix.MatrixGroupTypes
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchStarRatingWithLuxuryMatrixSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withOpsStarRatingWithLuxury(Seq(3))
      .withOpsStarRatingWithLuxuryNewDef(Seq(3))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withOpsStarRatingWithLuxury(Seq(4, 101))
      .withOpsStarRatingWithLuxuryNewDef(Seq(4))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withOpsStarRatingWithLuxury(Seq(5, 101))
      .withOpsStarRatingWithLuxuryNewDef(Seq(5, 101))
      .build()
  )

  val hotelIds         = (hotel1 to hotel3).toList
  val avail            = generateDefaultAvail(hotelIds)
  val propertyMetadata = generateDefaultPropertyInfoList(hotelIds)

  "GraphQL Schema" should {
    "Return aggregation result for StarRatingWithLuxury new definition (FLEX-1353 = B)" in {
      val matrixGroupRequest = Option(
        List(
          models.request.MatrixGroupRequest(MatrixGroupTypes.StarRatingWithLuxury, Some(10))
        )
      )
      val query = QueryBuilder.build(
        aValidSSRQuery,
        aValidCitySearchRequest.withMatrixGroup(matrixGroupRequest),
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val headers =
        aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "NHAFE-986,B,FLEX-1353,B")

      val mockModule = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)
      }
      val result = executeSSRGraphQL(
        query,
        mockModule,
        headers
      )

      whenReady(result) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)

        val matrixGroupResults = citySearchResponse.data.citySearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 4
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs List(
          TestMatrixItemResult(101, "CMSId:350972", 1),
          TestMatrixItemResult(3, "", 1),
          TestMatrixItemResult(4, "", 1),
          TestMatrixItemResult(5, "", 1)
        )
      }
    }

    "Return aggregation result for StarRatingWithLuxury" in {
      val matrixGroupRequest = Option(
        List(
          models.request.MatrixGroupRequest(MatrixGroupTypes.StarRatingWithLuxury, Some(10))
        )
      )
      val query = QueryBuilder.build(
        aValidSSRQuery,
        aValidCitySearchRequest.withMatrixGroup(matrixGroupRequest),
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "NHAFE-986,B")

      val mockModule = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)
      }
      val result = executeSSRGraphQL(
        query,
        mockModule,
        headers
      )

      whenReady(result) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)

        val matrixGroupResults = citySearchResponse.data.citySearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 4
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs List(
          TestMatrixItemResult(101, "CMSId:350972", 2),
          TestMatrixItemResult(3, "", 1),
          TestMatrixItemResult(4, "", 1),
          TestMatrixItemResult(5, "", 1)
        )
      }
    }
  }

}
