package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.HttpResponse
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.request.{ MatrixGroupRequest, Page }
import com.agoda.core.search.models.enumeration.matrix.MatrixGroupTypes
import com.agoda.papi.search.common.util.ContentDataExample._
import com.agoda.papi.search.common.util.CoreDataExamples.{ aValidCitySearchRequest, aValidSearchContext }
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixGroupResult
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.core.search.framework.dexsync.ElasticHotelInfo
import com.agoda.papi.search.types.HotelIdType
import com.agoda.property.search.framework.GqlMockModule
import constant.BeachAccessType
import org.joda.time.DateTime
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

// Migrate CitySearch-BeachAccessPopularFilterMatrix-Clay
class CitySearchBeachAccessPopularFilterMatrixSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  val hotelIds = List(hotel1, hotel2, hotel3, hotel4, hotel5)

  override val docs: List[ElasticHotelInfo] = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withBeachAccessType(List(BeachAccessType.PublicBeach2Km))
      .withFacilities(List(15, 80, 116, 93)) // Non-smoking, Car park, Front desk [24-hour], Swimming pool
      .withHotelAccomodation(List(29))       // Apartment/Flat
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withBeachAccessType(List(BeachAccessType.PublicBeach2Km))
      .withFacilities(List(15, 80, 90)) // Non-smoking, Car park, Internet
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withBeachAccessType(List(BeachAccessType.PublicBeach2Km))
      .withFacilities(List(15, 91)) // Non-smoking, Spa/sauna
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .withFacilities(List(15, 91)) // Non-smoking, Spa/sauna
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .build()
  )

  override def afterEach(): Unit = {
    super.afterEach()
    client.execute {
      bulk(
        docs.map(hotelInfo => indexInto(testIndex).source(hotelInfo).id(hotelInfo.hotelId.toString))
      )
    }.await

    refresh(testIndex)
  }

  private def executeTestCase(
      cityId: Int,
      additionalMatrixGroups: List[MatrixGroupRequest] = List.empty,
      hotelIds: List[HotelIdType] = hotelIds
  ): Future[HttpResponse] = {
    val avail            = generateDefaultAvail(hotelIds)
    val propertyInfoList = generateDefaultPropertyInfoList(hotelIds)

    val defaultMatrixGroups = List(
      MatrixGroupRequest(MatrixGroupTypes.HotelFacilities, Some(15)),
      MatrixGroupRequest(MatrixGroupTypes.StarRating, Some(20)),
      MatrixGroupRequest(MatrixGroupTypes.HotelAreaId, Some(70)),
      MatrixGroupRequest(MatrixGroupTypes.AccommodationType, Some(20))
    )
    val searchCriteria = aValidSearchCriteria
      .withCheckIn(DateTime.parse("2018-01-31T00:00:00.0000+07:00"))
      .withBookingDate(DateTime.parse("2018-01-25T11:10:26.0000+07:00"))
      .withAdults(2)
      .withChildren(0)
      .withRooms(1)
      .withLos(1)
      .build()

    val citySearchRequest = aValidCitySearchRequest
      .withCityId(cityId.toLong)
      .withSearchContext(aValidSearchContext.copy(showCMS = Some(true)))
      .withSearchCriteria(searchCriteria)
      .withPage(Some(Page(pageSize = 45, pageNumber = 1)))
      .withMatrixGroup(Some(defaultMatrixGroups ++ additionalMatrixGroups))
      .build()

    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyInfoList)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockESMatrixService(client)
    }

    executeSSRGraphQL(query, module)
  }

  private def parseMatrixResponse(resp: HttpResponse): List[TestMatrixGroupResult] = {
    val citySearchResponse = responseToCitySearchResponse(resp)
    val matrixGroupResults = citySearchResponse.data.citySearch.aggregation.get.matrixGroupResults

    matrixGroupResults
  }

  "TC1, popular filters returned from city search which is city has landmark = 5, show in popular filter" should {
    "return beach access filter with public beach when city has beach access" in {
      val matrixGroups = List(
        MatrixGroupRequest(MatrixGroupTypes.BeachAccessTypeIds, Some(20))
      )

      val respF = executeTestCase(
        cityId = 1692,
        additionalMatrixGroups = matrixGroups
      )

      whenReady(respF) { resp =>
        val matrixResults = parseMatrixResponse(resp)

        // Verify BeachAccessTypeIds matrix is present
        val beachAccessMatrix = matrixResults.find(_.matrixGroup == MatrixGroupTypes.BeachAccessTypeIds.key)
        beachAccessMatrix should be(defined)

        val beachAccessItems = beachAccessMatrix.get.matrixItemResults
        val publicBeachItem  = beachAccessItems.find(_.id == BeachAccessType.PublicBeach2Km)
        publicBeachItem should be(defined)
        publicBeachItem.get.count shouldBe 3

        // Verify HotelFacilities matrix has expected items with exact counts
        val hotelFacilitiesMatrix = matrixResults.find(_.matrixGroup == (MatrixGroupTypes.HotelFacilities.key))
        hotelFacilitiesMatrix should be(defined)

        val facilitiesItems = hotelFacilitiesMatrix.get.matrixItemResults

        // Non-smoking facility should be in 4 hotels (hotel1, hotel2, hotel3, hotel4)
        val nonSmokingItem = facilitiesItems.find(_.id == 15)
        nonSmokingItem should be(defined)
        nonSmokingItem.get.count shouldBe 4

        // Car park should be in 2 hotels (hotel1, hotel2)
        val carParkItem = facilitiesItems.find(_.id == 80)
        carParkItem should be(defined)
        carParkItem.get.count shouldBe 2

        // Front desk should be in 1 hotel (hotel1)
        val frontDeskItem = facilitiesItems.find(_.id == 116)
        frontDeskItem should be(defined)
        frontDeskItem.get.count shouldBe 1 // Exact count from Cucumber test

        // Verify AccommodationType matrix
        val accommodationMatrix = matrixResults.find(_.matrixGroup == MatrixGroupTypes.AccommodationType.key)
        accommodationMatrix should be(defined)

        val accommodationItems = accommodationMatrix.get.matrixItemResults
        // Apartment/Flat should be in 1 hotel (hotel1)
        val apartmentItem = accommodationItems.find(_.id == 29)
        apartmentItem should be(defined)
        apartmentItem.get.count shouldBe 1
      }
    }
  }

  "TC2, Search with the city that not has beach, not show in popular filter" should {
    "return empty beach access matrix when city has no beach access" in {
      // Override the elasticsearch data temporarily for this test
      val noBeachDocs = List(
        aValidElasticHotelInfo
          .withHotelId(hotel1)
          .withCityId(4310)
          .withFacilities(List(90)) // Internet only, no beach access
          .build(),
        aValidElasticHotelInfo
          .withHotelId(hotel2)
          .withCityId(4310)
          .withFacilities(List(91)) // Spa/sauna only, no beach access
          .build()
      )

      // Setup ES with no beach access data
      client.execute {
        bulk(
          noBeachDocs.map(hotelInfo => indexInto(testIndex).source(hotelInfo).id(hotelInfo.hotelId.toString))
        )
      }.await

      refresh(testIndex)

      val matrixGroups = List(
        MatrixGroupRequest(MatrixGroupTypes.BeachAccessTypeIds, Some(20))
      )

      val noBeachHotelIds = noBeachDocs.map(_.hotelId)

      val respF = executeTestCase(
        cityId = 4310,
        additionalMatrixGroups = matrixGroups,
        hotelIds = noBeachHotelIds
      )

      whenReady(respF) { resp =>
        val matrixResults = parseMatrixResponse(resp)

        // Verify BeachAccessTypeIds matrix is either empty or not present
        val beachAccessMatrix = matrixResults.find(_.matrixGroup == MatrixGroupTypes.BeachAccessTypeIds.key)
        beachAccessMatrix should be(defined)
        beachAccessMatrix.get.matrixItemResults should be(empty)
      }
    }
  }

  "TC3,Search with the city that has hotel(s) with multiple range of location score(7+,8+,9+) ,but not request location score matrix" should {
    "return hotel facilities matrix without location score matrix" in {

      val respF = executeTestCase(
        cityId = 1692
      )

      whenReady(respF) { resp =>
        val matrixResults = parseMatrixResponse(resp)

        // Verify HotelFacilities matrix is present
        val hotelFacilitiesMatrix = matrixResults.find(_.matrixGroup == "HotelFacilities")
        hotelFacilitiesMatrix should be(defined)

        val facilitiesItems = hotelFacilitiesMatrix.get.matrixItemResults

        // Verify specific facilities from Cucumber test with exact counts
        // Swimming pool should be in 1 hotel
        val swimmingPoolItem = facilitiesItems.find(_.id == 93)
        swimmingPoolItem should be(defined)
        swimmingPoolItem.get.count shouldBe 1

        // Internet should be in 1 hotel
        val internetItem = facilitiesItems.find(_.id == 90)
        internetItem should be(defined)
        internetItem.get.count shouldBe 1

        // Spa/sauna should be in 2 hotels
        val spaSaunaItem = facilitiesItems.find(_.id == 91)
        spaSaunaItem should be(defined)
        spaSaunaItem.get.count shouldBe 2

        // Verify no location score matrix is present
        val locationScoreMatrix = matrixResults.find(_.matrixGroup == "ReviewLocationScore")
        locationScoreMatrix should not be defined
      }
    }
  }

}
