package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.mocks.client.MockJarvisService
import com.agoda.core.search.models.request.RocketmilesRequestV2
import com.agoda.papi.search.common.service.RocketmilesRankingExperimentService.CITI_SITE_SLUG
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.DFDataExample.{ aValidExternalLoyaltyDisplay, aValidPAPIHotelPricing }
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.complexsearch.service.rocketmiles.RocketmilesRankingService
import com.agoda.papi.search.complexsearch.sorting.jarvis.JarvisService
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.RocketmilesRankingClient
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.model.RocketmilesRankingResponse
import com.agoda.papi.search.complexsearch.sorting.rocketmilesranking.util.RocketmilesRankingExpUtils
import com.agoda.papi.search.functionaltest.framework.model.{ TestHotelPricing, TestPropertySummary }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.pricing.PAPIHotelPricing
import com.agoda.papi.search.types.{ HotelIdType, RankingType }
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.property.search.framework.binding.module.RocketmilesRankingServiceNoOp
import com.agoda.property.search.utils.RocketmilesDataExample
import com.agoda.property.search.utils.builders.GqlTestDataBuilders._
import models.starfruit.ExternalLoyaltyDisplay
import org.mockito.{ ArgumentCaptor, Mockito, MockitoSugar }
import org.scalatest.BeforeAndAfterEach
import org.scalatest.prop.TableDrivenPropertyChecks

import scala.concurrent.Future

class CitySearchCitiRocketmilesRankingSpec
    extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach with TableDrivenPropertyChecks {

  val h1 = 101L
  val h2 = 102L
  val h3 = 103L
  val h4 = 104L
  val h5 = 105L
  val h6 = 106L
  val h7 = 107L

  val properties = Seq(h1, h2, h3, h4, h5, h6, h7)

  val mockRocketmilesRankingResponse = RocketmilesRankingResponse(
    properties.zipWithIndex.map(x => x._1.toString -> (x._2 + 1.0f)).toMap
  )

  val abTest                   = ActiveABTests()
  val jarvisCitiRankingExpName = abTest.useJarvisForCitiRanking

  val mockRcoketmilesRankingExpUtilsEnable = mock[RocketmilesRankingExpUtils]
  when(mockRcoketmilesRankingExpUtilsEnable.isRankingExperimentEnabled(any, any)).thenReturn(true)

  val mockRcoketmilesRankingExpUtilsDisable = mock[RocketmilesRankingExpUtils]
  when(mockRcoketmilesRankingExpUtilsDisable.isRankingExperimentEnabled(any, any)).thenReturn(false)

  val citiRocketmilesRequest = RocketmilesDataExample.aRocketmilesRequestV2
    .withSiteSlug(CITI_SITE_SLUG)
    .build()

  val nonCitiRocketmilesRequest = RocketmilesDataExample.aRocketmilesRequestV2
    .withSiteSlug("rocketmile-non-citi")
    .build()

  val mockJarvisService             = spy(new MockJarvisService)
  val mockRocketMilesClient         = mock[RocketmilesRankingClient]
  val mockRocketmilesRankingService = spy(new RocketmilesRankingServiceNoOp)

  when(mockRocketMilesClient.getRocketmilesRankingResponse(any, any)(any))
    .thenReturn(Future.successful(mockRocketmilesRankingResponse))

  val luxuryCollectionDisplay = aValidExternalLoyaltyDisplay(isTierOne = true)
  val hotelCollectionDisplay  = aValidExternalLoyaltyDisplay(isTierZero = true)
  val bothCollectionDisplay   = aValidExternalLoyaltyDisplay(isTierOne = true, isTierZero = true)

  override def beforeEach(): Unit = {
    super.beforeEach()
    Mockito.clearInvocations(mockJarvisService)
    Mockito.clearInvocations(mockRocketMilesClient)
    Mockito.clearInvocations(mockRocketmilesRankingService)
  }

  s"[$jarvisCitiRankingExpName=B] ComplexSearch with Citi Rocketmile Request" should {
    s"Re-rank within PAPI after get result from Jarvis by push hotel without external loyalty display down" in {
      val jarvisResult = Map(
        h1 -> 99d, // LHC
        h2 -> 98d, // -
        h3 -> 97d, // LHC+HC
        h4 -> 96d, // HC
        h5 -> 95d, // -
        h6 -> 94d, // HC
        h7 -> 93d  // -
      )

      val mockDfMap: Map[HotelIdType, PAPIHotelPricing] = Map(
        createDfHotelPair(h1, Some(luxuryCollectionDisplay)),
        createDfHotelPair(h2, None),
        createDfHotelPair(h3, Some(bothCollectionDisplay)),
        createDfHotelPair(h4, Some(hotelCollectionDisplay)),
        createDfHotelPair(h5, None),
        createDfHotelPair(h6, Some(hotelCollectionDisplay)),
        createDfHotelPair(h7, None)
      )

      val expectedOrder = Seq(
        (h1, Some(luxuryCollectionDisplay.items.flatMap(_.tierName))), // LHC
        (h3, Some(bothCollectionDisplay.items.flatMap(_.tierName))),   // LHC+HC
        (h4, Some(hotelCollectionDisplay.items.flatMap(_.tierName))),  // HC
        (h6, Some(hotelCollectionDisplay.items.flatMap(_.tierName))),  // HC
        (h2, None),                                                    // -
        (h5, None),                                                    // -
        (h7, None)                                                     // -
      )

      val response = executeTestCase(
        propertyIds = properties,
        rocketMilesRequest = citiRocketmilesRequest,
        rocketmilesRankingExpUtils = mockRcoketmilesRankingExpUtilsEnable,
        citiExperimentVariant = 'B',
        mockRankingDate = jarvisResult,
        mockDfMap = mockDfMap
      )

      whenReady(response) { res =>
        res.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(res)
        // check result order
        val propertiesResult =
          citySearchResponse.data.citySearch.properties.get.map(p => (p.propertyId, extractTierNames(p)))
        (propertiesResult should contain).theSameElementsInOrderAs(expectedOrder)
      }
    }
  }

  /* helper */

  private def createDfHotelPair(hotelId: Long, externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay]) =
    hotelId -> aValidPAPIHotelPricing(99000L + hotelId)
      .withHotelId(hotelId)
      .withRocketmilesExternalLoyaltyDisplay(externalLoyaltyDisplay)
      .build()

  private def extractTierNames(property: TestPropertySummary): Option[Seq[String]] =
    property.pricing.flatMap { price =>
      price.externalLoyaltyDisplay.map(display => display.items.flatMap(item => item.tierName))
    }

  private def executeTestCase(
      propertyIds: Seq[Long],
      rocketMilesRequest: RocketmilesRequestV2,
      rocketmilesRankingExpUtils: RocketmilesRankingExpUtils,
      citiExperimentVariant: Char,
      mockRankingDate: Map[HotelIdType, RankingType] = Map.empty,
      mockDfMap: Map[HotelIdType, PAPIHotelPricing]
  ): Future[HttpResponse] = {
    val propertyInfoList     = generateDefaultPropertyInfoList(propertyIds.toList)
    val availabilityDataList = generateDefaultAvail(propertyIds.toList)

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyInfoList)
      mockPushAvailability(Future.successful(availabilityDataList, Set.empty))
      mockDfService(mockDfMap)
      bind[RocketmilesRankingExpUtils] to rocketmilesRankingExpUtils
      bind[RocketmilesRankingClient] to mockRocketMilesClient
      bind[RocketmilesRankingService] to mockRocketmilesRankingService
      bind[JarvisService] to new MockJarvisService(mockRankingDate)
    }
    val citySearchRequest = aValidCitySearchRequest
      .withSearchCriteria(aValidSearchCriteria.build())
      .withRocketmilesRequestV2(rocketMilesRequest)
      .build()
    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val forceExpHeaders =
      s"$jarvisCitiRankingExpName,$citiExperimentVariant"
    val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, forceExpHeaders)

    executeSSRGraphQL(query = query, module = module, headers = headers)
  }

}
