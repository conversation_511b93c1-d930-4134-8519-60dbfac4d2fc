package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.models.ProviderIds
import com.agoda.mockito.helper.MockitoHelper
import com.agoda.papi.enums.room.{ CancellationGroup, ExternalLoyaltyItem, ExternalLoyaltyItemType }
import com.agoda.papi.search.common.framework.mock.SupplierMetadataServiceTest
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.supplier.common.services.SupplierMetadataService
import framework.MockModule
import models.pricing.enums.CorTypeFlags
import models.starfruit._
import org.specs2.mock.Mockito
import transformers.EnrichedChildRoom
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PropertySearchRoomSortingSpec extends PAPISearchFunctionalTest with MockitoHelper {

  val activeAbTests = ActiveABTests()

  val mockCollectionRoom1 =
    mockRoom("mockCollectionRoom1", (90, 100), ExternalLoyaltyItem.TierZero, isFitCapacity = true)
  val mockCollectionRoom2 =
    mockRoom("mockCollectionRoom2", (110, 120), ExternalLoyaltyItem.TierOne, isFitCapacity = true)
  val mockCollectionRoom3 =
    mockRoom("mockCollectionRoom3", (110, 120), ExternalLoyaltyItem.TierOne, isFitCapacity = true, Option(90, 100))

  val mockCollectionRoom4 =
    mockRoom(
      "mockCollectionRoom4",
      (110, 120),
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(90, 100),
      isBreakfastIncluded = true
    )

  val mockCollectionRoom5 =
    mockRoom(
      "mockCollectionRoom5",
      (110, 120),
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(90, 100),
      isBreakfastIncluded = true,
      isFreeCancellation = true
    )

  val mockCollectionRoom6 =
    mockRoom(
      "mockCollectionRoom6",
      (110, 120),
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(90, 100),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true
    )

  val mockCollectionRoom7 = mockRoom("mockCollectionRoom7", (110, 120), ExternalLoyaltyItem.TierZero)
  val mockCollectionRoom8 = mockRoom("mockCollectionRoom8", (100, 110), ExternalLoyaltyItem.TierZero)

  val mockCollectionRoom9 =
    mockRoom(
      "mockCollectionRoom9",
      (100, 120),
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      None,
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true
    )

  val mockCollectionRoom10 =
    mockRoom(
      "mockCollectionRoom10",
      (110, 120),
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(80, 90),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true
    )

  val mockNonCollectionRoom1 = mockRoom("mockNonCollectionRoom1", (200, 210))

  val mockNonCollectionRoom2 =
    mockRoom(
      "mockNonCollectionRoom2",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom3 =
    mockRoom(
      "mockNonCollectionRoom3",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(180, 190),
      isFreeCancellation = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom4 =
    mockRoom(
      "mockNonCollectionRoom4",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(180, 190),
      isBreakfastIncluded = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom5 =
    mockRoom(
      "mockNonCollectionRoom5",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(180, 190),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom6 =
    mockRoom(
      "mockNonCollectionRoom6",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(180, 190),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom7 = mockRoom("mockNonCollectionRoom7", (220, 230))

  val mockNonCollectionRoom8 =
    mockRoom(
      "mockNonCollectionRoom8",
      (220, 230),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      None,
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom9 =
    mockRoom(
      "mockNonCollectionRoom9",
      (200, 210),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      isPayAtHotel = true
    )

  val mockNonCollectionRoom10 = mockRoom(
    "mockNonCollectionRoom10",
    (210, 220),
    ExternalLoyaltyItem.Unknown,
    isFitCapacity = true,
    Option(170, 180),
    isBreakfastIncluded = true,
    isFreeCancellation = true,
    isAgodaDirectSupply = true,
    isPayAtHotel = true
  )

  val rooms = List(
    /** Without collection */
    mockNonCollectionRoom1,
    mockNonCollectionRoom2,
    mockNonCollectionRoom3,
    mockNonCollectionRoom4,
    mockNonCollectionRoom5,
    mockNonCollectionRoom6,
    mockNonCollectionRoom7,
    mockNonCollectionRoom8,
    mockNonCollectionRoom9,
    mockNonCollectionRoom10,
    /** With collection */
    mockCollectionRoom1,
    mockCollectionRoom2,
    mockCollectionRoom3,
    mockCollectionRoom4,
    mockCollectionRoom5,
    mockCollectionRoom6,
    mockCollectionRoom7,
    mockCollectionRoom8,
    mockCollectionRoom9,
    mockCollectionRoom10
  )

  val childRooms1 =
    List(mockCollectionRoom3, mockCollectionRoom4, mockCollectionRoom10).map(_.copy(masterTypeId = 5001))
  val childRooms2 =
    List(mockNonCollectionRoom10, mockNonCollectionRoom5, mockNonCollectionRoom6).map(_.copy(masterTypeId = 5002))

  val multipleMasterRooms = childRooms2 ++ childRooms1

  "PropertySearch /api/property" should {
    "return 1 master room with children rooms sorted" in {
      val hotelId = aValidHotelId

      val dfHotel = aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(rooms)
        .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
        .build()

      val hotelResult = Seq(dfHotel)

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.Exclusive)))
        .build()

      val module = new MockModule {
        mockDFResponse(dfResult)
        bind[SupplierMetadataService] to new SupplierMetadataServiceTest
      }

      val respF = executePropertySearch(request = request, module = module)

      val expectedChildRooms = List(
        /** Fit with collection */
        "mockCollectionRoom10",
        "mockCollectionRoom3",
        "mockCollectionRoom4",
        "mockCollectionRoom5",
        "mockCollectionRoom6",
        "mockCollectionRoom1",
        "mockCollectionRoom9",
        "mockCollectionRoom2",

        /** Fit without collection */
        "mockNonCollectionRoom10", // 170, pay at hotel, meaningful (sort by price)
        "mockNonCollectionRoom6",  // 180, breakfast, free can, pay at hotel, meaningful (sort by price)
        "mockNonCollectionRoom5",  // 180, breakfast, free can, pay at hotel, meaningful (sort by price)
        "mockNonCollectionRoom4",  // 180, breakfast, pay at hotel, meaningful (sort by price)
        "mockNonCollectionRoom8",  // 220, breakfast, fee can, pay at hotel, meaningful (sort by price)
        "mockNonCollectionRoom3",  // 180, fee can, pay at hotel, not meaningful
        "mockNonCollectionRoom9",  // 200, pay at hotel, not meaningful
        "mockNonCollectionRoom2",  // 210, pay at hotel, not meaningful

        /** Unfit with collection */
        "mockCollectionRoom8",
        "mockCollectionRoom7",

        /** Unfit without collection */
        "mockNonCollectionRoom1",
        "mockNonCollectionRoom7"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1
        val ChildRooms = properties.property.head.masterRooms.head.childrenRooms
        assertChildrenRooms(ChildRooms, expectedChildRooms)
      }
    }

    "return master rooms with children rooms sorted" in {
      val hotelId = aValidHotelId

      val dfHotel = aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(multipleMasterRooms)
        .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
        .build()

      val hotelResult = Seq(dfHotel)

      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.Exclusive)))
        .build()

      val module = new MockModule {
        mockDFResponse(dfResult)
        bind[SupplierMetadataService] to new SupplierMetadataServiceTest
      }

      val respF = executePropertySearch(request = request, module = module)

      val expectedChildRooms = List(
        /* Master 5001 with collection*/
        "mockCollectionRoom10",
        "mockCollectionRoom3",
        "mockCollectionRoom4",
        /* Master 5002 without collection*/
        /** Fit without collection */
        "mockNonCollectionRoom10",
        "mockNonCollectionRoom6",
        "mockNonCollectionRoom5"
      )

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1
        val ChildRooms = properties.property.head.masterRooms.head.childrenRooms
        assertChildrenRooms(ChildRooms, expectedChildRooms)
      }
    }

    "[ROOMIE-1734=B] return minimum 2 expanded offers when only 1 meaningful offer exists" in {
      val hotelId = aValidHotelId

      val cheapestRoom =
        mockRoom("cheapestRoom", (100, 110), ExternalLoyaltyItem.Unknown, isFitCapacity = true) // meaningful offer
      val room2 = mockRoom("room2", (150, 160), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room3 = mockRoom("room3", (150, 160), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room4 = mockRoom("room4", (200, 210), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room5 = mockRoom("room5", (250, 260), ExternalLoyaltyItem.Unknown, isFitCapacity = true)

      val testRooms = List(cheapestRoom, room2, room3, room4, room5)

      val dfHotel = aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(testRooms)
        .withSuggestPriceType(Some(aValidAllInclusiveSuggestPriceType))
        .build()

      val hotelResult = Seq(dfHotel)
      val dfResult    = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .withAdditionalExperimentForce(activeAbTests.offerRankingV4, "B")
        .build()

      val module = new MockModule {
        mockDFResponse(dfResult)
        bind[SupplierMetadataService] to new SupplierMetadataServiceTest
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1
        val childRooms = properties.property.head.masterRooms.head.childrenRooms

        val expandedOffers = childRooms.filter(_.priority.contains(1.0))
        expandedOffers should have size 2

        childRooms.head.priority shouldBe Some(1.0) // cheapest
        childRooms(1).priority shouldBe Some(1.0)   // second cheapest to fill minimum
        childRooms(2).priority shouldBe Some(0.0)
        childRooms(3).priority shouldBe Some(0.0)
        childRooms(4).priority shouldBe Some(0.0)
      }
    }

    "[ROOMIE-1734=B] maintain existing behavior when already has 2+ meaningful offers" in {
      val hotelId = aValidHotelId

      val cheapestRoom = mockRoom("cheapestRoom", (100, 110), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val breakfastRoom = mockRoom(
        "breakfastRoom",
        (120, 130),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isBreakfastIncluded = true
      )
      val cancellationRoom = mockRoom(
        "cancellationRoom",
        (140, 150),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isFreeCancellation = true
      )
      val room4 = mockRoom("room4", (200, 210), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room5 = mockRoom("room5", (250, 260), ExternalLoyaltyItem.Unknown, isFitCapacity = true)

      val testRooms = List(cheapestRoom, breakfastRoom, cancellationRoom, room4, room5)

      val dfHotel = aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(testRooms)
        .withSuggestPriceType(Some(aValidAllInclusiveSuggestPriceType))
        .build()

      val hotelResult = Seq(dfHotel)
      val dfResult    = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val request = aValidPropertyRequest
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .build()

      val module = new MockModule {
        mockDFResponse(dfResult)
        bind[SupplierMetadataService] to new SupplierMetadataServiceTest
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1
        val childRooms = properties.property.head.masterRooms.head.childrenRooms

        val expandedOffers = childRooms.filter(_.priority.contains(1.0))
        expandedOffers.size shouldBe 3

        val cheapestOffer = childRooms.find(_.roomIdentifiers.contains("cheapestRoom"))
        cheapestOffer.get.priority shouldBe Some(1.0)

        val breakfastOffer = childRooms.find(_.roomIdentifiers.contains("breakfastRoom"))
        breakfastOffer.get.priority shouldBe Some(1.0)

        val cancellationOffer = childRooms.find(_.roomIdentifiers.contains("cancellationRoom"))
        cancellationOffer.get.priority shouldBe Some(1.0)

        val room4Offer = childRooms.find(_.roomIdentifiers.contains("room4"))
        room4Offer.get.priority shouldBe Some(0.0)

        val room5Offer = childRooms.find(_.roomIdentifiers.contains("room5"))
        room5Offer.get.priority shouldBe Some(0.0)
      }
    }

    "[ROOMIE-1734=A] return 1 offer when only 1 meaningful offer exists" in {
      val hotelId = aValidHotelId

      val cheapestRoom =
        mockRoom("cheapestRoom", (100, 110), ExternalLoyaltyItem.Unknown, isFitCapacity = true) // meaningful offer
      val room2 = mockRoom("room2", (150, 160), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room3 = mockRoom("room3", (150, 160), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room4 = mockRoom("room4", (200, 210), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
      val room5 = mockRoom("room5", (250, 260), ExternalLoyaltyItem.Unknown, isFitCapacity = true)

      val testRooms = List(cheapestRoom, room2, room3, room4, room5)

      val dfHotel = aValidDFHotel
        .withHotelId(hotelId)
        .withRooms(testRooms)
        .withSuggestPriceType(Some(aValidAllInclusiveSuggestPriceType))
        .build()

      val hotelResult = Seq(dfHotel)
      val dfResult    = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))

      val m = Mockito.mock[AllocationContext]
      when(m.offerRankingV4).thenReturn('A')

      val request = aValidPropertyRequest
        .withHotelIds(List(hotelId))
        .withPricing(aValidPricingRequest.withRequiredPrice(Some(SuggestedPrice.AllInclusive)))
        .withAdditionalExperimentForce(activeAbTests.offerRankingV4, 'A'.toString)
        .build()

      val module = new MockModule {
        mockDFResponse(dfResult)
        bind[SupplierMetadataService] to new SupplierMetadataServiceTest
      }

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp: HttpResponse =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)
        properties.property.head.masterRooms should have size 1
        val childRooms = properties.property.head.masterRooms.head.childrenRooms

        val expandedOffers = childRooms.filter(_.priority.contains(1.0))
        expandedOffers should have size 1

        val cheapestOffer = childRooms.find(_.roomIdentifiers.contains("cheapestRoom"))
        cheapestOffer.get.priority shouldBe Some(1.0)

        val room2Offer = childRooms.find(_.roomIdentifiers.contains("room2"))
        room2Offer.get.priority shouldBe Some(0.0)

        val room3Offer = childRooms.find(_.roomIdentifiers.contains("room3"))
        room3Offer.get.priority shouldBe Some(0.0)

        val room4Offer = childRooms.find(_.roomIdentifiers.contains("room4"))
        room4Offer.get.priority shouldBe Some(0.0)

        val room5Offer = childRooms.find(_.roomIdentifiers.contains("room5"))
        room5Offer.get.priority shouldBe Some(0.0)
      }
    }
  }

  private def assertChildrenRooms(ChildRooms: List[EnrichedChildRoom], expectedChildRooms: Seq[String]): Unit = {
    val roomIdentifiers = ChildRooms.flatMap(_.roomIdentifiers)
    roomIdentifiers should have size expectedChildRooms.size

    (roomIdentifiers should contain).theSameElementsInOrderAs(expectedChildRooms)
  }

  private def mockRoom(
      roomIdentifier: String,
      price: (Double, Double),
      loyaltyItem: ExternalLoyaltyItemType = ExternalLoyaltyItem.Unknown,
      isFitCapacity: Boolean = false,
      displayPriceAfterCashback: Option[(Double, Double)] = None,
      isBreakfastIncluded: Boolean = false,
      isFreeCancellation: Boolean = false,
      isAgodaDirectSupply: Boolean = false,
      isPayAtHotel: Boolean = false
  ): Room = {
    val payment = if (isFreeCancellation) {
      aValidDFPayment.copy(
        cancellation = Cancellation(
          cxlCode = "1D100P_100P",
          cmsMappedCancellationGroup = CancellationGroup.FreeCancellation
        ),
        payAtHotel = PayAtHotel(isPayAtHotel)
      )
    } else {
      aValidDFPayment.copy(payAtHotel = PayAtHotel(isPayAtHotel))
    }

    val subSupplierId = if (isAgodaDirectSupply) ProviderIds.Ycs else ProviderIds.Unknown
    val externalLoyaltyDisplay = Some(
      ExternalLoyaltyDisplay(
        Seq(ExternalLoyaltyDisplayItem(loyaltyItem, None, None))
      )
    )
    val cashback = aValidCashback.copy(showPostCashbackPrice = displayPriceAfterCashback.isDefined)
    aValidDFRoom3038
      .withRoomIdentifier(roomIdentifier)
      .withPricing(createDfPricingMap(price._1, price._2, "USD", displayPriceAfterCashback))
      .withCapacity(aValidCapacity.copy(numberOfGuestsWithoutRoom = if (isFitCapacity) 0 else 1))
      .withisBreakfastIncluded(isBreakfastIncluded)
      .withPayment(payment)
      .withSubSupplierID(subSupplierId)
      .withSupplierID(ProviderIds.Ycs)
      .withExternalLoyaltyDisplay(externalLoyaltyDisplay)
      .withCashback(cashback)
  }

  private def createDfPricingMap(
      exclusive: Double,
      allInclusive: Double,
      currency: String,
      displayPriceAfterCashback: Option[(Double, Double)]
  ): Map[String, Pricing] = {
    val displayPrice = DisplayPrice(exclusive, allInclusive)
    val displayBasis = DisplayBasis(displayPrice, displayPrice, displayPrice)
    val priceSummary = aValidDisplaySummary
      .withPerRoomPerNight(
        aValidSummaryElement.copy(
          displayAfterCashback = displayPriceAfterCashback match {
            case Some(price) =>
              Some(
                aValidDisplayPrice
                  .withExclusive(price._1)
                  .withAllInclusive(price._2)
                  .build()
              )
            case _ => None
          }
        )
      )
    val dfPricing = Pricing(displayBasis, displayBasis, Seq(), Seq(), CorTypeFlags.NoCOR, priceSummary)
    Map(currency -> dfPricing)
  }

}
