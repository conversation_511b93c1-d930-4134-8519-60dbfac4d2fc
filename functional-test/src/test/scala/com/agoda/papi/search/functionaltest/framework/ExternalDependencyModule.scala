package com.agoda.papi.search.functionaltest.framework

import com.agoda.commons.dynamic.state.State
import com.agoda.commons.dynamic.state.consul.{ ConsulStateWatcher, Environment }
import com.agoda.core.search.repository.LandmarkNameRepositoryTest
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.externaldependency.repository.{ CidMappingRepositoryAgsql, LanguageRepository }
import com.agoda.papi.search.complexsearch.configuration.PiiEncryptionSettings
import com.agoda.papi.search.complexsearch.externaldependency.repository.LandmarkNameRepository
import com.agoda.papi.search.functionaltest.framework.defaultmock.PAPIExperimentManagerLogicDefaultMock
import com.agoda.papi.search.internalmodel.configuration.{ PAPIMessageSettings, TopBookedCitiesSetting }
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.typesafe.config.{ Config, ConfigFactory }
import framework.repository.CidMappingRepositoryAgsqlTest
import framework.testservice.PriceStreamHttpClientTest
import scaldi.Module

import scala.concurrent.Future

class ExternalDependencyModule extends Module {
  // Define every external dependencies here
  bind[TopBookedCitiesSetting] to TopBookedCitiesSetting(cities = Set.empty)
  bind[PAPIMessageSettings] to PAPIMessageSettings(isRawRequestMessageEnabled = false, botTrafficSamplingRate = 0.0d)
  bind[PiiEncryptionSettings] to PiiEncryptionSettings(key = None)
  bind[CidMappingRepositoryAgsql] to new CidMappingRepositoryAgsqlTest()

  bind[PAPIExperimentManagerLogic] to new PAPIExperimentManagerLogicDefaultMock(
    languageRepository = inject[LanguageRepository],
    cidMappingRepositoryAgsql = inject[CidMappingRepositoryAgsql]
  )

  bind[PriceStreamHttpClient[Future]] to new PriceStreamHttpClientTest
  bind[LandmarkNameRepository] to new LandmarkNameRepositoryTest
  // TODO: Start implementing ExternalDependencyModule and TestExternalDependencyModule
  // Override alone might leak the real implementation of ext-dep to flow-test
  // Need scaldi to help us catch any missing binding in TestExternalDependencyModule

  // Must-Mock Mandatory External Dependencies
  // Couchbase PropertyInfoList
  // Repository that convert the search-criteria to a list of property-ids (CityInfoRepository, AreaInfoRepository, etc.)
  // DFSQL Push
  // DFSQL Pull
  // ElasticSearch
  // Sttp[T] for GraphQL schema

  // Mandatory External Dependencies
  // DF
  // Content
  // CMSRepository

  // Optional External Dependencies
  // Jarvis
  // RMRanking
  // Zenith
  // Price-Stream
  // PaymentAPI
  // Gandalf
  // NPC
  // Manual-Cal client
  // Optional Repositories

  // Config-related Dependencies
  // Config file
  // Consul K/V
  bind[State[Config]] to State.of(
    ConfigFactory.empty()
  )

}
