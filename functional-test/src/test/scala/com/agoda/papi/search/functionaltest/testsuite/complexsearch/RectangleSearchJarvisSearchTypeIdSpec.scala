package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.commons.http.client.v2.RequestSettings
import com.agoda.commons.logging.metrics.NoOpMetricsReporter
import com.agoda.core.search.framework.builders.TestDataBuilders.{ aValidESPropertyMetaData, toESPropertyMetadata }
import com.agoda.ml.ranking.jarvis.{ JarvisRequest, JarvisResponse }
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.framework.mock.LanguageRepositoryTest
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidRectangleSearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import com.agoda.papi.search.complexsearch.model.ESPropertyMetaData
import com.agoda.papi.search.complexsearch.sorting.jarvis.{ JarvisClient, JarvisService, JarvisServiceDefault }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.types.WithHotelIdMap
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.specs2.mock.mockito.ArgumentCapture

import scala.concurrent.Future

class RectangleSearchJarvisSearchTypeIdSpec extends PAPISearchFunctionalTest {

  private def executeTestCase(
      propertyIds: List[Long],
      mockJarvisClient: JarvisClient,
      forceExperiments: Map[String, String] = Map.empty
  ): Future[HttpResponse] = {
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)
    val avail            = generateDefaultAvail(propertyIds)
    val hotelPropertyMetaData: WithHotelIdMap[ESPropertyMetaData] = propertyIds.map { hotelId =>
      hotelId -> aValidESPropertyMetaData.withHotelId(hotelId).build()
    }(collection.breakOut)

    val query = QueryBuilder.build(
      aValidRectanglSearchQuery,
      aValidRectangleSearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val module = new GqlMockModule {
      bind[JarvisService] to new NopContextMetrics with JarvisServiceDefault {
        implicit override val languageRepository: LanguageRepository = new LanguageRepositoryTest
        override val jarvisClient                                    = mockJarvisClient
        override val requestSettings                                 = RequestSettings.default
        override val reportingService                                = NoOpMetricsReporter
      }
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockAggregationService(Future.successful(hotelPropertyMetaData))
    }

    val agForceExperimentsValue = forceExperiments.map { case (k, v) => k + "," + v }.mkString(",")
    val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, agForceExperimentsValue)

    executeSSRGraphQL(
      query,
      module,
      headers
    )
  }

  "Rectangle Search when creating Jarvis request" should {
    "sent correct search type id imply from GQL query field when exp FUSION-6138 is B" in {
      val mockJarvisClient = mock[JarvisClient]
      when(mockJarvisClient.getJarvisRankingResponse(any, any, any))
        .thenReturn(Future.successful(Some(JarvisResponse())))

      val respF = executeTestCase(
        propertyIds = (1L to 10L).toList,
        mockJarvisClient,
        forceExperiments = Map("FUSION-6138" -> "B")
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        verifyJarvisRequestParam(mockJarvisClient, 9)
      }
    }

    "sent search type id from PAPI request's SearchContext when exp FUSION-6138 is A" in {
      val mockJarvisClient = mock[JarvisClient]
      when(mockJarvisClient.getJarvisRankingResponse(any, any, any))
        .thenReturn(Future.successful(Some(JarvisResponse())))

      val respF = executeTestCase(
        propertyIds = (1L to 10L).toList,
        mockJarvisClient,
        forceExperiments = Map("FUSION-6138" -> "A")
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        verifyJarvisRequestParam(mockJarvisClient, 1)
      }
    }
  }

  def verifyJarvisRequestParam(jarvisClient: JarvisClient, expectedSearchType: Int): Unit = {
    val jarvisRequestCaptor = new ArgumentCapture[JarvisRequest]
    verify(jarvisClient).getJarvisRankingResponse(jarvisRequestCaptor.capture, any, any)
    val requestValue = jarvisRequestCaptor.value
    requestValue.searchcriteria.searchType shouldEqual expectedSearchType
  }

}
