package com.agoda.papi.search.functionaltest.testsuite.pricestream

import akka.http.scaladsl.model.StatusCodes
import com.agoda.core.search.models.builders.TestModelsBuilders.aValidCityId
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.TestCheapestPriceByCheckIn
import com.agoda.pricestream.models.response.{ CheapestPriceByCheckIn, PropertyPriceTrend }
import framework.MockModule
import metaapi.PriceTrendByPropertyRequest
import models.starfruit.SuggestedPrice
import org.joda.time.DateTime
import request.PropertyContext
import util.builders.TestDataBuilders.aValidHotelId
import util.builders.requestbuilder.PropertyContextBuilder

class PriceStreamPriceTrendSpec extends PAPISearchFunctionalTest {

  val abTest  = new ActiveABTests()
  val expName = abTest.pricestreamPropertyPriceTrendNegativeExp

  val today      = DateTime.now().withTimeAtStartOfDay()
  val nextTwoDay = today.plusDays(2).withTimeAtStartOfDay()

  val hotelId1 = aValidHotelId
  val cityId   = aValidCityId

  def createRequest(
      propertyContext: PropertyContext,
      suggestedPrice: Int = 0,
      requiredPrice: Option[SuggestedPrice] = None
  ): PriceTrendByPropertyRequest =
    PriceTrendByPropertyRequest(
      context = propertyContext,
      cityId = cityId,
      los = 2,
      occupancy = 1,
      startDate = today,
      endDate = nextTwoDay,
      supportedChannel = List(),
      isUserLoggedIn = false,
      currency = "THB",
      suggestedPrice = suggestedPrice,
      requiredPrice = requiredPrice
    )

  val priceTrendHotel1 = PropertyPriceTrend(
    propertyId = hotelId1,
    los = 2,
    prices = Seq(
      CheapestPriceByCheckIn(
        checkIn = today,
        price = 100d
      ),
      CheapestPriceByCheckIn(
        checkIn = nextTwoDay,
        price = 150d
      )
    )
  )

  val propertyToPriceTrendMap = Map(
    hotelId1 -> priceTrendHotel1
  )

  "PriceStreamPriceTrend endpoint /api/pricestream/pricetrend" should {
    "return InternalServerError when not put any property ids in request" in {
      val propertyContext = PropertyContextBuilder
        .newBuilder()
        .withPropertyIds(List.empty)
        .build()

      val request = createRequest(propertyContext)

      val module = new MockModule {
        mockPriceStreamPropertyTrend(propertyToPriceTrendMap)
      }

      val resF = executePriceStreamPriceTrend(
        request,
        module
      )

      whenReady(resF) { res =>
        res.status shouldBe StatusCodes.InternalServerError
      }
    }

    "return price trend when request with suggestedPrice = 1" in {
      val propertyContext = PropertyContextBuilder
        .newBuilder()
        .withPropertyIds(List(hotelId1))
        .build()

      val request = createRequest(propertyContext, suggestedPrice = 1)

      val module = new MockModule {
        mockPriceStreamPropertyTrend(propertyToPriceTrendMap)
      }

      val resF = executePriceStreamPriceTrend(
        request,
        module
      )

      whenReady(resF) { res =>
        res.status shouldBe StatusCodes.OK

        val priceTrend = responseToPriceTrendResponse(res)
        priceTrend.propertyId shouldBe hotelId1
        priceTrend.los shouldBe priceTrendHotel1.los
        assertPriceTrend(priceTrend.prices)
      }

    }

    "return price trend when request with AllInclusive RequiredPrice" in {
      val propertyContext = PropertyContextBuilder
        .newBuilder()
        .withPropertyIds(List(hotelId1))
        .build()

      val request = createRequest(propertyContext, requiredPrice = Some(SuggestedPrice.AllInclusive))

      val module = new MockModule {
        mockPriceStreamPropertyTrend(propertyToPriceTrendMap)
      }

      val resF = executePriceStreamPriceTrend(
        request,
        module
      )

      whenReady(resF) { res =>
        res.status shouldBe StatusCodes.OK

        val priceTrend = responseToPriceTrendResponse(res)
        priceTrend.propertyId shouldBe hotelId1
        priceTrend.los shouldBe priceTrendHotel1.los
        assertPriceTrend(priceTrend.prices)
      }

    }

    s"[$expName=B] return empty price trend when request with suggestedPrice = 1" in {
      val propertyContext = PropertyContextBuilder
        .newBuilder()
        .withPropertyIds(List(hotelId1))
        .withAdditionalExperiments(expName, "B")
        .build()

      val request = createRequest(propertyContext, requiredPrice = Some(SuggestedPrice.AllInclusive))

      val module = new MockModule {
        mockPriceStreamPropertyTrend(propertyToPriceTrendMap)
      }

      val resF = executePriceStreamPriceTrend(
        request,
        module
      )

      whenReady(resF) { res =>
        res.status shouldBe StatusCodes.OK

        val priceTrend = responseToPriceTrendResponse(res)
        priceTrend.propertyId shouldBe hotelId1
        priceTrend.prices shouldBe empty
      }

    }
  }

  private def assertPriceTrend(actualPriceTrends: Seq[TestCheapestPriceByCheckIn]) = {
    val expectedPriceTrend = priceTrendHotel1.prices.map { price =>
      TestCheapestPriceByCheckIn(
        checkIn = price.checkIn,
        price = price.price
      )
    }
    actualPriceTrends should contain theSameElementsAs (expectedPriceTrend)
  }

}
