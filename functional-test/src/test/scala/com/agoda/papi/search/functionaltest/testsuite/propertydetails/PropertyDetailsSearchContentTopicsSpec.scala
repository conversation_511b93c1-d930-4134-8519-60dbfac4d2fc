package com.agoda.papi.search.functionaltest.testsuite.propertydetails

import akka.http.scaladsl.model.StatusCodes
import com.agoda.common.graphql.model.Wrapper
import com.agoda.core.search.framework.builders.TestDataBuilders.aValidPropertyDetailsRequest
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.util.ContentTopicsDataExample
import com.agoda.papi.search.common.util.ContentTopicsDataExample.{ aValidContentTopicsRequest, aValidTopic }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.functionaltest.framework.model.{
  TestContentTopic,
  TestContentTopicImage,
  TestContentTopicSentimentScore,
  TestContentTopicsResponse
}
import com.agoda.property.search.framework.GqlMockModule

class PropertyDetailsSearchContentTopicsSpec extends PAPISearchFunctionalTest {

  "Property Details Search" should {
    "return content Topics" in {
      val query = aValidPropertyDetailsSearchRequest
        .withPropertyDetailsRequest(aValidPropertyDetailsRequest.copy(propertyIds = List(1001L)))
        .withContentTopics(aValidContentTopicsRequest)
        .build()

      val module = new GqlMockModule {
        mockTopicsService(
          Map(
            1001L -> Wrapper(
              ContentWithPropertyId(1001L),
              ContentTopicsDataExample.jsonContentTopicsResponse
            )
          )
        )
      }

      val respF = executePropertyDetailsSearchGraphQL(query, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val propertyDetailsSearchResponse = responseToPropertyDetailsSearchResponse(resp)
        val propertyIds = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails.map(_.propertyId)
        val contentTopics = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails
          .flatMap(_.contentDetail.flatMap(_.contentTopics))
        propertyIds should contain theSameElementsAs List(1001L)
        contentTopics should have size 1
        contentTopics should contain theSameElementsAs List(
          TestContentTopicsResponse(
            topics = Some(
              Vector(
                TestContentTopic(
                  topic = Some(aValidTopic.topic),
                  sentimentScore = aValidTopic.sentimentScore.map(score =>
                    TestContentTopicSentimentScore(
                      positive = Some(score.positive),
                      negative = Some(score.negative),
                      neutral = Some(score.neutral)
                    )
                  ),
                  reviewSnippets = Some(aValidTopic.reviewSnippets),
                  images = Some(
                    aValidTopic.images.map(img =>
                      TestContentTopicImage(
                        url = Some(img.url),
                        imageId = Some(img.imageId),
                        imageSource = Some(img.imageSource)
                      )
                    )
                  ),
                  summary = aValidTopic.summary
                )
              )
            )
          )
        )
      }
    }
  }

}
