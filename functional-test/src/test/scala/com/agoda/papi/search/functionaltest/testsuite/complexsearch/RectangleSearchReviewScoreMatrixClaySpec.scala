package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.StatusCodes
import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.matrix.{ FilterKeys, MatrixGroupTypes }
import com.agoda.core.search.models.request.MatrixGroupRequest
import com.agoda.core.search.models.{ FilterRequest, RangeFilterItem, RangeFilterRequest }
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidRectangleSearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

// Confirms no effect on ReviewScoreMatrix with FUSION-5339 experiment
class RectangleSearchReviewScoreMatrixClaySpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {
  override def beforeAll(): Unit = mockEsBeforeAll()

  val propertyIds      = 1L to 6L
  val propertyMetadata = generateDefaultPropertyInfoList(propertyIds.toList)
  val avail            = generateDefaultAvail(propertyIds.toList)

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel6)
      .build()
  )

  "rectangle Search filter by review score" should {

    "review score matrix result with range filter selected filters (FUSION-5339 = A)" in {
      val rectangleSearchRequest = buildRequest()
      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,A")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 4
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs
          List(
            TestMatrixItemResult(9, "CMSId:78266", 0),
            TestMatrixItemResult(8, "CMSId:78267", 0),
            TestMatrixItemResult(7, "CMSId:78268", 0),
            TestMatrixItemResult(6, "CMSId:78269", 0)
          )

      }
    }
    "review score matrix result with range filter selected filters (FUSION-5339 = B)" in {
      val rectangleSearchRequest = buildRequest()
      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,B")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 4
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs
          List(
            TestMatrixItemResult(9, "CMSId:78266", 0),
            TestMatrixItemResult(8, "CMSId:78267", 0),
            TestMatrixItemResult(7, "CMSId:78268", 0),
            TestMatrixItemResult(6, "CMSId:78269", 0)
          )

      }
    }
    "review score matrix result with multiple filter selected filters (FUSION-5339 = A)" in {
      val rectangleSearchRequest = buildRequestWithOR()
      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,A")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 4
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs
          List(
            TestMatrixItemResult(9, "CMSId:78266", 0),
            TestMatrixItemResult(8, "CMSId:78267", 0),
            TestMatrixItemResult(7, "CMSId:78268", 0),
            TestMatrixItemResult(6, "CMSId:78269", 0)
          )

      }
    }
    "review score matrix result with multiple filter selected filters (FUSION-5339 = B)" in {
      val rectangleSearchRequest = buildRequestWithOR()
      val query = QueryBuilder.build(
        aValidRectanglSearchQuery,
        rectangleSearchRequest,
        DFDataExample.aPricingRequestParameters,
        contentSummaryRequest
      )
      val module = new GqlMockModule {
        mockCachePropertyInfoList(propertyMetadata)
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockESMatrixService(client)

      }
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "FUSION-5339,B")

      val respF = executeSSRGraphQL(
        query,
        module,
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val rectangleSearchResponse = responseToRectangleSearchResponse(resp)

        val matrixGroupResults = rectangleSearchResponse.data.rectangleSearch.aggregation.get.matrixGroupResults

        matrixGroupResults.head.matrixItemResults should have size 4
        matrixGroupResults.head.matrixItemResults should contain theSameElementsAs
          List(
            TestMatrixItemResult(9, "CMSId:78266", 0),
            TestMatrixItemResult(8, "CMSId:78267", 0),
            TestMatrixItemResult(7, "CMSId:78268", 0),
            TestMatrixItemResult(6, "CMSId:78269", 0)
          )

      }
    }

  }

  private def buildRequest() = {

    val filterRequest = FilterRequest(rangeFilters =
      Some(
        List(
          RangeFilterRequest(
            FilterKeys.ReviewScore,
            ranges = List(RangeFilterItem(9.0, Some(10.0)))
          )
        )
      )
    )

    val rectangleSearchRequest = aValidRectangleSearchRequest
      .copy(searchRequest =
        aValidRectangleSearchRequest.searchRequest
          .withFilterRequest(filterRequest)
          .withMatrixGroup(Some(List(MatrixGroupRequest(MatrixGroupTypes.ReviewScore, Some(20)))))
          .build()
      )

    rectangleSearchRequest
  }

  private def buildRequestWithOR() = {

    val filterRequest = FilterRequest(rangeFilters =
      Some(
        List(
          RangeFilterRequest(
            FilterKeys.ReviewScore,
            ranges = List(RangeFilterItem(8.0, Some(10.0)), RangeFilterItem(9.0, Some(10.0)))
          )
        )
      )
    )

    val rectangleSearchRequest = aValidRectangleSearchRequest
      .copy(searchRequest =
        aValidRectangleSearchRequest.searchRequest
          .withFilterRequest(filterRequest)
          .withMatrixGroup(Some(List(MatrixGroupRequest(MatrixGroupTypes.ReviewScore, Some(20)))))
          .build()
      )

    rectangleSearchRequest
  }

}
