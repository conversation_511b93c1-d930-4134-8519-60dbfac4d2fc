package com.agoda.papi.search.functionaltest.testsuite.booking

import akka.http.scaladsl.model.StatusCodes
import api.request.PaymentChargeTypes
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import framework.MockModule
import models.starfruit._
import org.mockito.MockitoSugar
import org.scalatest.prop.TableDrivenPropertyChecks
import request._
import transformers.{ ExchangeRateOption, Properties }
import util.builders.DefaultTestDataExample
import util.builders.TestDataBuilders._
import util.builders.requestbuilder.CreditCardPaymentRequestBuilder

import scala.concurrent.Future

/*
 * FXI feature is a feature that play with customer credit card currency and exchange rate.
 * As customer may use credit card which has bank currency different from what customer select as display currency.
 * M150 is a feature which uplifts 5% to the total amount based on some criteria
 * like when customer's credit card is different from the hotel's payment currency
 *
 * M150 and FXI should be disabled for US customer due to legal issue.
 * */
class BookingEndpointM150andFXISpec extends PAPISearchFunctionalTest with MockitoSugar with TableDrivenPropertyChecks {

  s"BookingEndpoint /api/booking" should {

    "ExchangeRateOption cases should use ExchangeRateOption FloatV2" in {
      Table(
        ("M150 eligible", "paylater", "uplift", "expectedExchangeRateOption", "expectedPaymentChargeOption"),
        (true, true, 1.0d, ExchangeRateOption.FloatV2, PaymentChargeTypes.PayLater.i),
        (true, true, 0.0d, ExchangeRateOption.FloatV2, PaymentChargeTypes.PayLater.i),
        (true, false, 1.0d, ExchangeRateOption.FXI, PaymentChargeTypes.PayNow.i),
        (true, false, 0.0d, ExchangeRateOption.None, PaymentChargeTypes.PayNow.i),
        (false, true, 1.0d, ExchangeRateOption.FXI, PaymentChargeTypes.PayLater.i),
        (false, true, 0.0d, ExchangeRateOption.None, PaymentChargeTypes.PayLater.i),
        (false, false, 1.0d, ExchangeRateOption.FXI, PaymentChargeTypes.PayNow.i),
        (false, false, 0.0d, ExchangeRateOption.None, PaymentChargeTypes.PayNow.i)
      )
        .forEvery { (m150Eligible, isPaylater, upliftAmount, expectedExchangeRateOption, expectedPaymentChargeOption) =>
          val origin             = "TH"
          val currency           = "EUR"
          val paymentCurrency    = "THB"
          val creditCardCurrency = "THB"
          val hotelId            = 10091L
          val roomUid            = "a"
          val roomIdentifier     = "roomIdentifier-a"

          val upliftPayment          = PaymentBreakdown(1d, 1d, 1d)
          val paymentFeaturePayment  = PaymentFeature(upliftPayment, 1.0d)
          val paymentFeatureCurrency = PaymentFeature(upliftPayment, 2.0d)

          val dfHotel = prepareDfResponse(
            hotelId = hotelId,
            roomUid = roomUid,
            roomIdentifier = roomIdentifier,
            isPaylater = isPaylater,
            m150Eligible = m150Eligible,
            upliftAmount = upliftAmount,
            paymentCurrency = paymentCurrency,
            currency = currency,
            paymentFeatureOfPaymentCurrency = paymentFeaturePayment,
            paymentFeatureOfCurrency = paymentFeatureCurrency
          )

          val masterRoomMetadata = aValidMasterRoomMeta
            .withRoomId(1L)
            .build()

          val contentMasterRooms = aValidRoomInformationResponse
            .withHotelId(hotelId)
            .withMasterRoomResponse(
              Vector(
                aValidMasterRoomResponse.withMasterRoomMetadata(masterRoomMetadata).build()
              )
            )
            .build()

          val request = prepareRequest(hotelId, creditCardCurrency, origin, isPaylater)

          val module = new MockModule {
            mockDFResponse(Map(hotelId -> dfHotel))
            mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
          }

          val respF = executeBookingEndpoint(request = request, module = module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            val roomIdentifiers =
              properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
            (roomIdentifiers should contain).theSameElementsInOrderAs(List(roomIdentifier))

            assertBooking(properties, expectedExchangeRateOption, expectedPaymentChargeOption)
            assertRoomPaymentFeature(
              properties,
              currency,
              paymentCurrency,
              paymentFeatureCurrency,
              paymentFeaturePayment
            )
          }
        }
    }

  }

  private def assertBooking(properties: Properties, expectedExRateOption: Int, expectedPaymentChargeOption: Int) = {
    val booking = properties.property.head.booking.get.booking.head

    booking.creditCard.payment.exchangeRateOption shouldBe Some(expectedExRateOption)
    booking.creditCard.chargeOption shouldBe Some(expectedPaymentChargeOption)
  }

  private def assertRoomPaymentFeature(
      properties: Properties,
      currency: String,
      paymentCurrency: String,
      paymentFeatureCurrency: PaymentFeature,
      paymentFeaturePayment: PaymentFeature
  ) = {
    val aRoom = properties.property.head.masterRooms.head.childrenRooms.head

    aRoom.paymentFeature.get(currency).upliftAmount shouldBe paymentFeatureCurrency.upliftAmount
    aRoom.paymentFeature.get(paymentCurrency).upliftAmount shouldBe paymentFeaturePayment.upliftAmount
  }

  private def changePaymentCharge(
      creditCardPaymentRequest: CreditCardPaymentRequest,
      isPaylater: Boolean
  ): CreditCardPaymentRequestBuilder =
    if (isPaylater) {
      creditCardPaymentRequest.withPayLater()
    } else {
      creditCardPaymentRequest.withPayNow()
    }

  private def prepareRequest(
      hotelId: Long,
      creditCardCurrency: String,
      origin: String,
      isPaylater: Boolean,
      countryIdOfIssuingBank: Option[Int] = None
  ): PropertyRequest = {
    val context = aValidContext
      .withOrigin(origin)
      .withPropertyIds(List(hotelId))

    val creditCardPaymentRequest = changePaymentCharge(aValidCreditCardPaymentRequest, isPaylater)
      .withCreditCardCurrency(creditCardCurrency)
      .withCountryIdOfIssuingBank(countryIdOfIssuingBank)
      .build()

    val pricingRequest = aValidPricingRequest
      .withCreditCardPaymentRequest(creditCardPaymentRequest)
      .build()

    val request = aValidPropertyRequest
      .withContext(context)
      .withSearchType(SearchTypes.Property)
      .withPricing(pricingRequest)
      .build()

    request
  }

  private def prepareDfResponse(
      hotelId: Long,
      roomUid: String,
      roomIdentifier: String,
      isPaylater: Boolean,
      m150Eligible: Boolean,
      upliftAmount: Double,
      paymentCurrency: String,
      currency: String,
      paymentFeatureOfPaymentCurrency: PaymentFeature,
      paymentFeatureOfCurrency: PaymentFeature
  ): Hotel = {
    val room1Payment = DefaultTestDataExample.aValidDFPayment
      .withPayLater(PayLater(payLater = isPaylater))
      .build()

    val aDfRoom1 = aValidDFRoom
      .withRoomTypeId(1L)
      .withRoomIdentifier(roomIdentifier)
      .withM150(M150(isEligible = m150Eligible, None))
      .withPaymentFeature(Map(paymentCurrency -> paymentFeatureOfPaymentCurrency, currency -> paymentFeatureOfCurrency))
      .withPayment(room1Payment)
      .withUID(roomUid)
      .withBOR(None)
      .build()

    val aBookingRoom1 = aValidBookingRoom
      .withUid(roomUid)
      .withLocalCurrency(currency)
      .withPaymentCurrency(paymentCurrency)
      .withUpliftAmount(Some(upliftAmount))
      .build()

    val bookingSummary1: BookingSummary = BookingSummary(List(aBookingRoom1), 1)

    val aBundleSegment = aValidBundleSegment
      .copy(
        room = aDfRoom1,
        bookingSummary = Some(bookingSummary1)
      )

    val aRoomBundle = aValidRoomBundles
      .copy(
        mergedRoom = aDfRoom1,
        segments = List(aBundleSegment)
      )

    val dfHotel = aValidDFHotel
      .withHotelId(hotelId)
      .withRooms(Seq(aDfRoom1))
      .withBookingSummary(Some(bookingSummary1))
      .withRoomBundleList(List(aRoomBundle))
      .build()

    dfHotel
  }

}
