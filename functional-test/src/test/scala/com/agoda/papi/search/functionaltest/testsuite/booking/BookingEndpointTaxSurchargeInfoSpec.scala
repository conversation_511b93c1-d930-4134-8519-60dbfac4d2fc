package com.agoda.papi.search.functionaltest.testsuite.booking

import akka.http.scaladsl.model.StatusCodes
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.constant.mappings.CountryIds
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import constant.TaxConstants
import framework.MockModule
import models.pricing.enums.ChargeTypes
import org.scalatest.prop.TableDrivenPropertyChecks
import request._
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class BookingEndpointTaxSurchargeInfoSpec extends PAPISearchFunctionalTest with TableDrivenPropertyChecks {

  val activeAbTests = ActiveABTests()

  "Booking-endpoint /api/booking" should {
    "return valid property back with correct taxSurchargeInfo JTBFP-1293 A" in {
      val surchargeExclusive =
        aValidDisplaySurcharge.withBreakdown(Seq(aValidSurcharge.withInclude(Some(false)))).build()
      val surchargeInclusive =
        aValidDisplaySurcharge.withBreakdown(Seq(aValidSurcharge.withInclude(Some(true)))).build()
      val taxExclusive = aValidDisplayTaxAndFee.withBreakdown(Seq(aValidTaxAndFee.withInclude(Some(false)))).build()
      val taxInclusive = aValidDisplayTaxAndFee.withBreakdown(Seq(aValidTaxAndFee.withInclude(Some(true)))).build()

      Table(
        ("charges", "expected"),
        // surchargeExclusive and surchargeInclusive should retun includesive CMSId:63344 and exclusive CMSId:63345
        (
          Seq(surchargeExclusive, surchargeInclusive),
          "CMSId:63344: Some description USD 60.0<br>CMSId:63345: Some description USD 60.0"
        ),
        // surchargeExclusive only should retun exclusive CMSId:63345
        (
          Seq(surchargeExclusive),
          "CMSId:63345: Some description USD 60.0"
        ),
        // surchargeInclusive only should retun includesive CMSId:63344
        (
          Seq(surchargeInclusive),
          "CMSId:63344: Some description USD 60.0"
        ),
        // No surcharge should return taxInclusive
        (
          Seq(),
          ""
        ),
        // taxExclusive only should return exclusive CMSId:63345
        (
          Seq(taxExclusive),
          "CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876)"
        ),
        // taxInclusive only should retun includesive CMSId:63344
        (
          Seq(taxInclusive),
          "CMSId:63344: CMSId:60863 USD 60.0"
        ),
        // surchargeExclusive combi  taxExclusive should return only excludsive cmsId:63345
        (
          Seq(surchargeExclusive, taxExclusive),
          "CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876), Some description USD 60.0"
        ),
        // surchargeInclusive combi with taxExclusive should retun includesive CMSId:63344 and exclusive CMSId:63345
        (
          Seq(surchargeInclusive, taxExclusive),
          "CMSId:63344: Some description USD 60.0<br>CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876)"
        ),
        // taxInclusive with taxExclusive should retun includesive CMSId:63344 and exclusive CMSId:63345
        (
          Seq(taxInclusive, taxExclusive),
          "CMSId:63344: CMSId:60863 USD 60.0<br>CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876)"
        ),
        // surchargeInclusive with taxInclusive should should return only excludsive cmsId:63344
        (
          Seq(surchargeInclusive, taxInclusive),
          "CMSId:63344: CMSId:60863 USD 60.0, Some description USD 60.0"
        )
      )
        .forEvery { (charges, expected) =>
          val dfHotel = aValidDFHotel
            .withHotelId(1001)
            .withCountryId(CountryIds.HongKong)
            .withRooms(
              Seq(
                aValidDFRoom
                  .withRoomTypeId(1L)
                  .withRoomIdentifier("a")
                  .withPricing(
                    Map("USD" -> aValidDFPricing.withDisplayCharge(charges))
                  )
                  .build()
              )
            )
            .withBookingSummary(Some(aValidDFBookingSummary))
            .build()
          val contentMasterRooms = aValidRoomInformationResponse
            .withHotelId(1001)
            .withMasterRoomResponse(
              Vector(
                aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
              )
            )
            .build()
          val module = new MockModule {
            mockDFResponse(Map(1001L -> dfHotel))
            mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
          }
          val request = aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(1001L))
            .withPricing(aValidPricingRequest)
            .withAdditionalExperimentForce(activeAbTests.hongKongHatTax, "B")
            .withAdditionalExperimentForce(activeAbTests.isDmcIdRemoveEnabled, "A")
            .build()

          val respF = executeBookingEndpoint(request = request, module = module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            val roomIdentifiers =
              properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
            (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))
            properties.property.head.booking.get.rooms.head.taxSurchargeInfo shouldBe expected
          }
        }
    }

    "return valid property back with correct taxSurchargeInfo when JTBFP-1293 B" in {
      val surchargeExclusive =
        aValidDisplaySurcharge.withBreakdown(Seq(aValidSurcharge.withInclude(Some(false)))).build()
      val surchargeInclusive =
        aValidDisplaySurcharge.withBreakdown(Seq(aValidSurcharge.withInclude(Some(true)))).build()
      val taxExclusive = aValidDisplayTaxAndFee.withBreakdown(Seq(aValidTaxAndFee.withInclude(Some(false)))).build()
      val taxInclusive = aValidDisplayTaxAndFee.withBreakdown(Seq(aValidTaxAndFee.withInclude(Some(true)))).build()

      Table(
        ("charges", "expected"),
        // surchargeExclusive and surchargeInclusive should retun includesive CMSId:63344 and exclusive CMSId:63345
        (
          Seq(surchargeExclusive, surchargeInclusive),
          "CMSId:63344: Some description USD 60.0<br>CMSId:63345: Some description USD 60.0"
        ),
        // surchargeExclusive only should retun exclusive CMSId:63345
        (
          Seq(surchargeExclusive),
          "CMSId:63345: Some description USD 60.0"
        ),
        // surchargeInclusive only should retun includesive CMSId:63344
        (
          Seq(surchargeInclusive),
          "CMSId:63344: Some description USD 60.0"
        ),
        // No surcharge should return taxInclusive
        (
          Seq(),
          ""
        ),
        // taxExclusive only should return exclusive CMSId:63345
        (
          Seq(taxExclusive),
          "CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876)"
        ),
        // taxInclusive only should retun includesive CMSId:63344
        (
          Seq(taxInclusive),
          "CMSId:63344: CMSId:60863 USD 60.0"
        ),
        // surchargeExclusive combi  taxExclusive should return only excludsive cmsId:63345
        (
          Seq(surchargeExclusive, taxExclusive),
          "CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876), Some description USD 60.0"
        ),
        // surchargeInclusive combi with taxExclusive should retun includesive CMSId:63344 and exclusive CMSId:63345
        (
          Seq(surchargeInclusive, taxExclusive),
          "CMSId:63344: Some description USD 60.0<br>CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876)"
        ),
        // taxInclusive with taxExclusive should retun includesive CMSId:63344 and exclusive CMSId:63345
        (
          Seq(taxInclusive, taxExclusive),
          "CMSId:63344: CMSId:60863 USD 60.0<br>CMSId:63345: CMSId:60863 USD 60.0 (CMSId:60876)"
        ),
        // surchargeInclusive with taxInclusive should should return only excludsive cmsId:63344
        (
          Seq(surchargeInclusive, taxInclusive),
          "CMSId:63344: CMSId:60863 USD 60.0, Some description USD 60.0"
        )
      )
        .forEvery { (charges, expected) =>
          val dfHotel = aValidDFHotel
            .withHotelId(1001)
            .withCountryId(CountryIds.HongKong)
            .withRooms(
              Seq(
                aValidDFRoom
                  .withRoomTypeId(1L)
                  .withRoomIdentifier("a")
                  .withPricing(
                    Map("USD" -> aValidDFPricing.withDisplayCharge(charges))
                  )
                  .build()
              )
            )
            .withBookingSummary(Some(aValidDFBookingSummary))
            .build()
          val contentMasterRooms = aValidRoomInformationResponse
            .withHotelId(1001)
            .withMasterRoomResponse(
              Vector(
                aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
              )
            )
            .build()
          val module = new MockModule {
            mockDFResponse(Map(1001L -> dfHotel))
            mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
          }
          val request = aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(1001L))
            .withPricing(aValidPricingRequest)
            .withAdditionalExperimentForce(activeAbTests.hongKongHatTax, "B")
            .withAdditionalExperimentForce(activeAbTests.isDmcIdRemoveEnabled, "B")
            .build()

          val respF = executeBookingEndpoint(request = request, module = module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK
            val properties = responseToProperties(resp)
            val roomIdentifiers =
              properties.property.flatMap(_.masterRooms.flatMap(_.childrenRooms.flatMap(_.roomIdentifiers)))
            (roomIdentifiers should contain).theSameElementsInOrderAs(List("a"))
            properties.property.head.booking.get.rooms.head.taxSurchargeInfo shouldBe expected
          }
        }
    }
  }

}
