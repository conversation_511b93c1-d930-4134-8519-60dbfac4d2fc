package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import enumerations.RecommendationTypes
import framework.MockModule
import request.PropertyRecommendationRequest
import transformers.PropertiesRecommendationResponse
import util.builders.TestDataBuilders._
import util.builders.requestbuilder.PropertyRecommendationRequestBuilder

import scala.concurrent.Future

class UpTierRecommendationSpec extends PAPISearchFunctionalTest {

  "UpTier Recommendation /api/recommendation" should {
    val originalHotelId      = 100L
    val samePriceHotelId     = 201L
    val soldOutHotelId       = 202L
    val inactiveHotelId      = 203L
    val invalidHotelId       = 204L
    val inRangeLowerHotelId  = 205L
    val inRangeHigherHotelId = 206L
    val tooExpensiveHotelId  = 207L
    val tooCheapHotelId      = 208L

    val allHotelIdsWithContent =
      List(
        originalHotelId,
        samePriceHotelId,
        soldOutHotelId,
        inRangeLowerHotelId,
        inRangeHigherHotelId,
        tooExpensiveHotelId,
        tooCheapHotelId
      )
    val allRecommendedIds =
      List(
        samePriceHotelId,
        soldOutHotelId,
        inactiveHotelId,
        invalidHotelId,
        inRangeLowerHotelId,
        inRangeHigherHotelId,
        tooExpensiveHotelId,
        tooCheapHotelId
      )

    "return only properties with prices in valid range for an up-tier recommendation" in {
      val request: PropertyRecommendationRequest = PropertyRecommendationRequestBuilder
        .newBuilder()
        .withPropertyIds(List(originalHotelId))
        .withRecommendationType(RecommendationTypes.UpTier)
        .build()
      val originalDfHotel = aValidDFHotel
        .withHotelId(originalHotelId)
        .withRooms(
          Seq(
            aValidDFRoom
              .withRoomTypeId(1L)
              .withPricing(
                Map(
                  "USD" -> aValidDFPricing
                    .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(100.0)))
                    .build()
                )
              )
              .build()
          )
        )
        .build()
      val samePriceDfHotel = aValidDFHotel
        .withHotelId(samePriceHotelId)
        .withRooms(
          Seq(
            aValidDFRoom
              .withRoomTypeId(2L)
              .withPricing(
                Map(
                  "USD" -> aValidDFPricing
                    .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(100.0)))
                    .build()
                )
              )
              .build()
          )
        )
        .build()
      val inRangeLowerDfHotel = aValidDFHotel
        .withHotelId(inRangeLowerHotelId)
        .withRooms(
          Seq(
            aValidDFRoom
              .withRoomTypeId(3L)
              .withPricing(
                Map(
                  "USD" -> aValidDFPricing
                    .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(98.0)))
                    .build()
                )
              )
              .build()
          )
        )
        .build()
      val inRangeHigherDfHotel = aValidDFHotel
        .withHotelId(inRangeHigherHotelId)
        .withRooms(
          Seq(
            aValidDFRoom
              .withRoomTypeId(4L)
              .withPricing(
                Map(
                  "USD" -> aValidDFPricing
                    .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(110.0)))
                    .build()
                )
              )
              .build()
          )
        )
        .build()
      val tooExpensiveDfHotel = aValidDFHotel
        .withHotelId(tooExpensiveHotelId)
        .withRooms(
          Seq(
            aValidDFRoom
              .withRoomTypeId(5L)
              .withPricing(
                Map(
                  "USD" -> aValidDFPricing
                    .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(130.0)))
                    .build()
                )
              )
              .build()
          )
        )
        .build()
      val tooCheapDfHotel = aValidDFHotel
        .withHotelId(tooCheapHotelId)
        .withRooms(
          Seq(
            aValidDFRoom
              .withRoomTypeId(6L)
              .withPricing(
                Map(
                  "USD" -> aValidDFPricing
                    .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(85.0)))
                    .build()
                )
              )
              .build()
          )
        )
        .build()

      val soldOutHotel = aValidDFHotel
        .withHotelId(soldOutHotelId)
        .withRooms(Seq.empty)
        .build()

      val module = new MockModule {
        mockFeatureStoreService(
          Map(originalHotelId -> allRecommendedIds)
        )

        val contentResponses = allHotelIdsWithContent.map { id =>
          aValidContentPropertyResponse
            .withHotelId(id)
            .withSummary(Some(aValidInformationSummaryResponse.withHotelId(id)))
            .build()
        }
        mockContentResponse(Future.successful(contentResponses))

        mockDFRecommendation(
          Map(
            originalHotelId      -> originalDfHotel,
            samePriceHotelId     -> samePriceDfHotel,
            soldOutHotelId       -> soldOutHotel,
            inRangeLowerHotelId  -> inRangeLowerDfHotel,
            inRangeHigherHotelId -> inRangeHigherDfHotel,
            tooExpensiveHotelId  -> tooExpensiveDfHotel,
            tooCheapHotelId      -> tooCheapDfHotel
          )
        )
      }

      val respF = executeRecommendationSearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK

        val recommendationResponse = responseToRecommendations(resp)
        val recommendedProperties  = recommendationResponse.recommendations

        recommendedProperties.map(_.propertyId.get) shouldBe List(
          inRangeLowerHotelId,
          samePriceHotelId,
          inRangeHigherHotelId
        )
      }
    }
  }

}
