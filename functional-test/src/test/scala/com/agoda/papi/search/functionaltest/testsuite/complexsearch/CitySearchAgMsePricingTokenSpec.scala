package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels.hotel1
import com.agoda.core.search.models.response.AvailabilityData
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.framework.mock.DFServiceDefaultTest
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.DFDataExample.{ aClientInfo, aContextRequest }
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.complexsearch.DFService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.internalmodel.request.PricingPropertiesRequestWrapper
import com.agoda.papi.search.types.WithHotelIdMap
import com.agoda.platform.service.context.{ GlobalContext, ServiceHeaders }
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.property.search.utils.builders.GqlTestDataBuilders.{ toComplexSearchRequest => _ }
import org.joda.time.DateTime
import org.mockito.ArgumentMatchers.{ any => mockitoAny }
import org.mockito.Mockito
import org.scalatest.OptionValues
import org.specs2.mock.mockito.ArgumentCapture

import scala.collection.JavaConverters.asScalaIterator
import scala.concurrent.Future

class CitySearchAgMsePricingTokenSpec extends PAPISearchFunctionalTest with OptionValues {
  val meta: PropertyInfoList = generateDefaultPropertyInfoList(List(hotel1))

  val abTest = ActiveABTests()

  val avail: WithHotelIdMap[AvailabilityData] = WithHotelIdMap(
    hotel1 -> aValidAvailabilityData.withHotelId(hotel1).withSupplierInfo(List(aValidSupplierInfo)).build()
  )

  def mockModuleCity(dfService: DFService): GqlMockModule =
    new GqlMockModule {
      bind[DFService] to dfService
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockCachePropertyInfoList(meta)
    }

  " Request has a correct lowercase AG-MSE-PRICING-TOKEN header when ACTB-MSE = A" should {
    s"read the lowercase header do filter and override the header with desktop SSR time format T17:00:00Z when A of ${abTest.enableMultiProductMSEheader} and when JTBFP-1293 A" in {
      val dfService = Mockito.spy(new DFServiceDefaultTest)

      val citySearchRequest = aValidCitySearchRequest
        .withSearchCriteria(
          aValidCitySearchRequest.searchCriteria.copy(
            itineraryCriteria = aValidCitySearchRequest.searchCriteria.itineraryCriteria.map(
              _.copy(
                bookingDateOpt = Some(DateTime.parse("2024-10-08T11:12:13Z")),
                checkInDate = "2024-10-10"
              )
            )
          )
        )
        .build()
      val pricingRequestParameter = DFDataExample.aPricingRequestParameters.copy(
        context = aContextRequest.copy(clientInfo = aClientInfo.copy(cid = 1)),
        pricing = DFDataExample.aPricingRequestParameters.pricing.copy(
          checkIn = DateTime.parse("2024-10-09T17:00:00Z"),
          checkout = DateTime.parse("2024-10-12T17:00:00Z")
        )
      )

      val query = QueryBuilder.build(
        aValidSSRQuery,
        citySearchRequest,
        pricingRequestParameter,
        contentSummaryRequest
      )

      val module = mockModuleCity(dfService)

      executeSSRGraphQL(
        query,
        module,
        headers = aValidSSRHttpHeaders ++ Seq(
          RawHeader(
            "ag-mse-pricing-token",
            """[{"cid":"1","cin":"2024-10-10","los":"3", "hid":"101", "mpt":"msePricingToken"}
              |,{"cid":"1","cin":"2024-10-10","los":"2", "hid":"201", "mpt":"anotherToken"}]""".stripMargin
          ),
          RawHeader(ServiceHeaders.AgForceExperiments.toString, "ACTB-MSE,A"),
          RawHeader(ServiceHeaders.AgForceExperiments.toString, "JTBFP-1293,A")
        )
      )

      val dfRequestCaptor     = new ArgumentCapture[PricingPropertiesRequestWrapper]
      val globalContextCaptor = new ArgumentCapture[GlobalContext]
      Mockito
        .verify(dfService)
        .process(dfRequestCaptor.capture, globalContextCaptor.capture, mockitoAny())(mockitoAny())
      val capturedDFRequests     = asScalaIterator(dfRequestCaptor.values.iterator()).toList
      val overridePricingRequest = capturedDFRequests.headOption.flatMap(_.externalUserContext)
      overridePricingRequest shouldBe None

      val capturedGlobalContext = asScalaIterator(globalContextCaptor.values.iterator()).toList
      val capturedAgMsePricingToken =
        capturedGlobalContext.headOption.map(_.getRawAgHeaders("AG-MSE-PRICING-TOKEN"))
      capturedDFRequests should not be empty
      capturedAgMsePricingToken.value shouldBe
        """[{"mpt":"msePricingToken","los":"3","cid":"1","hid":"101","cin":"2024-10-10"}]"""

    }

    s"read the lowercase header do filter and override the header with desktop SSR time format T17:00:00Z when A of ${abTest.enableMultiProductMSEheader} and when JTBFP-1293 B" in {
      val dfService = Mockito.spy(new DFServiceDefaultTest)

      val citySearchRequest = aValidCitySearchRequest
        .withSearchCriteria(
          aValidCitySearchRequest.searchCriteria.copy(
            itineraryCriteria = aValidCitySearchRequest.searchCriteria.itineraryCriteria.map(
              _.copy(
                bookingDateOpt = Some(DateTime.parse("2024-10-08T11:12:13Z")),
                checkInDate = "2024-10-10"
              )
            )
          )
        )
        .build()
      val pricingRequestParameter = DFDataExample.aPricingRequestParameters.copy(
        context = aContextRequest.copy(clientInfo = aClientInfo.copy(cid = 1)),
        pricing = DFDataExample.aPricingRequestParameters.pricing.copy(
          checkIn = DateTime.parse("2024-10-09T17:00:00Z"),
          checkout = DateTime.parse("2024-10-12T17:00:00Z")
        )
      )

      val query = QueryBuilder.build(
        aValidSSRQuery,
        citySearchRequest,
        pricingRequestParameter,
        contentSummaryRequest
      )

      val module = mockModuleCity(dfService)

      executeSSRGraphQL(
        query,
        module,
        headers = aValidSSRHttpHeaders ++ Seq(
          RawHeader(
            "ag-mse-pricing-token",
            """[{"cid":"1","cin":"2024-10-10","los":"3", "hid":"101", "mpt":"msePricingToken"}
              |,{"cid":"1","cin":"2024-10-10","los":"2", "hid":"201", "mpt":"anotherToken"}]""".stripMargin
          ),
          RawHeader(ServiceHeaders.AgForceExperiments.toString, "ACTB-MSE,A"),
          RawHeader(ServiceHeaders.AgForceExperiments.toString, "JTBFP-1293,B")
        )
      )

      val dfRequestCaptor     = new ArgumentCapture[PricingPropertiesRequestWrapper]
      val globalContextCaptor = new ArgumentCapture[GlobalContext]
      Mockito
        .verify(dfService)
        .process(dfRequestCaptor.capture, globalContextCaptor.capture, mockitoAny())(mockitoAny())
      val capturedDFRequests     = asScalaIterator(dfRequestCaptor.values.iterator()).toList
      val overridePricingRequest = capturedDFRequests.headOption.flatMap(_.externalUserContext)
      overridePricingRequest shouldBe None

      val capturedGlobalContext = asScalaIterator(globalContextCaptor.values.iterator()).toList
      val capturedAgMsePricingToken =
        capturedGlobalContext.headOption.map(_.getRawAgHeaders("AG-MSE-PRICING-TOKEN"))
      capturedDFRequests should not be empty
      capturedAgMsePricingToken.value shouldBe
        """[{"mpt":"msePricingToken","los":"3","cid":"1","hid":"101","cin":"2024-10-10"}]"""

    }
  }

  " Request has a correct lowercase AG-MSE-PRICING-TOKEN header when ACTB-MSE = B" should {
    s"read the lowercase header do filter and override the header with desktop SSR time format T17:00:00Z when B of ${abTest.enableMultiProductMSEheader}" in {
      val dfService = Mockito.spy(new DFServiceDefaultTest)

      val citySearchRequest = aValidCitySearchRequest
        .withSearchCriteria(
          aValidCitySearchRequest.searchCriteria.copy(
            itineraryCriteria = aValidCitySearchRequest.searchCriteria.itineraryCriteria.map(
              _.copy(
                bookingDateOpt = Some(DateTime.parse("2024-10-08T11:12:13Z")),
                checkInDate = "2024-10-10"
              )
            )
          )
        )
        .build()
      val pricingRequestParameter = DFDataExample.aPricingRequestParameters.copy(
        context = aContextRequest.copy(clientInfo = aClientInfo.copy(cid = 1)),
        pricing = DFDataExample.aPricingRequestParameters.pricing.copy(
          checkIn = DateTime.parse("2024-10-09T17:00:00Z"),
          checkout = DateTime.parse("2024-10-12T17:00:00Z")
        )
      )

      val query = QueryBuilder.build(
        aValidSSRQuery,
        citySearchRequest,
        pricingRequestParameter,
        contentSummaryRequest
      )

      val module = mockModuleCity(dfService)

      executeSSRGraphQL(
        query,
        module,
        headers = aValidSSRHttpHeaders ++ Seq(
          RawHeader(
            "ag-mse-pricing-token",
            """{"property":[{"cid":"1","cin":"2024-10-10","los":"3", "hid":"101", "mpt":"msePricingToken"}
              |,{"cid":"1","cin":"2024-10-10","los":"2", "hid":"201", "mpt":"anotherToken"}],"activity":[]}""".stripMargin
          ),
          RawHeader(ServiceHeaders.AgForceExperiments.toString, "ACTB-MSE,B")
        )
      )

      val dfRequestCaptor     = new ArgumentCapture[PricingPropertiesRequestWrapper]
      val globalContextCaptor = new ArgumentCapture[GlobalContext]
      Mockito
        .verify(dfService)
        .process(dfRequestCaptor.capture, globalContextCaptor.capture, mockitoAny())(mockitoAny())
      val capturedDFRequests     = asScalaIterator(dfRequestCaptor.values.iterator()).toList
      val overridePricingRequest = capturedDFRequests.headOption.flatMap(_.externalUserContext)
      overridePricingRequest shouldBe None

      val capturedGlobalContext = asScalaIterator(globalContextCaptor.values.iterator()).toList
      val capturedAgMsePricingToken =
        capturedGlobalContext.headOption.map(_.getRawAgHeaders("AG-MSE-PRICING-TOKEN"))
      capturedDFRequests should not be empty
      capturedAgMsePricingToken.value shouldBe
        """{"property":[{"mpt":"msePricingToken","los":"3","cid":"1","hid":"101","cin":"2024-10-10"}],"activity":[]}"""

    }
  }

}
