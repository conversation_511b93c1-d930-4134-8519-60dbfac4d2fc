package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.mocks.client.MockJarvisServiceForKoddiJarvisMigration
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.common.util.context.ActiveABTests
import com.agoda.papi.search.complexsearch.sorting.jarvis.JarvisService
import com.agoda.papi.search.functionaltest.framework.model.TestSponsoredDetail
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.mockito.{ Mockito, MockitoSugar }
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchJarvisMigrationSpec extends PAPISearchFunctionalTest with MockitoSugar with BeforeAndAfterEach {
  val properties = Seq(101L, 102L, 103L)

  val abTest                = ActiveABTests()
  val koddiMigrationExpName = abTest.koddiJarvisMigration

  val mockJarvisServiceTest1 = spy(new MockJarvisServiceForKoddiJarvisMigration)

  val mockJarvisServiceTest2 = spy(
    new MockJarvisServiceForKoddiJarvisMigration(isKoddiJarvisMigrationExpEnabled = true)
  )

  override def beforeEach(): Unit = {
    super.beforeEach()
    Mockito.clearInvocations(mockJarvisServiceTest1)
    Mockito.clearInvocations(mockJarvisServiceTest2)
  }

  private def executeTestCase(
      propertyIds: Seq[Long],
      mockJarvisService: MockJarvisServiceForKoddiJarvisMigration,
      koddiJarvisMigrationExperimentVariant: Char
  ): Future[HttpResponse] = {
    val propertyInfoList     = generateDefaultPropertyInfoList(propertyIds.toList)
    val availabilityDataList = generateDefaultAvail(propertyIds.toList)

    val module = new GqlMockModule {
      mockCachePropertyInfoList(propertyInfoList)
      mockPushAvailability(Future.successful(availabilityDataList, Set.empty))
      bind[JarvisService] to mockJarvisService
    }

    val citySearchRequest = aValidCitySearchRequest

    val query = QueryBuilder.build(
      aValidSSRQueryWithSponsoredDetail,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val forceExpHeaders =
      s"$koddiMigrationExpName,$koddiJarvisMigrationExperimentVariant"
    val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, forceExpHeaders)

    executeSSRGraphQL(query = query, module = module, headers = headers)
  }

  private def shouldCallJarvisAssertion(mockJarvisService: MockJarvisServiceForKoddiJarvisMigration) =
    verify(mockJarvisService, times(1)).getRankingHotelResponse(
      any,
      any,
      any,
      any,
      any,
      any
    )(
      any,
      any,
      any
    )

  s"[DSINNO-1415] Koddi Jarvis migration " should {
    "DSINNO-1415 = A should return sponsoredDetail from Everest response" in {
      val respF = executeTestCase(
        propertyIds = properties,
        mockJarvisService = mockJarvisServiceTest1,
        koddiJarvisMigrationExperimentVariant = 'A'
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        shouldCallJarvisAssertion(mockJarvisServiceTest1)
        citySearchResponse.data.citySearch.properties.foreach { props =>
          props.foreach { prop =>
            prop.sponsoredDetail shouldBe Some(
              TestSponsoredDetail(
                trackingData = None,
                isShowSponsoredFlag = false,
                sponsoredType = "NoSponsored"
              )
            )
          }
        }
      }
    }

    "DSINNO-1415 = B should return sponsoredDetail from Jarvis response" in {
      val respF = executeTestCase(
        propertyIds = properties,
        mockJarvisService = mockJarvisServiceTest2,
        koddiJarvisMigrationExperimentVariant = 'B'
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        shouldCallJarvisAssertion(mockJarvisServiceTest2)
        citySearchResponse.data.citySearch.properties.foreach { props =>
          props.foreach { prop =>
            prop.sponsoredDetail shouldBe Some(
              TestSponsoredDetail(
                trackingData = Some(s"TRACKINGDATA${prop.propertyId}"),
                isShowSponsoredFlag = true,
                sponsoredType = "CostPerClick"
              )
            )
          }
        }
      }
    }
  }

}
