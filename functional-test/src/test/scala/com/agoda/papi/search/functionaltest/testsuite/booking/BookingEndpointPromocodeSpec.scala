package com.agoda.papi.search.functionaltest.testsuite.booking

import akka.http.scaladsl.model.{ HttpEntity, HttpResponse, StatusCode, StatusCodes }
import com.agoda.papi.search.api.util.PAPIExperimentManagerLogic
import com.agoda.papi.search.common.util.context.{ ActiveABTests, AllocationContext }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.fasterxml.jackson.databind.JsonMappingException
import framework.MockModule
import models.starfruit._
import org.mockito.MockitoSugar
import org.scalatest.Assertion
import org.scalatest.prop.TableDrivenPropertyChecks
import request._
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class BookingEndpointPromocodeSpec extends PAPISearchFunctionalTest with MockitoSugar with TableDrivenPropertyChecks {

  val activeAbTests = ActiveABTests()

  val mockAllocationContextB = {
    val m = mock[AllocationContext]
    when(m.stopFetchingCDBforPMCEnrichment).thenReturn('B')
    m
  }

  val selectedCampaign = aValidDFCampaign
    .withCampaignId(601)
    .withCampaignMessages(
      Some(
        Map(
          1 -> CampaignMessage(message = "ABC", params = Map("urgencyType" -> "1")),
          2 -> CampaignMessage(message = "DEF", params = Map("urgencyType" -> "2"))
        )
      )
    )
    .build()

  val campaignAwithCampaignMessage = aValidDFCampaign
    .withCampaignId(602)
    .withCampaignMessages(Some(Map(1 -> CampaignMessage(message = "GHI", params = Map("redemptions" -> "10K+")))))
    .build()

  val campaignBnoMessage = aValidDFCampaign
    .withCampaignId(603)
    .build()

  val campaignCEmptyMessage = aValidDFCampaign
    .withCampaignId(604)
    .withCampaignMessages(Some(Map(1 -> CampaignMessage(message = "", params = Map.empty))))
    .build()

  "BookingEndpoint /api/booking" should {
    "CampaignMessages" should {
      "return valid CampaignMessages when message exists Or message is empty Or no campaignMessage" in {
        val campaignFeature = CampaignFeatures(
          selected = Some(selectedCampaign),
          campaigns = List(
            campaignAwithCampaignMessage,
            campaignBnoMessage,
            campaignCEmptyMessage
          )
        )

        val dfHotel = aValidDFHotel
          .withHotelId(1001L)
          .withRooms(
            Seq(
              aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").withCampaignFeature(Some(campaignFeature)).build()
            )
          )
          .withBookingSummary(Some(aValidDFBookingSummary))
          .build()
        val contentMasterRooms = aValidRoomInformationResponse
          .withHotelId(1001L)
          .withMasterRoomResponse(
            Vector(
              aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
            )
          )
          .build()
        val request = aValidPropertyRequest
          .withSearchType(SearchTypes.Property)
          .withHotelIds(List(1001L))
          .withPricing(aValidPricingRequest)
          .withAdditionalExperimentForce(activeAbTests.stopFetchingCDBforPMCEnrichment, "B")
          .build()

        val module = new MockModule {
          mockDFResponse(Map(1001L -> dfHotel))
          mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        }

        val respF = executeBookingEndpoint(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties         = responseToProperties(resp)
          val campaignPromotions = properties.property.head.masterRooms.head.childrenRooms.head.campaignPromotions

          (campaignPromotions.get.map(_.campaignId) should contain).allOf(601, 602, 603, 604)

          // Verify the first campaign's multiple messages
          val selectedCampaign = campaignPromotions.get.find(_.campaignId == 601)
          selectedCampaign shouldBe defined
          selectedCampaign.get.campaignMessages shouldBe defined
          selectedCampaign.get.campaignMessages.get.size shouldBe 2
          selectedCampaign.get.campaignMessages.get(1).message shouldBe "ABC"
          selectedCampaign.get.campaignMessages.get(1).params shouldBe Map("urgencyType" -> "1")
          selectedCampaign.get.campaignMessages.get(2).message shouldBe "DEF"
          selectedCampaign.get.campaignMessages.get(2).params shouldBe Map("urgencyType" -> "2")

          // Verify the second campaign's single message
          val campaignA = campaignPromotions.get.find(_.campaignId == 602)
          campaignA shouldBe defined
          campaignA.get.campaignMessages shouldBe defined
          campaignA.get.campaignMessages.get.size shouldBe 1
          campaignA.get.campaignMessages.get(1).message shouldBe "GHI"
          campaignA.get.campaignMessages.get(1).params shouldBe Map("redemptions" -> "10K+")

          // Verify the third campaign has no messages
          val campaignB = campaignPromotions.get.find(_.campaignId == 603)
          campaignB shouldBe defined
          campaignB.get.campaignMessages shouldBe None

          // Verify the fourth campaign's empty message
          val campaignC = campaignPromotions.get.find(_.campaignId == 604)
          campaignC shouldBe defined
          campaignC.get.campaignMessages shouldBe defined
          campaignC.get.campaignMessages.get.size shouldBe 1
          campaignC.get.campaignMessages.get(1).message shouldBe ""
          campaignC.get.campaignMessages.get(1).params shouldBe Map.empty
        }
      }

      "return empty CampaignMessages when messageId key exists but value is null" in {
        val dfCampaign = aValidDFCampaign
          .withCampaignId(601)
          .withCampaignMessages(Some(Map(1 -> null)))

        val campaignFeature = CampaignFeatures(
          selected = None,
          campaigns = List(dfCampaign)
        )

        val dfHotel = aValidDFHotel
          .withHotelId(1001L)
          .withRooms(
            Seq(
              aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").withCampaignFeature(Some(campaignFeature)).build()
            )
          )
          .withBookingSummary(Some(aValidDFBookingSummary))
          .build()
        val contentMasterRooms = aValidRoomInformationResponse
          .withHotelId(1001L)
          .withMasterRoomResponse(
            Vector(
              aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
            )
          )
          .build()
        val request = aValidPropertyRequest
          .withHotelIds(List(1001L))
          .withPricing(aValidPricingRequest)
          .withAdditionalExperimentForce(activeAbTests.stopFetchingCDBforPMCEnrichment, "B")
          .build()

        val module = new MockModule {
          mockDFResponse(Map(1001L -> dfHotel))
          mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        }

        val respF = executeBookingEndpoint(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties         = responseToProperties(resp)
          val campaignPromotions = properties.property.head.masterRooms.head.childrenRooms.head.campaignPromotions

          // Verify the first campaign's multiple messages
          val campaign = campaignPromotions.get.find(_.campaignId == 601)
          campaign shouldBe defined
          campaign.get.campaignMessages shouldBe defined
          campaign.get.campaignMessages.get shouldBe Map.empty
        }
      }

      "PAPI client should throw deserialization exception when messageId key exists but value doesn't have message and params field ({}) as both are required field" in {
        val dfCampaign = aValidDFCampaign
          .withCampaignId(601)
          .withCampaignMessages(Some(Map(1 -> CampaignMessage(message = null, params = null))))

        val campaignFeature = CampaignFeatures(
          selected = None,
          campaigns = List(dfCampaign)
        )

        val dfHotel = aValidDFHotel
          .withHotelId(1001L)
          .withRooms(
            Seq(
              aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").withCampaignFeature(Some(campaignFeature)).build()
            )
          )
          .withBookingSummary(Some(aValidDFBookingSummary))
          .build()
        val contentMasterRooms = aValidRoomInformationResponse
          .withHotelId(1001L)
          .withMasterRoomResponse(
            Vector(
              aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
            )
          )
          .build()
        val request = aValidPropertyRequest
          .withHotelIds(List(1001L))
          .withPricing(aValidPricingRequest)
          .withAdditionalExperimentForce(activeAbTests.stopFetchingCDBforPMCEnrichment, "B")
          .build()

        val module = new MockModule {
          mockDFResponse(Map(1001L -> dfHotel))
          mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        }

        val respF = executeBookingEndpoint(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK

          val exception = intercept[Exception](responseToProperties(resp))
          exception shouldBe a[JsonMappingException]
          exception.getMessage should include("[OptionalMapDeserializer] Fail to deserialize value: {}")
        }
      }
    }

    "CampaignFeature" should {
      "return empty campaignPromotions when DF CampaignFeature is Option(null)" in {
        val dfHotel = aValidDFHotel
          .withHotelId(1001L)
          .withRooms(
            Seq(
              aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").withCampaignFeature(Option(null)).build()
            )
          )
          .withBookingSummary(Some(aValidDFBookingSummary))
          .build()
        val contentMasterRooms = aValidRoomInformationResponse
          .withHotelId(1001L)
          .withMasterRoomResponse(
            Vector(
              aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
            )
          )
          .build()
        val request = aValidPropertyRequest
          .withHotelIds(List(1001L))
          .withPricing(aValidPricingRequest)
          .withAdditionalExperimentForce(activeAbTests.stopFetchingCDBforPMCEnrichment, "B")
          .build()

        val module = new MockModule {
          mockDFResponse(Map(1001L -> dfHotel))
          mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        }

        val respF = executeBookingEndpoint(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties = responseToProperties(resp)
          val campaignPromotions = properties.property.head.masterRooms.head.childrenRooms
            .find(_.roomIdentifiers.contains("a"))
            .get
            .campaignPromotions

          campaignPromotions shouldBe Some(List.empty)
        }
      }

      "return empty campaignPromotions when have CampaignFeature but have no selected and campaigns" in {
        val campaignFeature = CampaignFeatures(
          selected = None,
          campaigns = List.empty
        )

        val dfHotel = aValidDFHotel
          .withHotelId(1001L)
          .withRooms(
            Seq(
              aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").withCampaignFeature(Some(campaignFeature)).build()
            )
          )
          .withBookingSummary(Some(aValidDFBookingSummary))
          .build()
        val contentMasterRooms = aValidRoomInformationResponse
          .withHotelId(1001L)
          .withMasterRoomResponse(
            Vector(
              aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
            )
          )
          .build()
        val request = aValidPropertyRequest
          .withHotelIds(List(1001L))
          .withPricing(aValidPricingRequest)
          .withAdditionalExperimentForce(activeAbTests.stopFetchingCDBforPMCEnrichment, "B")
          .build()

        val module = new MockModule {
          mockDFResponse(Map(1001L -> dfHotel))
          mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        }

        val respF = executeBookingEndpoint(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties = responseToProperties(resp)
          val campaignPromotions = properties.property.head.masterRooms.head.childrenRooms
            .find(_.roomIdentifiers.contains("a"))
            .get
            .campaignPromotions

          campaignPromotions shouldBe Some(List.empty)
        }
      }

      "return empty campaignPromotions when CampaignFeature is None" in {
        val dfHotel = aValidDFHotel
          .withHotelId(1001L)
          .withRooms(
            Seq(
              aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").withCampaignFeature(None).build()
            )
          )
          .withBookingSummary(Some(aValidDFBookingSummary))
          .build()
        val contentMasterRooms = aValidRoomInformationResponse
          .withHotelId(1001L)
          .withMasterRoomResponse(
            Vector(
              aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
            )
          )
          .build()
        val request = aValidPropertyRequest
          .withHotelIds(List(1001L))
          .withPricing(aValidPricingRequest)
          .withAdditionalExperimentForce(activeAbTests.stopFetchingCDBforPMCEnrichment, "B")
          .build()

        val module = new MockModule {
          mockDFResponse(Map(1001L -> dfHotel))
          mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
        }

        val respF = executeBookingEndpoint(request = request, module = module)

        whenReady(respF) { resp =>
          resp.status shouldBe StatusCodes.OK
          val properties = responseToProperties(resp)
          val campaignPromotions = properties.property.head.masterRooms.head.childrenRooms
            .find(_.roomIdentifiers.contains("a"))
            .get
            .campaignPromotions

          campaignPromotions shouldBe Some(List.empty)
        }
      }
    }
  }

}
