package com.agoda.papi.search.functionaltest.framework.model

import com.agoda.papi.search.internalmodel.response.{ DistanceEnrichInfo, DistanceInfo }
import com.agoda.papi.search.types.CountryIdType
import org.joda.time.DateTime
import types.CityIdType

case class TestCitySearchResponse(
    data: DataCitySearchResponse
)

case class DataCitySearchResponse(
    citySearch: TestComplexSearchResponse
)

case class TestRectangleSearchResponse(
    data: DataRectangleSearchResponse
)

case class DataRectangleSearchResponse(
    rectangleSearch: TestComplexSearchResponse
)

case class TestAreaSearchResponse(data: DataAreaSearchResponse)

case class DataAreaSearchResponse(
    areaSearch: TestComplexSearchResponse
)

case class TestLandmarkSearchResponse(data: DataLandmarkSearchResponse)

case class DataLandmarkSearchResponse(
    landmarkSearch: TestComplexSearchResponse
)

case class TestRadiusSearchResponse(data: DataRadiusSearchResponse)

case class DataRadiusSearchResponse(
    radiusSearch: TestComplexSearchResponse
)

case class TestRegionSearchResponse(data: DataRegionSearchResponse)

case class DataRegionSearchResponse(
    regionSearch: TestComplexSearchResponse
)

case class TestTopAreaSearchResponse(data: DataTopAreaSearchResponse)

case class DataTopAreaSearchResponse(
    topAreaSearch: TestComplexSearchResponse
)

case class TestCityCenterSearchResponse(data: DataCityCenterSearchResponse)

case class DataCityCenterSearchResponse(
    cityCenterSearch: TestComplexSearchResponse
)

case class TestStateSearchResponse(data: DataStateSearchResponse)

case class DataStateSearchResponse(
    stateSearch: TestComplexSearchResponse
)

case class TestComplexSearchResponse(
    searchResult: Option[TestSearchResult],
    properties: Option[List[TestPropertySummary]],
    aggregation: Option[TestAggregationResponse],
    geoPlaces: Option[List[TestGeoPlace]],
    extraAgodaHomes: Option[List[TestPropertySummary]],
    highlyRatedAgodaHomes: Option[List[TestPropertySummary]],
    bannerCampaignInfo: Option[List[TestBannerCampaignInfo]],
    featuredPulseProperties: Option[List[TestPropertySummary]]
)

case class TestPropertySummary(
    propertyId: Long,
    propertyResultType: String,
    enrichment: Option[TestPropertySummaryEnrichment],
    pricing: Option[TestHotelPricing],
    content: Option[TestContentSummary],
    soldOut: Option[TestSoldOutSummary],
    metaLab: Option[TestPropertyAttributesResponse],
    dateless: Option[TestDatelessSummary],
    sponsoredDetail: Option[TestSponsoredDetail]
)

case class TestPropertySummaryEnrichment(
    pricingBadges: Option[TestPricingBadges],
    topSellingPoint: List[TestTopSellingPointInfo] = List.empty,
    uniqueSellingPoint: Option[List[TestUniqueSellingPointInfo]],
    distance: Option[TestDistanceEnrichInfo]
)

case class TestDistanceEnrichInfo(
    fromUser: Option[TestDistanceInfo] = None,
    fromExtraProperty: Option[TestDistanceInfo] = None,
    fromLandmark: Option[TestDistanceInfo] = None,
    fromAreaCenter: Option[TestDistanceInfo] = None
)

case class TestDistanceInfo(locationId: Option[Long], distanceM: Option[Double], locationName: Option[String])

case class TestTopSellingPointInfo(
    tspType: String,
    value: Option[Double] = None,
    cmsId: Option[Int] = None
)

case class TestUniqueSellingPointInfo(
    uspType: Option[String]
)

case class TestPricingBadges(badges: List[String])

case class TestContentSummary(
    nonHotelAccommodation: Option[TestContentNonHotelAccommodation],
    reviews: Option[TestContentReviewResponse],
    highlight: Option[TestCityContentHighlights],
    synopsis: Option[TestContentSynopsis],
    informationSummary: Option[TestContentInformationSummary],
    localInformation: Option[TestContentLocalInformation],
    images: Option[TestContentImagesResponse]
)

case class TestCityContentHighlights(favoriteFeatures: Option[TestContentFavoriteFeatures])

case class TestContentFavoriteFeatures(features: List[TestContentHighlight])

case class TestContentHighlight(id: Int)

case class TestContentSynopsis(aggregatedFacilities: List[TestContentAggregatedFacility])

case class TestContentAggregatedFacility(id: Int)

case class TestContentNonHotelAccommodation(masterRooms: Option[List[TestContentMasterRoom]])

case class TestContentMasterRoom(roomSizeSqm: Double, noOfBeds: Int, noOfBedrooms: Int, propertyId: Long)

case class TestContentReviewResponse(contentReview: List[TestContentReview])

case class TestContentReview(cumulative: Option[TestContentReviewTotal])

case class TestContentReviewTotal(reviewCount: Long, score: Double)

case class TestSoldOutSummary(soldOutPrice: TestSoldOutPrice)

case class TestSoldOutPrice(cheapestPrice: Double, averagePrice: Double)

case class TestSearchResult(
    searchInfo: TestSearchInfo
)

case class TestSearchInfo(
    totalFilteredHotels: Long,
    hasEscapesPackage: Boolean,
    totalActiveHotels: Long,
    searchStatus: Option[TestSearchStatusAndCriteria],
    objectInfo: Option[TestObjectExtraInfo],
    pollingInfoResponse: Option[TestPollingInfoResponse]
)

case class TestSearchStatusAndCriteria(searchStatus: String, searchCriteria: TestSearchCriteria)

case class TestObjectExtraInfo(
    cityId: Option[CityIdType],
    cityName: Option[String],
    cityEnglishName: String,
    countryId: Option[CountryIdType],
    countryName: Option[String],
    countryEnglishName: String,
    mapZoomLevel: Int,
    gmtOffset: Int,
    gmtOffsetMinutes: Int,
    centerLatitude: Double,
    centerLongitude: Double,
    mapLatitude: Double,
    mapLongitude: Double,
    objectName: Option[String],
    defaultRadius: Int,
    mapDisplayInfo: Option[TestMapDisplayInfo],
    shouldShowMap: Boolean,
    cityCenterPolygon: Option[TestPolygonCoordinatesResponse],
    wlPreferredCityName: Option[String],
    wlPreferredCountryName: Option[String]
)

case class TestMapDisplayInfo(mapZoomLevel: Int, mapLatitude: Double, mapLongitude: Double)

case class TestPolygonCoordinatesResponse(
    geoPoints: List[TestGeoPointResponse],
    touristAreaCenterPoint: TestGeoPointResponse
)

case class TestGeoPointResponse(lat: Double, long: Double)

case class TestAggregationResponse(
    matrixGroupResults: List[TestMatrixGroupResult],
    propertyLandmarkInfo: Option[List[TestPropertyLandmarkInfo]]
)

case class TestSearchCriteria(
    checkIn: DateTime
)

case class TestMatrixGroupResult(matrixGroup: String, matrixItemResults: List[TestMatrixItemResult])

case class TestMatrixItemResult(
    id: Long,
    name: String,
    count: Long,
    syncId: Option[String] = None
)

case class TestPropertyLandmarkInfo(
    propertyId: Long,
    distance: Option[Double],
    landmarkName: Option[String],
    landmarkInfo: List[TestLandmarkInfo],
    nearestLandmarkInfo: Option[TestLandmarkInfo]
)

case class TestLandmarkInfo(
    landmarkId: Long,
    landmarkCityId: Long,
    landmarkSubtypeCategoryId: Long,
    landmarkName: String,
    landmarkDistance: Double
)

case class TestPropertyAttributesResponse(propertyId: Long, attributes: List[TestPropertyAttribute])

case class TestHotelPricing(
    cheapestRoomOffer: Option[TestCheapestRoomOffer],
    externalLoyaltyDisplay: Option[TestExternalLoyaltyDisplay],
    dayuseInfo: Option[TestDayUseInfo]
)

case class TestCheapestRoomOffer(uid: Option[String], pricing: List[TestDFCurrencyPrice])

case class TestDayUseInfo(dayuseCheckInTimes: Option[Seq[TestDayUseCheckInTime]])

case class TestDayUseCheckInTime(hourOfDay: Option[Int], minuteOfHour: Option[Int])

case class TestDFCurrencyPrice(price: Option[TestDFRoomPricing])

case class TestDFRoomPricing(
    perBook: Option[TestPriceType],
    perRoomPerNight: Option[TestPriceType],
    perNight: Option[TestPriceType]
)

case class TestPriceType(exclusive: Option[TestDFPrices], inclusive: Option[TestDFPrices])

case class TestDFPrices(display: Double)

case class TestExternalLoyaltyDisplay(items: Seq[TestExternalLoyaltyDisplayItem])

case class TestExternalLoyaltyDisplayItem(
    tierId: Option[String],
    tierName: Option[String],
    tierDescription: Option[String]
)

case class TestDatelessSummary(price: Option[TestSoldOutPrice])

case class TestGeoPlace(
    id: Int,
    landmarkTypeId: Int,
    placeType: Int,
    name: String,
    abbreviation: String,
    latitude: Double,
    longitude: Double
)

case class TestPollingInfoResponse(
    pollId: String,
    pollAttempt: Int,
    shouldPoll: Boolean,
    suggestedPollIntervalMs: Int
)

case class TestSponsoredDetail(
    isShowSponsoredFlag: Boolean,
    sponsoredType: String,
    trackingData: Option[String]
)

case class TestContentLocalInformation(
    transportation: Option[TestContentTransportation],
    hasAirportTransfer: Option[Boolean]
)

case class TestContentTransportation(airports: Option[List[TestContentAirport]])

case class TestContentAirport(
    name: Option[String],
    distanceLongText: Option[String],
    distanceShortText: Option[String],
    mode: Option[List[TestContentTransportationMode]]
)

case class TestContentTransportationMode(
    name: Option[String],
    duration: Option[Int],
    modeIcon: Option[String]
)
