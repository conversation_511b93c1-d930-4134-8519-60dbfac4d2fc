package com.agoda.papi.search.functionaltest.testsuite.propertydetails

import akka.http.scaladsl.model.StatusCodes
import com.agoda.content.models.propertycontent.ContentImagesResponse
import com.agoda.core.search.framework.builders.TestDataBuilders.{
  aValidContentImageRequest,
  aValidPropertyDetailsRequest
}
import com.agoda.papi.search.common.util.ContentDataExample
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.property.search.framework.GqlMockModule

class PropertyDetailsSearchContentImagesSpec extends PAPISearchFunctionalTest {

// Migrate it/src/test/resources/features/property-details/IT-ImagesGQL.feature
  "Property Details Search" should {

    "return main image and images of the hotel when requested" in {
      val query = aValidPropertyDetailsSearchRequest
        .withPropertyDetailsRequest(aValidPropertyDetailsRequest.copy(propertyIds = List(1001L)))
        .withContentImages(aValidContentImageRequest)
        .build()
      val aValidContentHotelImageResponse = ContentDataExample.aValidContentImageResponse.copy(caption = "hotelImg")
      val aValidContentMainImageResponse  = ContentDataExample.aValidContentImageResponse

      val module = new GqlMockModule {
        mockImageService(
          Map(
            1001L -> ContentImagesResponse(
              1001L,
              Some(aValidContentMainImageResponse),
              Vector(aValidContentHotelImageResponse),
              None,
              Some(Vector(ContentDataExample.aValidContentVideoResponse)),
              None,
              None,
              None
            )
          )
        )
      }

      val respF = executePropertyDetailsSearchGraphQL(query, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val propertyDetailsSearchResponse = responseToPropertyDetailsSearchResponse(resp)
        val propertyIds = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails.map(_.propertyId)
        val contentImages = propertyDetailsSearchResponse.data.propertyDetailsSearch.propertyDetails
          .flatMap(_.contentDetail.flatMap(_.contentImages))
        propertyIds should contain theSameElementsAs List(1001L)
        contentImages.map(
          _.mainImage.get.caption shouldBe Some(aValidContentMainImageResponse.caption)
        ) // verify has main Image
        contentImages.flatMap(_.hotelImages.map(_.caption)) should contain theSameElementsAs List(
          Some(aValidContentHotelImageResponse.caption)
        ) // verify has hotel Image
      }
    }
  }

}
