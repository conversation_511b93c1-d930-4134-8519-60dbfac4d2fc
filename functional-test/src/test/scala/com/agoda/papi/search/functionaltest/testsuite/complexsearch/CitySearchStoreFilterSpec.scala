package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.headers.RawHeader
import akka.http.scaladsl.model.{ HttpHeader, HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.framework.constants.TestStoreIds._
import com.agoda.core.search.framework.dexsync.ElasticHotelInfo
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.enumeration.matrix.FilterKeys
import com.agoda.core.search.models.{ FilterRequest, IdsFilterRequest }
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchStoreFilterSpec extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override val docs: List[ElasticHotelInfo] = List(
    aValidElasticHotelInfo.withHotelId(hotel1).withStoreIds(List(storeId1)).build(),
    aValidElasticHotelInfo.withHotelId(hotel2).withStoreIds(List(storeId2, storeId3)).build(),
    aValidElasticHotelInfo.withHotelId(hotel3).withStoreIds(List()).build(),
    aValidElasticHotelInfo.withHotelId(hotel4).withStoreIds(List(storeId1, storeId2, storeId3)).build()
  )

  private val hotelIds = (hotel1 to hotel4).toList

  private def executeTestCase(
      hotelIds: List[Long],
      storeFilters: List[Long],
      headers: Seq[HttpHeader] = aValidSSRHttpHeaders
  ): Future[HttpResponse] = {
    val searchContext = aValidSearchContext.copy(endpointSearchType = Some(SearchTypes.CitySearch))
    val citySearchRequest = aValidCitySearchRequest
      .withSearchContext(searchContext)
      .withSearchCriteria(aValidSearchCriteria.build())
      .withFilterRequest(
        FilterRequest(
          idsFilters = Some(
            List(
              IdsFilterRequest(
                FilterKeys.StoreIds,
                ids = storeFilters
              )
            )
          )
        )
      )
      .build()

    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val avail            = generateDefaultAvail(hotelIds)
    val propertyMetadata = generateDefaultPropertyInfoList(hotelIds)

    val mockModule = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockESMatrixService(client)
    }

    executeSSRGraphQL(
      query,
      mockModule,
      headers
    )
  }

  "City search" should {
    "return properties for 1 store filters when JTBFP-1293 A" in {
      val respF = executeTestCase(hotelIds, List(storeId1))
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels shouldBe 2
        citySearchResponse.data.citySearch.properties.get.map(_.propertyId) should equal(List(hotel1, hotel4))
      }
    }
    "return properties for 1 store filters when JTBFP-1293 B" in {
      val respF = executeTestCase(
        hotelIds,
        List(storeId1),
        headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "JTBFP-1293,B")
      )
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels shouldBe 2
        citySearchResponse.data.citySearch.properties.get.map(_.propertyId) should equal(List(hotel1, hotel4))
      }
    }
    "return properties for 2 store filters when JTBFP-1293 A" in {
      val respF = executeTestCase(hotelIds, List(storeId1, storeId2))
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels shouldBe 3
        citySearchResponse.data.citySearch.properties.get.map(_.propertyId) should equal(List(hotel1, hotel2, hotel4))
      }
    }
    "return properties for 2 store filters when JTBFP-1293 B" in {
      val respF = executeTestCase(
        hotelIds,
        List(storeId1, storeId2),
        headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "JTBFP-1293,B")
      )
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels shouldBe 3
        citySearchResponse.data.citySearch.properties.get.map(_.propertyId) should equal(List(hotel1, hotel2, hotel4))
      }
    }
    "return no properties for missing filter" in {
      val respF = executeTestCase(hotelIds, List(storeId4))
      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse = responseToCitySearchResponse(resp)
        citySearchResponse.data.citySearch.searchResult.get.searchInfo.totalFilteredHotels shouldBe 0
        citySearchResponse.data.citySearch.properties.get.map(_.propertyId) should equal(List.empty)
      }
    }
  }

}
