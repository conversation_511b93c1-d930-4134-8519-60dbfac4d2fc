package com.agoda.papi.search.functionaltest.testsuite.common

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.models.request.SearchContext
import com.agoda.papi.search.common.service.WhiteLabelService
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextHolder }
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.property.service.ClusterService.DFPropertyWithCacheTokenServiceWrapper
import framework.MockModule
import framework.testservice.DFPropertyWithCacheTokenServiceWrapperTest
import models.starfruit.{ PropertySearchRequest, SuggestedPrice }
import org.mockito.{ Mockito, MockitoSugar }
import org.scalatest.prop.TableDrivenPropertyChecks
import org.specs2.mock.mockito.ArgumentCapture
import request.PropertyRequest
import util.builders.TestDataBuilders._
import scaldi.Module

import scala.concurrent.Future
import scala.jdk.CollectionConverters.asScalaIterator

trait CommonSearchContextForRestEndpointsTestSuit
    extends PAPISearchFunctionalTest with MockitoSugar with TableDrivenPropertyChecks {

  val activeABTest = new ActiveABTests()

  private def getDfHotel(hotelId: Long) =
    aValidDFHotel
      .withHotelId(hotelId)
      .withRooms(Seq(aValidDFRoom.withRoomTypeId(1L).withRoomIdentifier("a").build()))
      .build()

  private def getContentMasterRooms(hotelId: Long) =
    aValidRoomInformationResponse
      .withHotelId(hotelId)
      .withMasterRoomResponse(
        Vector(
          aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build()).build()
        )
      )
      .build()

  val aHotelId = 1001L

  def executeFxiTestCases(
      baseRequest: PropertyRequest,
      executeFunc: (PropertyRequest, Module) => Future[HttpResponse]
  ) =
    Table(
      (
        activeABTest.fixMissingSearchContextInRestEndpointsBug,
        "isDisableFxi from WL Service",
        "expected search context value in WL Service",
        "expected turnOffFxi flag send to DF"
      ),
      ("A", true, "None", Some(true)),
      ("A", false, "None", Some(false)),
      ("B", true, "None", Some(true)),
      ("B", false, "None", Some(false))
    ).forEvery { (expVariant, isDisableFxiReturn, expectedSearchContext, expectedTurnOffFxi) =>
      s"[${activeABTest.fixMissingSearchContextInRestEndpointsBug}=$expVariant] When Whitelabel Service isDisableFxi return $isDisableFxiReturn, " +
        s"expect $expectedSearchContext search context in WL Service call and " +
        s"expect $expectedTurnOffFxi turnOffFxi flag send to DF" in {

          val whitelabelService = mock[WhiteLabelService]
          when(whitelabelService.isValidWhiteLabelKey(any, any)).thenReturn(true)
          when(whitelabelService.isDisableFxi(any)).thenReturn(isDisableFxiReturn)

          var dfServiceRecorder: DFPropertyWithCacheTokenServiceWrapper = null

          val module = new MockModule {
            mockDFResponse(Map(aHotelId -> getDfHotel(aHotelId)))
            mockRoomInformationService(Future.successful(Seq(getContentMasterRooms(aHotelId))))
            bind[WhiteLabelService] to whitelabelService
            bind[DFPropertyWithCacheTokenServiceWrapper].to {
              lazy val wrapper = new DFPropertyWithCacheTokenServiceWrapperTest
              val spyMock      = Mockito.spy(wrapper)
              dfServiceRecorder = spyMock
              spyMock
            }
          }

          val requestWithExp = baseRequest
            .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, expVariant)
            .build()

          val respF = executeFunc(requestWithExp, module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK

            val wlServiceCaptor = new ArgumentCapture[ContextHolder]()
            verify(whitelabelService, times(1)).isDisableFxi(wlServiceCaptor.capture)
            assertSearchContext(wlServiceCaptor.value.searchContext, expectedSearchContext)

            val dfRequestCaptor = new ArgumentCapture[PropertySearchRequest]
            verify(dfServiceRecorder, times(1)).serviceCallWithRetries(dfRequestCaptor.capture, any)(any)
            val capturedDFRequests = asScalaIterator(dfRequestCaptor.values.iterator()).toList
            capturedDFRequests.foreach(r => r.pricing.features.turnOffFxi shouldBe expectedTurnOffFxi)
          }
        }
    }

  def executeSuggestPriceTestCases(
      baseRequest: PropertyRequest,
      executeFunc: (PropertyRequest, Module) => Future[HttpResponse]
  ) =
    Table(
      (
        activeABTest.fixMissingSearchContextInRestEndpointsBug,
        "isAllowedExclusivePrice from WL Service",
        "expected search context value in WL Service",
        "expected suggest price send to DF"
      ),
      ("A", true, "None", SuggestedPrice.Exclusive),
      ("A", false, "None", SuggestedPrice.AllInclusive),
      ("B", true, "None", SuggestedPrice.Exclusive),
      ("B", false, "None", SuggestedPrice.AllInclusive)
    ).forEvery((expVariant, isAllowedExclusivePriceReturn, expectedSearchContext, expectedSuggestPrice) =>
      s"[${activeABTest.fixMissingSearchContextInRestEndpointsBug}=$expVariant] When Whitelabel Service isAllowedExclusivePriceReturn return $isAllowedExclusivePriceReturn, " +
        s"expect $expectedSearchContext search context in WL Service call and " +
        s"expect $expectedSuggestPrice SuggestPrice send to DF" in {

          val whitelabelService = mock[WhiteLabelService]
          when(whitelabelService.isValidWhiteLabelKey(any, any)).thenReturn(true)
          when(whitelabelService.isAllowedExclusivePrice(any)).thenReturn(isAllowedExclusivePriceReturn)

          var dfServiceRecorder: DFPropertyWithCacheTokenServiceWrapper = null

          val module = new MockModule {
            mockDFResponse(Map(aHotelId -> getDfHotel(aHotelId)))
            mockRoomInformationService(Future.successful(Seq(getContentMasterRooms(aHotelId))))
            bind[WhiteLabelService] to whitelabelService
            bind[DFPropertyWithCacheTokenServiceWrapper].to {
              lazy val wrapper = new DFPropertyWithCacheTokenServiceWrapperTest
              val spyMock      = Mockito.spy(wrapper)
              dfServiceRecorder = spyMock
              spyMock
            }
          }

          val requestWithExp = baseRequest
            .withAdditionalExperimentForce(activeABTest.fixMissingSearchContextInRestEndpointsBug, expVariant)
            .build()

          val respF = executeFunc(requestWithExp, module)

          whenReady(respF) { resp =>
            resp.status shouldBe StatusCodes.OK

            val wlServiceCaptor = new ArgumentCapture[ContextHolder]()
            verify(whitelabelService, times(2)).isAllowedExclusivePrice(wlServiceCaptor.capture)

            val capturedContexts = asScalaIterator(wlServiceCaptor.values.iterator()).toList
            capturedContexts.map(c => assertSearchContext(c.searchContext, expectedSearchContext))

            val dfRequestCaptor = new ArgumentCapture[PropertySearchRequest]
            verify(dfServiceRecorder, times(1)).serviceCallWithRetries(dfRequestCaptor.capture, any)(any)
            val capturedDFRequests = asScalaIterator(dfRequestCaptor.values.iterator()).toList
            capturedDFRequests.foreach(r => r.suggestedPrice shouldBe expectedSuggestPrice.toString())
          }
        }
    )

  private def assertSearchContext(searchContext: Option[SearchContext], expectedSearchContext: String) =
    if (expectedSearchContext == "None")
      searchContext shouldBe empty
    else
      searchContext shouldBe defined

}
