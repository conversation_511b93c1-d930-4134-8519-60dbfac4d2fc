package com.agoda.papi.search.functionaltest.testsuite.property

import akka.http.scaladsl.model.StatusCodes
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import framework.MockModule
import models.starfruit.ExtraInformation
import request.SearchTypes
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class PricingExtraInfoSpec extends PAPISearchFunctionalTest {

  "PropertySearch /api/property" should {
    "return property with correct pricing extra info including totalPriceMandatoryWithoutTaxAndFee" in {
      val extraInfo = ExtraInformation(
        10.0,
        20.0,
        50.0,
        15.0,
        15.0,
        15.0,
        30.0,
        45.0,
        None,
        None,
        None,
        None,
        None,
        Some(20.0)
      )

      val dfPricing = aValidDFPricing.copy(extraInfo = Some(extraInfo))
      val dfRoom = aValidDFRoom
        .withRoomTypeId(1L)
        .withRoomIdentifier("a")
        .withPricing(Map("USD" -> dfPricing))
        .build()

      val dfHotel = aValidDFHotel
        .withHotelId(1001L)
        .withRooms(Seq(dfRoom))
        .build()

      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(1001L)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse
              .withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build())
              .build()
          )
        )
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1001L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1001L))
        .withPricing(aValidPricingRequest)
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)

        properties.property should have size 1
        val property = properties.property.head
        property.masterRooms should have size 1
        val masterRoom = property.masterRooms.head
        masterRoom.childrenRooms should have size 1
        val childRoom = masterRoom.childrenRooms.head

        // Verify the pricing extra info
        childRoom.pricing should not be empty
        val usdPricing = childRoom.pricing("USD")
        usdPricing.extraInfo should be(defined)

        val pricingExtraInfo = usdPricing.extraInfo.get
        pricingExtraInfo.roomTaxAndFeePRPN shouldBe 10.0
        pricingExtraInfo.roomTaxAndFeePN shouldBe 20.0
        pricingExtraInfo.roomTaxAndFeeWithExcludedPB shouldBe 50.0
        pricingExtraInfo.marginPerNightUSD shouldBe 15.0
        pricingExtraInfo.roomWithExtraBeds shouldBe 30.0
        pricingExtraInfo.totalPriceWithoutTaxAndFee shouldBe 45.0
        pricingExtraInfo.totalPriceMandatoryWithoutTaxAndFee shouldBe Some(20.0d)
      }
    }

    "return property with no pricing extra info when extraInfo is not provided" in {
      val dfRoom = aValidDFRoom
        .withRoomTypeId(1L)
        .withRoomIdentifier("b")
        .withPricing(Map("USD" -> aValidDFPricing))
        .build()

      val dfHotel = aValidDFHotel
        .withHotelId(1002L)
        .withRooms(Seq(dfRoom))
        .build()

      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(1002L)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse
              .withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build())
              .build()
          )
        )
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1002L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1002L))
        .withPricing(aValidPricingRequest)
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)

        properties.property should have size 1
        val property = properties.property.head
        property.masterRooms should have size 1
        val masterRoom = property.masterRooms.head
        masterRoom.childrenRooms should have size 1
        val childRoom = masterRoom.childrenRooms.head

        // Verify that pricing info exists but extra info is None
        childRoom.pricing should not be empty
        val usdPricing = childRoom.pricing("USD")
        usdPricing.extraInfo should be(None)
      }
    }

    "return property with partial pricing extra info when some fields are missing" in {
      val partialExtraInfo = ExtraInformation(
        25.0,
        30.0,
        0.0,
        12.5,
        12.5,
        12.5,
        0.0,
        35.0,
        None,
        None,
        None,
        None,
        None,
        None // totalPriceMandatoryWithoutTaxAndFee is not set
      )

      val dfPricing = aValidDFPricing.copy(extraInfo = Some(partialExtraInfo))
      val dfRoom = aValidDFRoom
        .withRoomTypeId(1L)
        .withRoomIdentifier("c")
        .withPricing(Map("USD" -> dfPricing))
        .build()

      val dfHotel = aValidDFHotel
        .withHotelId(1003L)
        .withRooms(Seq(dfRoom))
        .build()

      val contentMasterRooms = aValidRoomInformationResponse
        .withHotelId(1003L)
        .withMasterRoomResponse(
          Vector(
            aValidMasterRoomResponse
              .withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(1L).build())
              .build()
          )
        )
        .build()

      val module = new MockModule {
        mockDFResponse(Map(1003L -> dfHotel))
        mockRoomInformationService(Future.successful(Seq(contentMasterRooms)))
      }

      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(1003L))
        .withPricing(aValidPricingRequest)
        .build()

      val respF = executePropertySearch(request = request, module = module)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val properties = responseToProperties(resp)

        properties.property should have size 1
        val property = properties.property.head
        property.masterRooms should have size 1
        val masterRoom = property.masterRooms.head
        masterRoom.childrenRooms should have size 1
        val childRoom = masterRoom.childrenRooms.head

        // Verify the pricing extra info
        childRoom.pricing should not be empty
        val usdPricing = childRoom.pricing("USD")
        usdPricing.extraInfo should be(defined)

        val pricingExtraInfo = usdPricing.extraInfo.get
        pricingExtraInfo.roomTaxAndFeePRPN shouldBe 25.0
        pricingExtraInfo.roomTaxAndFeePN shouldBe 30.0
        pricingExtraInfo.totalPriceWithoutTaxAndFee shouldBe 35.0
        pricingExtraInfo.totalPriceMandatoryWithoutTaxAndFee should be(None)
      }
    }
  }

}
