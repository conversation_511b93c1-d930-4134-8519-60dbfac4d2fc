import akka.http.scaladsl.model.headers.RawHeader
import com.agoda.papi.search.common.util.CoreDataExamples.{ aValidCitySearchRequest, aValidSearchCriteria }
import akka.http.scaladsl.model.{ HttpHeader, HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders.{
  aValidCachePropertyInfo,
  aValidPushCityAvailResponse,
  toPushCityAvailResponseBuilder
}
import com.agoda.core.search.framework.constants.TestHotels.hotel1
import com.agoda.core.search.models.builders.TestModelsBuilders.toCitySearchRequest
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class CitySearchIgnoreRequestedNumberOfRoomsForNhaSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {
  override def beforeAll(): Unit = mockEsBeforeAll()

  private def executeTestCase(
      propertyIds: Seq[Long],
      headers: Seq[HttpHeader]
  ): Future[HttpResponse] = {
    val mockDFResponseMap = Map(
      hotel1 -> DFDataExample
        .aValidPAPIHotelPricing(hotel1)
    )
    val citySearchRequest = aValidCitySearchRequest
      .withSearchCriteria(
        aValidSearchCriteria
          .copy(itineraryCriteria = aValidSearchCriteria.itineraryCriteria.map(_.copy(rooms = 2, adults = 3)))
      )
      .build()
    val query = QueryBuilder.build(
      aValidSSRQuery,
      citySearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )
    val meta = PropertyInfoList(Vector(aValidCachePropertyInfo.withHotelId(hotel1).withIsNhaForDisplay(true)))
    val module = new GqlMockModule {
      mockCachePropertyInfoList(meta)
      mockDfService(mockDFResponseMap)
      mockCityAvailReadRepository(
        pushResult = Seq(
          aValidPushCityAvailResponse
            .withHotelId(hotel1.toInt)
            .withMaxOccupancy(Some(4))
            .withRemainingRooms(Some(1))
            .build()
        ),
        pullResult = Seq.empty
      )
      mockWhiteLabelService(isAgodaHomeResponse = true)
    }
    executeSSRGraphQL(
      query,
      module,
      headers
    )
  }

  "City Search" should {
    "Return NHA properties even where remainingRooms < requestedRooms when NHAFE-1091 is B and agoda WL" in {
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "NHAFE-1091,B") :+
        RawHeader(ServiceHeaders.AgWhiteLabelKey.toString, "F1A5905F-9620-45E5-9D91-D251C07E0B42")
      val respF = executeTestCase(
        propertyIds = List(hotel1),
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse  = responseToCitySearchResponse(resp)
        val propertySummaryList = citySearchResponse.data.citySearch.properties.get

        val nhaProperties = propertySummaryList.map(_.propertyId)
        nhaProperties shouldBe List(101)
      }
    }

    "Return no NHA properties even where remainingRooms < requestedRooms, NHAFE-1091 is B but whitel-label is non agoda" in {
      val headers = aValidSSRHttpHeaders :+
        RawHeader(ServiceHeaders.AgForceExperiments.toString, "NHAFE-1091,B") :+
        RawHeader(ServiceHeaders.AgWhiteLabelKey.toString, "46BFD421-3F38-45FD-A1CF-58766401AFDC")

      val respF = executeTestCase(
        propertyIds = List(hotel1),
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse  = responseToCitySearchResponse(resp)
        val propertySummaryList = citySearchResponse.data.citySearch.properties.get

        val nhaProperties = propertySummaryList.map(_.propertyId)
        nhaProperties shouldBe empty
      }
    }

    "Return no NHA properties even where remainingRooms < requestedRooms when NHAFE-1091 is A" in {
      val headers = aValidSSRHttpHeaders :+ RawHeader(ServiceHeaders.AgForceExperiments.toString, "NHAFE-1091,A") :+
        RawHeader(ServiceHeaders.AgWhiteLabelKey.toString, "F1A5905F-9620-45E5-9D91-D251C07E0B42")
      val respF = executeTestCase(
        propertyIds = List(hotel1),
        headers
      )

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val citySearchResponse  = responseToCitySearchResponse(resp)
        val propertySummaryList = citySearchResponse.data.citySearch.properties.get

        val nhaProperties = propertySummaryList.map(_.propertyId)
        nhaProperties shouldBe empty
      }
    }
  }

}
