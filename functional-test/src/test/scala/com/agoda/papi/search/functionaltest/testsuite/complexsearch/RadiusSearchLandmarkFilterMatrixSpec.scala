package com.agoda.papi.search.functionaltest.testsuite.complexsearch

import akka.http.scaladsl.model.{ HttpResponse, StatusCodes }
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.enumeration.SearchTypes
import com.agoda.core.search.models.enumeration.matrix.MatrixGroupTypes
import com.agoda.core.search.models.request.MatrixGroupRequest
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.complexsearch.model.ESPropertyMetaData
import com.agoda.papi.search.complexsearch.util.elasticsearch.MockElasticService
import com.agoda.papi.search.functionaltest.framework.model.TestMatrixItemResult
import com.agoda.papi.search.functionaltest.framework.PAPISearchFunctionalTest
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.papi.search.types.WithHotelIdMap
import com.agoda.property.search.framework.GqlMockModule
import org.scalatest.BeforeAndAfterEach

import scala.concurrent.Future

class RadiusSearchLandmarkFilterMatrixSpec
    extends PAPISearchFunctionalTest with MockElasticService with BeforeAndAfterEach {

  override def beforeAll(): Unit =
    mockEsBeforeAll()

  val landmark1 = 32423L
  val landmark2 = 32425L
  val landmark3 = 32448L

  override val docs = List(
    aValidElasticHotelInfo
      .withHotelId(hotel1)
      .withLandmarkIds(List(landmark1, landmark2, landmark3))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel2)
      .withLandmarkIds(List(landmark1, landmark2))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel3)
      .withLandmarkIds(List(landmark1))
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel4)
      .build(),
    aValidElasticHotelInfo
      .withHotelId(hotel5)
      .build()
  )

  val propertyIds = List(hotel1, hotel2, hotel3, hotel4, hotel5)

  private val hotelPropertyMetaData: WithHotelIdMap[ESPropertyMetaData] = propertyIds.map { h =>
    h -> aValidESPropertyMetaData.withHotelId(h).build()
  }(collection.breakOut)

  private def executeTestCase(
      propertyIds: List[Long],
      matrixGroupRequests: List[MatrixGroupRequest]
  ): Future[HttpResponse] = {
    val searchContext = aValidSearchContext.copy(endpointSearchType = Some(SearchTypes.RadiusSearch))
    val radiusSearchRequest = aValidRadiusSearchRequest
      .withSearchContext(searchContext)
      .withMatrixGroup(Some(matrixGroupRequests))
      .build()

    val query = QueryBuilder.build(
      aValidRadiusSearchQuery,
      radiusSearchRequest,
      DFDataExample.aPricingRequestParameters,
      contentSummaryRequest
    )

    val avail            = generateDefaultAvail(propertyIds)
    val propertyMetadata = generateDefaultPropertyInfoList(propertyIds)

    val mockModule = new GqlMockModule {
      mockCachePropertyInfoList(propertyMetadata)
      mockPushAvailability(Future.successful((avail, Set.empty)))
      mockESMatrixService(client)
      mockAggregationService(Future.successful(hotelPropertyMetaData))
    }

    executeSSRGraphQL(
      query,
      mockModule
    )
  }

  // migrate from CitySearch-LandmarkFilterMatrix.feature : TC3
  "Radius search" should {
    "return matrix group result for landmarkIds correctly" in {
      val matrixGroupRequests = List(
        MatrixGroupRequest(MatrixGroupTypes.LandmarkIds, Some(10))
      )

      val respF = executeTestCase(propertyIds, matrixGroupRequests)

      whenReady(respF) { resp =>
        resp.status shouldBe StatusCodes.OK
        val radiusSearchResponse = responseToRadiusSearchResponse(resp)
        val matrixGroupResults   = radiusSearchResponse.data.radiusSearch.aggregation.map(_.matrixGroupResults)
        val landmarkIdMatrixItemResults =
          matrixGroupResults
            .flatMap(_.find(_.matrixGroup == MatrixGroupTypes.LandmarkIds.entryName))
            .map(_.matrixItemResults)
        landmarkIdMatrixItemResults shouldBe Some(
          List(
            TestMatrixItemResult(landmark1, s"mockname_$landmark1", count = 3),
            TestMatrixItemResult(landmark2, s"mockname_$landmark2", count = 2),
            TestMatrixItemResult(landmark3, s"mockname_$landmark3", count = 1)
          )
        )
      }
    }
  }

}
