query getPropertyDetails(
  $PropertyDetailsRequest: PropertyDetailsRequest!
  $ContentImagesRequest: ContentImagesRequest!
  $ContentReviewScoreRequest: ContentReviewScoreRequest
  $ContentInformationSummary: ContentInformationSummaryRequest
  $ContentExperienceRequest: ContentExperienceRequest
  $ContentLocalInformationRequest: ContentLocalInformationRequest
  $ContentFeaturesRequest: ContentFeaturesRequest
  $ContentHighlightsRequest: ContentHighlightsRequest
  $ContentQnaRequest: ContentQnaRequest
  $ContentTopicsRequest: ContentTopicsRequest
  $PriceStreamMetaLabRequest: PriceStreamMetaLabRequest!
  $BannerCampaignRequest: BannerCampaignRequest!
  $ContentRateCategoryRequest: ContentRateCategoryRequest
  $GrowthProgramInfoRequest: GrowthProgramInfoRequest!
  $HostProfileRequest: $HostProfileRequest
) {
  propertyDetailsSearch(PropertyDetailsRequest: $PropertyDetailsRequest) {
    propertyDetails {
      propertyId
      propertyMetaInfo{
        propertyMetaRanking{
          numberOfProperty
          metrics{
            metricName
            rank
            absoluteValue
          }
        }
      }
      metaLab(PriceStreamMetaLabRequest: $PriceStreamMetaLabRequest) {
          propertyId
          propertyAttributes {
              attributeId
              dataType
              version
              value
          }
      }
      contentDetail {
        contentEngagement{
          propertyId
          peopleLooking
          noOfPeopleLooking
          lastBooking
          todayBooking
          lastBook
        }
        contentReviewScore(ContentReviewScoreRequest: $ContentReviewScoreRequest) {
          combinedReviewScore {
            cumulative {
              score
              reviewCount
              maxScore
            }
          }
          providerReviewScore {
            providerId
            cumulative {
              reviewCount
              score
              maxScore
            }
            trendingScore {
              recentReviews
              past14DaysUplift
              past30DaysUplift
            }
            demographics {
              groups {
                id
                reviewCount
                grades {
                  id
                  score
                  cityAverage
                  subGrades {
                    name
                    score
                  }
                }
                scoreDistribution {
                  id
                  reviewCount
                }
              }
              allGuest {
                id
                reviewCount
                grades {
                  id
                  score
                  cityAverage
                  subGrades {
                    name
                    score
                  }
                }
                scoreDistribution {
                  id
                  reviewCount
                }
              }
            }
          }
        }
        contentImages(ContentImagesRequest: $ContentImagesRequest) {
          mainImage {
            id
            caption
            typeId
            providerId
            groupId
            groupEntityId
            uploadedDate
            urls {
              key
              value
            }
            captionLocalizations {
              locale
              value
            }
          }
          hotelImages {
            id
            caption
            typeId
            providerId
            groupId
            groupEntityId
            uploadedDate
            urls {
              key
              value
            }
            captionLocalizations {
              locale
              value
            }
          }
        }
        contentSummary(ContentInformationSummaryRequest: $ContentInformationSummary) {
          localeName
          propertyType
          awardYear
          defaultName
          displayName
          geoInfo{
            latitude,
            longitude
          }
        }
        contentExperiences(ContentExperienceRequest: $ContentExperienceRequest) {
          experience {
            name
            symbol
            landmarks {
              name
              geoInfo {
                latitude
                longitude
                obfuscatedLat
                obfuscatedLong
              }
              scores {
                providerId
                reviewCount
                reviewCommentsCount
                demographic
                score
                scoreText
                maxScore
                countText
              }
              distanceInKm
            }
          }
        }
        contentLocalInformation(ContentLocalInformationRequest: $ContentLocalInformationRequest) {
          nearbyProperties {
            id
            categoryName
            categorySymbol
            places {
              name
              distanceInKm
              geoInfo {
                latitude
                longitude
              }
            }
          }
          nearbyPlaces {
            name
            abbr
            duration
            durationIcon
            geoInfo {
              latitude
              longitude
            }
            typeName
            landmarkId
            distanceInKm
          }
          topPlaces {
            name
            abbr
            duration
            durationIcon
            geoInfo {
              latitude
              longitude
            }
            typeName
            landmarkId
            distanceInKm
          }
          cuisines {
            id
            name
            restaurants {
              id
              name
              distance
              cuisinesOffered
            }
          }
          popularLandmarkNumber
          locationSubscore {
            poiScore
            transportationScore
            airportScore
          }
        }
      	contentInformation{
            propertyId
            description{
                long
                short
            }
            usefulInfoGroups{
                id
                name
                order
                usefulInfo{
                    id
                    name
                    description
                    symbol
                    propertyId
                    nameLocalizations{
                        locale
                        value
                    }
                }
            }
            notes{
                publicNotes
                importantNotes
                transportationNotes
            }
            numberOfRoom
            policies{
                children{
                    stayFreeAgeRange{
                        min
                        max
                    }
                    details{
                        isInfant
                        title
                        description
                    }
                }
                minAge
                adult
                extraBed
                additional
                hotelAgePolicy{
                    infantAges{
                        min
                        max
                    }
                    childAges{
                        min
                        max
                    }
                    minGuestAge
                    isChildStayFree
                }
            }
            messaging{
                hostName
                isAllowedPreBooking
                isAllowedPostBooking
                isAllowedWithBooking
                isAllowedInHouseFeedback
                isAllowedInHouseRequest
                isAllowedMessagingPlatform
                isShowCustomerInfoInVoucher
                emailDisplaySetting
                responsiveRate
            }
            blockedNationalities{
                id
                localeName
            }
            local{
                name
                language{
                    id
                    localeName
                    locale
                }
                address{
                    country
                    city
                    state
                    area
                    address1
                    address2
                    postalCode
                    GmtOffset
                    countryId
                    cityId
                    areaId
                    countryCode
                }
            }
            chainId
            childrenStayFreeTypeId
            formerlyName
            localizations{
                notes{
                    importantNotes{
                        locale
                        notes
                    }
                    publicNotes{
                        locale
                        notes
                    }
                }
                description{
                    long{
                        locale
                        value
                    }
                    short{
                        locale
                        value
                    }
                }
                blockedNationalities{
                    locale
                    blockedCountries{
                        id
                        localeName
                    }
                }
            }
            isExtraBedAvailable
            contractInfo{
                id
            }
            breakfastInfo{
                cuisines
            }
            isAgodaVerified
            restaurantOnSite{
                id
                name
                servings
                cuisines
            }
            coffeeInfo{
                caption
                rating
                reviewCount
            }
            isEcoFriendly
            checkInInformation{
                checkInFrom{
                    hh
                    mm
                }
                checkInUntil{
                    hh
                    mm
                }
                checkOutFrom{
                    hh
                    mm
                }
                checkOutUntil{
                    hh
                    mm
                }
            }
            isSendSmsForBooking
            isGlobalPartnerServiceBranding
        }
        contentFeatures(ContentFeaturesRequest: $ContentFeaturesRequest) {
          featureGroups {
            id
            name
            symbol
            features {
              id
              featureName
              featureNameLocalizationList {
                value
                locale
              }
              symbol
              available
              order
              images {
                id
                caption
                typeId
                highResolutionSizes
                providerId
                groupId
                groupEntityId
                urls {
                  key
                  value
                }
                captionLocalizations {
                  value
                  locale
                }
                uploadedDate
                captionIdML
              }
            }
          }
        }
        contentHighlights(ContentHighlightsRequest : $ContentHighlightsRequest) {
          favoriteFeatures {
            id
            name
            symbol
            toolTip
          }
          locations {
            id
            name
            symbol
            toolTip
          }
          locationHighlightMessage {
            title
          }
          locationHighlights {
            message
            distanceKm
            highlightType
          }
        }
        contentQna(ContentQnaRequest: $ContentQnaRequest) {
            qnas {
                question
                answer
                roomTypeId
                roomType
                questionWhen
                answerWhen
            }
            hasMore
        }
        contentTopics(ContentTopicsRequest: $ContentTopicsRequest) {
          topics {
            topic
            sentimentScore {
              positive
              negative
              neutral
            }
            reviewSnippets
            images {
              url
              imageId
              imageSource
            }
            summary
          }
        }
        contentRateCategory(ContentRateCategoryRequest: $ContentRateCategoryRequest) {
            rateCategories {
                rateCategoryId
                localizedRateCategoryName
                inventoryType
            }
        }
      }
      bannerCampaignInfo(BannerCampaignRequest: $BannerCampaignRequest) {
        campaignId
        ranking
      }
      growthProgramInfo(GrowthProgramInfoRequest: $GrowthProgramInfoRequest) {
        badges
      }
      hostProfile(HostProfileRequest: $HostProfileRequest) {
       userId
      }
    }
  }
}
