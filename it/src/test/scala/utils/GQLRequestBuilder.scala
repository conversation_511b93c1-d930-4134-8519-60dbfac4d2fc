package utils

import api.request.FeatureFlag
import com.agoda.commons.traffic.TrafficInfo
import com.agoda.content.models._
import com.agoda.content.models.db.image.ImageSizeRequest
import com.agoda.content.models.request.sf.{ Experiments => _, ForcedExperiment => _, SearchCriteria => _, _ }
import com.agoda.core.search.models.enumeration.{ SearchTypes, SortFields, Sorting, SortOrders, TravellerType }
import com.agoda.core.search.models.request.{
  APORequest,
  BaseRequest,
  BenefitsFilterDefinition,
  BookingDurationFilterDefinition,
  ComplexSearchRequest,
  DealsFilterDefinition,
  DoubleEndedRangeFilterDefinition,
  Experiments,
  ExtraAgodaHomesRequest,
  ExtraHotelsRequest,
  FeaturedAgodaHomesRequest,
  FeaturedLuxuryHotelsRequest,
  FeaturedPulsePropertiesRequest,
  FeatureFlagRequest,
  FeatureSwitch,
  FilterDefinition,
  ForcedExperiment,
  HighlyRatedAgodaHomesRequest,
  IdsFilterDefinition,
  ItineraryCriteria,
  KidsStayForFreeDefinition,
  MatrixField,
  MatrixGroupRequest,
  NameFilterDefinition,
  OneOrMoreFilterDefinition,
  PackagingToken,
  Page,
  PaymentModeFilterDefinition,
  PaymentOptionsFilterDefinition,
  PersonalizedPropertiesRequest,
  PollingInfoRequest => PollingInfoRequestModel,
  PriceFilterDefinition,
  RangeFilterDefinition,
  RoomBenefitsFilterDefinition,
  SearchContext,
  SearchCriteria,
  SearchDetailRequest,
  SingleEndedRangeFilterDefinition,
  SupplierPullMetadataRequest,
  TermFilterDefinition,
  TermsFilterDefinition,
  TextFilterDefinition
}
import com.agoda.papi.search.internalmodel._
import com.agoda.core.search.models.enumeration.SearchTypes.CitySearch
import com.agoda.papi.search.internalmodel.request._
import com.agoda.papi.search.types.HotelIdType
import com.agoda.papi.enums.request.RequestedPrice
import com.agoda.papi.enums.room.ApplyType
import com.agoda.papi.search.internalmodel
import com.agoda.papi.search.internalmodel.builders.{ QueryBuilder, QueryReader }
import com.agoda.papi.search.internalmodel.builders.request.{
  BannerCampaignRequestItTest,
  ComplexSearchParametersBuilder,
  PropertyAllotmentSearchParametersBuilder,
  PropertyDetailsRequestItTest,
  PropertyDetailsSearchParametersBuilder,
  PropertySummarySearchBuilder,
  SearchRequestParameters
}
import com.agoda.platform.service.context.ServiceHeaders
import com.agoda.papi.search.internalmodel.builders.request._
import com.typesafe.config.ConfigFactory
import constants.GQLConfigPaths
import executor.GQLContext
import io.circe.Json
import models.db.HotelId
import models.starfruit.{ FilterRequest, OccupancyRequest, _ }
import com.agoda.core.search.models
import com.agoda.core.search.models.{
  CompositeFilterRequest,
  CustomFilterRequest,
  DynamicFilterRequest,
  ElasticFilterRequest,
  IdsFilterRequest,
  RangeFilterRequest,
  TextFilterRequest
}
import com.agoda.core.search.utils.ItineraryCriteriaHelper
import com.agoda.papi.search.internalmodel.growthprogram.GrowthProgramInfoRequest
import com.agoda.upi.models.request.CartBaseRequest
import org.joda.time.DateTime

import java.util.UUID

/** <AUTHOR>   phi
  */
class GQLRequestBuilder extends SearchRequestParameters {
  // scalastyle:off
  var tracingEnable: Boolean                       = false
  var searchType                                   = ""
  var daysInAdvance: Int                           = 30
  var extraHotelIds: List[HotelIdType]             = List.empty
  var enableFiltersForExtraHotels: Option[Boolean] = None
  var bookingDate: DateTime                        = DateTime.now
  var checkInDate: DateTime                        = bookingDate.plusDays(daysInAdvance)
  var checkOutDate: DateTime                       = _
  var pageSize: Int                                = 45
  var pageNumber: Int                              = 1
  var apoRequest: Option[APORequest]               = None
  var priceHistogramBins: Option[Int]              = None
  var los: Int                                     = _
  var rooms: Int                                   = _
  var adults: Int                                  = _
  var children: Int                                = _
  var travellerType: Option[TravellerType]         = None
  var childAges: Option[List[Option[Int]]]         = None
  var childrenTypes: Option[List[ChildType]]       = None
  var ratePlans: List[Int]                         = List.empty
  var isUserLoggedIn: Boolean                      = _
  var enableOpaqueChannel: Option[Boolean]         = None
  var currency: String                             = ""
  var isAPO: Boolean                               = false
  var isAPSPeek: Boolean                           = _
  var isRPM2Included: Boolean                      = _
  var isIncludeUsdAndLocalCurrency: Boolean        = _
  var requiredBasis: Option[ApplyType]             = None
  var requiredPrice: Option[RequestedPrice]        = None
  var isAllowBookOnRequest: Option[Boolean]        = None
  var showUnavailable: Option[Boolean]             = None
  var showRemainingProperties: Option[Boolean]     = None
  var enableDealsOfTheDayFilter: Option[Boolean]   = None
  var zeroCountFiltersInMatrix: Option[Boolean]    = None
  var flags: Option[List[FeatureSwitch]]           = None
  var suggestionLimit: Option[Int]                 = None
  var synchronous: Option[Boolean]                 = None
  var sorting: Sorting                             = Sorting.default
  var searchId: String                             = ""
  var sessionId: Int                               = 0
  var locale: String                               = "en-us"
  var origin: String                               = ""
  var userId: Option[String]                       = None
  val randomUserId                                 = UUID.randomUUID().toString // in case no input for user id
  var cid: Int                                     = _
  var memberId: Long                               = _
  var platform: Int                                = 1
  var systemId: Option[Int]                        = None
  var deviceTypeId: Option[Int]                    = Some(1)
  var storeFrontId: Option[Int]                    = Some(1)
  var abTests: List[ABTest]                        = List.empty
  var experimentCS: Option[Experiments]            = None
  var experimentCT: List[StringStringTuple]        = List.empty
  var experimentDF: List[Experiment]               = List.empty
  val experimentsToForceInDF: List[Experiment]     = List(Experiment("OPA-1878", 'B'))
  var filters: List[FilterDefinition]              = List.empty
  var matrix: List[MatrixField]                    = List.empty
  var matrixGroup: List[MatrixGroupRequest]        = List.empty
  var showCMS: Boolean                             = _
  var trafficData: Option[TrafficInfo]             = None
  var ipAddress: Option[String]                    = None
  var priusId: Int                                 = _
  var allowOverrideOccupancy: Boolean              = false
  var featureFlags: List[FeatureFlag]              = List.empty
  var summaryRequest: Boolean                      = false
  var nonHotelAccommodation: Boolean               = false
  var isNeedPricingMessaging: Boolean           = false // can not find a place for this field yet, need to confirm
  var hotelIds: List[HotelId]                   = List.empty
  var hotelId: HotelId                          = _
  var isMSE: Boolean                            = false
  var isAllowRoomTypeNotGuarantee: Boolean      = false
  var whiteLabelKey: Option[String]             = None
  var whiteLabelId: Option[Int]                 = None
  var isFullMatrixItems: Boolean                = false
  var fetchTealiumNames: Option[Boolean]        = None
  var isDatelessSearch: Option[Boolean]         = None
  var hasItineraryCriteria: Boolean             = false
  var bookingDurationType: Option[List[String]] = None
  var bookingDurationTypeCustomFilters: Option[BookingDurationFilterDefinition] = None
  var isPoll: Option[Boolean]                                                   = None
  var attributesId: List[Long]                                                  = List.empty
  var packagingClientToken: Option[String]                                      = None
  var packagingISystemToken: Option[String]                                     = None
  var cart: Option[CartBaseRequest]                                             = None
  var personalizedPropertiesRequest: Option[PersonalizedPropertiesRequest] =
    None
  var featuredAgodaHomesRequest: Option[FeaturedAgodaHomesRequest]           = None
  var extraAgodaHomesRequest: Option[ExtraAgodaHomesRequest]                 = None
  var highlyRatedAgodaHomesRequest: Option[HighlyRatedAgodaHomesRequest]     = None
  var sortByCheckInTimeDayUseSSR: Option[Boolean]                            = None
  var featuredLuxuryHotelsRequest: Option[FeaturedLuxuryHotelsRequest]       = None
  var featuredPulsePropertiesRequest: Option[FeaturedPulsePropertiesRequest] = None
  var enableEscapesPackage: Option[Boolean]                                  = None
  var filterCheapestRoomEscapesPackage: Option[Boolean]                      = None
  var showRoomFacilities: Option[Boolean]                                    = None
  var showRoomSize: Option[Boolean]                                          = None
  var disableEscapesPackage: Option[Boolean]                                 = None
  var returnCheapestEscapesOfferOnSSR: Option[Boolean]                       = None
  var enablePushDayUseRates: Option[Boolean]                                 = None

  // TODO: For CitySearch for now, need to set isSSR and cheapestOnly as true, need to figure a better way not to hard code these values
  var isSSR: Option[Boolean] = Some(true)
  var cheapestOnly: Boolean  = true

  /* New Filters */
  var idsFilters: Option[List[IdsFilterRequest]]     = None
  var rangeFilters: Option[List[RangeFilterRequest]] = None
  var textFilters: Option[List[TextFilterRequest]]   = None

  /* Custom Filters */
  var priceFilter: Option[PriceFilterDefinition]             = None
  var nameFilter: Option[NameFilterDefinition]               = None
  var benefits: Option[BenefitsFilterDefinition]             = None
  var paymentModel: Option[PaymentModeFilterDefinition]      = None
  var roomBenefits: Option[RoomBenefitsFilterDefinition]     = None
  var deals: Option[DealsFilterDefinition]                   = None
  var paymentOptions: Option[PaymentOptionsFilterDefinition] = None
  var freeCancellation: Boolean                              = false
  var taxReceipt: Boolean                                    = false
  var pulsePromotions: Boolean                               = false
  val kidsStayForFree: Option[KidsStayForFreeDefinition]     = None

  /* Elastic Filters */
  var range: List[RangeFilterDefinition]      = Nil
  var term: List[TermFilterDefinition]        = Nil
  var terms: List[TermsFilterDefinition]      = Nil
  var ids: List[IdsFilterDefinition]          = Nil
  var orFilters: List[CompositeFilterRequest] = Nil

  /* Dynamic Filters */
  var text: List[TextFilterDefinition]                    = List.empty
  var check: List[OneOrMoreFilterDefinition]              = Nil
  var singleSlide: List[SingleEndedRangeFilterDefinition] = List.empty
  var doubleSlide: List[DoubleEndedRangeFilterDefinition] = List.empty

  var indexes: List[Int] = List.empty[Int]

  var supplierPullMetadataRequest: Option[SupplierPullMetadataRequest] = None
  var queryRemoves: List[String]                                       = List.empty
  var providerIds: List[Int]                                           = List.empty
  var defaultProviderOnly: Boolean                                     = false
  var suggestedPrice: String                                           = ""

  private val CONFIG = ConfigFactory
    .load(GQLConfigPaths.CONFIG_FILE)
    .getConfig(GQLConfigPaths.CONFIG_PATH)

  private val INT_TEST_CONFIG      = CONFIG.getConfig(GQLConfigPaths.INTEGRATION_TEST)
  private val TEST_TYPE            = CONFIG.getString(GQLConfigPaths.TEST_TYPE)
  var allowNullResult: Boolean     = false
  var isFailureTest: Boolean       = false
  var useRawJson: Boolean          = false
  var variables: Json              = _
  var headers: Map[String, String] = _
  var agForceExperiment: String    = _

  /** @param id
    *   the experiment id
    * @param variant
    *   user A or B
    */
  def addExperiment(id: String, variant: String): Unit = {
    // CitySearchRequest
    val currentForceExperiment: Vector[ForcedExperiment] =
      experimentCS.flatMap(_.forceByExperiment).getOrElse(Vector.empty)
    val newForceExperiment: Vector[ForcedExperiment] = ForcedExperiment(
      id,
      variant
    ) +: currentForceExperiment
    val newExperimentCS: Option[Experiments] =
      if (experimentCS.isDefined)
        experimentCS.map(_.copy(forceByExperiment = Some(newForceExperiment)))
      else
        Some(
          Experiments(
            forceByVariant = None,
            forceByExperiment = Some(newForceExperiment)
          )
        )
    experimentCS = newExperimentCS
    // ContentSummaryRequest
    experimentCT = experimentCT :+ StringStringTuple(id, variant)
    // PricingSummaryRequest
    experimentDF = experimentDF :+ Experiment(id, variant.head)
  }

  /** To build GQL context from parameters above.
    *
    * @return
    *   query context includes GQL query and variables
    */
  def buildQueryContext[T <: BaseRequest[ComplexSearchRequest]](
      complexSearchRequestBuilder: ComplexSearchParametersBuilder[T]
  ): GQLContext = {
    // add these experiments by default for all test scenarios
    addExperiment("MIGRATE-BCOM-TEST-DATA", "B")
    addExperiment("MIGRATE-ALL-PUSH-TEST-DATA", "B")

    val complexSearchRequest = buildComplexSearchRequest(
      complexSearchRequestBuilder
    )
    val pricingRequestParameters       = buildPricingRequestParameters()
    val contentSummaryRequest          = buildContentSummaryRequest()
    val priceStreamMetaLabRequest      = buildPriceStreamMetaLabRequest
    val bannerCampaignRequest          = buildBannerCampaignRequest
    val contentExtraRoomSummaryRequest = buildContentExtraRoomSummaryRequest

    val QUERY_FILE                 = INT_TEST_CONFIG.getString(GQLConfigPaths.SSR_QUERY)
    val defaultQueryString: String = QueryReader.readQuery(QUERY_FILE)

    val EXP_VARIANT: String = TEST_TYPE match {
      case GQLConfigPaths.K8S =>
        INT_TEST_CONFIG.getConfig(GQLConfigPaths.K8S).getString(GQLConfigPaths.EXP_VARIANT)
      case GQLConfigPaths.PRELIVE =>
        INT_TEST_CONFIG.getConfig(GQLConfigPaths.PRELIVE).getString(GQLConfigPaths.EXP_VARIANT)
      case GQLConfigPaths.FAILURE => ""
    }

    // Update query to match with types for non city search requests
    val queryString = defaultQueryString
      .replaceAll(
        "CitySearchRequest",
        complexSearchRequestBuilder.parameterName
      )
      .replaceAll("citySearch", complexSearchRequestBuilder.queryName)

    GQLContext(
      query = QueryBuilder.build(
        compressGqlQuery(queryString),
        complexSearchRequest,
        pricingRequestParameters,
        contentSummaryRequest,
        priceStreamMetaLabRequest,
        bannerCampaignRequest,
        contentExtraRoomSummaryRequest
      ),
      headers =
        try
          if (this.headers == null) {
            if (EXP_VARIANT != "")
              // 2022-08-18, This header doesn't work in DF.
              buildHeader() ++ Map("AG-FORCE-ALL-EXPERIMENTS" -> EXP_VARIANT)
            else
              buildHeader()
          } else this.headers
        catch {
          case x: Exception =>
            if (this.headers == null) buildHeader() else this.headers
        }
    )
  }

  def buildQueryContext(
      propertyDetailsRequestBuilder: PropertyDetailsSearchParametersBuilder
  ): GQLContext = {

    val QUERY_FILE =
      INT_TEST_CONFIG.getString(GQLConfigPaths.PROPERTY_DETAILS_QUERY)
    val defaultQueryString: String = QueryReader.readQuery(QUERY_FILE)

    GQLContext(
      query = QueryBuilder.build(
        compressGqlQuery(defaultQueryString),
        buildPropertyDetailsRequest,
        buildContentImagesRequest,
        buildContentReviewScoreRequest,
        buildContentLocalInformationRequest,
        buildContentExperienceRequest,
        buildContentFeaturesRequest,
        buildContentHighlightsRequest,
        buildContentQnaRequest,
        buildContentTopicsRequest,
        buildPriceStreamMetaLabRequest,
        buildBannerCampaignRequest,
        buildContentRateCategoryRequest,
        buildGrowthProgramInfoRequest
      ),
      headers = buildHeader()
    )
  }

  def buildQueryContext(
      propertySummarySearchBuilder: PropertySummarySearchBuilder
  ): GQLContext = {

    val QUERY_FILE =
      INT_TEST_CONFIG.getString(GQLConfigPaths.PROPERTY_SUMMARY_SEARCH_QUERY)
    val defaultQueryString: String   = QueryReader.readQuery(QUERY_FILE)
    val propertySummarySearchRequest = propertySummarySearchBuilder.apply(hotelIds)
    val pricingRequestParameters     = buildPricingRequestParameters()
    val contentSummaryRequest        = buildContentSummaryRequest()
    GQLContext(
      query = QueryBuilder.buildPropertySummarySearchQuery(
        compressGqlQuery(defaultQueryString),
        propertySummarySearchRequest,
        Some(pricingRequestParameters),
        Some(contentSummaryRequest),
        None,
        Some(PollingInfoRequestModel(Some("pollid-1"), Some(1)))
      ),
      headers = buildHeader()
    )
  }

  def buildQueryContext(variables: Json, headers: Map[String, String], isComplexSearch: Boolean): GQLContext = {
    if (!useRawJson) return null
    val QUERY_FILE =
      if (isComplexSearch) INT_TEST_CONFIG.getString(GQLConfigPaths.SSR_QUERY)
      else INT_TEST_CONFIG.getString(GQLConfigPaths.PROPERTY_DETAILS_QUERY)
    val defaultQueryString: String = QueryReader.readQuery(QUERY_FILE)

    GQLContext(
      query = QueryBuilder.build(
        query = compressGqlQuery(defaultQueryString),
        variables = variables
      ),
      headers = headers
    )
  }

  def buildQueryContext(
      propertyAllotmentSearchBuilder: PropertyAllotmentSearchParametersBuilder
  ): GQLContext = {

    val QUERY_FILE =
      INT_TEST_CONFIG.getString(GQLConfigPaths.PROPERTY_ALLOTMENT_QUERY)
    val defaultQueryString: String = QueryReader.readQuery(QUERY_FILE)
    GQLContext(
      query = QueryBuilder.buildPropertyAllotmentSearchQuery(
        compressGqlQuery(defaultQueryString),
        buildPropertyAllotmentRequest
      ),
      headers = buildHeader()
    )
  }

  def compressGqlQuery(query: String): String = {
    var compressed: String = query
    compressed = compressed.replaceAll("\n", "")
    compressed = compressed.replaceAll("[ ]+", " ")
    compressed = compressed.replaceAll("[ ]*[{][ ]*", "{")
    compressed = compressed.replaceAll("[ ]*[}][ ]*", "}")
    compressed = compressed.replaceAll("[ ]*[(][ ]*", "(")
    compressed = compressed.replaceAll("[ ]*[)][ ]*", ")")
    compressed = compressed.replaceAll("[ ]*[:][ ]*", ":")
    if (queryRemoves.nonEmpty) {
      queryRemoves.foreach(r => compressed = compressed.replaceAll(s"$r[ ]*\\{[a-zA-Z\\s]*\\}", ""))
    }
    compressed
  }

  /** To build up citySearchRequest
    *
    * @return
    *   citySearchRequest
    */
  private def buildComplexSearchRequest[T <: BaseRequest[ComplexSearchRequest]](
      complexSearchRequestBuilder: ComplexSearchParametersBuilder[T]
  ): T = {
    lazy val aValidFeatureFlagRequest = Some(
      FeatureFlagRequest(
        isAllowBookOnRequest = if (isAllowBookOnRequest.isEmpty) false else isAllowBookOnRequest.get,
        showUnAvailable = showUnavailable,
        filterFavorites = None,
        showBeachIcon = None,
        flagMap = None,
        isEnableSupplierFinancialInfo = None,
        isFullMatrixItems = Some(isFullMatrixItems),
        showRemainingProperties = showRemainingProperties,
        shouldShowHomesFirst = None,
        fiveStarDealOfTheDay = None,
        flags = flags,
        fetchNamesForTealium = fetchTealiumNames,
        zeroCountFiltersInMatrix = zeroCountFiltersInMatrix,
        enableDealsOfTheDayFilter = enableDealsOfTheDayFilter
      )
    )

    lazy val aValidSearchCriteria = buildSearchCriteria(
      aValidFeatureFlagRequest
    )

    lazy val pollingInfoRequest = isPoll match {
      case Some(true) => PollingInfoRequestModel(Some("pollid"), Some(4))
      case _          => PollingInfoRequestModel(Some("pollid2"), Some(5))
    }

    lazy val aValidSearchContext: SearchContext = SearchContext(
      userId = userId,
      memberId = memberId,
      locale = locale,
      cid = cid,
      origin = origin,
      platform = platform,
      searchId = Some(searchId),
      deviceTypeId = deviceTypeId,
      experiments = experimentCS,
      trafficInfo = None,
      isRetry = None,
      showCMS = Some(showCMS),
      storeFrontId = storeFrontId,
      pageTypeId = None,
      whiteLabelKey = whiteLabelKey,
      ipAddress = ipAddress,
      endpointSearchType = Some(CitySearch),
      trackSteps = None,
      isGeneratedSearchIdFromNone = Some(false),
      isGeneratedSearchIdFromEmptyString = Some(false),
      pollingInfoRequest = Some(pollingInfoRequest)
    )

    lazy val extraHotelRequest =
      buildExtraHotelRequest(extraHotelIds, enableFiltersForExtraHotels)
    lazy val aSearchDetailRequest = Some(
      SearchDetailRequest(priceHistogramBins)
    )

    lazy val aValidSearchRequest = ComplexSearchRequest(
      searchCriteria = aValidSearchCriteria,
      searchContext = aValidSearchContext,
      packaging = buildPackageRequest(packagingClientToken, packagingISystemToken),
      cart = cart,
      filterRequest = Some(
        models.FilterRequest(
          idsFilters = idsFilters,
          rangeFilters = rangeFilters,
          textFilters = textFilters,
          customFilters = Some(
            CustomFilterRequest(
              price = priceFilter,
              aps = false,
              vip = None,
              apo = false,
              name = nameFilter,
              benefits = benefits,
              paymentModel = paymentModel,
              freeCancellation = freeCancellation,
              taxReceipt = taxReceipt,
              roomBenefits = roomBenefits,
              deals = deals,
              paymentOptions = paymentOptions,
              roomId = None,
              bookingDurationType = bookingDurationTypeCustomFilters,
              filterCheapestRoomEscapesPackage = filterCheapestRoomEscapesPackage,
              pulsePromotions = pulsePromotions
            )
          ),
          elasticFilters = Some(
            ElasticFilterRequest(
              range = range,
              term = term,
              terms = terms,
              ids = ids,
              and = None,
              or = None,
              orFilters = if (orFilters.nonEmpty) Some(orFilters) else None
            )
          ),
          dynamicFilters = Some(DynamicFilterRequest(check = check))
        )
      ),                               // filter
      matrix = Some(matrix),           // matrix
      matrixGroup = Some(matrixGroup), // new matrix request
      page = Some(Page(pageSize, pageNumber)),
      apoRequest = apoRequest,
      searchHistory = Some(Vector.empty),
      searchDetailRequest = aSearchDetailRequest,
      isTrimmedResponseRequested = false,
      featuredAgodaHomesRequest = featuredAgodaHomesRequest,
      featuredLuxuryHotelsRequest = featuredLuxuryHotelsRequest,
      featuredPulsePropertiesRequest = featuredPulsePropertiesRequest,
      highlyRatedAgodaHomesRequest = highlyRatedAgodaHomesRequest,
      extraAgodaHomesRequest = extraAgodaHomesRequest,
      personalizedPropertiesRequest = personalizedPropertiesRequest,
      extraHotels = extraHotelRequest
    )

    complexSearchRequestBuilder.apply(this, aValidSearchRequest)
  }

  /** * Search Criteria Builder
    */
  private def buildSearchCriteria(
      featureFlagRequest: Option[FeatureFlagRequest]
  ): SearchCriteria = {
    val aValidItineraryCriteria: Option[ItineraryCriteria] =
      if (hasItineraryCriteria) {
        Some(
          ItineraryCriteria(
            bookingDateOpt = Some(bookingDate),
            checkInDate = ItineraryCriteriaHelper.getCheckInDate(checkInDate),
            los = los,
            rooms = rooms,
            adults = adults,
            children = children,
            childAges = childAges
          )
        )
      } else None

    SearchCriteria(
      bookingDate =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else Some(bookingDate),
      checkInDate =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else Some(checkInDate),
      los =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else Some(los),
      rooms =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else Some(rooms),
      adults =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else Some(adults),
      children =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else Some(children),
      childAges =
        if (hasItineraryCriteria || isDatelessSearch.getOrElse(false)) None
        else childAges,
      ratePlans = Some(ratePlans),
      isUserLoggedIn = isUserLoggedIn,
      currency = currency,
      travellerType = travellerType,
      isAPSPeek = Some(isAPSPeek),
      enableOpaqueChannel = enableOpaqueChannel,
      isEnabledPartnerChannelSelection = None,
      sorting = Some(sorting),
      requiredBasis = requiredBasis,
      requiredPrice = requiredPrice,
      suggestionLimit = suggestionLimit,
      synchronous = synchronous,
      supplierPullMetadataRequest = supplierPullMetadataRequest,
      isRoomSuggestionRequested = false,
      isAPORequest = isAPO,
      hasAPOFilter = false,
      isAllowBookOnRequest = isAllowBookOnRequest,
      featureFlagRequest =
        featureFlagRequest, // should hook up with aValidFeatureFlagRequest, waiting for fixing from PAPI to be merged to develop
      itineraryCriteria = aValidItineraryCriteria,
      isDatelessSearch = isDatelessSearch
    )
  }

  /** To build up pricingRequestParameters
    *
    * @return
    *   pricingRequestParameters
    */
  private def buildPricingRequestParameters(): PricingRequestParameters = {
    checkOutDate = checkInDate.plusDays(if (isDatelessSearch.getOrElse(false)) 2 else los)
    lazy val aSessionInfo = SessionInfo(
      sessionId = sessionId,
      memberId = memberId.toInt,
      isLogin = isUserLoggedIn
    )

    lazy val aClientInfo = ClientInfo(
      cid = cid,
      userId = userId.getOrElse(randomUserId),
      languageId = 1,
      languageUse = 1,
      platform = platform,
      origin = origin,
      storefront = storeFrontId.get,
      searchId = searchId,
      trafficData = trafficData,
      deviceTypeId = deviceTypeId,
      ipAddress = ipAddress,
      affiliateId = None
    )

    lazy val aFeatureRequest = FeatureRequest(
      newRateModel = false,
      overrideOccupancy = false,
      crossOutRate = false,
      priusId = priusId, // response this one has not been implemented yet
      isMSE = Some(isMSE),
      isAllowRoomTypeNotGuarantee = Some(isAllowRoomTypeNotGuarantee),
      isAPSPeek = isAPSPeek,
      synchronous = if (synchronous.isDefined) synchronous.get else false,
      isRPM2Included = isRPM2Included,
      isIncludeUsdAndLocalCurrency = isIncludeUsdAndLocalCurrency,
      enableEscapesPackage = enableEscapesPackage,
      filterCheapestRoomEscapesPackage = filterCheapestRoomEscapesPackage,
      disableEscapesPackage = disableEscapesPackage,
      returnCheapestEscapesOfferOnSSR = returnCheapestEscapesOfferOnSSR,
      sortByCheckInTimeDayUseSSR = sortByCheckInTimeDayUseSSR,
      enablePushDayUseRates = enablePushDayUseRates
    )

    lazy val aDetailRequest = DetailRequest(
      priceBreakdown = false,
      itemBreakdown = false,
      cheapestPriceOnly = cheapestOnly
    )

    lazy val aFilterRequest = FilterRequest(
      ratePlans = ratePlans,
      suppliers = List(),
      secretDealOnly = false
    )

    lazy val anOccupancyRequest = OccupancyRequest(
      adults = adults,
      children = children,
      rooms = rooms,
      childAges = childAges.getOrElse(List.empty),
      childrenTypes = childrenTypes
    )

    lazy val aPricingRequest = PricingRequest(
      occupancy = anOccupancyRequest,
      filters = aFilterRequest,
      details = aDetailRequest,
      features = aFeatureRequest,
      checkIn = checkInDate,
      checkout = checkOutDate,
      currency = currency,
      bookingDate = bookingDate,
      roomRequest = None,
      partner = None,
      refId = None,
      paymentId = None,
      featureFlag = featureFlags,
      maxRooms = None,
      includedPriceInfo = None,
      discountRequest = None,
      payment = None,
      supplierPullMetadata = None,
      mseHotelIds = None,
      fencedRate = None,
      roomSelection = None,
      whiteLabelKey = whiteLabelKey,
      bookingDurationType = bookingDurationType,
      localCheckInDate = Some(checkInDate.toString("YYYY-MM-dd")),
      localCheckoutDate = Some(checkOutDate.toString("YYYY-MM-dd"))
    )

    lazy val aContextRequest = ContextRequest(
      sessionInfo = aSessionInfo,
      clientInfo = aClientInfo,
      abTests = abTests,
      experiment = experimentDF ++ experimentsToForceInDF,
      isAllowBookOnRequest = isAllowBookOnRequest,
      ttl = None,
      packaging =
        if (packagingClientToken != None && packagingISystemToken != None)
          Some(PackagingRequest(Option(PackagingRequestToken(packagingClientToken.get, packagingISystemToken))))
        else None,
      cartRequest = cart
    )

    PricingRequestParameters(
      context = aContextRequest,
      pricing = aPricingRequest,
      cheapestOnly = cheapestOnly,
      suggestedPrice = suggestedPrice,
      isSSR = isSSR,
      booking = None
    )
  }

  /** To build up contentSummaryRequest
    *
    * @return
    *   contentSummaryRequest
    */
  private def buildContentSummaryRequest(): ContentSummaryRequest = {
    val contextImpl = PropertyContentContext(
      rawUserId = userId,
      memberId = Some(memberId),
      userOrigin = Some(origin),
      locale = locale,
      forceExperimentsByVariantNew = None,
      forceExperimentsByIdNew = experimentCT,
      storeFrontId = storeFrontId,
      showCMS = Some(showCMS),
      apo = Some(false),
      searchCriteria = None,
      correlationId = None,
      platform = None,
      occupancy = None,
      trafficInfo = None,
      deviceTypeId = deviceTypeId,
      cid = Some(cid.toString),
      localizations = None,
      whiteLabelKey = whiteLabelKey
    )

    lazy val reviews: Option[ReviewRequest] = Some(
      ReviewRequest(
        commentary = Some(
          CommentaryRequest(
            providerIds = Some(List.empty),
            page = com.agoda.content.models.request
              .Page(pageSize = pageSize, pageNumber = pageNumber),
            demographicId = 0,
            isOnlyCurated = 0,
            sorting = 1,
            filters = None,
            topCommentSize = None
          )
        ),
        demographics = Some(
          DemographicsRequest(
            providerIds = Some(providerIds),
            filter = None,
            showRoomReview = None
          )
        ),
        summaries = Some(SummariesRequest(providerIds = Some(providerIds), isAPO)),
        cumulative = Some(CumulativeRequest(providerIds = Some(providerIds))),
        filters = Some(
          com.agoda.content.models
            .FilterRequest(providerIds = Some(providerIds), demographicId = 0)
        ),
        positiveMentions = None,
        counters = None
      )
    )

    ContentSummaryRequest(
      context = contextImpl,
      reviews = reviews,
      images = Some(
        ImagesRequest(
          page = None,
          indexOffset = None,
          maxWidth = None,
          maxHeight = None,
          imageSizes = None
        )
      ),
      information = Some(
        InformationRequest(
          entertainment = Some(EntertainmentRequest(sizes = None, imageSizes = None)),
          highlightedFeaturesOrderPriority = None
        )
      ),
      summary = if (summaryRequest) Some(SummaryRequest()) else None,
      features = Some(
        FeaturesRequest(
          groupType = None,
          includeMissing = true,
          tags = None,
          images = Some(
            ImageSizeRequest(
              maxWidth = None,
              maxHeight = None,
              imageSizes = Some(List.empty)
            )
          )
        )
      ),
      experiences = None,
      localInformation = Some(
        LocalInformationRequest(
          Some(
            com.agoda.content.models.db.image.ImageSizeRequest(
              maxWidth = None,
              maxHeight = None,
              imageSizes = Some(List.empty)
            )
          )
        )
      ),
      checkInOut = Some(true),
      engagement = Some(true),
      rooms = Some(
        RoomRequest(
          includeDmcRoomId = None,
          includeMissing = None,
          featureLimit = None,
          showRoomFacilities = showRoomFacilities,
          showRoomSize = showRoomSize
        )
      ),
      highlights = Some(
        HighlightsRequest(
          maxNumberOfItems = Option(10),
          images = Some(
            com.agoda.content.models.db.image.ImageSizeRequest(
              maxWidth = None,
              maxHeight = None,
              imageSizes = Some(List.empty)
            )
          )
        )
      ),
      nonHotelAccommodation = Some(nonHotelAccommodation).filter(_ == true),
      synopsis = Some(true),
      usp = None,
      personalizedInformation = Some(true),
      rateCategories = Some(true)
    )
  }

  /** To build GQL header.
    *
    * @return
    *   GQL header
    */
  private def buildHeader(): Map[String, String] = {
    var headers: Map[String, String] = Map.empty
    headers += (ServiceHeaders.AgDebug.toString  -> "true")
    headers += (ServiceHeaders.AgOrigin.toString -> origin)
    if (userId.isDefined)
      headers += (ServiceHeaders.AgUserId.toString -> userId.get)
    headers += (ServiceHeaders.AgMemberId.toString -> memberId.toString)
    if (whiteLabelKey.isDefined)
      headers += (ServiceHeaders.AgWhiteLabelKey.toString -> whiteLabelKey.get)
    headers += (ServiceHeaders.AgDeviceTypeId.toString    -> deviceTypeId.get.toString)
    if (whiteLabelId.isDefined)
      headers += (ServiceHeaders.AgWhiteLabelId.toString -> whiteLabelId.get.toString)
    headers += (ServiceHeaders.AgPlatformId.toString     -> platform.toString)
    if (systemId.isDefined)
      headers += (ServiceHeaders.AgSystemId.toString   -> systemId.get.toString)
    headers += (ServiceHeaders.AgStoreFrontId.toString -> storeFrontId.get.toString)
    headers += (ServiceHeaders.AgLanguageId.toString -> GQLLanguageMapping.langMap(
      locale
    ))
    headers += (ServiceHeaders.AgLanguageLocale.toString                                  -> locale)
    if (agForceExperiment != null) headers += (ServiceHeaders.AgForceExperiments.toString -> agForceExperiment)
    if (tracingEnable) headers += ("AG-ATF-TRACING-MODE"                                  -> "performance")

    headers
  }

  private def buildPackageRequest(packagingClientToken: Option[String], packagingISystemToken: Option[String]) =
    packagingClientToken.map(clientToken =>
      com.agoda.core.search.models.request
        .Packaging(Some(PackagingToken(clientToken, packagingISystemToken)))
    )

  /** Build up extra hotels request
    *
    * @param extraHotels
    *   list of extra hotel ids
    * @param enableFiltersForExtraHotels
    *   true or false
    * @return
    *   extra hotels request
    */
  private def buildExtraHotelRequest(
      extraHotels: List[HotelIdType],
      enableFiltersForExtraHotels: Option[Boolean]
  ): Option[ExtraHotelsRequest] =
    extraHotels.nonEmpty match {
      case true =>
        Option(
          ExtraHotelsRequest(
            extraHotelIds = extraHotels,
            enableFiltersForExtraHotels = enableFiltersForExtraHotels
          )
        )
      case _ => None
    }

  private def buildPropertyDetailsRequest: PropertyDetailsRequestItTest =
    PropertyDetailsRequestItTest(hotelIds)

  private def buildContentImagesRequest: ContentImagesRequest = {
    val pageOption = Some(
      com.agoda.content.models.request.Page(pageSize, pageNumber)
    )
    val cityIdOption = Option(cityId).map(_.toLong)
    ContentImagesRequest(
      page = pageOption,
      indexOffset = None,
      cityId = cityIdOption
    )
  }

  private def buildContentReviewScoreRequest: ContentReviewScoreRequest =
    ContentReviewScoreRequest(None, None)

  private def buildContentLocalInformationRequest: ContentLocalInformationRequest =
    ContentLocalInformationRequest(None)

  private def buildContentExperienceRequest: ContentExperienceRequest =
    ContentExperienceRequest(None)

  private def buildContentFeaturesRequest: ContentFeaturesRequest =
    ContentFeaturesRequest(None, None, None, None, None)

  private def buildContentHighlightsRequest: ContentHighlightsRequest =
    ContentHighlightsRequest(None, None, None, None)

  private def buildContentQnaRequest: ContentQnaRequest = {
    val pageOption = Some(
      com.agoda.content.models.request.Page(10, pageNumber)
    )
    ContentQnaRequest(Vector(1), pageOption)
  }

  private def buildContentTopicsRequest: TopicsRequest =
    TopicsRequest(None)

  private def buildPriceStreamMetaLabRequest: PriceStreamMetaLabRequest =
    PriceStreamMetaLabRequest(attributesId)

  private def buildBannerCampaignRequest: BannerCampaignRequestItTest =
    builders.request.BannerCampaignRequestItTest(checkInDate)

  private def buildPropertyAllotmentRequest: PropertyAllotmentSearchRequest =
    PropertyAllotmentSearchRequest(hotelId, SearchTypes.PropertyAllotmentSearch)

  private def buildContentExtraRoomSummaryRequest: ContentExtraRoomSummaryRequest =
    ContentExtraRoomSummaryRequest(Some(true))

  private def buildContentRateCategoryRequest: ContentRateCategoryRequest =
    ContentRateCategoryRequest(List(RateCategoryIdWithRoom(2954213, None, None)), None)

  private def buildGrowthProgramInfoRequest: GrowthProgramInfoRequest = if (isDatelessSearch.getOrElse(false)) {
    GrowthProgramInfoRequest(
      bookingDate,
      checkInDate,
      checkOutDate = checkInDate.plusDays(1),
      lengthOfStay = 1,
      rooms = 1,
      adults = 2,
      children = 0
    )
  } else {
    GrowthProgramInfoRequest(
      bookingDate,
      checkInDate,
      checkOutDate = checkInDate.plusDays(los),
      lengthOfStay = los,
      rooms,
      adults,
      children
    )
  }

  // scalastyle:on

}
