import com.waioeka.sbt.CucumberPlugin.{ cucumberTestReports, plugin }

name    := "papi-test-framework"
version := "1.0"

exportJars := false

val ROCKETMILES_API_CLIENT_VERSION = "5.0.4"

// Change this to another test framework if you prefer
libraryDependencies ++= Seq(
  "com.typesafe"          % "config"            % Versions.typesafeConfig,
  "io.cucumber"           % "cucumber-core"     % Versions.cucumber,
  "io.cucumber"          %% "cucumber-scala"    % Versions.cucumber,
  "io.cucumber"           % "cucumber-jvm"      % Versions.cucumber,
  "io.cucumber"           % "cucumber-junit"    % Versions.cucumber,
  "org.scalatest"        %% "scalatest"         % Versions.scalaTest,
  "org.scalatestplus"    %% "mockito-3-4"       % Versions.scalaTestPlusMockito % Test,
  "com.typesafe.akka"    %% "akka-http-core"    % Versions.akkaHttp,
  "com.agoda.rocketmiles" % "api-clients"       % ROCKETMILES_API_CLIENT_VERSION,
  ("com.agoda.commons"   %% "ag-graphql-schema" % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
  ("com.agoda.commons" %% "ag-graphql-testkit" % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
  "org.sangria-graphql" %% "sangria"          % Versions.Agoda.sangria,
  ("io.netty"            % "netty-all"        % Versions.netty).force(),
  "com.agoda"           %% "akka-http-client" % Versions.akkaHttpClient,
  "com.agoda.commons"   %% "ag-concurrency"   % Versions.Agoda.agConcurrency,
  ("com.agoda.testing"   % "cucumber-6-hook"  % Versions.Agoda.agodaJvmTesting % Runtime)
    .exclude("com.agoda.commons", "ag-observability-api")
)

libraryDependencies ~= { _.map(moduleID => Versions.commonExcludeWithoutCommonsLogging(moduleID)) }

excludeDependencies += "org.slf4j" % "slf4j-log4j12"

enablePlugins(CucumberPlugin)
// cucumber-6-hook brings com.agoda.testing.ObservabilityHook into the classpath
CucumberPlugin.glues     := List("steps", "com.agoda.testing")
CucumberPlugin.mainClass := "io.cucumber.core.cli.Main"
CucumberPlugin.features  := List("it/src/test/resources/features")

cucumberTestReports := new File(new File(target.value, "test-reports"), "cucumber")

plugin := {
  import com.waioeka.sbt.Plugin._
  val cucumberDir = cucumberTestReports.value
  IO.createDirectory(cucumberDir)
  List(
    PrettyPlugin,
    HtmlPlugin(new File(cucumberDir, "cucumber.html")),
    JsonPlugin(new File(cucumberDir, "cucumber.json")),
    JunitPlugin(new File(cucumberDir, "cucumber-results.xml"))
  )
}

enablePlugins(JavaAppPackaging)

mainClass := Some("CucumberBoot")

Universal / mappings +=
  (Test / packageBin).map { jar =>
    jar -> ("lib/" + jar.getName)
  }.value

def getFileUtil(previousDirectory: String, f: File): Seq[(File, String)] =
  if (f.isDirectory) {
    val dirName = f.getName
    val prefix  = if (previousDirectory.isEmpty) s"$dirName/" else s"$previousDirectory/$dirName/"
    val files   = f.listFiles()
    files.flatMap(dirFile => getFileUtil(prefix, dirFile))
  } else {
    Seq(f -> s"$previousDirectory/${f.name}")
  }

Universal / mappings ++= {
  getFileUtil("", (Test / sourceDirectory).value / "resources")
}
