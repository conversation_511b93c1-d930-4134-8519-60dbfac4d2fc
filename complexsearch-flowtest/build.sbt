name := "complexsearch-flowtest"

libraryDependencies ++= Seq(
  /** JAVA LIB * */
  "io.netty" % "netty-all" % Versions.netty % Test,

  /** SCALA LIB * */
  "com.sksamuel.elastic4s"     %% "elastic4s-core"    % Versions.elastic4s        % Test,
  "com.sksamuel.elastic4s"     %% "elastic4s-testkit" % Versions.elastic4s        % Test,
  "org.testcontainers"          % "elasticsearch"     % Versions.esTestcontainers % Test,
  "org.testcontainers"          % "testcontainers"    % Versions.esTestcontainers % Test,
  "org.ow2.asm"                 % "asm"               % Versions.asm              % Test,
  "org.ow2.asm"                 % "asm-commons"       % Versions.asm              % Test,
  "org.ow2.asm"                 % "asm-tree"          % Versions.asm              % Test,
  "org.ow2.asm"                 % "asm-util"          % Versions.asm              % Test,
  "org.eclipse.jetty.aggregate" % "jetty-all"         % "9.4.15.v20190215"        % Test,
  "com.github.tomakehurst"      % "wiremock-jre8"     % Versions.wiremock         % Test
)

libraryDependencies ~= { _.map(moduleID => Versions.commonExclude(moduleID)) }
