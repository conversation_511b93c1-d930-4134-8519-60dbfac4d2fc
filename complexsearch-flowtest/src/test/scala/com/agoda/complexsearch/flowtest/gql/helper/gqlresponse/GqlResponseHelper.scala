package com.agoda.complexsearch.flowtest.gql.helper.gqlresponse

import com.agoda.content.models.circe.Serialization._
import com.agoda.content.models.propertycontent.{
  ContentCombinedReviewScore,
  ContentReviewScoreResponse,
  ContentSummary
}
import com.agoda.core.search.models.enumeration.matrix.MatrixGroupType
import com.agoda.core.search.models.response.matrix.{ MatrixGroupResult, MatrixItemResult }
import com.agoda.core.search.models.response.{
  Histogram,
  PropertyLandmarkInfo,
  SearchInfo,
  SortMatrixResult,
  UrgencyDetail
}
import com.agoda.papi.pricing.supply.models.GrowthProgramInfo
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultType
import com.agoda.papi.search.internalmodel.growthprogram.PAPIGrowthProgramInfo
import com.agoda.papi.search.internalmodel.response._
import com.agoda.papi.search.internalmodel.{ PropertyDetail, PropertySummaryEnrichment }
import com.agoda.papi.search.types.HotelIdType
import com.agoda.pricing.models.circe.Encoders._
import com.agoda.papi.search.common.graphql.Codecs._
import io.circe.derivation.deriveDecoder
import io.circe.{ Decoder, Json }
import models.{ DayuseInfo, HotelPricing }

trait GqlTestResponseHelper {

  case class SearchResultObject(
      searchInfo: SearchInfo,
      sortMatrix: Option[SortMatrixResult],
      histogram: Histogram,
      urgencyDetail: Option[UrgencyDetail],
      cid: Option[Int],
      isFreeTextSortMatch: Option[Boolean]
  )

  import com.agoda.papi.search.internalmodel.serializer.decoder.ResponseDecoders._
  implicit val searchResultObjectDecoder: Decoder[SearchResultObject] = deriveDecoder[SearchResultObject]

  implicit class JsonParserHelper(result: Json) {
    def propertiesList: List[PropertySummary] =
      getProperties(result)
    def searchResult: Option[SearchResultObject] =
      getSearchResult(result)
    def propertyDetailsResult: List[PropertyDetail] =
      getPropertyDetails(result)
    def contentCombinedReviewScoreResult: List[ContentCombinedReviewScore] =
      getContentCombinedReviewScore(result)
    def growthProgramInfoResult: Map[HotelIdType, Option[PAPIGrowthProgramInfo]] =
      getGrowthProgramInfo(result)
    def matrixGroupResults: List[MatrixGroupResult] =
      getMatrixGroupResults(result)
    def propertyLandmarkInfos: List[PropertyLandmarkInfo] =
      getPropertyLandmarkInfos(result)
    def topSellingPointResult: Map[HotelIdType, PropertySummaryEnrichment] =
      getTopSellingPoints(result)
    def propertyResultType: Map[HotelIdType, PropertyResultType] =
      getPropertiesResultType(result)
    def pageToken: Option[String] =
      getPageToken(result)
    def propertyGrowthProgramInfoMap: Map[HotelIdType, Option[GrowthProgramInfo]] =
      getPropertyGrowthProgramInfoMap(result)
    def dayuseInfoMap: Map[HotelIdType, Option[DayuseInfo]] =
      getDayUseInfoMap(result)
    def isConnectedTripApplicable: Option[Boolean] =
      getIsConnectedTripApplicable(result)
    def containsProperties: Boolean         = containsPropertiesObject(result)
    def containsMatrixGroupResults: Boolean = containsMatrixGroupResultsObject(result)
    def featuredPulseProperties: List[PropertySummary] =
      getFeaturedPulseProperties(result)
    def containsFeaturedPulseProperties: Boolean = containsFeaturedPulsePropertiesObject(result)
  }

  implicit class AggregationResponseHelper(matrixGroupResults: List[MatrixGroupResult]) {
    def findItemsByGroup(matrixGroup: MatrixGroupType): List[MatrixItemResult] =
      matrixGroupResults.find(m => m.matrixGroup == matrixGroup).map(_.matrixItemResults).getOrElse(List.empty)
  }

  // TODO: FIX missing feild in .
  implicit class PropertySummaryHelper(propertySummary: PropertySummary) {

    def pricingObject: Option[HotelPricing] =
      propertySummary.pricing.as[HotelPricing] match {
        case Right(value) => Some(value)
        case Left(err)    => throw err
      }

    def contentObject: Option[ContentSummary] =
      propertySummary.content.as[ContentSummary] match {
        case Right(value) => Some(value)
        case Left(err)    => throw err
      }

  }

  def getProperties(result: Json): List[PropertySummary] = {
    val propertiesJson: Json = (result \\ "properties").head
    val properties: List[PropertySummary] = propertiesJson.as[List[PropertySummary]] match {
      case Right(value) => value
      case Left(err)    => throw err
    }
    properties
  }

  def getSearchResult(result: Json): Option[SearchResultObject] = {
    val propertiesJson: Json = (result \\ "searchResult").head
    val searchResult: Option[SearchResultObject] = propertiesJson.as[SearchResultObject] match {
      case Right(value) => Some(value)
      case Left(err)    => throw err
    }
    searchResult
  }

  def getPropertyDetails(result: Json): List[PropertyDetail] = {
    val propertyDetailJson: Json = (result \\ "propertyDetails").head
    val propertyDetailsResult =
      propertyDetailJson.as[List[PropertyDetail]] match {
        case Right(value) => value
        case Left(err)    => throw err
      }

    propertyDetailsResult
  }

  def getContentCombinedReviewScore(result: Json): List[ContentCombinedReviewScore] = {
    val contentCombinedReviewScoreJsonList = (result \\ "propertyDetails")
      .flatMap(_ \\ "contentDetail")
      .flatMap(_ \\ "contentReviewScore")
      .flatMap(_ \\ "combinedReviewScore")
    val contentCombinedReviewScoreResult =
      contentCombinedReviewScoreJsonList.filterNot(_.isNull).map { contentCombinedReviewScoreJson =>
        contentCombinedReviewScoreJson.as[ContentCombinedReviewScore] match {
          case Right(value) => value
          case Left(err)    => throw err
        }
      }
    contentCombinedReviewScoreResult
  }

  def getGrowthProgramInfo(result: Json): Map[HotelIdType, Option[PAPIGrowthProgramInfo]] = {
    val propertyDetailJson: Option[Json] = (result \\ "propertyDetails").headOption
    val propertyDetailJsonArray = propertyDetailJson match {
      case Some(value) => value.asArray
      case _           => None
    }

    val growthProgramInfoMap: Map[HotelIdType, Option[PAPIGrowthProgramInfo]] = propertyDetailJsonArray match {
      case Some(value) =>
        value.map { v =>
          val propertyId = (v \\ "propertyId").head.as[HotelIdType] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          val growthProgramInfoJson = (v \\ "growthProgramInfo").head
          val growthProgramInfo = if (growthProgramInfoJson.isNull) {
            None
          } else {
            growthProgramInfoJson.as[PAPIGrowthProgramInfo] match {
              case Right(value) => Some(value)
              case Left(err)    => throw err
            }
          }

          propertyId -> growthProgramInfo
        }.toMap
      case _ => Map.empty[HotelIdType, Option[PAPIGrowthProgramInfo]]
    }
    growthProgramInfoMap
  }

  def getPageToken(result: Json): Option[String] = {
    val propertiesJsonO: Option[Json] = ((result \\ "searchEnrichment").head \\ "pageToken").headOption
    val searchResult: Option[String] = propertiesJsonO.flatMap { propertiesJson =>
      if (propertiesJson.isNull) {
        None
      } else {
        propertiesJson.as[String] match {
          case Right(value) => Some(value)
          case Left(err)    => throw err
        }
      }
    }
    searchResult
  }

  def getPropertyGrowthProgramInfoMap(result: Json): Map[HotelIdType, Option[GrowthProgramInfo]] = {
    val propertiesJsonArray = (result \\ "properties").head.asArray

    propertiesJsonArray match {
      case Some(value) =>
        value.map { v =>
          val propertyId = (v \\ "propertyId").head.as[Long] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          val growthProgramInfoJson = ((v \\ "pricing").head \\ "growthProgramInfo").head
          val growthProgramInfo = if (growthProgramInfoJson.isNull) {
            None
          } else {
            growthProgramInfoJson.as[GrowthProgramInfo] match {
              case Right(value) => Some(value)
              case Left(err)    => throw err
            }
          }

          propertyId -> growthProgramInfo
        }.toMap
      case _ => Map.empty
    }
  }

  def getDayUseInfoMap(result: Json): Map[HotelIdType, Option[DayuseInfo]] = {
    val propertiesJsonArray = (result \\ "properties").head.asArray
    propertiesJsonArray match {
      case Some(value) =>
        value.map { v =>
          val propertyId = (v \\ "propertyId").head.as[Long] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          val dayUseInfoJson = ((v \\ "pricing").head \\ "dayuseInfo").head
          val dayuseInfo = if (dayUseInfoJson.isNull) {
            None
          } else {
            dayUseInfoJson.as[DayuseInfo] match {
              case Right(value) => Some(value)
              case Left(err)    => throw err
            }
          }

          propertyId -> dayuseInfo
        }.toMap
      case _ => Map.empty
    }
  }

  def getIsConnectedTripApplicable(json: Json): Option[Boolean] = {
    val propertiesJson0: Option[Json] = ((json \\ "searchEnrichment").head \\ "isConnectedTripApplicable").headOption
    val searchResult: Option[Boolean] = propertiesJson0.flatMap { propertiesJson =>
      if (propertiesJson.isNull) None
      else {
        propertiesJson.as[Boolean] match {
          case Right(value) => Some(value)
          case Left(err)    => throw err
        }
      }
    }
    searchResult
  }

  def getMatrixGroupResults(result: Json): List[MatrixGroupResult] = {
    val propertiesJson: Json = (result \\ "matrixGroupResults").head
    val matrixGroupResults: List[MatrixGroupResult] = propertiesJson.as[List[MatrixGroupResult]] match {
      case Right(value) => value
      case Left(err)    => throw err
    }
    matrixGroupResults
  }

  def getPropertyLandmarkInfos(result: Json): List[PropertyLandmarkInfo] = {
    val propertiesJson: Json = (result \\ "propertyLandmarkInfo").head
    val PropertyLandmarkInfo: List[PropertyLandmarkInfo] = propertiesJson.as[List[PropertyLandmarkInfo]] match {
      case Right(value) => value
      case Left(err)    => throw err
    }
    PropertyLandmarkInfo
  }

  def getTopSellingPoints(result: Json): Map[HotelIdType, PropertySummaryEnrichment] = {

    val propertiesJson: Json = (result \\ "properties").head
    val hotelEnrichmentMap: Map[HotelIdType, PropertySummaryEnrichment] = propertiesJson.asArray match {
      case Some(value) =>
        value.map { v =>
          val propertyId = ((v \\ "propertyId").head).as[HotelIdType] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          val enrichment = ((v \\ "enrichment").head).as[PropertySummaryEnrichment] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          propertyId -> enrichment
        }.toMap
      case _ => Map.empty[HotelIdType, PropertySummaryEnrichment]
    }
    hotelEnrichmentMap
  }

  def getPropertiesResultType(result: Json): Map[HotelIdType, PropertyResultType] = {
    val propertiesJson: Json = (result \\ "properties").head
    val propertyTypeMap: Map[HotelIdType, PropertyResultType] = propertiesJson.asArray match {
      case Some(value) =>
        value.map { v =>
          val propertyId = (v \\ "propertyId").head.as[HotelIdType] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          val propertyResultType = (v \\ "propertyResultType").head.as[PropertyResultType] match {
            case Right(value) => value
            case Left(t)      => throw t
          }

          propertyId -> propertyResultType
        }.toMap
      case _ => Map.empty[HotelIdType, PropertyResultType]
    }
    propertyTypeMap
  }

  def getFeaturedPulseProperties(result: Json): List[PropertySummary] = {
    val propertiesJson = (result \\ "featuredPulseProperties").head
    val properties: List[PropertySummary] = propertiesJson.as[List[PropertySummary]] match {
      case Right(value) => value
      case Left(err)    => throw err
    }
    properties
  }

  def containsPropertiesObject(result: Json): Boolean =
    (result \\ "properties").nonEmpty
  def containsMatrixGroupResultsObject(result: Json): Boolean =
    (result \\ "matrixGroupResults").nonEmpty
  def containsFeaturedPulsePropertiesObject(result: Json): Boolean =
    (result \\ "featuredPulseProperties").nonEmpty
}
