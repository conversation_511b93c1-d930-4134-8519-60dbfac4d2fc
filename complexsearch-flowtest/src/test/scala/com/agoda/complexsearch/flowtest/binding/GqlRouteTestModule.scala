package com.agoda.complexsearch.flowtest.binding

import akka.actor.{ ActorSystem, Scheduler }
import akka.http.scaladsl.server.Directive0
import com.agoda.common.graphql.query.{
  Cached<PERSON>ueryP<PERSON><PERSON>,
  PreProcessQueryValidator,
  PreProcessQueryValidatorImpl,
  QueryValidatorSettings
}
import com.agoda.common.graphql.schema.loader.SchemaLoader
import com.agoda.commons.dynamic.state.consul.ConsulStateWatcher
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.observability.api.configuration.tracing.FeatureTracingConfiguration
import com.agoda.commons.observability.internal.tracing.TraceStateExtraction
import com.agoda.papi.search.api.binding.GqlSchemaTestModule
import com.agoda.papi.search.api.http.graphql.GraphQLRoute
import com.agoda.papi.search.api.http.graphql.resolver.ResolverComposer
import com.agoda.papi.search.api.util.directive.{ PiiEncryptionDirective, PiiFilterDirective }
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.handler.datafetcher.pricestream.PriceStreamFetcherService
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.service.{
  PiiEncryptionService,
  RegulatoryBlockingService,
  RequestSegmentMetricsGenerator,
  WhiteLabelService
}
import com.agoda.papi.search.common.util.circuitbreaker.{ CircuitBreakerWrapper, NoOpCircuitBreakerWrapper }
import com.agoda.papi.search.common.util.context.{ DefaultGlobalContextService, GlobalContextService }
import com.agoda.papi.search.common.util.hotelshutdown.{
  HotelShutdownSetting,
  HotelShutdownUtils,
  HotelShutdownUtilsImpl
}
import com.agoda.papi.search.common.util.logger.{ HadoopLogger, NoOpHadoopLogger }
import com.agoda.papi.search.complexsearch.configuration.PiiEncryptionSettings
import com.agoda.papi.search.complexsearch.graphql.resolver._
import com.agoda.papi.search.complexsearch.service._
import com.agoda.papi.search.internalmodel.configuration.{
  PAPIMessageSettings,
  TopBookedCitiesSetting,
  TopBookedCitiesSettings
}
import com.agoda.papi.search.property.graphql.resolver.{ PropertyDetailResolverLevel1, PropertySummaryResolverLevel1 }
import com.agoda.papi.search.property.service.datafetcher.{ PropertyDetailsDataFetcher, PropertySummaryDataFetcher }
import com.agoda.property.search.framework.binding.module.GqlBaseTestModules
import com.github.blemale.scaffeine.{ Cache, Scaffeine }
import com.typesafe.config.{ Config, ConfigFactory }
import io.opentelemetry.api.OpenTelemetry
import io.opentracing.Tracer
import org.mockito.Mockito
import sangria.ast.Document

import scala.concurrent.ExecutionContext
import scala.util.Try
import com.agoda.commons.dynamic.state.State
import com.agoda.papi.search.api.config.SsrDelayInjectConfig

trait GqlRouteTestModule extends GqlBaseTestModules with GqlSchemaTestModule {

  private val configs = ConfigFactory.load()

  bind[State[Config]] to State.of(ConfigFactory.empty())
  bind[Scheduler] to inject[ActorSystem].scheduler
  bind[ResolverLevel1] to injected[ResolverLevel1]
  bind[ResolverLevel2] to injected[ResolverLevel2]
  bind[ResolverLevel3] to injected[ResolverLevel3]
  bind[ResolverLevel4] to injected[ResolverLevel4]
  bind[PropertyDetailResolverLevel1] to injected[PropertyDetailResolverLevel1]
  bind[PropertySummaryResolverLevel1] to injected[PropertySummaryResolverLevel1]
  bind[PropertyAllotmentSearchResolver] to injected[PropertyAllotmentSearchResolver]
  bind[PriceTrendSearchResolver] to injected[PriceTrendSearchResolver]

  bind[GraphQLRoute] to new GraphQLRoute {
    override val ec: ExecutionContext = inject[ExecutionContext]

    override val schemaLoader: SchemaLoader[GraphQLContext, Unit] =
      try
        inject[SchemaLoader[GraphQLContext, Unit]]
      catch {
        case t: Throwable =>
          errorLog(Components.PAPI, "!!!! shut down server - can't run schemaLoader !!!", Some(t))
          sys.exit(1)
      }

    override val resolverComposer: ResolverComposer                = inject[ResolverComposer]
    override val dataFetcher                                       = inject[DataFetcher]
    override val propertyDetailsDataFetcher                        = inject[PropertyDetailsDataFetcher]
    override val hotelPriceDataFetcher: PropertySummaryDataFetcher = inject[PropertySummaryDataFetcher]
    override val metricsReporter: MetricsReporter                  = inject[MetricsReporter]
    override val tracer: Tracer                                    = inject[Tracer]
    override val hadoopLogger                                      = inject[HadoopLogger]
    override val globalContextService: GlobalContextService        = inject[GlobalContextService]
    override val searchExperimentManagerService: SearchExperimentManagerService =
      inject[SearchExperimentManagerService]
    override val cachedQueryParser: CachedQueryParser               = inject[CachedQueryParser]
    override val preProcessQueryValidator: PreProcessQueryValidator = inject[PreProcessQueryValidator]
    override val webGateApiKey: String =
      Try(configs.getString("webgateApiKey")).getOrElse("63ee62a3-69a1-4c5f-9921-ab599bfbb19b")
    override val maxQueryDepth: Int = Try(configs.getInt("graphql.max-query-depth")).getOrElse(200)
    override val topBookedCitiesSetting: TopBookedCitiesSetting             = inject[TopBookedCitiesSetting]
    override val papiMessageSettings: PAPIMessageSettings                   = inject[PAPIMessageSettings]
    override val priceTrendSummaryDataFetcher: PriceTrendSummaryDataFetcher = inject[PriceTrendSummaryDataFetcher]
    override val propertyAllotmentSummaryDataFetcher: PropertyAllotmentSummaryDataFetcher =
      inject[PropertyAllotmentSummaryDataFetcher]

    override val handlePiiHeaders: Directive0 = inject[PiiEncryptionSettings].key match {
      case Some(x) => new PiiEncryptionDirective(PiiEncryptionService(x))
      case _       => new PiiFilterDirective()
    }

    override val piiEncryptionService: Option[PiiEncryptionService]             = inject[Option[PiiEncryptionService]]
    override val requestSegmentMetricsGenerator: RequestSegmentMetricsGenerator = inject[RequestSegmentMetricsGenerator]
    override val whiteLabelService: WhiteLabelService                           = inject[WhiteLabelService]
    override val scheduler: Scheduler                                           = inject[Scheduler]
    override val injectSsrDelayConfig: SsrDelayInjectConfig =
      SsrDelayInjectConfig(State.of(10d), State.of(20d), State.of(30d), State.of(true))
  }

  bind[TopBookedCitiesSetting] to TopBookedCitiesSettings()

  bind[PAPIMessageSettings] to PAPIMessageSettings()

  bind[PiiEncryptionSettings] to PiiEncryptionSettings()

  bind[Option[PiiEncryptionService]].toProvider(None)

  bind[TraceStateExtraction] to new TraceStateExtraction(FeatureTracingConfiguration.DISABLED)
  bind[GlobalContextService] to injected[DefaultGlobalContextService]

  bind[HadoopLogger] to NoOpHadoopLogger

  bind[io.opentelemetry.api.trace.Tracer] to OpenTelemetry.noop().getTracer("noop")

  bind[CachedQueryParser] to new CachedQueryParser(inject[Cache[String, Document]])
  bind[PreProcessQueryValidator] to new PreProcessQueryValidatorImpl(QueryValidatorSettings.fromConfig(configs))

  bind[HotelShutdownSetting] to HotelShutdownSetting()
  bind[HotelShutdownUtils] to new HotelShutdownUtilsImpl(inject[HotelShutdownSetting])

  bind[ConsulStateWatcher] to ConsulStateWatcher.noop()

  bind[PriceStreamDataFetcher] to injected[DefaultPriceStreamDataFetcher]

  bind[PriceStreamFetcherService] to Mockito.mock(classOf[PriceStreamFetcherService])

  bind[Cache[String, Document]] to Scaffeine().build[String, Document]()
}
