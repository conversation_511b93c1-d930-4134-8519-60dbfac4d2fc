package com.agoda.complexsearch.flowtest.gql.filter

import com.agoda.common.graphql.schema.loader.SchemaLoader
import com.agoda.complexsearch.flowtest.gql.helper.GqlTestHelpers
import com.agoda.complexsearch.flowtest.gql.helper.query.QueryGenerator
import com.agoda.complexsearch.flowtest.gql.query.GqlQueriesBuilders
import com.agoda.complexsearch.flowtest.gql.search.BaseGQLIntegrationTest
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.{ FilterRequest, IdsFilterRequest }
import com.agoda.core.search.models.enumeration.matrix.FilterKeys
import com.agoda.core.search.models.response.AvailabilityData
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.util.DFDataExample
import com.agoda.papi.search.types.{ HotelIdType, WithHotelIdMap }
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.papi.search.common.util.DFDataExample.{
  aValidConsolidatedAppliedDiscount,
  aValidPAPIDFBundleOffer,
  aValidPAPIDFCurrencyPrice,
  aValidPAPIRoomOffer
}
import com.agoda.property.search.utils.builders.GqlTestDataBuilders._
import constant.ConsolidatedAppliedDiscountType.{ downliftDiscount, promoDiscount }
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

import scala.collection.mutable
import scala.concurrent.Future

class AutoApplyWalletPromosFilterSpec
    extends AnyWordSpec with Matchers with BaseGQLIntegrationTest with GqlTestHelpers {

  val meta: PropertyInfoList = PropertyInfoList(
    generateDefaultPropertyInfoList((hotel1 to hotel3).toList).propertyInfoList
  )

  val hotelList: Seq[HotelIdType] = (hotel1 to hotel3).toList

  val avail: WithHotelIdMap[AvailabilityData] = hotelList.map { hotelId =>
    hotelId -> aValidAvailabilityData
      .withHotelId(hotelId)
      .build()
  }(collection.breakOut): mutable.Map[HotelIdType, AvailabilityData]

  val mockModuleCity: GqlMockModule = new GqlMockModule {

    mockDfService(
      Map(
        hotel1 -> DFDataExample.aValidPAPIHotelPricing(hotel1).build(),
        hotel2 -> DFDataExample
          .aValidPAPIHotelPricing(hotel2)
          .withOffers(
            Seq(
              aValidPAPIDFBundleOffer(
                List(
                  aValidPAPIRoomOffer(2, List(aValidPAPIDFCurrencyPrice)).copy(
                    consolidatedAppliedDiscount = Some(aValidConsolidatedAppliedDiscount(promoDiscount))
                  )
                )
              )
            )
          )
          .build(),
        hotel3 -> DFDataExample
          .aValidPAPIHotelPricing(hotel3)
          .withOffers(
            Seq(
              aValidPAPIDFBundleOffer(
                List(
                  aValidPAPIRoomOffer(3, List(aValidPAPIDFCurrencyPrice)).copy(
                    consolidatedAppliedDiscount = Some(aValidConsolidatedAppliedDiscount(downliftDiscount))
                  )
                )
              )
            )
          )
          .build()
      )
    )

    mockPushAvailability(Future.successful((avail, Set.empty)))
    mockCachePropertyInfoList(meta)
  }

  injectionTest(mockModuleCity) { implicit injector =>
    val schemaLoader: SchemaLoader[GraphQLContext, Unit] = inject[SchemaLoader[GraphQLContext, Unit]]

    val idsFiltersWithAutoApplyWalletPromosFilterKey =
      Some(List(IdsFilterRequest(FilterKeys.AutoApplyWalletPromos, List.empty)))

    "with AutoApplyWalletPromos Filter" should {
      "return all hotels when no promo filter in idsFilter" in {
        val request             = aValidCitySearchRequest.build()
        val fullFragment        = QueryGenerator.genFullQuery(schemaLoader.schema)
        val queryString: String = new GqlQueriesBuilders().withAggregation().build() ++ fullFragment
        val query =
          QueryBuilder.build(queryString, request, DFDataExample.aPricingRequestParameters, contentSummaryRequest)

        val result       = executeQuery(query)
        val searchResult = result.searchResult.get

        searchResult.searchInfo.totalActiveHotels should be(3L)
        searchResult.searchInfo.totalFilteredHotels should be(3L)
        result.propertiesList.map(_.propertyId) should contain theSameElementsAs List(hotel1, hotel2, hotel3)
      }

      "return only hotels having auto-applied promo when having promo filter in idsFilter" in {
        val request = aValidCitySearchRequest
          .withFilterRequest(FilterRequest(idsFilters = idsFiltersWithAutoApplyWalletPromosFilterKey))
          .build()
        val fullFragment        = QueryGenerator.genFullQuery(schemaLoader.schema)
        val queryString: String = new GqlQueriesBuilders().withAggregation().build() ++ fullFragment
        val query =
          QueryBuilder.build(queryString, request, DFDataExample.aPricingRequestParameters, contentSummaryRequest)

        val result       = executeQuery(query)
        val searchResult = result.searchResult.get

        searchResult.searchInfo.totalActiveHotels should be(3L)
        searchResult.searchInfo.totalFilteredHotels should be(2L)
        result.propertiesList.map(_.propertyId) should contain theSameElementsAs List(hotel2, hotel3)
      }
    }
  }

}
