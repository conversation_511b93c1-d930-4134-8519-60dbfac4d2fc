package com.agoda.complexsearch.flowtest.gql.resolver

import com.agoda.common.graphql.model.Wrapper
import com.agoda.common.graphql.schema.arguments.ArgumentProvider
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.content.models.circe.Serialization._
import com.agoda.content.models.propertycontent.ContentSummary
import com.agoda.core.search.framework.{ FutureTestSet, MockModule }
import com.agoda.experiments.RequestContext
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.service.RequestSegmentMetricsGenerator
import com.agoda.papi.search.common.util.CoreDataExamples._
import com.agoda.papi.search.common.util.ResolverDataExamples.{
  aValidPAPIHotelPricing,
  propertyIds,
  propertySummarySearchDeferred,
  whiteLabelClientApiMultiEnv
}
import com.agoda.papi.search.common.util.context.{ DefaultGlobalContextService, ExperimentContext }
import com.agoda.papi.search.common.util.logger.HadoopLogger
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag }
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultTypes
import com.agoda.papi.search.internalmodel.serializer.PAPIHotelPricingSerializer._
import com.agoda.papi.search.internalmodel.{ PropertySponsoredDetail, PropertySummaryEnvelope }
import com.agoda.papi.search.property.graphql.resolver.PropertySummaryResolverLevel1
import com.agoda.papi.search.property.service.datafetcher.PropertySummaryDataFetcher
import com.agoda.papi.search.utils.PAPIContentSummaryHelper._
import com.agoda.property.search.framework.binding.GqlTestBinding
import com.agoda.property.search.utils.MockitoHelper
import com.agoda.complexsearch.flowtest.gql.helper.{ MetricMatcher, SimpleMockMetricsReporter }
import com.agoda.platform.service.context.ServiceHeaders
import io.circe.syntax.EncoderOps
import org.mockito.{ ArgumentMatchers => AM }
import org.specs2.mock.Mockito
import org.specs2.mutable.Specification

import scala.concurrent.{ ExecutionContext, Future }

class PropertySummaryResolverLevel1Spec(implicit ec: ExecutionContext)
    extends Specification with FutureTestSet with Mockito with GqlTestBinding {

  implicit val hadoopLogger = mock[HadoopLogger]
  implicit val reporter     = mock[MetricsReporter]

  implicit val experimentManagerMock: SearchExperimentManagerService = {
    val managerMock = mock[SearchExperimentManagerService]
    doReturn(
      Future.successful(
        ExperimentContext(requestContext =
          Some(
            RequestContext(
              "1",
              "2",
              "3",
              "4",
              "5",
              "6",
              "7",
              "8"
            )
          )
        )
      )
    ).when(managerMock).createExperimentContext(any, any, any)(any)
    managerMock
  }

  val propertySummaryDataFetcher = mock[PropertySummaryDataFetcher]
  val contentSummary =
    ContentSummary(aValidPAPIHotelPricing.hotelId, None, None, None, None, Vector.empty, None, None, None, None)

  val expectedPropertySummaryEnvelope = List(
    PropertySummaryEnvelope(
      aValidPAPIHotelPricing.hotelId,
      Wrapper(contentSummary, contentSummary.asJson),
      Some(Wrapper(aValidPAPIHotelPricing.copy(isReady = false), aValidPAPIHotelPricing.copy(isReady = false).asJson)),
      None,
      None,
      None,
      PropertySponsoredDetail(),
      PropertyResultTypes.NormalProperty
    )
  )

  val mockGraphQLContext =
    GraphQLContext(
      mock[ArgumentProvider],
      globalContext.addHeaders(ServiceHeaders.AgPlatformId.toString -> "1007"),
      reportingDataBuilder,
      defaultRequestContext,
      Some(aValidPAPISearchResultTracker),
      piiContext,
      NoOpTracer,
      requestSegmentMetricsGenerator = new RequestSegmentMetricsGenerator(NoOpMetricsReporter)
    )

  injectionTest(new MockModule {
    bind[PropertySummaryDataFetcher] to propertySummaryDataFetcher
    bind[PropertySummaryResolverLevel1] to injected[PropertySummaryResolverLevel1]
  }) { implicit injector =>
    val propertySummaryResolverLevel1 = inject[PropertySummaryResolverLevel1]
    "Level 1 Deferred" should {
      "return list of PropertySummaryEnvelope that contains pricing" in {

        MockitoHelper
          .doReturn(Future.successful(expectedPropertySummaryEnvelope))
          .when(propertySummaryDataFetcher)
          .fetchPropertiesSummary(
            AM.eq(propertyIds.toList),
            AM.eq(propertySummarySearchDeferred.pricingRequestParameters),
            anyObject,
            anyObject,
            anyObject
          )(anyObject, anyObject)

        val summarySearchDeferredV = Vector(propertySummarySearchDeferred)
        val resolverResult =
          propertySummaryResolverLevel1.pricingPropertiesResolveLevel1(
            summarySearchDeferredV,
            mockGraphQLContext,
            AnyRef
          )
        val resolverResultCollected = summarySearchDeferredV.collect(resolverResult)

        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[List[PropertySummaryEnvelope]] shouldEqual expectedPropertySummaryEnvelope
        }
      }
      "report metrics for property readiness" in {

        val metricsReporter = new SimpleMockMetricsReporter(
          List(MetricMatcher(MeasurementKey.NotReadyPropertiesSummarySearch, 1))
        )

        MockitoHelper
          .doReturn(Future.successful(expectedPropertySummaryEnvelope))
          .when(propertySummaryDataFetcher)
          .fetchPropertiesSummary(
            AM.eq(propertyIds.toList),
            AM.eq(propertySummarySearchDeferred.pricingRequestParameters),
            anyObject,
            anyObject,
            anyObject
          )(anyObject, anyObject)

        val resolverWithMetrics = new PropertySummaryResolverLevel1(
          NoOpTracer,
          new DefaultGlobalContextService(whiteLabelClientApiMultiEnv),
          experimentManagerMock,
          propertySummaryDataFetcher,
          metricsReporter
        )

        val summarySearchDeferredV = Vector(propertySummarySearchDeferred)
        val resolverResult =
          resolverWithMetrics.pricingPropertiesResolveLevel1(
            summarySearchDeferredV,
            mockGraphQLContext,
            AnyRef
          )
        val resolverResultCollected = summarySearchDeferredV.collect(resolverResult)

        whenReady(resolverResultCollected.head) { res =>
          res.asInstanceOf[List[PropertySummaryEnvelope]] shouldEqual expectedPropertySummaryEnvelope

          whenReady(metricsReporter.promise.future) { _ =>
            val metrics = metricsReporter.sentMetrics.filter(_.metric == MeasurementKey.NotReadyPropertiesSummarySearch)
            metrics should have size 1

            val metric = metrics.head
            metric.value shouldEqual 1
            metric.tags(MeasurementTag.SearchType) shouldEqual ("PropertySummarySearch")
            metric.tags(MeasurementTag.TotalSize) shouldEqual ("1")
            metric.tags(MeasurementTag.AllPropertiesIsReady) shouldEqual ("false")
            metric.tags(MeasurementTag.AllPropertiesNotReady) shouldEqual ("true")
            metric.tags(MeasurementTag.PlatformId) shouldEqual ("1007")
          }
        }
      }
    }
  }

}
