package com.agoda.complexsearch.flowtest.gql.search

import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.complexsearch.flowtest.gql.helper.GqlTestHelpers
import com.agoda.core.search.framework.MockModule
import com.agoda.core.search.models.request.PollingInfoRequest
import com.agoda.core.search.models.response.PollingInfoResponse
import com.agoda.papi.pricing.supply.models.GrowthProgramInfo
import com.agoda.pricing.models.circe.Encoders.GrowthProgramInfoDecode
import com.agoda.papi.search.common.graphql.Codecs._
import com.agoda.papi.search.common.util.context.WithCorrelationId
import com.agoda.papi.search.common.util.ContentDataExample.aValidContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.search.common.util.DFDataExample.{ aPricingRequestParameters, aValidPAPIHotelPricing }
import com.agoda.papi.search.complexsearch.service.APOService
import com.agoda.papi.search.complexsearch.service.pricingbadge.{ PricingBadgesService, PricingBadgesServiceImpl }
import com.agoda.papi.search.internalmodel.builders.{ QueryBuilder, QueryReader }
import com.agoda.papi.search.internalmodel.database.CdbPropertyInfo
import com.agoda.papi.search.internalmodel.pricing.{ PAPIDayuseCheckInTime, PAPIDayuseInfo }
import com.agoda.papi.search.internalmodel.request.PropertySummarySearchRequest
import com.agoda.papi.search.internalmodel.response.PropertySummary
import com.agoda.papi.search.internalmodel.serializer.decoder.ResponseDecoders.PollingInfoResponseDecoder
import com.agoda.papi.search.types.{ CityIdType, PropertyId }
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.property.search.test.it.data.APOServiceMock
import com.typesafe.config.Config
import io.circe.Json
import models.DayuseInfo
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import models.DayuseCheckInTime
import scalacache.Flags

import scala.collection.mutable
import scala.concurrent.Future

class PropertySummarySearchSpec extends AnyWordSpec with Matchers with BaseGQLIntegrationTest with GqlTestHelpers {

  injectionTest(new MockModule {
    bind[APOService] to new APOServiceMock
    bind[PricingBadgesService] to PricingBadgesServiceImpl
  }) { implicit injector =>
    "GraphQL Schema" should {
      val queryString: String = QueryReader.readQueryWithFragment("/graphql/propertySummarySearch.gql")
      "Return properties summary when both PricingSummaryRequest and ContentSummaryRequest are provided" in {
        val propertySummarySearchRequest = PropertySummarySearchRequest(List(1001L, 1002L))
        val pollingInfoRequest           = PollingInfoRequest(Some("1"), Some(1))
        val query = QueryBuilder.buildPropertySummarySearchQuery(
          queryString,
          propertySummarySearchRequest,
          Some(aPricingRequestParameters),
          Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t),
          None,
          Some(pollingInfoRequest)
        )
        val result = executeQuery(query)
        result \\ dataPath should not be Nil
        result \\ errorPath should be(Nil)

        val summaryJson: Json    = (result \\ "propertySummarySearch").head
        val propertiesJson: Json = (summaryJson \\ "properties").head
        val properties: List[PropertySummary] = propertiesJson.as[List[PropertySummary]] match {
          case Right(value) => value
          case Left(err)    => throw err
        }

        properties.size should be(2)
        properties.filter(property => !property.pricing.isNull && !property.content.isNull).size should be(2)

        val pollingInfoJson: Json = (summaryJson \\ "pollingInfo").head
        val pollingInfoResponse: PollingInfoResponse = pollingInfoJson.as[PollingInfoResponse] match {
          case Right(value) => value
          case Left(err)    => throw err
        }

        pollingInfoResponse should be(new PollingInfoResponse(Some("1"), Some(1), Some(true), Some(2000)))
      }
      "Return properties summary when only PricingSummaryRequest is provided" in {
        val queryString                  = """
          query propertySummarySearch($PricingSummaryRequest: PricingRequestParameters, $PropertySummarySearchRequest: PropertySummarySearchRequest!){
            propertySummarySearch(PropertySummarySearchRequest: $PropertySummarySearchRequest){
              properties(PricingSummaryRequest: $PricingSummaryRequest){
                propertyId
                pricing {
                  hotelId
                }
                content {
                  propertyId
                }
              }
            }
          }
        """
        val propertySummarySearchRequest = PropertySummarySearchRequest(List(1001L, 1002L))
        val query = QueryBuilder.buildPropertySummarySearchQuery(
          queryString,
          propertySummarySearchRequest,
          Some(aPricingRequestParameters),
          None,
          None,
          None
        )
        val result = executeQuery(query)
        result \\ dataPath should not be Nil
        result \\ errorPath should be(Nil)

        val summaryJson: Json    = (result \\ "propertySummarySearch").head
        val propertiesJson: Json = (summaryJson \\ "properties").head
        val properties: List[PropertySummary] = propertiesJson.as[List[PropertySummary]] match {
          case Right(value) => value
          case Left(err)    => throw err
        }

        properties.size should be(2)
        properties.filter(property => !property.pricing.isNull).size should be(2)
      }
      "Return properties summary when only ContentSummaryRequest is provided" in {
        val queryString                  = """
          query propertySummarySearch($ContentSummaryRequest: ContentSummaryRequest, $PropertySummarySearchRequest: PropertySummarySearchRequest!){
            propertySummarySearch(PropertySummarySearchRequest: $PropertySummarySearchRequest){
              properties(ContentSummaryRequest: $ContentSummaryRequest){
                propertyId
                pricing {
                  hotelId
                }
                content {
                  propertyId
                }
              }
            }
          }
        """
        val propertySummarySearchRequest = PropertySummarySearchRequest(List(1001L, 1002L))
        val query = QueryBuilder.buildPropertySummarySearchQuery(
          queryString,
          propertySummarySearchRequest,
          None,
          Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t),
          None,
          None
        )
        val result = executeQuery(query)
        result \\ dataPath should not be Nil
        result \\ errorPath should be(Nil)

        val summaryJson: Json    = (result \\ "propertySummarySearch").head
        val propertiesJson: Json = (summaryJson \\ "properties").head
        val properties: List[PropertySummary] = propertiesJson.as[List[PropertySummary]] match {
          case Right(value) => value
          case Left(err)    => throw err
        }

        properties.size should be(2)
        properties.filter(property => !property.content.isNull).size should be(2)
      }
    }
  }

  "PropertySummarySearch" should {
    "return growth program info badges" in {
      val aValidPAPIHotelPricingMap = Map(
        1000L -> aValidPAPIHotelPricing(1000L),
        1001L -> aValidPAPIHotelPricing(1001L, growthProgramInfo = Some(GrowthProgramInfo(List()))),
        1002L -> aValidPAPIHotelPricing(1002L, growthProgramInfo = Some(GrowthProgramInfo(List("AGODA_PREFERRED"))))
      )
      val mockModule = new GqlMockModule {
        mockDfService(aValidPAPIHotelPricingMap, markNonMockAsUnavailable = true)
      }

      injectionTest(mockModule) { implicit injector =>
        val queryString: String = QueryReader.readQuery("/graphql/propertySummarySearchGrowthProgramInfo.gql")
        val query = QueryBuilder.buildPropertySummarySearchQuery(
          queryString,
          PropertySummarySearchRequest(List(1000L, 1001L, 1002L)),
          Some(aPricingRequestParameters),
          Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t),
          None,
          None
        )

        val result = executeQuery(query)

        result \\ dataPath should not be Nil
        result \\ errorPath should be(Nil)
        result.propertyGrowthProgramInfoMap shouldBe Map(
          1000L -> None,
          1001L -> Some(GrowthProgramInfo(List())),
          1002L -> Some(GrowthProgramInfo(List("AGODA_PREFERRED")))
        )
      }
    }
  }

  "PropertySummarySearch" should {
    "return day use info" in {
      val dayuseCheckInTimes = Seq(PAPIDayuseCheckInTime(10), PAPIDayuseCheckInTime(14))
      val dayuseInfo         = PAPIDayuseInfo(dayuseCheckInTimes)
      val hotelPricing       = aValidPAPIHotelPricing(1000L, dayuseInfo = Option(dayuseInfo))
      val aValidPAPIHotelPricingMap = Map(
        1000L -> hotelPricing
      )
      val mockModule = new GqlMockModule {
        mockDfService(aValidPAPIHotelPricingMap, markNonMockAsUnavailable = true)
      }

      injectionTest(mockModule) { implicit injector =>
        val queryString: String = QueryReader.readQuery("/graphql/propertySummarySearchDayUseInfo.gql")
        val query = QueryBuilder.buildPropertySummarySearchQuery(
          queryString,
          PropertySummarySearchRequest(List(1000L)),
          Some(aPricingRequestParameters),
          Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t),
          None,
          None
        )

        val result = executeQuery(query)

        result \\ dataPath should not be Nil
        result \\ errorPath should be(Nil)
        result.dayuseInfoMap shouldBe Map(
          1000L -> Some(DayuseInfo(List(DayuseCheckInTime(10, 0), DayuseCheckInTime(14, 0)), None, None, None))
        )
      }
    }
  }

}
