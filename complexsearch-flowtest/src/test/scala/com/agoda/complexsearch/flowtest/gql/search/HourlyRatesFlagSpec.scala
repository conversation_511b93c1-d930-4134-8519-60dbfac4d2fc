package com.agoda.complexsearch.flowtest.gql.search

import com.agoda.common.graphql.schema.loader.SchemaLoader
import com.agoda.commons.http.client.v2.HttpClient
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.complexsearch.flowtest.gql.helper.GqlTestHelpers
import com.agoda.complexsearch.flowtest.gql.helper.query.QueryGenerator
import com.agoda.complexsearch.flowtest.gql.query.GqlQueriesBuilders
import com.agoda.core.search.framework.builders.TestDataBuilders._
import com.agoda.core.search.framework.constants.TestHotels._
import com.agoda.core.search.models.response.AvailabilityData
import com.agoda.papi.search.common.framework.mock.CAPIServiceTest
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.property.search.framework.GqlMockModule
import com.agoda.papi.search.common.util.ContentDataExample.contentSummaryRequest
import com.agoda.papi.search.common.util.CoreDataExamples.aValidCitySearchRequest
import com.agoda.property.search.utils.builders.GqlTestDataBuilders._
import com.agoda.papi.search.internalmodel.builders.QueryBuilder
import com.typesafe.config.Config
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import com.agoda.papi.search.complexsearch.DFService
import org.mockito.Mockito

import scala.concurrent.{ ExecutionContext, Future }
import org.mockito.ArgumentMatchers.any

import scala.collection.JavaConverters._
import org.specs2.mock.mockito.ArgumentCapture
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.service.{ CalculateChannelResult, MockDFChunkableClient, PropertyMetaService }
import com.agoda.papi.search.common.util.{ CoreDataExamples, DFDataExample }
import com.agoda.papi.search.common.util.configuration.{
  ChunkableWrapperSettings,
  EndpointSettings,
  MergeSettings,
  SplitSettings
}
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.internalmodel.database.{ CdbPropertyInfo, ProductHotels }
import com.agoda.papi.search.internalmodel.request.PricingPropertiesRequestWrapper
import com.agoda.papi.search.types.{ HotelIdType, WithHotelIdMap }
import com.agoda.platform.service.context.ServiceHeaders
import DFDataExample.mock
import com.agoda.papi.search.cacheclient.model.PropertyInfoList
import com.agoda.papi.search.common.externaldependency.chunkable.client.{ DFChannelEnrichService, DFChunkableService }

class HourlyRatesFlagSpec extends AnyWordSpec with Matchers with BaseGQLIntegrationTest with GqlTestHelpers {
  implicit val ec: ExecutionContext = ExecutionContexts.defaultContext
  val channelEnrichService          = mock[DFChannelEnrichService]
  val defaultSettings =
    ChunkableWrapperSettings(SplitSettings(chunkSize = 1), MergeSettings(1.0), mock[EndpointSettings])

  val meta = PropertyInfoList(Vector(aValidCachePropertyInfo.withHotelId(hotel1)))

  val hourlyMeta = PropertyInfoList(
    Vector(aValidCachePropertyInfo.withHotelId(hotel1).withHourlyProviderIdList(Seq(1)))
  )

  val propertyMetaService = mock[PropertyMetaService]

  val avail: WithHotelIdMap[AvailabilityData] = WithHotelIdMap(
    hotel1 -> aValidAvailabilityData.withHotelId(hotel1).withSupplierInfo(List(aValidSupplierInfo)).build()
  )

  Mockito
    .when(channelEnrichService.enrichedChannelRequest(any(), any(), any(), any(), any(), any(), any())(any()))
    .thenReturn(Future.successful(Map(CalculateChannelResult(List(1)) -> Seq(101L))))

  Mockito
    .when(propertyMetaService.getProductHotelsByHotel(any())(any(), any()))
    .thenReturn(Future.successful(Map.empty[HotelIdType, ProductHotels]))

  "SSR Search with HourlyRates Flag" when {
    def mockModuleCity(dfService: DFChunkableService, meta: PropertyInfoList) =
      new GqlMockModule {

        mockDfService(
          Map(
            hotel1 -> DFDataExample.aValidPAPIHotelPricing(hotel1).build()
          )
        )

        bind[DFService] to dfService
        mockPushAvailability(Future.successful((avail, Set.empty)))
        mockCachePropertyInfoList(meta)
      }
    val client: HttpClient[Future] = mock[HttpClient[Future]]

    "Property with no hourly suppliers should include overridePricingRequest with hasHourlyRates as false  in DF request" in {
      val dfChunkableClient = Mockito.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        dfChunkableClient,
        defaultSettings,
        channelEnrichService,
        new CAPIServiceTest,
        propertyMetaService,
        NoOpTracer,
        Components.PAPI
      )
      injectionTest(mockModuleCity(dfChunkableService, meta)) { implicit injector =>
        val schemaLoader: SchemaLoader[GraphQLContext, Unit] = inject[SchemaLoader[GraphQLContext, Unit]]
        val citySearchRequest                                = aValidCitySearchRequest.build()
        val pricingRequestParameter                          = DFDataExample.aPricingRequestParameters.build()
        val fullFragment                                     = QueryGenerator.genFullQuery(schemaLoader.schema)
        val queryString: String = new GqlQueriesBuilders().withAggregation().build() ++ fullFragment
        val query = QueryBuilder.build(queryString, citySearchRequest, pricingRequestParameter, contentSummaryRequest)
        executeQuery(
          query,
          globalContext = CoreDataExamples.globalContext
        )

        val dfRequestCaptor = new ArgumentCapture[PricingPropertiesRequestWrapper]
        Mockito.verify(dfChunkableClient).serviceCall(dfRequestCaptor.capture, any(), any())(any(), any())
        val capturedDFRequests     = asScalaIterator(dfRequestCaptor.values.iterator()).toList
        val overridePricingRequest = capturedDFRequests.headOption.flatMap(_.pricingRequest.overridePricingRequest)
        overridePricingRequest should not be empty
        overridePricingRequest.get.hasHourlyRates shouldBe Some(false)
      }
    }

    "Property with hourly suppliers should include overridePricingRequest with hasHourlyRates as true  in DF request" in {
      val dfChunkableClient = Mockito.spy(new MockDFChunkableClient(mock[ServiceMesh], client))
      val dfChunkableService = new DFChunkableService(
        dfChunkableClient,
        defaultSettings,
        channelEnrichService,
        new CAPIServiceTest,
        propertyMetaService,
        NoOpTracer,
        Components.PAPI
      )
      injectionTest(mockModuleCity(dfChunkableService, hourlyMeta)) { implicit injector =>
        val schemaLoader: SchemaLoader[GraphQLContext, Unit] = inject[SchemaLoader[GraphQLContext, Unit]]
        val citySearchRequest                                = aValidCitySearchRequest.build()
        val pricingRequestParameter                          = DFDataExample.aPricingRequestParameters.build()
        val fullFragment                                     = QueryGenerator.genFullQuery(schemaLoader.schema)
        val queryString: String = new GqlQueriesBuilders().withAggregation().build() ++ fullFragment
        val query = QueryBuilder.build(queryString, citySearchRequest, pricingRequestParameter, contentSummaryRequest)
        executeQuery(
          query,
          globalContext = CoreDataExamples.globalContext.addHeaders(ServiceHeaders.AgCid.toString -> "1")
        )

        val dfRequestCaptor = new ArgumentCapture[PricingPropertiesRequestWrapper]
        Mockito.verify(dfChunkableClient).serviceCall(dfRequestCaptor.capture, any(), any())(any(), any())
        val capturedDFRequests     = asScalaIterator(dfRequestCaptor.values.iterator()).toList
        val overridePricingRequest = capturedDFRequests.headOption.flatMap(_.pricingRequest.overridePricingRequest)
        overridePricingRequest should not be empty
        overridePricingRequest.get.hasHourlyRates shouldBe Some(true)
      }
    }
  }

}
