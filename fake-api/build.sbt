name := "fake-api"

libraryDependencies ++= Seq(
  ("com.agoda.testing" %% "scalatest-trait" % Versions.Agoda.agodaJvmTesting)
    .exclude("com.agoda.commons", "ag-observability-api"),
  ("com.agoda.testing" % "fake-api-framework" % Versions.Agoda.agodaJvmTesting)
    .exclude("com.agoda.commons", "ag-observability-api")
)

enablePlugins(JavaServerAppPackaging)

Compile / sources ++= (Test / sources).value
Compile / sourceDirectories ++= (Test / sourceDirectories).value
Compile / unmanagedResourceDirectories += baseDirectory.value / "../src/main/resources" // Force `Compile / compile` to copy config files in root's `src` into `fake-api`
Compile / unmanagedResources := {
  val resources = (Compile / unmanagedResources).value
  resources.filterNot { resource =>
    resource.getName == "application.conf" && resource.getParentFile.getPath.contains("../src/main/resources")
  }
}

Compile / mainClass := Some("com.agoda.papi.search.fakeapi.Boot")
