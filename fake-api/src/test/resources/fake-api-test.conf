ag-observability {
  service-name = "propertyapisearchdocker-fakeapitest"
  tracing {
    enabled = true
  }
}

ag-http-client {
  mesh {
    services {
      fake-papi {
        discovery {
          method = "static"
          static {
            hosts = [${?PROPERTYAPI_HOST_PORT}] // Replace ${?PROPERTYAPI_HOST_PORT} with devstack stack URL (without http:// part)
          }
        }
      }
    }
  }
}