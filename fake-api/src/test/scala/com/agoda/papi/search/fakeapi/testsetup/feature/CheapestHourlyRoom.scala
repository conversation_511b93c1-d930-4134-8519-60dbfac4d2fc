package com.agoda.papi.search.fakeapi.testsetup.feature

import com.agoda.content.models.db.image.ImageResponse
import com.agoda.core.search.framework.builders.TestDataBuilders.{ aValidCachePropertyInfo, aValidProductHotels }
import com.agoda.papi.search.fakeapi.testsetup.model.{
  CheapestHourlyRoomFeatureRequest,
  HotelFeatureRequest,
  HotelFeatureRequests
}
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.cacheclient.model.PropertyInfo
import com.agoda.testing.fakeapi.framework.TestIdAwareResourceAccessor
import models.starfruit.TimeInterval
import transformers.Property
import util.builders.TestDataBuilders.{ aValidEnrichedChildRoom, aValidEnrichedMasterRoom }

import scala.concurrent.Future
import scala.collection.concurrent

/** CheapestHourlyRoom handles both:
  *   1. HotelFeature: Configures PropertyInfoListCacheReader and ProductHotelsService for hourly support 2.
  *      HotelOverride: Sets the cheapestHourlyRoom field in property response
  */
class CheapestHourlyRoom extends HotelFeature with HotelOverride {

  // HotelFeature methods for service configuration
  override def preSetup(
      coreData: HotelCoreData,
      additionalData: HotelAdditionalData,
      featureRequest: HotelFeatureRequest
  ): HotelAdditionalData = additionalData

  override def setup(
      coreData: HotelCoreData,
      additionalData: HotelAdditionalData,
      featureRequest: HotelFeatureRequest
  ): Future[Unit] = {
    // Configure cache property info with hourly provider support
    val propertyInfo = aValidCachePropertyInfo
      .copy(
        hotelId = coreData.hotelId,
        hourlyProviderIdList = Seq(332)
      )

    // Store the property info in the test setup cache
    val cacheMap = new TestIdAwareResourceAccessor[concurrent.Map[Long, PropertyInfo]]()
      .getOrElseUpdate(() => new concurrent.TrieMap[Long, PropertyInfo]())

    cacheMap.put(coreData.hotelId, propertyInfo)

    // Configure ProductHotelsService to return hasHourlyRate = true for this hotel
    val productHotelsCacheMap = new TestIdAwareResourceAccessor[concurrent.Map[Long, ProductHotels]]()
      .getOrElseUpdate(() => new concurrent.TrieMap[Long, ProductHotels]())

    val productHotels = aValidProductHotels.copy(
      hotelId = coreData.hotelId,
      hasHourlyRate = true,
      masterHotelSupplierId = Some(332)
    )

    productHotelsCacheMap.put(coreData.hotelId, productHotels)

    Future.successful(())
  }

  // HotelOverride method for property field modification
  override def overrideHotel(
      coreData: HotelCoreData,
      additionalData: HotelAdditionalData,
      featureRequest: HotelFeatureRequest,
      property: Property
  ): Property = {
    val cheapestHourlyRoomRequest = featureRequest.asInstanceOf[CheapestHourlyRoomFeatureRequest]
    val isHourlyRequest           = cheapestHourlyRoomRequest.isHourlyRequest.getOrElse(false)

    // Create the hourly master room with children rooms
    val hourlyMasterRoom = aValidEnrichedMasterRoom.copy(
      images = Vector(
        ImageResponse(
          id = 89559091,
          caption = "View",
          captionId = 29,
          typeId = 7,
          group = "Rooms",
          groupId = Some("room"),
          locations = Map("a" -> "https://pix8.agoda.net/hotelImages/10637/-1/00fec9.jpg?ca=10&ce=1&s=800x600"),
          highResolutionSizes = Some(Vector("a")),
          providerId = 332
        )
      ),
      childrenRooms = List(
        aValidEnrichedChildRoom.copy(
          supplierId = Some(332L),
          hourlyAvailableSlots = Some(
            Seq(
              TimeInterval(from = "10:00", duration = 3),
              TimeInterval(from = "20:00", duration = 3),
              TimeInterval(from = "21:00", duration = 3),
              TimeInterval(from = "22:00", duration = 3),
              TimeInterval(from = "23:00", duration = 8)
            )
          )
        )
      )
    )

    if (isHourlyRequest) {
      // For hourly requests: set cheapestHourlyRoom to None
      property.copy(
        cheapestHourlyRoom = None
      )
    } else {
      // For nightly requests: set cheapestHourlyRoom
      property.copy(
        cheapestHourlyRoom = Some(hourlyMasterRoom)
      )
    }
  }

  override def extractFeatureRequest(featureRequests: HotelFeatureRequests): Option[HotelFeatureRequest] =
    featureRequests.cheapestHourlyRoom
}
