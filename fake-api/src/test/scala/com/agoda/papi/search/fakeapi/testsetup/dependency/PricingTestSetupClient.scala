package com.agoda.papi.search.fakeapi.testsetup.dependency

import com.agoda.commons.http.client.v2.{HttpClient, RequestSettings}
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.papi.search.fakeapi.testsetup.model.{PricingHotelFeatureRequests, PricingRoomFeatureRequests, PricingTestSetupRequest, PricingTestSetupResponse}
import com.agoda.papi.search.fakeapi.testsetup.model.PricingTestSetupSerializer._
import com.agoda.commons.serialization.v1.instances.circe._
import cats.instances.future._
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.testing.fakeapi.framework.DataMutator
import sttp.model.Uri

import scala.concurrent.{ExecutionContext, Future}
import scala.compat.java8.FunctionConverters._

class PricingTestSetupClient(httpClient: HttpClient[Future], mesh: ServiceMesh) {

  implicit val ec: ExecutionContext = ExecutionContexts.defaultContext

  private val pricingHotelTestSetupRequestDataMutator = new DataMutator[Long, PricingHotelFeatureRequests]()
  private val pricingRoomTestSetupRequestDataMutator  = new DataMutator[(Long, Int), PricingRoomFeatureRequests]()

  def addHotelLevelMutator(hotelId: Long, mutator: PricingHotelFeatureRequests => PricingHotelFeatureRequests): Unit =
    pricingHotelTestSetupRequestDataMutator.addMutator(hotelId, mutator.asJava)

  def addRoomLevelMutator(
      hotelId: Long,
      roomIndex: Int,
      mutator: PricingRoomFeatureRequests => PricingRoomFeatureRequests
  ): Unit =
    pricingRoomTestSetupRequestDataMutator.addMutator(hotelId -> roomIndex, mutator.asJava)

  def testSetup(pricingTestSetupRequest: PricingTestSetupRequest): Future[PricingTestSetupResponse] = {
    val mutatedPricingTestSetupRequest = applyMutators(pricingTestSetupRequest)
    mesh.request { (host, builder) =>
      httpClient.post[PricingTestSetupRequest, PricingTestSetupResponse](
        Uri(host.host, host.port).withWholePath("test/setup/v2"),
        mutatedPricingTestSetupRequest,
        builder,
        RequestSettings.default
      )(circeSerializer[PricingTestSetupRequest], circeDeserializer[PricingTestSetupResponse])
    }
  }

  private def applyMutators(pricingTestSetupRequest: PricingTestSetupRequest): PricingTestSetupRequest = {
    val mutatedPricingHotelTestSetupRequests =
      pricingTestSetupRequest.hotels.map(_.map { pricingHotelTestSetupRequest =>
        val mutatedHotelFeatures = if (pricingHotelTestSetupRequestDataMutator.contains(pricingHotelTestSetupRequest.hotelId)) {
          val defaultData = pricingHotelTestSetupRequest.features.getOrElse(PricingHotelFeatureRequests.empty)
          val mutatedData = pricingHotelTestSetupRequestDataMutator.applyMutators(pricingHotelTestSetupRequest.hotelId, defaultData)
          Some(mutatedData)
        } else {
          pricingHotelTestSetupRequest.features
        }
        val mutatedRooms =
          pricingHotelTestSetupRequest.rooms.map(_.zipWithIndex.map { case (pricingRoomTestSetupRequest, idx) =>
            val mutatedRoomFeatures =
              if (pricingRoomTestSetupRequestDataMutator.contains(pricingHotelTestSetupRequest.hotelId -> idx)) {
                val defaultData = pricingRoomTestSetupRequest.features.getOrElse(PricingRoomFeatureRequests.empty)
                val mutatedData = pricingRoomTestSetupRequestDataMutator.applyMutators(
                  pricingHotelTestSetupRequest.hotelId -> idx,
                  defaultData
                )
                Some(mutatedData)
              } else {
                pricingRoomTestSetupRequest.features
              }
            pricingRoomTestSetupRequest.copy(features = mutatedRoomFeatures)
          })
        pricingHotelTestSetupRequest.copy(features = mutatedHotelFeatures, rooms = mutatedRooms)
      })
    pricingTestSetupRequest.copy(hotels = mutatedPricingHotelTestSetupRequests)
  }

}
