package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.papi.search.fakeapi.testsetup.model.{HotelFeatureRequest, HotelFeatureRequests, AvailableFiltersFeatureRequest}
import transformers.{Property, PropertyFilters}
import util.builders.TestDataBuilders.aValidPropertyFilterTags

class AvailableFilters extends HotelOverride {

  override def overrideHotel(coreData: HotelCoreData, additionalData: HotelAdditionalData, featureRequest: HotelFeatureRequest, property: Property): Property = {
    val filtersRequest = featureRequest.asInstanceOf[AvailableFiltersFeatureRequest]
    val title = filtersRequest.title.getOrElse("")
    val tags = filtersRequest.tags.getOrElse(aValidPropertyFilterTags)
    val filterGroups = filtersRequest.filterGroups
    
    property.copy(
      availableFilters = Some(PropertyFilters(
        title = title,
        tags = tags,
        filterGroups = filterGroups.filter(_.nonEmpty)
      ))
    )
  }

  override def extractFeatureRequest(featureRequests: HotelFeatureRequests): Option[HotelFeatureRequest] = featureRequests.availableFilters
} 