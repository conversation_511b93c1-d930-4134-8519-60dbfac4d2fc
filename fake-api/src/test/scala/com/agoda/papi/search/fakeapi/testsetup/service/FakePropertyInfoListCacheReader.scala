package com.agoda.papi.search.fakeapi.testsetup.service

import com.agoda.papi.search.cacheclient.model.{PropertyInfo, PropertyInfoList}
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.complexsearch.externaldependency.propertyinfolistcache.PropertyInfoListCacheReader
import com.agoda.testing.fakeapi.framework.TestIdAwareResourceAccessor

import scala.collection.mutable
import scala.concurrent.Future
import scala.collection.concurrent

class FakePropertyInfoListCacheReader extends PropertyInfoListCacheReader {

  override def getPropertyInfoList(
      cityIds: Set[Long],
      propertyIds: mutable.Set[Long]
  )(implicit contextHolder: ContextHolder): Future[PropertyInfoList] = {
    // Get PropertyInfo data from test setup cache
    val cacheMap = new TestIdAwareResourceAccessor[concurrent.Map[Long, PropertyInfo]]()
      .getOrElseUpdate(() => new concurrent.TrieMap[Long, PropertyInfo]())

    // Filter PropertyInfo by requested propertyIds if specified
    val filteredPropertyInfo = if (propertyIds.nonEmpty) {
      cacheMap.values.filter(propertyInfo => propertyIds.contains(propertyInfo.hotelId)).toSeq
    } else {
      cacheMap.values.toSeq
    }

    Future.successful(PropertyInfoList(filteredPropertyInfo))
  }

  override def isCacheReady(): Future[Boolean] = Future.successful(true)
} 