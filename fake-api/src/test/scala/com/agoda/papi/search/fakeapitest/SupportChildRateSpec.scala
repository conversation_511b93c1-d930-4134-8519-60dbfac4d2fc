package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class SupportChildRateSpec extends FakeAPIBaseSpec {
  "SupportChildRate feature" should {
    "return default value when no feature configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {"supportChildRate": {}},
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.isSupportChildRate shouldBe Some(false)
      }
    }

    "return isSupportChildRate=true when testSetupRequest isSupportChildRate=true" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {"supportChildRate": {"isSupportChildRate": true}},
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.isSupportChildRate shouldBe Some(true)
      }
    }
  }
}
