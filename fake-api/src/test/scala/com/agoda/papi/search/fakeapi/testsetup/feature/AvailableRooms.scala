package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.papi.search.fakeapi.testsetup.model.{AvailableRoomsFeatureRequest, RoomFeatureRequest, RoomFeatureRequests}
import transformers.{EnrichedChildRoom, EnrichedMasterRoom}

class AvailableRooms extends RoomOverride {

  override def overrideMasterRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, masterRoom: EnrichedMasterRoom): EnrichedMasterRoom = masterRoom

  override def overrideChildRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, childRoom: EnrichedChildRoom): EnrichedChildRoom = {
    val count = featureRequest.asInstanceOf[AvailableRoomsFeatureRequest].count.getOrElse(1)
    childRoom.copy(
      availableRooms = Some(count)
    )
  }

  override def extractFeatureRequest(featureRequests: RoomFeatureRequests): Option[RoomFeatureRequest] = featureRequests.availableRooms
}
