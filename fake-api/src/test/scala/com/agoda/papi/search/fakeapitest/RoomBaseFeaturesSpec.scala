package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class RoomBaseFeaturesSpec extends FakeAPIBaseSpec {
  "RoomBaseFeatures feature" should {
    "return default value when isNonSmokingRoom is not provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"roomBaseFeatures": {}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.features.size shouldBe 12
        properties.property.head.masterRooms.head.features.last.name shouldBe "bathrooms"
      }
    }

    "return default value + non-smoking-room when isNonSmokingRoom=true" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"roomBaseFeatures": {"isNonSmokingRoom": true}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.features.size shouldBe 13
        properties.property.head.masterRooms.head.features.last.name shouldBe "non-smoking-room"
      }
    }
  }

}