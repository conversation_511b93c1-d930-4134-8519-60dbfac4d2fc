package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class FacilityGroupsSpec extends FakeAPIBaseSpec {
  "FacilityGroups feature" should {
    "return default value when isNonSmokingRoom is not provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"facilityGroups": {}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.facilityGroups.size shouldBe 2
        properties.property.head.masterRooms.head.facilityGroups.last.id shouldBe Some(26)
      }
    }

    "return default value + non-smoking-room when isNonSmokingRoom=true" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"facilityGroups": {"isNonSmokingRoom": true}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.facilityGroups.size shouldBe 3
        properties.property.head.masterRooms.head.facilityGroups.last.id shouldBe Some(15)
      }
    }
  }

}
