package com.agoda.papi.search.fakeapi.testsetup.feature

import com.agoda.content.models.db.rateCategories.{RateCategoriesResponse, RateCategoryAmendmentPolicyRules, RateCategoryCancellationChargeRate, RateCategoryCancellationPolicy, RateCategoryCheckTime, RateCategoryChildPolicies, RateCategoryChildPolicy, RateCategoryDetail, RateCategoryImage, RateCategoryPolicyPrice, RateCategoryResponse, RoomTypeWithDescription}
import com.agoda.papi.search.fakeapi.testsetup.model.{HotelFeatureRequest, HotelFeatureRequests, RateCategoryFeatureRequest}
import transformers.Property

class RateCategory extends HotelOverride
{

  override def overrideHotel(coreData: HotelCoreData,
                             additionalData: HotelAdditionalData,
                             featureRequest: HotelFeatureRequest, property: Property): Property = {
    val filtersRequest = featureRequest.asInstanceOf[RateCategoryFeatureRequest]
    val stayTypeId = filtersRequest.stayTypeId

    val rateCategories = property.masterRooms.flatMap { master =>
      master.childrenRooms.flatMap { room =>
        room.ratePlan.map { ratePlan =>
          RateCategoryResponse(
            rateCategoryId = ratePlan.id,
            stayPackageType = stayTypeId,
            localizedRateCategoryName = Some("Best Flexible Rate"),
            inventoryType = Some("UnknownInventoryType"),
            checkIn = Some(RateCategoryCheckTime(startTime = Some("14:00"), endTime = Some("22:00"))),
            checkOut = Some(RateCategoryCheckTime(startTime = Some("10:00"), endTime = Some("12:00"))),
            gender = Some("M"),
            detail = Some(RateCategoryDetail(
              content = Some("Your Best Flexible Rate"),
              inclusions = Some("Enjoy our Best Flexible Rate, ideal for families in Bangkok."),
              exclusions = Some("Just dummy data for exclusion"),
              termsAndConditions = Some("Terms and Conditions"),
              images = Some(List(RateCategoryImage(id = 26266267, caption = "", locations = Map("original" -> "https://pix8.agoda.net/rateplan-images/16219170/c5ffc59931b61095494ff7172a669260.jpg?ce=0")))),
              roomDescriptions = Some(List(RoomTypeWithDescription(roomTypeId = room.typeId.getOrElse(1), description = Some("Room Description 1")))),
              childPolicies = Some(RateCategoryChildPolicies(
                acceptChild = Some(true),
                policies = List(
                  RateCategoryChildPolicy(
                    description = Some("Policy 1"),
                    categoryId = 1,
                    isRoomCapacityIncluded = true,
                    minAge = Some(5),
                    maxAge = Some(12),
                    priceDiscount = RateCategoryPolicyPrice(code = Some("USD"), value = 100, unit = "USD")
                  ),
                  RateCategoryChildPolicy(
                    description = Some("Policy 2"),
                    categoryId = 2,
                    isRoomCapacityIncluded = false,
                    minAge = Some(3),
                    maxAge = Some(8),
                    priceDiscount = RateCategoryPolicyPrice(code = Some("USD"), value = 100, unit = "USD")
                  )
                )
              )),
              cancellationPolicies = Some(RateCategoryCancellationPolicy(
                feeSettingTypeId = Some(1),
                chargeRanges = List("Range1", "Range2"),
                chargeRates = List(RateCategoryCancellationChargeRate(
                  minGuest = Some(1),
                  maxGuest = Some(2),
                  charges = List(
                    RateCategoryPolicyPrice(code = Some("A"), value = 100, unit = "USD"),
                    RateCategoryPolicyPrice(code = Some("B"), value = 200, unit = "USD")
                  )
                )),
                amendmentPolicyRules =  Some(RateCategoryAmendmentPolicyRules(stayLengthRuleId = Some(10), guestSizeRuleId = Some(20)))
              )),
              supplierPlanCode = Some("SupplierPlanCode"),
              paymentNotice = Some("Payment Notice"),
              notice = Some("Notice"),
            ))
          )
        }
      }
    }

    property.copy(
      rateCategory = Some(RateCategoriesResponse(
        propertyId = coreData.hotelId,
        rateCategories = rateCategories.toVector,
        escapeRateCategories = Vector.empty
      ))
    )
  }

  override def extractFeatureRequest(featureRequests: HotelFeatureRequests): Option[HotelFeatureRequest] = featureRequests.rateCategory
}
