package com.agoda.papi.search.fakeapitest

import com.agoda.papi.search.fakeapi.testsetup.model._
import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class FreeBreakfastSpec extends FakeAPIBaseSpec {

  "Property Search with FreeBreakfast feature setup for DF Fake API" should {
    "successfully return hotel with free breakfast" in {
      // Test setup request with FreeBreakfast feature
      val hotelId = 101L
      val benefitId = 1

      val testSetupRequest =
        s"""
          |{
          |  "hotels": [
          |    {
          |      "hotelId": $hotelId,
          |      "rooms": [
          |        {"features": {"freeBreakfast": {"benefitId": $benefitId}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin

      // Property search request for the same hotel
      val propertyRequest = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(hotelId)),
          pricing = Some(aValidPricingRequest)
        )

      // Execute test setup and property search
      val resultF = executePropertySearchWithTestSetup(testSetupRequest, propertyRequest)
      
      whenReady(resultF) { case (testSetupResponse, properties) =>
        // Verify test setup was successful
        testSetupResponse.error shouldBe None
        
        // Verify the test setup response shows the hotel and room were created successfully
        val hotelResponse = testSetupResponse.data.get.hotels.flatMap(_.find(_.hotelId == hotelId))
        hotelResponse shouldBe defined
        hotelResponse.get.status shouldBe Status.Success
        hotelResponse.get.rooms.map(_.nonEmpty) shouldBe Some(true)

        // Check if the property exists in the response
        properties.property.map(_.propertyId) should contain(hotelId)
        
        // Get the hotel rooms from the response
        val hotel = properties.property.find(_.propertyId == hotelId)
        hotel.isDefined shouldBe true
        hotel.get.masterRooms.size should be > 0
        val masterRoom = hotel.get.masterRooms.head
        masterRoom.childrenRooms.nonEmpty shouldBe true
        val childrenRoom = masterRoom.childrenRooms.head

        // Check if freeBreakfast benefit information is available
        childrenRoom.benefits.exists(_.id == benefitId) shouldBe true
        childrenRoom.isBreakfastIncluded shouldBe Some(true)
      }
    }
  }
} 