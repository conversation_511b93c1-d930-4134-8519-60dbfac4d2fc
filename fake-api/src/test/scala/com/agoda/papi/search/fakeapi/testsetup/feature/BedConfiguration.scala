package com.agoda.papi.search.fakeapi.testsetup.feature

import com.agoda.content.models.db.information.{BedConfigurationResponse, BedDetailResponse, BedOptionResponse, BedResponse, BedroomConfigurationResponse}
import com.agoda.papi.search.fakeapi.testsetup.model.{RoomFeatureRequest, RoomFeatureRequests}
import transformers.{EnrichedChildRoom, EnrichedMasterRoom}

class BedConfiguration extends RoomOverride {

  override def overrideMasterRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, masterRoom: EnrichedMasterRoom): EnrichedMasterRoom = masterRoom.copy(
    bedConfiguration2 = Some(
      BedConfigurationResponse(
        bed = None,
        configuration = Some(BedDetailResponse(
          title = "Sleeping arrangements",
          symbol = "bed-detail",
          bedrooms = Vector(BedroomConfigurationResponse(
            title = "Common space",
            options = Vector(
              BedOptionResponse(
                beds = Vector(BedResponse(
                  typeId = "Single",
                  name = "2 single beds",
                  symbol = Some("single-bed"),
                  quantity = 2,
                  bedTypeName = Some("Single bed")
                ))
              ),
              BedOptionResponse(
                beds = Vector(BedResponse(
                  typeId = "Double",
                  name = "1 double bed",
                  symbol = Some("double-bed"),
                  quantity = 1,
                  bedTypeName = Some("Double bed")
                ))
              )
            )
          ))
        ))
      )
    )
  )

  override def overrideChildRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, childRoom: EnrichedChildRoom): EnrichedChildRoom = childRoom

  override def extractFeatureRequest(featureRequests: RoomFeatureRequests): Option[RoomFeatureRequest] = featureRequests.bedConfiguration
}
