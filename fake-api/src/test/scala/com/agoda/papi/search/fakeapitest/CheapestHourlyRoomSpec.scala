package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aHourlyPricingRequest, aValidContext, aValidPropertyRequest, aValidPricingRequest}
import util.builders.requestbuilder.FeatureFlagRequestBuilder

class CheapestHourlyRoomSpec extends FakeAPIBaseSpec {
  "CheapestHourlyRoom feature" should {
    "return null cheapestHourlyRoom for hourly requests when feature is enabled and isHourlyRequest = true" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {
          |        "cheapestHourlyRoom": {
          |          "isHourlyRequest": true
          |        }
          |      },
          |      "rooms": [{}]
          |    }
          |  ]
          |}
          |""".stripMargin
      
      val featureFlagRequest = FeatureFlagRequestBuilder.newBuilder()
        .withShowCheapestHourlyRate(Some(true))
        .build()
      
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aHourlyPricingRequest),
          featureFlag = Some(featureFlagRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val property = properties.property.head
        // For hourly requests: cheapestHourlyRoom should be None
        property.cheapestHourlyRoom should be(None)
      }
    }

    "return cheapestHourlyRoom for nightly requests when feature is enabled and isHourlyRequest = false" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {
          |        "cheapestHourlyRoom": {
          |          "isHourlyRequest": false
          |        }
          |      },
          |      "rooms": [{}]
          |    }
          |  ]
          |}
          |""".stripMargin
      
      val featureFlagRequest = FeatureFlagRequestBuilder.newBuilder()
        .withShowCheapestHourlyRate(Some(true))
        .build()
      
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest),
          featureFlag = Some(featureFlagRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val property = properties.property.head
        // For nightly requests: cheapestHourlyRoom should be populated
        property.cheapestHourlyRoom should not be None
        
        val cheapestHourlyRoom = property.cheapestHourlyRoom.get
        cheapestHourlyRoom.childrenRooms should not be empty
        cheapestHourlyRoom.images should not be empty
        cheapestHourlyRoom.childrenRooms.head.hourlyAvailableSlots should not be None
      }
    }
  }
} 