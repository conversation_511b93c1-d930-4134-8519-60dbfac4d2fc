package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.content.models.db.information.RoomFeatureResponse
import com.agoda.papi.search.fakeapi.testsetup.model.{RoomBaseFeaturesFeatureRequest, RoomFeatureRequest, RoomFeatureRequests}
import transformers.{EnrichedChildRoom, EnrichedMasterRoom}

class RoomBaseFeatures extends RoomOverride {

  override def overrideMasterRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, masterRoom: EnrichedMasterRoom): EnrichedMasterRoom = {
    val request = featureRequest.asInstanceOf[RoomBaseFeaturesFeatureRequest]
    val isNonSmokingRoom = request.isNonSmokingRoom.getOrElse(false)
    val baseFeatures = Vector(
      RoomFeatureResponse(name = "size", text = "43 m²/463 ft²", symbol = "sqm"),
      RoomFeatureResponse(name = "view", text = "City view", symbol = "views"),
      RoomFeatureResponse(name = "smoking-allowed", text = "Smoking Room", symbol = "smoking-allowed"),
      RoomFeatureResponse(name = "bathtub", text = "Shower and bathtub", symbol = "bathtub"),
      RoomFeatureResponse(name = "shower", text = "Shower", symbol = "shower"),
      RoomFeatureResponse(name = "shower-and-bathtub", text = "Shower bathtub", symbol = "shower-and-bathtub"),
      RoomFeatureResponse(name = "separate-shower-and-tub", text = "Separate Shower and bathtub", symbol = "separate-shower-and-bathtub"),
      RoomFeatureResponse(name = "private-pool", text = "Private pool", symbol = "private-pool"),
      RoomFeatureResponse(name = "infantCotAvailable", text = "Baby cot available", symbol = "baby-cot"),
      RoomFeatureResponse(name = "room-size-sqm", text = "43", symbol = "room-size-sqm"),
      RoomFeatureResponse(name = "room-size-sqft", text = "463", symbol = "room-size-sqft"),
      RoomFeatureResponse(name = "bathrooms", text = "Shared Bathrooms", symbol = "bathrooms")
    )
    val features = if (isNonSmokingRoom) {
      baseFeatures :+ RoomFeatureResponse(name = "non-smoking-room", text = "Non-smoking", symbol = "non-smoking-room")
    } else {
      baseFeatures
    }
    
    masterRoom.copy(
      features = features
    )
  }

  override def overrideChildRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, childRoom: EnrichedChildRoom): EnrichedChildRoom = childRoom

  override def extractFeatureRequest(featureRequests: RoomFeatureRequests): Option[RoomFeatureRequest] = featureRequests.roomBaseFeatures
} 