package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.papi.search.fakeapi.testsetup.model.{HotelFeatureRequest, HotelFeatureRequests, SupportChildRateFeatureRequest}
import transformers.Property

class SupportChildRate extends HotelOverride {

  override def overrideHotel(coreData: HotelCoreData, additionalData: HotelAdditionalData, featureRequest: HotelFeatureRequest, property: Property): Property = {
    val isSupportChildRate = featureRequest.asInstanceOf[SupportChildRateFeatureRequest].isSupportChildRate.getOrElse(false)
    property.copy(
      isSupportChildRate = Some(isSupportChildRate)
    )
  }

  override def extractFeatureRequest(featureRequests: HotelFeatureRequests): Option[HotelFeatureRequest] = featureRequests.supportChildRate
}
