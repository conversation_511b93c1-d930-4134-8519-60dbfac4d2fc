package com.agoda.papi.search.fakeapi.route.schema

import com.agoda.papi.search.fakeapi.testsetup.model.{ASOFeatureRequest, AgencyFeatureRequest, AvailableFiltersFeatureRequest, AvailableRoomsFeatureRequest, BNPLFeatureRequest, BORFeatureRequest, BedConfigurationFeatureRequest, CashbackFeatureRequest, ChannelDiscountFeatureRequest, CheapestHourlyRoomFeatureRequest, FacilityGroupsFeatureRequest, FreeBreakfastFeatureRequest, FreeCancellationFeatureRequest, HotelFeatureRequests, HotelTestSetupRequest, HotelTestSetupResponse, HourlyAvailableSlotsFeatureRequest, JTBChildRateFeatureRequest, M150FeatureRequest, PaymentFeatureRequest, RateCategoryFeatureRequest, RoomBaseFeaturesFeatureRequest, RoomBundlesFeatureRequest, RoomFeatureRequests, RoomTestSetupRequest, RoomTestSetupResponse, RurubuInventoryFeatureRequest, Status, SupportChildRateFeatureRequest, TestSetupRequest, TestSetupResponse, TestSetupResponseData}
import com.agoda.papi.search.fakeapi.testsetup.model.TestSetupSerializer._
import sttp.tapir._
import sttp.tapir.json.circe._
import transformers.{FilterGroup, FilterTag}

object TestSetupEndpoint {
  implicit lazy val FilterTagSchema: Schema[FilterTag]                                           = Schema.derived
  implicit lazy val FilterGroupSchema: Schema[FilterGroup]                                       = Schema.derived
  
  // Room-level FeatureRequest
  implicit lazy val AgencyFeatureRequestSchema: Schema[AgencyFeatureRequest]                     = Schema.derived
  implicit lazy val ASOFeatureRequestSchema: Schema[ASOFeatureRequest]                           = Schema.derived
  implicit lazy val BNPLFeatureRequestSchema: Schema[BNPLFeatureRequest]                         = Schema.derived
  implicit lazy val ChannelDiscountFeatureRequestSchema: Schema[ChannelDiscountFeatureRequest]   = Schema.derived
  implicit lazy val FreeBreakfastFeatureRequestSchema: Schema[FreeBreakfastFeatureRequest]       = Schema.derived
  implicit lazy val FreeCancellationFeatureRequestSchema: Schema[FreeCancellationFeatureRequest] = Schema.derived
  implicit lazy val JTBChildRateFeatureRequestSchema: Schema[JTBChildRateFeatureRequest]         = Schema.derived
  implicit lazy val M150FeatureRequestSchema: Schema[M150FeatureRequest]                         = Schema.derived
  implicit lazy val RurubuInventoryFeatureRequestSchema: Schema[RurubuInventoryFeatureRequest]   = Schema.derived
  implicit lazy val PaymentFeatureRequestSchema: Schema[PaymentFeatureRequest]                   = Schema.derived
  implicit lazy val BedConfigurationFeatureRequestSchema: Schema[BedConfigurationFeatureRequest] = Schema.derived
  implicit lazy val RoomBaseFeaturesFeatureRequestSchema: Schema[RoomBaseFeaturesFeatureRequest] = Schema.derived
  implicit lazy val FacilityGroupsFeatureRequestSchema: Schema[FacilityGroupsFeatureRequest]     = Schema.derived
  implicit lazy val HourlyAvailableSlotsFeatureRequestSchema: Schema[HourlyAvailableSlotsFeatureRequest]     = Schema.derived
  implicit lazy val CashbackFeatureRequestSchema: Schema[CashbackFeatureRequest]                 = Schema.derived
  implicit lazy val AvailableRoomsFeatureRequestSchema: Schema[AvailableRoomsFeatureRequest]     = Schema.derived
  
  // Hotel-level FeatureRequest
  implicit lazy val BORFeatureRequestSchema: Schema[BORFeatureRequest]                           = Schema.derived
  implicit lazy val SupportChildRateFeatureRequestSchema: Schema[SupportChildRateFeatureRequest] = Schema.derived
  implicit lazy val AvailableFiltersFeatureRequestSchema: Schema[AvailableFiltersFeatureRequest] = Schema.derived
  implicit lazy val RoomBundlesFeatureRequestSchema: Schema[RoomBundlesFeatureRequest]           = Schema.derived 
  implicit lazy val CheapestHourlyRoomFeatureRequestSchema: Schema[CheapestHourlyRoomFeatureRequest] = Schema.derived
  implicit lazy val RateCategoryFeatureRequestSchema: Schema[RateCategoryFeatureRequest]         = Schema.derived

  implicit lazy val RoomFeatureRequestsSchema: Schema[RoomFeatureRequests]     = Schema.derived
  implicit lazy val RoomTestSetupRequestSchema: Schema[RoomTestSetupRequest]   = Schema.derived
  implicit lazy val HotelFeatureRequestsSchema: Schema[HotelFeatureRequests]   = Schema.derived
  implicit lazy val HotelTestSetupRequestSchema: Schema[HotelTestSetupRequest] = Schema.derived
  implicit lazy val TestSetupRequestSchema: Schema[TestSetupRequest]           = Schema.derived

  implicit lazy val StatusSchema: Schema[Status.Value] = Schema.derivedEnumerationValue[Status.Value]
  implicit lazy val RoomTestSetupResponseSchema: Schema[RoomTestSetupResponse]   = Schema.derived
  implicit lazy val HotelTestSetupResponseSchema: Schema[HotelTestSetupResponse] = Schema.derived
  implicit lazy val TestSetupResponseDataSchema: Schema[TestSetupResponseData]   = Schema.derived
  implicit lazy val TestSetupResponseSchema: Schema[TestSetupResponse]           = Schema.derived

  val testSetupEndpoint = endpoint.post
    .in("test" / "setup" / "v2")
    .in(jsonBody[TestSetupRequest])
    .out(jsonBody[TestSetupResponse])
    .errorOut(stringBody)
    .description(
      """
      | Setup Fake PAPI to have defined feature for any given hotelId.
      |
      | To see which response field/value can be expected from each feature, please check the following test suite
      |
      | Feature Documentation: https://gitlab.agodadev.io/IT-Platform/propertyapi/-/tree/develop/fake-api/src/test/scala/com/agoda/papi/search/fakeapitest?ref_type=heads
      |""".stripMargin)

}
