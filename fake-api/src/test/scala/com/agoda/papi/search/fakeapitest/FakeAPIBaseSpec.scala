package com.agoda.papi.search.fakeapitest

import akka.http.scaladsl.model.HttpHeader
import com.agoda.commons.http.client.v2.HttpClient
import com.agoda.commons.http.client.v2.settings.{HttpClientSettings, HttpClientSettingsBuilder}
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.papi.search.fakeapi.testsetup.model.TestSetupSerializer._
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.property.client.PAPISearchAgHttpClient
import com.typesafe.config.{Config, ConfigFactory}
import request.PropertyRequest
import sttp.client3.SttpBackend
import sttp.client3.asynchttpclient.future.AsyncHttpClientFutureBackend
import transformers.Properties
import cats.instances.future._
import com.agoda.commons.observability.api.tracing.{BaggageItems, TracingModes}
import com.agoda.commons.observability.sdk.AgOpenTelemetry
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.fakeapi.testsetup.model.{TestSetupRequest, TestSetupResponse}
import com.agoda.papi.search.fakeapitest.FakeAPIBaseSpec.{agOpenTelemetry, fakePAPIHttpClient, fakePAPIServiceMesh, papiSearchAgHttpClient}
import com.agoda.testing.scala.scalatest.`trait`.ObservabilityTestExtension
import sttp.model.Uri
import io.circe.parser.decode
import io.opentelemetry.api.GlobalOpenTelemetry
import io.opentelemetry.api.baggage.Baggage
import io.opentelemetry.context.Context
import models.starfruit.{BookingFilter, BookingRequest, OverrideRoomIdentifier, RoomIdentifier}
import org.scalatest.concurrent.ScalaFutures
import org.scalatest.matchers.should.Matchers
import org.scalatest.time.{Seconds, Span}
import org.scalatest.wordspec.AnyWordSpec

import java.util.UUID
import scala.concurrent.{ExecutionContext, Future}

trait FakeAPIBaseSpec extends FakeAPIBaseSpecWithoutObservability with ObservabilityTestExtension

trait FakeAPIBaseSpecWithoutObservability extends AnyWordSpec with Matchers with ScalaFutures {

  private val tracer                = agOpenTelemetry.agTracer()
  implicit val defaultPatience      = PatienceConfig(timeout = Span(60, Seconds))
  implicit val ec: ExecutionContext = ExecutionContexts.defaultContext

  def executePropertySearchWithTestSetup(
      testSetupRequest: String,
      request: PropertyRequest,
      customHeaders: scala.collection.immutable.Seq[HttpHeader] = scala.collection.immutable.Seq.empty
  ): Future[(TestSetupResponse, Properties)] = {
    val testSetupResponseF = executeTestSetupWithoutTestId(testSetupRequest)
    testSetupResponseF.flatMap { testSetupResponse =>
      papiSearchAgHttpClient.getPropertyResponse(request, customHeaders).map { properties =>
        testSetupResponse -> properties
      }
    }
  }

  def executeBookingSearchWithTestSetup(
      testSetupRequest: String,
      request: PropertyRequest,
      customHeaders: scala.collection.immutable.Seq[HttpHeader] = scala.collection.immutable.Seq.empty
  ): Future[(TestSetupResponse, Properties)] = {
    val testSetupResponseF = executeTestSetupWithoutTestId(testSetupRequest)
    testSetupResponseF.flatMap { testSetupResponse =>
      val roomIdentifiers = testSetupResponse.data.map(_.hotels.getOrElse(Seq.empty).flatMap(_.rooms.getOrElse(Seq.empty))).getOrElse(Seq.empty).map { room =>
        RoomIdentifier(uid = room.roomIdentifier, overrideIdentifier = OverrideRoomIdentifier())
      }
      val requestWithRoomIdentifier = request.copy(
        booking = Some(BookingRequest(filters = List(BookingFilter(roomIdentifiers = roomIdentifiers.toList))))
      )
      papiSearchAgHttpClient.getPropertyForBookingResponse(requestWithRoomIdentifier, customHeaders).map { properties =>
        testSetupResponse -> properties
      }
    }
  }

  def executeTestSetupWithoutTestId(testSetupRequest: String): Future[TestSetupResponse] = {
    val testSetupResponseF = fakePAPIServiceMesh.request { (host, meshBuilder) =>
      decode[TestSetupRequest](testSetupRequest)
        .map { deserializedTestSetupRequest =>
          fakePAPIHttpClient.post[TestSetupRequest, TestSetupResponse](
            Uri(host.host, host.port).withWholePath("test/setup/v2"),
            deserializedTestSetupRequest,
            meshBuilder
          )(circeSerializer[TestSetupRequest], circeDeserializer[TestSetupResponse])
        } match {
        case Left(value)  => Future.failed(value)
        case Right(value) => value
      }
    }
    testSetupResponseF
  }

  def withTestId[T](f: => Future[T]): Future[T] =
    /** We need Future.successful(Context.root().makeCurrent()) to ensure that the TestRunner thread always has Context
      * in ThreadLocal This is because TracedExecutionContext will not clear any Context from previous runnable when
      * current ThreadLocal doesn't have any Context. This causes the issue where TracedExecutionContext uses the
      * Context from previous Runnable and causes the current Runnable to get the Context which it should not.
      */
    Future.successful(Context.root().makeCurrent()).flatMap { _ =>
      val span  = tracer.spanBuilder("Test Execution").setNoParent().startSpan()
      val scope = span.makeCurrent()
      val baggageScope = Baggage
        .current()
        .toBuilder
        .put("testId", UUID.randomUUID().toString)
        .put(BaggageItems.TRACING_MODE, TracingModes.FEATURE)
        .put(BaggageItems.TRACING_FEATURES, "${Features.RPC.SERVER_REQUEST}|${Features.RPC.SERVER_RESPONSE}")
        .build()
        .makeCurrent()
      try
        f.transform { resultT =>
          span.end()
          resultT
        }
      finally {
        baggageScope.close()
        scope.close()
      }

    }

}

object FakeAPIBaseSpec {

  lazy val config: Config = ConfigFactory.load("fake-api-test.conf")

  val agOpenTelemetry: AgOpenTelemetry = AgOpenTelemetry.of(config)

  GlobalOpenTelemetry.set(agOpenTelemetry)

  implicit val ec: ExecutionContext = ExecutionContexts.defaultContext

  lazy val fakePAPIServiceMesh: ServiceMesh = {
    val settings    = ServiceMeshSettingsBuilder("fake-papi", config).build()
    val serviceMesh = ServiceMesh(settings)
    serviceMesh
  }

  lazy val fakePAPIHttpClientSettings: HttpClientSettings = {
    val settings = HttpClientSettingsBuilder("fake-papi", config).build()
    settings
  }

  lazy val sttpBackend: SttpBackend[Future, Any] = {
    val sttpBackend = AsyncHttpClientFutureBackend()
    sttpBackend
  }

  lazy val fakePAPIHttpClient: HttpClient[Future] =
    HttpClient(fakePAPIHttpClientSettings)(sttpBackend)

  lazy val papiSearchAgHttpClient: PAPISearchAgHttpClient[Future] =
    new PAPISearchAgHttpClient[Future](fakePAPIServiceMesh, fakePAPIHttpClientSettings)(sttpBackend, implicitly)
}
