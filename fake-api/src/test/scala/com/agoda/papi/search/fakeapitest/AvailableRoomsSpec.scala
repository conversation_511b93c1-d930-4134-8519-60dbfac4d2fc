package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class AvailableRoomsSpec extends FakeAPIBaseSpec {
  "AvailableRooms feature" should {
    "return default values when no feature configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {
          |          "features": {
          |            "availableRooms": {}
          |          }
          |        }
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        childRoom.availableRooms shouldBe Some(1)  // Default count is 1
      }
    }

    "return custom available rooms when configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {
          |          "features": {
          |            "availableRooms": {
          |              "count": 5
          |            }
          |          }
          |        }
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        childRoom.availableRooms shouldBe Some(5)  // Custom count is 5
      }
    }
  }
} 