package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class RoomBundlesSpec extends FakeAPIBaseSpec {
  "RoomBundles feature" should {
    "return default values when no feature configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {
          |        "roomBundles": {}
          |      },
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val roomBundles = properties.property.head.roomBundles
        roomBundles should not be empty

        val bundle = roomBundles.head
        bundle.bundleType shouldBe 1
        bundle.bundleTypeId shouldBe None
        bundle.segments should have size 2
        bundle.mergedMasterRoom should have size 1
        bundle.mergedMasterRoom.head.maxOccupancy should be > 0
        
        bundle.segments.zipWithIndex.foreach { case (segment, index) =>
          segment.los should be > 0
          segment.quantity shouldBe Some(1)
          segment.masterRooms should have size 1
          
          val masterRoom = segment.masterRooms.head
          masterRoom.typeId shouldBe (if (index == 0) 171 else 178)
          masterRoom.maxOccupancy should be > 0
          masterRoom.images should not be empty
          masterRoom.childrenRooms should have size 1
          
          val childRoom = masterRoom.childrenRooms.head
          childRoom.roomIdentifiers should not be empty
          childRoom.capacity should not be None
          childRoom.stayPackageType shouldBe Some(0)
          childRoom.benefits should not be empty
          childRoom.filterTags should not be empty
          childRoom.campaignPromotions should not be None
          childRoom.campaignPromotions.get should not be empty
          childRoom.requestedCurrencyCode shouldBe Some("THB")
          childRoom.pricing should not be empty
          childRoom.cashback shouldBe None
          
          if (index > 0) {
            segment.checkIn.isAfter(bundle.segments(index - 1).checkIn) shouldBe true
          }
        }
      }
    }

    "return custom room bundles when configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {
          |        "roomBundles": {
          |          "bundleType": 2,
          |          "bundleTypeId": "CustomBundle",
          |          "isSingleBundleSegment": true,
          |          "hasCashback": true
          |        }
          |      },
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val roomBundles = properties.property.head.roomBundles
        roomBundles should not be empty

        val bundle = roomBundles.head
        bundle.bundleType shouldBe 2
        bundle.bundleTypeId shouldBe Some("CustomBundle")
        bundle.segments should have size 1
        bundle.mergedMasterRoom should have size 1
        bundle.mergedMasterRoom.head.maxOccupancy should be > 0
        
        val segment = bundle.segments.head
        segment.los should be > 0
        segment.quantity shouldBe Some(1)
        segment.masterRooms should have size 1
        
        val masterRoom = segment.masterRooms.head
        masterRoom.typeId shouldBe 171
        masterRoom.maxOccupancy should be > 0
        masterRoom.images should not be empty
        masterRoom.childrenRooms should have size 1
        
        val childRoom = masterRoom.childrenRooms.head
        childRoom.roomIdentifiers should not be empty
        childRoom.capacity should not be None
        childRoom.stayPackageType shouldBe Some(0)
        childRoom.benefits should not be empty
        childRoom.filterTags should not be empty
        childRoom.campaignPromotions should not be None
        childRoom.campaignPromotions.get should not be empty
        childRoom.requestedCurrencyCode shouldBe Some("THB")
        childRoom.pricing should not be empty
        childRoom.cashback should not be None
        childRoom.cashback.get.cashbackVersion should not be None
        childRoom.cashback.get.showPostCashbackPrice shouldBe true
      }
    }
  }
} 