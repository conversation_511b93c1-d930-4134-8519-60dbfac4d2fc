package com.agoda.papi.search.fakeapi.testsetup.`override`

import akka.http.scaladsl.model.{ContentTypes, HttpMethods, HttpRequest, HttpResponse}
import akka.http.scaladsl.server.{Directive, Directive0, RouteResult}
import akka.stream.Materializer
import com.agoda.commons.http.server.akka.directives.TracingDirective
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.testing.fakeapi.framework.TestIdAwareResourceAccessor
import com.agoda.papi.search.fakeapi.testsetup.engine.{ HotelTestSetupInput, RoomTestSetupInput }
import com.agoda.papi.search.fakeapi.testsetup.model.{ PricingHotelTestSetupResponse, PricingRoomTestSetupResponse }
import serializer.Serialization
import transformers.{EnrichedChildRoom, EnrichedMasterRoom, Properties}

import java.util.concurrent.ConcurrentHashMap
import scala.concurrent.duration._
import scala.concurrent.{ExecutionContext, Future}
import scala.collection.concurrent
import scala.jdk.CollectionConverters.mapAsScalaConcurrentMapConverter

class OverrideAkkaHttpMiddleware(propertyAndBookingOverrideEngine: PropertyAndBookingOverrideEngine)
    extends TracingDirective {

  private val availableAkkaOverrideEngine: Seq[AkkaOverrideEngine] = Seq(propertyAndBookingOverrideEngine)

  implicit private val ec: ExecutionContext = ExecutionContexts.defaultContext

  def createMiddleware(): Directive0 = Directive[Unit] { inner => requestContext =>
    val matchedOverrideEngine0 = availableAkkaOverrideEngine.find(_.matchedFor(requestContext.request))
    // DO NOT perform any response encoding, so we can treat the response as raw json string
    val noEncodingRequestContext = requestContext.mapRequest { originalRequest =>
      originalRequest.mapHeaders(_.filterNot(_.is("accept-encoding")))
    }
    inner()(noEncodingRequestContext).flatMap {
      case RouteResult.Complete(response) =>
        matchedOverrideEngine0 match {
          case Some(matchedOverrideEngine) => matchedOverrideEngine.overrideResponse(response).map(RouteResult.Complete)
          case None                        => Future.successful(RouteResult.Complete(response))
        }
      case x: RouteResult.Rejected => Future.successful(x)
    }
  }

}

trait AkkaOverrideEngine {
  def matchedFor(httpRequest: HttpRequest): Boolean
  def overrideResponse(httpResponse: HttpResponse): Future[HttpResponse]
}

class PropertyAndBookingOverrideEngine(materializer: Materializer) extends AkkaOverrideEngine {

  implicit private val m: Materializer      = materializer
  implicit private val ec: ExecutionContext = ExecutionContexts.defaultContext

  private val hotelTestSetupInputMap = new TestIdAwareResourceAccessor[concurrent.Map[Long, HotelTestSetupInput]]()
  private val pricingHotelTestSetupResponseMap =
    new TestIdAwareResourceAccessor[concurrent.Map[Long, PricingHotelTestSetupResponse]]()

  def matchedFor(httpRequest: HttpRequest): Boolean =
    httpRequest.method == HttpMethods.POST && (httpRequest.uri.path
      .toString() == "/api/property" || httpRequest.uri.path.toString() == "/api/booking")

  def overrideResponse(httpResponse: HttpResponse): Future[HttpResponse] = {
    val responseEntityF = httpResponse.entity.toStrict(10.seconds)
    responseEntityF.map { responseEntity =>
      val properties           = Serialization.jacksonMapper.readValue[Properties](responseEntity.data.utf8String)
      val overriddenProperties = overrideProperties(properties)
      httpResponse.withEntity(
        ContentTypes.`application/json`,
        Serialization.jacksonMapper.writeValueAsBytes(overriddenProperties)
      )
    }
  }

  def updateHotelTestSetupInput(testSetupInput: HotelTestSetupInput): Unit =
    hotelTestSetupInputMap.getOrElseUpdate(() => new ConcurrentHashMap[Long, HotelTestSetupInput]().asScala).put(testSetupInput.coreData.hotelId, testSetupInput)

  def updatePricingHotelTestSetupResponse(pricingHotelTestSetupResponse: PricingHotelTestSetupResponse): Unit =
    pricingHotelTestSetupResponseMap
      .getOrElseUpdate(() => new ConcurrentHashMap[Long, PricingHotelTestSetupResponse]().asScala)
      .put(pricingHotelTestSetupResponse.hotelId, pricingHotelTestSetupResponse)

  private def overrideProperties(properties: Properties): Properties = {
    val testSetupInput = hotelTestSetupInputMap.get().orElse(new ConcurrentHashMap[Long, HotelTestSetupInput]().asScala)
    val overriddenProperty = properties.property.map { originalProperty =>
      val hotelTestSetupInput0 = testSetupInput.get(originalProperty.propertyId)
      hotelTestSetupInput0
        .map { hotelTestSetupInput =>
          val pricingRoomTestSetupResponses =
            pricingHotelTestSetupResponseMap
              .getOrElseUpdate(() => new ConcurrentHashMap[Long, PricingHotelTestSetupResponse]().asScala)
              .get(hotelTestSetupInput.coreData.hotelId)
              .flatMap(_.rooms)
              .getOrElse(Seq.empty)
          val overriddenMasterRooms =
            if (pricingRoomTestSetupResponses.size != hotelTestSetupInput.roomTestSetupInputs.size) {
              originalProperty.masterRooms
            } else {
              hotelTestSetupInput.roomTestSetupInputs
                .zip(pricingRoomTestSetupResponses)
                .foldLeft(originalProperty.masterRooms) {
                  case (originalMasterRooms, (roomTestSetupInput, pricingRoomTestSetupResponse)) =>
                    val masterRoomsWithOverridenChildrenRooms = originalMasterRooms.map { originalMasterRoom =>
                      overrideMasterRoom(originalMasterRoom, roomTestSetupInput, pricingRoomTestSetupResponse)
                    }
                    masterRoomsWithOverridenChildrenRooms
                }
            }
          //
          // We can override $.property[*].booking.rooms here similar to what we do with overriddenMasterRooms
          //
          val overriddenProperty = hotelTestSetupInput.appliedOverrides.foldLeft(originalProperty) {
            case (p, (overridee, featureRequest)) =>
              overridee.overrideHotel(
                coreData = hotelTestSetupInput.coreData,
                additionalData = hotelTestSetupInput.additionalData,
                featureRequest = featureRequest,
                property = p
              )
          }
          overriddenProperty.copy(masterRooms = overriddenMasterRooms)
        }
        .getOrElse(originalProperty)
    }
    properties.copy(property = overriddenProperty)
  }

  private def overrideMasterRoom(
      originalMasterRoom: EnrichedMasterRoom,
      roomTestSetupInput: RoomTestSetupInput,
      pricingRoomTestSetupResponse: PricingRoomTestSetupResponse
  ): EnrichedMasterRoom = {
    val (overridenChildrenRooms, shouldOverrideMasterRoomOrNot) =
      originalMasterRoom.childrenRooms.foldLeft(Seq.empty[EnrichedChildRoom] -> false) {
        case ((overridingChildreRooms, shouldOverrideMasterRoom), originalChildrenRoom) =>
          if (originalChildrenRoom.ratePlan.exists(_.id == pricingRoomTestSetupResponse.rateCategoryId)) {
            val overridenChildrenRoom = overrideChildrenRoom(originalChildrenRoom, roomTestSetupInput)
            (overridingChildreRooms :+ overridenChildrenRoom) -> true
          } else {
            (overridingChildreRooms :+ originalChildrenRoom) -> shouldOverrideMasterRoom
          }
      }
    val overridenMasterRoom = if (shouldOverrideMasterRoomOrNot) {
      roomTestSetupInput.appliedOverrides.foldLeft(originalMasterRoom) { case (z, (overridee, featureRequest)) =>
        overridee.overrideMasterRoom(
          roomTestSetupInput.coreData,
          roomTestSetupInput.additionalData,
          featureRequest,
          z
        )
      }
    } else {
      originalMasterRoom
    }
    overridenMasterRoom.copy(
      childrenRooms = overridenChildrenRooms.toList
    )
  }

  private def overrideChildrenRoom(
      childrenRoom: EnrichedChildRoom,
      roomTestSetupInput: RoomTestSetupInput
  ): EnrichedChildRoom =
    roomTestSetupInput.appliedOverrides.foldLeft(childrenRoom) { case (z, (overridee, featureRequest)) =>
      overridee.overrideChildRoom(
        roomTestSetupInput.coreData,
        roomTestSetupInput.additionalData,
        featureRequest,
        z
      )
    }

}
