package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.content.models.db.image.ImageResponse
import com.agoda.papi.search.fakeapi.testsetup.model.{HotelFeatureRequest, HotelFeatureRequests, RoomBundlesFeatureRequest}
import enumerations.CampaignTypes
import models.starfruit.{Cashback, DisplayBasis, DisplayPrice, PromotionInfoMessage}
import org.joda.time.DateTime
import transformers.{EnrichedCapacity, Property}
import util.builders.TestDataBuilders.{aValidEnrichMasterRoomBundleSegment, aValidEnrichedBenefit, aValidEnrichedCampaign, aValidEnrichedChildRoom, aValidEnrichedMasterRoom, aValidEnrichedPricing, aValidPromotionPeekPrice, aValidRoomBundle}

class RoomBundles extends HotelOverride {

  override def overrideHotel(coreData: HotelCoreData, additionalData: HotelAdditionalData, featureRequest: HotelFeatureRequest, property: Property): Property = {
    val roomBundlesRequest = featureRequest.asInstanceOf[RoomBundlesFeatureRequest]
    val bundleType = roomBundlesRequest.bundleType.getOrElse(1)
    val bundleTypeId = roomBundlesRequest.bundleTypeId
    val hasCashback = roomBundlesRequest.hasCashback.getOrElse(false)
    val isSingleBundleSegment = roomBundlesRequest.isSingleBundleSegment.getOrElse(false)
    val enrichedPricing = aValidEnrichedPricing.copy(
      promotionPricePeek = Some(aValidPromotionPeekPrice.copy(
        promotionCode = Some("2NDABC1"),
        campaignId = Some(520471),
        cid = Some(1911841),
        promoAppliedOnFinalPrice = true,
        isMultipleCampaignPromotion = true
      ))
    )
    val masterRoom = aValidEnrichedMasterRoom.copy(
      typeId = 171,
      maxOccupancy = 2,
      images = Vector(ImageResponse(id = 89559091, caption = "View", captionId = 29, typeId = 7, group = "Rooms", groupId = Some("room"), locations = Map("a" -> "https://pix8.agoda.net/hotelImages/10637/-1/00fec9.jpg?ca=10&ce=1&s=800x600"), highResolutionSizes = Some(Vector("a")), providerId = 332)),
      childrenRooms = List(aValidEnrichedChildRoom.copy(
        roomIdentifiers = Some("CiAIkICHAxACGP7ZxAkgDCi+5y0wBEoHNUQxTl8xTlCYBRICCAEaBBACKAQ="),
        supplierId = Some(332L),
        capacity = Some(EnrichedCapacity(adults = 2, children = 1, occupancy = 2, extraBed = 0, maxExtraBed = 1, symbol = "max-occupancy-override", allowedFreeChildrenAndInfants = Some(1), numberOfGuestsWithoutRoom = 0)),
        stayPackageType = Some(0),
        availableRooms = Some(2),
        benefits = Seq(aValidEnrichedBenefit),
        filterTags = Seq("breakfast-include"),
        campaignPromotions = Some(List(aValidEnrichedCampaign.copy(
          campaignId = 520471,
          cid = 1911841,
          promotionCode = "2NDABC1",
          campaignType = CampaignTypes.PromotionCode,
          messages = Some(PromotionInfoMessage(
            campaignName = Some("Promotion Discount"),
            title = Some("2nd App Booking Coupon applied"),
            titleWithDiscount = Some("Special Discount Coupon Applied: ฿ 223 Off")
          ))
        ))),
        requestedCurrencyCode = Some("THB"),
        pricing = Map("THB" -> enrichedPricing),
        cashback = if (hasCashback) Some(Cashback(cashbackVersion = Some("Aggressive"), showPostCashbackPrice = true)) else None,
      ))
    )
    val segment1 = aValidEnrichMasterRoomBundleSegment.copy(
      checkIn = DateTime.now().plusDays(1),
      los = 2,
      quantity = Some(1),
      masterRooms = List(masterRoom)
    )
    val segment2 = aValidEnrichMasterRoomBundleSegment.copy(
      checkIn = DateTime.now().plusDays(3),
      los = 3,
      quantity = Some(1),
      masterRooms = List(masterRoom.copy(typeId = 178))
    )
    val segments = if (isSingleBundleSegment) {
      List(segment1)
    } else {
      List(
        segment1,
        segment2
      )
    }
    val displayPrice = DisplayPrice(60.0, 80.0)
    val totalPrice = DisplayBasis(displayPrice, displayPrice, displayPrice)
    
    property.copy(
      roomBundles = List(aValidRoomBundle.copy(
        bundleType = bundleType,
        bundleTypeId = bundleTypeId.filter(_.nonEmpty),
        segments = segments,
        mergedMasterRoom = List(masterRoom),
        totalPrice = totalPrice,
        saveAmount = Some(totalPrice)
      ))
    )
  }

  override def extractFeatureRequest(featureRequests: HotelFeatureRequests): Option[HotelFeatureRequest] = featureRequests.roomBundles
}
