package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}
import models.starfruit.TimeInterval

class HourlyAvailableSlotsSpec extends FakeAPIBaseSpec {
  "HourlyAvailableSlots feature" should {
    "return complete hourlyAvailableSlots when feature is enabled" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {
          |          "features": {
          |            "hourlyAvailableSlots": {}
          |          }
          |        }
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val property = properties.property.head
        property.masterRooms should not be empty
        
        val masterRoom = property.masterRooms.head
        masterRoom.childrenRooms should not be empty
        
        val childRoom = masterRoom.childrenRooms.head
        childRoom.hourlyAvailableSlots should not be None
        
        val slots = childRoom.hourlyAvailableSlots.get
        slots should have size 5
        
        // Verify the default slots from the HourlyAvailableSlots implementation
        val expectedSlots = Seq(
          TimeInterval(from = "10:00", duration = 3),
          TimeInterval(from = "20:00", duration = 3),
          TimeInterval(from = "21:00", duration = 3),
          TimeInterval(from = "22:00", duration = 3),
          TimeInterval(from = "23:00", duration = 8)
        )
        
        slots should contain theSameElementsAs expectedSlots
      }
    }
  }
} 