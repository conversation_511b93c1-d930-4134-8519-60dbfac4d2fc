package com.agoda.papi.search.fakeapi.testsetup.engine

import com.agoda.testing.fakeapi.framework.TestIdAwareResourceAccessor
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.fakeapi.testsetup.`override`.PropertyAndBookingOverrideEngine
import com.agoda.papi.search.fakeapi.testsetup.dependency.PricingTestSetupClient
import com.agoda.papi.search.fakeapi.testsetup.feature.{ASO, Agency, AvailableFilters, AvailableRooms, BNPL, BOR, BedConfiguration, Cashback, ChannelDiscount, CheapestHourlyRoom, FacilityGroups, Feature, FreeBreakfast, FreeCancellation, HotelAdditionalData, HotelBaseFeature, HotelCoreData, HotelFeature, HotelOverride, HourlyAvailableSlots, JTBChildRate, M150, Payment, RateCategory, RoomAdditionalData, RoomBaseFeature, RoomBaseFeatures, RoomBundles, RoomCoreData, RoomFeature, RoomOverride, RurubuInventory, SupportChildRate}
import com.agoda.papi.search.fakeapi.testsetup.model.{HotelFeatureRequest, HotelFeatureRequests, HotelTestSetupRequest, HotelTestSetupResponse, PricingHotelTestSetupRequest, PricingHotelTestSetupResponse, PricingRoomTestSetupRequest, PricingTestSetupRequest, RoomFeatureRequest, RoomFeatureRequests, RoomTestSetupRequest, RoomTestSetupResponse, Status, TestSetupRequest, TestSetupResponse, TestSetupResponseData}
import io.opentelemetry.context.Context

import java.util.concurrent.{ConcurrentHashMap, Executors}
import scala.collection.mutable
import scala.concurrent.{Await, ExecutionContext, Future, Promise}
import scala.concurrent.duration._
import scala.collection.concurrent
import scala.jdk.CollectionConverters.mapAsScalaConcurrentMapConverter
import scala.util.{Failure, Success}

class TestSetupEngine(
    propertyAndBookingOverrideEngine: PropertyAndBookingOverrideEngine,
    pricingTestSetupClient: PricingTestSetupClient,
    agency: Agency,
    aso: ASO,
    bor: BOR,
    bnpl: BNPL,
    channelDiscount: ChannelDiscount,
    freeBreakfast: FreeBreakfast,
    freeCancellation: FreeCancellation,
    jtbChildRate: JTBChildRate,
    m150: M150,
    rurubuInventory: RurubuInventory,
    payment: Payment,
    supportChildRate: SupportChildRate,
    bedConfiguration: BedConfiguration,
    roomBaseFeatures: RoomBaseFeatures,
    facilityGroups: FacilityGroups,
    availableFilters: AvailableFilters,
    roomBundles: RoomBundles,
    hourlyAvailableSlots: HourlyAvailableSlots,
    cashback: Cashback,
    availableRooms: AvailableRooms,
    cheapestHourlyRoom: CheapestHourlyRoom,
    rateCategory: RateCategory
) {

  implicit val ec: ExecutionContext = ExecutionContexts.defaultContext

  private val workerThread = Context.taskWrapping(Executors.newSingleThreadExecutor())
  private val hotelIdStore = new TestIdAwareResourceAccessor[concurrent.Map[Long, Future[HotelTestSetupResponse]]]()

  private val availableFeatures = Seq[Feature](
    agency,
    aso,
    bnpl,
    bor,
    channelDiscount,
    freeBreakfast,
    freeCancellation,
    jtbChildRate,
    m150,
    rurubuInventory,
    payment,
    supportChildRate,
    bedConfiguration,
    roomBaseFeatures,
    facilityGroups,
    availableFilters,
    roomBundles,
    hourlyAvailableSlots,
    cashback,
    availableRooms,
    cheapestHourlyRoom,
    rateCategory
  )

  def setup(testSetupRequest: TestSetupRequest): Future[TestSetupResponse] = {
    val promise = Promise[TestSetupResponse]()
    // Ensure that only 1 test-setup-request is processed at a time to avoid any possible race-condition
    workerThread.submit { () =>
      val testSetupResponseF = doSetup(testSetupRequest)
      try {
        Await.ready(testSetupResponseF, 1.minute)
        testSetupResponseF.value match {
          case Some(Success(value))     => promise.success(value)
          case Some(Failure(exception)) => promise.failure(exception)
          case None => promise.failure(new RuntimeException("Timeout while the worker-thread waits for test setup"))
        }
      } catch {
        case ex: Exception => promise.failure(ex)
      }
    }
    promise.future
  }

  private def doSetup(testSetupRequest: TestSetupRequest): Future[TestSetupResponse] = {
    val hotelTestSetupResponseFs = testSetupRequest.hotels.getOrElse(List.empty).map { hotelTestSetupRequest =>
      hotelIdStore.getOrElseUpdate(() => new ConcurrentHashMap[Long, Future[HotelTestSetupResponse]]().asScala).get(hotelTestSetupRequest.hotelId) match {
        case Some(hotelTestSetupResponseF) =>
          hotelTestSetupResponseF.map { hotelTestSetupResponse =>
            hotelTestSetupResponse.copy(status = Status.Duplicated)
          }
        case None =>
          val hotelTestSetupResponseF = processHotelTestSetupRequest(hotelTestSetupRequest)
          hotelIdStore.getOrElseUpdate(() => new ConcurrentHashMap[Long, Future[HotelTestSetupResponse]]().asScala).put(hotelTestSetupRequest.hotelId, hotelTestSetupResponseF)
          hotelTestSetupResponseF
      }
    }

    Future.sequence(hotelTestSetupResponseFs).map { hotelTestSetupResponses =>
      val duplicatedHotelIds = hotelTestSetupResponses.filter(_.status == Status.Duplicated).map(_.hotelId)
      val failedHotelIds     = hotelTestSetupResponses.filter(_.status == Status.Failure).map(_.hotelId)
      val error =
        if (duplicatedHotelIds.isEmpty && failedHotelIds.isEmpty) {
          None
        } else {
          Some(s"duplicated hotel-ids = $duplicatedHotelIds, failed hotel-ids = $failedHotelIds")
        }
      TestSetupResponse(data = Some(TestSetupResponseData(Some(hotelTestSetupResponses))), error = error)
    }
  }

  private def processHotelTestSetupRequest(
      hotelTestSetupRequest: HotelTestSetupRequest
  ): Future[HotelTestSetupResponse] = {
    val hotelTestSetupsF = doSetupHotel(hotelTestSetupRequest)
    hotelTestSetupsF.flatMap { hotelTestSetupInput =>
      propertyAndBookingOverrideEngine.updateHotelTestSetupInput(hotelTestSetupInput)
      val pricingRoomTestSetupRequests =
        hotelTestSetupRequest.rooms.getOrElse(List.empty).map { roomTestSetupRequest =>
          PricingRoomTestSetupRequest(roomTypeIdGroup = roomTestSetupRequest.roomTypeIdGroup, features = None)
        }
      val pricingHotelTestSetupRequest = PricingTestSetupRequest(
        hotels = Some(
          List(
            PricingHotelTestSetupRequest(
              hotelId = hotelTestSetupRequest.hotelId,
              features = None,
              rooms = Some(pricingRoomTestSetupRequests)
            )
          )
        )
      )
      pricingTestSetupClient.testSetup(pricingHotelTestSetupRequest).flatMap { pricingTestSetupResponse =>
        pricingTestSetupResponse.data.flatMap(_.hotels.flatMap(_.headOption)) match {
          case Some(pricingHotelTestSetupResponse) => {
            propertyAndBookingOverrideEngine.updatePricingHotelTestSetupResponse(pricingHotelTestSetupResponse)
            doSetupHotelAfterDF(hotelTestSetupInput, pricingHotelTestSetupResponse).map { _ =>
              val rooms =
                pricingHotelTestSetupResponse.rooms.getOrElse(Seq.empty).map { pricingRoomTestSetupResponse =>
                  RoomTestSetupResponse(
                    supplierId = pricingRoomTestSetupResponse.supplierId,
                    roomIdentifier = pricingRoomTestSetupResponse.roomIdentifier,
                    rateCategoryId = pricingRoomTestSetupResponse.rateCategoryId,
                    roomTypeId = pricingRoomTestSetupResponse.roomTypeId
                  )
                }
              HotelTestSetupResponse(
                hotelId = pricingHotelTestSetupResponse.hotelId,
                status = Status.Success,
                rooms = Some(rooms)
              )
            }
          }
          case None =>
            Future.failed(
              new RuntimeException(
                s"DF returns no response for hotel-id=${hotelTestSetupRequest.hotelId}. Error=${pricingTestSetupResponse.error}"
              )
            )
        }

      }

    }
  }

  private def doSetupHotel(
      hotelTestSetupRequest: HotelTestSetupRequest
  ): Future[HotelTestSetupInput] = {
    val hotelFeatures = availableFeatures.collect { case x: HotelBaseFeature => x }
    val appliedBaseFeatures = hotelFeatures.flatMap { feature =>
      val featureRequests = hotelTestSetupRequest.features.getOrElse(HotelFeatureRequests.empty)
      feature.extractFeatureRequest(featureRequests).map(feature -> _)
    }
    val appliedFeatures = appliedBaseFeatures.collect { case (feature: HotelFeature, featureRequest) =>
      feature -> featureRequest
    }
    val appliedOverrides = appliedBaseFeatures.collect { case (feature: HotelOverride, featureRequest) =>
      feature -> featureRequest
    }

    val coreData       = HotelCoreData(hotelId = hotelTestSetupRequest.hotelId)
    val additionalData = HotelAdditionalData()
    val updatedAdditionalData = appliedFeatures.foldLeft(additionalData) { case (result, (feature, featureRequest)) =>
      feature.preSetup(coreData, result, featureRequest)
    }

    val hotelTestSetupsF = Future.sequence(appliedFeatures.map { case (feature, featureRequest) =>
      feature.setup(coreData, updatedAdditionalData, featureRequest)
    })
    hotelTestSetupsF.flatMap { _ =>
      Future
        .sequence(hotelTestSetupRequest.rooms.getOrElse(Seq.empty).zipWithIndex.map {
          case (roomTestSetupRequest, idx) =>
            doSetupRoom(hotelTestSetupRequest, roomTestSetupRequest, idx)
        })
        .map { roomTestSetupInputs =>
          HotelTestSetupInput(
            testSetupRequest = hotelTestSetupRequest,
            appliedFeatures = appliedFeatures,
            appliedOverrides = appliedOverrides,
            coreData = coreData,
            additionalData = updatedAdditionalData,
            roomTestSetupInputs = roomTestSetupInputs
          )
        }
    }
  }

  private def doSetupRoom(
      hotelTestSetupRequest: HotelTestSetupRequest,
      roomTestSetupRequest: RoomTestSetupRequest,
      roomIndex: Int
  ): Future[RoomTestSetupInput] = {
    val roomFeatures = availableFeatures.collect { case x: RoomBaseFeature => x }
    val appliedBaseFeatures = roomFeatures.flatMap { feature =>
      val featureRequests = roomTestSetupRequest.features.getOrElse(RoomFeatureRequests.empty)
      feature.extractFeatureRequest(featureRequests).map(feature -> _)
    }
    val appliedFeatures = appliedBaseFeatures.collect { case (feature: RoomFeature, featureRequest) =>
      feature -> featureRequest
    }
    val appliedOverrides = appliedBaseFeatures.collect { case (feature: RoomOverride, featureRequest) =>
      feature -> featureRequest
    }

    val coreData       = RoomCoreData(hotelId = hotelTestSetupRequest.hotelId, roomIndex = roomIndex)
    val additionalData = RoomAdditionalData()
    val updatedAdditionalData = appliedFeatures.foldLeft(additionalData) { case (result, (feature, featureRequest)) =>
      feature.preSetup(coreData, result, featureRequest)
    }

    Future
      .sequence(appliedFeatures.map { case (feature, featureRequest) =>
        feature.setup(coreData, updatedAdditionalData, featureRequest)
      })
      .map { _ =>
        RoomTestSetupInput(
          testSetupRequest = roomTestSetupRequest,
          appliedFeatures = appliedFeatures,
          appliedOverrides = appliedOverrides,
          coreData = coreData,
          additionalData = updatedAdditionalData
        )
      }
  }

  private def doSetupHotelAfterDF(
      hotelTestSetupInput: HotelTestSetupInput,
      dfHotelTestSetupResponse: PricingHotelTestSetupResponse
  ): Future[Unit] = {
    val hotelTestSetupFs = hotelTestSetupInput.appliedFeatures.map { case (feature, featureRequest) =>
      feature.setupAfterFakeDF(
        hotelTestSetupInput.coreData,
        hotelTestSetupInput.additionalData,
        featureRequest,
        dfHotelTestSetupResponse
      )
    }
    Future.sequence(hotelTestSetupFs).flatMap { _ =>
      val roomTestSetupFs =
        hotelTestSetupInput.roomTestSetupInputs.zipWithIndex.flatMap { case (roomTestSetupInput, roomIndex) =>
          val dfRoomTestSetupResponse0 = dfHotelTestSetupResponse.rooms.flatMap(_.lift(roomIndex))
          dfRoomTestSetupResponse0.map { dfRoomTestSetupResponse =>
            val roomTestSetupFs = roomTestSetupInput.appliedFeatures.map { case (feature, featureRequest) =>
              feature.setupAfterFakeDF(
                roomTestSetupInput.coreData,
                roomTestSetupInput.additionalData,
                featureRequest,
                dfRoomTestSetupResponse
              )
            }
            Future.sequence(roomTestSetupFs).map(_ => ())
          }
        }
      Future.sequence(roomTestSetupFs).map(_ => ())
    }
  }

}

case class HotelTestSetupInput(
    testSetupRequest: HotelTestSetupRequest,
    appliedFeatures: Seq[(HotelFeature, HotelFeatureRequest)],
    appliedOverrides: Seq[(HotelOverride, HotelFeatureRequest)],
    coreData: HotelCoreData,
    additionalData: HotelAdditionalData,
    roomTestSetupInputs: Seq[RoomTestSetupInput]
)

case class RoomTestSetupInput(
    testSetupRequest: RoomTestSetupRequest,
    appliedFeatures: Seq[(RoomFeature, RoomFeatureRequest)],
    appliedOverrides: Seq[(RoomOverride, RoomFeatureRequest)],
    coreData: RoomCoreData,
    additionalData: RoomAdditionalData
)
