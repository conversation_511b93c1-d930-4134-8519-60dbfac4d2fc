package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class RoomTypeIdGroupSpec extends FakeAPIBaseSpec {

  "RoomTypeIdGroup input" should {
    "allow PAPI to return multiple master-rooms with different child-rooms" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"roomTypeIdGroup": 0, "features": {"freeBreakfast": {"benefitId": 2}}},
          |        {"roomTypeIdGroup": 1, "features": {"bnpl": {}}},
          |        {"roomTypeIdGroup": 0},
          |        {"roomTypeIdGroup": 1, "features": {"freeBreakfast": {"benefitId": 30}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (testSetupResponse, properties) =>
        val firstGroupRoomTypeId  = testSetupResponse.data.get.hotels.get.head.rooms.get.head.roomTypeId
        val secondGroupRoomTypeId = testSetupResponse.data.get.hotels.get.head.rooms.get.apply(1).roomTypeId
        properties.property.head.propertyId shouldBe 101
        properties.property.head.masterRooms.map(_.typeId) should contain theSameElementsAs Seq(
          firstGroupRoomTypeId,
          secondGroupRoomTypeId
        )

        val firstGroupMasterRoom = properties.property.head.masterRooms.find(_.typeId == firstGroupRoomTypeId).get
        firstGroupMasterRoom.childrenRooms.map(_.benefits.headOption.map(_.id)) should contain theSameElementsAs Seq(
          Some(2L),
          None
        )

        val secondGroupMasterRoom = properties.property.head.masterRooms.find(_.typeId == secondGroupRoomTypeId).get
        secondGroupMasterRoom.childrenRooms.map(_.benefits.headOption.map(_.id)) should contain theSameElementsAs Seq(
          Some(30L),
          None
        )
        secondGroupMasterRoom.childrenRooms.map(
          _.payment.map(_.payLater.isEligible)
        ) should contain theSameElementsAs Seq(Some(true), Some(false))
      }
    }
  }

}
