package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.papi.search.fakeapi.testsetup.model.{CashbackFeatureRequest, RoomFeatureRequest, RoomFeatureRequests}
import models.starfruit.Cashback
import transformers.{EnrichedChildRoom, EnrichedMasterRoom}
import util.builders.TestDataBuilders.aValidEnrichedPricing

class Cashback extends RoomOverride {

  override def overrideMasterRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, masterRoom: EnrichedMasterRoom): EnrichedMasterRoom = masterRoom

  override def overrideChildRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, childRoom: EnrichedChildRoom): EnrichedChildRoom = {
    val cashbackRequest = featureRequest.asInstanceOf[CashbackFeatureRequest]
    val cashbackVersion = cashbackRequest.cashbackVersion.getOrElse("Aggressive")
    val showPostCashbackPrice = cashbackRequest.showPostCashbackPrice.getOrElse(true)
    
    childRoom.copy(
      cashback = Some(Cashback(
        cashbackVersion = Some(cashbackVersion),
        showPostCashbackPrice = showPostCashbackPrice
      )),
      pricing = Map("THB" -> aValidEnrichedPricing)
    )
  }

  override def extractFeatureRequest(featureRequests: RoomFeatureRequests): Option[RoomFeatureRequest] = featureRequests.cashback
}
