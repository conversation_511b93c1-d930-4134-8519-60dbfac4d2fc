package com.agoda.papi.search.fakeapitest

import com.agoda.papi.search.fakeapi.testsetup.model.Status

class SimpleFakePAPISpec extends FakeAPIBaseSpecWithoutObservability {

  "Fake PAPI" should {
    "return TestSetupResponse with non-empty error field when the hotel-id is duplicated" in {
      val firstTestSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 1,
          |      "rooms": [{}]
          |    },
          |    {
          |      "hotelId": 2,
          |      "rooms": [{}]
          |    }
          |  ]
          |}
          |""".stripMargin
      val secondTestSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 2,
          |      "rooms": [{}]
          |    },
          |    {
          |      "hotelId": 3,
          |      "rooms": [{}]
          |    }
          |  ]
          |}
          |""".stripMargin

      val resultF = withTestId {
        for {
          firstTestSetupResponse  <- executeTestSetupWithoutTestId(firstTestSetupRequest)
          secondTestSetupResponse <- executeTestSetupWithoutTestId(secondTestSetupRequest)
        } yield firstTestSetupResponse -> secondTestSetupResponse

      }

      whenReady(resultF) { case (firstResponse, secondResponse) =>
        firstResponse.error shouldBe None
        firstResponse.data.get.hotels.get.map(_.status) should not contain Status.Duplicated
        firstResponse.data.get.hotels.get.map(_.hotelId) should contain theSameElementsAs Seq(1L, 2L)

        secondResponse.error should not be None
        secondResponse.data.get.hotels.get.find(_.hotelId == 2L).get.status shouldBe Status.Duplicated
        secondResponse.data.get.hotels.get.find(_.hotelId == 3L).get.status shouldBe Status.Success
      }
    }
    "return TestSetupResponse with empty error field when using duplicated hotel-id with different testId" in {
      val firstTestSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 1,
          |      "rooms": [{}]
          |    },
          |    {
          |      "hotelId": 2,
          |      "rooms": [{}]
          |    }
          |  ]
          |}
          |""".stripMargin
      val secondTestSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 2,
          |      "rooms": [{}]
          |    },
          |    {
          |      "hotelId": 3,
          |      "rooms": [{}]
          |    }
          |  ]
          |}
          |""".stripMargin

      val resultF = for {
        firstTestSetupResponse  <- withTestId(executeTestSetupWithoutTestId(firstTestSetupRequest))
        secondTestSetupResponse <- withTestId(executeTestSetupWithoutTestId(secondTestSetupRequest))
      } yield firstTestSetupResponse -> secondTestSetupResponse

      whenReady(resultF) { case (firstResponse, secondResponse) =>
        firstResponse.error shouldBe None
        firstResponse.data.get.hotels.get.map(_.status) should not contain Status.Duplicated
        firstResponse.data.get.hotels.get.map(_.hotelId) should contain theSameElementsAs Seq(1L, 2L)

        secondResponse.error shouldBe None
        secondResponse.data.get.hotels.get.map(_.status) should not contain Status.Duplicated
        secondResponse.data.get.hotels.get.map(_.hotelId) should contain theSameElementsAs Seq(2L, 3L)
      }
    }
  }

}
