package com.agoda.papi.search.fakeapi.testsetup.feature
import com.agoda.papi.search.fakeapi.testsetup.model.{RoomFeatureRequest, RoomFeatureRequests}
import models.starfruit.TimeInterval
import transformers.{EnrichedChildRoom, EnrichedMasterRoom}

class HourlyAvailableSlots extends RoomOverride {

  override def overrideMasterRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, masterRoom: EnrichedMasterRoom): EnrichedMasterRoom = masterRoom

  override def overrideChildRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, childRoom: EnrichedChildRoom): EnrichedChildRoom = childRoom.copy(
    hourlyAvailableSlots = Some(Seq(
      TimeInterval(from = "10:00", duration = 3),
      TimeInterval(from = "20:00", duration = 3),
      TimeInterval(from = "21:00", duration = 3),
      TimeInterval(from = "22:00", duration = 3),
      TimeInterval(from = "23:00", duration = 8),
    ))
  )

  override def extractFeatureRequest(featureRequests: RoomFeatureRequests): Option[RoomFeatureRequest] = featureRequests.hourlyAvailableSlots
}
