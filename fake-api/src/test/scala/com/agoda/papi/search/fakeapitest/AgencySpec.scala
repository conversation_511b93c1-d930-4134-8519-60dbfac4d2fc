package com.agoda.papi.search.fakeapitest

import models.pricing.enums.PaymentModels
import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class AgencySpec extends FakeAPIBaseSpec {
  "Agency feature" should {
    "return payment.model = Agency" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"agency": {}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.childrenRooms.head.payment.get.paymentModel shouldBe PaymentModels.Agency
      }
    }
    "return correct response on /api/booking" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"agency": {}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executeBookingSearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.childrenRooms.head.payment.get.paymentModel shouldBe PaymentModels.Agency
      }
    }
  }
}
