package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class BedConfigurationSpec extends FakeAPIBaseSpec {
  "BedConfiguration feature" should {
    "return complete bedConfiguration structure" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"bedConfiguration": {}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val bedConfig = properties.property.head.masterRooms.head.bedConfiguration2
        bedConfig should not be None
        bedConfig.get.configuration should not be None
        
        val config = bedConfig.get.configuration.get
        config.title shouldBe "Sleeping arrangements"
        config.symbol shouldBe "bed-detail"
        config.bedrooms.size shouldBe 1
        
        val bedroom = config.bedrooms.head
        bedroom.title shouldBe "Common space"
        bedroom.options.size shouldBe 2
        
        val singleBedOption = bedroom.options.head
        val singleBed = singleBedOption.beds.head
        singleBed.typeId shouldBe "Single"
        singleBed.name shouldBe "2 single beds"
        singleBed.quantity shouldBe 2
        singleBed.symbol shouldBe Some("single-bed")
        singleBed.bedTypeName shouldBe Some("Single bed")
        
        val doubleBedOption = bedroom.options(1)
        val doubleBed = doubleBedOption.beds.head
        doubleBed.typeId shouldBe "Double"
        doubleBed.name shouldBe "1 double bed"
        doubleBed.quantity shouldBe 1
        doubleBed.symbol shouldBe Some("double-bed")
        doubleBed.bedTypeName shouldBe Some("Double bed")
      }
    }
  }
}
