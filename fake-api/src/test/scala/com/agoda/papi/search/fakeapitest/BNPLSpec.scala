package com.agoda.papi.search.fakeapitest

import org.joda.time.DateTime
import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class BNPLSpec extends FakeAPIBaseSpec {

  "bnpl feature" should {
    "return default BNPL when not being configured" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {"features": {"bnpl": {}}}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(
            aValidPricingRequest.copy(
              bookingDate = DateTime.parse("2024-01-01"),
              checkIn = DateTime.parse("2024-01-03"),
              lengthOfStay = 1,
            )
          )
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        properties.property.head.masterRooms.head.childrenRooms.head.payment.get.payLater.isEligible shouldBe true
      }
    }
  }

}
