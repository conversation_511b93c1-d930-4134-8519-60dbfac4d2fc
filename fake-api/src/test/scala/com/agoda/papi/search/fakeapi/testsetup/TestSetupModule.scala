package com.agoda.papi.search.fakeapi

import akka.actor.ActorSystem
import com.agoda.commons.http.client.v2.HttpClient
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.papi.search.fakeapi.route.AkkaHttpTestSetupRoute
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.util.circuitbreaker.{CircuitBreakerWrapper, NoOpCircuitBreakerWrapper}
import com.agoda.papi.search.property.service.ClusterService.{ClusterServiceSettings, DFPropertyWithCacheTokenServiceWrapper, DFPropertyWithCacheTokenServiceWrapperImpl, DFRequestFilterConfig}
import com.agoda.commons.http.client.v2.settings.{HttpClientSettings, HttpClientSettingsBuilder}
import com.agoda.commons.logging.metrics.{MetricsReporter, NoOpMetricsReporter}
import com.agoda.commons.observability.api.tracing.FeatureTracer
import com.agoda.commons.observability.sdk.AgOpenTelemetry
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.papi.search.common.service.WhiteLabelService
import com.agoda.papi.search.common.service.cancellationpolicy.BNPLService
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.common.util.measurement.ContextScheduledMetrics
import com.agoda.papi.search.fakeapi.testsetup.`override`.{OverrideAkkaHttpMiddleware, PropertyAndBookingOverrideEngine}
import com.agoda.papi.search.fakeapi.testsetup.dependency.PricingTestSetupClient
import com.agoda.papi.search.fakeapi.testsetup.engine.TestSetupEngine
import com.agoda.papi.search.fakeapi.testsetup.feature.{ASO, Agency, AvailableFilters, AvailableRooms, BNPL, BOR, BedConfiguration, Cashback, ChannelDiscount, CheapestHourlyRoom, FacilityGroups, FreeBreakfast, FreeCancellation, HourlyAvailableSlots, JTBChildRate, M150, Payment, RateCategory, RoomBaseFeatures, RoomBundles, RurubuInventory, SupportChildRate}
import com.agoda.papi.search.fakeapi.testsetup.service.FakePropertyInfoListCacheReader
import com.agoda.papi.search.property.api.metrics.RetryGauge
import com.agoda.papi.search.property.externaldependency.repository.ExtraBedAgeRepository
import com.agoda.papi.search.property.service.{BookingPredictionService, BoutiqueHotelService, ContentEnrichHelper, ContentEnrichService, EnrichBookingInformationService, HotelPaymentOptionService, PAPICMSService, PriceSelectionService, ProductHotelsService, UserLocaleService}
import com.agoda.papi.search.property.service.enrich.{EnrichMultiRoomSuggestionService, EnrichRoomBundleService, EnrichRoomHelper}
import com.agoda.papi.search.property.service.pricestream.PriceStreamService
import com.agoda.papi.search.complexsearch.externaldependency.propertyinfolistcache.PropertyInfoListCacheReader
import com.agoda.supplier.common.services.SupplierMetadataService
import com.typesafe.config.{Config, ConfigFactory}
import com.typesafe.scalalogging.LazyLogging
import framework.testservice.FakeProductHotelsService
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.opentracingshim.OpenTracingShim
import scaldi.Module
import sttp.client3.SttpBackend
import io.opentracing.Tracer

import scala.concurrent.{ExecutionContext, Future}

class TestSetupModule extends Module with LazyLogging {
  val config: Config      = ConfigFactory.load()

  lazy val serviceMeshSettings = ServiceMeshSettingsBuilder(
    "dragonfruit",
    config
  ).build()

  val agOpenTelemetry = AgOpenTelemetry.of(config).registerGlobal()

  bind[AgOpenTelemetry].toNonLazy(agOpenTelemetry)
  bind[FeatureTracer].toNonLazy(FeatureTracer.of(config))

  bind[io.opentracing.Tracer].toNonLazy {
    val otel = inject[AgOpenTelemetry]
    OpenTracingShim.createTracerShim(otel)
  }

  bind[HttpClientSettings] to {
    val config = inject[Config]
    HttpClientSettingsBuilder("dragonfruit", config)
      .build()
  }

  bind[ClusterServiceSettings].identifiedBy('dfSettings) to ClusterServiceSettings("dragonfruitV2")
  bind[ServiceMesh].identifiedBy('mesh) to ServiceMesh(serviceMeshSettings)

  bind[PropertyAndBookingOverrideEngine] to injected[PropertyAndBookingOverrideEngine]
  bind[PricingTestSetupClient] to new PricingTestSetupClient(
    httpClient = HttpClient[Future](inject[HttpClientSettings])(inject[SttpBackend[Future, Any]]),
    mesh = inject[ServiceMesh](identified by 'mesh)
  )
  bind[Agency] to injected[Agency]
  bind[ASO] to injected[ASO]
  bind[BNPL] to injected[BNPL]
  bind[BOR] to injected[BOR]
  bind[ChannelDiscount] to injected[ChannelDiscount]
  bind[FreeBreakfast] to injected[FreeBreakfast]
  bind[FreeCancellation] to injected[FreeCancellation]
  bind[JTBChildRate] to injected[JTBChildRate]
  bind[M150] to injected[M150]
  bind[RurubuInventory] to injected[RurubuInventory]
  bind[Payment] to injected[Payment]
  bind[SupportChildRate] to injected[SupportChildRate]
  bind[BedConfiguration] to injected[BedConfiguration]
  bind[RoomBaseFeatures] to injected[RoomBaseFeatures]
  bind[FacilityGroups] to injected[FacilityGroups]
  bind[AvailableFilters] to injected[AvailableFilters]
  bind[RoomBundles] to injected[RoomBundles]
  bind[HourlyAvailableSlots] to injected[HourlyAvailableSlots]
  bind[Cashback] to injected[Cashback]
  bind[AvailableRooms] to injected[AvailableRooms]
  bind[CheapestHourlyRoom] to injected[CheapestHourlyRoom]
  bind[RateCategory] to injected[RateCategory]

  bind[TestSetupEngine] to injected[TestSetupEngine]
  bind[AkkaHttpTestSetupRoute] to injected[AkkaHttpTestSetupRoute]
  bind[OverrideAkkaHttpMiddleware] to injected[OverrideAkkaHttpMiddleware]

  bind[DFRequestFilterConfig] to DFRequestFilterConfig()
  bind[CircuitBreakerWrapper] to NoOpCircuitBreakerWrapper

  /**
   * TODO: PAPI team needs to refactor this DFPropertyWithCacheTokenServiceWrapper class
   * Fake needs to override DF client to use the real HTTP client.
   * But because of the messy implementation of this DFPropertyWithCacheTokenServiceWrapper,
   * We need to initialize this big Service class instead of just the DF client.
   * Ideally, we should have the following:
   * PROD binding:
   * bind[DFClient] to injected[DFClientImpl]
   * bind[EnrichmentRepository] to injected[EnrichmentRepositoryImpl]
   * bind[DFPropertyWithCacheTokenServiceWrapper] to injected[DFPropertyWithCacheTokenServiceWrapperImpl]
   * Flow test binding:
   * bind[DFClient] to injected[MockDFClient]
   * bind[EnrichmentRepository] to injected[MockEnrichmentRepository]
   * Fake PAPI binding:
   * bind[DFClient] to injected[DFClientImpl]
   * bind[EnrichmentRepository] to injected[MockEnrichmentRepository]
   */
  bind[DFPropertyWithCacheTokenServiceWrapper] to new DFPropertyWithCacheTokenServiceWrapperImpl
    with ContentEnrichHelper with NopScheduledMetrics {
    val languageRepository: LanguageRepository              = inject[LanguageRepository]
    val clusterSettingsConfig                               = ClusterServiceSettings(configName)
    override val serviceMesh: ServiceMesh                   = inject[ServiceMesh](identified by 'mesh)
    override val productHotelsService: ProductHotelsService = inject[ProductHotelsService]
    override val config: ClusterServiceSettings             = clusterSettingsConfig

    lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
      .withOpenTelemetry(inject[OpenTelemetry])
      .build()

    override val httpClient: HttpClient[Future] =
      HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]])
    implicit override val system: ActorSystem               = inject[ActorSystem]
    override val retryGauge: RetryGauge                     = inject[RetryGauge]
    override val requestFilterConfig: DFRequestFilterConfig = inject[DFRequestFilterConfig]
    override val supplierMetadataService                    = inject[SupplierMetadataService]
    override val enrichBookingInformationService: EnrichBookingInformationService =
      inject[EnrichBookingInformationService]
    override val enrichRoomService: EnrichRoomHelper                = inject[EnrichRoomHelper]
    override val userLocaleService: UserLocaleService               = inject[UserLocaleService]
    override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
    override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
    override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
    override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
    override val circuitBreakerWrapper: CircuitBreakerWrapper       = NoOpCircuitBreakerWrapper
    override val bnplService: BNPLService                           = inject[BNPLService]
    override val contentEnrichService: ContentEnrichService         = inject[ContentEnrichService]
    override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
      inject[EnrichMultiRoomSuggestionService]
    override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
    override val priceSelectionService: PriceSelectionService         = inject[PriceSelectionService]
    override val extraBedAgeRepository: ExtraBedAgeRepository         = inject[ExtraBedAgeRepository]
    override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
    implicit override val cmsService: PAPICMSService                  = inject[PAPICMSService]
    implicit override val ec: ExecutionContext                        = ExecutionContexts.defaultContext

    override def tracer: Tracer = NoOpTracer
  }

  // Use the default test FakeProductHotelsService that can be configured by features
  bind[ProductHotelsService] to injected[FakeProductHotelsService]

  // Use the fake PropertyInfoListCacheReader that reads from test setup cache
  bind[PropertyInfoListCacheReader] to new FakePropertyInfoListCacheReader()

}

trait NopScheduledMetrics extends ContextScheduledMetrics {
  override val reportingService: MetricsReporter = NoOpMetricsReporter
}
