package com.agoda.papi.search.fakeapitest

import com.agoda.content.models.request.sf.ContentRateCategoriesRequest
import util.builders.TestDataBuilders.{aValidContext, aValidFeatureFlagRequest, aValidPricingRequest, aValidPropertyRequest}

class RateCategorySpec extends FakeAPIBaseSpec {

  "RateCategory feature" should {
    "return rate categories with default stayTypeId when no configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {"rateCategory": {}},
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest),
          rateCategories = Some(ContentRateCategoriesRequest(images = None, escapeRateCategories = None))
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (testSetupResponse, properties) =>
        val rateCategoryId = testSetupResponse.data.get.hotels.get.head.rooms.get.head.rateCategoryId
        properties.property.size shouldBe 1
        properties.property.head.propertyId shouldBe 101
        properties.property.head.rateCategory shouldBe defined
        val rateCategories = properties.property.head.rateCategory.get
        rateCategories.propertyId shouldBe 101
        rateCategories.rateCategories.size shouldBe 1
        val rateCategory = rateCategories.rateCategories.head
        rateCategory.rateCategoryId shouldBe rateCategoryId
        rateCategory.stayPackageType shouldBe None // Default stayTypeId is None
        rateCategory.localizedRateCategoryName shouldBe Some("Best Flexible Rate")
        rateCategory.inventoryType shouldBe Some("UnknownInventoryType")
        rateCategory.gender shouldBe Some("M")
        rateCategory.detail shouldBe defined
        rateCategory.checkIn shouldBe defined
        rateCategory.checkIn.get.startTime shouldBe Some("14:00")
        rateCategory.checkIn.get.endTime shouldBe Some("22:00")
        rateCategory.checkOut shouldBe defined
        rateCategory.checkOut.get.startTime shouldBe Some("10:00")
        rateCategory.checkOut.get.endTime shouldBe Some("12:00")
        val detail = rateCategory.detail.get
        detail.content shouldBe Some("Your Best Flexible Rate")
        detail.inclusions shouldBe Some("Enjoy our Best Flexible Rate, ideal for families in Bangkok.")
        detail.exclusions shouldBe Some("Just dummy data for exclusion")
        detail.termsAndConditions shouldBe Some("Terms and Conditions")
        detail.supplierPlanCode shouldBe Some("SupplierPlanCode")
        detail.paymentNotice shouldBe Some("Payment Notice")
        detail.notice shouldBe Some("Notice")
        detail.roomDescriptions shouldBe defined
        val roomDescriptions = detail.roomDescriptions.get
        roomDescriptions.size shouldBe 1
        val roomDescription = roomDescriptions.head
        roomDescription.description shouldBe Some("Room Description 1")
        val childPolicies = detail.childPolicies.get
        childPolicies.acceptChild shouldBe Some(true)
        childPolicies.policies.size shouldBe 2
        val policy1 = childPolicies.policies.head
        policy1.description shouldBe Some("Policy 1")
        policy1.categoryId shouldBe 1
        policy1.isRoomCapacityIncluded shouldBe true
        policy1.minAge shouldBe Some(5)
        policy1.maxAge shouldBe Some(12)
        policy1.priceDiscount.code shouldBe Some("USD")
        policy1.priceDiscount.value shouldBe 100
        policy1.priceDiscount.unit shouldBe "USD"
        val cancellationPolicy = detail.cancellationPolicies.get
        cancellationPolicy.feeSettingTypeId shouldBe Some(1)
        cancellationPolicy.chargeRanges shouldBe List("Range1", "Range2")
        cancellationPolicy.chargeRates.size shouldBe 1
        val chargeRate = cancellationPolicy.chargeRates.head
        chargeRate.minGuest shouldBe Some(1)
        chargeRate.maxGuest shouldBe Some(2)
        chargeRate.charges.size shouldBe 2
        chargeRate.charges.head.code shouldBe Some("A")
        chargeRate.charges.head.value shouldBe 100
        chargeRate.charges.head.unit shouldBe "USD"
        cancellationPolicy.amendmentPolicyRules shouldBe defined
        cancellationPolicy.amendmentPolicyRules.get.stayLengthRuleId shouldBe Some(10)
        cancellationPolicy.amendmentPolicyRules.get.guestSizeRuleId shouldBe Some(20)
        val images = detail.images.get
        images.size shouldBe 1
        val image = images.head
        image.id shouldBe 26266267
        image.caption shouldBe ""
        image.locations("original") shouldBe "https://pix8.agoda.net/rateplan-images/16219170/c5ffc59931b61095494ff7172a669260.jpg?ce=0"
      }
    }

    "return rate categories with custom stayTypeId when provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {"rateCategory": {"stayTypeId": 2}},
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest),
          rateCategories = Some(ContentRateCategoriesRequest(images = None, escapeRateCategories = None))
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (testSetupResponse, properties) =>
        val rateCategoryId = testSetupResponse.data.get.hotels.get.head.rooms.get.head.rateCategoryId
        properties.property.size shouldBe 1
        properties.property.head.propertyId shouldBe 101
        properties.property.head.rateCategory shouldBe defined
        val rateCategories = properties.property.head.rateCategory.get
        rateCategories.propertyId shouldBe 101
        rateCategories.rateCategories.size shouldBe 1
        val rateCategory = rateCategories.rateCategories.head
        rateCategory.rateCategoryId shouldBe rateCategoryId
        rateCategory.stayPackageType shouldBe Some(2)
        rateCategory.localizedRateCategoryName shouldBe Some("Best Flexible Rate")
        rateCategory.inventoryType shouldBe Some("UnknownInventoryType")
        rateCategory.gender shouldBe Some("M")
        rateCategory.detail shouldBe defined
        rateCategory.checkIn shouldBe defined
        rateCategory.checkIn.get.startTime shouldBe Some("14:00")
        rateCategory.checkIn.get.endTime shouldBe Some("22:00")
        rateCategory.checkOut shouldBe defined
        rateCategory.checkOut.get.startTime shouldBe Some("10:00")
        rateCategory.checkOut.get.endTime shouldBe Some("12:00")
        val detail = rateCategory.detail.get
        detail.content shouldBe Some("Your Best Flexible Rate")
        detail.inclusions shouldBe Some("Enjoy our Best Flexible Rate, ideal for families in Bangkok.")
        detail.exclusions shouldBe Some("Just dummy data for exclusion")
        detail.termsAndConditions shouldBe Some("Terms and Conditions")
        detail.supplierPlanCode shouldBe Some("SupplierPlanCode")
        detail.paymentNotice shouldBe Some("Payment Notice")
        detail.notice shouldBe Some("Notice")
        detail.roomDescriptions shouldBe defined
        val roomDescriptions = detail.roomDescriptions.get
        roomDescriptions.size shouldBe 1
        val roomDescription = roomDescriptions.head
        roomDescription.description shouldBe Some("Room Description 1")
        val childPolicies = detail.childPolicies.get
        childPolicies.acceptChild shouldBe Some(true)
        childPolicies.policies.size shouldBe 2
        val policy1 = childPolicies.policies.head
        policy1.description shouldBe Some("Policy 1")
        policy1.categoryId shouldBe 1
        policy1.isRoomCapacityIncluded shouldBe true
        policy1.minAge shouldBe Some(5)
        policy1.maxAge shouldBe Some(12)
        policy1.priceDiscount.code shouldBe Some("USD")
        policy1.priceDiscount.value shouldBe 100
        policy1.priceDiscount.unit shouldBe "USD"
        val cancellationPolicy = detail.cancellationPolicies.get
        cancellationPolicy.feeSettingTypeId shouldBe Some(1)
        cancellationPolicy.chargeRanges shouldBe List("Range1", "Range2")
        cancellationPolicy.chargeRates.size shouldBe 1
        val chargeRate = cancellationPolicy.chargeRates.head
        chargeRate.minGuest shouldBe Some(1)
        chargeRate.maxGuest shouldBe Some(2)
        chargeRate.charges.size shouldBe 2
        chargeRate.charges.head.code shouldBe Some("A")
        chargeRate.charges.head.value shouldBe 100
        chargeRate.charges.head.unit shouldBe "USD"
        cancellationPolicy.amendmentPolicyRules shouldBe defined
        cancellationPolicy.amendmentPolicyRules.get.stayLengthRuleId shouldBe Some(10)
        cancellationPolicy.amendmentPolicyRules.get.guestSizeRuleId shouldBe Some(20)
        val images = detail.images.get
        images.size shouldBe 1
        val image = images.head
        image.id shouldBe 26266267
        image.caption shouldBe ""
        image.locations("original") shouldBe "https://pix8.agoda.net/rateplan-images/16219170/c5ffc59931b61095494ff7172a669260.jpg?ce=0"
      }
    }
  }
} 