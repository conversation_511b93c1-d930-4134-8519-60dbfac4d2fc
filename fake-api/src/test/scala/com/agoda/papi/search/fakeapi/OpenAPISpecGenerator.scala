package com.agoda.papi.search.fakeapi

import com.agoda.papi.search.fakeapi.route.schema.TestSetupEndpoint
import sttp.apispec.openapi.OpenAPI
import sttp.tapir.docs.openapi.OpenAPIDocsInterpreter
import sttp.apispec.openapi.circe.yaml.RichOpenAPI

import java.io.{File, FileWriter}

object OpenAPISpecGenerator extends App {
  val docs: OpenAPI = OpenAPIDocsInterpreter()
    .toOpenAPI(
      List(
        TestSetupEndpoint.testSetupEndpoint
      ),
      "Fake PAPI OpenAPI",
      "1.0"
    )
    .openapi("3.0.3")

  val fileWriter = new FileWriter(new File("fake_papi_openapi_spec.yml"))
  fileWriter.write(docs.toYaml3_0_3)
  fileWriter.close()
}