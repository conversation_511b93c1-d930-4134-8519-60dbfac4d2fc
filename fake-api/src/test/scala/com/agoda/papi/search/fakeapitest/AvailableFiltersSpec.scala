package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyFilterTags, aValidPropertyRequest}

class AvailableFiltersSpec extends FakeAPIBaseSpec {
  "AvailableFilters feature" should {
    "return default values when no feature configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {
          |        "availableFilters": {}
          |      },
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val availableFilters = properties.property.head.availableFilters
        availableFilters should not be None

        val filters = availableFilters.get
        filters.title shouldBe ""  // Default empty string for title
        filters.tags shouldBe aValidPropertyFilterTags  // Default to aValidPropertyFilterTags when not configured
        filters.filterGroups shouldBe None  // None for filterGroups
      }
    }

    "return custom property filters when configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "features": {
          |        "availableFilters": {
          |          "title": "Property Filters",
          |          "tags": [
          |            {
          |              "id": "breakfast-included",
          |              "title": "Breakfast included",
          |              "symbol": "breakfast-icon",
          |              "groupId": "meals"
          |            },
          |            {
          |              "id": "free-cancellation",
          |              "title": "Free cancellation",
          |              "symbol": "cancel-icon",
          |              "groupId": "cancellation"
          |            }
          |          ],
          |          "filterGroups": [
          |            {
          |              "id": "meals",
          |              "groupName": "Meals"
          |            },
          |            {
          |              "id": "cancellation",
          |              "groupName": "Cancellation Policies"
          |            }
          |          ]
          |        }
          |      },
          |      "rooms": [
          |        {}
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val availableFilters = properties.property.head.availableFilters
        availableFilters should not be None

        val filters = availableFilters.get
        filters.title shouldBe "Property Filters"

        // Verify tags
        filters.tags.size shouldBe 2

        val breakfastTag = filters.tags.head
        breakfastTag.id shouldBe "breakfast-included"
        breakfastTag.title shouldBe "Breakfast included"
        breakfastTag.symbol shouldBe "breakfast-icon"
        breakfastTag.groupId shouldBe Some("meals")

        val cancellationTag = filters.tags(1)
        cancellationTag.id shouldBe "free-cancellation"
        cancellationTag.title shouldBe "Free cancellation"
        cancellationTag.symbol shouldBe "cancel-icon"
        cancellationTag.groupId shouldBe Some("cancellation")

        // Verify filter groups
        val groups = filters.filterGroups.get
        groups.size shouldBe 2

        val mealsGroup = groups.head
        mealsGroup.id shouldBe "meals"
        mealsGroup.groupName shouldBe "Meals"

        val cancellationGroup = groups(1)
        cancellationGroup.id shouldBe "cancellation"
        cancellationGroup.groupName shouldBe "Cancellation Policies"
      }
    }
  }
} 