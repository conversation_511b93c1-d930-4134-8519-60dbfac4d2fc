package com.agoda.papi.search.fakeapi.testsetup.feature

import com.agoda.content.models.db.features.{Feature, FeatureGroup}
import com.agoda.papi.search.fakeapi.testsetup.model.{FacilityGroupsFeatureRequest, RoomFeatureRequest, RoomFeatureRequests}
import transformers.{EnrichedChildRoom, EnrichedMasterRoom}

class FacilityGroups extends RoomOverride {

  override def overrideMasterRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, masterRoom: EnrichedMasterRoom): EnrichedMasterRoom = {
    val request = featureRequest.asInstanceOf[FacilityGroupsFeatureRequest]
    val isNonSmokingRoom = request.isNonSmokingRoom.getOrElse(false)
    val baseFacilityGroups = Vector(
      FeatureGroup(
        id = Some(24),
        name = Some("Bathroom and toiletries"),
        order = Some(1),
        symbol = Some("toiletries"),
        features = Some(Vector(
          Feature(
            id = Some(26),
            featureName = Some("Bathrobes"),
            symbol = Some("bathrobes"),
            available = Some(true),
            tags = None,
            order = Some(160),
            featureTags = None,
            images = None,
            personalizedRank = None,
            featureNameLocalizations = None,
            featureNameLocalizationList = None,
            emphasis = None,
            highlighted = None
          ),
          Feature(
            id = Some(26),
            featureName = Some("Toiletries"),
            symbol = Some("toiletries"),
            available = Some(true),
            tags = None,
            order = Some(142),
            featureTags = None,
            images = None,
            personalizedRank = None,
            featureNameLocalizations = None,
            featureNameLocalizationList = None,
            emphasis = None,
            highlighted = None
          )
        ))
      ),
      FeatureGroup(
        id = Some(26),
        name = Some("Entertainment"),
        order = Some(1),
        symbol = Some("free-wifi-in-all-rooms"),
        features = Some(Vector(
          Feature(
            id = Some(109),
            featureName = Some("Free Wi-Fi in all rooms!"),
            symbol = Some("free-wifi-in-all-rooms"),
            available = Some(true),
            tags = None,
            order = Some(38),
            featureTags = None,
            images = None,
            personalizedRank = None,
            featureNameLocalizations = None,
            featureNameLocalizationList = None,
            emphasis = None,
            highlighted = None
          ),
          Feature(
            id = Some(109),
            featureName = Some("Satellite/cable channels"),
            symbol = Some("satellite-television"),
            available = Some(true),
            tags = None,
            order = Some(134),
            featureTags = None,
            images = None,
            personalizedRank = None,
            featureNameLocalizations = None,
            featureNameLocalizationList = None,
            emphasis = None,
            highlighted = None
          )
        ))
      )
    )
    val facilityGroups = if (isNonSmokingRoom) {
      baseFacilityGroups :+ FeatureGroup(
        id = Some(15),
        name = Some("Non-smoking"),
        order = Some(1),
        symbol = Some("non-smoking-room"),
        features = Some(Vector(
          Feature(
            id = Some(15),
            featureName = Some("Non-smoking"),
            symbol = Some("non-smoking-room"),
            available = Some(true),
            tags = None,
            order = Some(113),
            featureTags = None,
            images = None,
            personalizedRank = None,
            featureNameLocalizations = None,
            featureNameLocalizationList = None,
            emphasis = None,
            highlighted = None
          )
        ))
      )
    } else {
      baseFacilityGroups
    }
    masterRoom.copy(
      facilityGroups = facilityGroups
    )
  }

  override def overrideChildRoom(coreData: RoomCoreData, additionalData: RoomAdditionalData, featureRequest: RoomFeatureRequest, childRoom: EnrichedChildRoom): EnrichedChildRoom = childRoom

  override def extractFeatureRequest(featureRequests: RoomFeatureRequests): Option[RoomFeatureRequest] = featureRequests.facilityGroups
}
