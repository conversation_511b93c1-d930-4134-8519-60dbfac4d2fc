package com.agoda.papi.search.fakeapi.testsetup.model

import com.agoda.papi.search.fakeapi.testsetup.model.Status.Status
import io.circe.Decoder.Result
import transformers.{FilterGroup, FilterTag}

case class TestSetupRequest(
    hotels: Option[Seq[HotelTestSetupRequest]],
    contracts: Option[Seq[String]]
)

case class HotelTestSetupRequest(
    hotelId: Long,
    features: Option[HotelFeatureRequests],
    rooms: Option[Seq[RoomTestSetupRequest]]
)

case class RoomTestSetupRequest(
    roomTypeIdGroup: Option[Int],
    features: Option[RoomFeatureRequests]
)

case class TestSetupResponse(data: Option[TestSetupResponseData], error: Option[String])

case class TestSetupResponseData(hotels: Option[Seq[HotelTestSetupResponse]])

case class HotelTestSetupResponse(hotelId: Long, status: Status, rooms: Option[Seq[RoomTestSetupResponse]])

case class RoomTestSetupResponse(
    supplierId: Option[Int],
    roomIdentifier: String,
    rateCategoryId: Long,
    roomTypeId: Long
)

case class HotelFeatureRequests(
    bor: Option[BORFeatureRequest],
    supportChildRate: Option[SupportChildRateFeatureRequest],
    availableFilters: Option[AvailableFiltersFeatureRequest],
    roomBundles: Option[RoomBundlesFeatureRequest],
    cheapestHourlyRoom: Option[CheapestHourlyRoomFeatureRequest],
    rateCategory: Option[RateCategoryFeatureRequest]
)

object HotelFeatureRequests {

  val empty: HotelFeatureRequests = HotelFeatureRequests(
    bor = None,
    supportChildRate = None,
    availableFilters = None,
    roomBundles = None,
    cheapestHourlyRoom = None,
    rateCategory = None
  )

}

sealed trait HotelFeatureRequest

case class BORFeatureRequest(doNotUse: Option[Boolean] = None) extends HotelFeatureRequest

case class SupportChildRateFeatureRequest(isSupportChildRate: Option[Boolean] = None) extends HotelFeatureRequest

case class AvailableFiltersFeatureRequest(title: Option[String] = None, 
                                          tags: Option[Seq[FilterTag]] = None, 
                                          filterGroups: Option[Seq[FilterGroup]] = None) extends HotelFeatureRequest

case class RoomBundlesFeatureRequest(bundleType: Option[Int] = None,
                                     bundleTypeId: Option[String] = None,
                                     isSingleBundleSegment: Option[Boolean] = None,
                                     hasCashback:  Option[Boolean] = None) extends HotelFeatureRequest

case class CheapestHourlyRoomFeatureRequest(isHourlyRequest: Option[Boolean] = None) extends HotelFeatureRequest

case class RateCategoryFeatureRequest(stayTypeId: Option[Int] = None) extends HotelFeatureRequest

case class RoomFeatureRequests(
    agency: Option[AgencyFeatureRequest],
    aso: Option[ASOFeatureRequest],
    bnpl: Option[BNPLFeatureRequest],
    channelDiscount: Option[ChannelDiscountFeatureRequest],
    freeBreakfast: Option[FreeBreakfastFeatureRequest],
    freeCancellation: Option[FreeCancellationFeatureRequest],
    jtbChildRate: Option[JTBChildRateFeatureRequest],
    m150: Option[M150FeatureRequest],
    rurubuInventory: Option[RurubuInventoryFeatureRequest],
    payment: Option[PaymentFeatureRequest],
    bedConfiguration: Option[BedConfigurationFeatureRequest],
    roomBaseFeatures: Option[RoomBaseFeaturesFeatureRequest],
    facilityGroups: Option[FacilityGroupsFeatureRequest],
    hourlyAvailableSlots: Option[HourlyAvailableSlotsFeatureRequest],
    cashback: Option[CashbackFeatureRequest],
    availableRooms: Option[AvailableRoomsFeatureRequest]
)

object RoomFeatureRequests {

  val empty: RoomFeatureRequests = RoomFeatureRequests(
    agency = None,
    aso = None,
    bnpl = None,
    channelDiscount = None,
    freeBreakfast = None,
    freeCancellation = None,
    jtbChildRate = None,
    m150 = None,
    rurubuInventory = None,
    payment = None,
    bedConfiguration = None,
    roomBaseFeatures = None,
    facilityGroups = None,
    hourlyAvailableSlots = None,
    cashback = None,
    availableRooms = None
  )

}

sealed trait RoomFeatureRequest

case class AgencyFeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class ASOFeatureRequest(stayPackageType: Option[Int], localizedName: Option[String]) extends RoomFeatureRequest

case class BNPLFeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class ChannelDiscountFeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class FreeBreakfastFeatureRequest(benefitId: Option[Int]) extends RoomFeatureRequest

case class FreeCancellationFeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class JTBChildRateFeatureRequest(jtbSupplierId: Option[Int]) extends RoomFeatureRequest

case class M150FeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class RurubuInventoryFeatureRequest(inventoryType: Option[Int], jtbSupplierId: Option[Int])
    extends RoomFeatureRequest

case class PaymentFeatureRequest(isNoCreditCard: Option[Boolean] = None,
                                 isPayAtHotel: Option[Boolean] = None) extends RoomFeatureRequest

case class BedConfigurationFeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class RoomBaseFeaturesFeatureRequest(isNonSmokingRoom: Option[Boolean] = None) extends RoomFeatureRequest

case class FacilityGroupsFeatureRequest(isNonSmokingRoom: Option[Boolean] = None) extends RoomFeatureRequest

case class HourlyAvailableSlotsFeatureRequest(doNotUse: Option[Boolean] = None) extends RoomFeatureRequest

case class CashbackFeatureRequest(cashbackVersion: Option[String] = None,
                                  showPostCashbackPrice: Option[Boolean] = None) extends RoomFeatureRequest

case class AvailableRoomsFeatureRequest(count: Option[Int] = None) extends RoomFeatureRequest

object Status extends Enumeration {
  type Status = Value
  val Success, Failure, Duplicated = Value
}

object TestSetupSerializer {
  import io.circe._, io.circe.generic.semiauto._
  implicit val AgencyFeatureRequestEncoder: Encoder[AgencyFeatureRequest] =
    deriveEncoder[AgencyFeatureRequest]
  implicit val AgencyFeatureRequestDecoder: Decoder[AgencyFeatureRequest] =
    deriveDecoder[AgencyFeatureRequest]
  implicit val ASOFeatureRequestEncoder: Encoder[ASOFeatureRequest] =
    deriveEncoder[ASOFeatureRequest]
  implicit val ASOFeatureRequestDecoder: Decoder[ASOFeatureRequest] =
    deriveDecoder[ASOFeatureRequest]
  implicit val BNPLFeatureRequestEncoder: Encoder[BNPLFeatureRequest] =
    deriveEncoder[BNPLFeatureRequest]
  implicit val BNPLFeatureRequestDecoder: Decoder[BNPLFeatureRequest] =
    deriveDecoder[BNPLFeatureRequest]
  implicit val ChannelDiscountFeatureRequestEncoder: Encoder[ChannelDiscountFeatureRequest] =
    deriveEncoder[ChannelDiscountFeatureRequest]
  implicit val ChannelDiscountFeatureRequestDecoder: Decoder[ChannelDiscountFeatureRequest] =
    deriveDecoder[ChannelDiscountFeatureRequest]
  implicit val FreeBreakfastFeatureRequestEncoder: Encoder[FreeBreakfastFeatureRequest] =
    deriveEncoder[FreeBreakfastFeatureRequest]
  implicit val FreeBreakfastFeatureRequestDecoder: Decoder[FreeBreakfastFeatureRequest] =
    deriveDecoder[FreeBreakfastFeatureRequest]
  implicit val FreeCancellationFeatureRequestEncoder: Encoder[FreeCancellationFeatureRequest] =
    deriveEncoder[FreeCancellationFeatureRequest]
  implicit val FreeCancellationFeatureRequestDecoder: Decoder[FreeCancellationFeatureRequest] =
    deriveDecoder[FreeCancellationFeatureRequest]
  implicit val JTBChildRateFeatureRequestEncoder: Encoder[JTBChildRateFeatureRequest] =
    deriveEncoder[JTBChildRateFeatureRequest]
  implicit val JTBChildRateFeatureRequestDecoder: Decoder[JTBChildRateFeatureRequest] =
    deriveDecoder[JTBChildRateFeatureRequest]
  implicit val M150FeatureRequestEncoder: Encoder[M150FeatureRequest] =
    deriveEncoder[M150FeatureRequest]
  implicit val M150FeatureRequestDecoder: Decoder[M150FeatureRequest] =
    deriveDecoder[M150FeatureRequest]
  implicit val RurubuInventoryFeatureRequestEncoder: Encoder[RurubuInventoryFeatureRequest] =
    deriveEncoder[RurubuInventoryFeatureRequest]
  implicit val RurubuInventoryFeatureRequestDecoder: Decoder[RurubuInventoryFeatureRequest] =
    deriveDecoder[RurubuInventoryFeatureRequest]
  implicit val PaymentFeatureRequestEncoder: Encoder[PaymentFeatureRequest] =
    deriveEncoder[PaymentFeatureRequest]
  implicit val PaymentFeatureRequestDecoder: Decoder[PaymentFeatureRequest] =
    deriveDecoder[PaymentFeatureRequest]
  implicit val BedConfigurationFeatureRequestEncoder: Encoder[BedConfigurationFeatureRequest] =
    deriveEncoder[BedConfigurationFeatureRequest]
  implicit val BedConfigurationFeatureRequestDecoder: Decoder[BedConfigurationFeatureRequest] =
    deriveDecoder[BedConfigurationFeatureRequest]
  implicit val RoomBaseFeaturesFeatureRequestEncoder: Encoder[RoomBaseFeaturesFeatureRequest] =
    deriveEncoder[RoomBaseFeaturesFeatureRequest]
  implicit val RoomBaseFeaturesFeatureRequestDecoder: Decoder[RoomBaseFeaturesFeatureRequest] =
    deriveDecoder[RoomBaseFeaturesFeatureRequest]
  implicit val FacilityGroupsFeatureRequestEncoder: Encoder[FacilityGroupsFeatureRequest] =
    deriveEncoder[FacilityGroupsFeatureRequest]
  implicit val FacilityGroupsFeatureRequestDecoder: Decoder[FacilityGroupsFeatureRequest] =
    deriveDecoder[FacilityGroupsFeatureRequest]
  implicit val HourlyAvailableSlotsFeatureRequestEncoder: Encoder[HourlyAvailableSlotsFeatureRequest] =
    deriveEncoder[HourlyAvailableSlotsFeatureRequest]
  implicit val HourlyAvailableSlotsFeatureRequestDecoder: Decoder[HourlyAvailableSlotsFeatureRequest] =
    deriveDecoder[HourlyAvailableSlotsFeatureRequest]
  implicit val CashbackFeatureRequestEncoder: Encoder[CashbackFeatureRequest] =
    deriveEncoder[CashbackFeatureRequest]
  implicit val CashbackFeatureRequestDecoder: Decoder[CashbackFeatureRequest] =
    deriveDecoder[CashbackFeatureRequest]
  implicit val AvailableRoomsFeatureRequestEncoder: Encoder[AvailableRoomsFeatureRequest] =
    deriveEncoder[AvailableRoomsFeatureRequest]
  implicit val AvailableRoomsFeatureRequestDecoder: Decoder[AvailableRoomsFeatureRequest] =
    deriveDecoder[AvailableRoomsFeatureRequest]
  implicit val RoomFeatureRequestsEncoder: Encoder[RoomFeatureRequests]         = deriveEncoder[RoomFeatureRequests]
  implicit val RoomFeatureRequestsDecoder: Decoder[RoomFeatureRequests]         = deriveDecoder[RoomFeatureRequests]
  implicit val RoomTestSetupRequestEncoder: Encoder[RoomTestSetupRequest]       = deriveEncoder[RoomTestSetupRequest]
  implicit val RoomTestSetupRequestDecoder: Decoder[RoomTestSetupRequest]       = deriveDecoder[RoomTestSetupRequest]

  implicit val BORFeatureRequestEncoder: Encoder[BORFeatureRequest] =
    deriveEncoder[BORFeatureRequest]
  implicit val BORFeatureRequestDecoder: Decoder[BORFeatureRequest] =
    deriveDecoder[BORFeatureRequest]
  implicit val SupportChildRateFeatureRequestEncoder: Encoder[SupportChildRateFeatureRequest] =
    deriveEncoder[SupportChildRateFeatureRequest]
  implicit val SupportChildRateFeatureRequestDecoder: Decoder[SupportChildRateFeatureRequest] =
    deriveDecoder[SupportChildRateFeatureRequest]
  implicit val AvailableFiltersFeatureRequestEncoder: Encoder[AvailableFiltersFeatureRequest] =
    deriveEncoder[AvailableFiltersFeatureRequest]
  implicit val AvailableFiltersFeatureRequestDecoder: Decoder[AvailableFiltersFeatureRequest] =
    deriveDecoder[AvailableFiltersFeatureRequest]
  implicit val FilterTagEncoder: Encoder[FilterTag] = deriveEncoder[FilterTag]
  implicit val FilterTagDecoder: Decoder[FilterTag] = deriveDecoder[FilterTag]
  implicit val FilterGroupEncoder: Encoder[FilterGroup] = deriveEncoder[FilterGroup]
  implicit val FilterGroupDecoder: Decoder[FilterGroup] = deriveDecoder[FilterGroup]
  implicit val RoomBundlesFeatureRequestEncoder: Encoder[RoomBundlesFeatureRequest] =
    deriveEncoder[RoomBundlesFeatureRequest]
  implicit val RoomBundlesFeatureRequestDecoder: Decoder[RoomBundlesFeatureRequest] = 
    deriveDecoder[RoomBundlesFeatureRequest]
  implicit val CheapestHourlyRoomFeatureRequestEncoder: Encoder[CheapestHourlyRoomFeatureRequest] =
    deriveEncoder[CheapestHourlyRoomFeatureRequest]
  implicit val CheapestHourlyRoomFeatureRequestDecoder: Decoder[CheapestHourlyRoomFeatureRequest] =
    deriveDecoder[CheapestHourlyRoomFeatureRequest]
  implicit val RateCategoryFeatureRequestEncoder: Encoder[RateCategoryFeatureRequest] =
    deriveEncoder[RateCategoryFeatureRequest]
  implicit val RateCategoryFeatureRequestDecoder: Decoder[RateCategoryFeatureRequest] =
    deriveDecoder[RateCategoryFeatureRequest]
  implicit val HotelFeatureRequestsEncoder: Encoder[HotelFeatureRequests]   = deriveEncoder[HotelFeatureRequests]
  implicit val HotelFeatureRequestsDecoder: Decoder[HotelFeatureRequests]   = deriveDecoder[HotelFeatureRequests]
  implicit val HotelTestSetupRequestEncoder: Encoder[HotelTestSetupRequest] = deriveEncoder[HotelTestSetupRequest]
  implicit val HotelTestSetupRequestDecoder: Decoder[HotelTestSetupRequest] = deriveDecoder[HotelTestSetupRequest]

  implicit val TestSetupRequestEncoder: Encoder[TestSetupRequest] = deriveEncoder[TestSetupRequest]
  implicit val TestSetupRequestDecoder: Decoder[TestSetupRequest] = deriveDecoder[TestSetupRequest]

  implicit val statusEncoder: Encoder[Status.Value] = Encoder.encodeEnumeration(Status)
  implicit val statusDecoder: Decoder[Status.Value] = Decoder.decodeEnumeration(Status)

  implicit val RoomTestSetupResponseEncoder: Encoder[RoomTestSetupResponse] = deriveEncoder[RoomTestSetupResponse]
  implicit val RoomTestSetupResponseDecoder: Decoder[RoomTestSetupResponse] = deriveDecoder[RoomTestSetupResponse]

  implicit val HotelTestSetupResponseEncoder: Encoder[HotelTestSetupResponse] = deriveEncoder[HotelTestSetupResponse]
  implicit val HotelTestSetupResponseDecoder: Decoder[HotelTestSetupResponse] = deriveDecoder[HotelTestSetupResponse]

  implicit val TestSetupResponseDataEncoder: Encoder[TestSetupResponseData] = deriveEncoder[TestSetupResponseData]
  implicit val TestSetupResponseDataDecoder: Decoder[TestSetupResponseData] = deriveDecoder[TestSetupResponseData]

  implicit val TestSetupResponseEncoder: Encoder[TestSetupResponse] = deriveEncoder[TestSetupResponse]
  implicit val TestSetupResponseDecoder: Decoder[TestSetupResponse] = deriveDecoder[TestSetupResponse]
}