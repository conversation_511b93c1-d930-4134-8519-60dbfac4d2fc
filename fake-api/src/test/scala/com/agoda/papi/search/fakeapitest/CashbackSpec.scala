package com.agoda.papi.search.fakeapitest

import util.builders.TestDataBuilders.{aValidContext, aValidPricingRequest, aValidPropertyRequest}

class CashbackSpec extends FakeAPIBaseSpec {
  "Cashback feature" should {
    "return default values when no feature configuration is provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {
          |          "features": {
          |            "cashback": {}
          |          }
          |        }
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        
        // Verify cashback defaults
        childRoom.cashback should not be None
        val cashback = childRoom.cashback.get
        cashback.cashbackVersion shouldBe Some("Aggressive")  // Default version
        cashback.showPostCashbackPrice shouldBe true  // Default showPostCashbackPrice
        
        // Verify pricing
        childRoom.pricing should contain key "THB"
        childRoom.pricing("THB") should not be None
      }
    }

    "return custom cashback configuration when provided" in {
      val testSetupRequest =
        """
          |{
          |  "hotels": [
          |    {
          |      "hotelId": 101,
          |      "rooms": [
          |        {
          |          "features": {
          |            "cashback": {
          |              "cashbackVersion": "Conservative",
          |              "showPostCashbackPrice": false
          |            }
          |          }
          |        }
          |      ]
          |    }
          |  ]
          |}
          |""".stripMargin
      val request = aValidPropertyRequest
        .copy(
          context = aValidContext.copy(propertyIds = List(101L)),
          pricing = Some(aValidPricingRequest)
        )

      val resultF = executePropertySearchWithTestSetup(
        testSetupRequest,
        request
      )

      whenReady(resultF) { case (_, properties) =>
        val childRoom = properties.property.head.masterRooms.head.childrenRooms.head
        
        // Verify custom cashback values
        childRoom.cashback should not be None
        val cashback = childRoom.cashback.get
        cashback.cashbackVersion shouldBe Some("Conservative")
        cashback.showPostCashbackPrice shouldBe false
        
        // Verify pricing is still present
        childRoom.pricing should contain key "THB"
        childRoom.pricing("THB") should not be None
      }
    }
  }
} 