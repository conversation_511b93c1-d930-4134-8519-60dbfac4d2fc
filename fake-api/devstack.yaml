# yaml-language-server: $schema=https://devstack.agodadev.io/api/v1/schema/devstack-v1.json
schema: v1
service: propertyapisearchdocker
environments:
  fake-api:
    description: Fake PAPI
    dependencies:
      - registry:
          service: propertyapipricingdocker
          environment: fake-api
          version: latest
    spec:
      # For consumers who use contract testing with fake PAPI
      # :8080 without contract-generator sidecar
      # :8081 with contract-generator sidecar
      sidecars:
        - name: contract-generator
          artifact:
            image:
              repository: e2e-test/contract-generator/local
              tag: latest
          healthCheck:
            readiness:
              httpGet:
                path: /contract-generator/health-check
                port: 8081
              initialDelaySeconds: 5
              periodSeconds: 5
              failureThreshold: 3
            startup:
              httpGet:
                path: /contract-generator/health-check
                port: 8081
              initialDelaySeconds: 30
              periodSeconds: 5
              failureThreshold: 40
          envs:
            DOWNSTREAM_URL: http://localhost:8080
          resources:
            limits:
              cpu: "1"
              memory: 512Mi
            requests:
              cpu: "1"
              memory: 512Mi
          mountPaths:
            - path: "/artifacts"
      ports:
        - number: 8080
          protocol: http
        - number: 8081
          protocol: http
        - number: 5005
          protocol: tcp
        - number: 3333
          protocol: tcp
      artifact:
        image:
          repository: ${FAKEAPI_IMAGE_REPO:-e2e-test/fake-api/propertyapisearchdocker/develop}
          tag: ${FAKEAPI_IMAGE_TAG:-latest}
      healthCheck:
        readiness:
          httpGet:
            path: /ag-http-server/status
            port: 8080
          initialDelaySeconds: 5
          periodSeconds: 5
          failureThreshold: 3
        startup:
          httpGet:
            path: /ag-http-server/status
            port: 8080
          initialDelaySeconds: 30
          periodSeconds: 5
          failureThreshold: 40
      envs:
        DEPLOYMENT: devstack
        TZ: Asia/Bangkok
        PROPERTYAPI_HOST_PORT: "localhost:8080"
      resources:
        limits:
          cpu: "5"
          memory: 8Gi
        requests:
          cpu: "2"
          memory: 8Gi
      consul:
        enabled: false
      vault:
        enabled: false
      ingresses:
        - enabled: true
          port: 8081
        - enabled: true
          port: 8080