FROM reg-hk.agodadev.io/infra/infra-ubuntu-graalvm-ce-java17
ENV PROJECT_HOME /opt
ENV JAVA_OPTS "--add-opens=java.base/java.lang=ALL-UNNAMED --add-opens=java.base/java.math=ALL-UNNAMED --add-opens=java.base/java.util=ALL-UNNAMED --add-opens=java.base/java.util.concurrent=ALL-UNNAMED --add-opens=java.base/java.net=ALL-UNNAMED --add-opens=java.base/java.text=ALL-UNNAMED"

RUN mkdir -p $PROJECT_HOME/fake-api \
    && mkdir -p $PROJECT_HOME/fake-api/sflogs
WORKDIR $PROJECT_HOME/fake-api

COPY fake-api/target/universal/stage/ ./

RUN chmod +x bin/fake-api

ENTRYPOINT []
CMD ["./bin/fake-api", "$JAVA_OPTS", "-Dconfig.resource=fake-api.conf", "-Dlogback.configurationFile=logback-fake-api.xml"]