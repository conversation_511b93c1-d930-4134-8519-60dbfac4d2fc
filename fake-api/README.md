# PAPI Fake-API

## How to use Fake PAPI in your test

At the beginning of each test case, make a call to `/test/setup/v2` to set up Fake PAPI with features.

You can use the following OpenAPI specification to generate the Fake PAPI client:

https://dev-portal.agodadev.io/components/propertyapisearchdocker/api-docs?spec=fake-api

To see what you can expect from each fake features, check the following test suite:

https://gitlab.agodadev.io/IT-Platform/propertyapi/-/tree/develop/fake-api/src/test/scala/com/agoda/papi/search/fakeapitest?ref_type=heads

## How to run or debug Fake PAPI

1. Deploy Fake API devstack with `devstack --config ./fake-api/devstack.yaml deploy`
2. Enable **mirrord**
3. Go to `fake-api/test/scala/com/agoda/papi/search/fakeapi/Boot.scala`
4. Click IntelliJ **Run/Debug** button and select **Modify Run Configuration...**
5. In **Build and run** section, select **Modify options** and check **Add VM options**
6. Put the following into **VM options**
  ```
  -Dlogback.configurationFile=logback-fake-api.xml -Dconfig.resource=fake-api.conf
  ```
7. Proceed with either **Run or Debug!**

## How to run Fake PAPI test

1. Deploy Fake API devstack with `devstack --config ./fake-api/devstack.yaml deploy`
    - NOTE: If you want to run with local Fake PAPI, follow the step in [this section](#How-to-run-or-debug-Fake-PAPI)
2. Enable **mirrord** OR Update `fake-api-test.conf` with PAPI Ingress URL from devstack
    - NOTE: `mirrord` approach doesn't work sometimes, so updating `fake-api-test.conf` is preferred
3. Run any test suite in `fake-api/test/scala/com/agoda/papi/search/fakeapitest` package!

## How to run Fake PAPI contract test
1. Deploy Fake API devstack with `devstack --config ./fake-api/devstack.yaml deploy`
2. Enable **mirrord** OR Update `fake-api-test.conf` with PAPI Ingress URL from devstack
   - NOTE: `mirrord` approach doesn't work sometimes, so updating `fake-api-test.conf` is preferred
4. Replace DEVSTACK_URL with your devstack url and Run!
   ```
   BROKER_URL=http://pactbroker.agoda.local \
   DEVSTACK_URL=8080-propertyapisearchdocker-stack-1056550.devstack.qa.agoda.is \
   CONTRACT_TAG=production \
   VERIFICATION_TIMEOUT=240 \
   sbt "pactTest/pact:test"
   ```

## How to add new feature to Fake PAPI

1. Create a new `FeatureRequest` that will be used by the consumer to set up your feature
   - Create a new `FeatureRequest` in `com.agoda.papi.search.fakeapi.testsetup.model`
   - If it's a room-level feature, uses `RoomFeatureRequest`. If it's a hotel-level feature, uses `HotelFeatureRequest`
   - Update `RoomFeatureRequests` or `HotelFeatureRequests` model to include your new feature
     - For `Override` feature, also add `@description("This is an override feature. There is no guarantee on the correctness of the response")`
   - Add circe `Encoder/Decoder` for your new model in `com.agoda.search.fakeapi.testsetup.model.TestSetupSerializer`
   - Add tapir `Schema` for your new model in `com.agoda.search.fakeapi.route.schema.TestSetupEndpoint`
2. Create a new implementation of either `RoomFeature`, `RoomOverride`, `HotelFeature`, or `HotelOverride`
   - **RoomFeature or HotelFeature** should be used if you know how to setup PAPI dependencies to enable your feature
   - **RoomOverride or HotelOverride** should be used it's an existing feature that you don't know how to setup PAPI dependencies for
3. Update `TestSetupEngine.availableFeatures` to include you new feature
4. Update `TestSetupModule` to include the `bind[...]` definition of the newly-implemented `Feature`
5. Add a new test file for the `Feature` in `com.agoda.papi.search.fakeapitest` package

**TBA: Example MRs**