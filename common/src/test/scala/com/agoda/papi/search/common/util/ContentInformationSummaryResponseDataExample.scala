package com.agoda.papi.search.common.util

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.circe.Serialization.{
  ContentInformationSummaryEncoder,
  ContentInformationSummaryRequestEncoder
}
import com.agoda.content.models.db.nonHotelAccommodation.NHAHostLevel
import com.agoda.content.models.propertycontent._
import com.agoda.content.models.propertycontent.enums.PropertyType
import com.agoda.content.models.request.sf.ContentInformationSummaryRequest
import com.agoda.papi.search.types.PropertyId
import io.circe.parser.parse

object ContentInformationSummaryResponseDataExample {

  private val contentInformationSummaryResponse = ContentInformationSummary(
    propertyId = 1,
    localeName = "Hotel Local Name",
    defaultName = "Hotel Name",
    accommodationType = 1,
    displayName = None,
    propertyType = PropertyType.Hotel,
    remarks = None,
    address = Some(
      ContentAddress(
        country = ContentIdName(106, "Mock Country"),
        city = ContentIdName(1, "Mock City"),
        area = ContentIdName(1, "Mock Area"),
        state = "Some state",
        address1 = "Silom",
        address2 = "Silom",
        postalCode = Option("Silom"),
        gmtOffset = Some(7),
        utcOffset = None,
        stateInfo = ContentIdName(123, "Mock State")
      )
    ),
    geoInfo = None,
    rating = 0.0,
    spokenLanguages = Vector.empty,
    awardYear = None,
    hasHostExperience = false,
    agodaGuaranteeProgram = false,
    accommodation = None,
    atmospheres = Vector.empty,
    propertyLinks = None,
    accommodationTypes = List.empty,
    awardsAndAccolades = ContentAwardsAndAccolades(None, None),
    isInfantCotAvailable = false,
    highlightedFeatures = Vector.empty,
    isHygienePlusOptOut = None,
    nhaSummary = None,
    propertyStatus = None,
    asqInfos = Vector.empty,
    luxury = None,
    hotelCharacter = None,
    isSustainableTravel = None,
    description = None,
    isBOR = None,
    isLuxuryHotel = None,
    checkInOutInfo = None
  )

  private val contentInformationSummaryResponseForNHA = contentInformationSummaryResponse.copy(
    nhaSummary = Some(
      ContentNhaSummary(
        hostLevel = Some(NHAHostLevel(id = 1, name = "some name")),
        isRareFind = Some(true),
        supportedLongStay = Some(true),
        hostType = None,
        isProfessionallyManaged = None,
        hostPropertyCount = None,
        arrivalGuide = None
      )
    ),
    propertyType = PropertyType.SingleRoom,
    hasHostExperience = true
  )

  private val contentInformationSummaryResponseForMNHA = contentInformationSummaryResponse.copy(
    nhaSummary = Some(
      ContentNhaSummary(
        hostLevel = Some(NHAHostLevel(id = 1, name = "some name")),
        isRareFind = Some(true),
        supportedLongStay = Some(true),
        hostType = None,
        isProfessionallyManaged = None,
        hostPropertyCount = None,
        arrivalGuide = None
      )
    ),
    propertyType = PropertyType.NonHotel,
    hasHostExperience = true
  )

  val jsonContentInformationSummaryResponse = parse(
    circeSerializer(ContentInformationSummaryEncoder).serialize(contentInformationSummaryResponse)
  ).right.get

  val contentInformationSummaryWrapperList = List(
    Wrapper(contentInformationSummaryResponse, jsonContentInformationSummaryResponse)
  )

  val contentInformationSummaryResponseWrapperMap: Map[PropertyId, Wrapper[ContentInformationSummary]] = Map(
    1L -> Wrapper(contentInformationSummaryResponse, jsonContentInformationSummaryResponse)
  )

  val jsonContentInformationSummaryResponseForNHA = parse(
    circeSerializer(ContentInformationSummaryEncoder).serialize(contentInformationSummaryResponseForNHA)
  ).right.get

  val contentInformationSummaryWrapperListForNHA = List(
    Wrapper(contentInformationSummaryResponseForNHA, jsonContentInformationSummaryResponseForNHA)
  )

  val contentInformationSummaryResponseWrapperMapForNHA: Map[PropertyId, Wrapper[ContentInformationSummary]] =
    Map(1L -> Wrapper(contentInformationSummaryResponseForNHA, jsonContentInformationSummaryResponseForNHA))

  val jsonContentInformationSummaryResponseForMNHA = parse(
    circeSerializer(ContentInformationSummaryEncoder).serialize(contentInformationSummaryResponseForMNHA)
  ).right.get

  val contentInformationSummaryResponseWrapperMapForMNHA: Map[PropertyId, Wrapper[ContentInformationSummary]] =
    Map(1L -> Wrapper(contentInformationSummaryResponseForMNHA, jsonContentInformationSummaryResponseForMNHA))

  private val contentInformationSummaryRequest = ContentInformationSummaryRequest(None, None)

  private val jsonContentInformationSummaryRequest = parse(
    circeSerializer(ContentInformationSummaryRequestEncoder).serialize(contentInformationSummaryRequest)
  ).right.get

  val contentInformationSummaryRequestWrapper: Option[Wrapper[ContentInformationSummaryRequest]] = Option(
    Wrapper(contentInformationSummaryRequest, jsonContentInformationSummaryRequest)
  )

}
