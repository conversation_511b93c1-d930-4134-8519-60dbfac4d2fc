package com.agoda.papi.search.common.util

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.http.client.v2.HttpClient
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.content.models.circe.Serialization.ContentSummaryEncoder
import com.agoda.content.models.db.experiences.Landmark
import com.agoda.content.models.db.image.{ ImageResponse, VideoResponse }
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.reviews.{ CombinedReviewScoreResponse, ReviewTotal }
import com.agoda.content.models.propertycontent._
import com.agoda.content.models.propertycontent.enums.PropertyType
import com.agoda.content.models.request.sf._
import com.agoda.content.models.{ LocalizationValue, PropertyId }
import com.agoda.papi.content.graphql.ContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.search.common.externaldependency.chunkable.client.HttpContentChunkableClient
import com.agoda.papi.search.common.util.circuitbreaker.NoOpCircuitBreakerWrapper
import com.agoda.papi.search.common.util.configuration.{
  ChunkableWrapperSettings,
  EndpointSettings,
  MergeSettings,
  SplitSettings
}
import com.agoda.papi.search.common.util.constant.ProviderIds
import com.agoda.papi.search.internalmodel.content.PAPIContentSummary
import com.agoda.papi.search.utils.PAPIContentSummaryHelper
import com.agoda.papi.search.utils.PAPIContentSummaryHelper._
import com.netflix.config.scala.{ DynamicBooleanProperty, DynamicIntProperty }
import io.circe.parser.parse
import io.circe.syntax._
import org.joda.time.DateTime
import org.mockito.Mockito._
import sttp.client3.SttpBackend

import java.util.UUID
import scala.concurrent.Future
import scala.concurrent.duration._

object ContentDataExample {
  implicit val sttpBackend: SttpBackend[Future, Nothing] = mock(classOf[SttpBackend[Future, Nothing]])
  val client: HttpClient[Future]                         = mock(classOf[HttpClient[Future]])
  val contentServiceMesh: ServiceMesh                    = mock(classOf[ServiceMesh])
  val mockEndpointSettings =
    EndpointSettings(DynamicIntProperty("fake-path", 5), 5.seconds, DynamicBooleanProperty("fake-path", false))
  val defaultContentWrapper =
    new HttpContentChunkableClient(contentServiceMesh, client, NoOpTracer, NoOpCircuitBreakerWrapper)
  val splitSettings       = SplitSettings(chunkSize = 2)
  val aValidContentReview = ContentReview(ProviderIds.Ycs, true, None, None, None, None)

  val aValidContentInformationSummary = ContentInformationSummary(
    propertyId = 1,
    localeName = "Hotel Local Name",
    defaultName = "Hotel Name",
    accommodationType = 1,
    accommodationTypes = List(ContentAccommodation(1, "Hotel")),
    displayName = None,
    propertyType = PropertyType.Hotel,
    remarks = None,
    address = Some(
      ContentAddress(
        country = ContentIdName(106, "Mock Country"),
        city = ContentIdName(1, "Mock City"),
        area = ContentIdName(1, "Mock Area"),
        state = "Mock State",
        address1 = "Address1",
        address2 = "Address2",
        postalCode = Some("10400"),
        gmtOffset = Some(7),
        utcOffset = None,
        stateInfo = ContentIdName(123, "Mock State")
      )
    ),
    geoInfo = None,
    rating = 0.0,
    spokenLanguages = Vector.empty,
    awardYear = None,
    hasHostExperience = false,
    agodaGuaranteeProgram = false,
    accommodation = None,
    atmospheres = Vector.empty,
    propertyLinks = None,
    awardsAndAccolades = ContentAwardsAndAccolades(None, None),
    isInfantCotAvailable = false,
    highlightedFeatures = Vector.empty,
    isHygienePlusOptOut = None,
    nhaSummary = None,
    propertyStatus = None,
    asqType = None,
    asqInfos = Vector.empty,
    luxury = None,
    hotelCharacter = None,
    isSustainableTravel = None,
    description = None,
    isBOR = None,
    isLuxuryHotel = None,
    checkInOutInfo = None
  )

  val aValidContentRoomInformation = ContentRoomInformation(
    Set.empty,
    Vector(
      ContentMasterRoomInformation(
        12L,
        Some(50.0),
        Vector(ContentPropertyFacility(id = 1, propertyFacilityName = "wifi", None))
      )
    )
  )

  val summaryResponseTmpl = ContentSummary(
    propertyId = 0,
    Some(aValidContentInformationSummary),
    None,
    None,
    None,
    Vector.empty,
    None,
    None,
    None,
    None,
    roomInformation = Some(aValidContentRoomInformation)
  )

  val contextTmpl = PropertyContentContext(
    rawUserId = Some(new UUID(1, 1).toString),
    memberId = None,
    userOrigin = None,
    locale = "",
    forceExperimentsByVariantNew = None,
    forceExperimentsByIdNew = List.empty
  )

  val contentPropertiesRequestTmpl = ContentPropertiesSummaryRequestEnvelope(
    propertyIds = List.empty,
    Wrapper[ContentSummaryRequest](
      ContentSummaryRequest(contextTmpl, rooms = None),
      ContentSummaryRequest(contextTmpl, rooms = None)
        .asJson(com.agoda.content.models.circe.Serialization.ContentSummaryRequestEncoder)
    ),
    Some(OverrideContentRequest(apo = None))
  )

  val aValidContentPropertiesSummaryRequestEnvelope = contentPropertiesRequestTmpl

  val contentSummaryRequest = ContentSummaryRequest(contextTmpl, rooms = None)
  val defaultSettings = ChunkableWrapperSettings(SplitSettings(chunkSize = 3), MergeSettings(1.0), mockEndpointSettings)

  val propertyIds: List[PropertyId]       = List(1001, 1002, 1003, 1004, 1005)
  def searchpropertyIds: List[PropertyId] = (1001L to 1100L).toList ++ (2001L to 2005L).toList
  val propertyIdA                         = 1001
  val propertyIdB                         = 1002
  val propertyIdC                         = 1003
  val propertyIdD                         = 1004
  val propertyIdE                         = 1005
  val propertyListA: List[PropertyId]     = List(1001, 1002)
  val propertyListB: List[PropertyId]     = List(1003, 1004)
  val propertyListC: List[PropertyId]     = List(1005)
  val propertyResponseA: ContentSummary   = summaryResponseTmpl.copy(propertyId = propertyIdA)
  val propertyResponseB: ContentSummary   = summaryResponseTmpl.copy(propertyId = propertyIdB)
  val propertyResponseC: ContentSummary   = summaryResponseTmpl.copy(propertyId = propertyIdC)
  val propertyResponseD: ContentSummary   = summaryResponseTmpl.copy(propertyId = propertyIdD)
  val propertyResponseE: ContentSummary   = summaryResponseTmpl.copy(propertyId = propertyIdE)

  val jsonA = parse(circeSerializer.serialize(propertyResponseA)).right.get
  val jsonB = parse(circeSerializer.serialize(propertyResponseB)).right.get
  val jsonC = parse(circeSerializer.serialize(propertyResponseC)).right.get
  val jsonD = parse(circeSerializer.serialize(propertyResponseD)).right.get
  val jsonE = parse(circeSerializer.serialize(propertyResponseE)).right.get

  val propertyResponseWrapperA: Wrapper[PAPIContentSummary] = Wrapper(propertyResponseA, jsonA)
  val propertyResponseWrapperB: Wrapper[PAPIContentSummary] = Wrapper(propertyResponseB, jsonB)
  val propertyResponseWrapperC: Wrapper[PAPIContentSummary] = Wrapper(propertyResponseC, jsonC)
  val propertyResponseWrapperD: Wrapper[PAPIContentSummary] = Wrapper(propertyResponseD, jsonD)
  val propertyResponseWrapperE: Wrapper[PAPIContentSummary] = Wrapper(propertyResponseE, jsonE)

  val propertyIdsA: List[PropertyId] = List(1001, 1002)
  val propertyIdsB: List[PropertyId] = List(1003, 1004)
  val propertyIdsC: List[PropertyId] = List(1005)

  val contentPropertiesSummaryRequestA = contentPropertiesRequestTmpl.copy(propertyIds = propertyIdsA)
  val contentPropertiesSummaryRequestB = contentPropertiesRequestTmpl.copy(propertyIds = propertyIdsB)
  val contentPropertiesSummaryRequestC = contentPropertiesRequestTmpl.copy(propertyIds = propertyIdsC)

  val contentSummaryResponseA = List(propertyResponseWrapperA, propertyResponseWrapperB)
  val contentSummaryResponseB = List(propertyResponseWrapperC, propertyResponseWrapperD)
  val contentSummaryResponseC = List(propertyResponseWrapperE)

  val propertyIdsListA: List[PropertyId] = List(1001, 1002, 1003)
  val propertyIdsListB: List[PropertyId] = List(1004, 1005)

  val mockContentPropertiesSummaryRequestA = contentPropertiesRequestTmpl.copy(propertyIds = propertyIdsListA)
  val mockContentPropertiesSummaryRequestB = contentPropertiesRequestTmpl.copy(propertyIds = propertyIdsListB)

  val mockContentSummaryResponseA = List(propertyResponseWrapperA, propertyResponseWrapperB, propertyResponseWrapperC)
  val mockContentSummaryResponseB = List(propertyResponseWrapperD, propertyResponseWrapperE)

  val mockContentClient = spy(
    new HttpContentChunkableClient(contentServiceMesh, client, NoOpTracer, NoOpCircuitBreakerWrapper)
  )

  val mockValidContentResponse = List(
    propertyResponseWrapperA,
    propertyResponseWrapperB,
    propertyResponseWrapperC,
    propertyResponseWrapperD,
    propertyResponseWrapperE
  )

  val mockValidContentResponsePS = List(propertyResponseWrapperA, propertyResponseWrapperB)

  val contentPropertiesSummaryRequest = contentPropertiesRequestTmpl.copy(propertyIds = propertyIds)

  def contentWrapperList: List[Wrapper[PAPIContentSummary]] = List.empty

  def aValidContentPropertiesList(hotelIds: List[Long]): List[Wrapper[PAPIContentSummary]] =
    hotelIds.map { hotelId =>
      val propertyResponse: ContentSummary = summaryResponseTmpl.copy(propertyId = hotelId)
      val json                             = parse(circeSerializer.serialize(propertyResponse)).right.get
      Wrapper(PAPIContentSummaryHelper(propertyResponse), json)
    }

  def aValidContentPropertiesMap(hotelIds: List[Long]): Map[Long, Wrapper[PAPIContentSummary]] =
    aValidContentPropertiesList(hotelIds).map { h =>
      h.t.propertyId -> h
    }.toMap

  def aValidContentSummary(
      propertyId: Long,
      informationSummary: Option[ContentInformationSummary] = Some(aValidContentInformationSummary),
      localInformation: Option[ContentLocalInformation] = None,
      roomInformation: Option[ContentRoomInformation] = Some(aValidContentRoomInformation)
  ): ContentSummary =
    ContentSummary(
      propertyId: Long,
      informationSummary = informationSummary,
      localInformation = None,
      highlight = None,
      nonHotelAccommodation = None,
      facilities = Vector.empty,
      familyFeatures = None,
      reviews = None,
      propertyEngagement = None,
      images = None,
      roomInformation = roomInformation,
      synopsis = None,
      personalizedInformation = None,
      features = None,
      rateCategories = None
    )

  lazy val aValidContentTransportationTypeAirport = ContentTransportationType(
    name = "Airport A",
    distanceLongText = "",
    distanceShortText = "",
    mode = Vector(
      ContentTransportationMode(
        name = "Transportation 1",
        duration = 10,
        modeIcon = "Icon"
      )
    )
  )

  lazy val aValidContentTransportation = ContentTransportation(
    airports = Vector(aValidContentTransportationTypeAirport)
  )

  lazy val aValidContentLocalInformation = ContentLocalInformation(
    aValidContentTransportation,
    None,
    true
  )

  lazy val aValidContentReviewTotal      = ContentReviewTotal(reviewCount = 100, score = 7.5d, maxScore = 10)
  lazy val aValidContentReviewScoreGrade = ContentReviewScoreGrade(id = "some id", score = 7.5d, None, None, None, None)
  lazy val aValidContentReviewGroupScore =
    ContentReviewGroupScore(id = 1, reviewCount = 100, grades = Vector(aValidContentReviewScoreGrade))
  lazy val aValidContentReviewScores   = ContentReviewScores(Vector(aValidContentReviewGroupScore))
  lazy val aValidContentReviewResponse = ContentReviewResponse(List(aValidContentReview), None, None)
  lazy val aValidCombinedReviewScoreResponse =
    CombinedReviewScoreResponse(332L, 10L, 10L, "demo", score = 7.8d, "Good", 9, None)

  val aValidContentLocations         = ContentLocations("key", "value")
  val aValidContentLocalizationValue = LocalizationValue("en-us", "caption")

  val aValidContentImageResponse = ImageResponse(
    id = 1L,
    caption = "main",
    typeId = 1,
    providerId = 1,
    groupId = Some("groupId"),
    groupEntityId = Some(1),
    urls = List(aValidContentLocations),
    captionLocalizations = Some(List(aValidContentLocalizationValue)),
    uploadedDate = Some(DateTime.now()),
    locations = Map.empty,
    group = "",
    captionId = 1
  )

  val aValidContentVideoResponse = VideoResponse(
    id = 1L,
    location = "1.mp4"
  )

  val aValidContentGeoInfo = ContentGeoInfo(
    latitude = 1.0,
    longitude = 1.2,
    obfuscatedLat = 1.2,
    obfuscatedLong = 1.0
  )

  val aValidContentPlace = Place(
    name = Some("name"),
    abbr = Some("abbr"),
    distance = Some(0.1),
    distanceUnit = Some("km"),
    distanceDisplay = Some("0.1 km"),
    distanceLongText = Some("distance is 0.1km"),
    distanceShortText = Some("distance 0.1km"),
    duration = Some(5),
    durationIcon = Some("duration icon"),
    geoInfo = Some(aValidContentGeoInfo),
    images = Some(Vector(aValidContentImageResponse)),
    typeName = Some("typename"),
    typeId = Some(1),
    landmarkId = Some(1),
    distanceInKm = Some(0.1)
  )

  val aValidContentNearbyProperty = NearbyProperty(
    id = Some("some id"),
    categoryName = Some("category name"),
    categorySymbol = Some("category symbol"),
    places = Some(Vector(aValidContentPlace))
  )

  val aValidContentRestaurant = Restaurant(
    id = Some(1),
    name = Some("some restaurant"),
    distance = Some(0.3),
    cuisinesOffered = Some("some cuisine")
  )

  val aValidContentCuisine = Cuisine(
    id = Some(1),
    name = Some("some cuisine"),
    images = Some(Vector(aValidContentImageResponse)),
    restaurants = Some(Vector(aValidContentRestaurant))
  )

  val aValidContentLocationSubScore = LocationSubscore(
    poiScore = Some(1.0),
    transportationScore = Some(2.0),
    airportScore = Some(3.0)
  )

  val aValidContentLocalInformationResponse = LocalInformation(
    propertyId = 1L,
    nearbyProperties = Some(Vector(aValidContentNearbyProperty)),
    nearbyPlaces = Some(Vector(aValidContentPlace)),
    topPlaces = Some(Vector(aValidContentPlace)),
    cuisines = Some(Vector(aValidContentCuisine)),
    transportation = None,
    summary = None,
    popularLandmarkNumber = Some(10),
    locationSubscore = Some(aValidContentLocationSubScore)
  )

  val aValidReviewTotal = ReviewTotal(
    providerId = Some(332L),
    reviewCount = Some(102L),
    reviewCommentsCount = Some(123L),
    demographic = Some("allGuests"),
    score = Some(1.0),
    scoreText = Some("excellent"),
    maxScore = Some(1),
    countText = Some("some count text")
  )

  val aValidLandmark = Landmark(
    name = Some("some landmark"),
    geoInfo = Some(aValidContentGeoInfo),
    distance = Some(1.0),
    distanceUnit = Some("km"),
    scores = Some(aValidReviewTotal),
    images = Some(Vector(aValidContentImageResponse)),
    distanceInKm = Some(1.0)
  )

}
