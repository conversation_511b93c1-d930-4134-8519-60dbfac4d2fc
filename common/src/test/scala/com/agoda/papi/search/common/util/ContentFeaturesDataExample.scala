package com.agoda.papi.search.common.util

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.LocalizationValue
import com.agoda.content.models.circe.Serialization.{
  ContentFeaturesRequestEncoder,
  ContentFeaturesResponseEncoder,
  FeatureGroupTypesEncoder
}
import com.agoda.content.models.db.features._
import com.agoda.content.models.propertycontent.ContentFeaturesResponse
import com.agoda.content.models.request.sf.ContentFeaturesRequest
import com.agoda.papi.search.types.PropertyId
import io.circe.parser.parse

object ContentFeaturesDataExample {

  val aValidHotelFacility = HotelFacility(
    id = Some(1),
    providerId = Some(Vector(332)),
    name = Some("some facility")
  )

  val aValidFeatureTag = FeatureTag(
    id = Some("some feature tag")
  )

  val aValidFeature = Feature(
    id = Some(1),
    featureName = Some("some feature name"),
    featureNameLocalizations = Some(Map("en-us" -> "engglish us feature name")),
    featureNameLocalizationList = Some(List(LocalizationValue("en-us", "engglish us feature name"))),
    symbol = Some("some symbol"),
    emphasis = Some(false),
    available = Some(true),
    tags = Some("some tag"),
    order = Some(1),
    highlighted = Some(false),
    images = Some(Vector.empty),
    featureTags = Some(Vector(aValidFeatureTag)),
    providerIds = Some(List(123)),
    personalizedRank = Some(1)
  )

  val aValidFeatureGroup = FeatureGroup(
    id = Some(1),
    name = Some("some name"),
    symbol = Some("symbol"),
    order = Some(1),
    features = Some(Vector(aValidFeature))
  )

  val aValidFeatureGroupType = FeatureGroupType(
    id = Some(1),
    name = Some("feature group type"),
    featureGroups = Some(Vector(aValidFeatureGroup))
  )

  val aValidFeatureGroupTypes = FeatureGroupTypes(
    propertyId = 1234L,
    featureGroupTypes = Some(Vector(aValidFeatureGroupType)),
    hotelFacilities = Some(Vector(aValidHotelFacility)),
    summary = None,
    facilityHighlights = None,
    additionalInfos = None,
    luxuryFacilityHighlights = None
  )

  val aValidContentFeaturesResponse = ContentFeaturesResponse(
    propertyId = 1234L,
    featureGroups = Some(Vector(aValidFeatureGroup)),
    hotelFacilities = Some(Vector(aValidHotelFacility)),
    summary = None,
    facilityHighlights = None,
    additionalInfos = None,
    luxuryFacilityHighlights = None
  )

  val jsonContentFeaturesResponse = parse(
    circeSerializer(ContentFeaturesResponseEncoder).serialize(aValidContentFeaturesResponse)
  ).right.get

  val jsonFeatureGroupTypes = parse(
    circeSerializer(FeatureGroupTypesEncoder).serialize(aValidFeatureGroupTypes)
  ).right.get

  val contentFeaturesRequest = ContentFeaturesRequest(None, None, None, None, None)

  val jsonContentFeaturesRequest = parse(
    circeSerializer(ContentFeaturesRequestEncoder).serialize(contentFeaturesRequest)
  ).right.get

  val contentFeaturesRequestWrapper: Option[Wrapper[ContentFeaturesRequest]] = Option(
    Wrapper(contentFeaturesRequest, jsonContentFeaturesRequest)
  )

  val contentFeaturesResponseWrapperMap: Map[PropertyId, Wrapper[ContentFeaturesResponse]] = Map(
    1L -> Wrapper(aValidContentFeaturesResponse, jsonContentFeaturesResponse)
  )

}
