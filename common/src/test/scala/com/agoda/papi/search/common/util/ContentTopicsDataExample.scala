package com.agoda.papi.search.common.util

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.circe.Serialization.{ ContentTopicsResponseEncoder, TopicsRequestEncoder }
import com.agoda.content.models.propertycontent.{
  ContentTopic,
  ContentTopicImage,
  ContentTopicSentimentScore,
  ContentTopicsResponse
}
import com.agoda.content.models.request.sf.TopicsRequest
import com.agoda.papi.search.types.PropertyId
import io.circe.parser.parse

object ContentTopicsDataExample {

  val aValidTopic = ContentTopic(
    topic = "Breakfast",
    sentimentScore = Some(
      ContentTopicSentimentScore(
        positive = 0.8,
        negative = 0.1,
        neutral = 0.1
      )
    ),
    reviewSnippets = Vector(
      "Snippet1",
      "Snippet2"
    ),
    images = Vector(
      ContentTopicImage(
        url = "//example.com/image1.jpg",
        imageId = 1,
        imageSource = "ycs"
      )
    ),
    summary = Some("Summary")
  )

  val aValidContentTopicsResponse = ContentTopicsResponse(
    propertyId = 1234L,
    topics = Vector(aValidTopic)
  )

  val aValidContentTopicsRequest = TopicsRequest(limit = Some(10))

  val jsonContentTopicsResponse = parse(
    circeSerializer(ContentTopicsResponseEncoder).serialize(aValidContentTopicsResponse)
  ).right.get

  val jsonContentTopicsRequest = parse(
    circeSerializer(TopicsRequestEncoder).serialize(aValidContentTopicsRequest)
  ).right.get

  val contentTopicsRequestWrapper: Option[Wrapper[TopicsRequest]] = Option(
    Wrapper(aValidContentTopicsRequest, jsonContentTopicsRequest)
  )

  val contentTopicsResponseWrapperMap: Map[PropertyId, Wrapper[ContentTopicsResponse]] = Map(
    1L -> Wrapper(aValidContentTopicsResponse, jsonContentTopicsResponse)
  )

}
