package com.agoda.papi.search.common.util

import api.request.FeatureFlag
import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.PropertyId
import com.agoda.core.search.models.builders.TestModelsBuilders.avalidSearchResult
import com.agoda.core.search.models.response._
import com.agoda.core.search.models.response.matrix.MatrixGroupResult
import com.agoda.papi.search.common.externaldependency.chunkable.ChunkableServiceFuture
import com.agoda.papi.search.common.util.ContentDataExample._
import com.agoda.papi.search.common.util.CoreDataExamples.globalContext
import com.agoda.papi.search.common.util.DFDataExample._
import com.agoda.papi.search.common.util.EnrichmentDataExample.aValidPropertyId
import com.agoda.papi.search.internalmodel
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultTypes
import com.agoda.papi.search.internalmodel.pricing.PAPIHotelPricing
import com.agoda.papi.search.internalmodel.promocode.BannerCampaignRequest
import com.agoda.papi.search.internalmodel._
import com.agoda.pricing.models.circe.Encoders._
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import io.circe.parser.parse
import models.pricing.enums.ApplyTypes
import models.pricing.enums.RequestedPrice.AllInclusive
import models.starfruit.{ PricingRequestParameters, PropertySearchRequest }
import models.{ DFSuggestPriceType, HotelPricing }
import org.joda.time.DateTime
import org.mockito.Mockito

import scala.collection.mutable

object ResolverDataExamples {
  val whiteLabelClientApiMultiEnv: WhiteLabelClientMultiEnv = Mockito.mock(classOf[WhiteLabelClientMultiEnv])
  Mockito.when(whiteLabelClientApiMultiEnv.getTokenAccordingToWhiteLabelId(0)).thenReturn("")

  val cityId                 = 100
  val propertyId1            = 1001
  val propertyId2            = 1002
  val propertyIds: Seq[Long] = Seq(propertyId1, propertyId2)
  val DFPropertiesRequest    = aValidPricingPropertiesRequest.withPropertyIds(propertyIds)
  val defaultDFService       = mock[ChunkableServiceFuture[PropertySearchRequest, List[Wrapper[HotelPricing]]]]
  val jsonDFRequest = parse(circeSerializer[PricingRequestParameters].serialize(aPricingRequestParameters)).right.get
  val pricingParametersWrapper = Wrapper(aPricingRequestParameters, jsonDFRequest)

  val pricingParametersWrapperE = Wrapper(
    aPricingRequestParameters.copy(pricing = aPricingRequest.copy(featureFlag = List(FeatureFlag.ConnectedTrip))),
    jsonDFRequest
  )

  val pricingParametersWrapperWithNoFeatureFlag =
    Wrapper(aPricingRequestParameters.copy(pricing = aPricingRequest.copy(featureFlag = List.empty)), jsonDFRequest)
  val expectedResult = List(Wrapper(HotelC, JsonC), Wrapper(HotelD, JsonD))

  val contentPropertyIds: List[PropertyId] = List(1001, 1002)
  val contentPropertiesSummaryRequest      = contentPropertiesRequestTmpl.copy(propertyIds = contentPropertyIds)

  val jsonContentRequest = parse(
    circeSerializer(com.agoda.content.models.circe.Serialization.ContentSummaryRequestEncoder)
      .serialize(contentSummaryRequest)
  ).right.get

  val contentSummaryWrapper    = Wrapper(contentSummaryRequest, jsonContentRequest)
  val contentPropertiesWrapper = contentPropertiesSummaryRequest

  val propertySearchRequestWrapper = PropertySearchRequestWrapper(
    contentSummaryWrapper,
    Some(pricingParametersWrapper),
    None,
    None,
    searchRequest = CoreDataExamples.aValidCitySearchRequest,
    globalContext,
    capiEmailDomain = ""
  )

  val propertySearchRequestWrapperE = PropertySearchRequestWrapper(
    contentSummaryWrapper,
    Some(pricingParametersWrapperE),
    None,
    None,
    searchRequest = CoreDataExamples.aValidCitySearchRequest,
    globalContext,
    capiEmailDomain = ""
  )

  val propertySearchRequestWrapperWithNoFeatureFlag = PropertySearchRequestWrapper(
    contentSummaryWrapper,
    Some(pricingParametersWrapperWithNoFeatureFlag),
    None,
    None,
    searchRequest = CoreDataExamples.aValidCitySearchRequest,
    globalContext,
    capiEmailDomain = ""
  )

  val propertyRequestDeferred = PropertyRequestDeferred(propertySearchRequestWrapper, avalidSearchResult)
  val aggregationRequestDeferred =
    AggregationRequestDeferred(CoreDataExamples.aValidCitySearchRequest, avalidSearchResult)

  val propertySummarySearchDeferred = PropertySummarySearchDeferred(
    propertyIds.toList,
    Some(aPricingRequestParameters),
    Some(aValidContentPropertiesSummaryRequestEnvelope.contentSummaryRequest.t)
  )

  val pollingInfoDeferred = PollingInfoDeferred(Some(aValidPollingInfoRequest))

  val aValidPAPIHotelPricing = PAPIHotelPricing(
    aValidPropertyId,
    isReady = true,
    isAvailable = true,
    cheapestRoomOffer = None,
    suppliersSummary = None,
    supplierInfo = None,
    suggestedRoomQuantity = 1,
    suggestPriceType = DFSuggestPriceType(AllInclusive, ApplyTypes.PB)
  )

  val pointMaxInformationDeferred = PointMaxInformationDeferred(propertySearchRequestWrapper)

  val aValidPropertySummaryModel =
    PropertySummaryModel(100L, None, None, PropertyResultTypes.NormalProperty, None, None, None)
  val bookingHistoryDeferredPropertyA = BookingHistoryDeferred(Some(aValidPropertySummaryModel.copy(propertyId = 100L)))
  val bookingHistoryDeferredPropertyB = BookingHistoryDeferred(Some(aValidPropertySummaryModel.copy(propertyId = 200L)))
  val bookingHistoryDeferredPropertyC = BookingHistoryDeferred(Some(aValidPropertySummaryModel.copy(propertyId = 300L)))
  val bookingHistoryDeferredNone      = BookingHistoryDeferred(None)

  lazy val aValidRatingMatrixResult1 = Seq(
    MatrixResultElement("", 3, Option(315), Some("starRating")),
    MatrixResultElement("", 4, Option(274), Some("starRating"))
  )

  lazy val aValidFacilityMatrixResult = Seq(
    MatrixResultElement("Swimming Pool", 93, Option(642), Some("hotelFacilities")),
    MatrixResultElement("Internet", 90, Option(987), Some("hotelFacilities"))
  )

  val aValidAggregationResponse = AggregationResponse(
    propertyLandmarkInfo = Some(List(PropertyLandmarkInfo(1001, Some(10.5), Some("Airport"), List.empty, None))),
    matrix = Some(
      MatrixResult(result =
        mutable.Map("hotelFacilities" -> aValidFacilityMatrixResult, "starRating" -> aValidRatingMatrixResult1)
      )
    ),
    popularFilterMatrix = Seq.empty[PopularFilterResult],
    personalizedFilterMatrix = Seq.empty[PopularFilterResult],
    segmentFilterMatrix = Seq.empty[PopularFilterResult],
    matrixGroupResults = List.empty[MatrixGroupResult]
  )

  val propertyRequestDefered = Vector(propertyRequestDeferred)

  val aggregationDeferred = Vector(aggregationRequestDeferred)

  val propertySummaryEnvelopeA = PropertySummaryEnvelope(
    1001,
    propertyResponseWrapperA,
    Some(hotelWrapperA),
    None,
    None,
    None,
    PropertySponsoredDetail(),
    PropertyResultTypes.NormalProperty
  )

  val propertySummaryEnvelopeB = internalmodel.PropertySummaryEnvelope(
    1002,
    propertyResponseWrapperB,
    Some(hotelWrapperB),
    None,
    None,
    None,
    PropertySponsoredDetail(),
    PropertyResultTypes.NormalProperty
  )

  val propertySummaryEnvelopeE = internalmodel.PropertySummaryEnvelope(
    1003,
    propertyResponseWrapperE,
    Some(hotelWrapperE),
    None,
    None,
    None,
    PropertySponsoredDetail(),
    PropertyResultTypes.NormalProperty
  )

  val propertySearchServiceResponse  = List(propertySummaryEnvelopeA, propertySummaryEnvelopeB)
  val propertySearchServiceResponseE = List(propertySummaryEnvelopeE)

  val cumulativePropertyResponse =
    CumulativePropertiesResponse(propertySearchServiceResponse, pagingFlowMetadata = None)
  val cumulativePropertyResponseE =
    internalmodel.CumulativePropertiesResponse(propertySearchServiceResponseE, pagingFlowMetadata = None)

  val aValidContentRequestJson =
    """
      |{
      |  "ContentPropertiesSummaryRequest":{
      |  "propertyIds" : [
      |    1001,
      |    1002
      |  ],
      |  "contentSummaryRequest" : {
      |     "context" : {
      |      "rawUserId" : "00000000-0000-0001-0000-000000000001",
      |      "memberId" : null,
      |      "userOrigin" : null,
      |      "locale" : "",
      |      "forceExperimentsByVariantNew" : null,
      |      "forceExperimentsByIdNew" : [
      |      ],
      |      "storeFrontId" : null,
      |      "showCMS" : null,
      |      "apo" : null,
      |      "searchCriteria" : null,
      |      "correlationId" : null,
      |      "platform" : null,
      |      "occupancy" : null,
      |      "trafficInfo" : null,
      |      "deviceTypeId" : null,
      |      "cid" : null,
      |      "localizations" : null,
      |      "requestMeta" : null,
      |      "whiteLabelKey" : null
      |    },
      |  "reviews" : null,
      |  "reviewDetails" : null,
      |  "images" : null,
      |  "information" : null,
      |  "summary" : null,
      |  "features" : null,
      |  "experiences" : null,
      |  "localInformation" : null,
      |  "checkInOut" : null,
      |  "engagement" : null,
      |  "rooms" : null,
      |  "highlights" : null,
      |  "nonHotelAccommodation" : null,
      |  "synopsis" : null,
      |  "usp" : null,
      |  "personalizedInformation" : null,
      |  "rateCategories" : null,
      |  "contentRateCategories" : null
      |},
      |  "overrideContentRequest": {
      |     "apo": null,
      |     "selectedLandmarkId": null
      |  }
      |}
      |}
    """.stripMargin

  val contentImagesDeferred = ContentImagesDeferred(1, ContentImageResponseDataExample.contentImagesRequestWrapper)
  val contentReviewScoreDeferred =
    ContentReviewScoreDeferred(1, ContentReviewScoreResponseDataExample.contentReviewScoreRequestWrapper)
  val contentReviewSummariesDeferred =
    ContentReviewSummariesDeferred(1, ContentReviewSummariesResponseDataExample.contentReviewSummariesRequestWrapper)

  val contentInformationSummaryDeferred = ContentInformationSummaryDeferred(
    1,
    ContentInformationSummaryResponseDataExample.contentInformationSummaryRequestWrapper
  )

  val contentFeaturesDeferred = ContentFeaturesDeferred(1, ContentFeaturesDataExample.contentFeaturesRequestWrapper)
  val contentHighlightsDeferred =
    ContentHighlightsDeferred(1, ContentHighlightsDataExample.contentHighlightsRequestWrapper)
  val contentQnaDeferred    = ContentQnaDeferred(1, ContentQnaDataExample.contentQnaRequestWrapper)
  val contentTopicsDeferred = ContentTopicsDeferred(1, ContentTopicsDataExample.contentTopicsRequestWrapper)
  val creditCardCampaignInfoDeferred =
    CreditCardCampaignInfoDeferred(propertySearchRequestWrapper, cumulativePropertyResponse)
  val creditCardCampaignInfosDeferred =
    CreditCardCampaignInfosDeferred(propertySearchRequestWrapper, cumulativePropertyResponse)
  val hostProfileDeferred = HostProfileDeferred(1, None)
  val propertyBannerCampaignInfoDeferred =
    PropertyBannerCampaignInfoDeferred(1, BannerCampaignRequest(DateTime.parse("2021-01-01")))

  val searchBannerCampaignInfoDeferred =
    SearchBannerCampaignInfoDeferred(
      CoreDataExamples.aValidRegionSearchRequest,
      BannerCampaignRequest(DateTime.now)
    )

  val priceTrendSearchDetailsDeferred = PriceTrendSearchDetailsDeferred(aValidPriceTrendSearchRequest)

  val propertyAllotmentSearchDetailsDeferred = PropertyAllotmentSearchDetailsDeferred(
    CoreDataExamples.aValidPropertyAllotmentSearchRequest
  )

  val contentRateCategoryDeferred =
    ContentRateCategoryDeferred(1, ContentRateCategoryDataExample.contentRateCategoryRequestWrapper)

}
