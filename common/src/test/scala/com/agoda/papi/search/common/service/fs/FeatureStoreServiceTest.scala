package com.agoda.papi.search.common.service.fs

import com.agoda.feast.api.AnyEntity
import com.agoda.feast.proto.types.Value.Val.{ Int32ListVal, StringListVal }
import com.agoda.feast.proto.types.{ Int32List, StringList, Value }
import com.agoda.papi.search.common.service.fs.DefaultFeatureStoreServiceUtils.FeatureStoreEntities.{
  CityIdAndPlatformIdEntity,
  CityIdEntity
}
import com.agoda.papi.search.common.service.fs.FeatureStoreService

import scala.concurrent.Future
import scala.concurrent.duration.Duration

class FeatureStoreServiceTest extends FeatureStoreService {

  private val mockResult = Map(
    "actions" -> Value(
      StringListVal(
        StringList(
          Seq("search-filter-hotelfacilities", "search-filter-reviewlocationscore", "search-filter-hotelfacilities")
        )
      )
    ),
    "values" -> Value(StringListVal(StringList(Seq("83", "8", "93"))))
  )

  private val mockResultInvalid = Map(
    "actions" -> Value(
      StringListVal(
        StringList(
          Seq("search-filter-hotelfacilities", "search-filter-reviewlocationscore", "search-filter-hotelfacilities")
        )
      )
    ),
    "values" -> Value(Int32ListVal(Int32List(Seq(83, 8, 93))))
  )

  override def getSingleEntityFeatures(
      featureSet: String,
      entity: AnyEntity,
      features: Seq[String]
  ): Future[Map[String, Value]] =
    if (entity.asInstanceOf[CityIdEntity].city_id == 1) {
      throw new Exception("Feature Store Exception")
    } else if (entity.asInstanceOf[CityIdEntity].city_id == 2) {
      Future.successful(mockResultInvalid)
    } else {
      Future.successful(mockResult)
    }

  /** Get Features for Composite entities.
    *
    * @param featureSet
    *   name of Feature Set.
    * @param entities
    * @param features
    * @return
    *   Features, each map correspond to a row for each entity.
    */
  override def getCompositeEntitiesFeatures(
      featureSet: String,
      entities: Seq[AnyEntity],
      features: Seq[String],
      maxAge: Option[Duration]
  ): Future[Seq[Map[String, Value]]] =
    if (entities.head.asInstanceOf[CityIdAndPlatformIdEntity].city_id == 1) {
      throw new Exception("Feature Store Exception")
    } else if (entities.head.asInstanceOf[CityIdAndPlatformIdEntity].city_id == 2) {
      Future.successful(Seq(mockResultInvalid))
    } else {
      Future.successful(Seq(mockResult))
    }

}
