package com.agoda.papi.search.common.util

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.circe.Serialization.{ ContentImagesRequestRequestEncoder, ContentImagesResponseEncoder }
import com.agoda.content.models.propertycontent.ContentImagesResponse
import com.agoda.content.models.request.sf.ContentImagesRequest
import com.agoda.papi.search.types.PropertyId
import io.circe.parser.parse

object ContentImageResponseDataExample {

  val contentImagesResponse = ContentImagesResponse(
    1L,
    Some(ContentDataExample.aValidContentImageResponse),
    Vector(ContentDataExample.aValidContentImageResponse.copy(caption = "hotelImg")),
    None,
    Some(Vector(ContentDataExample.aValidContentVideoResponse)),
    None,
    None,
    None
  )

  val jsonContentImagesResponse = parse(
    circeSerializer(ContentImagesResponseEncoder).serialize(contentImagesResponse)
  ).right.get

  val contentImagesResponseWrapperList = List(Wrapper(contentImagesResponse, jsonContentImagesResponse))

  val contentImagesResponseWrapperMap: Map[PropertyId, Wrapper[ContentImagesResponse]] = Map(
    1L -> Wrapper(contentImagesResponse, jsonContentImagesResponse)
  )

  private val contentImagesRequest = ContentImagesRequest(None, None, None)

  private val jsonContentImagesRequest = parse(
    circeSerializer(ContentImagesRequestRequestEncoder).serialize(contentImagesRequest)
  ).right.get

  val contentImagesRequestWrapper = Wrapper(contentImagesRequest, jsonContentImagesRequest)
}
