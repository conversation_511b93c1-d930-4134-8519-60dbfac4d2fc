package com.agoda.papi.search.common.util

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.content.models.OccupancyRequest
import com.agoda.content.models.circe.Serialization.{
  ContentHighlightsRequestEncoder,
  ContentHighlightsResponseEncoder
}
import com.agoda.content.models.db.highlights.{ Highlight, HighlightDistanceInformation, HighlightInformation }
import com.agoda.content.models.propertycontent.ContentHighlightsResponse
import com.agoda.content.models.request.sf.{ ContentFiltersApplied, ContentHighlightsRequest }
import com.agoda.papi.search.types.PropertyId
import io.circe.parser.parse

object ContentHighlightsDataExample {

  val aValidHighlight = Highlight(
    id = Some(1),
    name = Some("name"),
    title = Some("title"),
    symbol = Some("symbol"),
    category = Some("category"),
    order = Some(1),
    images = Some(Vector.empty),
    tooltip = Some("tool tip"),
    highlightInformation = None,
    topicName = Some("topic")
  )

  val aValidHighlightInformation = HighlightInformation(
    title = Some("title"),
    highlightItems = None
  )

  val aValidHighlightDistanceInformation = HighlightDistanceInformation(
    message = Some("message"),
    distanceKm = Some(0.4),
    highlightType = Some("highligtytype")
  )

  val aValidHighLight = Highlight(
    id = Some(1),
    name = Some(""),
    title = Some(""),
    symbol = Some(""),
    category = None,
    order = Some(1),
    topicName = Some("topic")
  )

  val aValidContentHighlightsResponse = ContentHighlightsResponse(
    propertyId = 1234L,
    favoriteFeatures = Some(Vector(aValidHighlight)),
    locations = Some(Vector(aValidHighlight)),
    locationHighlightMessage = Some(aValidHighlightInformation),
    locationHighlights = Some(Vector(aValidHighlightDistanceInformation)),
    atfPropertyHighlights = Some(Vector(aValidHighLight)),
    luxury = None,
    propertyCollection = None
  )

  val aValidContentHighlightsRequest = ContentHighlightsRequest(
    maxNumberOfItems = Some(10),
    occupancyRequest = Some(
      OccupancyRequest(
        numberOfAdults = 2,
        numberOfChildren = 0,
        travelerType = None
      )
    ),
    images = None,
    contentFiltersApplied = Some(
      ContentFiltersApplied(
        facilityIds = Some(Seq.empty),
        benefitIds = Some(Seq.empty),
        locationScores = Some(Seq.empty),
        landmarkIds = Some(Seq.empty),
        areaIds = Some(Seq.empty)
      )
    )
  )

  val jsonContentHighlightsResponse = parse(
    circeSerializer(ContentHighlightsResponseEncoder).serialize(aValidContentHighlightsResponse)
  ).right.get

  val jsonContentHighlightsRequest = parse(
    circeSerializer(ContentHighlightsRequestEncoder).serialize(aValidContentHighlightsRequest)
  ).right.get

  val contentHighlightsRequestWrapper: Option[Wrapper[ContentHighlightsRequest]] = Option(
    Wrapper(aValidContentHighlightsRequest, jsonContentHighlightsRequest)
  )

  val contentHighlightsResponseWrapperMap: Map[PropertyId, Wrapper[ContentHighlightsResponse]] = Map(
    1L -> Wrapper(aValidContentHighlightsResponse, jsonContentHighlightsResponse)
  )

}
