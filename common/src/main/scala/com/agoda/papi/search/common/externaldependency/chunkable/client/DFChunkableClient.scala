package com.agoda.papi.search.common.externaldependency.chunkable.client

import cats.MonadError
import com.agoda.common.graphql.model.Wrapper
import com.agoda.common.graphql.model.WrapperSerializer.toListDeserializer
import com.agoda.commons.http.client.v2.{ HttpClient, RequestSettings => ClientRequestSettings }
import com.agoda.commons.http.mesh.v2.{ RequestSettings, ServiceMesh }
import com.agoda.commons.serialization.v1.instances.circe._
import com.agoda.commons.tracing.core.SpanMaterializer
import com.agoda.commons.tracing.core.TracerOps._
import com.agoda.commons.tracing.models.SpanTags
import com.agoda.core.search.models.response.CheapestCheckInCheckOut
import com.agoda.papi.pricing.graphql.EnvelopeSerializer.PricingPropertiesRequestEnvelopeEncoder
import com.agoda.papi.pricing.graphql.PricingPropertiesRequestEnvelope
import com.agoda.papi.search.common.externaldependency.chunkable.ChunkableServiceFuture
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.model.Component
import com.agoda.papi.search.common.service.{ CalculateChannelResult, PropertyMetaService }
import com.agoda.papi.search.common.util.circuitbreaker.CircuitBreakerWrapper
import com.agoda.papi.search.common.util.configuration.{ ChunkableWrapperSettings, EndpointSettings, SplitSettings }
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext, WithCorrelationId }
import com.agoda.papi.search.internalmodel.pricing.PAPIHotelPricing
import com.agoda.papi.search.internalmodel.request.PricingPropertiesRequestWrapper
import com.agoda.papi.search.internalmodel.serializer.PAPIHotelPricingSerializer
import com.agoda.platform.service.context.GlobalContext
import com.typesafe.scalalogging.LazyLogging
import io.opentracing.Tracer
import models.starfruit.{ DiscountRequest, OverridePricingRequest }
import sttp.model.Uri
import sttp.model.Uri._

import scala.concurrent.{ ExecutionContext, Future }

trait DFChunkableClient extends ChunkableClientFuture[PricingPropertiesRequestWrapper, List[Wrapper[PAPIHotelPricing]]]

class HttpDFChunkableClient(
    dfServiceMesh: ServiceMesh,
    client: HttpClient[Future],
    tracer: Tracer,
    circuitBreaker: CircuitBreakerWrapper
) extends DFChunkableClient with LazyLogging {

  private val endpointTemplate: Uri = uri"http://localhost:80/Api/v2/properties"

  override def split(
      request: PricingPropertiesRequestWrapper,
      settings: SplitSettings,
      experimentContext: ExperimentContext
  ): List[PricingPropertiesRequestWrapper] =
    if (request.hotelOverrideCheckInDate.nonEmpty) {
      splitByOverrideCheckIn(request)
    } else {
      splitByHotelSizeHints(request, settings)
    }

  def splitByHotelSizeHints(
      request: PricingPropertiesRequestWrapper,
      settings: SplitSettings
  ): List[PricingPropertiesRequestWrapper] =
    tracer.withSpan(
      "chunk-split",
      SpanMaterializer.customSpan(_.setTag("num-hotel", request.pricingRequest.propertyIds.size))
    ) {
      val hotelSizeHints = request.hotelSizeHints
      val hotelsWithSize = request.pricingRequest.propertyIds.map { hotelId =>
        Element(hotelId, hotelSizeHints.getOrElse(hotelId, 1))
      }
      val chunkSize  = Math.max(settings.chunkSize, 1) // prevent divide by zero
      val partitions = GreedyItemPartitioner.partition(hotelsWithSize, chunkSize)
      partitions.map { partition =>
        val hasAtLeastOnePropertyWithHourlyRate =
          partition.map(_.item).exists(id => request.hasHourlyRatesMap.getOrElse(id, false))
        val overridePricingRequest = if (hasAtLeastOnePropertyWithHourlyRate) {
          Some(
            request.pricingRequest.overridePricingRequest
              .getOrElse(OverridePricingRequest(None))
              .copy(hasHourlyRates = Some(true))
          )
        } else {
          request.pricingRequest.overridePricingRequest
        }
        request.copy(pricingRequest =
          request.pricingRequest.copy(
            propertyIds = partition.map(_.item),
            overridePricingRequest = overridePricingRequest
          )
        )
      }.toList
    }

  private def splitByOverrideCheckIn(
      request: PricingPropertiesRequestWrapper
  ): List[PricingPropertiesRequestWrapper] = {
    val dateToPropertyIds = request.hotelOverrideCheckInDate.toList
      .groupBy { case (_, dt) => dt }
      .mapValues(_.map { case (propertyId, _) => propertyId })
    val batchSizes = dateToPropertyIds.values.map(_.size)

    tracer.withSpan(
      "chunk-split-flexible",
      SpanMaterializer.customSpan(_.setTag("batch-size", batchSizes.mkString(",")))
    ) {
      dateToPropertyIds.map { case (CheapestCheckInCheckOut(checkInDate, checkOutDate), propertyIds) =>
        request.copy(pricingRequest =
          request.pricingRequest.copy(
            propertyIds = propertyIds,
            overridePricingRequest = Some(
              request.pricingRequest.overridePricingRequest
                .getOrElse(OverridePricingRequest(None))
                .copy(
                  checkInDate = Some(checkInDate),
                  checkOutDate = Some(checkOutDate),
                  hasHourlyRates = Some(propertyIds.exists(id => request.hasHourlyRatesMap.getOrElse(id, false)))
                )
            )
          )
        )
      }(collection.breakOut)
    }
  }

  override def aggregate(responses: List[List[Wrapper[PAPIHotelPricing]]]): List[Wrapper[PAPIHotelPricing]] =
    responses.flatten

  override def serviceCall(
      chunkRequest: PricingPropertiesRequestWrapper,
      globalContext: GlobalContext,
      endpointSettings: EndpointSettings
  )(implicit
      ME: cats.MonadError[Future, Throwable],
      contextHolder: ContextHolder
  ): Future[List[Wrapper[PAPIHotelPricing]]] = {
    import io.circe.syntax._
    //  ToDO: move this code to Chunkable Service
    tracer
      .activeSpan()
      .setTag(SpanTags.OBJECT_ID, chunkRequest.pricingRequest.propertyIds.mkString(", "))
      .setTag("properties-size", chunkRequest.pricingRequest.propertyIds.length)

    if (globalContext.isDebug.contains(true)) {
      logger.info(s"DF req ==> ${chunkRequest.pricingRequest.asJson.noSpaces}")
    }

    dfServiceMesh.request(
      rpc = { (host, meshBuilder) =>
        val requestBuilder = getMeshBuilder(meshBuilder, globalContext)
        client.post[PricingPropertiesRequestEnvelope, List[Wrapper[PAPIHotelPricing]]](
          endpointTemplate.host(host.host).port(host.port),
          chunkRequest.pricingRequest,
          requestBuilder,
          ClientRequestSettings.default.copy(extraMetricTags = contextHolder.commonContextTags)
        )(
          circeSerializer(PricingPropertiesRequestEnvelopeEncoder),
          toListDeserializer(PAPIHotelPricingSerializer.PAPIHotelPricingDecoder)
        )
      },
      requestSettings = RequestSettings(endpointSettings.maxNumberOfAttempts)
    )
  }

  override val circuitBreakerWrapper: CircuitBreakerWrapper = circuitBreaker
}

object DFChunkableService {

  def getOverrideEmail(emailDomain: String, rawDiscountRequest: Option[DiscountRequest]): Option[String] = {
    val shouldOverrideEmailDomain = emailDomain.nonEmpty
    val existingEmail             = rawDiscountRequest.flatMap(_.email)
    if (shouldOverrideEmailDomain && existingEmail.isEmpty) {
      Some(emailDomain)
    } else {
      None
    }
  }

  def getOverrideDFRequest(
      request: PricingPropertiesRequestEnvelope,
      capiRequestContext: Option[String],
      emailDomain: String,
      channels: Option[CalculateChannelResult],
      externalUserContext: Option[String] = None,
      hasHourlyRates: Boolean
  ): OverridePricingRequest = {

    val overridePricingRequest = request.overridePricingRequest
      .getOrElse(OverridePricingRequest(None))
      .copy(
        userContext = capiRequestContext,
        emailDomain = getOverrideEmail(emailDomain, request.pricingRequestParameters.t.pricing.discountRequest),
        ratePlans = channels.map(_.channelIds),
        cheapestOnly = Some(true),
        externalUserContext = externalUserContext,
        hasHourlyRates = Some(hasHourlyRates)
      )
    overridePricingRequest
  }

}

/** class to enrich channel ids for properties in DF Specifically temporary purpose
  */
//TODO: Remove this logic. Done to support production live dates. PLTAPI-290 Should be removed ASAP.
class DFChunkableService(
    client: ChunkableClientFuture[PricingPropertiesRequestWrapper, List[Wrapper[PAPIHotelPricing]]],
    settings: ChunkableWrapperSettings,
    channelService: DFChannelEnrichService,
    capiService: CAPIService,
    propertyMetaService: PropertyMetaService,
    tracer: Tracer,
    component: Component
)(implicit ME: MonadError[Future, Throwable], ec: ExecutionContext)
    extends ChunkableServiceFuture[PricingPropertiesRequestWrapper, List[Wrapper[PAPIHotelPricing]]](
      client,
      settings,
      tracer,
      component
    ) {

  import DFChunkableService._

  override def process(
      request: PricingPropertiesRequestWrapper,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit contextHolder: ContextHolder): Future[List[Wrapper[PAPIHotelPricing]]] = {
    val pricingRequest = request.pricingRequest
    val finalRequestF = for {
      productHotelMapping <- propertyMetaService.getProductHotelsByHotel(pricingRequest.propertyIds)(
        new WithCorrelationId {
          override def getCorrelationId: String = globalContext.getCorrelationId.getOrElse(DEFAULT_PAPI_SEARCH_ID)
        },
        contextHolder
      )
      hasHourlyRates = productHotelMapping.values.exists(_.hasHourlyRate)
      channelMap <- channelService.enrichedChannelRequest(
        pricingRequest,
        globalContext,
        request.enableOpaqueChannel,
        request.isEnabledPartnerChannelSelection,
        request.isPackageDeal,
        productHotelMapping,
        experimentContext
      )
    } yield channelMap.map { channelToPropertyId =>
      val propertyIdsPerChannelList = channelToPropertyId._2
      val channels                  = channelToPropertyId._1
      val overridePricingRequest: OverridePricingRequest = getOverrideDFRequest(
        pricingRequest,
        request.capiRequestContext,
        request.capiEmailDomain,
        Some(channels),
        request.externalUserContext,
        hasHourlyRates
      )

      request.copy(pricingRequest =
        pricingRequest.copy(
          propertyIds = propertyIdsPerChannelList,
          overridePricingRequest = Some(overridePricingRequest)
        )
      )
    }.toList

    val requestFinal = finalRequestF.recover { case _: Exception =>
      List(request)
    }

    requestFinal.flatMap { requestGroup =>
      Future
        .sequence(requestGroup.map(request => super.process(request, globalContext, experimentContext)))
        .map(_.flatten)
    }
  }

}
