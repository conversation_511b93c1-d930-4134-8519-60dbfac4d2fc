package com.agoda.papi.search.common.externaldependency.content

import com.agoda.common.graphql.model.Wrapper
import com.agoda.papi.content.graphql.ContentGroupTopicsRequestEnvelope
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext }
import com.agoda.platform.service.context.GlobalContext
import com.typesafe.scalalogging.LazyLogging

import scala.concurrent.Future

trait TopicsService {

  def getTopics(
      contentGroupTopicsRequestEnvelope: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]]

}

class TopicsServiceImpl(topicsExternalService: TopicsExternalService) extends TopicsService with LazyLogging {

  override def getTopics(
      contentGroupTopicsRequestEnvelope: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit contextHolder: ContextHolder): Future[List[Wrapper[ContentWithPropertyId]]] =
    topicsExternalService.process(contentGroupTopicsRequestEnvelope, globalContext, experimentContext)

}
