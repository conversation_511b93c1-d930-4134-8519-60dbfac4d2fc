package com.agoda.papi.search.common.binding

import cats.effect.{ ContextShift, IO }
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.dynamic.state.consul.{ ConsulStateWatcher, Environment }
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.observability.api.configuration.tracing.FeatureTracingConfiguration
import com.agoda.commons.observability.api.tracing.FeatureTracer
import com.agoda.commons.observability.internal.tracing.TraceStateExtraction
import com.agoda.commons.observability.sdk.AgOpenTelemetry
import com.agoda.commons.sql.hikari.HikariDataSourcePool
import com.agoda.commons.sql.hikari.load.balancing.{ DataSourcePoolLoadBalancer, HikariDataSourceResource }
import com.agoda.commons.sql.{ Sql, SqlConnection }
import com.agoda.commons.sql.settings.SqlSettingsBuilder
import com.agoda.commons.sql.settings.MetricsSettings.ReportSettings
import com.agoda.papi.search.common.externaldependency.vault.VaultAuthenticationSettings
import com.agoda.papi.search.common.model.KillSwitchConfig
import com.agoda.papi.search.common.service.RocketmilesRankingExperimentService
import com.agoda.papi.search.common.service.fs.{ CacheFeatureStoreService, FeatureStoreService }
import com.agoda.papi.search.common.util.circuitbreaker.{ CircuitBreakerWrapper, ExternalServiceCircuitBreakerSettings }
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.typesafe.config.{ Config, ConfigFactory }
import scaldi.Module
import io.opentelemetry.api.OpenTelemetry
import io.opentelemetry.opentracingshim.OpenTracingShim
import io.opentracing.Tracer

import scala.concurrent.ExecutionContext

class CommonModule extends Module {
  private val config          = ConfigFactory.load()
  private val agOpenTelemetry = AgOpenTelemetry.of(config).registerGlobal()

  // Global OpenTelemetry can only be set once.
  // If any library uses GlobalOpenTelemetry.get() it would set the NOP instance.
  // Run this non lazy to get this bootstrapping as early in the flow to ensure we're configuring it first.
  bind[AgOpenTelemetry].toNonLazy(agOpenTelemetry)
  bind[FeatureTracer].toNonLazy(FeatureTracer.of(config))
  bind[TraceStateExtraction].toNonLazy(new TraceStateExtraction(FeatureTracingConfiguration.of(config)))
  bind[io.opentelemetry.api.trace.Tracer] to agOpenTelemetry.getTracer("propertyapisearchdocker")

  bind[io.opentracing.Tracer].toNonLazy {
    val otel = inject[OpenTelemetry]
    OpenTracingShim.createTracerShim(otel)
  }

  private val vaultCredentialsOpt: Option[VaultAuthenticationSettings] =
    if (config.getBoolean("ag-vault.enable")) Some(VaultAuthenticationSettings(config)) else None
  bind[Option[VaultAuthenticationSettings]] to vaultCredentialsOpt

  implicit private val dbIOContextShift: ContextShift[IO] = IO.contextShift(ExecutionContexts.ioContext)

  bind[HikariDataSourcePool] to HikariDataSourcePool
    .builder("cdb", config)
    .withOpenTelemetry(inject[AgOpenTelemetry])
    .build()

  bind[DataSourcePoolLoadBalancer[HikariDataSourceResource]] to new DataSourcePoolLoadBalancer[
    HikariDataSourceResource
  ](
    inject[HikariDataSourcePool].getPoolName,
    inject[HikariDataSourcePool].getDatasourcesState,
    DataSourcePoolLoadBalancer.DEFAULT_WEIGHT_STRATEGY
  )

  bind[IO[Sql[IO, SqlConnection[IO]]]] to {

    val sqlSettings = SqlSettingsBuilder("cdb")
      .withConfig(config)
      .withOpenTelemetry(inject[OpenTelemetry])
      .withDataSourcePool(inject[HikariDataSourcePool])
      .withReportSettings(
        ReportSettings(
          totalExecutionTime = true,
          perCallExecutionTime = true,
          perCallProcessingTime = false
        )
      )
      .build()

    Sql[IO](sqlSettings)
  }

  bind[ConsulStateWatcher] to ConsulStateWatcher.builder().build()

  bind[State[Config]] to State.of(
    ConfigFactory.empty(),
    inject[ConsulStateWatcher]
      .watchAppKv(
        "it-propertyapi-search",
        Environment.builder().withCustomEnvironment(System.getenv("APC_CLUSTER_NAME")).build()
      )
  )

  bind[ExternalServiceCircuitBreakerSettings] to ExternalServiceCircuitBreakerSettings.fromConfigAndConsul(
    config,
    inject[State[Config]]
  )

  bind[State[KillSwitchConfig]] to inject[State[Config]]
    .map { c =>
      KillSwitchConfig.from(c)
    }

  bind[RocketmilesRankingExperimentService] to injected[RocketmilesRankingExperimentService]

  bind[FeatureStoreService] to CacheFeatureStoreService(
    "papi-feature-Store",
    10000,
    inject[MetricsReporter],
    inject[Tracer],
    ExecutionContext.global,
    inject[CircuitBreakerWrapper](identified by 'FsCircuitBreaker)
  )

}
