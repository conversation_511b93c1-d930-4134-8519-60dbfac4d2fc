package com.agoda.papi.search.common.util

import api.request.FeatureFlag
import com.agoda.common.graphql.model.Wrapper
import com.agoda.content.models.request.sf.{ ContentSummaryRequest, OverrideContentRequest }
import com.agoda.core.search.models.enumeration.SearchStatuses
import com.agoda.core.search.models.request._
import com.agoda.core.search.models.response.{ AggregationInput, SearchResult }
import com.agoda.core.search.models.{ request, CancellationType, CancellationTypes, PropertyData }
import com.agoda.papi.content.graphql.ContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.enums.hotel.StayPackageTypes
import com.agoda.papi.enums.room.CancellationGroup
import com.agoda.papi.pricing.graphql.{ PricingPropertiesRequestEnvelope => DFPricingPropertiesRequestEnvelope }
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextHolder }
import com.agoda.papi.search.internalmodel.pricing.{ PAPIDFCurrencyPrice, PAPIHotelPricing, PAPIRoomOffer }
import com.agoda.papi.search.internalmodel.request._
import com.agoda.papi.search.internalmodel.{
  AlternativeDatePricingOverride,
  PricingRequestContainer,
  PropertySearchRequestWrapper,
  PropertySummaryEnvelope
}
import com.agoda.papi.search.types.{ HotelIdType, PropertyId, PropertySummaryEnvelopeList, WithHotelIdMap }
import com.agoda.platform.service.context.GlobalContext
import com.agoda.pricing.models.circe.Encoders._
import io.circe.syntax._
import models.pricing.enums.{ ApplyType, ApplyTypes, PaymentModels, RequestedPrice }
import models.starfruit.{ Experiment, OverridePricingRequest }
import models.{ DFSuggestPriceType, HotelPricing }
import org.joda.time.{ DateTime, Days }

import scala.reflect.runtime.universe._

/** <AUTHOR>   mkhan
  */
object ModelHelper {

  /** Content Summary Request is without propertyIds Method to add propertyIds in the ContentSummaryRequest Wrapper to
    * get response from content
    *
    * @param propertyIds
    * @param wrapper
    * @return
    *   Content request wrapper with propertyIds
    */
  def toContentPropertiesSummaryRequestWrapper(
      propertyIds: List[PropertyId],
      wrapper: Wrapper[ContentSummaryRequest],
      overrideContentRequest: Option[OverrideContentRequest]
  ): ContentPropertiesSummaryRequestEnvelope =
    ContentPropertiesSummaryRequestEnvelope(propertyIds, wrapper, overrideContentRequest)

  /** Pricing Request Parameters is without propertyIds Method to add propertyIds in the PricingRequestParameters
    * Wrapper to get response from pricing
    *
    * @param propertyIds
    * @param pricingRequestWrapper
    * @param overridePricingRequest
    *   pricing request won't be serialize in PAPI. this field will be used to mutate pricing request.
    * @param globalContext
    * @param searchRequest
    * @return
    *   Pricing request wrapper with propertyIds
    */
  def toPricingPropertiesRequestEnvelope(
      propertyIds: List[PropertyId],
      pricingRequestWrapper: Wrapper[models.starfruit.PricingRequestParameters],
      overridePricingRequest: Option[OverridePricingRequest],
      globalContext: GlobalContext,
      searchRequest: BaseRequest[ComplexSearchRequest]
  )(implicit searchContextHolder: ContextHolder): DFPricingPropertiesRequestEnvelope = {
    val originalCid = globalContext.getCid
    val fallbackCid = pricingRequestWrapper.t.context.clientInfo.cid
    val isForceBVariant =
      isPricingForceBVariant(ActiveABTests().isThaiGovCampaign, pricingRequestWrapper.t.context.experiment)
    val isPackagingRequest = pricingRequestWrapper.t.context.packaging.nonEmpty || searchRequest.isPackagingSearch
    val bltCid = overrideCidForThaiGovCampaign(Some(fallbackCid), globalContext, isForceBVariant, isPackagingRequest)

    val overridePricingRequestWithCid: Option[OverridePricingRequest] = if (bltCid != originalCid) {
      overridePricingRequest
        .map(_.copy(cid = bltCid))
        .orElse(Some(OverridePricingRequest(filterAPO = None, cid = bltCid)))
    } else {
      overridePricingRequest match {
        case Some(_) => overridePricingRequest
        case None    => None
      }
    }

    DFPricingPropertiesRequestEnvelope(
      propertyIds,
      pricingRequestWrapper,
      overridePricingRequestWithCid
    )
  }

  def setIsApoRequest(propertySearchRequestWrapper: PropertySearchRequestWrapper): PropertySearchRequestWrapper = {
    val originalOverridePricing: OverridePricingRequest =
      propertySearchRequestWrapper.overridePricingRequest.getOrElse(new OverridePricingRequest(filterAPO = None))
    val newOverridePricingRequest = originalOverridePricing.copy(filterAPO = Some(true))

    val originalOverrideContent: OverrideContentRequest =
      propertySearchRequestWrapper.overrideContentRequest.getOrElse(new OverrideContentRequest(apo = None))
    val newOverrideContentRequest = originalOverrideContent.copy(apo = Some(true))
    propertySearchRequestWrapper.copy(
      overrideContentRequest = Some(newOverrideContentRequest),
      overridePricingRequest = Some(newOverridePricingRequest)
    )
  }

  def overrideYesterdaySearchAndUserContext(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult
  ): PropertySearchRequestWrapper = {
    val propertySearchRequestWrapperTmp = searchResult.searchInfo.searchStatus
      .map { status =>
        status.searchStatus match {
          case SearchStatuses.CheckInChanged =>
            val basePrcingRequest = propertySearchRequestWrapper.pricingRequestWrapper.map(_.t.pricing)
            val newCheckIn        = status.searchCriteria.checkIn
            val los = basePrcingRequest.map(req => Days.daysBetween(req.checkIn, req.checkout).getDays).getOrElse(0)
            val overrideRequest =
              overridePricingCheckInAndOut(propertySearchRequestWrapper.overridePricingRequest, newCheckIn, los)
            propertySearchRequestWrapper.copy(overridePricingRequest = Some(overrideRequest.pricingOverride))
          case _ => propertySearchRequestWrapper
        }
      }
      .getOrElse(propertySearchRequestWrapper)

    propertySearchRequestWrapperTmp.copy(
      capiRequestContext = searchResult.capiRequestContext,
      externalUserContext = searchResult.externalUserContext,
      externalUserProfileInfoContext = searchResult.externalUserProfileInfoContext
    )
  }

  def overridePricingCheckInAndOut(
      overridePricingRequest: Option[OverridePricingRequest],
      newCheckIn: DateTime,
      los: Int
  ): AlternativeDatePricingOverride = {
    val newCheckOut = newCheckIn.plusDays(los)
    AlternativeDatePricingOverride(
      newCheckIn,
      newCheckOut,
      overridePricingRequest
        .getOrElse(OverridePricingRequest(None))
        .copy(
          checkInDate = Some(newCheckIn),
          checkOutDate = Some(newCheckOut)
        )
    )
  }

  def updatePropertySearchRequestWrapper(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      searchResult: SearchResult,
      enableOverrideRequiredBasis: Boolean
  )(implicit searchContextHolder: ContextHolder): PropertySearchRequestWrapper = {
    val yesterDaySearchOverridden =
      ModelHelper.overrideYesterdaySearchAndUserContext(propertySearchRequestWrapper, searchResult)
    val flexibleOverridden = ModelHelper.overrideDfRequestForFlexibleRoomSearch(
      yesterDaySearchOverridden
    )
    val requiredBasisOverridden = ModelHelper.overrideDfRequestForRequiredBasis(
      flexibleOverridden,
      enableOverrideRequiredBasis
    )
    ModelHelper.filterReturnHotelNotReadyIfPullNotReadyFlag(requiredBasisOverridden)
  }

  /** Create Context holder for core search request
    *
    * @param request
    * @tparam IN
    * @return
    */
  // TODO: clean up this
  def toContextHolderWithSearchRequest[IN <: BaseSearchRequest: TypeTag](
      request: IN,
      searchContextHolder: ContextHolder
  ): ContextHolder = {
    implicit val experimentContext = searchContextHolder.experimentContext
    // TODO: Need to add whitelabelservice id

    searchContextHolder.copy(
      experimentContext = experimentContext,
      searchContext = Some(request.searchContext),
      isGql = true
    )
  }

  def toPackagingContext[IN <: BaseSearchRequest: TypeTag](
      searchRequest: IN,
      flightPrice: Option[Double]
  ): Option[PackagingContext] =
    if (searchRequest.isPackagingSearch) {
      Some(PackagingContext(flightPrice))
    } else None

  def toCoreSearchAggrgeationRequest(
      searchRequest: BaseRequest[ComplexSearchRequest],
      filteredCityIds: List[Long],
      aggregationInput: AggregationInput,
      matrixHotelInput: MatrixHotelInput,
      allProperties: WithHotelIdMap[PropertyData]
  ): AggregationRequest = {
    val baseMatrixRequest = BaseMatrixRequest(
      aggregationInput.cityIds.toArray,
      filteredCityIds,
      searchRequest.searchCriteria.getItineraryCriteria.flatMap(_.childAges.map(_.flatten)),
      aggregationInput.hasInsiderDeal,
      aggregationInput.cityIdPopularFilter,
      aggregationInput.requestedCityIds,
      aggregationInput.isFullMatrixItem
    )

    AggregationRequest(
      searchRequest.searchCriteria,
      searchRequest.searchContext,
      searchRequest.packaging,
      searchRequest.searchRequest.filterRequest,
      searchRequest.searchRequest.matrixRequest,
      baseMatrixRequest,
      matrixHotelInput,
      allProperties
    )
  }

  def toCoreSearchPostPagingAggrgeationRequest(
      searchRequest: BaseRequest[ComplexSearchRequest],
      filteredCityIds: List[Long],
      aggregationInput: AggregationInput,
      postPagingMatrixHotelInput: PostPagingMatrixHotelInput,
      allProperties: WithHotelIdMap[PropertyData]
  ): PostPagingAggregationRequest = {
    val baseMatrixRequest = BaseMatrixRequest(
      aggregationInput.cityIds.toArray,
      filteredCityIds,
      searchRequest.searchCriteria.getItineraryCriteria.flatMap(_.childAges.map(_.flatten)),
      aggregationInput.hasInsiderDeal,
      aggregationInput.cityIdPopularFilter,
      aggregationInput.requestedCityIds,
      aggregationInput.isFullMatrixItem
    )

    PostPagingAggregationRequest(
      searchRequest.searchCriteria,
      searchRequest.searchContext,
      searchRequest.packaging,
      searchRequest.searchRequest.filterRequest,
      searchRequest.searchRequest.matrixRequest,
      baseMatrixRequest,
      postPagingMatrixHotelInput,
      allProperties
    )
  }

  private def toChildRoomInfoSummary(cheapestRoomOffer: PAPIRoomOffer, currency: String): ChildRoomInfoSummary = {
    val roomPricing = cheapestRoomOffer.pricing.find(cp => cp.currency == currency)
    val roomCapacity = Capacity(
      cheapestRoomOffer.capacity.perOfferMaxFreeChildren.getOrElse(0),
      cheapestRoomOffer.capacity.perOfferFreeChildren.getOrElse(0)
    )
    request.ChildRoomInfoSummary(
      cheapestRoomOffer.typeId,
      cheapestRoomOffer.uid,
      cheapestRoomOffer.channel.id.toInt,
      roomPricing.map(toPriceInfoSummary(_)),
      Some(roomCapacity)
    )
  }

  private def toPriceInfoSummary(dfCurrencyPrice: PAPIDFCurrencyPrice): PriceInfoSummary = {
    val display = dfCurrencyPrice.price.display
    PriceInfoSummary(
      perBook = RoomPrice(display.perBook.exclusive, display.perBook.allInclusive),
      perRoomPerNight = RoomPrice(display.perRoomPerNight.exclusive, display.perRoomPerNight.allInclusive),
      totalDiscountPercent = dfCurrencyPrice.price.totalDiscount
    )
  }

  def toCoreSearchCancellation(cancellationGroup: CancellationGroup): CancellationType = {
    val cancellationType = CancellationTypes.fields.get(cancellationGroup.entryName)
    cancellationType.getOrElse(CancellationTypes.None)
  }

  def toPropertyInformation(
      property: PropertySummaryEnvelope,
      isAPO: Option[Boolean],
      requestedCurrency: String,
      isRecommended: Boolean
  ): PropertyInformation = {

    val pricing = property.pricing.map(_.t)
    val content = property.content
    val cheapestRoomOffer: Option[PAPIRoomOffer] =
      pricing.flatMap(p => p.cheapestBundle.flatMap(b => b.roomOffers.headOption.map(_.room)))
    val childRooms = cheapestRoomOffer
      .map(cro => if (!cro.isSuggested) Seq(toChildRoomInfoSummary(cro, requestedCurrency)) else Seq.empty)
      .getOrElse(Seq.empty)
    val suggestedRoom = cheapestRoomOffer
      .map(cro => if (cro.isSuggested) Seq(toChildRoomInfoSummary(cro, requestedCurrency)) else Seq.empty)
      .getOrElse(Seq.empty)
    val channels   = pricing.flatMap(_.hotelAggregatedOptions.map(_.channelIds)).getOrElse(Set.empty)
    val benefitIds = pricing.flatMap(_.hotelAggregatedOptions.map(_.benefits.map(_.id))).getOrElse(Seq.empty)

    // (2021-07-01) PropertySummaryEnvelop.pricing.payment is already the aggregated payment option (the best payment option) calculated by DF
    val payment                    = pricing.flatMap(_.payment)
    val externalLoyaltyDisplayData = pricing.flatMap(p => p.externalLoyaltyDisplay.map(_.items)).getOrElse(Seq.empty)
    val collectionTierIds          = externalLoyaltyDisplayData.map(_.tierId)

    val paymentInfoSummary =
      PaymentInfoSummary(
        taxReceipt = payment.map(_.taxReceipt.isEligible),
        payAtHotel = payment.map(_.payAtHotel.isEligible),
        payAtCheckin = None,
        payLater = payment.map(_.payLater.isEligible),
        noCC = payment.map(_.noCreditCard.isEligible),
        cancellationType = payment.map(dfPayment => toCoreSearchCancellation(dfPayment.cancellation.cancellationType)),
        payNowEnable = pricing.map(
          _.roomLevelPayLaterData
            .getOrElse(Seq.empty)
            .map(_.paymentModel)
            .exists(payment => payment == PaymentModels.Merchant | payment == PaymentModels.MerchantCommission)
        )
      )

    val hasASORate = property.pricing.flatMap(
      _.t.cheapestStayPackageRatePlans.map(_.exists(_.stayPackageType == StayPackageTypes.Escapes.i))
    )
    val hasPulsePromo = PulseHelper.hasPulsePromotion(property)
    request.PropertyInformation(
      id = property.propertyId,
      isMSE = cheapestRoomOffer.exists(_.isMseProperty),
      isReady = pricing.exists(_.isReady),
      // APS not used for SSR
      enableAPS = false,
      isRecommendedProperty = isRecommended,
      // Suggested Price needs to be moved to DF
      suggestedPrice = pricing.map(p => toRequestedPrice(p.suggestPriceType.suggestPrice)),
      availableChannels = channels,
      availableBenefits = benefitIds,
      payment = paymentInfoSummary,
      contentMasterRoomIds = content.t.roomInformation.map(_.roomIds).getOrElse(Set.empty),
      contentCumulativeReviewScore = content.t.reviews.flatMap(_.cumulative.map(_.score)),
      availableMasterRoomIds = pricing.map(_.availableRoomIds).getOrElse(Set.empty),
      cheapestRoomId = cheapestRoomOffer.map(_.uid),
      childRooms = childRooms,
      suggestedRooms = suggestedRoom,
      isAPO = isAPO,
      // Multiroom Suggestion field. Feature not being used
      multiTypeRoomSuggestionSummaries = Seq.empty,
      hasASORate,
      hasPulsePromo,
      collectionTierIds,
      cheapestRoomHasAutoApplyWalletPromo = cheapestRoomOffer.exists(_.isConsolidatedAppliedEligible)
    )
  }

  def toNotAvailableHotelPricingWrapper(hotelId: Long): Wrapper[PAPIHotelPricing] = {
    val papiHotelPricing = PAPIHotelPricing(
      hotelId = hotelId,
      isReady = true,
      isAvailable = false,
      cheapestRoomOffer = None,
      suppliersSummary = None,
      suppliersSummaries = Nil,
      supplierInfo = None,
      suggestedRoomQuantity = 0,
      suggestPriceType = DFSuggestPriceType(RequestedPrice.AllInclusive, ApplyTypes.PB)
    )
    val dfHotelPricing = HotelPricing(
      hotelId = hotelId,
      isReady = true,
      isAvailable = false,
      pointmax = None,
      cheapestRoomOffer = None,
      roomBundle = None,
      suppliersSummary = None,
      suppliersSummaries = Nil,
      supplierInfo = None,
      suggestedRoomQuantity = 0,
      suggestPriceType = DFSuggestPriceType(RequestedPrice.AllInclusive, ApplyTypes.PB),
      isSuggested = false,
      isMultiHotelEligible = false,
      isPackageEligible = false,
      isCartEligible = false
    )
    Wrapper(papiHotelPricing, dfHotelPricing.asJson)
  }

  def toPropertyWithOptionalPricing(property: PropertySummaryEnvelope): PropertySummaryEnvelope =
    property.pricing match {
      case Some(_) => property
      case None =>
        property.copy(pricing =
          property.pricing.map(Some(_)).getOrElse(Some(toNotAvailableHotelPricingWrapper(property.propertyId)))
        )
    }

  def toRequestedPrice(
      requestedPrice: models.pricing.enums.RequestedPrice
  ): com.agoda.papi.enums.request.RequestedPrice =
    com.agoda.papi.enums.request.RequestedPrice.getRequestedPrice(requestedPrice.i)

  def toCorePostFilterRequest(
      searchRequest: BaseRequest[ComplexSearchRequest],
      properties: PropertySummaryEnvelopeList,
      allProperties: WithHotelIdMap[PropertyData],
      recommendedProperties: Set[HotelIdType],
      requestedCurrency: String
  ): PostFilterRequest = {
    val propertyInformation = properties.map(kv =>
      toPropertyInformation(
        kv,
        allProperties.get(kv.propertyId).map(_.availability.exists(_.isAPO)),
        requestedCurrency,
        recommendedProperties.contains(kv.propertyId)
      )
    )

    PostFilterRequest(
      searchRequest.searchCriteria,
      searchRequest.searchContext,
      searchRequest.searchRequest.packaging,
      searchRequest.searchRequest.filterRequest,
      propertyInformation
    )
  }

  def toPricingRequestContainer(propertySearchRequestWrapper: PropertySearchRequestWrapper): PricingRequestContainer =
    PricingRequestContainer(
      enabledOpaqueChannel = propertySearchRequestWrapper.searchRequest.searchCriteria.enableOpaqueChannel,
      propertySearchRequestWrapper.searchRequest.searchCriteria.isEnabledPartnerChannelSelection
    )

  def isPAPIForceBVariant(experimentName: String, forcedExperiments: Option[Vector[ForcedExperiment]]): Boolean =
    forcedExperiments.exists(_.exists(f => f.id == experimentName && f.variant == "B"))

  def isPricingForceBVariant(experimentName: String, forcedExperiments: List[Experiment]): Boolean =
    forcedExperiments.exists(f => f.name == experimentName && f.variant == 'B')

  // Mimic hack from Mobile APP client to clay. Kick to default MAPI cid to get normal Agoda Sponsor.
  // Will be remove after 31 Oct 2020
  private val mapiSiteId = 1605717
  private val bltCid     = 1881563

  def overrideCidForThaiGovCampaign(
      fallbackCid: Option[Int],
      globalContext: GlobalContext,
      isThaiGovExperiment: Boolean,
      isPackagingRequest: Boolean
  ): Option[Int] = {
    val originalCid    = globalContext.getCid.orElse(fallbackCid)
    val originalOrigin = globalContext.getOrigin

    if (
      originalCid.contains(bltCid) && (originalOrigin.exists(
        _.compareToIgnoreCase("TH") != 0
      ) || !isThaiGovExperiment || isPackagingRequest)
    ) {
      Some(mapiSiteId)
    } else {
      originalCid
    }
  }

  def overrideDfRequestForFlexibleRoomSearch(
      propertySearchRequestWrapper: PropertySearchRequestWrapper
  ): PropertySearchRequestWrapper = {
    val addAdditionalFlexibleMultiRoomFlag =
      propertySearchRequestWrapper.searchRequest.searchCriteria.featureFlagRequest
        .flatMap(_.isFlexibleMultiRoomSearch)
        .getOrElse(false)

    if (addAdditionalFlexibleMultiRoomFlag) {
      propertySearchRequestWrapper.copy(overridePricingRequest =
        propertySearchRequestWrapper.overridePricingRequest
          .map(
            _.copy(
              additionalFeatureFlags = Some(
                FeatureFlag.FlexibleMultiRoom :: propertySearchRequestWrapper.overridePricingRequest
                  .flatMap(_.additionalFeatureFlags)
                  .getOrElse(List.empty[FeatureFlag])
              )
            )
          )
          .orElse(
            Some(
              OverridePricingRequest(
                additionalFeatureFlags = Some(List(FeatureFlag.FlexibleMultiRoom)),
                filterAPO = None
              )
            )
          )
      )
    } else {
      propertySearchRequestWrapper
    }
  }

  def overrideDfRequestForRequiredBasis(
      propertySearchRequestWrapper: PropertySearchRequestWrapper,
      enableOverrideRequiredBasis: Boolean
  ): PropertySearchRequestWrapper = if (
    propertySearchRequestWrapper.pricingRequestWrapper
      .flatMap(_.t.pricing.requiredBasis)
      .isEmpty && enableOverrideRequiredBasis
  ) {
    val requiredBasis: Option[ApplyType] =
      propertySearchRequestWrapper.searchRequest.searchCriteria.requiredBasis.flatMap(r =>
        ApplyTypes.fields.get(r.entryName)
      )

    propertySearchRequestWrapper.copy(overridePricingRequest =
      propertySearchRequestWrapper.overridePricingRequest
        .map(_.copy(requiredBasis = requiredBasis))
        .orElse(
          Some(
            OverridePricingRequest(
              filterAPO = None,
              requiredBasis = requiredBasis
            )
          )
        )
    )
  } else propertySearchRequestWrapper

  def filterReturnHotelNotReadyIfPullNotReadyFlag(
      propertySearchRequestWrapper: PropertySearchRequestWrapper
  )(implicit searchContextHolder: ContextHolder): PropertySearchRequestWrapper = {
    val shouldFilterFlag = searchContextHolder.experimentContext.isBVariant(
      searchContextHolder.allocationContext.ignoreReturnHotelNotReadyIfPullNotReady
    )

    if (shouldFilterFlag) {
      val updatedPricingRequestWrapper = propertySearchRequestWrapper.pricingRequestWrapper.map { wrapper =>
        val originalFeatureFlags = wrapper.t.pricing.featureFlag
        val filteredFeatureFlags = originalFeatureFlags.filterNot(_ == FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
        val updatedPricing       = wrapper.t.pricing.copy(featureFlag = filteredFeatureFlags)
        val updatedPricingRequestParameters = wrapper.t.copy(pricing = updatedPricing)
        Wrapper(updatedPricingRequestParameters, updatedPricingRequestParameters.asJson)
      }

      // Filter the flag from additionalFeatureFlags in overridePricingRequest
      val updatedOverridePricingRequest = propertySearchRequestWrapper.overridePricingRequest.map { overrideRequest =>
        overrideRequest.copy(
          additionalFeatureFlags = overrideRequest.additionalFeatureFlags
            .map(_.filterNot(_ == FeatureFlag.ReturnHotelNotReadyIfPullNotReady))
            .filter(_.nonEmpty)
        )
      }

      propertySearchRequestWrapper.copy(
        pricingRequestWrapper = updatedPricingRequestWrapper,
        overridePricingRequest = updatedOverridePricingRequest
      )
    } else {
      propertySearchRequestWrapper
    }
  }

}
