package com.agoda.papi.search.common.service.fs

import com.agoda.commons.cache.settings.MetricHandlerSettings
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.feast.api.EntityType
import com.agoda.feast.proto.types.Value
import com.agoda.feast.proto.types.Value.Val
import com.agoda.feast.proto.types.Value.Val._
import com.agoda.featurestore.online.LocalCache.CachedValue
import com.agoda.papi.search.common.util.circuitbreaker.CircuitBreakerWrapper
import com.agoda.platform.cache.caffeine.MetricEnabledCaffeineCache
import com.github.benmanes.caffeine.cache.Caffeine
import com.typesafe.config.ConfigFactory
import io.opentracing.Tracer
import scalacache.Entry

import scala.concurrent.ExecutionContextExecutor

object CacheFeatureStoreService {

  def apply(
      localCacheName: String,
      localCacheSize: Int,
      reporter: <PERSON><PERSON><PERSON><PERSON>eporter,
      tracer: Tracer,
      executionContext: ExecutionContextExecutor,
      circuitBreaker: CircuitBreakerWrapper
  ): FeatureStoreService = {
    lazy val config = ConfigFactory.load()
    lazy val cache = MetricEnabledCaffeineCache[CachedValue](
      localCacheName,
      Caffeine
        .newBuilder()
        .maximumSize(localCacheSize)
        .build[String, Entry[CachedValue]](),
      reporter,
      MetricHandlerSettings(config)
    )

    FeatureStoreService(config, cache, reporter, tracer, circuitBreaker)(
      executionContext
    )
  }

}

object DefaultFeatureStoreServiceUtils {
  private val SEARCH_FILTER_PREFIX = "search-filter-"

  val FALLBACK_CITY_ID = -1

  val SECONDS_IN_90_DAYS = 60 * 60 * 24 * 90

  val MAPPED_FILTERS = Map(
    "mob-filter-area"                  -> "hotelareaid",
    "mob-filter-payment-option"        -> "paymentoptions",
    "mob-filter-review-score"          -> "reviewscore",
    "mob-filter-room-amenity"          -> "roomamenities",
    "mob-filter-facility"              -> "hotelfacilities",
    "mob-filter-bedrooms"              -> "numberofbedrooms",
    "mob-filter-vip-deals-filter-page" -> "deals",
    "mob-filter-star-rating"           -> "starrating",
    "mob-filter-accommodation-type"    -> "accommodationtype",
    "mob-filter-family"                -> "family",
    "mob-filter-host-level"            -> "hostlevel",
    "mob-filter-room-offer"            -> "roombenefits",
    "mob-filter-location-score"        -> "reviewlocationscore"
  ).map(x => x._1 -> s"$SEARCH_FILTER_PREFIX${x._2}")

  object FeatureSets {
    val FILTERS_FLOW_POPULAR_FILTERS =
      "prod_personalization_ssr_recommended_filters_array"
    val FILTERS_FLOW_POPULAR_FILTERS_V2 =
      "prod_personalization_ssr_recommended_filters"
    val UPTIER_PROPERTY_RECOMMENDATION_V1 =
      "uptier_property_recommendation_v1"
  }

  def valToAnyRef(value: Value): AnyRef = {
    val v = value.`val` match {
      case _: BoolVal       => value.getBoolVal
      case _: Int32Val      => value.getInt32Val
      case _: Int64Val      => value.getInt64Val
      case _: BytesVal      => value.getBytesVal
      case _: FloatVal      => value.getFloatVal
      case _: DoubleVal     => value.getDoubleVal
      case _: StringVal     => value.getStringVal
      case _: BoolListVal   => value.getBoolListVal.`val`
      case _: Int32ListVal  => value.getInt32ListVal.`val`
      case _: Int64ListVal  => value.getInt64ListVal.`val`
      case _: BytesListVal  => value.getBytesListVal.toByteArray
      case _: FloatListVal  => value.getFloatListVal.`val`
      case _: DoubleListVal => value.getDoubleListVal.`val`
      case _: StringListVal => value.getStringListVal.`val`
      case Val.Empty        => None
    }
    v.asInstanceOf[AnyRef]
  }

  object FeatureStoreEntities {
    case class CityIdEntity(city_id: Long)                                 extends EntityType[CityIdEntity]
    case class CityIdAndPlatformIdEntity(city_id: Long, platform_id: Long) extends EntityType[CityIdAndPlatformIdEntity]
    case class PropertyIdEntity(property_id: Long)                         extends EntityType[PropertyIdEntity]
    case class HotelIdEntity(hotel_id: Long)                               extends EntityType[HotelIdEntity]
  }

  object RoomRecoFlow {
    val TRAVELLER_TYPE_VAR = "TRAVELLERTYPE"
    val ROOM_RECO_FEATURE_SET_OLD =
      "prod_personalization_adaptive_room_recommendation_" + TRAVELLER_TYPE_VAR
    val ROOM_RECO_FEATURE_SET_NEW =
      "prod_personalization_adaptive_room_recommendation_v2_" + TRAVELLER_TYPE_VAR
    val MAX_ROOM_RECO_LIMIT_NEW_FS = 3
  }

}
