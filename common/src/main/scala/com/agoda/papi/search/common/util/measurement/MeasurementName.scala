package com.agoda.papi.search.common.util.measurement

object MeasurementName {

  final val CoreSearchPostFilter               = "coresearch.postfilter"
  final val CoreSearchAggregation              = "coresearch.aggregation"
  final val ParseQuery                         = "query.parse"
  final val ExecuteQuery                       = "query.execute"
  final val RequestDeserialize                 = "request.deserialize"
  final val AlternateDateFeature               = "search.alternatedate"
  final val PostPageSorting                    = "search.postpagesorting"
  final val CapiUserContext                    = "search.usercontext"
  final val CapiMemberDetail                   = "search.memberdetail"
  final val SearchResultCountBigCities         = "search.result.hotel.count.bigcities"
  final val SearchResultCount                  = "search.result.hotel.count"
  final val PagedPropertiesCount               = "search.paged.hotel.count"
  final val PagedPropertiesSentToDFCount       = "search.paged.hotel.to.df.count"
  final val PagedPropertiesReceivedFromDFCount = "search.paged.hotel.from.df.count"
  final val PageTokenSize                      = "search.pagetoken.size"
  final val CityAvailSPCallRowCount            = "cityavail.spcall.rowcount"
  final val SqlContext                         = "sql-context"
  final val PropertyDetailsCount               = "search.property.details.hotel.count"
  final val ConsecutiveSqlShardFailure         = "consecutiveSqlShardFailure"
  final val AvailabilityFallbackResponse       = "AvailabilityFallbackResponse"
  final val IgnoreOverrideCheckIn              = "IgnoreOverrideCheckIn"
  final val CallRateCategoryDetailService      = "callRateCategoryDetailService"

  object DataFetch {
    final val CoreSearchPreFilter           = "coresearch.prefilter"
    final val SearchAvailable               = "search.available"
    final val SearchApo                     = "search.apo"
    final val SearchExtra                   = "search.extra"
    final val GeoPlace                      = "enrichment.geoplace"
    final val Enrichment                    = "enrichment"
    final val BookingHistory                = "booking.history"
    final val SearchSoldOut                 = "search.soldout"
    final val SearchDateless                = "search.dateless"
    final val SearchAggregation             = "search.aggregation"
    final val SearchPostPagingAggregation   = "search.postpagingaggregation"
    final val SearchFeaturedAgodaHomes      = "search.featuredagodahomes"
    final val SearchHighlyRatedAgodaHomes   = "search.highlyratedagodahomes"
    final val SearchExtraAgodaHomes         = "search.extraagodahomes"
    final val SearchFeaturedLuxuryHotels    = "search.featuredluxuryhotels"
    final val SearchFeaturedPulseProperties = "search.featuredpulseproperties"
    final val EnrichSearchResult            = "enrichment.enrichsearchresult"
    final val MergeSearch                   = "search.merge"
    final val Images                        = "content.images"
    final val ReviewScore                   = "content.reviewscore"
    final val InformationSummary            = "content.summary"
    final val Experiences                   = "content.experiences"
    final val ReviewSummaries               = "content.reviewsummaries"
    final val Information                   = "content.information"
    final val LocalInformation              = "content.localnformation"
    final val HostProfile                   = "capi.hostprofile"
    final val HostProperties                = "capi.hostproperties"
    final val PropertyMetaInfo              = "pricestream.propertyMetaInfo"
    final val MetaLab                       = "pricestream.metaLab"
    final val Engagement                    = "content.engagement"
    final val Features                      = "content.features"
    final val Highlights                    = "content.highlights"
    final val Qna                           = "content.qna"
    final val Topics                        = "content.topics"
    final val RoomSummary                   = "content.roomSummary"
    final val RateCategory                  = "content.rateCategory"

    final val CreditCardCampaignInfo    = "creditCardCampaignInfo"
    final val PointMaxDescription       = "pointMaxDescription"
    final val SuppliersInformation      = "suppliersInformation"
    final val BenefitInformation        = "benefitInformation"
    final val IsConnectedTripApplicable = "searchenrichment.isConnectedTripApplicable"
    final val IsPopular                 = "isPopular"
    final val PackagingDetails          = "packagingDetails"
    final val HotelRoomInformation      = "hotelRoomInformation"
    final val UpdateSearchResult        = "updateSearchResult"
    final val UpdateIsComplete          = "updateIsComplete"

  }

  object SearchType {
    final val SearchTypeTag            = "searchType"
    final val DummySearchTypeForWarmup = "warmup"
  }

  object ACM {
    final val AvailableProperties = "acm.ssr.availableproperties"
    final val TotalProperties     = "acm.ssr.totalproperties"
    final val PagedProperties     = "acm.ssr.pagedproperties"
  }

  object RequestSegment {
    final val Latency                    = "requestsegment.latency"
    final val NormalPropertiesToPageSize = "requestsegment.normaltopagesize"
    final val PagingIteration            = "requestsegment.paging-iteration"
    final val HeuristicLogicPrecision    = "requestsegment.heuristic-logic-precision"
  }

  object HeuristicAccuracyChecker {
    final val FilterAccuracyFalseNegative       = "filter-accuracy.false-negative"
    final val FilterAccuracyRecall              = "filter-accuracy.recall"
    final val FilterAccuracyCount               = "filter-accuracy.count"
    final val AvailabilityAccuracyFalseNegative = "availability-accuracy.false-negative"
    final val AvailabilityAccuracyRecall        = "availability-accuracy.recall"
    final val AvailabilityCount                 = "availability-accuracy.count"
  }

}
