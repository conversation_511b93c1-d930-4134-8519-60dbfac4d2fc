package com.agoda.papi.search.common.rest

import com.agoda.content.models.db.information.PropertyEngagement
import com.agoda.core.search.models.request._
import com.agoda.core.search.models.{
  CancellationPoliciesResult,
  CancellationPoliciesResultEntry,
  PolicyPhase,
  PolicyPhaseFee
}
import com.agoda.papi.enums.hotel.TaxRegistrationType
import com.agoda.upi.models.common.Arrangement
import com.agoda.upi.models.request.{ CartBaseRequest, CartItemMetadata, CartMetadata }
import io.circe.Decoder.Result
import io.circe.{ Codec, Decoder, DecodingFailure, Encoder, HCursor, Json, KeyDecoder, KeyEncoder }
import io.circe.derivation.{ deriveCodec, deriveDecoder, deriveEncoder }
import request.{
  CancellationPolicyRequest,
  CreditCardPaymentRequest,
  ForcedExperiment,
  PropertyContext,
  PropertyRequest
}
import transformers.RecommendedUpgrade

import java.text.SimpleDateFormat
import java.time.format.DateTimeFormatter
import java.util.Date
import scala.util.{ Failure, Success, Try }

object OpenApiCirceCodecs {

  import com.agoda.papi.search.internalmodel.serializer.encoder.CustomEncoders._
  import com.agoda.papi.search.internalmodel.serializer.decoder.CustomDecoders._

  private val stringDecoder = implicitly[Decoder[String]]
  val formatter             = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss.SSSXXX")

  // We have to put custom encoder/decoder of java.util.Date for v2 endpoints here
  // because we don't want to break GraphQL encoder/decoder which still "use yyyy-MM-dd" format

  implicit val JavaUtilDateEncoder: Encoder[Date] = new Encoder[Date] {
    override def apply(date: Date): Json =
      Json.fromString(formatter.format(date))
  }

  implicit val JavaUtilDateDecoder: Decoder[Date] = new Decoder[Date] {

    override def apply(c: HCursor): Result[Date] = stringDecoder.apply(c).flatMap { s =>
      Try(formatter.parse(s)) match {
        case Success(value) => Right(value)
        case Failure(_)     => Left(DecodingFailure(s"$s' is not a member of java.util.Date", c.history))
      }
    }

  }

  // import here to override encoder/decoder for java.util.Date and not using import encoder/decoder from ContentAPI
  implicit val PropertyEngagementEncoder: Encoder.AsObject[PropertyEngagement] = deriveEncoder[PropertyEngagement]
  implicit val PropertyEngagementDecoder: Decoder[PropertyEngagement]          = deriveDecoder[PropertyEngagement]

  import com.agoda.pricing.models.circe.Encoders._

  import com.agoda.content.models.circe.EncoderDecoder._
  import com.agoda.content.models.circe.Serialization.{
    PropertyEngagementDecoder => _,
    PropertyEngagementEncoder => _,
    TrafficInfoDecoder => _,
    TrafficInfoEncoder => _,
    _
  }
  import com.agoda.content.models.circe.Serialization2._

  import com.agoda.papi.search.internalmodel.serializer.encoder.EnumEncoders._
  import com.agoda.papi.search.internalmodel.serializer.decoder.EnumDecoders._

  import com.agoda.papi.search.internalmodel.serializer.decoder.RequestDecoders._

  import com.agoda.papi.search.internalmodel.serializer.encoder.ResponseEncoders._
  import com.agoda.papi.search.internalmodel.serializer.decoder.ResponseDecoders._

  implicit val cancellationRequestEntryEncoder        = deriveEncoder[CancellationRequestEntry]
  implicit val cancellationRequestEntryDecoder        = deriveDecoder[CancellationRequestEntry]
  implicit val forcedExperiment                       = deriveEncoder[ForcedExperiment]
  implicit val forcedExperimentDecoder                = deriveDecoder[ForcedExperiment]
  implicit val experimentsEncoder                     = deriveEncoder[request.Experiments]
  implicit val experimentsDecoder                     = deriveDecoder[request.Experiments]
  implicit val botEncoder                             = deriveEncoder[request.Bot]
  implicit val botDecoder                             = deriveDecoder[request.Bot]
  implicit val searchHistoryEncoder                   = deriveEncoder[SearchHistory]
  implicit val searchHistoryDecoder                   = deriveDecoder[SearchHistory]
  implicit val trackStepsEncoder                      = deriveEncoder[TrackSteps]
  implicit val trackStepsDecoder                      = deriveDecoder[TrackSteps]
  implicit val pollingInfoRequestEncoder              = deriveEncoder[PollingInfoRequest]
  implicit val pollingInfoRequestDecoder              = deriveDecoder[PollingInfoRequest]
  implicit val arrangementEncoder                     = deriveEncoder[Arrangement]
  implicit val arrangementDecoder                     = deriveDecoder[Arrangement]
  implicit val cartItemMetadataEncoder                = deriveEncoder[CartItemMetadata]
  implicit val cartItemMetadataDecoder                = deriveDecoder[CartItemMetadata]
  implicit val cartMetadata                           = deriveEncoder[CartMetadata]
  implicit val cartMetadataDecoder                    = deriveDecoder[CartMetadata]
  implicit val cartBaseRequestEncoder                 = deriveEncoder[CartBaseRequest]
  implicit val cartBaseRequestDecoder                 = deriveDecoder[CartBaseRequest]
  implicit val propertyContextEncoder                 = deriveEncoder[PropertyContext]
  implicit val propertyContextDecoder                 = deriveDecoder[PropertyContext]
  implicit val cancellationPolicyRequestEncoder       = deriveEncoder[CancellationPolicyRequest]
  implicit val cancellationPolicyRequestDecoder       = deriveDecoder[CancellationPolicyRequest]
  implicit val policyPhaseFeeEncoder                  = deriveEncoder[PolicyPhaseFee]
  implicit val policyPhaseFeeDecoder                  = deriveDecoder[PolicyPhaseFee]
  implicit val policyPhaseEncoder                     = deriveEncoder[PolicyPhase]
  implicit val policyPhaseDecoder                     = deriveDecoder[PolicyPhase]
  implicit val cancellationPoliciesResultEntryEncoder = deriveEncoder[CancellationPoliciesResultEntry]
  implicit val cancellationPoliciesResultEntryDecoder = deriveDecoder[CancellationPoliciesResultEntry]
  implicit val cancellationPoliciesResultEncoder      = deriveEncoder[CancellationPoliciesResult]
  implicit val cancellationPoliciesResultDecoder      = deriveDecoder[CancellationPoliciesResult]

  /** case class Sorting( sortField: SortField, sortOrder: SortOrder, sortParams: Option[SortParams] = None,
    * hourlySortParams: Option[HourlySortParams] = None )
    */
  implicit val geoPointEncoder           = deriveEncoder[GeoPoint]
  implicit val geoPointDecoder           = deriveDecoder[GeoPoint]
  implicit val radiusRequestEncoder      = deriveEncoder[request.RadiusRequest]
  implicit val radiusRequestDecoder      = deriveDecoder[request.RadiusRequest]
  implicit val boxRequestEncoder         = deriveEncoder[request.BoxRequest]
  implicit val boxRequestDecoder         = deriveDecoder[request.BoxRequest]
  implicit val FilterDefinitionEncoder   = deriveEncoder[FilterDefinition]
  implicit val FilterDefinitionDecoder   = deriveDecoder[FilterDefinition]
  implicit val searchCriteriaEncoder     = deriveEncoder[request.SearchCriteria]
  implicit val searchCriteriaDecoder     = deriveDecoder[request.SearchCriteria]
  implicit val featureFlagRequestEncoder = deriveEncoder[request.FeatureFlagRequest]
  implicit val featureFlagRequestDecoder = deriveDecoder[request.FeatureFlagRequest]

  /* DF Request*/
  implicit val campaignInfoEncoder = deriveEncoder[models.starfruit.CampaignInfo]
  implicit val campaignInfoDecoder = deriveDecoder[models.starfruit.CampaignInfo]

  implicit val creditCardPaymentRequestEncoder = deriveEncoder[CreditCardPaymentRequest]
  implicit val creditCardPaymentRequestDecoder = deriveDecoder[CreditCardPaymentRequest]

  implicit val paymentRequestEncoder = deriveEncoder[request.PaymentRequest]
  implicit val paymentRequestDecoder = deriveDecoder[request.PaymentRequest]

  implicit val packagingTokenEncoder = deriveEncoder[com.agoda.core.search.models.request.PackagingToken]
  implicit val packagingTokenDecoder = deriveDecoder[com.agoda.core.search.models.request.PackagingToken]

  implicit val packagingEncoder = deriveEncoder[com.agoda.core.search.models.request.Packaging]
  implicit val packagingDecoder = deriveDecoder[com.agoda.core.search.models.request.Packaging]

  implicit val PAPIImageEncoder = deriveEncoder[logger.Image]
  implicit val PAPIImageDecoder = deriveDecoder[logger.Image]

  implicit val PAPIRoomFilterCriteriaEncoder = deriveEncoder[request.RoomFilterCriteria]
  implicit val PAPIRoomFilterCriteriaDecoder = deriveDecoder[request.RoomFilterCriteria]

  // REST ONLY
  implicit val roomRequestPAPIEncoder = deriveEncoder[request.RoomRequestPAPI]
  implicit val roomRequestPAPIDecoder = deriveDecoder[request.RoomRequestPAPI]

  implicit val propertyPricingEncoder = deriveEncoder[request.PropertyPricing]
  implicit val propertyPricingDecoder = deriveDecoder[request.PropertyPricing]

  implicit val recommendationFilterEncoder = deriveEncoder[request.RecommendationFilter]
  implicit val recommendationFilterDecoder = deriveDecoder[request.RecommendationFilter]

  implicit val recommendationEncoder = deriveEncoder[request.Recommendation]
  implicit val recommendationDecoder = deriveDecoder[request.Recommendation]

  implicit val filterRequestEncoder = deriveEncoder[com.agoda.content.models.FilterRequest]
  implicit val filterRequestDecoder = deriveDecoder[com.agoda.content.models.FilterRequest]

  implicit val topSellingPointRequestEncoder = deriveEncoder[request.TopSellingPointRequest]
  implicit val topSellingPointRequestDecoder = deriveDecoder[request.TopSellingPointRequest]

  implicit val soldOutAlternateDatesRequestEncoder = deriveEncoder[request.SoldOutAlternateDatesRequest]
  implicit val soldOutAlternateDatesRequestDecoder = deriveDecoder[request.SoldOutAlternateDatesRequest]

  implicit val rateCategoryRequestPAPIEncoder = deriveEncoder[request.PAPIRateCategoryRequest]
  implicit val rateCategoryRequestPAPIDecoder = deriveDecoder[request.PAPIRateCategoryRequest]

  implicit val propertyRequestEncoder = deriveEncoder[PropertyRequest]
  implicit val propertyRequestDecoder = deriveDecoder[PropertyRequest]

  /** Property Response * */

  implicit val enrichedCheapestRoomEncoder = deriveEncoder[transformers.EnrichedCheapestRoom]
  implicit val enrichedCheapestRoomDecoder = deriveDecoder[transformers.EnrichedCheapestRoom]

  implicit val cmsFlagDataEncoder = deriveEncoder[transformers.CmsFlagData]
  implicit val cmsFlagDataDecoder = deriveDecoder[transformers.CmsFlagData]

  implicit val priceEncoder = deriveEncoder[models.starfruit.Price]
  implicit val priceDecoder = deriveDecoder[models.starfruit.Price]

  implicit val enrichedChargeBreakdownEncoder = deriveEncoder[transformers.EnrichedChargeBreakdown]
  implicit val enrichedChargeBreakdownDecoder = deriveDecoder[transformers.EnrichedChargeBreakdown]

  implicit val enrichedChannelEncoder = deriveEncoder[transformers.EnrichedChannel]
  implicit val enrichedChannelDecoder = deriveDecoder[transformers.EnrichedChannel]

  implicit val enrichedChargeEncoder = deriveEncoder[transformers.EnrichedCharge]
  implicit val enrichedChargeDecoder = deriveDecoder[transformers.EnrichedCharge]

  implicit val enrichedPackagingTokenEncoder = deriveEncoder[transformers.EnrichedPackagingToken]
  implicit val enrichedPackagingTokenDecoder = deriveDecoder[transformers.EnrichedPackagingToken]

  implicit val enrichedPackageProductDisplayPriceEncoder =
    deriveEncoder[transformers.EnrichedPackageProductDisplayPrice]
  implicit val enrichedPackageProductDisplayPriceDecoder =
    deriveDecoder[transformers.EnrichedPackageProductDisplayPrice]

  implicit val enrichedPackagingPricingEncoder = deriveEncoder[transformers.EnrichedPackagingPricing]
  implicit val enrichedPackagingPricingDecoder = deriveDecoder[transformers.EnrichedPackagingPricing]

  implicit val enrichedPackagingEncoder = deriveEncoder[transformers.EnrichedPackaging]
  implicit val enrichedPackagingDecoder = deriveDecoder[transformers.EnrichedPackaging]

  implicit val enrichedCancellationEncoder = deriveEncoder[transformers.EnrichedCancellation]
  implicit val enrichedCancellationDecoder = deriveDecoder[transformers.EnrichedCancellation]

  implicit val enrichedTaxReceiptDetailEncoder = deriveEncoder[transformers.EnrichedTaxReceiptDetail]
  implicit val enrichedTaxReceiptDetailDecoder = deriveDecoder[transformers.EnrichedTaxReceiptDetail]

  implicit val enrichedTaxReceiptEncoder = deriveEncoder[transformers.EnrichedTaxReceipt]
  implicit val enrichedTaxReceiptDecoder = deriveDecoder[transformers.EnrichedTaxReceipt]

  implicit val enrichedPayLaterEncoder = deriveEncoder[transformers.EnrichedPayLater]
  implicit val enrichedPayLaterDecoder = deriveDecoder[transformers.EnrichedPayLater]

  implicit val enrichedNoCreditCardEncoder = deriveEncoder[transformers.EnrichedNoCreditCard]
  implicit val enrichedNoCreditCardDecoder = deriveDecoder[transformers.EnrichedNoCreditCard]

  implicit val enrichedPayAtHotelEncoder = deriveEncoder[transformers.EnrichedPayAtHotel]
  implicit val enrichedPayAtHotelDecoder = deriveDecoder[transformers.EnrichedPayAtHotel]

  implicit val enrichedPayAtCheckInEncoder = deriveEncoder[transformers.EnrichedPayAtCheckIn]
  implicit val enrichedPayAtCheckInDecoder = deriveDecoder[transformers.EnrichedPayAtCheckIn]

  implicit val m150InfoEncoder = deriveEncoder[transformers.M150Info]
  implicit val m150InfoDecoder = deriveDecoder[transformers.M150Info]

  implicit val enrichedNoPrePaymentRequiredEncoder = deriveEncoder[transformers.EnrichedNoPrePaymentRequired]
  implicit val enrichedNoPrePaymentRequiredDecoder = deriveDecoder[transformers.EnrichedNoPrePaymentRequired]

  implicit val displayPriceEncoder = deriveEncoder[com.agoda.payment.model.DisplayPrice]
  implicit val displayPriceDecoder = deriveDecoder[com.agoda.payment.model.DisplayPrice]

  implicit val enrichedInstallmentPlanEncoder = deriveEncoder[com.agoda.payment.model.EnrichedInstallmentPlan]
  implicit val enrichedInstallmentPlanDecoder = deriveDecoder[com.agoda.payment.model.EnrichedInstallmentPlan]

  implicit val installmentDetailsEncoder = deriveEncoder[com.agoda.payment.model.InstallmentDetails]
  implicit val installmentDetailsDecoder = deriveDecoder[com.agoda.payment.model.InstallmentDetails]

  implicit val enrichedPaymentEncoder = deriveEncoder[transformers.EnrichedPayment]
  implicit val enrichedPaymentDecoder = deriveDecoder[transformers.EnrichedPayment]

  implicit val zenithCheapestRoomEncoder = deriveEncoder[transformers.ZenithCheapestRoom]
  implicit val zenithCheapestRoomDecoder = deriveDecoder[transformers.ZenithCheapestRoom]

  implicit val recomendationMseResultsEncoder = deriveEncoder[transformers.RecomendationMseResults]
  implicit val recomendationMseResultsDecoder = deriveDecoder[transformers.RecomendationMseResults]

  implicit val bookingHistoryEncoder = deriveEncoder[transformers.BookingHistory]
  implicit val bookingHistoryDecoder = deriveDecoder[transformers.BookingHistory]

  implicit val topSellingPointEncoder = deriveEncoder[transformers.TopSellingPoint]
  implicit val topSellingPointDecoder = deriveDecoder[transformers.TopSellingPoint]

  implicit val matchScoreEncoder = deriveEncoder[transformers.MatchScore]
  implicit val matchScoreDecoder = deriveDecoder[transformers.MatchScore]

  implicit val zenithResponseEnrichedEncoder = deriveEncoder[transformers.ZenithResponseEnriched]
  implicit val zenithResponseEnrichedDecoder = deriveDecoder[transformers.ZenithResponseEnriched]

  implicit val recommendationCorrelationEncoder = deriveEncoder[transformers.RecommendationCorrelation]
  implicit val recommendationCorrelationDecoder = deriveDecoder[transformers.RecommendationCorrelation]

  implicit val cmsDataEncoder = deriveEncoder[transformers.CmsData]
  implicit val cmsDataDecoder = deriveDecoder[transformers.CmsData]

  implicit val recommendationHeaderEncoder = deriveEncoder[transformers.RecommendationHeader]
  implicit val recommendationHeaderDecoder = deriveDecoder[transformers.RecommendationHeader]

  implicit val propertyTooltipEncoder = deriveEncoder[transformers.PropertyTooltip]
  implicit val propertyTooltipDecoder = deriveDecoder[transformers.PropertyTooltip]

  implicit val filterTagCodec: Codec.AsObject[transformers.FilterTag]     = deriveCodec[transformers.FilterTag]
  implicit val filterGroupCodec: Codec.AsObject[transformers.FilterGroup] = deriveCodec[transformers.FilterGroup]

  implicit val propertyFiltersEncoder = deriveEncoder[transformers.PropertyFilters]
  implicit val propertyFiltersDecoder = deriveDecoder[transformers.PropertyFilters]

  implicit val ratePlanMainImageEncoder = deriveEncoder[transformers.RatePlanMainImage]
  implicit val ratePlanMainImageDecoder = deriveDecoder[transformers.RatePlanMainImage]

  implicit val enrichedRatePlanEncoder = deriveEncoder[transformers.EnrichedRatePlan]
  implicit val enrichedRatePlanDecoder = deriveDecoder[transformers.EnrichedRatePlan]

  implicit val enrichedBenefitEncoder = deriveEncoder[transformers.EnrichedBenefit]
  implicit val enrichedBenefitDecoder = deriveDecoder[transformers.EnrichedBenefit]

  implicit val enrichedCapacityEncoder = deriveEncoder[transformers.EnrichedCapacity]
  implicit val enrichedCapacityDecoder = deriveDecoder[transformers.EnrichedCapacity]

  implicit val enrichedPartnersRoomAndExtraBedChargeEncoder =
    deriveEncoder[transformers.EnrichedPartnersRoomAndExtraBedCharge]
  implicit val enrichedPartnersRoomAndExtraBedChargeDecoder =
    deriveDecoder[transformers.EnrichedPartnersRoomAndExtraBedCharge]

  implicit val enrichedPartnersPriceEncoder = deriveEncoder[transformers.EnrichedPartnersPrice]
  implicit val enrichedPartnersPriceDecoder = deriveDecoder[transformers.EnrichedPartnersPrice]

  implicit val enrichedPartnersSurchargeEncoder = deriveEncoder[transformers.EnrichedPartnersSurcharge]
  implicit val enrichedPartnersSurchargeDecoder = deriveDecoder[transformers.EnrichedPartnersSurcharge]

  implicit val enrichedPartnerSurchargesEncoder = deriveEncoder[transformers.EnrichedPartnerSurcharges]
  implicit val enrichedPartnerSurchargesDecoder = deriveDecoder[transformers.EnrichedPartnerSurcharges]

  implicit val enrichedPartnerPromotionSavingsEncoder = deriveEncoder[transformers.EnrichedPartnerPromotionSavings]
  implicit val enrichedPartnerPromotionSavingsDecoder = deriveDecoder[transformers.EnrichedPartnerPromotionSavings]

  implicit val enrichedPartnerCancellationEncoder = deriveEncoder[transformers.EnrichedPartnerCancellation]
  implicit val enrichedPartnerCancellationDecoder = deriveDecoder[transformers.EnrichedPartnerCancellation]

  implicit val enrichedPartnerTaxAndFeeBreakdownEncoder =
    deriveEncoder[transformers.EnrichedPartnerTaxAndFeeBreakdown]
  implicit val enrichedPartnerTaxAndFeeBreakdownDecoder =
    deriveDecoder[transformers.EnrichedPartnerTaxAndFeeBreakdown]

  implicit val enrichedPartnerTaxAndFeeEncoder = deriveEncoder[transformers.EnrichedPartnerTaxAndFee]
  implicit val enrichedPartnerTaxAndFeeDecoder = deriveDecoder[transformers.EnrichedPartnerTaxAndFee]

  implicit val enrichedPartnersEncoder = deriveEncoder[transformers.EnrichedPartners]
  implicit val enrichedPartnersDecoder = deriveDecoder[transformers.EnrichedPartners]

  implicit val enrichedDiscountMessageEncoder = deriveEncoder[transformers.EnrichedDiscountMessage]
  implicit val enrichedDiscountMessageDecoder = deriveDecoder[transformers.EnrichedDiscountMessage]

  implicit val additionalPriceDisplayBasisEncoder = deriveEncoder[transformers.AdditionalPriceDisplayBasis]
  implicit val additionalPriceDisplayBasisDecoder = deriveDecoder[transformers.AdditionalPriceDisplayBasis]

  implicit val priceWithChannelEncoder = deriveEncoder[transformers.PriceWithChannel]
  implicit val priceWithChannelDecoder = deriveDecoder[transformers.PriceWithChannel]

  implicit val packagePriceAndSavingEncoder = deriveEncoder[transformers.PackagePriceAndSaving]
  implicit val packagePriceAndSavingDecoder = deriveDecoder[transformers.PackagePriceAndSaving]

  implicit val enrichedPricingEncoder = deriveEncoder[transformers.EnrichedPricing]
  implicit val enrichedPricingDecoder = deriveDecoder[transformers.EnrichedPricing]

  implicit val appliedPromotionEncoder = deriveEncoder[transformers.AppliedPromotion]
  implicit val appliedPromotionDecoder = deriveDecoder[transformers.AppliedPromotion]

  implicit val enrichedPromotionEncoder = deriveEncoder[transformers.EnrichedPromotion]
  implicit val enrichedPromotionDecoder = deriveDecoder[transformers.EnrichedPromotion]

  implicit val enrichedPointMaxEncoder = deriveEncoder[transformers.EnrichedPointMax]
  implicit val enrichedPointMaxDecoder = deriveDecoder[transformers.EnrichedPointMax]

  implicit val enrichedDMCPolicyTextEncoder = deriveEncoder[transformers.EnrichedDMCPolicyText]
  implicit val enrichedDMCPolicyTextDecoder = deriveDecoder[transformers.EnrichedDMCPolicyText]

  implicit val roomTooltipEncoder = deriveEncoder[transformers.RoomTooltip]
  implicit val roomTooltipDecoder = deriveDecoder[transformers.RoomTooltip]

  implicit val pointsMaxBadgeEncoder = deriveEncoder[transformers.PointsMaxBadge]
  implicit val pointsMaxBadgeDecoder = deriveDecoder[transformers.PointsMaxBadge]

  implicit val dFPromotionWrapperEncoder = deriveEncoder[transformers.DFPromotionWrapper]
  implicit val dFPromotionWrapperDecoder = deriveDecoder[transformers.DFPromotionWrapper]

  implicit val dFDownliftDataWrapperEncoder = deriveEncoder[transformers.DFDownliftDataWrapper]
  implicit val dFDownliftDataWrapperDecoder = deriveDecoder[transformers.DFDownliftDataWrapper]

  implicit val dFDailyPromotionWrapperEncoder = deriveEncoder[transformers.DFDailyPromotionWrapper]
  implicit val dFDailyPromotionWrapperDecoder = deriveDecoder[transformers.DFDailyPromotionWrapper]

  implicit val priceInfosEncoder = deriveEncoder[transformers.PriceInfos]
  implicit val priceInfosDecoder = deriveDecoder[transformers.PriceInfos]

  implicit val enrichedCampaignPromotionEncoder = deriveEncoder[transformers.EnrichedCampaignPromotion]
  implicit val enrichedCampaignPromotionDecoder = deriveDecoder[transformers.EnrichedCampaignPromotion]

  implicit val bORInfoEncoder = deriveEncoder[transformers.BORInfo]
  implicit val bORInfoDecoder = deriveDecoder[transformers.BORInfo]

  implicit val bORTooltipEncoder = deriveEncoder[transformers.BORTooltip]
  implicit val bORTooltipDecoder = deriveDecoder[transformers.BORTooltip]

  implicit val enrichedBOREncoder = deriveEncoder[transformers.EnrichedBOR]
  implicit val enrichedBORDecoder = deriveDecoder[transformers.EnrichedBOR]

  implicit val enrichedPromoCmsDataEncoder = deriveEncoder[transformers.EnrichedPromoCmsData]
  implicit val enrichedPromoCmsDataDecoder = deriveDecoder[transformers.EnrichedPromoCmsData]

  implicit val enrichedCampaignMessageEncoder = deriveEncoder[transformers.EnrichedCampaignMessage]
  implicit val enrichedCampaignMessageDecoder = deriveDecoder[transformers.EnrichedCampaignMessage]

  implicit val enrichedCampaignEncoder = deriveEncoder[transformers.EnrichedCampaign]
  implicit val enrichedCampaignDecoder = deriveDecoder[transformers.EnrichedCampaign]

  implicit val corBreakdownItemEncoder = deriveEncoder[transformers.CorBreakdownItem]
  implicit val corBreakdownItemDecoder = deriveDecoder[transformers.CorBreakdownItem]

  implicit val corBreakdownEncoder = deriveEncoder[transformers.CorBreakdown]
  implicit val corBreakdownDecoder = deriveDecoder[transformers.CorBreakdown]

  implicit val corSummaryEncoder = deriveEncoder[transformers.CorSummary]
  implicit val corSummaryDecoder = deriveDecoder[transformers.CorSummary]

  implicit val roomPricingMessageEncoder = deriveEncoder[transformers.RoomPricingMessage]
  implicit val roomPricingMessageDecoder = deriveDecoder[transformers.RoomPricingMessage]

  implicit val fencedRateEncoder = deriveEncoder[transformers.FencedRate]
  implicit val fencedRateDecoder = deriveDecoder[transformers.FencedRate]

  implicit val priceChangeEncoder = deriveEncoder[metapricing.PriceChange]
  implicit val priceChangeDecoder = deriveDecoder[metapricing.PriceChange]

  implicit val alternativeDateWithPriceEncoder = deriveEncoder[transformers.AlternativeDateWithPrice]
  implicit val alternativeDateWithPriceDecoder = deriveDecoder[transformers.AlternativeDateWithPrice]

  implicit val enrichedPastBookingInfoEncoder = deriveEncoder[transformers.EnrichedPastBookingInfo]
  implicit val enrichedPastBookingInfoDecoder = deriveDecoder[transformers.EnrichedPastBookingInfo]

  implicit val enrichedPastBookingInfoResponseEncoder = deriveEncoder[transformers.EnrichedPastBookingInfoResponse]
  implicit val enrichedPastBookingInfoResponseDecoder = deriveDecoder[transformers.EnrichedPastBookingInfoResponse]

  implicit val pricingDisplayPresentationEncoder = deriveEncoder[transformers.PricingDisplayPresentation]
  implicit val pricingDisplayPresentationDecoder = deriveDecoder[transformers.PricingDisplayPresentation]

  implicit val savingEncoder = deriveEncoder[com.agoda.upi.models.common.Saving]
  implicit val savingDecoder = deriveDecoder[com.agoda.upi.models.common.Saving]

  implicit val detailedSavingEncoder = deriveEncoder[com.agoda.upi.models.common.DetailedSaving]
  implicit val detailedSavingDecoder = deriveDecoder[com.agoda.upi.models.common.DetailedSaving]

  implicit val cartProductFlightPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.product.CartProductFlightPrice]
  implicit val cartProductFlightPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.product.CartProductFlightPrice]

  implicit val priceAndSavingEncoder = deriveEncoder[com.agoda.upi.models.common.PriceAndSaving]
  implicit val priceAndSavingDecoder = deriveDecoder[com.agoda.upi.models.common.PriceAndSaving]

  implicit val cartProductPropertyPriceEncoder =
    deriveEncoder[com.agoda.upi.models.cart.product.CartProductPropertyPrice]
  implicit val cartProductPropertyPriceDecoder =
    deriveDecoder[com.agoda.upi.models.cart.product.CartProductPropertyPrice]

  implicit val cartProductPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.product.CartProductPrice]
  implicit val cartProductPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.product.CartProductPrice]

  implicit val cartProductPassengerDisplayEncoder =
    deriveEncoder[com.agoda.upi.models.cart.product.CartProductPassengerDisplay]
  implicit val cartProductPassengerDisplayDecoder =
    deriveDecoder[com.agoda.upi.models.cart.product.CartProductPassengerDisplay]

  implicit val cartProductBundleDisplayEncoder =
    deriveEncoder[com.agoda.upi.models.cart.product.CartProductBundleDisplay]
  implicit val cartProductBundleDisplayDecoder =
    deriveDecoder[com.agoda.upi.models.cart.product.CartProductBundleDisplay]

  implicit val cartProductFlightDisplayEncoder =
    deriveEncoder[com.agoda.upi.models.cart.product.CartProductFlightDisplay]
  implicit val cartProductFlightDisplayDecoder =
    deriveDecoder[com.agoda.upi.models.cart.product.CartProductFlightDisplay]

  implicit val cartProductPropertyDisplayEncoder =
    deriveEncoder[com.agoda.upi.models.cart.product.CartProductPropertyDisplay]
  implicit val cartProductPropertyDisplayDecoder =
    deriveDecoder[com.agoda.upi.models.cart.product.CartProductPropertyDisplay]

  implicit val cartProductDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.product.CartProductDisplay]
  implicit val cartProductDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.product.CartProductDisplay]

  implicit val cartProductPricingEncoder = deriveEncoder[com.agoda.upi.models.cart.product.CartProductPricing]
  implicit val cartProductPricingDecoder = deriveDecoder[com.agoda.upi.models.cart.product.CartProductPricing]

  implicit val cartTotalPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartTotalPrice]
  implicit val cartTotalPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartTotalPrice]

  implicit val cartTotalEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartTotal]
  implicit val cartTotalDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartTotal]

  implicit val cartDifferentialPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartDifferentialPrice]
  implicit val cartDifferentialPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartDifferentialPrice]

  implicit val cartDifferentialEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartDifferential]
  implicit val cartDifferentialDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartDifferential]

  implicit val cartPassengerDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPassengerDisplay]
  implicit val cartPassengerDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPassengerDisplay]

  implicit val cartBundleDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartBundleDisplay]
  implicit val cartBundleDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartBundleDisplay]

  implicit val cartFlightDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartFlightDisplay]
  implicit val cartFlightDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartFlightDisplay]

  implicit val cartPropertyDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPropertyDisplay]
  implicit val cartPropertyDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPropertyDisplay]

  implicit val cartDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartDisplay]
  implicit val cartDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartDisplay]

  implicit val loyaltyBurnOfferEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.LoyaltyBurnOffer]
  implicit val loyaltyBurnOfferDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.LoyaltyBurnOffer]

  implicit val loyaltyEarnOfferEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.LoyaltyEarnOffer]
  implicit val loyaltyEarnOfferDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.LoyaltyEarnOffer]

  implicit val differentialLoyaltyBurnOfferEncoder =
    deriveEncoder[com.agoda.upi.models.cart.pricing.DifferentialLoyaltyBurnOffer]
  implicit val differentialLoyaltyBurnOfferDecoder =
    deriveDecoder[com.agoda.upi.models.cart.pricing.DifferentialLoyaltyBurnOffer]

  implicit val differentialLoyaltyEarnOfferEncoder =
    deriveEncoder[com.agoda.upi.models.cart.pricing.DifferentialLoyaltyEarnOffer]
  implicit val differentialLoyaltyEarnOfferDecoder =
    deriveDecoder[com.agoda.upi.models.cart.pricing.DifferentialLoyaltyEarnOffer]

  implicit val upiLoyaltyRewardUnitEncoder = deriveEncoder[com.agoda.upi.models.common.UPILoyaltyRewardUnit]
  implicit val upiLoyaltyRewardUnitDecoder = deriveDecoder[com.agoda.upi.models.common.UPILoyaltyRewardUnit]

  implicit val splitTenderContextEncoder = deriveEncoder[com.agoda.upi.models.common.SplitTenderContext]
  implicit val splitTenderContextDecoder = deriveDecoder[com.agoda.upi.models.common.SplitTenderContext]

  implicit val externalLoyaltyOfferEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.ExternalLoyaltyOffer]
  implicit val externalLoyaltyOfferDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.ExternalLoyaltyOffer]

  implicit val differentialExternalLoyaltyOfferEncoder =
    deriveEncoder[com.agoda.upi.models.cart.pricing.DifferentialExternalLoyaltyOffer]
  implicit val differentialExternalLoyaltyOfferDecoder =
    deriveDecoder[com.agoda.upi.models.cart.pricing.DifferentialExternalLoyaltyOffer]

  implicit val cartBundlePriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartBundlePrice]
  implicit val cartBundlePriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartBundlePrice]

  implicit val cartFlightPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartFlightPrice]
  implicit val cartFlightPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartFlightPrice]

  implicit val cartPropertyPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPropertyPrice]
  implicit val cartPropertyPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPropertyPrice]

  implicit val cartPriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPrice]
  implicit val cartPriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPrice]

  implicit val cartRegularDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartRegularDisplay]
  implicit val cartRegularDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartRegularDisplay]

  implicit val cartBundleChangePriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartBundleChangePrice]
  implicit val cartBundleChangePriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartBundleChangePrice]

  implicit val cartFlightChangePriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartFlightChangePrice]
  implicit val cartFlightChangePriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartFlightChangePrice]

  implicit val cartPropertyChangePriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPropertyChangePrice]
  implicit val cartPropertyChangePriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPropertyChangePrice]

  implicit val cartChangePriceEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartChangePrice]
  implicit val cartChangePriceDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartChangePrice]

  implicit val cartChangeDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartChangeDisplay]
  implicit val cartChangeDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartChangeDisplay]

  implicit val cartBasisDisplayEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartBasisDisplay]
  implicit val cartBasisDisplayDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartBasisDisplay]

  implicit val cartBundleBasisEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartBundleBasis]
  implicit val cartBundleBasisDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartBundleBasis]

  implicit val cartFlightBasisEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartFlightBasis]
  implicit val cartFlightBasisDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartFlightBasis]

  implicit val cartPropertyBasisEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPropertyBasis]
  implicit val cartPropertyBasisDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPropertyBasis]

  implicit val cartBasisEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartBasis]
  implicit val cartBasisDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartBasis]

  implicit val cartPricingEncoder = deriveEncoder[com.agoda.upi.models.cart.pricing.CartPricing]
  implicit val cartPricingDecoder = deriveDecoder[com.agoda.upi.models.cart.pricing.CartPricing]

  implicit val cartProductEncoder = deriveEncoder[com.agoda.upi.models.cart.product.CartProduct]
  implicit val cartProductDecoder = deriveDecoder[com.agoda.upi.models.cart.product.CartProduct]

  implicit val cartEncoder = deriveEncoder[com.agoda.upi.models.cart.Cart]
  implicit val cartDecoder = deriveDecoder[com.agoda.upi.models.cart.Cart]

  implicit val checkInInformationEncoder = deriveEncoder[models.starfruit.CheckInInformation]
  implicit val checkInInformationDecoder = deriveDecoder[models.starfruit.CheckInInformation]

  implicit val originalPriceEncoder = deriveEncoder[transformers.OriginalPrice]
  implicit val originalPriceDecoder = deriveDecoder[transformers.OriginalPrice]

  implicit val originalRoomDetailEncoder = deriveEncoder[transformers.OriginalRoomDetail]
  implicit val originalRoomDetailDecoder = deriveDecoder[transformers.OriginalRoomDetail]

  implicit val enrichedWalletPromotionEncoder = deriveEncoder[transformers.EnrichedWalletPromotion]
  implicit val enrichedWalletPromotionDecoder = deriveDecoder[transformers.EnrichedWalletPromotion]

  implicit val occupancyMessageEncoder = deriveEncoder[transformers.OccupancyMessage]
  implicit val occupancyMessageDecoder = deriveDecoder[transformers.OccupancyMessage]

  implicit val enrichedChildRoomEncoder = deriveEncoder[transformers.EnrichedChildRoom]
  implicit val enrichedChildRoomDecoder = deriveDecoder[transformers.EnrichedChildRoom]

  implicit val groupMseRoomEncoder = deriveEncoder[transformers.GroupMseRoom]
  implicit val groupMseRoomDecoder = deriveDecoder[transformers.GroupMseRoom]

  implicit val recommendedUpgradeEncoder: Encoder[RecommendedUpgrade] = deriveEncoder[RecommendedUpgrade]
  implicit val recommendedUpgradeDecoder: Decoder[RecommendedUpgrade] = deriveDecoder[RecommendedUpgrade]

  implicit val enrichedMasterRoomEncoder = deriveEncoder[transformers.EnrichedMasterRoom]
    .mapJson { json =>
      // rename bedConfiguration2 to bedConfiguration to be align with property endpoint v1
      CirceUtil.renameField(json, "bedConfiguration2", "bedConfiguration")
    }

  implicit val enrichedMasterRoomDecoder = deriveDecoder[transformers.EnrichedMasterRoom]

  implicit val partnerPaxOptionEncoder = deriveEncoder[models.starfruit.PartnerPaxOption]
  implicit val partnerPaxOptionDecoder = deriveDecoder[models.starfruit.PartnerPaxOption]

  implicit val enrichedPseudoCouponMessageEncoder = deriveEncoder[transformers.EnrichedPseudoCouponMessage]
  implicit val enrichedPseudoCouponMessageDecoder = deriveDecoder[transformers.EnrichedPseudoCouponMessage]

  implicit val enrichedPriceMessagingEncoder = deriveEncoder[transformers.EnrichedPriceMessaging]
  implicit val enrichedPriceMessagingDecoder = deriveDecoder[transformers.EnrichedPriceMessaging]

  implicit val enrichedBookingCancellationEncoder = deriveEncoder[transformers.EnrichedBookingCancellation]
  implicit val enrichedBookingCancellationDecoder = deriveDecoder[transformers.EnrichedBookingCancellation]

  implicit val enrichedBookingGiftCardEarningEncoder = deriveEncoder[transformers.EnrichedBookingGiftCardEarning]
  implicit val enrichedBookingGiftCardEarningDecoder = deriveDecoder[transformers.EnrichedBookingGiftCardEarning]

  implicit val enrichedBookingBenefitEncoder = deriveEncoder[transformers.EnrichedBookingBenefit]
  implicit val enrichedBookingBenefitDecoder = deriveDecoder[transformers.EnrichedBookingBenefit]

  implicit val enrichedBookingRateCategoryEncoder = deriveEncoder[transformers.EnrichedBookingRateCategory]
  implicit val enrichedBookingRateCategoryDecoder = deriveDecoder[transformers.EnrichedBookingRateCategory]

  implicit val enrichedFireDrillContractEncoder = deriveEncoder[transformers.EnrichedFireDrillContract]
  implicit val enrichedFireDrillContractDecoder = deriveDecoder[transformers.EnrichedFireDrillContract]

  implicit val enrichedBookingSellInfoEncoder = deriveEncoder[transformers.EnrichedBookingSellInfo]
  implicit val enrichedBookingSellInfoDecoder = deriveDecoder[transformers.EnrichedBookingSellInfo]

  implicit val enrichedBookingYCSPromotionEncoder = deriveEncoder[transformers.EnrichedBookingYCSPromotion]
  implicit val enrichedBookingYCSPromotionDecoder = deriveDecoder[transformers.EnrichedBookingYCSPromotion]

  implicit val bORSupplierEncoder = deriveEncoder[transformers.BORSupplier]
  implicit val bORSupplierDecoder = deriveDecoder[transformers.BORSupplier]

  implicit val enrichedBookingCashbackEarningEncoder = deriveEncoder[transformers.EnrichedBookingCashbackEarning]
  implicit val enrichedBookingCashbackEarningDecoder = deriveDecoder[transformers.EnrichedBookingCashbackEarning]

  implicit val jodaLocalDateKeyEncoder: KeyEncoder[org.joda.time.LocalDate] = (key: org.joda.time.LocalDate) =>
    key.toString
  implicit val jodaLocalDateKeyDecoder: KeyDecoder[org.joda.time.LocalDate] = (s: String) =>
    Some(org.joda.time.LocalDate.parse(s))

  implicit val enrichedBookingRoomEncoder = deriveEncoder[transformers.EnrichedBookingRoom]
  implicit val enrichedBookingRoomDecoder = deriveDecoder[transformers.EnrichedBookingRoom]

  implicit val enrichedEBEPaymentEncoder = deriveEncoder[transformers.EnrichedEBEPayment]
  implicit val enrichedEBEPaymentDecoder = deriveDecoder[transformers.EnrichedEBEPayment]

  implicit val enrichedEBECreditCardEncoder = deriveEncoder[transformers.EnrichedEBECreditCard]
  implicit val enrichedEBECreditCardDecoder = deriveDecoder[transformers.EnrichedEBECreditCard]

  implicit val enrichedCampaignDiscountEncoder = deriveEncoder[transformers.EnrichedCampaignDiscount]
  implicit val enrichedCampaignDiscountDecoder = deriveDecoder[transformers.EnrichedCampaignDiscount]

  implicit val enrichedEBEHotelEncoder = deriveEncoder[transformers.EnrichedEBEHotel]
  implicit val enrichedEBEHotelDecoder = deriveDecoder[transformers.EnrichedEBEHotel]

  implicit val enrichedEBEBookingEncoder = deriveEncoder[transformers.EnrichedEBEBooking]
  implicit val enrichedEBEBookingDecoder = deriveDecoder[transformers.EnrichedEBEBooking]

  implicit val enrichedBookingItemEncoder = deriveEncoder[transformers.EnrichedBookingItem]
  implicit val enrichedBookingItemDecoder = deriveDecoder[transformers.EnrichedBookingItem]

  implicit val enrichedSupplierSummaryEncoder = deriveEncoder[transformers.EnrichedSupplierSummary]
  implicit val enrichedSupplierSummaryDecoder = deriveDecoder[transformers.EnrichedSupplierSummary]

  implicit val propertyUspEncoder = deriveEncoder[transformers.PropertyUsp]
  implicit val propertyUspDecoder = deriveDecoder[transformers.PropertyUsp]

  implicit val propertyUspRankEncoder = deriveEncoder[transformers.PropertyUspRank]
  implicit val propertyUspRankDecoder = deriveDecoder[transformers.PropertyUspRank]

  implicit val bookingDatesEncoder = deriveEncoder[com.agoda.core.search.models.response.BookingDates]
  implicit val bookingDatesDecoder = deriveDecoder[com.agoda.core.search.models.response.BookingDates]

  implicit val reviewInfoEncoder = deriveEncoder[com.agoda.core.search.models.response.ReviewInfo]
  implicit val reviewInfoDecoder = deriveDecoder[com.agoda.core.search.models.response.ReviewInfo]

  implicit val friendsInfoTypeEncoder = deriveEncoder[com.agoda.core.search.models.response.FriendsInfoType]
  implicit val friendsInfoTypeDecoder = deriveDecoder[com.agoda.core.search.models.response.FriendsInfoType]

  implicit val symbolAndTextEncoder = deriveEncoder[transformers.SymbolAndText]
  implicit val symbolAndTextDecoder = deriveDecoder[transformers.SymbolAndText]

  implicit val beddingInfoEncoder = deriveEncoder[transformers.BeddingInfo]
  implicit val beddingInfoDecoder = deriveDecoder[transformers.BeddingInfo]

  implicit val hotelAggregationInfoEncoder = deriveEncoder[transformers.HotelAggregationInfo]
  implicit val hotelAggregationInfoDecoder = deriveDecoder[transformers.HotelAggregationInfo]

  implicit val soldOutPricingEncoder = deriveEncoder[transformers.SoldOutPricing]
  implicit val soldOutPricingDecoder = deriveDecoder[transformers.SoldOutPricing]

  implicit val soldOutRoomInfoEncoder = deriveEncoder[transformers.SoldOutRoomInfo]
  implicit val soldOutRoomInfoDecoder = deriveDecoder[transformers.SoldOutRoomInfo]

  implicit val enrichedSoldOutRoomsEncoder = deriveEncoder[transformers.EnrichedSoldOutRooms]
  implicit val enrichedSoldOutRoomsDecoder = deriveDecoder[transformers.EnrichedSoldOutRooms]

  implicit val cityBookingHistoryEncoder = deriveEncoder[transformers.CityBookingHistory]
  implicit val cityBookingHistoryDecoder = deriveDecoder[transformers.CityBookingHistory]

  implicit val bookingFormHistoryEncoder = deriveEncoder[transformers.BookingFormHistory]
  implicit val bookingFormHistoryDecoder = deriveDecoder[transformers.BookingFormHistory]

  implicit val propertyLastYearBookingCountEncoder =
    deriveEncoder[com.agoda.pricestream.models.response.PropertyLastYearBookingCount]
  implicit val propertyLastYearBookingCountDecoder =
    deriveDecoder[com.agoda.pricestream.models.response.PropertyLastYearBookingCount]

  implicit val bookingTimeStampEncoder = deriveEncoder[com.agoda.pricestream.models.response.BookingTimeStamp]
  implicit val bookingTimeStampDecoder = deriveDecoder[com.agoda.pricestream.models.response.BookingTimeStamp]

  implicit val propertyRecentBookingEncoder = deriveEncoder[com.agoda.pricestream.models.response.PropertyRecentBooking]
  implicit val propertyRecentBookingDecoder = deriveDecoder[com.agoda.pricestream.models.response.PropertyRecentBooking]

  implicit val rankMetricEncoder = deriveEncoder[com.agoda.pricestream.models.response.RankMetric]
  implicit val rankMetricDecoder = deriveDecoder[com.agoda.pricestream.models.response.RankMetric]

  implicit val propertyMetaRankingEncoder = deriveEncoder[com.agoda.pricestream.models.response.PropertyMetaRanking]
  implicit val propertyMetaRankingDecoder = deriveDecoder[com.agoda.pricestream.models.response.PropertyMetaRanking]

  implicit val propertyPriceSchemaResponseEncoder =
    deriveEncoder[com.agoda.pricestream.models.response.PropertyPriceSchemaResponse]
  implicit val propertyPriceSchemaResponseDecoder =
    deriveDecoder[com.agoda.pricestream.models.response.PropertyPriceSchemaResponse]

  implicit val propertyPriceSchemaEncoder = deriveEncoder[com.agoda.pricestream.models.response.PropertyPriceSchema]
  implicit val propertyPriceSchemaDecoder = deriveDecoder[com.agoda.pricestream.models.response.PropertyPriceSchema]

  implicit val propertyMetaInfoEncoder = deriveEncoder[com.agoda.pricestream.models.response.PropertyMetaInfo]
  implicit val propertyMetaInfoDecoder = deriveDecoder[com.agoda.pricestream.models.response.PropertyMetaInfo]

  implicit val similarPropertyInfoEncoder = deriveEncoder[ranking.SimilarPropertyInfo]
  implicit val similarPropertyInfoDecoder = deriveDecoder[ranking.SimilarPropertyInfo]

  implicit val rankingExplanationMessageEncoder = deriveEncoder[ranking.RankingExplanationMessage]
  implicit val rankingExplanationMessageDecoder = deriveDecoder[ranking.RankingExplanationMessage]

  implicit val internalUtilityDataFromDFEncoder = deriveEncoder[transformers.InternalUtilityDataFromDF]
  implicit val internalUtilityDataFromDFDecoder = deriveDecoder[transformers.InternalUtilityDataFromDF]

  implicit val internalUtilityDataFromContentEncoder = deriveEncoder[transformers.InternalUtilityDataFromContent]
  implicit val internalUtilityDataFromContentDecoder = deriveDecoder[transformers.InternalUtilityDataFromContent]

  implicit val internalUtilityDataEncoder = deriveEncoder[transformers.InternalUtilityData]
  implicit val internalUtilityDataDecoder = deriveDecoder[transformers.InternalUtilityData]

  implicit val landmarkInfoEncoder = deriveEncoder[transformers.LandmarkInfo]
  implicit val landmarkInfoDecoder = deriveDecoder[transformers.LandmarkInfo]

  implicit val areaBookingCountModelEncoder = deriveEncoder[transformers.AreaBookingCountModel]
  implicit val areaBookingCountModelDecoder = deriveDecoder[transformers.AreaBookingCountModel]

  implicit val pricingSummaryEncoder = deriveEncoder[transformers.PricingSummary]
  implicit val pricingSummaryDecoder = deriveDecoder[transformers.PricingSummary]

  implicit val alternateDateSummaryEncoder = deriveEncoder[transformers.AlternateDateSummary]
  implicit val alternateDateSummaryDecoder = deriveDecoder[transformers.AlternateDateSummary]

  implicit val metaApiRankMetricEncoder = deriveEncoder[metaapi.RankMetric]
  implicit val metaApiRankMetricDecoder = deriveDecoder[metaapi.RankMetric]

  implicit val propertyRankingEncoder = deriveEncoder[metaapi.PropertyRanking]
  implicit val propertyRankingDecoder = deriveDecoder[metaapi.PropertyRanking]

  implicit val propertyLongStayPromotionEncoder =
    deriveEncoder[com.agoda.pricestream.models.response.PropertyLongStayPromotion]
  implicit val propertyLongStayPromotionDecoder =
    deriveDecoder[com.agoda.pricestream.models.response.PropertyLongStayPromotion]

  implicit val metaEngagementEncoder = deriveEncoder[transformers.MetaEngagement]
  implicit val metaEngagementDecoder = deriveDecoder[transformers.MetaEngagement]

  implicit val enrichedSuggestedRoomEncoder = deriveEncoder[transformers.EnrichedSuggestedRoom]
  implicit val enrichedSuggestedRoomDecoder = deriveDecoder[transformers.EnrichedSuggestedRoom]

  implicit val enrichedMultiRoomSuggestionEncoder = deriveEncoder[transformers.EnrichedMultiRoomSuggestion]
  implicit val enrichedMultiRoomSuggestionDecoder = deriveDecoder[transformers.EnrichedMultiRoomSuggestion]

  implicit val enrichMasterRoomBundleSegmentEncoder = deriveEncoder[transformers.EnrichMasterRoomBundleSegment]
  implicit val enrichMasterRoomBundleSegmentDecoder = deriveDecoder[transformers.EnrichMasterRoomBundleSegment]

  implicit val roomBundleEncoder = deriveEncoder[transformers.RoomBundle]
  implicit val roomBundleDecoder = deriveDecoder[transformers.RoomBundle]

  implicit val paymentMethodEncoder = deriveEncoder[payment.PaymentMethod]
  implicit val paymentMethodDecoder = deriveDecoder[payment.PaymentMethod]

  implicit val paymentCategoryEncoder = deriveEncoder[payment.PaymentCategory]
  implicit val paymentCategoryDecoder = deriveDecoder[payment.PaymentCategory]

  implicit val displayPaymentTypeEncoder = deriveEncoder[payment.DisplayPaymentType]
  implicit val displayPaymentTypeDecoder = deriveDecoder[payment.DisplayPaymentType]

  implicit val propertyResponseEncoder = deriveEncoder[transformers.Property]
  implicit val propertyResponseDecoder = deriveDecoder[transformers.Property]

  implicit val debugEncoder = deriveEncoder[transformers.Debug]
  implicit val debugDecoder = deriveDecoder[transformers.Debug]

  implicit val supplierInformationEncoder = deriveEncoder[transformers.SupplierInformation]
  implicit val supplierInformationDecoder = deriveDecoder[transformers.SupplierInformation]

  implicit val localizationResponseEncoder = deriveEncoder[transformers.LocalizationResponse]
  implicit val localizationResponseDecoder = deriveDecoder[transformers.LocalizationResponse]

  implicit val dFMetaResultEncoder = deriveEncoder[transformers.DFMetaResult]
  implicit val dFMetaResultDecoder = deriveDecoder[transformers.DFMetaResult]

  implicit val propertiesResponseEncoder = deriveEncoder[transformers.Properties]
  implicit val propertiesResponseDecoder = deriveDecoder[transformers.Properties]

  implicit val taxMetaDataEncoder = deriveEncoder[models.starfruit.TaxMetaData]
  implicit val taxMetaDataDecoder = deriveDecoder[models.starfruit.TaxMetaData]
}

object CirceUtil {

  def renameField(json: Json, fieldToRename: String, newName: String): Json = {
    val newJson = for {
      value   <- json.hcursor.downField(fieldToRename).focus
      newJson <- json.mapObject(_.add(newName, value)).hcursor.downField(fieldToRename).delete.top
    } yield newJson

    newJson.getOrElse(json)
  }

}
