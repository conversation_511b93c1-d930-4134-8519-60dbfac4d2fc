package com.agoda.papi.search.common.util.context

/** !! IMPORTANT !! Please help us keep ActiveABTests nice and tidy by grouping the experiments by team (JIRA pre-fix).
  */
case class AllocationContext(experimentContext: ExperimentContext) {
  import ActiveABTests.experiments

  lazy val medianPriceAsFilter: Char             = allocate(experiments.medianPriceAsFilter)
  lazy val usePriceAfterCashbackForSorting: Char = allocate(experiments.usePriceAfterCashbackForSorting)

  lazy val allowAPMinusOneSearch: Char             = allocate(experiments.allowAPMinusOneSearch)
  lazy val maxSavingHotels: Char                   = allocate(experiments.maxSavingAvailableHotels)
  lazy val includeStructuredBenefitsInFilter: Char = allocate(experiments.includeStructuredBenefitsInFilter)

  lazy val enableRateCategoriesForAllExternalServiceMode: Char = allocate(
    experiments.enableRateCategoriesForAllExternalServiceMode
  )

  lazy val disableJASOFilterRoomForNonJapanHotel: Char = allocate(experiments.disableJASOFilterRoomForNonJapanHotel)
  lazy val enableFilterWithAsqType: Char               = allocate(experiments.enableFilterWithAsqType)

  lazy val useOnlyActiveSuppliersForAvailabilityFlow: Char = allocate(
    experiments.useOnlyActiveSuppliersForAvailabilityFlow
  )

  lazy val useDFSupplierIdsSP: Char             = allocate(experiments.useDFSupplierIdsSP)
  lazy val enableBookNowPayLaterForPhuket: Char = allocate(experiments.enableBookNowPayLaterForPhuket)
  lazy val fixReviewSortingBug: Char            = allocate(experiments.fixReviewSortingBug)
  lazy val removeGeolocationFromACMKafka: Char  = allocate(experiments.removeGeolocationFromACMKafka)

  lazy val recalculateSponsoredPositionWithSeperationLogic: Char = allocate(
    experiments.recalculateSponsoredPositionWithSeperationLogic
  )

  lazy val enableWhiteLabelFencing: Char      = allocate(experiments.enableWhiteLabelFencing)
  lazy val enableRocketRankingForBessie: Char = allocate(experiments.enableRocketRankingForBessie)
  lazy val enableExternalVipProgram: Char     = allocate(experiments.enableExternalVipProgram)

  lazy val enablePromocodeInExternalLoyaltyProfile: Char = allocate(
    experiments.enablePromocodeInExternalLoyaltyProfile
  )

  lazy val doAggregationOnEmptyProperties: Char  = allocate(experiments.doAggregationOnEmptyProperties)
  lazy val stopFetchingCDBforPMCEnrichment: Char = allocate(experiments.stopFetchingCDBforPMCEnrichment)
  lazy val enableOverrideRequiredBasis: Char     = allocate(experiments.enableOverrideRequiredBasis)

  lazy val pricestreamBookingCountInfoNegativeTest: Char =
    allocate(experiments.pricestreamBookingCountInfoNegativeExp)
  lazy val pricestreamPropertyPriceTrendNegativeTest: Char =
    allocate(experiments.pricestreamPropertyPriceTrendNegativeExp)
  lazy val pricestreamPropertyMetaInfoNegativeTest: Char = allocate(experiments.pricestreamPropertyMetaInfoNegativeExp)
  lazy val pricestreamMetaLabNegativeTest: Char          = allocate(experiments.pricestreamMetaLabExp)

  lazy val pricestreamPropertyAvailabilityNegativeTest: Char = allocate(experiments.pricestreamPropertyAvailabilityExp)

  lazy val pricestreamPropertyLongStayPromotionNegativeTest: Char = allocate(
    experiments.pricestreamPropertyLongStayPromotionNegativeExp
  )

  lazy val disableAugmentedCityFeatureOnBigCity: Char = allocate(experiments.disableAugmentedCityFeatureOnBigCity)

  lazy val pricestreamCityPriceTrendNegativeExp: Char = allocate(experiments.pricestreamCityPriceTrendNegativeExp)
  lazy val negTestSecretDeals: Char                   = allocate(experiments.negTestSecretDeals)

  lazy val skipCallingPullSupplierForUnknownBots: Char = allocate(experiments.skipCallingPullSupplierForUnknownBots)

  lazy val skipCallingPullSupplier: Char = allocate(experiments.skipCallingPullSupplier)

  lazy val showPromotionsBreakdown: Char = allocate(experiments.showPromotionsBreakdown)

  lazy val skipBankIdValidationInInstallmentPlan: Char = allocate(experiments.skipBankIdValidationInInstallmentPlan)

  lazy val overLimitTimeBeforeCallDF: Char = allocate(experiments.overLimitTimeBeforeCallDF)

  lazy val enableSoldOutForExtraProperty: Char           = allocate(experiments.enableSoldOutForExtraProperty)
  lazy val disableAreaSearchOnES: Char                   = allocate(experiments.disableAreaSearchOnES)
  lazy val fixExtraPropertiesPositionWithSponsored: Char = allocate(experiments.fixExtraPropertiesPositionWithSponsored)
  lazy val aaTestPlatformIdZeroAndSystemIdZero: Char     = allocate(experiments.aaTestPlatformIdZeroAndSystemIdZero)
  lazy val fixtotalFilteredHotelBug: Char                = allocate(experiments.fixtotalFilteredHotelBug)
  lazy val fixLostAppliedFiltersInFilterMatrixBug: Char  = allocate(experiments.fixLostAppliedFiltersInFilterMatrixBug)
  lazy val addReviewScorePostFilter: Char                = allocate(experiments.addReviewScorePostFilter)
  lazy val useIndividualHourlyRates: Char                = allocate(experiments.useIndividualHourlyRates)

  lazy val negativeTestRoomReco: Char                = allocate(experiments.negativeTestRoomReco)
  lazy val negativeTestHotelReco: Char               = allocate(experiments.negativeTestHotelReco)
  lazy val negativeTestPropertyReco: Char            = allocate(experiments.negativeTestPropertyReco)
  lazy val migrateZenithCallingFSForFilterReco: Char = allocate(experiments.migrateZenithCallingFSForFilterReco)
  lazy val migrateRoomRecoFromZenithToFs: Char       = allocate(experiments.migrateRoomRecoFromZenithToFs)
  lazy val migrateSimilarPropertiesToJarvis: Char    = allocate(experiments.migrateSimilarPropertiesToJarvis)

  lazy val makeSoldOutPropPriceNonMandatory: Char = allocate(experiments.makeSoldOutPropPriceNonMandatory)

  lazy val readCityAvailProtobufOnly: Char   = allocate(experiments.readCityAvailProtobufOnly)
  lazy val enableRoomSizeFilter: Char        = allocate(experiments.enableRoomSizeFilter)
  lazy val enableAdditionalRoomFilters: Char = allocate(experiments.enableAdditionalRoomFilters)
  lazy val enablePropertyFilterSyncId: Char  = allocate(experiments.enablePropertyFilterSyncId)
  lazy val asoFilterMigration: Char          = allocate(experiments.asoFilterMigration)
  lazy val isReplaceDroneIcon: Char          = allocate(experiments.replaceDroneIcon)
  lazy val offerRankingV4: Char              = allocate(experiments.offerRankingV4)

  lazy val reduceLowerBoundPriceFilterBuffer: Char = allocate(experiments.reduceLowerBoundPriceFilterBuffer)

  lazy val fixMissingSearchContextInRestEndpointsBug: Char = allocate(
    experiments.fixMissingSearchContextInRestEndpointsBug
  )

  lazy val fixBugPackageDivideByZero: Char = allocate(experiments.fixBugPackageDivideByZero)

  lazy val reduceCbReadTime: Char = allocate(experiments.reduceCbReadTime)

  lazy val blockNonCompliantHotelSearchForIT: Char = allocate(experiments.blockNonCompliantHotelSearchForIT)

  lazy val blockNonCompliantHotelSearchForSA: Char = allocate(experiments.blockNonCompliantHotelSearchForSA)

  lazy val disableBlockNonCompliantHotelForVaticanAreaKS: Char = allocate(
    experiments.disableBlockNonCompliantHotelForVaticanAreaKS
  )

  lazy val useDisableDsaLicenseBlockingWLFeature: Char = allocate(
    experiments.useDisableDsaLicenseBlockingWLFeature
  )

  lazy val useOpsStarRating: Char               = allocate(experiments.useOpsStarRating)
  lazy val useJarvisForRocketmilesRanking: Char = allocate(experiments.useJarvisForRocketmilesRanking)
  lazy val useJarvisForCitiRanking: Char        = allocate(experiments.useJarvisForCitiRanking)

  lazy val showHkHatTaxAsAmountIncluded: Char = allocate(experiments.hongKongHatTax)

  lazy val enableSellingDirectConnectForJtb: Char = allocate(experiments.enableSellingDirectConnectForJtb)

  lazy val isDmcIdRemoveEnabled: Char = allocate(experiments.isDmcIdRemoveEnabled)

  lazy val enableMultiProductMSEheader: Char = allocate(experiments.enableMultiProductMSEheader)

  lazy val useRegulatoryBlockingForDsaCompliant: Char = allocate(experiments.useRegulatoryBlockingForDsaCompliant)

  lazy val ignoreRequestedNumberOfRoomsForNha: Char = allocate(experiments.ignoreRequestedNumberOfRoomsForNha)

  lazy val changeDefinitionOfLuxuryProperty: Char = allocate(experiments.changeDefinitionOfLuxuryProperty)

  lazy val koddiJarvisMigration: Char = allocate(experiments.koddiJarvisMigration)

  lazy val changedTotalActiveHotelsCount: Char           = allocate(experiments.changedTotalActiveHotelsCount)
  lazy val ignoreReturnHotelNotReadyIfPullNotReady: Char = allocate(experiments.ignoreReturnHotelNotReadyIfPullNotReady)

  lazy val fixWrongSearchTypeRequestParameter: Char = allocate(experiments.fixWrongSearchTypeRequestParameter)

  lazy val injectSsrDelay1: Char = allocate(experiments.injectSsrDelay1)
  lazy val injectSsrDelay2: Char = allocate(experiments.injectSsrDelay2)
  lazy val injectSsrDelay3: Char = allocate(experiments.injectSsrDelay3)

  def allocate(experimentName: String): Char =
    experimentContext.runABExperimentWithAllocation(experimentName, 'A', 'B')
}

case class ActiveABTests(
    // PFE
    medianPriceAsFilter: String = "PFE-6766",
    usePriceAfterCashbackForSorting: String = "PFE-12034",

    // Thai Gov campaign
    isThaiGovCampaign: String = "THAI-GOV",

    // FUSION
    useOnlyActiveSuppliersForAvailabilityFlow: String = "FUSION-4219-ACTIVE-SUPPLIERS",
    useDFSupplierIdsSP: String = "FUSION-4219-UPDATE-SP",
    fixReviewSortingBug: String = "FUSION-4268",
    removeGeolocationFromACMKafka: String = "FUSION-4337",
    doAggregationOnEmptyProperties: String = "FUSION-4695",
    pricestreamBookingCountInfoNegativeExp: String = "FUSION-4915-2",
    pricestreamPropertyPriceTrendNegativeExp: String = "FUSION-4921",
    pricestreamPropertyMetaInfoNegativeExp: String = "FUSION-4923",
    pricestreamMetaLabExp: String = "FUSION-4916",
    pricestreamPropertyAvailabilityExp: String = "FUSION-4924",
    pricestreamPropertyLongStayPromotionNegativeExp: String = "FUSION-4920",
    pricestreamCityPriceTrendNegativeExp: String = "FUSION-4925",
    disableAugmentedCityFeatureOnBigCity: String = "FUSION-5197",
    negTestSecretDeals: String = "FUSIONFE-3791RENAMED",
    disableAreaSearchOnES: String = "FUSION-5298",
    fixExtraPropertiesPositionWithSponsored: String = "FUSION-5374",
    aaTestPlatformIdZeroAndSystemIdZero: String = "FUSION-5395",
    makeSoldOutPropPriceNonMandatory: String = "FUSION-5524",
    readCityAvailProtobufOnly: String = "FUSION-5730",
    reduceLowerBoundPriceFilterBuffer: String = "FUSION-5746",
    fixBugPackageDivideByZero: String = "FUSION-5809",
    fixMissingSearchContextInRestEndpointsBug: String = "FUSION-5865",
    fixtotalFilteredHotelBug: String = "FUSION-5863",
    fixLostAppliedFiltersInFilterMatrixBug: String = "FUSION-5339",
    addReviewScorePostFilter: String = "FUSION-5235",
    useIndividualHourlyRates: String = "FUSION-6086",
    changedTotalActiveHotelsCount: String = "FUSION-6024",
    ignoreReturnHotelNotReadyIfPullNotReady: String = "FUSION-6248",

    // PACKAGES
    maxSavingAvailableHotels: String = "PACKAGES-436",

    // PricePush
    allowAPMinusOneSearch: String = "PPP-1442",

    // VEL

    // ESCAPE
    includeStructuredBenefitsInFilter: String = "ESC-PAPIFILTER",

    // PBS
    enableRateCategoriesForAllExternalServiceMode: String = "EX-7949",
    disableJASOFilterRoomForNonJapanHotel: String = "BLEAGLE-348",

    // ASQ
    enableFilterWithAsqType: String = "OPA-1030-FE",
    enableBookNowPayLaterForPhuket: String = "OPA-1878",

    // Property WL Fencing (Phase 1)
    enableWhiteLabelFencing: String = "PWF-21",
    recalculateSponsoredPositionWithSeperationLogic: String = "FUSION-5437",

    // Enable RocketMiles Ranking for Bessie traffic
    enableRocketRankingForBessie: String = "BWHB-144",
    // Bessie enable external VIP program
    enableExternalVipProgram: String = "BWHB-192",

    // PMC
    stopFetchingCDBforPMCEnrichment: String = "PMC-5098",
    enableOverrideRequiredBasis: String = "PMC-4603",

    // skip calling pull supplier for unknown bots
    skipCallingPullSupplierForUnknownBots: String = "FUSION-5199-SKIPPULLSUPPLY-UNKNOWNBOT",
    showPromotionsBreakdown: String = "JTBFP-922",
    skipCallingPullSupplier: String = "FUSION-5218",

    // skip bank id and bank name validation during fetching installment plans
    skipBankIdValidationInInstallmentPlan: String = "PIN-22-SKIP-VALIDATION",
    overLimitTimeBeforeCallDF: String = "FUSION-5302",

    // Viper
    enableSoldOutForExtraProperty: String = "SAR-3263",

    // Use Agoda PublishPrice for Rocket
    useAgodaPublishPriceForRocket: String = "BWHB-401",

    // FLEX
    changeDefinitionOfLuxuryProperty: String = "FLEX-1353",

    // Personalization
    negativeTestRoomReco: String = "PERSO-3262",
    negativeTestHotelReco: String = "PERSO-3264",
    negativeTestPropertyReco: String = "PERSO-3263",
    migrateZenithCallingFSForFilterReco: String = "PERSO-3354",
    migrateRoomRecoFromZenithToFs: String = "PERSO-3828",
    migrateSimilarPropertiesToJarvis: String = "PERSO-3497",

    // ROOMIE
    enableRoomSizeFilter: String = "ROOMIE-1140",
    enableAdditionalRoomFilters: String = "ROOMIE-1197",
    enablePropertyFilterSyncId: String = "ROOMIE-1402",
    asoFilterMigration: String = "ROOMIE-1311",
    replaceDroneIcon: String = "ROOMIE-1499",
    offerRankingV4: String = "ROOMIE-1734",

    // Content Enrichment
    reduceCbReadTime: String = "FUSION-5783-REDUCE-CB-READ-TIME",

    // Legal Tech
    blockNonCompliantHotelSearchForIT: String = "LT-818-IT",
    blockNonCompliantHotelSearchForSA: String = "LT-818-SA",
    disableBlockNonCompliantHotelForVaticanAreaKS: String = "LT-999-KS",
    useDisableDsaLicenseBlockingWLFeature: String = "LT-1208",

    // NHA Frontend
    useOpsStarRating: String = "NHAFE-986",

    // Rocketmiles Ranking
    useJarvisForRocketmilesRanking: String = "FUSION-5969",
    useJarvisForCitiRanking: String = "FUSION-5982",
    hongKongHatTax: String = "NEP-24987",

    // JTB direct connect supply
    enableSellingDirectConnectForJtb: String = "JTBFP-615-V2",
    isDmcIdRemoveEnabled: String = "JTBFP-1293",
    useRegulatoryBlockingForDsaCompliant: String = "LT-895",
    ignoreRequestedNumberOfRoomsForNha: String = "NHAFE-1091",

    // Koddi Jarvis Migration
    koddiJarvisMigration: String = "DSINNO-1415",
    enableMultiProductMSEheader: String = "ACTB-MSE",

    // Jarvis
    fixWrongSearchTypeRequestParameter: String = "FUSION-6138",

    // Inject Delay
    injectSsrDelay1: String = "FUSION-6166",
    injectSsrDelay2: String = "FUSION-6166-2",
    injectSsrDelay3: String = "FUSION-6166-3",

    // RTA Pricing (ExternalLoyaltyProfile customer/user promocode)
    enablePromocodeInExternalLoyaltyProfile: String = "RTAP-808"
)

object ActiveABTests {
  val experiments                                     = new ActiveABTests()
  def apply: ActiveABTests                            = experiments
  val allActiveExperimentsForLoadTest: Vector[String] = apply.productIterator.map(_.toString).toVector
}
