package com.agoda.papi.search.common.util.measurement

/** Created by vpathomsuriy on 1/11/2017 AD.
  */
object MeasurementKey {
  val PageSearch              = "pagesearch"
  val Search                  = "search"
  val Service                 = "service"
  val Hotelmeta               = "hotelmeta"
  val Jarvis                  = "jarvis"
  val SLService               = "slservice"
  val Review                  = "review"
  val JarvisPageRank          = "jarvisPageRank"
  val Textsearch              = "textsearch"
  val Request                 = "request"
  val FbDetailsKey            = "fbdetails"
  val FavoriteSearch          = "favoriteSearch"
  val GetMemberUserContext    = "getMemberUserContext"
  val PropertiesFetcher       = "fetchproperties"
  val ComplexResultHelper     = "complexresultsethelper"
  val CombineHotelList        = "combinehotellist"
  val ComplexPriceStream      = "complexPriceStream"
  val AvailabilityRatio       = "availabilityratio"
  val FetchPushAvailabilities = "fetchpushavail"
  val ContentRoomDetails      = "contentroomdetails"
  val PropertyRoomResult      = "search.result.room.count"

  val HotelPerRequest = "hotelsperrequest"
  val Rooms           = "rooms"

  val RequestPollingInfo = "request.pollinfo"

  val ReplacePiiEmail = "replaceemail.time"

  val PageIterationRatio = "pageiteration.pageratio"

  val IterationCount = "pageiteration.count"
  val PastCheckIn    = "pastcheckin"

  val coreSearch                      = "coresearch"
  val Manual                          = "manual"
  val Aggregation                     = "aggregation"
  val postFilter                      = "postfilter"
  val HostProfile                     = "hostprofile"
  val HostPropertiesInfo              = "hostPropertiesInfo"
  val PreFilteredPropertyPercent      = "prefilteredpropertypercent"
  val CapiMemberDetail                = "memberdetail"
  val esResponse                      = "es_response"
  val HotelMetadataCount              = "hotel.metadata.count"
  val SupplierMetadataCount           = "supplier.metadata.count"
  val FullFlexibleDateSearch          = "fullFlexibleDateSearch"
  val CapiLoyaltyProfile              = "loyaltyprofile"
  val NormalProperties                = "normal-properties"
  val NotReadyProperties              = "not-ready-properties"
  val NotReadyPropertiesSummarySearch = "not-ready-properties-summary-search"
  val ElasticSearch                   = "external-dependency.elasticsearch"
  val PropertyInfoListCacheMiss       = "property-info-list.cache-miss"
  val ExtraProperty                   = "extra-properties"
  val PageShortCircuitPropertyCount   = "pageshortcircuit.propertycount"
  val PageShortCircuitExpectedCount   = "pageshortcircuit.expectedcount"
  val startuptime                     = "startup.time"

  val RoomSorting                           = "roomSorting"
  val fetchPropertyMetadata                 = "fetchPropertyMetadata"
  val fetchPropertyMetadataPropertyInfoList = "fetchPropertyMetadataPropertyInfoList"

  val GrowthProgram = "growthProgram"

  val PackageNumberOfRequest = "package.num.request"

  val ssrSearchResultLatencyBeforeDelayInjected    = "ssr-search-result-latency.before-delay-injected"
  val ssrSearchResultLatencyActualDelayInjected    = "ssr-search-result-latency.actual-delay"
  val ssrSearchResultLatencyDelayToInject          = "ssr-search-result-latency.delay-to-injected"
  val ssrSearchResultLatencyDelayToInjectUnbounded = "ssr-search-result-latency.delay-to-injected-unbounded"
  val ssrSearchResultLatencyLimit                  = "ssr-search-result-latency.latency-limit"
  val ssrSearchResultBytestring                    = "ssr-search-result-latency.bytestring"
}

object ApplicationStatusKey {
  val applicationJustStarting = "application.start"
  val applicationStarted      = "application.started"
  val applicationExited       = "application.exit"
}

object ElasticServiceStep {
  val filterSearch                  = "filterSearch"
  val filterSearchRankingSort       = "filterSearchRankingSort"
  val filterSearchCount             = "filterSearchCount"
  val bestSellerFilterSearch        = "bestSellerFilter"
  val augmentedSearch               = "augmentedSearch"
  val getAugmentedCities            = "getAugmentedCities"
  val getAllHotelInCity             = "getAllHotelInCity"
  val getAllHotelById               = "getAllHotelById"
  val radiusSearch                  = "radiusSearch"
  val regionSearch                  = "regionSearch"
  val boxSearch                     = "boxSearch"
  val getFilterMatrix               = "getFilterMatrix"
  val getFilterMatrixMultCities     = "getFilterMatrixMultCities"
  val fetchDistance                 = "fetchDistance"
  val getNearestCityForRadiusSearch = "getNearestCityForRadiusSearch"
  val getNearestCityForBoxSearch    = "getNearestCityForBoxSearch"
  val getDistanceFromPoint          = "getDistanceFromPoint"
  val getDistanceFromField          = "getDistanceFromField"
  val multiPolygonSearch            = "multiPolygonSearch"
  val fetchExtraPropertyLocation    = "fetchExtraPropertyLocation"
  val getPersonalizedProperties     = "getPersonalizedProperties"
  val fetchLuxuryHotels             = "fetchLuxuryHotels"
  val fetchCombinbedReviews         = "fetchcombinedreviews"
  val fetchDistanceFromUser         = "fetchdistancefromuser"
}

object SearchSubSteps {
  val fetchProperties = "fetchproperties"
  val fetchCities     = "fetchcities"
  val availability    = "availability"
  val filter          = "filter"

  val preFilterBeforeAvailability = "pre-filter-before-availability"
  val preFilterAfterAvailability  = "pre-filter-after-availability"

  val sort = "sort"
}

object FetchPropertiesSubStep {
  val fetchMetadata      = "metadata"
  val fetchPropertyInfo  = "propertyinfo"
  val computeSellability = "compute_sellability"
  val fetchCityInfo      = "cityinfo"
  val augmentation       = "augmentation"
}

object AvailabilitySubSteps {
  val pullAvail     = "avail_pull"
  val pushAvail     = "avail_push"
  val mergeAvail    = "merge_avail"
  val checkPushPull = "check_pushpull"
  val allAvail      = "avail_all"
}

object FilterSubSteps {
  val customAND          = "custom_and"
  val customOR           = "custom_or"
  val avail              = "elastic_avail"
  val soldout            = "elastic_soldout"
  val count              = "elastic_count"
  val BestSellerFilter   = "best-seller-filter"
  val personalizedFilter = "personalized-filter"
}

object PostFilterSubSteps {
  val getHotelRoomBedConfigs   = "get_hotel_roombed_config"
  val createFilters            = "create_filters"
  val getFilteredProperties    = "get_filtered_properties"
  val getRecommendedProperties = "get_recommended_properties"
  val executeAllRules          = "execute_all_rules"
}

object SortSubSteps {
  val jarvis           = "prepare_jarvis"
  val jarvisCallFirst  = "call_jarvis_first"
  val review           = "prepare_review"
  val prepareAll       = "prepare_all"
  val sort             = "sort"
  val distanceFromUser = "distance_from_user"
}

object AggregationSubSteps {
  val metadata      = "metadata"
  val customMatrix  = "custommatrix"
  val elasticMatrix = "elasticmatrix"
}

object PageStep { // all under pagesearch goes here
  val metaData                           = "metaData"
  val Availabilities                     = "availabilities"
  val Unavailabilities                   = "unavailabilities"
  val PreAndTermsCustomFilter            = "preandcustomfilter"
  val preOrTermsCustomFilter             = "preorcustomfilter"
  val PostCustomFilter                   = "postcustomfilter"
  val Aggregation                        = "aggregation"
  val AggregationCount                   = "aggregation-count"
  val AggregationSort                    = "aggregation-sort"
  val BestSellerFilter                   = "best-seller-filter"
  val UnAvailAggregation                 = "unAvailAggregation"
  val FilterMatrix                       = "filtermatrix"
  val FilterFlow                         = "filtering"
  val SortingFlow                        = "sortingflow"
  val CustomMatrix                       = "custommatrix"
  val ElasticMatrix                      = "elasticmatrix"
  val Sorting                            = "sorting"
  val UnAvailSorting                     = "unAvailSorting"
  val SortingExcludingFilteredProperties = "sortingExcludingFilteredProperties"
  val Paging                             = "paging"
  val ApoPaging                          = "apoPaging"
  val RemainingResult                    = "remaining_result"
  val Flow                               = "flow"
  val OverallPaging                      = "overall_paging"
  val PropertySearch                     = "propertysearch"
  val UnAvailPaging                      = "unAvailPaging"
  val Available                          = "available"
  val APOPaging                          = "apo"
  val ExtraAgodaHomes                    = "extraAgodaHomes"
  val FeaturedAgodaHomes                 = "featuredAgodaHomes"
  val HighlyRatedAgodaHomes              = "highlyRatedAgodaHomes"
  val PersonalizedProperties             = "personalizedProperties"
  val UnavailPagingWithAlternateDates    = "unAvailPagingWithAlternateDates"
  val PrepareSortData                    = "prepareSortData"
  val PackResult                         = "packing"
  val GetExtraData                       = "extra"
  val GetExtraHotels                     = "GetExtraHotels"
  val GetFeaturesAgodaHomes              = "GetFeaturesAgodaHomes"
  val GetHighlyRatedAgodaHomes           = "GetHighlyRatedAgodaHomes"
  val GetPersonalizedProperties          = "GetPersonalizedProperties"
  val GetExtraAgodaHomes                 = "GetExtraAgodaHomes"
  val GetCityTopSearchHotels             = "GetCityTopSearchHotels"
  val DragonFruit                        = "df"
  val ElasticService                     = "elasticservice"
  val ReviewSortFilter                   = "reviewSortFilter"
  val PriceStreamApiCall                 = "priceStreamApiCall"
  val MergeAvail                         = "mergeavail"
  val FetchAllAvail                      = "fetchallavail"
  val FetchHotelMetaRanking              = "fetchHotelMetaRanking"
  val GetSpecialAttributeProperties      = "GetSpecialAttributeProperties"
  val PostPageSorting                    = "postpagesorting"
}

object Status {
  val SUCCESS = "Success"
  val FAILURE = "Failure"
  val TIMEOUT = "Timeout"
}

object PostPageSortingSubSteps {
  val affordableCategorySort = "affordable_category_sort"
  val priceSort              = "price_sort"
  val insiderDealSort        = "insider_deal_sort"
  val packageDealSort        = "package_deal_sort"
  val personalizeSort        = "personalize_sort"
  val noSort                 = "no_sort"
  val reviewAndPriceSort     = "review_and_price_sort"
}

object FetchStop {
  val fetchStopKey = "fetchstop"

  val filledAllProperty   = "filledallproperty"
  val noRemainingProperty = "noremainingproperty"
  val fetchLimit          = "exceedfetchlimit"
}

object TrackerStep {
  val MissingAvailData = "missing-availability"
  val SoldOut          = "sold-out"
  val UnAvailable      = "unavailable-hotels"
  val UnAvailSorting   = "unavailable-sorting"
}

object PageMeasurementStep {
  val Step           = "step"
  val SubStep        = "substep"
  val TotalStep      = "total"
  val DmcPushPullMap = "dmcpushpullmap"
}

object MeasurementTag {
  val QueryField = "queryfield"
  val PlatformId = "platformid"
  val CityId     = "cityid"
  val Cid        = "cid"

  val Guests                    = "guests"
  val Adult                     = "adult"
  val Children                  = "children"
  val UpdateRemainingRoomLogic  = "updateremainingroom"
  val FixBookOnRequestLogic     = "fix-book-on-request-logic"
  val RemoveUnusedAPOProperties = "remove-unused-apo-properties"

  val Name                                = "name"
  val Endpoint                            = "endpoint"
  val Platform                            = "platform"
  val PageTypeId                          = "pagetypeid"
  val NewIsBot                            = "newIsbot"
  val NewIsKnownBot                       = "newIsKnownBot"
  val NewTrafficType                      = "newTrafficType"
  val NewUserAgent                        = "newUseragent"
  val IsBot                               = "isbot"
  val BotType                             = "bottype"
  val IsKnownBot                          = "isKnownBot"
  val UserAgent                           = "useragent"
  val SearchType                          = "searchType"
  val IsReturnHotelNotReadyIfPullNotReady = "isReturnHotelNotReadyIfPullNotReady"
  val IsPageTokenEnabled                  = "isPageTokenEnabled"
  val NotReadyPercentage                  = "notReadyPercentage"
  val NotReadyPropertiesCount             = "notReadyPropertiesCount"
  val AllPropertiesIsReady                = "AllPropertiesIsReady"
  val AllPropertiesNotReady               = "AllPropertiesNotReady"
  val CityType                            = "city"
  val Success                             = "success"
  val ErrorType                           = "error.type"
  val WhiteLabelId                        = "whitelabelId"
  val IsBlockedRetry                      = "isBlockedRetry"
  val Priority                            = "priority"
  val DegradeMode                         = "degrademode"
  val IsRetry                             = "isRetry"
  val IsEmptyResult                       = "isEmptyResult"
  val IsStrictEntity                      = "isStrictEntity"
  val Iteration                           = "iteration"
  val PropertyResultType                  = "property-result-type"
  val WithChildren                        = "withchildren"
  val funnel                              = "funnel"
  val UsePageToken                        = "usepagetoken"
  val isPlatformAndSystemIdZero           = "isplatformandsystemidzero"
  val DatelessSearch                      = "datelesssearch"
  val elasticFilterApply                  = "elasticfilterapply"
  val RegularSearch                       = "regularsearch"
  val IsEnvoyRetry                        = "isEnvoyRetry"
  val IsReBookingRequest                  = "isReBookingRequest"
  val ClientRequestAttempt                = "clientrequestattempt"
  val EnvoyRequestAttempt                 = "envoyrequestattempt"
  val isRocketMile                        = "isRocketMile"
  val status                              = "status"
  val step                                = "step"
  val AggregationMode                     = "aggregationmode"
  val reBalanceDFChunks                   = "reBalanceDFChunks"
  val PulsePromotionsFilter               = "pulsepromotionsfilter"
  val KidsStayForFreeFilter               = "kidsStayForFreeFilter"
  val GreatForFamiliesFilter              = "greatForFamiliesFilter"
  val SortField                           = "sortField"
  val FlexibleSearchResult                = "flexibleSearchResult"
  val IsPageFulfilled                     = "is-page-fulfilled"
  val PageSize                            = "page-size"
  val OperationTags                       = "operation"
  val ElasticSearchOperation              = "elasticsearch-operation"
  val StatusCode                          = "statuscode"
  val IsPush                              = "ispush"
  val DBName                              = "dbname"
  val DBCluster                           = "dbcluster"
  val IsAugmentedCity                     = "isaugmentedcity"
  val LeadDays                            = "leaddays"
  val IsSoldOut                           = "isSoldOut"
  val ClientName                          = "client-name"
  val AgEnv                               = "ag-env"
  val clientProfileDevice                 = "clientprofile_device"
  val clientProfileOs                     = "clientprofile_os"
  val clientProfileClient                 = "clientprofile_client"
  val clientProfileSubClient              = "clientprofile_subclient"
  val AgGateKeeperRequestPriority         = "ag-gk-rq-priority"
  val FilterName                          = "filter-name"
  val PageNumber                          = "page-number"
  val Status                              = "status"
  val HasCustomFilters                    = "has-custom-filters"
  val ContainsFalseNegative               = "contains-false-negative"
  val DfResponseType                      = "df-response-type"
  val AllFilterCount                      = "all-filter-count"
  val reduceCbReadTimeExp                 = "reduce-cb-time-exp"
  val useJarvisForRocketmilesRankingExp   = "use-jarvis-for-rocketmiles-ranking-exp"
  val useJarvisForCitiRankingExp          = "use-jarvis-for-citi-ranking-exp"
  val isFetchHostProfile                  = "isFetchHostProfile"
  val PackagingFlowType                   = "packagingFlowType"
  val TotalSize                           = "totalSize"

  val ssrLatencyInjectedPercentage = "ssr-latency-injected-percentage"
  val injectSsrDelayExperiment1    = "inject-ssr-delay-experiment1"
  val injectSsrDelayExperiment2    = "inject-ssr-delay-experiment2"
  val injectSsrDelayExperiment3    = "inject-ssr-delay-experiment3"
  val isExceedMaximumSsrDelay      = "is-exceed-maximum-ssr-delay"
}

object ErrorTypeTag {
  val invalidRequest = "invalidrequest"
  val circuitBreaker = "circuitbreaker"
  val papiError      = "papierror"
}

object StreamKey {
  // common
  val streamPrefix = "propertyapi.stream"

  val papiEndToEndLatency   = s"$streamPrefix.endtoend.latency"
  val papiRequestLatency    = s"$streamPrefix.request.latency"
  val papiResponseLatency   = s"$streamPrefix.response.latency"
  val papiDeadLetterLatency = s"$streamPrefix.deadletter.latency"

  val papiDfRequestSize    = s"$streamPrefix.df.request.size"
  val serializationTypeTag = "type"
}

object PriceStreamTag {
  val PriceStreamPrefix = "pricestream"

  val CheapestpriceMessege = s"$PriceStreamPrefix.chepestpricemessage"
  val preFetchPriceTrends  = s"$PriceStreamPrefix.preFetchPriceTrends"

}

object SupplierFinancialsKey {
  val adjustmentCount = "adjustment.count"
}

object SqlKey {
  val dbcallWithfallback = "dbcall.withfallback"
}

object SqlTag {
  val doFallback = "fallback"
  val query      = "query"
}

object SpecialAttributeKey {
  val notCheapest = "sa.notcheapest"
  val Cheapest    = "sa.cheapest"
}

object PollingMetricsTag {
  val Tag_pollAttempt        = "poll_attempt"
  val Tag_pid                = "platform_id"
  val Tag_searchType         = "search_type"
  val Tag_hasPullPollingFlag = "has_poll_feature_flag"
}

object AsyncHttpClientMeasurementKeys {
  val totalConnection       = "asynchttpclient.totalconnection"
  val totalActiveConnection = "asynchttpclient.totalactiveconnection"
  val totalIdleConnection   = "asynchttpclient.totalidleconnection"

}

object HotelShutdownMetrics {
  val complexSearch = "hotel_shutdown.complex.search"
}
