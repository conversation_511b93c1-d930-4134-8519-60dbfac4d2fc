package com.agoda.papi.search.common.externaldependency.chunkable.client

import com.agoda.papi.pricing.graphql.PricingPropertiesRequestEnvelope
import com.agoda.papi.search.common.service.{ CalculateChannelParams, CalculateChannelResult, CalculateChannelService }
import com.agoda.papi.search.common.util.context.{ CmsExperimentContext, ContextHolder, ExperimentContext }
import com.agoda.papi.search.common.util.debug.PAPISearchResultTrackerNOOP
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.types.{ CountryIdType, HotelIdType }
import com.agoda.platform.service.context.GlobalContext

import scala.concurrent.{ ExecutionContext, Future }

/** class to enrich channel ids for properties in DF Specifically temporary purpose
  */
//TODO: Remove this logic. Done to support production live dates. PLTAPI-290 Should be removed ASAP.
class DFChannelEnrichService(calculateChannelService: CalculateChannelService) extends PAPIAppLogger {

  protected[client] def getChannelMapping(
      request: PricingPropertiesRequestEnvelope,
      globalContext: GlobalContext,
      enableOpaqueChannel: Option[Boolean],
      hotelCountryMapping: Map[HotelIdType, CountryIdType],
      isEnabledPartnerChannelSelection: Option[Boolean],
      isPackageDeal: Boolean,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext): Future[Map[CalculateChannelResult, Seq[Long]]] = {
    val pricing = request.pricingRequestParameters.t.pricing
    val channelsFuture = request.propertyIds.map { propertyId =>
      val countryId = hotelCountryMapping.getOrElse(propertyId, -1L)
      val channelParam = CalculateChannelParams(
        pricing.filters.ratePlans,
        request.pricingRequestParameters.t.context.clientInfo.cid,
        globalContext.getLanguageId.getOrElse(1),
        globalContext.getOrigin.getOrElse(""),
        globalContext.getPlatformId.getOrElse(0),
        globalContext.getPlatformId.getOrElse(0),
        globalContext.getMemberId.getOrElse(0) != 0,
        enableOpaqueChannel.getOrElse(false),
        countryId,
        isEnabledPartnerChannelSelection.getOrElse(false),
        isPackageDeal
      )
      calculateChannelService
        .calculateChannel(channelParam)(
          ContextHolder(
            experimentContext,
            CmsExperimentContext(experimentContext, None, None, None),
            PAPISearchResultTrackerNOOP,
            globalContext
          )
        )
        .map { result =>
          propertyId -> result
        }
    }

    Future.sequence(channelsFuture).map { channels =>
      channels.groupBy(_._2).map { grp =>
        CalculateChannelResult(grp._1.channelIds) -> grp._2.map(_._1)
      }
    }
  }

  def enrichedChannelRequest(
      request: PricingPropertiesRequestEnvelope,
      globalContext: GlobalContext,
      enableOpaqueChannel: Option[Boolean],
      isEnabledPartnerChannelSelection: Option[Boolean],
      isPackageDeal: Boolean,
      productHotelMapping: Map[HotelIdType, ProductHotels],
      experimentContext: ExperimentContext
  )(implicit
      executionContext: ExecutionContext
  ): Future[Map[CalculateChannelResult, Seq[Long]]] =
    getChannelMapping(
      request,
      globalContext,
      enableOpaqueChannel,
      productHotelMapping.mapValues(_.countryId),
      isEnabledPartnerChannelSelection,
      isPackageDeal,
      experimentContext
    )

}
