package com.agoda.papi.search.common.externaldependency

import com.agoda.common.graphql.model.Wrapper
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.information.{ InformationResponse, LocalInformation }
import com.agoda.content.models.propertycontent.{
  ContentImagesResponse,
  ContentInformationSummary,
  ContentReviewScoreResponse,
  ContentReviewSummariesResponse
}
import com.agoda.papi.content.graphql._
import com.agoda.papi.search.common.externaldependency.chunkable.ExternalService
import com.agoda.papi.search.common.model.content.ContentWithPropertyId

import scala.concurrent.Future

package object content {
  type ImageExternalService =
    ExternalService[Future, ContentGroupImagesRequestEnvelope, List[Wrapper[ContentImagesResponse]]]
  type ReviewScoreExternalService =
    ExternalService[Future, ContentGroupReviewScoreRequestEnvelope, List[Wrapper[ContentReviewScoreResponse]]]
  type ReviewSummariesExternalService =
    ExternalService[Future, ContentGroupReviewSummariesRequestEnvelope, List[Wrapper[ContentReviewSummariesResponse]]]
  type InformationSummaryExternalService =
    ExternalService[Future, ContentInformationSummaryGroupRequestEnvelope, List[Wrapper[ContentInformationSummary]]]
  type InformationExternalService =
    ExternalService[Future, ContentInformationGroupRequestEnvelope, List[Wrapper[InformationResponse]]]
  type ExperiencesExternalService =
    ExternalService[Future, ContentGroupExperienceRequestEnvelope, List[Wrapper[Experiences]]]
  type FeaturesExternalService =
    ExternalService[Future, ContentGroupFeaturesRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]
  type HighlightsExternalService =
    ExternalService[Future, ContentGroupHighlightsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]
  type QnaExternalService =
    ExternalService[Future, ContentGroupQnaRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]
  type TopicsExternalService =
    ExternalService[Future, ContentGroupTopicsRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]
  type LocalInformationExternalService =
    ExternalService[Future, ContentGroupLocalInformationRequestEnvelope, List[Wrapper[LocalInformation]]]
  type EngagementExternalService =
    ExternalService[Future, ContentGroupEngagementRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]
  type RateCategoryExternalService =
    ExternalService[Future, ContentGroupRateCategoryRequestEnvelope, List[Wrapper[ContentWithPropertyId]]]
}
