name := "common"

run / fork := true

enablePlugins(BuildInfoPlugin)

scalacOptions ++= Seq(
  "-language:implicitConversions",
  "-language:higherKinds",
  "-language:postfixOps",
  "-Ypartial-unification"
)

libraryDependencies ++= Seq(
  "com.netflix.archaius" % "archaius-scala" % Versions.archaiusScala,
  "com.typesafe.akka"   %% "akka-http"      % Versions.akkaHttp,
  "com.typesafe.akka"   %% "akka-actor"     % Versions.akka,
  "com.typesafe.akka"   %% "akka-stream"    % Versions.akka,
  ("com.agoda.commons"  %% "cache-remote"   % Versions.Agoda.cacheRemote)
    .withSources()
    .changing()
    .exclude("io.netty", "netty")
    .exclude("commons-logging", "commons-logging")
    .exclude("de.ruedigermoeller", "fst"),
  ("com.agoda.whitelabel.client" %% "wl-client-scala" % Versions.Agoda.whiteLabelClient)
    .excludeAll(
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module")
    )
    .exclude("com.agoda.adp.messaging", "adp-messaging-client")
    .exclude("com.agoda.adp.messaging", "adp-messaging-log4j12"),
  ("com.agoda.commons" %% "ag-graphql-schema" % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
  "org.sangria-graphql" %% "sangria"                % Versions.Agoda.sangria,
  "com.agoda.commons"   %% "ag-http-client-v2"      % Versions.Agoda.agHttpClient,
  "com.agoda.commons"   %% "ag-http-client-mesh-v2" % Versions.Agoda.agHttpClient,
  ("com.agoda.commons"  %% "ag-http-server"         % Versions.Agoda.agHttpServer)
    .exclude("com.agoda.acm", s"traffic-forwarder-akka-http_${scalaBinaryVersion.value}")
    .excludeAll(ExclusionRule("com.thesamet.scalapb")),
  ("com.agoda.acm" %% "traffic-forwarder-akka-http" % Versions.Agoda.trafficForwarder)
    .exclude("com.agoda.acm", s"traffic-forwarder_${scalaBinaryVersion.value}"),
  ("com.agoda.acm" %% "traffic-forwarder" % Versions.Agoda.trafficForwarder)
    .exclude("com.agoda.adp.messaging.kafka", "kafka-clients")
    .exclude("com.agoda.commons", s"ag-vault-scala_${scalaBinaryVersion.value}")
    .exclude("com.fasterxml.jackson.module", s"jackson-module-scala_${scalaBinaryVersion.value}"),
  "ch.qos.logback" % "logback-classic" % Versions.logback,
  // CSV Parser
  "com.github.marklister" %% "product-collections" % Versions.productCollection % "test",
  "org.scalatestplus"     %% "mockito-3-4"         % Versions.scalaTestPlusMockito,

  /** External Dependencies
    */
  ("com.agoda.pricestream" %% "client" % Versions.priceStreamClient).excludeAll(
    ExclusionRule(organization = "com.typesafe.akka"),
    ExclusionRule(organization = "com.agoda.commons", name = "ag-vault_2.12")
  ),
  ("com.agoda.winterfell" %% "client" % Versions.capi).excludeAll(
    ExclusionRule(organization = "joda-time"),
    ExclusionRule(organization = "com.agoda.commons"),
    ExclusionRule(organization = "com.agoda.styx"),
    ExclusionRule(organization = "com.typesafe.play"),
    ExclusionRule(organization = "com.google.code.gson"),
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.typesafe.akka"),
    ExclusionRule(organization = "com.google.guava"),
    ExclusionRule(organization = "org.joda"),
    ExclusionRule(organization = "org.slf4j"),
    ExclusionRule(organization = "com.typesafe"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "io.atomicbits"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule(organization = "io.atomicbits"),
    ExclusionRule(organization = "de.ruedigermoeller"),
    ExclusionRule("de.heikoseeberger", s"akka-http-circe_${scalaBinaryVersion.value}")
  ),
  ("com.agoda.commons" %% "data" % Versions.Agoda.commons)
    .withSources()
    .changing()
    .exclude("de.ruedigermoeller", "fst")
    .exclude("com.agoda.experiments", "experiments-platform-client"),
  ("com.agoda.experiments" %% "experiments-platform-client"    % Versions.Agoda.experiment).withSources().changing(),
  "com.agoda.platform"     %% "service-context"                % Versions.Agoda.serviceContext,
  "com.agoda.platform"     %% "service-context-client-profile" % Versions.Agoda.serviceContext,
  "com.agoda.commons"      %% "ag-http-api-v2"                 % Versions.Agoda.agHttpClient,
  "com.agoda.commons"      %% "ag-tracing-core"                % Versions.Agoda.agTracing,
  "com.agoda.commons"      %% "ag-tracing-noop"                % Versions.Agoda.agTracing,
  "com.agoda.commons"      %% "ag-tracing-api"                 % Versions.Agoda.agTracing,
  "com.agoda.commons"       % "ag-observability-sdk"           % Versions.Agoda.agObservability,
  "com.agoda.commons"      %% "ag-observability-sdk-akka-http" % Versions.Agoda.agObservability,
  "io.opentelemetry"        % "opentelemetry-opentracing-shim" % Versions.openTelemetryAlpha,
  "org.slf4j"               % "jul-to-slf4j"                   % Versions.slf4j,
  "org.specs2"             %% "specs2-mock"                    % Versions.specs2 % Test,
  "com.agoda.commons"      %% "ag-concurrency"                 % Versions.Agoda.agConcurrency,
  ("com.agoda.commons"     %% "ag-couchbase"                   % Versions.Agoda.agCouchbase).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule(organization = "com.fasterxml.jackson.dataformat")
  ),
  ("com.agoda.connectivity" %% "supply-cache-sqlserver" % Versions.Agoda.supplyCacheSql).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule(organization = "com.fasterxml.jackson.databind"),
    ExclusionRule(organization = "com.typesafe.akka")
  ),
  ("com.agoda.papi" %% "cancellation" % Versions.Agoda.cancellation).excludeAll(
    ExclusionRule("com.beachape", s"enumeratum_${scalaBinaryVersion.value}")
  ),
  "com.agoda"          %% "supplier-common"         % Versions.Agoda.supplierCommon,
  "org.scaldi"         %% "scaldi"                  % Versions.scaldi,
  "com.agoda.commons"   % "ag-dynamic-state-consul" % Versions.Agoda.agConsul,
  ("com.agoda.commons" %% "serialization"           % Versions.Agoda.commons).changing(),
  "com.agoda.platform" %% "ag-cache-api"            % Versions.Agoda.agCache,
  "com.agoda.platform" %% "ag-cache-couchbase"      % Versions.Agoda.agCache,
  "com.agoda.platform" %% "ag-cache-caffeine"       % Versions.Agoda.agCache,
  "com.github.cb372"   %% "scalacache-caffeine"     % Versions.scalacache,
  "com.agoda.commons"  %% "ag-couchbase"            % Versions.Agoda.agCouchbase,
  ("com.agoda.commons"  % "ag-vault"                % Versions.Agoda.agVault)
    .excludeAll(
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module"),
      ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
      ExclusionRule(organization = "com.fasterxml.jackson.databind"),
      ExclusionRule(organization = "com.typesafe.akka")
    ),
  ("com.agoda.commons" %% "ag-vault-scala" % Versions.Agoda.agVault)
    .excludeAll(
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module"),
      ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
      ExclusionRule(organization = "com.fasterxml.jackson.databind"),
      ExclusionRule(organization = "com.typesafe.akka")
    ),
  ("com.agoda.commons" %% "sql"                              % Versions.Agoda.commons).withSources().changing().force(),
  "com.agoda.commons"  %% "ag-protobuf-scalapb-0-11-x-spapi" % Versions.Agoda.agProtobuf,
  ("com.agoda"         %% "supplier-pricing"                 % Versions.supplierPricing)
    .exclude("com.typesafe.akka", s"akka-cluster_${scalaBinaryVersion.value}")
    .exclude("com.typesafe.akka", s"akka-cluster-tools_${scalaBinaryVersion.value}")
    .exclude("com.agoda.commons", s"ag-grpc-client_${scalaBinaryVersion.value}")
    .exclude("com.agoda.connectivity", s"supply-cache-sqlserver_${scalaBinaryVersion.value}")
    .exclude("log4j", "log4j")
    .excludeAll(ExclusionRule(organization = "com.thesamet.scalapb"))
    .force(),
  "com.typesafe.akka" %% "akka-slf4j" % Versions.akka,
  // Promo API Client
  ("com.agoda.gandalf" %% "client" % Versions.Agoda.promoApi).excludeAll(
    ExclusionRule(organization = "com.softwaremill.sttp.model"),
    ExclusionRule(organization = "com.typesafe.akka"),
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule("com.agoda.ml", s"pricing_client_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.platform")
  ),
  // Need the upgraded version of opentelemetry-jdbc to solve https://gitlab.agodadev.io/common-libs/ag-sql/-/issues/129
  "io.opentelemetry.instrumentation" % "opentelemetry-jdbc" % Versions.openTelemetryAlpha,
  "com.microsoft.sqlserver"          % "mssql-jdbc"         % Versions.msJdbc,
  "com.agoda.commons"                % "ag-sql-hikari"      % Versions.Agoda.agSql,
  ("com.agoda.commons"              %% "ag-sql-scala"       % Versions.Agoda.agSql)
    .withSources()
    .changing()
    .force()
    .excludeAll(ExclusionRule(organization = "org.specs2")),
  "com.ecwid.consul" % "consul-api-shaded" % Versions.Agoda.consulClient,

  // Growth Program API Client
  "com.agoda.supply.growth.program"       % "gp-grpc-client" % Versions.Agoda.growthProgramClient,
  ("com.agoda.regulatoryblocking.client" %% "client"         % Versions.Agoda.regulatoryBlockingClient).excludeAll(
    ExclusionRule(organization = "io.circe"),
    ExclusionRule(organization = "com.agoda.platform"),
    ExclusionRule("com.agoda.commons", s"ag-vault-scala_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", "ag-observability-api")
  ),
  ("com.agoda.commons"      % "ag-jdbc-cache-driver"            % Versions.Agoda.agJdbcCache).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.core"),
    ExclusionRule(organization = "com.fasterxml.jackson.module"),
    ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
    ExclusionRule(organization = "com.fasterxml.jackson.databind"),
    ExclusionRule(organization = "com.typesafe.akka")
  )
)

libraryDependencies ++= featurestoreDependencies()

def featurestoreDependencies(): Seq[ModuleID] = {

  val scalaVersion = scalapb.compiler.Version.scalapbVersion
  val newVersion = scalaVersion match {
    case "0.9"  => "scalapb-0-9-7"
    case "0.11" => "scalapb-0-11-x"
    case _      => ""
  }
  val artifactSuffix = if (newVersion == "scalapb-0-9-7") s"_${`newVersion`}" else ""

  Seq(
    "com.agoda.featurestore" %% s"client$artifactSuffix"                 % Versions.Agoda.fsOnlineClient,
    "com.agoda.featurestore" %% s"client-online-caffeine$artifactSuffix" % Versions.Agoda.fsOnlineClient
  )
}
