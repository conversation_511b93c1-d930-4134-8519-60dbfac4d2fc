stryker4s {
  base-dir: "property"
  reporters: ["console", "html", "json"]
  scala-dialect: "2.12"
  mutate: [
    "!target/**",
    "src/main/**/*.scala"
  ],
  test-filter: [
    "!api.Cancellation.CancellationPolicyPhasesSpec.CancellationPolicyPhasesGMTSpec",
    "!flowTest.propertysearch.TopMasterRoomFilterSpec",
    "!com.agoda.papi.search.property.service.ClusterService.ExternalServiceSpec",
    "!api.Cancellation.CancellationPolicyPhasesSpec.CancellationPolicyPhasesSpec",
    "!api.EnrichedRoomSpec"
  ],
}
