name := "property"

update / aggregate := false
updateOptions      := updateOptions.value.withCachedResolution(true)

libraryDependencies ++= Seq(
  "com.agoda.commons"      %% "ag-http-server-api"          % Versions.Agoda.agHttpServer,
  "com.agoda.commons"      %% "ag-http-server-jackson"      % Versions.Agoda.agHttpServer,
  "org.scalatest"          %% "scalatest"                   % Versions.scalaTest            % "test",
  "org.scalatestplus"      %% "mockito-3-4"                 % Versions.scalaTestPlusMockito % "test",
  "org.specs2"             %% "specs2-core"                 % Versions.specs2               % "test",
  "org.specs2"             %% "specs2-junit"                % Versions.specs2               % "test",
  "org.scalamock"          %% "scalamock-scalatest-support" % Versions.scalaMock            % "test",
  "org.scaldi"             %% "scaldi"                      % Versions.scaldi,
  "org.scaldi"             %% "scaldi-akka"                 % Versions.scaldi,
  "org.mockito"             % "mockito-core"                % Versions.mockito              % "test",
  ("com.agoda.experiments" %% "experiments-platform-client" % Versions.Agoda.experiment).withSources().changing(),
  ("com.agoda.commons"     %% "cache-remote"                % Versions.Agoda.cacheRemote)
    .withSources()
    .changing()
    .exclude("io.netty", "netty")
    .exclude("commons-logging", "commons-logging")
    .exclude("de.ruedigermoeller", "fst"),
  ("com.agoda.commons" %% "data" % Versions.Agoda.commons)
    .withSources()
    .changing()
    .exclude("io.netty", "netty")
    .exclude("commons-logging", "commons-logging")
    .exclude("de.ruedigermoeller", "fst"),
  ("com.agoda.commons" %% "utils" % Versions.Agoda.commons)
    .withSources()
    .changing()
    .excludeAll(
      ExclusionRule("commons-logging", "commons-logging"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}")
    ),
  ("com.agoda.commons" %% "metrics" % Versions.Agoda.commons)
    .withSources()
    .changing()
    .exclude("commons-logging", "commons-logging"),
  ("com.agoda.commons" %% "localization" % Versions.Agoda.commons)
    .withSources()
    .changing()
    .exclude("commons-logging", "commons-logging"),
  ("com.agoda.commons" %% "traffic"         % Versions.Agoda.commons).withSources().changing(),
  ("com.agoda.commons" %% "sql"             % Versions.Agoda.commons).withSources().changing(),
  ("com.agoda.commons" %% "serialization"   % Versions.Agoda.commons).withSources().changing(),
  "com.agoda"          %% "supplier-common" % Versions.Agoda.supplierCommon,
  "com.agoda.papi"     %% "enum-commons"    % Versions.Agoda.enumcommons,

  // Http Service Discovery
  ("com.agoda.commons" %% "ag-graphql-schema" % Versions.Agoda.agGraphQL)
    .exclude("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
  "com.agoda.commons"   %% "ag-graphql-util" % Versions.Agoda.agGraphQL,
  "org.sangria-graphql" %% "sangria"         % Versions.Agoda.sangria,

  // Jackson
  "com.fasterxml.jackson.core"     % "jackson-core"            % Versions.jackson,
  "com.fasterxml.jackson.core"     % "jackson-databind"        % Versions.jackson,
  "com.fasterxml.jackson.core"     % "jackson-annotations"     % Versions.jackson,
  "com.fasterxml.jackson.module"  %% "jackson-module-scala"    % Versions.jackson,
  "com.fasterxml.jackson.datatype" % "jackson-datatype-joda"   % Versions.jackson,
  "com.fasterxml.jackson.datatype" % "jackson-datatype-jsr310" % Versions.jackson,

  // top level
  "com.typesafe.akka" %% "akka-remote"         % Versions.akka,
  "com.typesafe.akka" %% "akka-actor"          % Versions.akka,
  "com.typesafe.akka" %% "akka-slf4j"          % Versions.akka,
  "com.typesafe.akka" %% "akka-testkit"        % Versions.akka     % Test,
  "com.typesafe.akka" %% "akka-stream-testkit" % Versions.akka     % Test,
  "com.typesafe.akka" %% "akka-http-testkit"   % Versions.akkaHttp % Test,
  "com.typesafe.akka" %% "akka-http"           % Versions.akkaHttp,
  "com.typesafe.akka" %% "akka-http-core"      % Versions.akkaHttp % Test,

  // CSV Parser
  "com.github.marklister" %% "product-collections" % Versions.productCollection % "test",

  // Everest
  "com.agoda.everest" %% "sl-client"           % Versions.slService,
  "com.agoda.everest" %% "model-playjson"      % Versions.slService,
  "de.heikoseeberger" %% "akka-http-play-json" % Versions.akkaHttpPlayJson,
  "com.zaxxer"         % "HikariCP"            % Versions.hikari,
  "com.google.guava"   % "guava"               % Versions.guava,
  ("commons-logging"   % "commons-logging"     % Versions.commonsLogging).force(),
  "org.kitesdk"        % "kite-data-hive"      % Versions.kiteDataHive % "test",
  ("org.apache.hive"   % "hive-metastore" % Versions.hiveMetastore % "test").excludeAll(ExclusionRule("asm", "asm")),
  "org.apache.avro"    % "avro-mapred"    % Versions.avroMapred    % "test",
  "com.agoda.commons" %% "ag-logging"     % Versions.Agoda.logging,
  "com.agoda.commons" %% "ag-tracing-rpc" % Versions.Agoda.agTracing,
  "com.agoda.commons" %% "ag-tracing-rpc-grpc" % Versions.Agoda.agTracing,
  "io.netty"           % "netty-all"           % Versions.netty,
  "io.netty"           % "netty-handler"       % Versions.netty,
  ("com.agoda.dfapi"  %% "models-circe"        % Versions.Agoda.dfapi)
    .excludeAll(
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule(organization = "com.agoda.experiments"),
      ExclusionRule(organization = "com.agoda.upi"), // ToDo: temporary to unblock CI. Have to be removed
      ExclusionRule("com.agoda.papi-streaming", s"models_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.dfapi", s"dfapi_models_${scalaBinaryVersion.value}"),
      ExclusionRule("com.thesamet.scalapb", s"scalapb-json4s_${scalaBinaryVersion.value}")
    ),
  ("com.agoda.commons" %% "ag-protobuf-scalapb-0-11-x-property" % Versions.Agoda.agProtobufProperty)
    .excludeAll(
      ExclusionRule("io.grpc"),
      ExclusionRule(organization = "com.typesafe.akka"),
      ExclusionRule("io.opentracing"),
      ExclusionRule(organization = "org.specs2"),
      ExclusionRule(organization = "org.scalaz"),
      ExclusionRule("com.agoda", s"supplier-pricing_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda", s"rflows_${scalaBinaryVersion.value}"),
      ExclusionRule("com.beachape", s"enumeratum_${scalaBinaryVersion.value}"),
      ExclusionRule("com.thesamet.scalapb", s"scalapb-runtime_${scalaBinaryVersion.value}"),
      ExclusionRule("com.thesamet.scalapb", s"scalapb-runtime-grpc_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda", s"rflows_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.platform", s"ag-grpc-client_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda", s"ypl_cancellation_${scalaBinaryVersion.value}"),
      ExclusionRule(organization = "com.agoda.everest"),
      ExclusionRule(organization = "com.thesamet.scalapb")
    ),
  ("com.agoda.bundler" %% "packaging" % Versions.Agoda.bundler).excludeAll(
    ExclusionRule(organization = "com.fasterxml.jackson.module")
  ),
  // White label client
  ("com.agoda.whitelabel.client" %% "wl-client-scala" % Versions.Agoda.whiteLabelClient)
    .excludeAll(
      ExclusionRule(organization = "com.fasterxml.jackson.core"),
      ExclusionRule(organization = "com.fasterxml.jackson.module")
    )
    .exclude("com.agoda.adp.messaging", "adp-messaging-client")
    .exclude("com.agoda.adp.messaging", "adp-messaging-log4j12"),
  "com.agoda.adp.messaging"      % "adp-messaging-client-testutils" % Versions.adpMessaging % "test",
  ("com.agoda.content-platform" %% "models-circe"                   % Versions.Agoda.contentPlatform).excludeAll(
    ExclusionRule("com.agoda.commons", s"cache-remote_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"data_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"utils_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"metrics_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"sql_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"ag-couchbase_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.commons", s"ag-sql_${scalaBinaryVersion.value}"),
    ExclusionRule("com.zaxxer", "HikariCP-java6"),
    ExclusionRule("com.sksamuel.elastic4s"),
    ExclusionRule(organization = "org.apache.lucene"),
    ExclusionRule(organization = "org.apache.logging.log4j").withConfigurations(Vector(ConfigRef("test"))),
    ExclusionRule("com.softwaremill.sttp.tapir"),
    ExclusionRule("com.agoda.platform", s"service-context_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.platform", s"service-context-akka-http_${scalaBinaryVersion.value}"),
    ExclusionRule("com.agoda.platform", s"service-context-client-profile_${scalaBinaryVersion.value}")
  ),
  "com.thesamet.scalapb" %% "scalapb-json4s"               % Versions.json4s,
  ("com.agoda.ml"        %% "zenith_client_scalapb-0-11-x" % Versions.zenithClient).excludeAll(
    ExclusionRule("com.agoda.ml", "zenith_model_scalapb-0-11-x_2.12"),
    ExclusionRule("com.agoda.commons", "ag-http-client-v2_2.12"),
    ExclusionRule("com.agoda.commons", "ag-http-client-mesh-v2_2.12")
  ),
  "com.agoda.ml"                  %% "zenith_model_scalapb-0-11-x"      % Versions.zenith_model,
  "com.agoda.commons"             %% "configuration"                    % Versions.Agoda.agConfiguration,
  "com.agoda.commons"             %% "ag-http-client-v2"                % Versions.Agoda.agHttpClient,
  "com.agoda.commons"             %% "ag-http-client-mesh-v2"           % Versions.Agoda.agHttpClient,
  "com.softwaremill.sttp.client3" %% "async-http-client-backend-future" % Versions.sttp
)
  .map(
    _.exclude("com.agoda.dcs", s"dcs_logging_${scalaBinaryVersion.value}")
      .exclude("commons-logging", "commons-logging")
      .exclude("org.slf4j", "slf4j-log4j12")
      .exclude("org.mockito", "mockito-all")
  )

libraryDependencies += "com.typesafe.scala-logging" %% "scala-logging" % Versions.scalalog

libraryDependencies += ("com.agoda.commons" %% "ag-couchbase" % Versions.Agoda.agCouchbase).excludeAll(
  ExclusionRule(organization = "com.fasterxml.jackson.core"),
  ExclusionRule(organization = "com.fasterxml.jackson.module"),
  ExclusionRule(organization = "com.fasterxml.jackson.datatype"),
  ExclusionRule(organization = "com.fasterxml.jackson.dataformat")
)

libraryDependencies += "com.agoda.commons" %% "ag-grpc-client" % Versions.Agoda.agGrpcV0

libraryDependencies += ("com.agoda.testing" % "fake-api-framework" % Versions.Agoda.agodaJvmTesting % Test)
  .exclude("com.agoda.commons", "ag-observability-api")

libraryDependencies ~= { _.map(moduleID => Versions.commonExclude(moduleID)) }
