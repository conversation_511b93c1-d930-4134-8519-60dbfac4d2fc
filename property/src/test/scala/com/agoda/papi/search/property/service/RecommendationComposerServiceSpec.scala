package com.agoda.papi.search.property.service

import com.agoda.content.models.{ PropertyId, PropertyResponse }
import com.agoda.ml.recommendation.zenith.common.RecommendationType
import com.agoda.ml.recommendation.zenith.common.RecommendationType.{
  PastBookingsRecommendation,
  RecentlyViewRecommendation
}
import com.agoda.papi.search.common.util.context.{ ContextHolder, DegradeFeaturesContext }
import com.agoda.papi.search.internalmodel.DegradeFeatures
import com.agoda.papi.search.property.service.ClusterService.ExternalServiceSpecSetup
import enumerations.{ RecommendationFilterComparisonTypes, RecommendationFilterFieldTypes }
import framework.repository.LanguageRepositoryTest
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.{ reset, when }
import org.scalatest.BeforeAndAfterEach
import org.scalatest.prop.TableDrivenPropertyChecks
import org.scalatest.wordspec.AnyWordSpec
import request.{ PropertyRequest, Recommendation, RecommendationFilter }
import types.{ CityIdType, HotelIdType }
import util.builders.TestDataBuilders._

import java.lang.Math.max
import scala.collection.mutable
import scala.concurrent.{ ExecutionContext, Future }

class RecommendationComposerServiceSpec
    extends AnyWordSpec with ExternalServiceSpecSetup with BeforeAndAfterEach with TableDrivenPropertyChecks {

  private val numberOfRecommendations    = 4L
  private val recommendationRequest      = Recommendation(numberOfRecommendations.toInt, List.empty)
  private val propertyRequestWithPricing = aValidPropertyRequest.withPricing(aValidPricingRequest).build()
  override val contextHolder             = createContextHolderFromRequest(propertyRequestWithPricing)

  override def beforeEach(): Unit = {
    // Reset mockZenith and set Default return value
    reset(mockZenithService)
    when(
      mockZenithService.getRecommendations(
        propertyId = any[PropertyId](),
        request = any[PropertyRequest](),
        recommendationRequest = any[Recommendation],
        recommendationType = any[RecommendationType]
      )(any(), any(), any())
    ).thenReturn(Future.successful(None))
    when(
      mockZenithService.getCityRecommendations(
        cityId = any[CityIdType](),
        request = any[PropertyRequest](),
        recommendationRequest = any[Recommendation](),
        recommendationType = any[RecommendationType]()
      )(any(), any(), any())
    ).thenReturn(Future.successful(None))
  }

  "filter recommendation response" should {
    "respond with exactly number of requested recommendations in the request if all of them satisfy the rules" in {
      val availableRecos = List.fill(8)(aValidZenithResponse)
      val filteredResponse = testExternalService.filterRecommendation(
        availableRecos,
        recommendationRequest,
        Map.empty
      )(propertyRequestWithPricing, contextHolder)

      filteredResponse.size shouldBe 4
    }
    "respond with exact number of requested recommendations when pricing is not requested" in {
      val availableRecos = List.fill(8)(aValidZenithResponse)
      val filteredResponse = testExternalService
        .filterRecommendation(availableRecos, recommendationRequest, Map.empty)(
          aValidPropertyRequest,
          contextHolder
        ) // No pricing request

      filteredResponse.size shouldBe 4
    }

    "respond with exact number of recommendations if sold out properties are requested using feature flag" in {
      val requestWithFeature =
        aValidPropertyRequest.withFeatureFlag(
          aValidFeatureFlagRequest
            .copy(soldOutSimilarProperties = Some(true))
        )

      val availableRecos = List(aValidZenithResponse, zenithResponseSoldOut, aValidZenithResponse, aValidZenithResponse)

      val filteredResponse = testExternalService.filterRecommendation(
        availableRecos,
        recommendationRequest,
        Map.empty
      )(requestWithFeature, contextHolder)

      filteredResponse.size shouldBe 4
    }
  }

  "testExternalService" should {
    "be able to build property request" in {

      val overridingIds  = List(100L, 200L, 300L)
      val recommendation = Recommendation(4, overridingIds)

      // JarvisService get recommendation from personalizedRecommendation field, so we put recommendation there.
      val incomingRecommendationRequest = aValidPropertyRequest.copy(personalizedRecommendation = Some(recommendation))

      val recommendedIds = List(1L, 2L, 3L, 4L, 5L)

      val request = testExternalService.buildPropertyRequest(
        incomingRecommendationRequest,
        overridingIds ++ recommendedIds
      )

      assert(request.context.propertyIds === List(100L, 200L, 300L, 1L, 2L, 3L, 4L, 5L))
    }
  }

  "compose recommendation list" should {
    implicit val ec: ExecutionContext       = ecMock
    implicit val lr: LanguageRepositoryTest = mockLanguageRepository
    implicit val cmsService: PAPICMSService = mockCmsService
    implicit val ctxHolder: ContextHolder   = contextHolder

    val inputPropertyIds = List(1L, 2L, 3L, 4L, 5L)
    val zenithRecommendation = Map(
      1L -> List(12L, 15L, 17L, 10L),
      3L -> List(31L, 35L, 37L, 39L, 40L),
      4L -> List(41L)
    )
    val jarvisRecommendation = Map(
      1L -> List(41L, 31L, 21L, 61L),
      3L -> List(23L, 43L, 63L, 13L, 3L),
      4L -> List(14L),
      5L -> List.empty // Empty property from Jarvis
    )

    "mockZenithService return correct recommendation" in {
      mockZenithServiceResponse(zenithRecommendation)
      inputPropertyIds.foreach { propertyId =>
        val zenithResponseOption = mockZenithService
          .getRecommendations(
            propertyId,
            propertyRequestWithPricing,
            recommendationRequest,
            RecommendationType.RecentlyViewRecommendation
          )

        whenReady(zenithResponseOption) { zenithResponse =>
          zenithResponse.map(_.hotelId) shouldBe zenithRecommendation.get(propertyId)
        }
      }
    }

    "compose recommendation list with Zenith return Empty recommendation" in {
      mockZenithServiceResponse(Map())
      val request = PropertyRequest(
        context = aValidContext
      )
      val responseFuture = testExternalService.composeComparisonRecommendationList(request)
      whenReady(responseFuture) {
        _ shouldBe Map()
      }
    }

    "compose recommendation list with Jarvis return Empty recommendation when PERSO-3497 is B" in {
      mockJarvisServiceResponse(Map())
      val request = PropertyRequest(
        context = aValidContext
      )
      val contextHolder = createContextHolderFromRequest(
        request,
        experimentContext = new ForceBExperimentContext(
          forceExperimentNames = Some(List("PERSO-3497"))
        )
      )
      val responseFuture =
        testExternalService.composeComparisonRecommendationList(request)(ec, lr, cmsService, contextHolder)
      whenReady(responseFuture) {
        _ shouldBe Map()
      }
    }

    "compose recommendation list with Zenith throw exception" in {
      mockZenithServiceFailureResponse()

      val request        = createPropertyRequest(List(1L), 1)
      val responseFuture = testExternalService.composeComparisonRecommendationList(request)
      whenReady(responseFuture) {
        _ shouldBe Map(1L -> List.empty)
      }
    }

    "compose recommendation list with Jarvis throw when PERSO-3497 is B" in {

      when(mockJarvisService.getSimilarPropertiesResponse(any(), any(), any(), any())(any(), any(), any()))
        .thenReturn(
          Future.failed(new RuntimeException("Jarvis Service Down"))
        )

      val request = createPropertyRequest(List(1L), 1)
      val contextHolder = createContextHolderFromRequest(
        request,
        experimentContext = new ForceBExperimentContext(
          forceExperimentNames = Some(List("PERSO-3497"))
        )
      )
      val responseFuture =
        testExternalService.composeComparisonRecommendationList(request)(ec, lr, cmsService, contextHolder)
      whenReady(responseFuture) {
        _ shouldBe Map(1L -> List.empty)
      }
    }

    "compose recommendation list return Empty recommendation" in {
      val request = createPropertyRequest()
      mockZenithServiceResponse(Map())

      val responseFuture = testExternalService
        .composeRecentlyViewedRecommendationList(request, Map.empty[HotelIdType, CityIdType])

      whenReady(responseFuture) {
        _ shouldBe Map()
      }
    }

    "compose recently viewed recommendation list throw exception" in {
      val request = createPropertyRequest(List(1L))
      mockZenithServiceFailureResponse()

      val responseFuture = testExternalService
        .composeRecentlyViewedRecommendationList(request, Map(1L -> 1L))

      whenReady(responseFuture) {
        _ shouldBe Map(1L -> List.empty)
      }
    }

    "compose past booking recommendation return Empty recommendation" in {
      val request = createPropertyRequest()
      mockZenithServiceResponse(Map())

      val responseFuture = testExternalService
        .composePastBookingsRecommendationList(request, Map.empty[HotelIdType, CityIdType])

      whenReady(responseFuture) {
        _ shouldBe Map()
      }
    }

    "compose past booking recommendation throw exception" in {
      val request = createPropertyRequest(List(1L))
      mockZenithServiceFailureResponse()

      val responseFuture = testExternalService
        .composePastBookingsRecommendationList(request, Map(1L -> 1L))

      whenReady(responseFuture) {
        _ shouldBe Map(1L -> List.empty)
      }
    }

    val recommendationList = (zenithRecommendation.values ++ jarvisRecommendation.values).toSeq

    "externalService compose past booking from Zenith return correct recommendation list" in {
      val PastBookingsNumberOfRecommendation = 10

      reset(mockRequestBuilderService)
      reset(mockDfRecommendationService)
      reset(mockSummaryService)
      when(mockSummaryService.process(any(), any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(mutable.Map.empty[Long, PropertyResponse]))

      val request = createPropertyRequest(
        propertyIds = inputPropertyIds,
        pastBookingsRecommendationAmount = PastBookingsNumberOfRecommendation
      )

      mockGettingRecoPropertyInfo(recommendationList, PastBookingsNumberOfRecommendation, request)

      mockZenithServiceCityResponse(
        jarvisRecommendation,
        aValidHotelToCityId,
        expectedRequest = Some(request),
        expectedRecommendationType = Some(PastBookingsRecommendation)
      )

      val responseFuture = testExternalService.composePastBookingsRecommendationList(request, aValidHotelToCityId)

      val expectedRecommendationResult = Map(
        1 -> List(41, 31, 21, 61),
        2 -> List.empty,
        3 -> List(23, 43, 63, 13, 3),
        4 -> List(14),
        5 -> List.empty
      )

      whenReady(responseFuture) { response =>
        response.mapValues(_.flatMap(_.propertyId)) should be(expectedRecommendationResult)
      }
    }

    "externalService compose past booking from Jarvis return correct empty list because of feature degradation" in {
      val PastBookingsNumberOfRecommendation = 10

      reset(mockRequestBuilderService)
      reset(mockDfRecommendationService)
      reset(mockSummaryService)
      when(mockSummaryService.process(any(), any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(mutable.Map.empty[Long, PropertyResponse]))

      val request = createPropertyRequest(
        propertyIds = inputPropertyIds,
        pastBookingsRecommendationAmount = PastBookingsNumberOfRecommendation
      )

      mockGettingRecoPropertyInfo(recommendationList, PastBookingsNumberOfRecommendation, request)

      val degradeContextHolder = createContextHolderFromRequest(
        request,
        degradeFeaturesContext = Some(DegradeFeaturesContext(Set(DegradeFeatures.RecommendationProperties)))
      )
      val responseFuture = testExternalService
        .composePastBookingsRecommendationList(request, aValidHotelToCityId)(ec, lr, cmsService, degradeContextHolder)

      val expectedRecommendationResult = Map(
        1 -> List.empty,
        2 -> List.empty,
        3 -> List.empty,
        4 -> List.empty,
        5 -> List.empty
      )

      whenReady(responseFuture) { response =>
        response.mapValues(_.flatMap(_.propertyId)) should be(expectedRecommendationResult)
      }
    }

    val testCases = Table(
      ("zenithRecommendationNumber", "jarvisRecommendationNumber"),
      (4, 0),
      (2, 0),
      (0, 4),
      (0, 2),
      (4, 4),
      (0, 0)
    )

    forAll(testCases) { (zenithRecommendationNumber: Int, jarvisRecommendationNumber: Int) =>
      s"externalService compose zenith and jarvis return correct recommendation list $zenithRecommendationNumber $jarvisRecommendationNumber" in {
        reset(mockRequestBuilderService)
        reset(mockDfRecommendationService)
        reset(mockSummaryService)
        when(mockSummaryService.process(any(), any(), any(), any(), any())(any()))
          .thenReturn(Future.successful(mutable.Map.empty[Long, PropertyResponse]))
        mockZenithServiceResponse(zenithRecommendation)

        val incomingRequest = createPropertyRequest(
          propertyIds = inputPropertyIds,
          zenithRecommendationNumber,
          jarvisRecommendationNumber
        )

        mockZenithServiceCityResponse(
          jarvisRecommendation,
          aValidHotelToCityId,
          expectedRequest = Some(incomingRequest),
          expectedRecommendationType = Some(RecentlyViewRecommendation)
        )

        // Set Expected Output for each services (zenith, requestBuilder, DragonFruit, summary)
        mockGettingRecoPropertyInfo(
          recommendationList,
          max(zenithRecommendationNumber, jarvisRecommendationNumber),
          incomingRequest
        )

        val expectedZenithRecommendations =
          if (zenithRecommendationNumber == 0) Map.empty
          else
            inputPropertyIds
              .map(propertyId =>
                propertyId -> zenithRecommendation
                  .getOrElse(propertyId, List.empty)
                  .slice(0, zenithRecommendationNumber)
                  .map(Some(_))
              )
              .toMap

        val expectedJarvisRecommendations = inputPropertyIds
          .map(propertyId =>
            propertyId -> jarvisRecommendation
              .getOrElse(propertyId, List.empty)
              .slice(0, jarvisRecommendationNumber)
              .map(Some(_))
          )
          .toMap

        mockZenithServiceCityResponse(
          jarvisRecommendation,
          aValidHotelToCityId,
          expectedRequest = Some(incomingRequest),
          expectedRecommendationType = Some(RecentlyViewRecommendation)
        )

        val jarvisResponse = testExternalService
          .composeRecentlyViewedRecommendationList(incomingRequest, aValidHotelToCityId)

        whenReady(jarvisResponse) {
          _.mapValues(_.map(_.propertyId)) shouldBe expectedJarvisRecommendations
        }
      }
    }

    "Should always be running on" in {
      runComposeRecommendationList(createContextHolderFromRequest(propertyRequestWithPricing))
    }

    "Recommendation service get NumberOf Recommendations " in {
      val noOfRecommendations = RecommendationService.getNumberOfRecommendations(2)(contextHolder)
      noOfRecommendations shouldBe 2
    }

    def runComposeRecommendationList(contextHolder: ContextHolder): Unit =
      forAll(testCases) { (recommendationAmount: Int, personalizedRecommendationAmount: Int) =>
        reset(mockRequestBuilderService)
        reset(mockDfRecommendationService)
        reset(mockSummaryService)
        when(mockSummaryService.process(any(), any(), any(), any(), any())(any()))
          .thenReturn(Future.successful(mutable.Map.empty[Long, PropertyResponse]))

        val incomingRequest = createPropertyRequest(
          propertyIds = inputPropertyIds,
          recommendationAmount,
          personalizedRecommendationAmount
        )

        mockZenithServiceResponse(zenithRecommendation)
        mockJarvisServiceResponse(zenithRecommendation)
        mockZenithServiceCityResponse(
          jarvisRecommendation,
          aValidHotelToCityId,
          expectedRequest = Some(incomingRequest)
        )

        val propertiesMissingInContent = Set(12L, 41L)
        // Set Expected Output for each services (zenith, requestBuilder, DragonFruit, summary)
        mockGettingRecoPropertyInfo(
          recommendationList,
          max(recommendationAmount, personalizedRecommendationAmount),
          incomingRequest,
          propertiesMissingInContent
        )

        val expectedZenithRecommendations =
          if (recommendationAmount == 0) Map.empty
          else
            inputPropertyIds
              .map(propertyId =>
                propertyId -> zenithRecommendation
                  .getOrElse(propertyId, List.empty)
                  .filterNot(propertiesMissingInContent.contains)
                  .slice(0, recommendationAmount)
                  .map(Some(_))
              )
              .toMap

        val expectedJarvisRecommendations = inputPropertyIds
          .map(propertyId =>
            propertyId -> jarvisRecommendation
              .getOrElse(propertyId, List.empty)
              .filterNot(propertiesMissingInContent.contains)
              .slice(0, personalizedRecommendationAmount)
              .map(Some(_))
          )
          .toMap

        testExternalService.summaryService
        val zenithResponse = testExternalService
          .composeComparisonRecommendationList(incomingRequest)(ec, lr, cmsService, contextHolder)

        whenReady(zenithResponse) {
          _.mapValues(_.map(_.propertyId)) shouldBe expectedZenithRecommendations
        }

        val similarPropertiesResponse = testExternalService
          .composeComparisonRecommendationList(incomingRequest)(
            ec,
            lr,
            cmsService,
            contextHolder.copy(experimentContext =
              new ForceBExperimentContext(
                forceExperimentNames = Some(List("PERSO-3497"))
              )
            )
          )

        whenReady(similarPropertiesResponse) {
          _.mapValues(_.map(_.propertyId)) shouldBe expectedZenithRecommendations
        }

        val jarvisResponse = testExternalService
          .composeRecentlyViewedRecommendationList(incomingRequest, aValidHotelToCityId)(
            ec,
            lr,
            cmsService,
            contextHolder
          )

        whenReady(jarvisResponse) {
          _.mapValues(_.map(_.propertyId)) shouldBe expectedJarvisRecommendations
        }
      }

    val moreStars35 = RecommendationFilter(
      RecommendationFilterFieldTypes.StarRating,
      RecommendationFilterComparisonTypes.More,
      35L
    )

    val equalPrice35 = RecommendationFilter(
      RecommendationFilterFieldTypes.Price,
      RecommendationFilterComparisonTypes.Equal,
      35L
    )

    val lessReview35 = RecommendationFilter(
      RecommendationFilterFieldTypes.ReviewScore,
      RecommendationFilterComparisonTypes.Less,
      35L
    )

    Table(
      ("name", "filters", "expected"),
      ("No filters", Option.empty[List[RecommendationFilter]], List((31, None), (35, None), (37, None))),
      ("More stars", Some(List(moreStars35)), List((37, Some(moreStars35)), (35, None), (31, None))),
      (
        "Equal price",
        Some(List(equalPrice35)),
        List((35, Some(equalPrice35)), (31, Some(equalPrice35)), (39, Some(equalPrice35)))
      ),
      ("Less review score", Some(List(lessReview35)), List((37, Some(lessReview35)), (35, None), (31, None))),
      (
        "2 filters",
        Some(List(equalPrice35, lessReview35)),
        List((37, Some(lessReview35)), (35, Some(equalPrice35)), (31, Some(equalPrice35)))
      )
    ).forEvery { (name, filters, expected) =>
      s"filters are applied correctly when $name" in {
        val recommendationRequest = Recommendation(3, Nil, filters = filters)

        val incomingRequest = createPropertyRequest(propertyIds = List(6L), 3, 3)
          .copy(recommendation = Some(recommendationRequest))

        mockPropertyRecommendationWithCustomResponse(
          zenithRecommendation = Map(6L -> List(31L, 35L, 37L, 39L, 40L)),
          propertyRequest = incomingRequest,
          starRatings = Map(37L -> 5d),
          reviewScores = Map(37L -> 1d),
          prices = Map(37L -> 10d)
        )

        val zenithResponse = testExternalService
          .composeComparisonRecommendationList(incomingRequest)(ec, lr, cmsService, contextHolder)

        whenReady(zenithResponse) { res =>
          res(6L).map(rec => (rec.propertyId.get, rec.filteredBy)) shouldBe expected
        }
      }
    }
  }

  "get number of recommendation" should {
    "be able to get number of recommendations from normal recommendation request" in {
      val incomingRequest = PropertyRequest(
        context = aValidContext,
        recommendation = Some(Recommendation(4, List.empty)),
        personalizedRecommendation = None,
        pastBookingsRecommendation = None
      )

      val result = testExternalService.getNumberOfRecommendations(incomingRequest)
      result should be(Some(4))
    }

    "be able to get number of recommendations from personalized recommendation request" in {
      val incomingRequest = PropertyRequest(
        context = aValidContext,
        recommendation = None,
        personalizedRecommendation = Some(Recommendation(4, List.empty)),
        pastBookingsRecommendation = None
      )

      val result = testExternalService.getNumberOfRecommendations(incomingRequest)
      result should be(Some(4))
    }

    "be able to get number of recommendations from pastbookings recommendation request" in {
      val incomingRequest = PropertyRequest(
        context = aValidContext,
        recommendation = None,
        personalizedRecommendation = None,
        pastBookingsRecommendation = Some(Recommendation(4, List.empty))
      )

      val result = testExternalService.getNumberOfRecommendations(incomingRequest)
      result should be(Some(4))
    }
  }

  "filterValidUpTierPropertyRecommendation" should {
    "return true when recommended property price is within valid range (0.9-1.2) of reference price" in {
      val referencePrice = 100.0
      val recommendedHotel = aValidZenithHotel.copy(
        cheapestPriceCrossedDisplay = aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(100.0)).build()
      )

      val result = testExternalService.filterValidUpTierPropertyRecommendation(
        Some(recommendedHotel),
        Some(referencePrice)
      )(propertyRequestWithPricing)

      result shouldBe true
    }

    "return false when recommended property price is too expensive (>1.2x reference price)" in {
      val referencePrice = 100.0
      val recommendedHotel = aValidZenithHotel.copy(
        cheapestPriceCrossedDisplay = aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(130.0)).build()
      )

      val result = testExternalService.filterValidUpTierPropertyRecommendation(
        Some(recommendedHotel),
        Some(referencePrice)
      )(propertyRequestWithPricing)

      result shouldBe false
    }

    "return false when recommended property price is too cheap (<0.9x reference price)" in {
      val referencePrice = 100.0
      val recommendedHotel = aValidZenithHotel.copy(
        cheapestPriceCrossedDisplay = aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(80.0)).build()
      )

      val result = testExternalService.filterValidUpTierPropertyRecommendation(
        Some(recommendedHotel),
        Some(referencePrice)
      )(propertyRequestWithPricing)

      result shouldBe false
    }

    "return false when reference price is missing" in {
      val recommendedHotel = aValidZenithHotel.copy(
        cheapestPriceCrossedDisplay = aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(100.0)).build()
      )

      val result = testExternalService.filterValidUpTierPropertyRecommendation(
        Some(recommendedHotel),
        None
      )(propertyRequestWithPricing)

      result shouldBe false
    }

    "return false when recommended hotel is None" in {
      val referencePrice = 100.0

      val result = testExternalService.filterValidUpTierPropertyRecommendation(
        None,
        Some(referencePrice)
      )(propertyRequestWithPricing)

      result shouldBe false
    }

    "return false when reference price is zero" in {
      val referencePrice = 0.0
      val recommendedHotel = aValidZenithHotel.copy(
        cheapestPriceCrossedDisplay = aValidDisplayBasis.withPerBook(aValidDisplayPrice.withAllInclusive(110.0)).build()
      )

      val result = testExternalService.filterValidUpTierPropertyRecommendation(
        Some(recommendedHotel),
        Some(referencePrice)
      )(propertyRequestWithPricing)

      result shouldBe false
    }
  }

}
