package com.agoda.papi.search.property.api.validation

import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec
import util.SampleRequests

class AreaSearchValidationRulesSpec extends AnyWordSpec with Matchers with SampleRequests {

  "Area search validation" should {
    "return no failure if area id > 0" in {
      val failure = AreaSearchValidationRules.validate(sampleAreaSearchRequest.copy(areaId = 1))
      failure shouldBe None
    }

    "return failure if area id <= 0" in {
      val failure = AreaSearchValidationRules.validate(sampleAreaSearchRequest.copy(areaId = 0))
      failure shouldBe Some(ValidationFailure("AreaID must be greater than 0."))
    }
  }

}
