import com.agoda.papi.search.property.service.recommendedUpgrade.{
  RecommendedUpgradeService,
  RecommendedUpgradeServiceImpl,
  UpgradeContext
}
import models.pricing.enums.ApplyTypes
import models.starfruit.SuggestedPrice
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import transformers.{ EnrichedMasterRoom, RecommendedUpgrade }
import util.builders.TestDataBuilders._

class RecommendedUpgradeServiceImplSpec extends AnyFunSpec with Matchers {

  val recommendedUpgradeService: RecommendedUpgradeService = new RecommendedUpgradeServiceImpl

  def mockMasterRoom(size: Double, price: Double, currency: String = "USD"): EnrichedMasterRoom =
    aValidEnrichedMasterRoom
      .withFeatures(Vector(aValidFeatureResponse.copy(name = "room-size-sqft", text = size.toString)))
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withPricing(
              Map(
                currency -> aValidEnrichedPricing
                  .withDisplay(
                    aValidDisplayBasis
                      .withPerRoomPerNight(aValidDisplayPrice.withAllInclusive(price).withExclusive(price))
                      .withPerNight(aValidDisplayPrice.withAllInclusive(price).withExclusive(price))
                      .withPerBook(aValidDisplayPrice.withAllInclusive(price).withExclusive(price))
                  )
                  .build()
              )
            )
            .build()
        )
      )
      .build()

  describe("RecommendedUpgradeServiceImpl.enrichUpgradeRecommendation") {
    it("should add recommendedUpgrade to the first eligible candidate and stop further processing") {
      val baseRoom   = mockMasterRoom(100, 100)
      val candidate1 = mockMasterRoom(109, 104) // 9% size, 4% price (should NOT upgrade)
      val candidate2 = mockMasterRoom(115, 110) // 15% size, 10% price (should upgrade)
      val candidate3 = mockMasterRoom(130, 112) // Would also qualify, but should not be marked

      val context = UpgradeContext(Some(ApplyTypes.PRPN), Some(SuggestedPrice.AllInclusive), Some("USD"))
      val result = recommendedUpgradeService.enrichUpgradeRecommendation(
        List(baseRoom, candidate1, candidate2, candidate3),
        context
      )

      result(1).recommendedUpgrade shouldBe None                                       // candidate1 not eligible
      result(2).recommendedUpgrade shouldBe Some(RecommendedUpgrade(10.0, Some(15.0))) // candidate2 is first eligible
      result(3).recommendedUpgrade shouldBe None                                       // candidate3 not checked
    }

    it("should stop processing further candidates if price diff > 15% is encountered") {
      val baseRoom   = mockMasterRoom(100, 100)
      val candidate1 = mockMasterRoom(109, 104) // 9% size, 4% price (should NOT upgrade)
      val candidate2 = mockMasterRoom(120, 120) // 20% size, 20% price (should NOT upgrade, price diff > 15%)
      val candidate3 = mockMasterRoom(130, 112) // Would qualify, but should not be checked

      val context = UpgradeContext(Some(ApplyTypes.PRPN), Some(SuggestedPrice.AllInclusive), Some("USD"))
      val result = recommendedUpgradeService.enrichUpgradeRecommendation(
        List(baseRoom, candidate1, candidate2, candidate3),
        context
      )

      result(1).recommendedUpgrade shouldBe None // candidate1 not eligible
      result(2).recommendedUpgrade shouldBe None // candidate2 not eligible, price diff > 15%
      result(3).recommendedUpgrade shouldBe None // candidate3 not checked
    }

    it("should not add recommendedUpgrade if no candidate qualifies") {
      val baseRoom   = mockMasterRoom(100, 100)
      val candidate1 = mockMasterRoom(109, 104) // 9% size, 4% price (should NOT upgrade)
      val candidate2 = mockMasterRoom(115, 120) // 15% size, 20% price (should NOT upgrade, price diff > 15%)

      val context = UpgradeContext(Some(ApplyTypes.PRPN), Some(SuggestedPrice.AllInclusive), Some("USD"))
      val result = recommendedUpgradeService.enrichUpgradeRecommendation(
        List(baseRoom, candidate1, candidate2),
        context
      )

      result(1).recommendedUpgrade shouldBe None
      result(2).recommendedUpgrade shouldBe None
    }

    it("should return the original list if less than 2 rooms") {
      val baseRoom = mockMasterRoom(100, 100)
      val context  = UpgradeContext(Some(ApplyTypes.PRPN), Some(SuggestedPrice.AllInclusive), Some("USD"))
      val result = recommendedUpgradeService.enrichUpgradeRecommendation(
        List(baseRoom),
        context
      )
      result shouldBe List(baseRoom)
    }

    it("should not add recommendedUpgrade for any invalid or zero price/size combination") {
      val testCases = Seq(
        ("base price zero", mockMasterRoom(100, 0), mockMasterRoom(130, 109)),
        ("candidate price zero", mockMasterRoom(100, 100), mockMasterRoom(130, 0)),
        ("base size zero", mockMasterRoom(0, 100), mockMasterRoom(130, 109)),
        ("candidate size zero", mockMasterRoom(100, 100), mockMasterRoom(0, 109)),
        ("both prices zero", mockMasterRoom(100, 0), mockMasterRoom(130, 0)),
        ("both sizes zero", mockMasterRoom(0, 100), mockMasterRoom(0, 109)),
        ("base price negative", mockMasterRoom(100, -10), mockMasterRoom(130, 109)),
        ("candidate price negative", mockMasterRoom(100, 100), mockMasterRoom(130, -10)),
        ("base size negative", mockMasterRoom(-10, 100), mockMasterRoom(130, 109)),
        ("candidate size negative", mockMasterRoom(100, 100), mockMasterRoom(-10, 109)),
        ("candidate price equal to base price", mockMasterRoom(100, 100), mockMasterRoom(130, 100)),
        ("candidate price less than base price", mockMasterRoom(100, 100), mockMasterRoom(130, 90))
      )

      for ((desc, baseRoom, candidateRoom) <- testCases) {
        val context = UpgradeContext(Some(ApplyTypes.PRPN), Some(SuggestedPrice.AllInclusive), Some("USD"))
        val result = recommendedUpgradeService.enrichUpgradeRecommendation(
          List(baseRoom, candidateRoom),
          context
        )
        withClue(s"Failed for case: $desc") {
          result(1).recommendedUpgrade shouldBe None
        }
      }
    }
  }

}
