package com.agoda.papi.search.property.service.ClusterService

import _root_.framework.FutureTestSet
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.rateCategories.RateCategoriesResponse
import com.agoda.papi.search.common.util.context.{ ContextGenerator, ContextHolder, ExecutionContexts }
import com.agoda.papi.search.property.service.PAPICMSService
import constant._
import mappings.CMSMapping
import models.starfruit.{ ClientInfo, ContextRequest, FeatureRequest, PricingRequest, PropertySearchRequest }
import org.mockito.ArgumentMatchers.{ any, anyBoolean, eq => mockitoEq }
import org.mockito.Mockito
import org.mockito.Mockito._
import _root_.framework.repository.LanguageRepositoryTest
import com.agoda.ml.recommendation.zenith.recommendation.RoomRecoResponse
import com.agoda.papi.pricing.supply.models.GrowthProgramInfo
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.util.context.ActiveABTests.experiments
import com.agoda.pricestream.models.response.CityMetaInfoResponse
import org.scalatest.wordspec.AnyWordSpec
import org.specs2.specification.Scope
import repository.ProviderIds
import request._
import suggestprice.SuggestPrice
import transformers.{ FilterTag, _ }
import types.{ HotelIdType, WithHotelIdMap }
import util.builders.TestDataBuilders._

import scala.collection.immutable.{ Set, Vector }
import scala.collection.mutable
import scala.concurrent.{ ExecutionContextExecutor, Future }

/** Created by sramamuniapp on 1/17/2017 AD.
  */
class ExternalServiceSpec extends AnyWordSpec with ExternalServiceSpecSetup with FutureTestSet {

  def doMockMasterRoom(): EnrichedMasterRoom = {
    val mockMasterRoom = aValidEnrichedMasterRoom.withChildrenRooms(List.empty)
    mockMasterRoom
  }

  val propertyId3: HotelIdType = 3L
  val cheapestRoomA            = aValidEnrichedCheapestRoom.copy(uid = "0012-uid")
  val cheapestRoomC            = aValidEnrichedCheapestRoom.copy(uid = "0031-uid")
  val childRoom11              = aValidEnrichedChildRoom.withUID(Some("0011-uid")).withTypeId(Some(9011L)).build()
  val childRoom12              = aValidEnrichedChildRoom.withUID(Some("0012-uid")).withTypeId(Some(9012L)).build()
  val childRoom13              = aValidEnrichedChildRoom.withUID(Some("0013-uid")).withTypeId(Some(9013L)).build()
  val childRoom21              = aValidEnrichedChildRoom.withUID(Some("0021-uid")).withTypeId(Some(9021L)).build()
  val childRoom22              = aValidEnrichedChildRoom.withUID(Some("0022-uid")).withTypeId(Some(9022L)).build()
  val childRoom23              = aValidEnrichedChildRoom.withUID(Some("0023-uid")).withTypeId(Some(9023L)).build()
  val childRoom31              = aValidEnrichedChildRoom.withUID(Some("0031-uid")).withTypeId(Some(8031L)).build()
  val masterRoom1 =
    aValidEnrichedMasterRoom.withTypeId(8011L).withChildrenRooms(List(childRoom11, childRoom12, childRoom13)).build()
  val masterRoom2 =
    aValidEnrichedMasterRoom.withTypeId(8021L).withChildrenRooms(List(childRoom21, childRoom22, childRoom23)).build()
  val masterRoom3 = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom31)).build()

  val nhaChildRoom11 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 9011L)
  val nhaChildRoom12 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 9012L)
  val nhaChildRoom13 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 9013L)
  val nhaChildRoom21 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 9021L)
  val nhaChildRoom22 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 9022L)
  val nhaChildRoom23 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 9023L)
  val nhaChildRoom31 = aValidNonHotelAccommodationChildRoom.copy(roomTypeId = 8031L)

  val nhaMasterRoom1 = aValidNonHotelAccommodationMasterRoom
    .withRoomTypeId(8011L)
    .withMaxOccupancy(10)
    .withNoOfBathrooms(5)
    .withNoOfBedrooms(4)
    .withNoOfBeds(8)
    .withRoomSizeSqm(90)
    .withChildrenRooms(Vector(nhaChildRoom11, nhaChildRoom12, nhaChildRoom13))
    .withHighlightedFacilities(Vector("kitchen"))
    .build()

  val nhaMasterRoom2 = aValidNonHotelAccommodationMasterRoom
    .withRoomTypeId(8021L)
    .withMaxOccupancy(8)
    .withNoOfBathrooms(4)
    .withNoOfBedrooms(6)
    .withNoOfBeds(8)
    .withRoomSizeSqm(72)
    .withChildrenRooms(Vector(nhaChildRoom21, nhaChildRoom22, nhaChildRoom23))
    .withHighlightedFacilities(Vector("iron"))
    .build()

  val nhaMasterRoom3 = aValidNonHotelAccommodationMasterRoom
    .withRoomTypeId(8031L)
    .withMaxOccupancy(6)
    .withNoOfBathrooms(5)
    .withNoOfBedrooms(6)
    .withNoOfBeds(6)
    .withRoomSizeSqm(66)
    .withHighlightedFacilities(Vector("washer"))
    .build()

  val nhaMasterRoom4 = aValidNonHotelAccommodationMasterRoom
    .withRoomTypeId(8041L)
    .withMaxOccupancy(8)
    .withNoOfBathrooms(4)
    .withNoOfBedrooms(6)
    .withNoOfBeds(8)
    .withRoomSizeSqm(72)
    .withChildrenRooms(Vector(nhaChildRoom21, nhaChildRoom31))
    .withHighlightedFacilities(Vector("tv"))
    .build()

  "property filter" should {

    implicit val ecMock = ExecutionContexts.defaultContext

    val contextHolderB = createContextHolderFromRequest(mockPropertyRequest)

    "find all distinct filter tags in child rooms and populate cms for each filter in sorted order" in {
      val childRoom1 = aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.BreakfastInclude, FilterID.FreeCancellation))
      val childRoom2 = aValidEnrichedChildRoom.withFilterTags(
        Seq(FilterID.PointsMax, FilterID.PayAtHotel, FilterID.PayLater, FilterID.noCCPropertyPage)
      )
      val childRoom3 = aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.BreakfastInclude, FilterID.PointsMax))
      val childRoom4 = aValidEnrichedChildRoom.withFilterTags(
        Seq(
          FilterID.MoreThanOrEqual20Sqm,
          FilterID.MoreThanOrEqual40Sqm,
          FilterID.MoreThanOrEqual60Sqm,
          FilterID.DinnerIncluded,
          FilterID.EarlyCheckIn,
          FilterID.LateCheckOut,
          FilterID.AirportTransfer,
          FilterID.AgodaSpecialOffers,
          FilterID.DayUse,
          FilterID.Overnight
        )
      )

      val masterRoom1 = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom1, childRoom2, childRoom3, childRoom4))

      val enrichedHotelWithContent = aValidEnrichedHotelWithContent.withPropertyId(1L).withMasterRooms(Seq(masterRoom1))

      when(mockCmsService.toCmsTextFuture(CMSMapping.BreakfastRoomFilterTile, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("breakfast")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.FreeCancellationRoomFilterTile, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("cancellation")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.PayLaterRoomFilterTile, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("payLater")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.PayAtHotelRoomFilterTile, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("payAtHotel")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.PointsMaxRoomFilterTile, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("pointsMax")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.noCCPropertyPageFilter, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("no-cc-required")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.FamilyFilterCmsId, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("family-filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.MoreThanOrEqual20SqmFilter, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("≥ 20 m²")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.MoreThanOrEqual40SqmFilter, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("≥ 40 m²")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.MoreThanOrEqual60SqmFilter, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("≥ 60 m²")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.DinnerIncludedMatrix, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("dinner-included")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.EarlyCheckInMatrix, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("early-checkin")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.LateCheckoutMatrix, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("late-checkout")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.AirportTransfer, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("airport-transfer")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.AgodaSpecialOffers, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Agoda Special Offers")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.DayUseFilter, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("day-use")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.OvernightFilter, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("overnight")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.meals, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Meals")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.cancellationPolicies, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Cancellation Policies")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.propertyFeatures, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Property Features")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.paymentOptions, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Payment Options")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.roomSize, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Room Sizes")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.others, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Others")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.specialOffers, 1)(ecMock, contextHolderB))
        .thenReturn(Future.successful(Some("Special Offers")))
      val fakePropertyRequest = aValidPropertyRequest.withContext(aValidContext.withLocale("en-us")).build()

      val propertyFiltersFuture = testExternalService.propertyFilters(Some(enrichedHotelWithContent))(
        ecMock,
        contextHolderB,
        fakePropertyRequest,
        mockCmsService,
        mockLanguageRepository
      )

      whenReady(propertyFiltersFuture) { propertyFilter =>
        propertyFilter should not be empty
        propertyFilter.get.title shouldBe "filters"
        (propertyFilter.get.tags should contain).theSameElementsInOrderAs(
          Seq(
            FilterTag(FilterID.DayUse, "day-use", Symbols.DayUseIcon, None, Some(FilterGroupID.Others)),
            FilterTag(FilterID.Overnight, "overnight", Symbols.OvernightIcon, None, Some(FilterGroupID.Others)),
            FilterTag(
              FilterID.noCCPropertyPage,
              "no-cc-required",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(
              FilterID.BreakfastInclude,
              "breakfast",
              Symbols.BreakfastIncludePropertySymbol,
              None,
              Some(FilterGroupID.Meals)
            ),
            FilterTag(
              FilterID.FreeCancellation,
              "cancellation",
              Symbols.FreeCancellation,
              None,
              Some(FilterGroupID.CancellationPolicies)
            ),
            FilterTag(FilterID.MoreThanOrEqual20Sqm, "≥ 20 m²", Symbols.roomSize, None, Some(FilterGroupID.RoomSize)),
            FilterTag(FilterID.MoreThanOrEqual40Sqm, "≥ 40 m²", Symbols.roomSize, None, Some(FilterGroupID.RoomSize)),
            FilterTag(FilterID.MoreThanOrEqual60Sqm, "≥ 60 m²", Symbols.roomSize, None, Some(FilterGroupID.RoomSize)),
            FilterTag(FilterID.PayLater, "payLater", Symbols.PayLater, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(FilterID.PayAtHotel, "payAtHotel", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.PointsMax,
              "pointsMax",
              Symbols.PointsMaxPropertySymbol,
              None,
              Some(FilterGroupID.PropertyFeatures)
            ),
            FilterTag(FilterID.DinnerIncluded, "dinner-included", Symbols.DinnerIcon, None, Some(FilterGroupID.Meals)),
            FilterTag(FilterID.EarlyCheckIn, "early-checkin", Symbols.CheckInIcon, None, Some(FilterGroupID.Others)),
            FilterTag(FilterID.LateCheckOut, "late-checkout", Symbols.CheckOutIcon, None, Some(FilterGroupID.Others)),
            FilterTag(
              FilterID.AirportTransfer,
              "airport-transfer",
              Symbols.AirportTransferIcon,
              None,
              Some(FilterGroupID.Others)
            ),
            FilterTag(
              FilterID.AgodaSpecialOffers,
              "Agoda Special Offers",
              Symbols.ASOIcon,
              None,
              Some(FilterGroupID.SpecialOffers)
            )
          )
        )
      }
    }

    "Should not return subTags and should not return family filter group" in {

      val fakePropertyRequest = aValidPropertyRequest
        .withContext(aValidContext.withShowCMS(Some(false)).withExperiment(None).withLocale("en-us"))
        .build()

      val contextHolder = createContextHolderFromRequest(fakePropertyRequest, new ForceBExperimentContext(None))

      val childRoom1 =
        aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.InterConnectingRooms, FilterID.FreeCancellation))
      val childRoom2 =
        aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.PayAtHotel, FilterID.PayLater, FilterID.noCCPropertyPage))
      val childRoom3 = aValidEnrichedChildRoom.withFilterTags(
        Seq(FilterID.BreakfastInclude, FilterID.BabyCotAvailable, FilterID.InterConnectingRooms)
      )
      val masterRoom1 = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom1, childRoom2, childRoom3))

      val enrichedHotelWithContent = aValidEnrichedHotelWithContent.withPropertyId(1L).withMasterRooms(Seq(masterRoom1))

      when(mockCmsService.toCmsTextFuture(CMSMapping.BreakfastRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("breakfast")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.FreeCancellationRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("cancellation")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.PayLaterRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("payLater")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.PayAtHotelRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("payAtHotel")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.noCCPropertyPageFilter, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("no-cc-required")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.interConnectingRooms, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("interconnecting-rooms")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.babyCotAvailable, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("baby-cot-available")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.FamilyFilterCmsId, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("family-filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.meals, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Meals")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.cancellationPolicies, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Cancellation Policies")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.paymentOptions, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Payment Options")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.roomFeatures, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Room Features")))

      val propertyFiltersFuture = testExternalService.propertyFilters(Some(enrichedHotelWithContent))(
        ecMock,
        contextHolder,
        fakePropertyRequest,
        mockCmsService,
        mockLanguageRepository
      )

      whenReady(propertyFiltersFuture) { propertyFilter =>
        propertyFilter should not be empty
        propertyFilter.get.title shouldBe "filters"
        (propertyFilter.get.tags should contain).theSameElementsInOrderAs(
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "no-cc-required",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(
              FilterID.BreakfastInclude,
              "breakfast",
              Symbols.BreakfastIncludePropertySymbol,
              None,
              Some(FilterGroupID.Meals)
            ),
            FilterTag(
              FilterID.FreeCancellation,
              "cancellation",
              Symbols.FreeCancellation,
              None,
              Some(FilterGroupID.CancellationPolicies)
            ),
            FilterTag(FilterID.PayLater, "payLater", Symbols.PayLater, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(FilterID.PayAtHotel, "payAtHotel", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.BabyCotAvailable,
              "baby-cot-available",
              Symbols.babyCotIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.InterConnectingRooms,
              "interconnecting-rooms",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          )
        )
      }
    }

    "Should return recommended for families filter for suggest room when feature flag is sent " in {
      val featureFlagRequest = aValidFeatureFlagRequest.withFlags(Some(Map(FeatureFlagKey.GreatForFamiliesMRS -> true)))
      val fakePropertyRequest =
        aValidPropertyRequest.withFeatureFlag(featureFlagRequest).withSearchType(SearchTypes.Property).build()
      val contextHolder = createContextHolderFromRequest(fakePropertyRequest)

      val childRoom =
        aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.InterConnectingRooms, FilterID.FreeCancellation))
      val suggestRoom = aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.RecommendedForFamilies))

      val masterRoom        = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom))
      val suggestMasterRoom = aValidEnrichedMasterRoom.withChildrenRooms(List(suggestRoom))

      val enrichedHotelWithContent = aValidEnrichedHotelWithContent
        .withPropertyId(1L)
        .withMasterRooms(Seq(masterRoom))
        .withSuggestedRooms(List(suggestMasterRoom))

      when(mockCmsService.toCmsTextFuture(CMSMapping.FreeCancellationRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("cancellation")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.interConnectingRooms, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("interconnecting-rooms")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.recommendedForFamilies, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("recommended-for-families")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.cancellationPolicies, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Cancellation Policies")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.roomFeatures, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Room Features")))

      val propertyFiltersFuture = testExternalService.propertyFilters(Some(enrichedHotelWithContent))(
        ecMock,
        contextHolder,
        fakePropertyRequest,
        mockCmsService,
        mockLanguageRepository
      )

      whenReady(propertyFiltersFuture) { propertyFilter =>
        propertyFilter should not be empty
        propertyFilter.get.title shouldBe "filters"
        (propertyFilter.get.tags should contain).theSameElementsInOrderAs(
          Seq(
            FilterTag(
              FilterID.FreeCancellation,
              "cancellation",
              Symbols.FreeCancellation,
              None,
              Some(FilterGroupID.CancellationPolicies)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "recommended-for-families",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.InterConnectingRooms,
              "interconnecting-rooms",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          )
        )
      }
    }

    "Should return recommended for families filter for suggest room when feature flag is sent and there is only suggest room" in {
      val featureFlagRequest = aValidFeatureFlagRequest.withFlags(Some(Map(FeatureFlagKey.GreatForFamiliesMRS -> true)))
      val fakePropertyRequest =
        aValidPropertyRequest.withFeatureFlag(featureFlagRequest).withSearchType(SearchTypes.Property).build()
      val contextHolder = createContextHolderFromRequest(fakePropertyRequest)

      val childRoom =
        aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.InterConnectingRooms, FilterID.FreeCancellation))
      val suggestRoom = aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.RecommendedForFamilies))

      val masterRoom        = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom))
      val suggestMasterRoom = aValidEnrichedMasterRoom.withChildrenRooms(List(suggestRoom))

      val enrichedHotelWithContent =
        aValidEnrichedHotelWithContent.withPropertyId(1L).withSuggestedRooms(List(suggestMasterRoom))

      when(mockCmsService.toCmsTextFuture(CMSMapping.FreeCancellationRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("cancellation")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.interConnectingRooms, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("interconnecting-rooms")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.recommendedForFamilies, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("recommended-for-families")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.roomFeatures, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Room Features")))

      val propertyFiltersFuture = testExternalService.propertyFilters(Some(enrichedHotelWithContent))(
        ecMock,
        contextHolder,
        fakePropertyRequest,
        mockCmsService,
        mockLanguageRepository
      )

      whenReady(propertyFiltersFuture) { propertyFilter =>
        propertyFilter should not be empty
        propertyFilter.get.title shouldBe "filters"
        (propertyFilter.get.tags should contain).theSameElementsInOrderAs(
          Seq(
            FilterTag(
              FilterID.RecommendedForFamilies,
              "recommended-for-families",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          )
        )
      }
    }

    "Should not return recommended for families filter for suggest room when feature flag is sent but search type is not property" in {
      val featureFlagRequest = aValidFeatureFlagRequest.withFlags(Some(Map(FeatureFlagKey.GreatForFamiliesMRS -> true)))
      val propertyRequest    = aValidPropertyRequest.withFeatureFlag(featureFlagRequest).build()
      val contextHolder      = createContextHolderFromRequest(propertyRequest)

      val childRoom =
        aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.InterConnectingRooms, FilterID.FreeCancellation))
      val suggestRoom = aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.RecommendedForFamilies))

      val masterRoom        = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom))
      val suggestMasterRoom = aValidEnrichedMasterRoom.withChildrenRooms(List(suggestRoom))

      val enrichedHotelWithContent = aValidEnrichedHotelWithContent
        .withPropertyId(1L)
        .withMasterRooms(Seq(masterRoom))
        .withSuggestedRooms(List(suggestMasterRoom))

      when(mockCmsService.toCmsTextFuture(CMSMapping.FreeCancellationRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("cancellation")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.interConnectingRooms, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("interconnecting-rooms")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.cancellationPolicies, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Cancellation Policies")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.roomFeatures, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Room Features")))

      val propertyFiltersFuture = testExternalService.propertyFilters(Some(enrichedHotelWithContent))(
        ecMock,
        contextHolder,
        propertyRequest,
        mockCmsService,
        mockLanguageRepository
      )

      whenReady(propertyFiltersFuture) { propertyFilter =>
        propertyFilter should not be empty
        propertyFilter.get.title shouldBe "filters"
        (propertyFilter.get.tags should contain).theSameElementsInOrderAs(
          Seq(
            FilterTag(
              FilterID.FreeCancellation,
              "cancellation",
              Symbols.FreeCancellation,
              None,
              Some(FilterGroupID.CancellationPolicies)
            ),
            FilterTag(
              FilterID.InterConnectingRooms,
              "interconnecting-rooms",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          )
        )
      }
    }

    "should use DroneIcon symbols when isReplaceDroneIcon experiment is enabled" in {
      val childRoom1 = aValidEnrichedChildRoom.withFilterTags(Seq(FilterID.BreakfastInclude, FilterID.FreeCancellation))
      val masterRoom1              = aValidEnrichedMasterRoom.withChildrenRooms(List(childRoom1))
      val enrichedHotelWithContent = aValidEnrichedHotelWithContent.withPropertyId(1L).withMasterRooms(Seq(masterRoom1))

      val fakePropertyRequest = aValidPropertyRequest.withContext(aValidContext.withLocale("en-us")).build()
      val contextHolder = createContextHolderFromRequest(
        fakePropertyRequest,
        new ForceBExperimentContext(Some(Seq(experiments.replaceDroneIcon)))
      )

      when(mockCmsService.toCmsTextFuture(CMSMapping.BreakfastRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("breakfast")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.FreeCancellationRoomFilterTile, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("cancellation")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("filters")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.meals, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Meals")))
      when(mockCmsService.toCmsTextFuture(CMSMapping.cancellationPolicies, 1)(ecMock, contextHolder))
        .thenReturn(Future.successful(Some("Cancellation Policies")))

      val propertyFiltersFuture = testExternalService.propertyFilters(Some(enrichedHotelWithContent))(
        ecMock,
        contextHolder,
        fakePropertyRequest,
        mockCmsService,
        mockLanguageRepository
      )

      whenReady(propertyFiltersFuture) { propertyFilter =>
        propertyFilter should not be empty
        propertyFilter.get.title shouldBe "filters"
        (propertyFilter.get.tags should contain).theSameElementsInOrderAs(
          Seq(
            FilterTag(
              FilterID.BreakfastInclude,
              "breakfast",
              Symbols.BreakfastIncludeDroneIcon,
              None,
              Some(FilterGroupID.Meals)
            ),
            FilterTag(
              FilterID.FreeCancellation,
              "cancellation",
              Symbols.FreeCancellationDroneIcon,
              None,
              Some(FilterGroupID.CancellationPolicies)
            )
          )
        )
      }
    }
  }

  "setTopSellingPointsData" should {
    implicit val fakePropertyRequest =
      aValidPropertyRequest.withPricing(aValidPricingRequest).withContext(aValidContext.withLocale("en-us")).build()
    implicit val contextHolder              = createContextHolderFromRequest(fakePropertyRequest)
    implicit val cmsService: PAPICMSService = mock[PAPICMSService]
    when(cmsService.toCmsText(any(), any())(any())).thenReturn(Option(""))

    val enrichedHotelWithContent = aValidEnrichedHotelWithContent.withPropertyId(1L)

    "return no TSP if not requested" in {
      val hotel = testExternalService.setTopSellingPointsData(None, Some(enrichedHotelWithContent), None)
      hotel.get.topSellingPoints.size shouldBe 0
    }

    "return DOD if requested" in {
      val hotel = testExternalService
        .setTopSellingPointsData(
          Some(TopSellingPointRequest(List(TopSellingPointIds.DealOfTheDay))),
          Some(enrichedHotelWithContent),
          None
        )
        .get
      hotel.topSellingPoints.size shouldBe 1
      hotel.topSellingPoints.head.id shouldBe TopSellingPointIds.DealOfTheDay
    }

    "return no TSP if invalid id requested" in {
      val hotel = testExternalService.setTopSellingPointsData(
        Some(TopSellingPointRequest(List(111))),
        Some(enrichedHotelWithContent),
        None
      )
      hotel.get.topSellingPoints.size shouldBe 0
    }

    "return best value for location, exclusive sale and DOD if requested" in {
      val hotel = testExternalService
        .setTopSellingPointsData(
          Some(
            TopSellingPointRequest(
              List(
                TopSellingPointIds.BestValueForLocation,
                TopSellingPointIds.DealOfTheDay,
                TopSellingPointIds.FlashSale
              )
            )
          ),
          Some(enrichedHotelWithContent),
          None
        )
        .get
      hotel.topSellingPoints.size shouldBe 3
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.DealOfTheDay) shouldBe true
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.BestValueForLocation) shouldBe true
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.FlashSale) shouldBe true
    }

    "return best value for location, DOD and Bestseller for hotel with average price diff TSP" in {
      val enrichedHotelPriceDiffTSP =
        enrichedHotelWithContent.withTSPs(List(TopSellingPoint(TopSellingPointIds.PriceDifferencePercent)))
      val hotel = testExternalService
        .setTopSellingPointsData(
          Some(
            TopSellingPointRequest(
              List(
                TopSellingPointIds.BestValueForLocation,
                TopSellingPointIds.DealOfTheDay,
                TopSellingPointIds.Bestseller
              )
            )
          ),
          Some(enrichedHotelPriceDiffTSP),
          None
        )
        .get

      hotel.topSellingPoints.size shouldBe 4
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.PriceDifferencePercent) shouldBe true
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.DealOfTheDay) shouldBe true
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.BestValueForLocation) shouldBe true
      hotel.topSellingPoints.exists(_.id == TopSellingPointIds.Bestseller) shouldBe true
    }

    "return five-star DOD if requested" in {
      val hotel = testExternalService
        .setTopSellingPointsData(
          Some(TopSellingPointRequest(List(TopSellingPointIds.FiveStarDealOfTheDay))),
          Some(enrichedHotelWithContent),
          None
        )
        .get
      hotel.topSellingPoints.size shouldBe 1
      hotel.topSellingPoints.head.id shouldBe TopSellingPointIds.FiveStarDealOfTheDay
    }
  }

  "limit top x master rooms" should {

    def getPricingWithDisplayPRPNSellEx(currency: String, price: Double): Map[String, EnrichedPricing] =
      Map(
        currency -> (aValidEnrichedPricing
          .withDisplay(aValidDisplayBasis.withPerRoomPerNight(aValidDisplayPrice.withExclusive(price))))
          .build
      )

    val requestUSDOcc2 = aValidPropertyRequest.withPricing(aValidPricingRequest)
    val requestUSDOcc1 = aValidPropertyRequest.withPricing(aValidPricingRequest.withAdult(1))
    val requestTHB     = aValidPropertyRequest.withPricing(aValidPricingRequest.withCurrency(Currencies.THB))

    "return limited master rooms for each with fit children included, select cheapest price from fit room" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 103d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0))),
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3)
    }

    "return limited master rooms for each with fit children included, ordering by isFit" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 103d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(2, 3, 1)
    }

    "return limited master rooms for each with fit children included, keep rooms if their prices are equal to the last on limit" ignore {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom4 = aValidEnrichedMasterRoom
        .withTypeId(4)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 102d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3, masterRoom4))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3, 4)
    }

    "return limited master rooms, return all rooms as the number of rooms is inside the limit" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 102d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3)
    }

    "return limited master rooms, return all rooms as the number of rooms equals the limit" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 102d))
          )
        )
        .build()
      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3)
    }

    "return limited master rooms, pick the room with fitRoom included first" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom4 = aValidEnrichedMasterRoom
        .withTypeId(4)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 91d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3, masterRoom4))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3, 4)
    }

    "return limited master rooms, pick the room with fitRoom included first - rooms with fitRoom included fits the limit" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom4 = aValidEnrichedMasterRoom
        .withTypeId(4)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 91d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3, masterRoom4))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3, 4)
    }

    "return limited master rooms, pick the room with fitRoom included first + keep rooms if their prices are equal to the last on limit" ignore {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom4 = aValidEnrichedMasterRoom
        .withTypeId(4)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3, masterRoom4))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3, 4)
    }

    "return limited master rooms, excludes master rooms with suggested rooms contained" ignore {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withTypeId(Option(1))
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 120d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withTypeId(Option(2))
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 130d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom4 = aValidEnrichedMasterRoom
        .withTypeId(4)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom5 = aValidEnrichedMasterRoom
        .withTypeId(5)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 102d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms = testExternalService.sortMasterRoom(
        List(masterRoom1, masterRoom2, masterRoom3, masterRoom4, masterRoom5)
      )(requestTHB)
      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3, 4, 5)
    }

    "return limited master rooms for each without fit children included, select cheapest price" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 101d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 102d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0))),
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(3, 1, 2)
    }

    "return limited master rooms for findRemaining func, select cheapest price from fit rooms" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 120d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0))),
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 150d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0))),
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(true))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 110d))
              .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(2, 3, 1)
    }

    "return limited master rooms for findRemaining func, select cheapest price from nonFit rooms" in {
      val masterRoom1 = aValidEnrichedMasterRoom
        .withTypeId(1)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 120d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0))),
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 90d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom2 = aValidEnrichedMasterRoom
        .withTypeId(2)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 150d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0))),
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 100d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()
      val masterRoom3 = aValidEnrichedMasterRoom
        .withTypeId(3)
        .withChildrenRooms(
          List(
            aValidEnrichedChildRoom
              .withIsFit(Option(false))
              .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.THB, 110d))
              .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
          )
        )
        .build()

      val remainingMasterRooms =
        testExternalService.sortMasterRoom(List(masterRoom1, masterRoom2, masterRoom3))(requestTHB)

      remainingMasterRooms.map(_.typeId) shouldBe List(1, 2, 3)
    }

    val masterRoomA = aValidEnrichedMasterRoom
      .withTypeId(1)
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withTypeId(Some(1))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 100d))
            .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ycs)),
          aValidEnrichedChildRoom
            .withTypeId(Some(2))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 300d))
            .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ihgd)),
          aValidEnrichedChildRoom
            .withTypeId(Some(3))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 75d))
            .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ycs))
        )
      )
      .build()
    val masterRoomB = aValidEnrichedMasterRoom
      .withTypeId(2)
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withTypeId(Some(4))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 75d))
            .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ycs))
        )
      )
      .build()
    val masterRoomC = aValidEnrichedMasterRoom
      .withTypeId(3)
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withTypeId(Some(5))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 100d))
            .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Bcom))
        )
      )
      .build()
    val masterRoomD = aValidEnrichedMasterRoom
      .withTypeId(4)
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withTypeId(Some(6))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 70d))
            .withCapacity(Some(EnrichedCapacity(2, 0, 0, 2, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Jtb))
        )
      )
      .build()
    val masterRoomE = aValidEnrichedMasterRoom
      .withTypeId(5)
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withTypeId(Some(7))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 75d))
            .withCapacity(Some(EnrichedCapacity(3, 0, 0, 3, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ycs)),
          aValidEnrichedChildRoom
            .withTypeId(Some(8))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 50d))
            .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ycs))
        )
      )
      .build()
    val masterRoomF = aValidEnrichedMasterRoom
      .withTypeId(6)
      .withChildrenRooms(
        List(
          aValidEnrichedChildRoom
            .withTypeId(Some(9))
            .withPricing(getPricingWithDisplayPRPNSellEx(Currencies.USD, 75d))
            .withCapacity(Some(EnrichedCapacity(1, 0, 0, 1, "", 0, 0)))
            .withSubSupplierId(Some(ProviderIds.Ihgd))
        )
      )
      .build()

    def mockSupplierMetadataService: Unit = {
      Mockito.when(testExternalService.supplierMetadataService.isAgodaBrand(ProviderIds.Ycs)).thenReturn(true)
      Mockito.when(testExternalService.supplierMetadataService.isAgodaBrand(ProviderIds.Ihgd)).thenReturn(true)
      Mockito.when(testExternalService.supplierMetadataService.isAgodaBrand(ProviderIds.Bcom)).thenReturn(false)
      Mockito.when(testExternalService.supplierMetadataService.isAgodaBrand(ProviderIds.Jtb)).thenReturn(false)
    }

    "return sorted master rooms correctly" in {
      mockSupplierMetadataService
      val regularMasterRoom = List(masterRoomA, masterRoomA, masterRoomA, masterRoomA, masterRoomA, masterRoomA)

      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      remainingMasterRooms.length shouldBe 6
      remainingMasterRooms.forall(regularMasterRoom.contains) shouldBe true
    }

    "return sorted master rooms fit room first" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomB)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomA, masterRoomB))
    }

    "return sorted master rooms non-fit room is possible to be in response" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomB, masterRoomF)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomF, masterRoomB))
    }

    "return sorted master rooms cheapest first" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomD)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomD, masterRoomA))
    }

    "return sorted master rooms with family fit logic" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomE)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomE, masterRoomA))
    }

    "return sorted master rooms YCS first" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomB, masterRoomC)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomA, masterRoomC, masterRoomB))
    }

    "return sorted master rooms Non-YCS" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomB, masterRoomC)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomA, masterRoomC, masterRoomB))
    }

    "return sorted master rooms newest room first" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomB)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc1)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(List(masterRoomB, masterRoomA))
    }

    "return sorted master rooms with correct sorting with search occ 2" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomB, masterRoomC, masterRoomD, masterRoomE, masterRoomF)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc2)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(
        List(masterRoomD, masterRoomE, masterRoomA, masterRoomC, masterRoomF, masterRoomB)
      )
    }

    "return sorted master rooms with correct sorting with search occ 1" in {
      mockSupplierMetadataService
      val regularMasterRoom    = List(masterRoomA, masterRoomB, masterRoomC, masterRoomD, masterRoomE, masterRoomF)
      val remainingMasterRooms = testExternalService.sortMasterRoom(regularMasterRoom)(requestUSDOcc1)

      (remainingMasterRooms should contain).theSameElementsInOrderAs(
        List(masterRoomE, masterRoomD, masterRoomF, masterRoomB, masterRoomA, masterRoomC)
      )
    }
  }

  "Enriching hotel" should {
    "map chain id to the room" in new EnrichmentScope {
      val hotel = testExternalService.getEnrichedHotel(
        request,
        aValidEnrichedHotelWithoutContent.copy(suggestedRooms = List(enrichedChildRoom)),
        Some(aValidRoomInformationResponse.copy(roomsInformation = masterRoomResponses)),
        singleRoomNonHotelAccommodation = None,
        soldOutRoomsPrice = Nil,
        isExternalVipDisplayEnabled = false,
        isRoomFilterEnabled = false,
        isValidEscapesFlow = false,
        isValidMergedDayUseOvernightFlow = false
      )(contextHolder, mockCmsService)
      val masterRoom = hotel.get.masterRooms(0)
      masterRoom.childrenRooms(0).directConnectProviderId shouldEqual Some(23)
      masterRoom.childrenRooms(1).directConnectProviderId shouldEqual Some(24)
    }
    "no children rooms if non content request" in new EnrichmentScope {
      override val request = mockPropertyRequest.copy(room =
        Some(RoomRequestPAPI(isContentOnly = Some(false), showChildRoomInContentOnly = Some(true)))
      )
      val hotel = testExternalService.getEnrichedHotel(
        request,
        aValidEnrichedHotelWithoutContent.copy(suggestedRooms = List(enrichedChildRoom)),
        Some(aValidRoomInformationResponse.copy(roomsInformation = masterRoomResponses)),
        singleRoomNonHotelAccommodation = None,
        soldOutRoomsPrice = Nil,
        isExternalVipDisplayEnabled = false,
        isRoomFilterEnabled = false,
        isValidEscapesFlow = false,
        isValidMergedDayUseOvernightFlow = false
      )(contextHolder, mockCmsService)
      val masterRoom = hotel.get.masterRooms(0)
      masterRoom.childrenRooms(0).directConnectProviderId shouldEqual None
    }
  }

  "filter master room" should {
    val masterRoomWRoomContent = aValidEnrichedMasterRoom
      .withTypeId(1)
      .withImage(Vector(aValidImageResponse))
      .withFacilityGroup(Vector(aValidFeatureGroup))
      .build()
    val masterRoomWOAnyRoomContent = aValidEnrichedMasterRoom.withTypeId(2).build()
    val masterRoomWOImage = aValidEnrichedMasterRoom.withTypeId(3).withFacilityGroup(Vector(aValidFeatureGroup)).build()
    val masterRoomWORoomFeature = aValidEnrichedMasterRoom.withTypeId(4).withImage(Vector(aValidImageResponse)).build()
    "do nothing if the rooms is in first 8 - mix no content room" in {
      val maxFilter    = 12
      val excludeCount = 8
      val masterRoom =
        List(masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWOAnyRoomContent, masterRoomWORoomFeature)
      val filtered = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 4
      (filtered should contain).theSameElementsInOrderAs(
        List(masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWOAnyRoomContent, masterRoomWORoomFeature)
      )
    }
    "do nothing if the rooms is in first 8 - only 1 room" in {
      val maxFilter    = 12
      val excludeCount = 8
      val masterRoom   = List(masterRoomWRoomContent)
      val filtered     = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 1
      (filtered should contain).theSameElementsInOrderAs(List(masterRoomWRoomContent))
    }
    "do nothing if the rooms is in first 8 - no mapping shouldn't be remove" in {
      val maxFilter    = 12
      val excludeCount = 8
      val masterRoom =
        List(masterRoomWRoomContent, masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWOAnyRoomContent)
      val filtered = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 4
      (filtered should contain).theSameElementsInOrderAs(
        List(masterRoomWRoomContent, masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWOAnyRoomContent)
      )
    }
    "remove all rooms where no images and no room content - 1" in {
      val maxFilter    = 12
      val excludeCount = 2
      val masterRoom = List(
        masterRoomWRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWOImage,
        masterRoomWRoomContent
      )
      val filtered = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 4
      (filtered should contain).theSameElementsInOrderAs(
        List(masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWOImage, masterRoomWRoomContent)
      )
    }
    "remove all rooms where no images and no room content - 2" in {
      val maxFilter    = 12
      val excludeCount = 2
      val masterRoom = List(
        masterRoomWRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWORoomFeature,
        masterRoomWRoomContent
      )
      val filtered = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 4
      (filtered should contain).theSameElementsInOrderAs(
        List(masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWORoomFeature, masterRoomWRoomContent)
      )
    }
    "remove maximum 12 rooms - 1" in {
      val maxFilter    = 2
      val excludeCount = 1
      val masterRoom = List(
        masterRoomWRoomContent,
        masterRoomWRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWOAnyRoomContent
      )
      val filtered = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 3
      (filtered should contain).theSameElementsInOrderAs(
        List(masterRoomWRoomContent, masterRoomWRoomContent, masterRoomWOAnyRoomContent)
      )
    }
    "remove maximum 12 rooms - 2" in {
      val maxFilter    = 2
      val excludeCount = 1
      val masterRoom = List(
        masterRoomWRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWRoomContent,
        masterRoomWOAnyRoomContent,
        masterRoomWOAnyRoomContent
      )
      val filtered = testExternalService.filterMasterRoom(masterRoom, maxFilter, excludeCount)

      filtered.length shouldBe 3
      (filtered should contain).theSameElementsInOrderAs(
        List(masterRoomWRoomContent, masterRoomWOAnyRoomContent, masterRoomWRoomContent)
      )
    }
  }

  "sortSuggestedMasterRoomsByPrice" should {

    def getPricingWithDisplayPBSellEx(currency: String, price: Double): Map[String, EnrichedPricing] =
      Map(
        currency -> (aValidEnrichedPricing
          .withDisplay(aValidDisplayBasis.withPerBook(aValidDisplayPrice.withExclusive(price))))
          .build
      )

    implicit val requestTHB = aValidPropertyRequest.withPricing(aValidPricingRequest.withCurrency(Currencies.THB))

    "sort master rooms according to child room per book price" in {
      val rooms = List(
        aValidEnrichedMasterRoom
          .withTypeId(1)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.THB, 100d))
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(2)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.THB, 101d))
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(3)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.THB, 90d))
            )
          )
          .build()
      )

      val result = testExternalService.sortSuggestedMasterRoomsByPrice(rooms)(requestTHB)

      result.map(_.typeId) shouldBe List(3, 1, 2)
    }

    "sort master rooms according to child room per book price when requested currency doesn't exist" in {
      val rooms = List(
        aValidEnrichedMasterRoom
          .withTypeId(1)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.USD, 100d))
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(2)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.USD, 101d))
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(3)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.USD, 90d))
            )
          )
          .build()
      )

      val result = testExternalService.sortSuggestedMasterRoomsByPrice(rooms)(requestTHB)

      result.map(_.typeId) shouldBe List(3, 1, 2)
    }

    "sort master rooms according to child room per book price when no currency in the request" in {
      val rooms = List(
        aValidEnrichedMasterRoom
          .withTypeId(1)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.USD, 100d))
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(2)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.USD, 101d))
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(3)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.USD, 90d))
            )
          )
          .build()
      )

      val result = testExternalService.sortSuggestedMasterRoomsByPrice(rooms)(aValidPropertyRequest)

      result.map(_.typeId) shouldBe List(3, 1, 2)
    }

    "put childless master room as last" in {
      val rooms = List(
        aValidEnrichedMasterRoom.withTypeId(1).build(),
        aValidEnrichedMasterRoom
          .withTypeId(2)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.THB, 101d))
            )
          )
          .build()
      )

      val result = testExternalService.sortSuggestedMasterRoomsByPrice(rooms)(requestTHB)

      result.map(_.typeId) shouldBe List(2, 1)
    }

    "put master room with child without pricing as last" in {
      val rooms = List(
        aValidEnrichedMasterRoom
          .withTypeId(1)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom
            )
          )
          .build(),
        aValidEnrichedMasterRoom
          .withTypeId(2)
          .withChildrenRooms(
            List(
              aValidEnrichedChildRoom.withPricing(getPricingWithDisplayPBSellEx(Currencies.THB, 101d))
            )
          )
          .build()
      )

      val result = testExternalService.sortSuggestedMasterRoomsByPrice(rooms)(requestTHB)

      result.map(_.typeId) shouldBe List(2, 1)
    }

    "handle empty list correctly" in {
      val result = testExternalService.sortSuggestedMasterRoomsByPrice(Nil)(requestTHB)
      result shouldBe Nil
    }
  }

  "getCheapestRoomTypeId" should {

    "return None when there is no enrichedHotel input" in {
      val result = testExternalService.getCheapestRoomTypeId(
        Seq(childRoom11, childRoom12, childRoom13, childRoom21, childRoom22, childRoom23),
        None
      )
      result shouldBe (None)
    }

    "return None when there is no cheapestRoom in enrichedHotel" in {
      val enrichedHotelWithContent = aValidEnrichedHotelWithContent
        .withPropertyId(propertyId3)
        .copy(cheapestRoom = None)
        .withMasterRooms(Seq(masterRoom1, masterRoom2))
        .build()

      val result = testExternalService.getCheapestRoomTypeId(
        Seq(childRoom11, childRoom12, childRoom13, childRoom21, childRoom22, childRoom23),
        Some(enrichedHotelWithContent)
      )

      result shouldBe (None)
    }

    "return None when no matching cheapestRoom uid" in {
      val enrichedHotelWithContent = aValidEnrichedHotelWithContent
        .withPropertyId(propertyId3)
        .withMasterRooms(Seq())
        .withCheapestRoom(cheapestRoomC)
        .build()

      val result = testExternalService.getCheapestRoomTypeId(
        Seq(childRoom11, childRoom12, childRoom13, childRoom21, childRoom22, childRoom23),
        Some(enrichedHotelWithContent)
      )

      result shouldBe None
    }

    "return correct roomTypeId when there is a matching cheapestRoom uid" in {
      val enrichedHotelWithContent = aValidEnrichedHotelWithContent
        .withPropertyId(propertyId3)
        .withMasterRooms(Seq())
        .withCheapestRoom(cheapestRoomA)
        .build()

      val result = testExternalService.getCheapestRoomTypeId(
        Seq(childRoom11, childRoom12, childRoom13, childRoom21, childRoom22, childRoom23),
        Some(enrichedHotelWithContent)
      )

      result shouldBe childRoom12.typeId
    }
  }

  "updateNhaWithCheapestMasterRoom" should {

    val nhaResponse = aValidNonHotelAccommodationResponse
      .withHotelId(propertyId3)
      .withNoOfBedrooms(3)
      .withNoOfBeds(4)
      .withHouseRules("sample house rule 3")
      .withIsRareFind(Some(true))
      .withSupportedLongStay(Some(true))

    "masterRoom list should be empty when original NonHotelAccommodation is None" in {
      val result = testExternalService.updateNhaWithCheapestMasterRoom(childRoom12.typeId.get, None)
      result shouldBe (None)
    }

    "masterRoom list should be empty when original NonHotelAccommodation has empty masterRoom list" in {
      val nhaResponseWithNoMasterRoom = nhaResponse
        .withMasterRooms(Vector())
        .build()
      val result =
        testExternalService.updateNhaWithCheapestMasterRoom(childRoom12.typeId.get, Some(nhaResponseWithNoMasterRoom))
      result shouldNot be(None)
      result.get.noOfBedrooms shouldBe nhaResponseWithNoMasterRoom.noOfBedrooms
      result.get.noOfBeds shouldBe nhaResponseWithNoMasterRoom.noOfBeds
      result.get.houseRules shouldBe nhaResponseWithNoMasterRoom.houseRules
      result.get.hostLevels shouldBe nhaResponseWithNoMasterRoom.hostLevels
      result.get.isRareFind shouldBe nhaResponseWithNoMasterRoom.isRareFind
      result.get.supportedLongStay shouldBe nhaResponseWithNoMasterRoom.supportedLongStay
      result.get.masterRooms shouldBe Vector.empty
    }

    "masterRoom list should be empty when there is no cheapestRoomTypeID" in {
      val nhaResponseWithMasterRooms = nhaResponse
        .withMasterRooms(Vector(nhaMasterRoom1, nhaMasterRoom2))
        .build()
      val result = testExternalService.updateNhaWithCheapestMasterRoom(0L, Some(nhaResponseWithMasterRooms))
      result shouldNot be(None)
      result.get.noOfBedrooms shouldBe nhaResponseWithMasterRooms.noOfBedrooms
      result.get.noOfBeds shouldBe nhaResponseWithMasterRooms.noOfBeds
      result.get.houseRules shouldBe nhaResponseWithMasterRooms.houseRules
      result.get.hostLevels shouldBe nhaResponseWithMasterRooms.hostLevels
      result.get.isRareFind shouldBe nhaResponseWithMasterRooms.isRareFind
      result.get.supportedLongStay shouldBe nhaResponseWithMasterRooms.supportedLongStay
      result.get.masterRooms shouldBe Vector.empty
    }

    "masterRoom list should be empty when there is no matching typeId in original NonHotelAccommodation" in {
      val nhaResponseWithMasterRooms = nhaResponse
        .withMasterRooms(Vector(nhaMasterRoom2))
        .build()
      val result =
        testExternalService.updateNhaWithCheapestMasterRoom(childRoom12.typeId.get, Some(nhaResponseWithMasterRooms))
      result shouldNot be(None)
      result.get.noOfBedrooms shouldBe nhaResponseWithMasterRooms.noOfBedrooms
      result.get.noOfBeds shouldBe nhaResponseWithMasterRooms.noOfBeds
      result.get.houseRules shouldBe nhaResponseWithMasterRooms.houseRules
      result.get.hostLevels shouldBe nhaResponseWithMasterRooms.hostLevels
      result.get.isRareFind shouldBe nhaResponseWithMasterRooms.isRareFind
      result.get.supportedLongStay shouldBe nhaResponseWithMasterRooms.supportedLongStay
      result.get.masterRooms shouldBe Vector.empty
    }

    "masterRoom list has one entry when cheapestRoom matches child RoomTypeId (single-or-multiple-room NHA)" in {
      val nhaResponseWithMasterRooms = nhaResponse
        .withMasterRooms(Vector(nhaMasterRoom1, nhaMasterRoom2, nhaMasterRoom3))
        .build()
      val result =
        testExternalService.updateNhaWithCheapestMasterRoom(childRoom12.typeId.get, Some(nhaResponseWithMasterRooms))
      result shouldNot be(None)
      result.get.noOfBedrooms shouldBe nhaResponseWithMasterRooms.noOfBedrooms
      result.get.noOfBeds shouldBe nhaResponseWithMasterRooms.noOfBeds
      result.get.houseRules shouldBe nhaResponseWithMasterRooms.houseRules
      result.get.hostLevels shouldBe nhaResponseWithMasterRooms.hostLevels
      result.get.isRareFind shouldBe nhaResponseWithMasterRooms.isRareFind
      result.get.supportedLongStay shouldBe nhaResponseWithMasterRooms.supportedLongStay
      result.get.masterRooms shouldBe Vector(nhaMasterRoom1)
    }

    "masterRoom list has one entry when cheapestRoom matches master RoomTypeId (single-or-multiple-room NHA)" in {
      val nhaResponseWithMasterRooms = nhaResponse
        .withMasterRooms(Vector(nhaMasterRoom1, nhaMasterRoom2, nhaMasterRoom3))
        .build()
      val result =
        testExternalService.updateNhaWithCheapestMasterRoom(childRoom31.typeId.get, Some(nhaResponseWithMasterRooms))
      result shouldNot be(None)
      result.get.noOfBedrooms shouldBe nhaResponseWithMasterRooms.noOfBedrooms
      result.get.noOfBeds shouldBe nhaResponseWithMasterRooms.noOfBeds
      result.get.houseRules shouldBe nhaResponseWithMasterRooms.houseRules
      result.get.hostLevels shouldBe nhaResponseWithMasterRooms.hostLevels
      result.get.isRareFind shouldBe nhaResponseWithMasterRooms.isRareFind
      result.get.supportedLongStay shouldBe nhaResponseWithMasterRooms.supportedLongStay
      result.get.masterRooms shouldBe Vector(nhaMasterRoom3)
    }

    "masterRoom list have one entry when original cheapestRoom matches many master or child RoomTypeId" in {
      val nhaResponseWithMasterRooms = nhaResponse
        .withMasterRooms(Vector(nhaMasterRoom1, nhaMasterRoom2, nhaMasterRoom3, nhaMasterRoom4))
        .build()
      val result =
        testExternalService.updateNhaWithCheapestMasterRoom(childRoom31.typeId.get, Some(nhaResponseWithMasterRooms))
      result shouldNot be(None)
      result.get.noOfBedrooms shouldBe nhaResponseWithMasterRooms.noOfBedrooms
      result.get.noOfBeds shouldBe nhaResponseWithMasterRooms.noOfBeds
      result.get.houseRules shouldBe nhaResponseWithMasterRooms.houseRules
      result.get.hostLevels shouldBe nhaResponseWithMasterRooms.hostLevels
      result.get.isRareFind shouldBe nhaResponseWithMasterRooms.isRareFind
      result.get.supportedLongStay shouldBe nhaResponseWithMasterRooms.supportedLongStay
      result.get.masterRooms shouldBe Vector(nhaMasterRoom3)
    }

  }

  "updateRequestForRoomFilterCriteria" should {

    val enrichedHotelWithOutContent = aValidEnrichedHotelWithoutContent

    "update request with filter and add RoomIds to room Request" in {

      implicit val request =
        aValidPropertyRequest.copy(room = Some(RoomRequestPAPI(isContentOnly = Some(true), filterCriteria = None)))
      implicit val contextHolder = createContextHolderFromRequest(request)
      val uRequest = testExternalService.updateRequestWithRoomFilterCriteria(
        (DFMetaResult(), Map(123L -> enrichedHotelWithOutContent))
      )

      uRequest.room.get.filterCriteria shouldBe Option(
        List[RoomFilterCriteria](RoomFilterCriteria(123L, Set(456L), Some(Set(456))))
      )

      uRequest.room.get.dynamicallyMappedRooms shouldBe None

    }

    "update request with dynamic mapping output from DF" in {

      implicit val request =
        aValidPropertyRequest.copy(room = Some(RoomRequestPAPI(isContentOnly = Some(true), filterCriteria = None)))
      implicit val contextHolder = createContextHolderFromRequest(request)
      val uRequest = testExternalService.updateRequestWithRoomFilterCriteria(
        (
          DFMetaResult(),
          Map(
            123L -> enrichedHotelWithOutContent.copy(rooms =
              Seq(
                aValidEnrichedChildRoom.copy(pricingRoomOption =
                  Option(aValidDFRoom.copy(dynamicRoomMappingToken = Option("some encoded data")))
                ),
                aValidEnrichedChildRoom.copy(
                  typeId = Some(1000),
                  masterTypeId = Some(455),
                  pricingRoomOption =
                    Option(aValidDFRoom.copy(dynamicRoomMappingToken = Option("some other encoded data")))
                ),
                aValidEnrichedChildRoom.copy(
                  typeId = Some(1001),
                  pricingRoomOption = Option(aValidDFRoom.copy(dynamicRoomMappingToken = None))
                )
              )
            )
          )
        )
      )

      uRequest.room.get.filterCriteria shouldBe Option(
        List[RoomFilterCriteria](RoomFilterCriteria(123L, Set(455L, 456L), Some(Set(455, 456))))
      )

      uRequest.room.get.dynamicallyMappedRooms shouldBe Option(
        List[String]("some encoded data", "some other encoded data")
      )
    }
  }

  "setSupportChildRateData" should {
    val enrichedHotelWithContent = aValidEnrichedHotelWithContent

    "should set as true when rateCategoryIdsWithChildRateEnabled has value" in {
      val rateCategoriesResponseMock = RateCategoriesResponse(aValidHotelId, Vector.empty, Vector.empty, Vector(123))

      val hotel = testExternalService
        .setSupportChildRateData(Some(rateCategoriesResponseMock), Some(enrichedHotelWithContent))
        .get

      hotel.isSupportChildRate shouldBe Some(true)
    }

    "should set as true when no rateCategoryIdsWithChildRateEnabled" in {
      val rateCategoriesResponseEmptyMock =
        RateCategoriesResponse(aValidHotelId, Vector.empty, Vector.empty, Vector.empty)

      val hotel = testExternalService
        .setSupportChildRateData(Some(rateCategoriesResponseEmptyMock), Some(enrichedHotelWithContent))
        .get

      hotel.isSupportChildRate shouldBe Some(false)
    }
  }

  "RateCategoriesService for Properties " should {
    "getRateCategories returned" in {
      implicit val request =
        aValidPropertyRequest.withEscapeRateCategories().withSearchType(SearchTypes.Properties).build()
      implicit val contextHolder = createContextHolderFromRequest(request)
      implicit val ecMock        = ExecutionContexts.defaultContext
      val expected = mutable.Map(aValidHotelId -> RateCategoriesResponse(aValidHotelId, Vector.empty, Vector.empty))
      when(mockRateCategoriesService.process(any(), any(), any(), any(), any())(any()))
        .thenReturn(Future.successful(expected))
      val rateCategoriesF = testExternalService.getRateCategories()
      whenReady(rateCategoriesF) { rateCategories =>
        rateCategories shouldEqual expected
      }
    }

    "getRateCategories not returned when not Escapes" in {
      implicit val request = aValidPropertyRequest.withRateCategories().withSearchType(SearchTypes.Properties).build()
      implicit val contextHolder = createContextHolderFromRequest(request)
      implicit val ecMock        = ExecutionContexts.defaultContext
      val rateCategoriesF        = testExternalService.getRateCategories()
      whenReady(rateCategoriesF) { rateCategories =>
        rateCategories shouldEqual Map.empty
      }
    }
  }

  "getEnrichedHotel" should {
    "return growthProgramInfo correctly" in {
      val request                               = mockPropertyRequest
      implicit val contextHolder: ContextHolder = createContextHolderFromRequest(request)
      implicit val cmsService: PAPICMSService   = mockCmsService

      val growthProgramInfoOpt = Some(GrowthProgramInfo(badges = List("BADGE")))

      val enrichedHotelWithOutContent = EnrichedHotelWithOutContent(
        10632L,
        9395L,
        106L,
        10675,
        None,
        None,
        Seq.empty,
        None,
        None,
        None,
        None,
        None,
        PropertyTooltip(),
        List.empty,
        true,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        Map.empty,
        List.empty,
        growthProgramInfo = growthProgramInfoOpt
      )

      val enrichedHotelWithContent = testExternalService.getEnrichedHotel(
        request,
        enrichedHotelWithOutContent,
        None,
        None,
        None,
        List.empty,
        Map.empty,
        List.empty,
        Vector.empty,
        isExternalVipDisplayEnabled = false,
        isRoomFilterEnabled = false,
        isValidEscapesFlow = false,
        isValidMergedDayUseOvernightFlow = false
      )

      enrichedHotelWithContent.map(_.growthProgramInfo shouldBe growthProgramInfoOpt)
    }
  }

  "searchCommon" should {
    "return growthProgramInfo correctly" in {
      val mockPropertyContext: PropertyContext = PropertyContext(
        deviceTypeId = None,
        searchId = "search",
        locale = "en-us",
        cid = 0,
        memberId = 2L,
        origin = "A1",
        platform = 1,
        propertyIds = List(propertyId3)
      )

      implicit val ecMock: ExecutionContextExecutor = ExecutionContexts.defaultContext
      implicit val request: PropertyRequest = mockPropertyRequest
        .copy(context = mockPropertyContext)
      implicit val cmsService: PAPICMSService                 = mockCmsService
      implicit val languageRepository: LanguageRepositoryTest = mockLanguageRepository
      implicit val contextHolder: ContextHolder               = createContextHolderFromRequest(request)

      def mockRequest: PropertySearchRequest = {
        val request = Mockito.mock(classOf[PropertySearchRequest])
        when(request.context).thenReturn(Mockito.mock(classOf[ContextRequest]))
        when(request.context.clientInfo).thenReturn(Mockito.mock(classOf[ClientInfo]))
        when(request.context.clientInfo.languageUse).thenReturn(1)
        when(request.pricing).thenReturn(Mockito.mock(classOf[PricingRequest]))
        when(request.pricing.features).thenReturn((Mockito.mock(classOf[FeatureRequest])))
        when(request.pricing.features.priusId).thenReturn(11)
        when(request.isSSR).thenReturn(Some(true))
        when(request.propertyIds).thenReturn(Seq(propertyId3))

        request
      }

      val growthProgramInfoOpt = Some(GrowthProgramInfo(badges = List("BADGE")))

      val enrichedHotelWithOutContent = EnrichedHotelWithOutContent(
        10632L,
        9395L,
        106L,
        10675,
        None,
        None,
        Seq.empty,
        None,
        None,
        None,
        None,
        None,
        PropertyTooltip(),
        List.empty,
        true,
        None,
        None,
        None,
        None,
        None,
        None,
        None,
        Map.empty,
        List.empty,
        growthProgramInfo = growthProgramInfoOpt
      )

      mockSummaryServiceResponse(
        List(propertyId3),
        request
      )

      when(testExternalService.soldOutPricesService.getSoldOutRoomsPrice(any(), any(), any())(any(), any()))
        .thenReturn(Future.successful[Map[HotelIdType, List[SoldOutRoomInfo]]](Map.empty))

      when(testExternalService.priceStreamService.getCityMetaInfoWithPropertyRequest(any(), any(), any()))
        .thenReturn(Future.successful(CityMetaInfoResponse(0, None, None, None, None)))

      when(testExternalService.dfPropertyWithCacheToken.process(any(), any(), any(), any(), any())(any()))
        .thenReturn(
          Future.successful(
            WithPropertyToken[WithHotelIdMap[EnrichedHotelWithOutContent]](
              DFMetaResult(),
              WithHotelIdMap(
                propertyId3 -> enrichedHotelWithOutContent
              )
            )
          )
        )

      when(mockCmsService.toCmsTextFuture(any(), any())(any(), any()))
        .thenReturn(Future.successful(Some("any word")))

      val sfRequest = List(PropertyAPIRequestModel(mockRequest, SuggestPrice()))

      val pricingAndContentsF = testExternalService.searchCommon(
        sfRequest,
        aValidHotelToCityId
      )

      whenReady(pricingAndContentsF) { pricingAndContents =>
        pricingAndContents.data.headOption.flatMap(_.hotel.flatMap(_.growthProgramInfo)) shouldBe growthProgramInfoOpt
      }
    }
  }

  "recommendedRooms" should {

    implicit val ecMock         = ExecutionContexts.defaultContext
    implicit val langRepository = Mockito.mock(classOf[LanguageRepository])

    "return empty recommended rooms if request recommend is False" in {
      implicit val contextHolder =
        ContextGenerator.createContextHolder()

      val propertyRequest = aValidPropertyRequest.copy(
        context = aValidPropertyRequest.context.copy(propertyIds = List(1L)),
        room = Some(aValidRoomRequest.copy(includeRoomRecommendation = Some(false)))
      )

      val resultF = testExternalService.getRecommendedRooms(propertyRequest)(ecMock, langRepository, contextHolder)

      whenReady(resultF) { result =>
        result should not be empty
      }

      verify(testExternalService.zenithService, times(0))
        .getRecommendedRooms(any(), any())(any(), any(), any())
      verify(testExternalService.recommendedRoomService, times(0))
        .getRecommendedRooms(any(), any())(any())
    }

    "return recommended rooms from Zenith when experiment migrateRoomRecoFromZenithToFs is A and negative test is A" in {

      implicit val contextHolder =
        ContextGenerator.createContextHolder()
      val propertyId = 10L
      val propertyRequest = aValidPropertyRequest.copy(
        context = aValidPropertyRequest.context.copy(propertyIds = List(propertyId)),
        room = Some(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
      )

      mockZenithRecommendedRoomsResponse(propertyId)

      val resultF = testExternalService.getRecommendedRooms(propertyRequest)(ecMock, langRepository, contextHolder)

      whenReady(resultF) { result =>
        result.find { case (pId, _) => pId == propertyId }.get shouldBe (propertyId, RoomRecoResponse.defaultInstance)
      }

      verify(testExternalService.recommendedRoomService, times(0))
        .getRecommendedRooms(any(), any())(any())
      verify(testExternalService.zenithService, times(1))
        .getRecommendedRooms(mockitoEq(propertyId), any[PropertyRequest]())(any(), any(), any())
    }

    "return empty recommended rooms from Zenith when experiment migrateRoomRecoFromZenithToFs is A " +
      "but negative test is B" in {

        implicit val contextHolder =
          ContextGenerator.createContextHolder(
            Some(Seq(experiments.negativeTestRoomReco))
          )

        val propertyId = 100L
        val propertyRequest = aValidPropertyRequest.copy(
          context = aValidPropertyRequest.context.copy(propertyIds = List(propertyId)),
          room = Some(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
        )

        mockRecommendedRoomsResponse(propertyId)

        val resultF = testExternalService.getRecommendedRooms(propertyRequest)(ecMock, langRepository, contextHolder)
        whenReady(resultF) { result =>
          result.find { case (pId, _) => pId == propertyId }.get shouldBe (propertyId, RoomRecoResponse.defaultInstance)
        }
        verify(testExternalService.recommendedRoomService, times(0)).getRecommendedRooms(any(), any())(any())
        verify(testExternalService.zenithService, times(0))
          .getRecommendedRooms(mockitoEq(propertyId), any())(any(), any(), any())
      }

    "return recommended rooms from recommended room service when experiment migrateRoomRecoFromZenithToFs is B" in {

      implicit val contextHolder =
        ContextGenerator.createContextHolder(Some(Seq(experiments.migrateRoomRecoFromZenithToFs)))

      val propertyId = 1000L
      val propertyRequest = aValidPropertyRequest.copy(
        context = aValidPropertyRequest.context.copy(propertyIds = List(propertyId)),
        room = Some(aValidRoomRequest.copy(includeRoomRecommendation = Some(true)))
      )

      mockRecommendedRoomsResponse(propertyId)

      val resultF = testExternalService.getRecommendedRooms(propertyRequest)(ecMock, langRepository, contextHolder)
      whenReady(resultF) { result =>
        result.find { case (pId, _) => pId == propertyId }.get shouldBe (propertyId, RoomRecoResponse.defaultInstance)
      }

      verify(testExternalService.recommendedRoomService, times(1)).getRecommendedRooms(any(), any())(any())
      verify(testExternalService.zenithService, times(0))
        .getRecommendedRooms(mockitoEq(propertyId), any())(any(), any(), any())
    }
  }

  trait EnrichmentScope extends Scope {

    val request = mockPropertyRequest.copy(room =
      Some(RoomRequestPAPI(isContentOnly = Some(true), showChildRoomInContentOnly = Some(true)))
    )

    val enrichedChildRoom = aValidEnrichedChildRoom.copy(typeId = Some(aValidChildRoomMeta.roomId))

    when(
      enrichContentForChildRoomHelper.updateChildRoomWithContent(
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any(),
        any()
      )(
        any(),
        any()
      )
    )
      .thenReturn(List(enrichedChildRoom))

    val masterRoomResponses: Vector[MasterRoomResponse] = Vector(
      aValidMasterRoomResponse
        .copy(
          metadata = aValidMasterRoomMeta.copy(provider =
            Some(aValidRoomProviderInformation.copy(directConnectProviderId = Some(23)))
          ),
          children = Vector(
            aValidChildRoomMeta.copy(provider =
              Some(aValidRoomProviderInformation.copy(directConnectProviderId = Some(24)))
            )
          )
        )
    )

  }

}
