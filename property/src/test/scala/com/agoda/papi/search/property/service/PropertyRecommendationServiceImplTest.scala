package com.agoda.papi.search.property.service

import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.tracing.noop.WithTracerNoop
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.service.fs.FeatureStoreService
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExecutionContexts }
import enumerations.RecommendationTypes
import framework.MockFrameworkHelper
import framework.repository.LanguageRepositoryTest
import org.mockito.ArgumentMatchers.any
import org.mockito.Mockito.when
import org.scalatest.funsuite.AnyFunSuite
import org.scalatestplus.mockito.MockitoSugar
import request.{ PropertyRecommendationRequest, Recommendation }
import com.agoda.papi.search.property.service.ClusterService.{
  ContentSummaryServiceWrapper,
  DFRecommendationServiceWrapper
}
import com.agoda.papi.search.property.service.jarvis.JarvisService
import util.builders.TestDataBuilders._

import scala.concurrent.{ ExecutionContext, Future }

class PropertyRecommendationServiceImplTest extends AnyFunSuite with MockitoSugar with MockFrameworkHelper {

  val underTest: PropertyRecommendationServiceImpl = new PropertyRecommendationServiceImpl with WithTracerNoop {
    implicit override val ec: ExecutionContext                           = ExecutionContexts.defaultContext
    implicit override val cmsService: PAPICMSService                     = mock[PAPICMSService]
    implicit override val languageRepository: LanguageRepository         = new LanguageRepositoryTest
    implicit override val bookingHistoryService: BookingHistoryService   = mock[BookingHistoryService]
    override val zenithService: ZenithService                            = mock[ZenithService]
    override val summaryService: ContentSummaryServiceWrapper            = mock[ContentSummaryServiceWrapper]
    override val requestBuilderService: RequestBuilderService            = mock[RequestBuilderService]
    override val dfRecommendationService: DFRecommendationServiceWrapper = mock[DFRecommendationServiceWrapper]
    override val reportingService: MetricsReporter                       = mock[MetricsReporter]
    override val cityObjectInfoService: CityObjectInfoService            = mock[CityObjectInfoService]
    override val jarvisService: JarvisService                            = mock[JarvisService]
    override val featureStoreService: FeatureStoreService                = mock[FeatureStoreService]
  }

  private val aValidRecommendation = Recommendation(4, List.empty)

  val comparisonRecommendation: Recommendation =
    aValidRecommendation.copy(recommendationType = Some(RecommendationTypes.Comparison))

  val comparisonPropertyRecommendation: PropertyRecommendationRequest = PropertyRecommendationRequest(
    context = aValidContext.copy(propertyIds = List(100001L)),
    pricing = Some(aValidPricingRequest),
    recommendation = comparisonRecommendation,
    cityId = None
  )

  private val recentlyViewedRecommendation: Recommendation =
    aValidRecommendation.copy(recommendationType = Some(RecommendationTypes.RecentlyViewed))

  val recentlyViewedRecommendationRequest: PropertyRecommendationRequest = PropertyRecommendationRequest(
    context = aValidContext,
    pricing = Some(aValidPricingRequest),
    recommendation = recentlyViewedRecommendation,
    cityId = Some(9395L)
  )

  val mapRecommendation: PropertyRecommendationRequest = PropertyRecommendationRequest(
    context = aValidContext,
    pricing = Some(aValidPricingRequest),
    recommendation = aValidRecommendation.copy(recommendationType = Some(RecommendationTypes.MapRecommendation)),
    cityId = Some(9395L)
  )

  test(s"convert propertyReco to propertyRequest on Similar Property request") {
    val result = underTest.convertToPropertyRequest(comparisonPropertyRecommendation)
    assert(result.context === aValidContext.copy(propertyIds = List(100001)))
    assert(result.pricing === Some(aValidPricingRequest))
    assert(result.recommendation === Some(comparisonRecommendation))
    assert(result.personalizedRecommendation === None)
  }

  test(s"convert propertyReco to propertyRequest on Personalized request") {
    val result = underTest.convertToPropertyRequest(recentlyViewedRecommendationRequest)
    assert(result.pricing === Some(aValidPricingRequest))
    assert(result.recommendation === None)
    assert(result.personalizedRecommendation === Some(recentlyViewedRecommendation))
  }

  test("doSearchRecommendation should return empty when recommendation type is not the type we expected") {
    val mockRequest = mapRecommendation
    when(underTest.cityObjectInfoService.getCityObjectInfo(any(), any())(any(), any()))
      .thenReturn(Future.successful(aValidObjectInfo))
    val result = underTest.searchRecommendation(mockRequest, mock[ContextHolder])

    whenReady(result) { res =>
      assert(res.recommendations === List.empty)
    }
  }

  test("doSearchRecommendation should throw error when cityId None when searching for RecentlyViewedRecommendation") {
    val mockRequest = recentlyViewedRecommendationRequest.copy(
      cityId = None
    )

    intercept[NoSuchElementException] {
      underTest.searchRecommendation(mockRequest, mock[ContextHolder])
    }

  }

}
