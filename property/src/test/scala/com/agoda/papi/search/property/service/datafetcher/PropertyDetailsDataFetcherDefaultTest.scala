package com.agoda.papi.search.property.service.datafetcher

import com.agoda.common.graphql.model.Wrapper
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.information.{ InformationResponse, LocalInformation }
import com.agoda.content.models.propertycontent.{
  ContentImagesResponse,
  ContentInformationSummary,
  ContentReviewScoreResponse,
  ContentReviewSummariesResponse
}
import com.agoda.papi.content.graphql._
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExperimentContext, WithCorrelationId }
import com.agoda.papi.search.internalmodel.growthprogram.{ GrowthProgramInfoRequestEnvelope, PAPIGrowthProgramInfo }
import com.agoda.papi.search.internalmodel.promocode.{ BannerCampaignGroupRequest, BannerCampaignInfo }
import com.agoda.papi.search.internalmodel.response.HostProfileResponse
import com.agoda.papi.search.types.PropertyId
import com.agoda.platform.service.context.GlobalContext
import com.agoda.papi.search.internalmodel.request.HostProfileRequest

import scala.concurrent.{ ExecutionContext, Future }

class PropertyDetailsDataFetcherDefaultTest extends PropertyDetailsDataFetcher {

  override def fetchContentImagesResponse(
      contentGroupImagesRequest: ContentGroupImagesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentImagesResponse]]] =
    Future.successful(Map.empty)

  override def fetchContentReviewScoreResponse(
      contentReviewScoreRequest: ContentGroupReviewScoreRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentReviewScoreResponse]]] =
    Future.successful(Map.empty)

  override def fetchContentReviewSummariesResponse(
      contentReviewSummariesRequest: ContentGroupReviewSummariesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentReviewSummariesResponse]]] =
    Future.successful(Map.empty)

  override def fetchContentInformationSummaryResponse(
      contentInformationSummaryRequest: ContentInformationSummaryGroupRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentInformationSummary]]] =
    Future.successful(Map.empty)

  override def fetchContentInformationResponse(
      contentInformationRequest: ContentInformationGroupRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[InformationResponse]]] =
    Future.successful(Map.empty)

  override def fetchContentExperiencesResponse(
      contentExperienceRequest: ContentGroupExperienceRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[Experiences]]] =
    Future.successful(Map.empty)

  override def fetchLocalInformationResponse(
      contentLocalInformationRequest: ContentGroupLocalInformationRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[LocalInformation]]] =
    Future.successful(Map.empty)

  override def fetchEngagementResponse(
      contentGroupEngagementRequest: ContentGroupEngagementRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    Future.successful(Map.empty)

  override def fetchContentFeaturesResponse(
      contentGroupFeaturesRequest: ContentGroupFeaturesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    Future.successful(Map.empty)

  override def fetchContentHighlightsResponse(
      contentGroupHighlightsRequestEnvelope: ContentGroupHighlightsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    Future.successful(Map.empty)

  override def fetchContentQnaResponse(
      contentGroupQnaRequestEnvelope: ContentGroupQnaRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    Future.successful(Map.empty)

  override def fetchContentRateCategoryResponse(
      contentGroupRateCategoryRequestEnvelope: ContentGroupRateCategoryRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    Future.successful(Map.empty)

  override def fetchHostProfile(
      propertyId: PropertyId,
      hostProfileRequest: Option[HostProfileRequest]
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Option[HostProfileResponse]] =
    Future.successful(None)

  override def fetchPropertyBannerCampaignResponse(
      bannerCampaignGroupRequest: BannerCampaignGroupRequest,
      globalContext: GlobalContext
  )(implicit
      ec: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Map[PropertyId, List[BannerCampaignInfo]]] =
    Future.successful(Map.empty)

  override def fetchGrowthProgramInfoResponse(
      growthProgramInfoRequestEnvelope: GrowthProgramInfoRequestEnvelope
  )(implicit
      ec: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Map[PropertyId, PAPIGrowthProgramInfo]] = Future.successful(Map.empty)

  override def fetchContentTopicsResponse(
      contentGroupTopicsRequestEnvelope: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] = Future.successful(Map.empty)

}
