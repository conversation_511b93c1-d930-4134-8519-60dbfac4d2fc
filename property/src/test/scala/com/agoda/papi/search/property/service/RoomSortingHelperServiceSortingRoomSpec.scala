package com.agoda.papi.search.property.service

import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.core.search.models.CancellationTypes
import com.agoda.papi.enums.room.{ ExternalLoyaltyItem, ExternalLoyaltyItemType }
import com.agoda.papi.search.common.util.CoreDataExamples.aValidContextHolder
import com.agoda.papi.search.common.util.context.ContextGenerator.{
  createContextHolderFromRequest,
  ForceBExperimentContext
}
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextHolder, ExecutionContexts }
import com.agoda.papi.search.property.service.roomSorting.RoomSortingHelperServiceImpl
import com.agoda.supplier.common.core.SQLContext
import com.agoda.supplier.common.models.SupplierId
import com.agoda.supplier.common.services.{ OverrideCriteria, SupplierMetadataService, SupplierMetadataServiceImpl }
import constant.StayPackageType
import io.opentracing.Tracer
import models.starfruit.SuggestedPrice
import org.mockito.Mockito
import org.scalatest.funspec.AnyFunSpec
import org.scalatest.matchers.should.Matchers
import repository.ProviderIds
import transformers.{ EnrichedChildRoom, EnrichedMasterRoom, EnrichedPricing }
import util.builders.TestDataBuilders._

import scala.concurrent.ExecutionContextExecutor

class RoomSortingHelperServiceSortingRoomSpec extends AnyFunSpec with Matchers {

  implicit val executionContext: ExecutionContextExecutor = ExecutionContexts.defaultContext
  implicit val contextHolder: ContextHolder               = aValidContextHolder

  val supplierMetadataService: SupplierMetadataService = new SupplierMetadataServiceImpl(
    Mockito.mock(classOf[SQLContext])
  ) {

    override def isAgodaBrand(supplier: SupplierId, overrideCriteria: Option[OverrideCriteria] = None): Boolean =
      supplier match {
        case ProviderIds.Ycs => true
        case _               => false
      }

  }

  val roomSortingHelperService =
    new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

  def reportingService: MetricsReporter = NoOpMetricsReporter

  def tracer: Tracer = NoOpTracer

  describe("Master room sorting") {
    // children rooms
    val collectionCashback100FitYcsChild = mockRoom(
      100,
      130,
      ExternalLoyaltyItem.TierZero,
      isFitCapacity = true,
      displayPriceAfterCashback = Option(100, 120),
      isAgodaDirectSupply = true
    )
    val collectionCashback100FitChild =
      mockRoom(
        100,
        120,
        ExternalLoyaltyItem.TierOne,
        isFitCapacity = true,
        displayPriceAfterCashback = Option(100, 120)
      )
    val collectionCashback100UnfitYcsChild =
      mockRoom(
        90,
        130,
        ExternalLoyaltyItem.TierZero,
        displayPriceAfterCashback = Option(100, 120),
        isAgodaDirectSupply = true
      )
    val collectionCashback100UnfitChild =
      mockRoom(120, 140, ExternalLoyaltyItem.TierOne, displayPriceAfterCashback = Option(100, 120))
    val collection110UnfitChild    = mockRoom(110, 130, ExternalLoyaltyItem.TierZero)
    val collection120UnfitYcsChild = mockRoom(120, 140, ExternalLoyaltyItem.TierOne, isAgodaDirectSupply = true)
    val nonCollection90FitChild    = mockRoom(90, 100, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val nonCollection90UnfitChild  = mockRoom(90, 100, ExternalLoyaltyItem.Unknown)
    val nonCollection100FitYcsChild =
      mockRoom(100, 110, ExternalLoyaltyItem.Unknown, isFitCapacity = true, isAgodaDirectSupply = true)
    val nonCollection100FitChild = mockRoom(100, 110, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val nonCollection200FitYcsChild =
      mockRoom(200, 210, ExternalLoyaltyItem.Unknown, isFitCapacity = true, isAgodaDirectSupply = true)
    val nonCollectionCashback100UnfitYcsChild =
      mockRoom(
        110,
        120,
        ExternalLoyaltyItem.Unknown,
        displayPriceAfterCashback = Option(100, 120),
        isAgodaDirectSupply = true
      )
    val nonCollection100UnfitChild    = mockRoom(100, 110, ExternalLoyaltyItem.Unknown)
    val nonCollection110UnfitYcsChild = mockRoom(110, 120, ExternalLoyaltyItem.Unknown, isAgodaDirectSupply = true)

    // master rooms
    val collectionCashback100FitYcs = getMasterRoom(List(collectionCashback100FitYcsChild))
    val collectionCashback100Fit    = getMasterRoom(List(collectionCashback100FitChild))
    val collectionCashback100UnfitYcs =
      getMasterRoom(List(collectionCashback100UnfitYcsChild))
    val collectionCashback100Unfit = getMasterRoom(List(collectionCashback100UnfitChild))
    val collection110Unfit         = getMasterRoom(List(collection110UnfitChild))
    val collection120UnfitYcs      = getMasterRoom(List(collection120UnfitYcsChild))
    val nonCollection90Fit         = getMasterRoom(List(nonCollection90FitChild))
    val nonCollection90Unfit       = getMasterRoom(List(nonCollection90UnfitChild))
    val nonCollection100FitYcs     = getMasterRoom(List(nonCollection100FitYcsChild))
    val nonCollection100Fit        = getMasterRoom(List(nonCollection100FitChild))
    val nonCollection200FitYcs     = getMasterRoom(List(nonCollection200FitYcsChild))
    val nonCollectionCashback100UnfitYcs =
      getMasterRoom(List(nonCollectionCashback100UnfitYcsChild))
    val nonCollection100Unfit    = getMasterRoom(List(nonCollection100UnfitChild))
    val nonCollection110UnfitYcs = getMasterRoom(List(nonCollection110UnfitYcsChild))
    val defaultRoomList = List(
      nonCollection90Fit,
      nonCollection100Fit,
      nonCollection100FitYcs,
      nonCollection100Unfit,
      nonCollectionCashback100UnfitYcs,
      nonCollection110UnfitYcs,
      nonCollection200FitYcs,
      collection110Unfit,
      collection120UnfitYcs,
      collectionCashback100Fit,
      collectionCashback100FitYcs,
      collectionCashback100Unfit,
      collectionCashback100UnfitYcs
    )
    it("should return correct order of master room with exclusive price") {
      val result = roomSortingHelperService.sortRooms(
        defaultRoomList,
        Some(SuggestedPrice.Exclusive),
        Some("THB")
      )
      result(0) shouldBe collectionCashback100FitYcs.copy(
        childrenRooms = List(collectionCashback100FitYcsChild.copy(priority = Some(1.0)))
      )
      result(1) shouldBe collectionCashback100Fit.copy(
        childrenRooms = List(collectionCashback100FitChild.copy(priority = Some(1.0)))
      )
      result(2) shouldBe collectionCashback100UnfitYcs.copy(
        childrenRooms = List(collectionCashback100UnfitYcsChild.copy(priority = Some(1.0)))
      )
      result(3) shouldBe collectionCashback100Unfit.copy(
        childrenRooms = List(collectionCashback100UnfitChild.copy(priority = Some(1.0)))
      )
      result(4) shouldBe collection110Unfit.copy(
        childrenRooms = List(collection110UnfitChild.copy(priority = Some(1.0)))
      )
      result(5) shouldBe collection120UnfitYcs.copy(
        childrenRooms = List(collection120UnfitYcsChild.copy(priority = Some(1.0)))
      )
      result(6) shouldBe nonCollection90Fit.copy(
        childrenRooms = List(nonCollection90FitChild.copy(priority = Some(1.0)))
      )
      result(7) shouldBe nonCollection100FitYcs.copy(
        childrenRooms = List(nonCollection100FitYcsChild.copy(priority = Some(1.0)))
      )
      result(8) shouldBe nonCollection100Fit.copy(
        childrenRooms = List(nonCollection100FitChild.copy(priority = Some(1.0)))
      )
      result(9) shouldBe nonCollection200FitYcs.copy(
        childrenRooms = List(nonCollection200FitYcsChild.copy(priority = Some(1.0)))
      )
      result(10) shouldBe nonCollectionCashback100UnfitYcs.copy(childrenRooms =
        List(nonCollectionCashback100UnfitYcsChild.copy(priority = Some(1.0)))
      )
      result(11) shouldBe
        nonCollection100Unfit.copy(childrenRooms = List(nonCollection100UnfitChild.copy(priority = Some(1.0))))
      result(12) shouldBe
        nonCollection110UnfitYcs.copy(childrenRooms = List(nonCollection110UnfitYcsChild.copy(priority = Some(1.0))))
    }

    it("should return correct order of master room with inclusive price") {
      val result = roomSortingHelperService.sortRooms(
        defaultRoomList,
        Some(SuggestedPrice.AllInclusive),
        Some("THB")
      )
      result(0) shouldBe collectionCashback100FitYcs.copy(
        childrenRooms = List(collectionCashback100FitYcsChild.copy(priority = Some(1.0)))
      )
      result(1) shouldBe collectionCashback100Fit.copy(
        childrenRooms = List(collectionCashback100FitChild.copy(priority = Some(1.0)))
      )
      result(2) shouldBe collectionCashback100UnfitYcs.copy(
        childrenRooms = List(collectionCashback100UnfitYcsChild.copy(priority = Some(1.0)))
      )
      result(3) shouldBe collectionCashback100Unfit.copy(
        childrenRooms = List(collectionCashback100UnfitChild.copy(priority = Some(1.0)))
      )
      result(4) shouldBe collection110Unfit.copy(
        childrenRooms = List(collection110UnfitChild.copy(priority = Some(1.0)))
      )
      result(5) shouldBe collection120UnfitYcs.copy(
        childrenRooms = List(collection120UnfitYcsChild.copy(priority = Some(1.0)))
      )
      result(6) shouldBe nonCollection90Fit.copy(
        childrenRooms = List(nonCollection90FitChild.copy(priority = Some(1.0)))
      )
      result(7) shouldBe nonCollection100FitYcs.copy(
        childrenRooms = List(nonCollection100FitYcsChild.copy(priority = Some(1.0)))
      )
      result(8) shouldBe nonCollection100Fit.copy(
        childrenRooms = List(nonCollection100FitChild.copy(priority = Some(1.0)))
      )
      result(9) shouldBe nonCollection200FitYcs.copy(
        childrenRooms = List(nonCollection200FitYcsChild.copy(priority = Some(1.0)))
      )
      result(10) shouldBe nonCollection100Unfit.copy(
        childrenRooms = List(nonCollection100UnfitChild.copy(priority = Some(1.0)))
      )
      result(11) shouldBe nonCollectionCashback100UnfitYcs.copy(childrenRooms =
        List(nonCollectionCashback100UnfitYcsChild.copy(priority = Some(1.0)))
      ) // inclusive 120
      result(12) shouldBe nonCollection110UnfitYcs.copy(
        childrenRooms = List(nonCollection110UnfitYcsChild.copy(priority = Some(1.0)))
      )
    }

    it("should sort collection before non collection") {
      val result = roomSortingHelperService.sortRooms(
        List(
          nonCollection90Fit,
          collectionCashback100Fit,
          nonCollection100Fit,
          collectionCashback100FitYcs
        ),
        Some(SuggestedPrice.Exclusive),
        Some("THB")
      )
      result(0) shouldBe collectionCashback100FitYcs.copy(
        childrenRooms = List(collectionCashback100FitYcsChild.copy(priority = Some(1.0)))
      )
      result(1) shouldBe collectionCashback100Fit.copy(
        childrenRooms = List(collectionCashback100FitChild.copy(priority = Some(1.0)))
      )
      result(2) shouldBe nonCollection90Fit.copy(
        childrenRooms = List(nonCollection90FitChild.copy(priority = Some(1.0)))
      )
      result(3) shouldBe nonCollection100Fit.copy(
        childrenRooms = List(nonCollection100FitChild.copy(priority = Some(1.0)))
      )
    }

    describe("collection") {
      it("should sort lower price to higher price") {
        val result = roomSortingHelperService.sortRooms(
          List(
            collectionCashback100Fit,
            collection110Unfit,
            collection120UnfitYcs
          ),
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        result(0) shouldBe collectionCashback100Fit.copy(
          childrenRooms = List(collectionCashback100FitChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe collection110Unfit.copy(
          childrenRooms = List(collection110UnfitChild.copy(priority = Some(1.0)))
        )
        result(2) shouldBe collection120UnfitYcs.copy(
          childrenRooms = List(collection120UnfitYcsChild.copy(priority = Some(1.0)))
        )
      }

      it("should sort with fit before unfit when prices are tied") {
        val result = roomSortingHelperService.sortRooms(
          List(
            collectionCashback100Unfit,
            collectionCashback100Fit
          ),
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        result(0) shouldBe collectionCashback100Fit.copy(
          childrenRooms = List(collectionCashback100FitChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe collectionCashback100Unfit.copy(
          childrenRooms = List(collectionCashback100UnfitChild.copy(priority = Some(1.0)))
        )
      }

      it("should sort with YCS before non YCS when prices and fit capacity are tied") {
        val result = roomSortingHelperService.sortRooms(
          List(
            collectionCashback100Fit,
            collectionCashback100FitYcs
          ),
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        result(0) shouldBe collectionCashback100FitYcs.copy(
          childrenRooms = List(collectionCashback100FitYcsChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe collectionCashback100Fit.copy(
          childrenRooms = List(collectionCashback100FitChild.copy(priority = Some(1.0)))
        )
      }
    }
    describe("non collection") {
      it("should sort fit before unfit") {
        val result = roomSortingHelperService.sortRooms(
          List(
            nonCollection100Unfit,
            nonCollection200FitYcs,
            nonCollection90Unfit,
            nonCollection100Fit
          ),
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        result(0) shouldBe nonCollection100Fit.copy(
          childrenRooms = List(nonCollection100FitChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe nonCollection200FitYcs.copy(
          childrenRooms = List(nonCollection200FitYcsChild.copy(priority = Some(1.0)))
        )
        result(2) shouldBe nonCollection90Unfit.copy(
          childrenRooms = List(nonCollection90UnfitChild.copy(priority = Some(1.0)))
        )
        result(3) shouldBe nonCollection100Unfit.copy(
          childrenRooms = List(nonCollection100UnfitChild.copy(priority = Some(1.0)))
        )
      }

      it("should sort by price when fit capacity are tied") {
        val result = roomSortingHelperService.sortRooms(
          List(
            nonCollection100Fit,
            nonCollection90Fit,
            nonCollection200FitYcs
          ),
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        result(0) shouldBe nonCollection90Fit.copy(childrenRooms =
          List(nonCollection90FitChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe nonCollection100Fit.copy(childrenRooms =
          List(nonCollection100FitChild.copy(priority = Some(1.0)))
        )
        result(2) shouldBe nonCollection200FitYcs.copy(childrenRooms =
          List(nonCollection200FitYcsChild.copy(priority = Some(1.0)))
        )
      }

      it("should sort with YCS before non YCS when prices and fit capacity are tied") {
        val result = roomSortingHelperService.sortRooms(
          List(
            nonCollection100Fit,
            nonCollection100FitYcs
          ),
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        result(0) shouldBe nonCollection100FitYcs.copy(childrenRooms =
          List(nonCollection100FitYcsChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe nonCollection100Fit.copy(childrenRooms =
          List(nonCollection100FitChild.copy(priority = Some(1.0)))
        )
      }
    }
    describe("without show cashback") {
      val collection100FitChild =
        notShowCashBack(
          mockRoom(
            100,
            120,
            ExternalLoyaltyItem.TierOne,
            isFitCapacity = true,
            displayPriceAfterCashback = Option(100, 120)
          )
        )
      val collection90UnfitYcsChild =
        notShowCashBack(
          mockRoom(
            90,
            130,
            ExternalLoyaltyItem.TierZero,
            displayPriceAfterCashback = Option(100, 120),
            isAgodaDirectSupply = true
          )
        )
      val collection120UnfitChild =
        notShowCashBack(mockRoom(120, 140, ExternalLoyaltyItem.TierOne, displayPriceAfterCashback = Option(100, 120)))
      val nonCollection110fitYcsChild =
        notShowCashBack(
          mockRoom(
            110,
            120,
            ExternalLoyaltyItem.Unknown,
            isFitCapacity = true,
            displayPriceAfterCashback = Option(100, 120),
            isAgodaDirectSupply = true
          )
        )
      val nonCollection90fitChild = notShowCashBack(
        mockRoom(
          90,
          110,
          ExternalLoyaltyItem.Unknown,
          isFitCapacity = true,
          displayPriceAfterCashback = Option(100, 120)
        )
      )
      val nonCollection100fitYcsChild =
        notShowCashBack(
          mockRoom(
            100,
            120,
            ExternalLoyaltyItem.Unknown,
            isFitCapacity = true,
            displayPriceAfterCashback = Option(100, 120),
            isAgodaDirectSupply = true
          )
        )
      val collection100Fit       = getMasterRoom(List(collection100FitChild))
      val collection90UnfitYcs   = getMasterRoom(List(collection90UnfitYcsChild))
      val collection120Unfit     = getMasterRoom(List(collection120UnfitChild))
      val nonCollection110fitYcs = getMasterRoom(List(nonCollection110fitYcsChild))
      val nonCollection90fit     = getMasterRoom(List(nonCollection90fitChild))
      val nonCollection100fitYcs = getMasterRoom(List(nonCollection100fitYcsChild))
      val roomsWithoutShowCashback = List(
        collection100Fit,
        collection90UnfitYcs,
        collection120Unfit,
        nonCollection110fitYcs,
        nonCollection90fit,
        nonCollection100fitYcs
      )
      it("should return correct order of master room with exclusive price") {
        val result = roomSortingHelperService.sortRooms(
          roomsWithoutShowCashback,
          Some(SuggestedPrice.Exclusive),
          Some("THB")
        )
        // collection
        result(0) shouldBe roomsWithoutShowCashback(1).copy(
          childrenRooms = List(collection90UnfitYcsChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe roomsWithoutShowCashback(0).copy(
          childrenRooms = List(collection100FitChild.copy(priority = Some(1.0)))
        )
        result(2) shouldBe roomsWithoutShowCashback(2).copy(
          childrenRooms = List(collection120UnfitChild.copy(priority = Some(1.0)))
        )
        // non collection
        result(3) shouldBe roomsWithoutShowCashback(4).copy(
          childrenRooms = List(nonCollection90fitChild.copy(priority = Some(1.0)))
        )
        result(4) shouldBe roomsWithoutShowCashback(5).copy(
          childrenRooms = List(nonCollection100fitYcsChild.copy(priority = Some(1.0)))
        )
        result(5) shouldBe roomsWithoutShowCashback(3).copy(
          childrenRooms = List(nonCollection110fitYcsChild.copy(priority = Some(1.0)))
        )
      }
      it("should return correct order of master room with inclusive price") {
        val result = roomSortingHelperService.sortRooms(
          roomsWithoutShowCashback,
          Some(SuggestedPrice.AllInclusive),
          Some("THB")
        )
        // collection
        result(0) shouldBe roomsWithoutShowCashback(0).copy(
          childrenRooms = List(collection100FitChild.copy(priority = Some(1.0)))
        )
        result(1) shouldBe roomsWithoutShowCashback(1).copy(
          childrenRooms = List(collection90UnfitYcsChild.copy(priority = Some(1.0)))
        )
        result(2) shouldBe roomsWithoutShowCashback(2).copy(
          childrenRooms = List(collection120UnfitChild.copy(priority = Some(1.0)))
        )
        // non collection
        result(3) shouldBe roomsWithoutShowCashback(4).copy(
          childrenRooms = List(nonCollection90fitChild.copy(priority = Some(1.0)))
        )
        result(4) shouldBe roomsWithoutShowCashback(3).copy(
          childrenRooms = List(nonCollection110fitYcsChild.copy(priority = Some(1.0)))
        )
        result(5) shouldBe roomsWithoutShowCashback(5).copy(
          childrenRooms = List(nonCollection100fitYcsChild.copy(priority = Some(1.0)))
        )
      }
    }
  }

  describe("1) Should return ordered master rooms with sorted children rooms") {
    val mockRoom1 = mockRoom(100, 120)
    val mockRoom2 = mockRoom(110, 135)
    val mockRoom3 = mockRoom(105, 130)

    val mockRoom4 = mockRoom(100, 120)
    val mockRoom5 = mockRoom(110, 130)
    val mockRoom6 = mockRoom(90, 100)

    val rooms1 = List(mockRoom1, mockRoom2, mockRoom3)
    val rooms2 = List(mockRoom4, mockRoom5, mockRoom6)
    val rooms3 = List(mockRoom1, mockRoom2, mockRoom3, mockRoom4, mockRoom5)

    val masterRoom1 = getMasterRoom(rooms1)
    val masterRoom2 = getMasterRoom(rooms2)
    val masterRoom3 = getMasterRoom(rooms3)

    it("should return master rooms with sorted children rooms by Exclusive price") {
      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.Exclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom2.copy(childrenRooms =
          List(
            mockRoom6.copy(priority = Some(1.0)),
            mockRoom4.copy(priority = Some(1.0)),
            mockRoom5.copy(priority = Some(1.0))
          )
        ),
        masterRoom1.copy(childrenRooms =
          List(
            mockRoom1.copy(priority = Some(1.0)),
            mockRoom3.copy(priority = Some(1.0)),
            mockRoom2.copy(priority = Some(1.0))
          )
        )
      )
    }

    it("should return master rooms with sorted children rooms by Exclusive price and no requested currency") {
      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.Exclusive),
        Option.empty[String]
      ) shouldBe List(
        masterRoom2.copy(childrenRooms =
          List(
            mockRoom6.copy(priority = Some(1.0)),
            mockRoom4.copy(priority = Some(1.0)),
            mockRoom5.copy(priority = Some(1.0))
          )
        ),
        masterRoom1.copy(childrenRooms =
          List(
            mockRoom1.copy(priority = Some(1.0)),
            mockRoom3.copy(priority = Some(1.0)),
            mockRoom2.copy(priority = Some(1.0))
          )
        )
      )
    }

    it("should return master rooms with sorted children rooms by Inclusive price") {
      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.AllInclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom2.copy(childrenRooms =
          List(
            mockRoom6.copy(priority = Some(1.0)),
            mockRoom4.copy(priority = Some(1.0)),
            mockRoom5.copy(priority = Some(1.0))
          )
        ),
        masterRoom1.copy(childrenRooms =
          List(
            mockRoom1.copy(priority = Some(1.0)),
            mockRoom3.copy(priority = Some(1.0)),
            mockRoom2.copy(priority = Some(1.0))
          )
        )
      )
    }

    it("should return master rooms with sorted children rooms by Inclusive price and no requested currency") {
      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.AllInclusive),
        None
      ) shouldBe List(
        masterRoom2.copy(childrenRooms =
          List(
            mockRoom6.copy(priority = Some(1.0)),
            mockRoom4.copy(priority = Some(1.0)),
            mockRoom5.copy(priority = Some(1.0))
          )
        ),
        masterRoom1.copy(childrenRooms =
          List(
            mockRoom1.copy(priority = Some(1.0)),
            mockRoom3.copy(priority = Some(1.0)),
            mockRoom2.copy(priority = Some(1.0))
          )
        )
      )
    }

    it("should return master rooms with one children room with Inclusive price") {
      val masterRoom1 = getMasterRoom(List(mockRoom1))
      val masterRoom2 = getMasterRoom(List(mockRoom2))

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.AllInclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List(mockRoom1.copy(priority = Some(1.0)))),
        masterRoom2.copy(childrenRooms = List(mockRoom2.copy(priority = Some(1.0))))
      )
    }

    it("should return master rooms with one children room with Inclusive price and no requested currency") {
      val masterRoom1 = getMasterRoom(List(mockRoom1.copy(priority = Some(1.0))))
      val masterRoom2 = getMasterRoom(List(mockRoom2.copy(priority = Some(1.0))))

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.AllInclusive),
        None
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List(mockRoom1.copy(priority = Some(1.0)))),
        masterRoom2.copy(childrenRooms = List(mockRoom2.copy(priority = Some(1.0))))
      )
    }

    it("should return master rooms with one children room with Exclusive price") {
      val masterRoom1 = getMasterRoom(List(mockRoom1))
      val masterRoom2 = getMasterRoom(List(mockRoom2))

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.Exclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List(mockRoom1.copy(priority = Some(1.0)))),
        masterRoom2.copy(childrenRooms = List(mockRoom2.copy(priority = Some(1.0))))
      )
    }

    it("should return master rooms with one children room with Exclusive price and no requested currency") {
      val masterRoom1 = getMasterRoom(List(mockRoom1))
      val masterRoom2 = getMasterRoom(List(mockRoom2))

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.Exclusive),
        None
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List(mockRoom1.copy(priority = Some(1.0)))),
        masterRoom2.copy(childrenRooms = List(mockRoom2.copy(priority = Some(1.0))))
      )
    }

    it("should return master rooms with empty children room with Exclusive price") {
      val masterRoom1 = getMasterRoom(List.empty)
      val masterRoom2 = getMasterRoom(List.empty)

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.Exclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List.empty),
        masterRoom2.copy(childrenRooms = List.empty)
      )
    }

    it("should return master rooms with empty children room with Exclusive price and no requested currency") {
      val masterRoom1 = getMasterRoom(List.empty)
      val masterRoom2 = getMasterRoom(List.empty)

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.Exclusive),
        None
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List.empty),
        masterRoom2.copy(childrenRooms = List.empty)
      )
    }

    it("should return master rooms with empty children room with Inclusive price") {
      val masterRoom1 = getMasterRoom(List.empty)
      val masterRoom2 = getMasterRoom(List.empty)

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.AllInclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List.empty),
        masterRoom2.copy(childrenRooms = List.empty)
      )
    }

    it("should return master rooms with empty children room with Inclusive price and no requested currency") {
      val masterRoom1 = getMasterRoom(List.empty)
      val masterRoom2 = getMasterRoom(List.empty)

      roomSortingHelperService.sortRooms(
        List(masterRoom1, masterRoom2),
        Some(SuggestedPrice.AllInclusive),
        None
      ) shouldBe List(
        masterRoom1.copy(childrenRooms = List.empty),
        masterRoom2.copy(childrenRooms = List.empty)
      )
    }

    it("should return sorted children rooms with priority when child room is more than default expand number ") {
      roomSortingHelperService.sortRooms(
        List(masterRoom3),
        Some(SuggestedPrice.Exclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom3.copy(
          childrenRooms = List(
            mockRoom1.copy(priority = Some(1.0)),
            mockRoom4.copy(priority = Some(1.0)),
            mockRoom3.copy(priority = Some(1.0)),
            mockRoom2.copy(priority = Some(1.0)),
            mockRoom5.copy(priority = Some(0))
          )
        )
      )
    }
  }

  describe("2) Should return one master room") {
    val mockCollectionRoom1 = mockRoom(90, 100, ExternalLoyaltyItem.TierZero, isFitCapacity = true)
    val mockCollectionRoom2 = mockRoom(110, 120, ExternalLoyaltyItem.TierOne, isFitCapacity = true)
    val mockCollectionRoom3 = mockRoom(110, 120, ExternalLoyaltyItem.TierOne, isFitCapacity = true, Option(110, 120))
    val mockCollectionRoom4 = mockRoom(
      110,
      120,
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(110, 120),
      isBreakfastIncluded = true
    )
    val mockCollectionRoom5 = mockRoom(
      110,
      120,
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(110, 120),
      isBreakfastIncluded = true,
      isFreeCancellation = true
    )
    val mockCollectionRoom6 = mockRoom(
      110,
      120,
      ExternalLoyaltyItem.TierOne,
      isFitCapacity = true,
      Option(110, 120),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true
    )
    val mockCollectionRoom7 = mockRoom(100, 110, ExternalLoyaltyItem.TierZero)

    val mockNonCollectionRoom1 = mockRoom(200, 210)
    val mockNonCollectionRoom2 = mockRoom(210, 220, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val mockNonCollectionRoom3 = mockRoom(210, 220, ExternalLoyaltyItem.Unknown, isFitCapacity = true, Option(210, 220))
    val mockNonCollectionRoom4 = mockRoom(
      210,
      220,
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(210, 220),
      isBreakfastIncluded = true
    )
    val mockNonCollectionRoom5 = mockRoom(
      210,
      220,
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(210, 220),
      isBreakfastIncluded = true,
      isFreeCancellation = true
    )
    val mockNonCollectionRoom6 =
      mockRoom(
        210,
        220,
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        Option(210, 220),
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
    val mockNonCollectionRoom7 = mockRoom(220, 230)
    val mockNonCollectionRoom8 = mockRoomWithNoPrice()

    val rooms = List(
      /** Without collection */
      mockNonCollectionRoom1,
      mockNonCollectionRoom2,
      mockNonCollectionRoom3,
      mockNonCollectionRoom4,
      mockNonCollectionRoom5,
      mockNonCollectionRoom6,
      mockNonCollectionRoom7,
      mockNonCollectionRoom8,

      /** With collection */
      mockCollectionRoom1,
      mockCollectionRoom2,
      mockCollectionRoom3,
      mockCollectionRoom4,
      mockCollectionRoom5,
      mockCollectionRoom6,
      mockCollectionRoom7
    )
    val masterRoom = getMasterRoom(rooms)

    it("should return one master room with Exclusive price") {
      roomSortingHelperService.sortRooms(
        List(masterRoom),
        Some(SuggestedPrice.Exclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom.copy(childrenRooms =
          List(
            /** Fit with collection */
            mockCollectionRoom1.copy(priority = Some(1.0)),
            mockCollectionRoom2.copy(priority = Some(1.0)),
            mockCollectionRoom3.copy(priority = Some(1.0)),
            mockCollectionRoom4.copy(priority = Some(1.0)),
            mockCollectionRoom5.copy(priority = Some(0)),
            mockCollectionRoom6.copy(priority = Some(0)),

            /** Fit without collection */
            mockNonCollectionRoom6.copy(priority = Some(0)),
            mockNonCollectionRoom5.copy(priority = Some(0)),
            mockNonCollectionRoom4.copy(priority = Some(0)),
            mockNonCollectionRoom2.copy(priority = Some(0)),
            mockNonCollectionRoom3.copy(priority = Some(0)),

            /** Unfit with collection */
            mockCollectionRoom7.copy(priority = Some(0)),

            /** Unfit without collection */
            mockNonCollectionRoom1.copy(priority = Some(0)),
            mockNonCollectionRoom7.copy(priority = Some(0)),
            mockNonCollectionRoom8.copy(priority = Some(0))
          )
        )
      )
    }

    it("should return one master room with Inclusive price") {
      roomSortingHelperService.sortRooms(
        List(masterRoom),
        Some(SuggestedPrice.AllInclusive),
        Option("THB")
      ) shouldBe List(
        masterRoom.copy(childrenRooms =
          List(
            /** Fit with collection */
            mockCollectionRoom1.copy(priority = Some(1.0)),
            mockCollectionRoom2.copy(priority = Some(1.0)),
            mockCollectionRoom3.copy(priority = Some(1.0)),
            mockCollectionRoom4.copy(priority = Some(1.0)),
            mockCollectionRoom5.copy(priority = Some(0)),
            mockCollectionRoom6.copy(priority = Some(0)),

            /** Fit without collection */
            mockNonCollectionRoom6.copy(priority = Some(0)),
            mockNonCollectionRoom5.copy(priority = Some(0)),
            mockNonCollectionRoom4.copy(priority = Some(0)),
            mockNonCollectionRoom2.copy(priority = Some(0)),
            mockNonCollectionRoom3.copy(priority = Some(0)),

            /** Unfit with collection */
            mockCollectionRoom7.copy(priority = Some(0)),

            /** Unfit without collection */
            mockNonCollectionRoom1.copy(priority = Some(0)),
            mockNonCollectionRoom7.copy(priority = Some(0)),
            mockNonCollectionRoom8.copy(priority = Some(0))
          )
        )
      )
    }
  }

  describe("3) Should return empty master room") {
    implicit val contextHolder: ContextHolder =
      createContextHolderFromRequest(aValidPropertyRequest)
    val roomSortingHelperService =
      new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

    it("should return empty master room with Exclusive price") {
      roomSortingHelperService.sortRooms(
        List.empty,
        Some(SuggestedPrice.Exclusive),
        Option("THB")
      ) shouldBe List.empty
    }

    it("should return empty master room with Inclusive price") {
      roomSortingHelperService.sortRooms(
        List.empty,
        Some(SuggestedPrice.AllInclusive),
        Option("THB")
      ) shouldBe List.empty
    }
  }

  describe("Fit Non collection room sorting") {
    val breakfastOptions    = Seq(false, true)
    val cancellationOptions = Seq(false, true)
    val supplyOptions       = Seq(false, true)
    val priceOptions        = Seq(200, 100)
    val asoOptions          = Seq(false, true)
    val payAtHotelOptions   = Seq(false, true)

    // Room   Index   Price   BreakfastIncluded   FreeCancellation   AgodaDirectSupply
    // ------------------------------------------------------------------------------
    // r1     0       200     NO                  NO                 NO
    // r2     1       200     NO                  NO                 YES
    // r3     2       200     NO                  YES                NO
    // r4     3       200     NO                  YES                YES
    // r5     4       200     YES                 NO                 NO
    // r6     5       200     YES                 NO                 YES
    // r7     6       200     YES                 YES                NO
    // r8     7       200     YES                 YES                YES
    // r9     8       100     NO                  NO                 NO
    // r10    9       100     NO                  NO                 YES
    // r11    10      100     NO                  YES                NO
    // r12    11      100     NO                  YES                YES
    // r13    12      100     YES                 NO                 NO
    // r14    13      100     YES                 NO                 YES
    // r15    14      100     YES                 YES                NO
    // r16    15      100     YES                 YES                YES
    describe("same price with Tie breaker") {
      val mockNonCollectionRooms = for {
        price        <- priceOptions
        breakfast    <- breakfastOptions
        cancellation <- cancellationOptions
        supply       <- supplyOptions
      } yield mockFitNonCollectionRoom(
        price,
        isBreakfastIncluded = breakfast,
        isFreeCancellation = cancellation,
        isAgodaDirectSupply = supply
      )
      val masterRoom = getMasterRoom(mockNonCollectionRooms.toList)

      it("should return one master room with child rooms sorted with meaningful top offers") {
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        ) shouldBe List(
          masterRoom.copy(childrenRooms =
            List(
              mockNonCollectionRooms(15).copy(priority = Some(1.0)), // cheapest
              mockNonCollectionRooms(14).copy(priority = Some(1.0)), // cheapest with breakfast
              mockNonCollectionRooms(11).copy(priority = Some(1.0)), // cheapest with free cancellation
              mockNonCollectionRooms(7).copy(priority = Some(1.0)),  // cheapest with free cancellation and breakfast
              mockNonCollectionRooms(13).copy(priority = Some(0)),
              mockNonCollectionRooms(12).copy(priority = Some(0)),
              mockNonCollectionRooms(10).copy(priority = Some(0)),
              mockNonCollectionRooms(9).copy(priority = Some(0)),
              mockNonCollectionRooms(8).copy(priority = Some(0)),
              mockNonCollectionRooms(6).copy(priority = Some(0)),
              mockNonCollectionRooms(5).copy(priority = Some(0)),
              mockNonCollectionRooms(4).copy(priority = Some(0)),
              mockNonCollectionRooms(3).copy(priority = Some(0)),
              mockNonCollectionRooms(2).copy(priority = Some(0)),
              mockNonCollectionRooms(1).copy(priority = Some(0)),
              mockNonCollectionRooms.head.copy(priority = Some(0))
            )
          )
        )
      }
    }

    // Room   Index   Price   BreakfastIncluded   FreeCancellation   AgodaDirectSupply
    // ------------------------------------------------------------------------------
    // r1     0       200     NO                  NO                 NO
    // r2     1       201     NO                  NO                 YES
    // r3     2       202     NO                  YES                NO
    // r4     3       203     NO                  YES                YES
    // r5     4       204     YES                 NO                 NO
    // r6     5       205     YES                 NO                 YES
    // r7     6       206     YES                 YES                NO
    // r8     7       207     YES                 YES                YES
    // r9     8       100     NO                  NO                 NO
    // r10    9       101     NO                  NO                 YES
    // r11    10      102     NO                  YES                NO
    // r12    11      103     NO                  YES                YES
    // r13    12      104     YES                 NO                 NO
    // r14    13      105     YES                 NO                 YES
    // r15    14      106     YES                 YES                NO
    // r16    15      107     YES                 YES                YES
    describe("different prices") {
      val mockNonCollectionRooms = for {
        price        <- priceOptions
        breakfast    <- breakfastOptions
        cancellation <- cancellationOptions
        supply       <- supplyOptions
        index = breakfastOptions.indexOf(breakfast) * cancellationOptions.size * supplyOptions.size +
          cancellationOptions.indexOf(cancellation) * supplyOptions.size +
          supplyOptions.indexOf(supply)
      } yield mockFitNonCollectionRoom(
        price + index,
        isBreakfastIncluded = breakfast,
        isFreeCancellation = cancellation,
        isAgodaDirectSupply = supply
      )
      val masterRoom = getMasterRoom(mockNonCollectionRooms.toList)

      it("should return one master room with child rooms sorted with meaningful top offers") {
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val test = roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )
        test shouldBe List(
          masterRoom.copy(childrenRooms =
            List(
              mockNonCollectionRooms(8).copy(priority = Some(1.0)),  // cheapest
              mockNonCollectionRooms(10).copy(priority = Some(1.0)), // cheapest with free cancellation
              mockNonCollectionRooms(12).copy(priority = Some(1.0)), // cheapest with breakfast
              mockNonCollectionRooms(14).copy(priority = Some(1.0)), // cheapest with free cancellation and breakfast
              mockNonCollectionRooms(9).copy(priority = Some(0)),
              mockNonCollectionRooms(11).copy(priority = Some(0)),
              mockNonCollectionRooms(13).copy(priority = Some(0)),
              mockNonCollectionRooms(15).copy(priority = Some(0)),
              mockNonCollectionRooms.head.copy(priority = Some(0)),
              mockNonCollectionRooms(1).copy(priority = Some(0)),
              mockNonCollectionRooms(2).copy(priority = Some(0)),
              mockNonCollectionRooms(3).copy(priority = Some(0)),
              mockNonCollectionRooms(4).copy(priority = Some(0)),
              mockNonCollectionRooms(5).copy(priority = Some(0)),
              mockNonCollectionRooms(6).copy(priority = Some(0)),
              mockNonCollectionRooms(7).copy(priority = Some(0))
            )
          )
        )
      }
    }

    describe("same price with ASO and Pay at Hotel") {
      val mockNonCollectionRooms = for {
        (room, index) <- (for {
          price        <- priceOptions
          breakfast    <- breakfastOptions
          cancellation <- cancellationOptions
          supply       <- supplyOptions
          payAtHotel   <- payAtHotelOptions
          aso          <- asoOptions
        } yield mockFitNonCollectionRoom(
          price,
          isBreakfastIncluded = breakfast,
          isFreeCancellation = cancellation,
          isAgodaDirectSupply = supply,
          isPayAtHotel = payAtHotel,
          isASO = aso
        )).zipWithIndex
      } yield room.copy(uid = Some(index.toString))
      val masterRoom = getMasterRoom(mockNonCollectionRooms.toList)

      it(
        "should return one master room with child rooms sorted with meaningful top offers including Pay at Hotel and ASO"
      ) {
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val sortedRooms = roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )

        val result         = sortedRooms.head.childrenRooms
        val expandedOffers = result.take(20)

        expandedOffers shouldBe List(
          mockNonCollectionRooms(60).copy(priority = Some(1.0)), // cheapest
          mockNonCollectionRooms(61).copy(priority = Some(1.0)), // cheapest with breakfast
          mockNonCollectionRooms(62).copy(priority = Some(1.0)), // cheapest with free cancellation
          mockNonCollectionRooms(63).copy(priority = Some(1.0)), // cheapest with free cancellation and breakfast
          mockNonCollectionRooms(58).copy(priority = Some(1.0)), // cheapest Pay at Hotel
          mockNonCollectionRooms(57).copy(priority = Some(1.0)), // cheapest ASO
          mockNonCollectionRooms(56).copy(priority = Some(0.0)),
          mockNonCollectionRooms(59).copy(priority = Some(0.0)),
          mockNonCollectionRooms(52).copy(priority = Some(0.0)),
          mockNonCollectionRooms(53).copy(priority = Some(0.0)),
          mockNonCollectionRooms(54).copy(priority = Some(0.0)),
          mockNonCollectionRooms(55).copy(priority = Some(0.0)),
          mockNonCollectionRooms(48).copy(priority = Some(0.0)),
          mockNonCollectionRooms(49).copy(priority = Some(0.0)),
          mockNonCollectionRooms(50).copy(priority = Some(0.0)),
          mockNonCollectionRooms(51).copy(priority = Some(0.0)),
          mockNonCollectionRooms(44).copy(priority = Some(0.0)),
          mockNonCollectionRooms(45).copy(priority = Some(0.0)),
          mockNonCollectionRooms(46).copy(priority = Some(0.0)),
          mockNonCollectionRooms(47).copy(priority = Some(0.0))
        )
      }
    }

    // ------------------------------------------------------------------------------
    // Room   Index   Price   BreakfastIncluded   FreeCancellation
    // ------------------------------------------------------------------------------
    // r1     0       200     NO                  NO
    // r2     1       200     NO                  NO
    // r3     2       100     NO                  NO
    // r4     3       100     NO                  NO
    // r5     4       50      NO                  NO
    // ------------------------------------------------------------------------------
    describe("with no meaningful offers") {
      val mockNonCollectionRooms = for {
        price <- Seq(200, 200, 100, 100, 50)
      } yield mockFitNonCollectionRoom(price)
      val masterRoom = getMasterRoom(mockNonCollectionRooms.toList)

      it("should return one master room with child rooms sorted with meaning top offers") {
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        ) shouldBe List(
          masterRoom.copy(childrenRooms =
            List(
              mockNonCollectionRooms(4).copy(priority = Some(1.0)), // cheapest
              mockNonCollectionRooms(2).copy(priority = Some(0)),
              mockNonCollectionRooms(3).copy(priority = Some(0)),
              mockNonCollectionRooms(0).copy(priority = Some(0)),
              mockNonCollectionRooms(1).copy(priority = Some(0))
            )
          )
        )
      }
    }

    // ------------------------------------------------------------------------------
    // Room   Index   Price   BreakfastIncluded   FreeCancellation
    // ------------------------------------------------------------------------------
    // r1     0       200     NO                  NO
    // r2     1       100     NO                  NO
    // ------------------------------------------------------------------------------
    describe("Fit Non collection room sorting with less than 3 rooms") {
      val mockNonCollectionRooms = for {
        price <- Seq(200, 100)
      } yield mockFitNonCollectionRoom(price)
      val masterRoom = getMasterRoom(mockNonCollectionRooms.toList)

      it("should return one master room with child rooms sorted without meaning top offers applied") {
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        ) shouldBe List(
          masterRoom.copy(childrenRooms =
            List(
              mockNonCollectionRooms(1).copy(priority = Some(1.0)),
              mockNonCollectionRooms(0).copy(priority = Some(1.0))
            )
          )
        )
      }
    }
  }

  describe("[ROOMIE-1734] Offer ranking v4 - minimum 2 expanded offers") {
    val offerRankingV4Exp = ActiveABTests().offerRankingV4

    val cheapestRoom = mockRoom(100, 110, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val breakfastRoom =
      mockRoom(120, 130, ExternalLoyaltyItem.Unknown, isFitCapacity = true, isBreakfastIncluded = true)
    val cancellationRoom =
      mockRoom(140, 150, ExternalLoyaltyItem.Unknown, isFitCapacity = true, isFreeCancellation = true)
    val room4 = mockRoom(200, 210, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val room5 = mockRoom(250, 260, ExternalLoyaltyItem.Unknown, isFitCapacity = true)

    val testRooms                    = List(cheapestRoom, breakfastRoom, cancellationRoom, room4, room5)
    val masterRoomWithMultipleOffers = getMasterRoom(testRooms)

    describe("ROOMIE-1734=A") {
      it("should use meaningful offers logic") {
        val experimentContext = new ForceBExperimentContext(Some(Seq.empty))
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest, experimentContext)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val result = roomSortingHelperService.sortRooms(
          List(masterRoomWithMultipleOffers),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )

        val sortedRooms = result.head.childrenRooms
        sortedRooms shouldBe List(
          cheapestRoom.copy(priority = Some(1.0)),
          breakfastRoom.copy(priority = Some(1.0)),
          cancellationRoom.copy(priority = Some(1.0)),
          room4.copy(priority = Some(0.0)),
          room5.copy(priority = Some(0.0))
        )
      }
    }

    describe("ROOMIE-1734=B") {
      it("should return minimum 2 expanded offers when only 1 meaningful offer exists") {
        val cheapestRoom = mockRoom(100, 110, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
        val room2        = mockRoom(150, 160, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
        val room3        = mockRoom(150, 160, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
        val room4        = mockRoom(200, 210, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
        val room5        = mockRoom(250, 260, ExternalLoyaltyItem.Unknown, isFitCapacity = true)

        val masterRoom = getMasterRoom(List(cheapestRoom, room2, room3, room4, room5))

        val experimentContext = new ForceBExperimentContext(Some(Seq(offerRankingV4Exp)))
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest, experimentContext)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val result = roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )

        val sortedRooms = result.head.childrenRooms

        sortedRooms shouldBe List(
          cheapestRoom.copy(priority = Some(1.0)),
          room2.copy(priority = Some(1.0)),
          room3.copy(priority = Some(0.0)),
          room4.copy(priority = Some(0.0)),
          room5.copy(priority = Some(0.0))
        )
      }

      it("should maintain existing behavior when already has 2+ meaningful offers") {
        val experimentContext = new ForceBExperimentContext(Some(Seq(offerRankingV4Exp)))
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest, experimentContext)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val result = roomSortingHelperService.sortRooms(
          List(masterRoomWithMultipleOffers),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )

        val sortedRooms = result.head.childrenRooms

        sortedRooms shouldBe List(
          cheapestRoom.copy(priority = Some(1.0)),
          breakfastRoom.copy(priority = Some(1.0)),
          cancellationRoom.copy(priority = Some(1.0)),
          room4.copy(priority = Some(0.0)),
          room5.copy(priority = Some(0.0))
        )
      }

      it("should handle edge case with only 1 offer") {
        val cheapestRoom = mockRoom(100, 110, ExternalLoyaltyItem.Unknown, isFitCapacity = true)

        val masterRoom = getMasterRoom(List(cheapestRoom))

        val experimentContext = new ForceBExperimentContext(Some(Seq(offerRankingV4Exp)))
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest, experimentContext)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val result = roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )

        val sortedRooms = result.head.childrenRooms

        sortedRooms shouldBe List(
          cheapestRoom.copy(priority = Some(1.0))
        )
      }

      it("should fallback to 4 expanded offers when no fit non-collection offers exist") {
        val collection100    = mockRoom(100, 110, ExternalLoyaltyItem.TierOne, isFitCapacity = true)
        val nonCollection150 = mockRoom(150, 160, ExternalLoyaltyItem.Unknown, isFitCapacity = true)
        val collection200    = mockRoom(200, 210, ExternalLoyaltyItem.TierZero, isFitCapacity = true)
        val nonCollection250 = mockRoom(250, 260, ExternalLoyaltyItem.Unknown, isFitCapacity = true)

        val masterRoom = getMasterRoom(List(collection100, nonCollection150, collection200, nonCollection250))

        val experimentContext = new ForceBExperimentContext(Some(Seq(offerRankingV4Exp)))
        implicit val contextHolder: ContextHolder =
          createContextHolderFromRequest(aValidPropertyRequest, experimentContext)
        val roomSortingHelperService =
          new RoomSortingHelperServiceImpl(supplierMetadataService, reportingService, tracer, contextHolder)

        val result = roomSortingHelperService.sortRooms(
          List(masterRoom),
          Some(SuggestedPrice.AllInclusive),
          Option("THB")
        )

        val sortedRooms = result.head.childrenRooms

        sortedRooms shouldBe List(
          collection100.copy(priority = Some(1.0)),
          collection200.copy(priority = Some(1.0)),
          nonCollection150.copy(priority = Some(1.0)),
          nonCollection250.copy(priority = Some(1.0))
        )
      }
    }
  }

  def mockRoomWithNoPrice(): EnrichedChildRoom =
    aValidEnrichedChildRoom.copy(pricing = Map.empty)

  def mockRoom(
      exclusive: Double,
      allInclusive: Double,
      loyaltyItemType: ExternalLoyaltyItemType = ExternalLoyaltyItem.Unknown,
      isFitCapacity: Boolean = false,
      displayPriceAfterCashback: Option[(Double, Double)] = None,
      isBreakfastIncluded: Boolean = false,
      isFreeCancellation: Boolean = false,
      isAgodaDirectSupply: Boolean = false,
      isPayAtHotel: Boolean = false,
      isAso: Boolean = false
  ): EnrichedChildRoom = {
    val capacity = aValidEnrichedCapacity.copy(numberOfGuestsWithoutRoom = if (isFitCapacity) 0 else 1)
    val loyaltyDisplay = loyaltyItemType match {
      case ExternalLoyaltyItem.Unknown  => Seq.empty
      case ExternalLoyaltyItem.TierZero => Seq(aValidExternalLoyaltyDisplayItemHotel)
      case ExternalLoyaltyItem.TierOne  => Seq(aValidExternalLoyaltyDisplayItemLuxury)
    }
    val payment = aValidEnrichedPayment
      .withCancellation(aValidEnrichedCancellation.withCancellationType({
        if (isFreeCancellation) CancellationTypes.FreeCancellation else CancellationTypes.None
      }))
      .withPayAtHotel(aValidEnrichedPayAtHotel.copy(isEligible = isPayAtHotel))
    val subSupplierId   = if (isAgodaDirectSupply) ProviderIds.Ycs else ProviderIds.Unknown
    val cashback        = aValidCashback.copy(showPostCashbackPrice = displayPriceAfterCashback.isDefined)
    val stayPackageType = if (isAso) Some(StayPackageType.agodaSpecialOffer) else None

    aValidEnrichedChildRoom
      .withCapacity(Some(capacity))
      .withExternalLoyaltyDisplay(loyaltyDisplay)
      .withPricing(getPrice(displayPriceAfterCashback, exclusive, allInclusive, isAgodaDirectSupply))
      .withBreakfast(Some(isBreakfastIncluded))
      .withPayment(Some(payment))
      .withSubSupplierId(Some(subSupplierId))
      .withCashback(Some(cashback))
      .withStayPackageType(stayPackageType)
  }

  def mockFitNonCollectionRoom(
      price: Int,
      isBreakfastIncluded: Boolean = false,
      isFreeCancellation: Boolean = false,
      isAgodaDirectSupply: Boolean = false,
      isPayAtHotel: Boolean = false,
      isASO: Boolean = false
  ): EnrichedChildRoom =
    mockRoom(
      price,
      price + 10,
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      None,
      isBreakfastIncluded,
      isFreeCancellation,
      isAgodaDirectSupply,
      isPayAtHotel,
      isASO
    )

  def getMasterRoom(childRooms: List[EnrichedChildRoom]): EnrichedMasterRoom =
    aValidEnrichedMasterRoom.withChildrenRooms(childRooms).build()

  def notShowCashBack(room: EnrichedChildRoom): EnrichedChildRoom =
    room.copy(cashback = room.cashback.map(cb => cb.copy(showPostCashbackPrice = false)))

  def getPrice(
      displayPriceAfterCashback: Option[(Double, Double)] = None,
      exclusive: Double,
      allInclusive: Double,
      isAgodaDirectSupply: Boolean = false
  ): Map[String, EnrichedPricing] = {

    val priceSummary = if (displayPriceAfterCashback.isDefined) {
      aValidSummaryElement.copy(
        displayAfterCashback = displayPriceAfterCashback match {
          case Some(price) =>
            Some(
              aValidDisplayPrice
                .withExclusive(price._1)
                .withAllInclusive(price._2)
                .build()
            )
          case _ => None
        }
      )
    } else aValidSummaryElement.copy(displayAfterCashback = None)
    val cnyPricing = Map(
      "CNY" -> aValidEnrichedPricing
        .withDisplay(
          aValidDisplayBasis
            .withPerRoomPerNight(aValidDisplayPrice.withExclusive(exclusive / 5).withAllInclusive(allInclusive / 5))
        )
        .withDisplaySummary(aValidDisplaySummary.withPerRoomPerNight(priceSummary))
        .build()
    )
    val thbPricing = Map(
      "THB" ->
        aValidEnrichedPricing
          .withDisplay(
            aValidDisplayBasis
              .withPerRoomPerNight(aValidDisplayPrice.withExclusive(exclusive).withAllInclusive(allInclusive))
          )
          .withDisplaySummary(aValidDisplaySummary.withPerRoomPerNight(priceSummary))
          .build()
    )
    if (isAgodaDirectSupply) {
      thbPricing
    } else {
      cnyPricing ++ thbPricing
    }
  }

}
