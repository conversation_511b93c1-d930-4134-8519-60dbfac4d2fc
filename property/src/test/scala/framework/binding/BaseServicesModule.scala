package framework.binding

import com.agoda.bundler.packaging.BundleService
import com.agoda.commons.logging.metrics.{ MetricsReporter, NoOpMetricsReporter }
import com.agoda.commons.observability.api.tracing.FeatureTracer
import com.agoda.commons.tracing.noop.NoOpTracer
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.externaldependency.repository.ProductHotelRepositoryAgsql
import com.agoda.papi.search.common.framework.mock
import com.agoda.papi.search.common.framework.mock.{
  CalculateChannelServiceTest,
  CancellationPolicyServiceTest,
  CAPIServiceTest,
  RegulatoryBlockingServiceTest,
  SupplierMetadataServiceTest,
  WhiteLabelServiceTest
}
import com.agoda.papi.search.common.service.cancellationpolicy.{ BNPLService, CancellationPolicyService }
import com.agoda.papi.search.common.service.{
  CalculateChannelService,
  CMSService,
  ExchangeDataService,
  LocaleService,
  LocaleServiceImpl,
  RegulatoryBlockingService,
  WhiteLabelService
}
import com.agoda.papi.search.common.service.fs.{ FeatureStoreService, FeatureStoreServiceTest }
import com.agoda.papi.search.property.externaldependency.repository.{
  OverlapBookingCountIOSource,
  SoldOutRoomsPriceRepositoryAgsql
}
import com.agoda.papi.search.property.service.ClusterService.{ ExternalService, RecommendedRoomsService }
import com.agoda.papi.search.property.service.ClusterService.propertyfilter.PropertyFilterSortingService
import com.agoda.papi.search.property.service.enrich._
import com.agoda.papi.search.property.service.pricestream.{
  ComplexSearchPriceStreamEnrichService,
  PriceStreamService,
  PriceStreamUrgencyService
}
import com.agoda.papi.search.property.service._
import com.agoda.papi.search.property.service.complexresult.{
  HotelResultMappingHelperService,
  HotelResultMappingHelperServiceImpl
}
import com.agoda.papi.cancellation.CxlPolicyService
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.property.service.recommendedUpgrade.{
  RecommendedUpgradeService,
  RecommendedUpgradeServiceImpl
}
import com.agoda.papi.search.property.service.roomSorting.{ RoomSortingHelperService, RoomSortingHelperServiceImpl }
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.agoda.supplier.common.services.SupplierMetadataService
import com.agoda.supplier.services.PreAllocationExperimentService
import com.typesafe.config.Config
import framework.repository.{ OverlapBookingCountIOSourceTest, ProductHotelRepositoryAgsqlTest }
import framework.testservice._
import framework.testservice.traithelper.InstallmentServiceTest
import io.opentracing.Tracer
import org.mockito.Mockito
import scaldi.Module

import scala.concurrent.Future

/*
  should contain services, all binding MUST extend real implementations
 */
private[binding] trait BaseServicesModule extends Module {

  bind[CalculateChannelService] to new CalculateChannelServiceTest()

  bind[CampaignInformationService] to injected[CampaignInformationServiceImpl]
  bind[ContentEnrichService] to injected[ContentEnrichServiceImpl]
  bind[ContentEnrichHelper].toProvider(new ContentEnrichHelperTest)
  bind[ExchangeDataService].toProvider(new ExchangeDataServiceTest)
  bind[PriceMessagingService].toProvider(new PriceMessagingServiceTest)
  bind[PriceSelectionService].toProvider(new PriceSelectionServiceTest)
  bind[ProductHotelsService].toProvider(new ProductHotelsServiceTest)
  bind[HotelPaymentOptionService] to injected[HotelPaymentOptionServiceTest]
  bind[CxlPolicyService] to new CxlPolicyService {}
  bind[CancellationPolicyService].toProvider(new CancellationPolicyServiceTest)
  bind[BNPLService].toProvider(new framework.testservice.BNPLServiceTest)
  bind[ExternalService].toProvider(new ExternalServiceTest)
  bind[StarfruitService].toProvider(new StarfruitServiceTest)
  bind[RequestBuilderService].toProvider(new RequestBuilderServiceTest)
  bind[SupplierMetadataService] to new SupplierMetadataServiceTest
  bind[HotelResultMappingHelperService] to injected[HotelResultMappingHelperServiceImpl]
  bind[BookingFormHistoryService] to injected[BookingFormHistoryServiceImpl]
  bind[BookingHistoryService] to injected[BookingHistoryServiceImpl]
  bind[ProductHotelRepositoryAgsql] to new ProductHotelRepositoryAgsqlTest
  bind[EnrichContentForChildRoomHelper] to new EnrichFilterTagsForRoomHelperTest
  bind[PreAllocationExperimentReportHelper] to new PreAllocationExperimentReportHelperTest
  bind[PriceSnapshotService] to new PriceSnapshotServiceTest
  bind[MetricsReporter] to NoOpMetricsReporter
  bind[Config] to Mockito.mock(classOf[Config])
  bind[PreAllocationExperimentService] to injected[framework.testservice.PreAllocationExperimentServiceTest]
  bind[SoldOutRoomsPriceRepositoryAgsql] to new SoldOutRoomsPriceRepositoryAgsqlTest
  bind[SoldOutRoomPricesService] to new SoldOutRoomPricesServiceTest
  bind[PropertyFilterSortingService] to new PropertyFilterSortingServiceTest
  bind[PriceStreamHttpClient[Future]] to new PriceStreamHttpClientTest
  bind[PriceStreamService] to new PriceStreamServiceTest

  bind[AreaPopularityService] to injected[AreaPopularityServiceImpl]
  bind[BookingCountService] to injected[BookingCountServiceImpl]
  bind[PricingMessageEnrichHelper] to new PricingMessageEnrichHelperTest
  bind[EnrichRoomHelper] to new EnrichRoomHelperTest
  bind[PointsMaxEnrichHelper] to new PointsMaxEnrichHelperTest
  bind[EnrichPaymentHelper] to new EnrichPaymentHelperTest
  bind[EnrichBookingInformationService] to new EnrichBookingInformationServiceTest
  bind[UserLocaleService] to new UserLocaleServiceTest
  bind[EnrichCampaignService] to new EnrichCampaignServiceTest
  bind[EnrichPricingService] to new EnrichPricingServiceTest
  bind[EnrichPackagePricingService] to new EnrichPackagePricingServiceTest
  bind[InstallmentService] to new InstallmentServiceTest
  bind[ComplexSearchPriceStreamEnrichService] to new ComplexSearchPriceStreamEnrichServiceTest
  bind[PropertyRecommendationService] to new PropertyRecommendationServiceTest
  bind[DisplayPaymentTypesService] to new DisplayPaymentTypesServiceTest
  bind[SpecialRequestsService] to new SpecialRequestsServiceTest
  bind[BookingFrequencyRateService] to injected[BookingFrequencyRateServiceImpl]
  bind[ExchangeDataService]
    .toProvider(new mock.ExchangeDataServiceTest)

  bind[CMSService].toProvider(new mock.CMSServiceTest)
  bind[LocaleService].toProvider(injected[LocaleServiceImpl])
  bind[Tracer] to NoOpTracer
  bind[FeatureTracer] to FeatureTracer.noop()
  bind[EnrichMultiRoomSuggestionService] to new EnrichMultiRoomSuggestionServiceTest
  bind[OverlapBookingCountIOSource] to new OverlapBookingCountIOSourceTest
  bind[BookingPredictionService] to injected[BookingPredictionServiceImpl]
  bind[PriceStreamUrgencyService] to new PriceStreamUrgencyServiceTest
  bind[BoutiqueHotelService] to new BoutiqueHotelServiceTest
  bind[CAPIService] to new CAPIServiceTest()
  bind[EnrichRoomBundleService] to new EnrichRoomBundleServiceImpl {}
  bind[BundleService] to new BundleServiceTest
  bind[EnrichChargeService] to new EnrichChargeServiceTest {}
  bind[OriginalRoomDetailHelper] to new OriginalRoomDetailHelperTest {}
  bind[CityObjectInfoService] to injected[CityObjectInfoServiceImpl]
  bind[WhiteLabelService] to new WhiteLabelServiceTest
  bind[RoomSortingHelperService] to injected[RoomSortingHelperServiceImpl]
  bind[RecommendedUpgradeService] to injected[RecommendedUpgradeServiceImpl]
  bind[PropertySearchTokenService] to new PropertySearchTokenServiceImpl with PAPIAppLogger {}
  bind[DateService] to injected[DateServiceImpl]
  bind[RecommendedRoomsService] to new RecommendedRoomsServiceTest {}
  bind[RegulatoryBlockingService] to new RegulatoryBlockingServiceTest {}
  bind[FeatureStoreService] to new FeatureStoreServiceTest()
}
