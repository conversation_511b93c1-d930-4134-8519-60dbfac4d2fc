package framework.testservice

import akka.actor.ActorSystem
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.client.v2.HttpClient
import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.bathInformation.BathInformationResponse
import com.agoda.content.models.db.checkInOut.CheckInOut
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.features.FeatureGroupTypes
import com.agoda.content.models.db.highlights.Highlights
import com.agoda.content.models.db.image.ImagesResponse
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.nonHotelAccommodation.NonHotelAccommodation
import com.agoda.content.models.db.personalized.PersonalizedInformation
import com.agoda.content.models.db.positiveMentions.ReviewPositiveMentions
import com.agoda.content.models.db.rateCategories.{ RateCategoriesResponse, RateCategoryResponse }
import com.agoda.content.models.db.reviews._
import com.agoda.content.models.db.synopsis.Synopsis
import com.agoda.content.models.db.transportationInformation.TransportationInformationResponse
import com.agoda.content.models.request.sf._
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.service.WhiteLabelService
import com.agoda.papi.search.common.service.cancellationpolicy.BNPLService
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.common.util.circuitbreaker.{ CircuitBreakerWrapper, NoOpCircuitBreakerWrapper }
import com.agoda.papi.search.common.util.circuitbreaker.{ CircuitBreakerWrapper, NoOpEndpointCircuitBreakerWrapper }
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExecutionContexts }
import com.agoda.testing.fakeapi.framework.DataMutator
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import models.starfruit.{ Hotel, Properties, PropertySearchRequest }
import scaldi.{ Injectable, Injector }
import com.agoda.papi.search.property.service.ClusterService._
import com.agoda.papi.search.property.service._
import util.builders.TestDataBuilders._
import com.agoda.supplier.common.services.{ SupplierMetadata, SupplierMetadataService }
import com.netflix.config.scala.{ DynamicBooleanProperty, DynamicIntProperty }
import org.mockito.Mockito
import com.agoda.papi.search.property.service.enrich.{
  EnrichCampaignService,
  EnrichChargeService,
  EnrichMultiRoomSuggestionService,
  EnrichPackagePricingService,
  EnrichRoomBundleService,
  EnrichRoomHelper
}
import com.agoda.papi.search.property.api.metrics.RetryGauge
import com.agoda.papi.search.property.externaldependency.repository.{ ExtraBedAgeRepository, TaxDescriptionRepository }
import com.agoda.papi.search.property.service.ClusterService.{
  BaseDFPropertyService,
  BathInformationServiceWrapperImpl,
  CheckInOutServiceWrapperImpl,
  ClusterServiceSettings,
  ContentSummaryServiceWrapperImpl,
  ContentVersionServiceImpl,
  DFPropertyWithCacheTokenServiceWrapperImpl,
  DFRecommendationServiceWrapperImpl,
  DFRequestFilterConfig,
  DFVersionServiceImpl,
  EngagementServiceWrapperImpl,
  ExperienceServiceWrapperImpl,
  FeatureServiceWrapperImpl,
  HighlightServiceWrapperImpl,
  HotelInformationSummaryServiceWrapperImpl,
  HotelInfoServiceWrapperImpl,
  ImageServiceWrapperImpl,
  LocalInformationServiceWrapperImpl,
  NonHotelAccommodationServiceWrapperImpl,
  PersonalizedInformationServiceWrapperImpl,
  RateCategoriesServiceWrapperImpl,
  RateCategoryServiceWrapperImpl,
  ReviewCommentServiceWrapperImpl,
  ReviewCountersServiceWrapperImpl,
  ReviewFiltersServiceWrapperImpl,
  ReviewPositiveMentionsServiceWrapperImpl,
  ReviewScoreServiceWrapperImpl,
  ReviewSnippetServiceWrapperImpl,
  ReviewTotalServiceWrapperImpl,
  RoomDetailServiceWrapperImpl,
  SynopsisServiceWrapperImpl,
  TransportationInformationServiceWrapperImpl
}
import com.agoda.papi.search.property.service.{
  BookingPredictionService,
  BoutiqueHotelService,
  ContentEnrichService,
  EnrichBookingInformationService,
  HotelPaymentOptionService,
  PAPICMSService,
  PriceSelectionService,
  ProductHotelsService,
  UserLocaleService
}
import com.agoda.papi.search.property.service.enrich.{
  EnrichChargeService,
  EnrichMultiRoomSuggestionService,
  EnrichPackagePricingService,
  EnrichRoomBundleService,
  EnrichRoomHelper
}
import com.agoda.papi.search.property.service.pricestream.PriceStreamService
import transformers.{ EnrichedHotelWithOutContent, RecommendationHotel, WithPropertyToken }
import types.WithHotelIdMap

import scala.collection.mutable
import scala.concurrent.duration._
import scala.concurrent.{ ExecutionContext, Future }
import sttp.model.Uri._

import scala.compat.java8.FunctionConverters._
import scala.jdk.CollectionConverters.asScalaBufferConverter

trait FakeChunkableWrapper extends Injectable with NopContextMetrics {
  implicit val injector: Injector

  val config: ClusterServiceSettings = new ClusterServiceSettings(
    1 second,
    DynamicIntProperty("fake-path", 3),
    "fake-router",
    "papi",
    Integer.MAX_VALUE,
    uri"http://localhost:80/api/test",
    "",
    DynamicBooleanProperty("fake-path", false),
    DynamicBooleanProperty("fake-path", false),
    false,
    false
  ) // so that there's 1 chunkin

  implicit val system: ActorSystem                 = inject[ActorSystem]
  val retryGauge: RetryGauge                       = inject[RetryGauge]
  val serviceMesh: ServiceMesh                     = Mockito.mock(classOf[ServiceMesh])
  implicit val ec: ExecutionContext                = ExecutionContexts.defaultContext
  val circuitBreakerWrapper: CircuitBreakerWrapper = NoOpCircuitBreakerWrapper
}

trait BaseDFServiceWrapperTest[ServiceOut] extends FakeChunkableWrapper with BaseDFPropertyService[ServiceOut] {
  override val requestFilterConfig: DFRequestFilterConfig                    = new DFRequestFilterConfig(8)
  implicit override val cmsService: PAPICMSService                           = inject[PAPICMSService]
  implicit override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
  implicit override val languageRepository: LanguageRepository               = inject[LanguageRepository]
  override val contentEnrichService: ContentEnrichService                    = inject[ContentEnrichService]
  override val priceSelectionService: PriceSelectionService                  = inject[PriceSelectionService]
  override val bnplService: BNPLService                                      = inject[BNPLService]
  override val extraBedAgeRepository: ExtraBedAgeRepository                  = inject[ExtraBedAgeRepository]
  override val productHotelsService: ProductHotelsService                    = inject[ProductHotelsService]
  override val supplierMetadataService: SupplierMetadataService              = inject[SupplierMetadataService]
  override val enrichBookingInformationService: EnrichBookingInformationService =
    inject[EnrichBookingInformationService]
  override val enrichRoomService: EnrichRoomHelper  = inject[EnrichRoomHelper]
  override val userLocaleService: UserLocaleService = inject[UserLocaleService]
  override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
    inject[EnrichMultiRoomSuggestionService]
  override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
  override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
  override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
  override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
  override val circuitBreakerWrapper: CircuitBreakerWrapper       = NoOpCircuitBreakerWrapper
  override val whiteLabelService: WhiteLabelService               = inject[WhiteLabelService]

}

class DFPropertyWithCacheTokenServiceWrapperTest(implicit val injector: Injector)
    extends DFPropertyWithCacheTokenServiceWrapperImpl
      with BaseDFServiceWrapperTest[WithPropertyToken[WithHotelIdMap[EnrichedHotelWithOutContent]]] {

  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Option[Properties]] = {
    val result: Seq[Hotel] =
      request.propertyIds.map(id => aValidDFHotel.withHotelId(id).build())(collection.breakOut)
    Future.successful(Some(aValidDFProperties.copy(hotels = result)))
  }

}

class RoomDetailServiceWrapperTest(implicit val injector: Injector)
    extends RoomDetailServiceWrapperImpl with FakeChunkableWrapper {
  override val languageRepository: LanguageRepository = inject[LanguageRepository]
  override def httpClient: HttpClient[Future]         = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetRoomDetailsGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[RoomInformationResponse]] = {
    val result: Seq[RoomInformationResponse] = request.propertyIds.map { id =>
      val requestingRoomTypeIds =
        request.roomRequest.filterCriteria
          .map(_.roomIdFilters.getOrElse(List.empty).flatMap(_.roomIds).toVector)
          .getOrElse(Vector.empty)
      val roomInformations = requestingRoomTypeIds.map { roomId =>
        aValidMasterRoomResponse.withMasterRoomMetadata(aValidMasterRoomMeta.withRoomId(roomId).build()).build()
      }
      aValidRoomInformationResponse.withHotelId(id).withMasterRoomResponse(roomInformations).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ExperienceServiceWrapperTest(implicit val injector: Injector)
    extends ExperienceServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetExperienceGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[Experiences]] = {
    val result: Seq[Experiences] = request.propertyIds.map { id =>
      aValidExperiences.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class DFRecommendationServiceWrapperTest(implicit val injector: Injector)
    extends DFRecommendationServiceWrapperImpl with BaseDFServiceWrapperTest[mutable.Map[Long, RecommendationHotel]] {
  override val supplierMetadataService: SupplierMetadataService = inject[SupplierMetadataService]

  override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Option[Properties]] = {
    val result: Seq[Hotel] =
      request.propertyIds.map(id => aValidDFHotel.withHotelId(id).build())(collection.breakOut)
    Future.successful(Some(aValidDFProperties.copy(hotels = result)))
  }

  override def httpClient: HttpClient[Future]                      = inject[HttpClient[Future]]
  override val enrichPackagingService: EnrichPackagePricingService = inject[EnrichPackagePricingService]
  override val enrichChargeService: EnrichChargeService            = inject[EnrichChargeService]
}

class DFVersionServiceTest(implicit val injector: Injector) extends DFVersionServiceImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: Option[Int], mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[String] =
    Future.successful("some version")

}

class ContentVersionServiceTest(implicit val injector: Injector)
    extends ContentVersionServiceImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: Option[Int], mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[String] =
    Future.successful("some version")

}

class ReviewCommentServiceWrapperTest(implicit val injector: Injector)
    extends ReviewCommentServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewCommentsGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewComments]] = {
    val result: Seq[ReviewComments] = request.propertyIds.map { id =>
      aValidReviewComments.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ReviewPositiveMentionsServiceWrapperTest(implicit val injector: Injector)
    extends ReviewPositiveMentionsServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewPositiveMentionsGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewPositiveMentions]] = {
    val result: Seq[ReviewPositiveMentions] = request.propertyIds.map { id =>
      aValidReviewPositiveMentions.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ReviewCountersServiceWrapperTest(implicit val injector: Injector)
    extends ReviewCountersServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewCounterGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewCounters]] = {
    val result: Seq[ReviewCounters] = request.propertyIds.map { id =>
      aValidReviewCounters.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class HotelInfoServiceWrapperTest(implicit val injector: Injector)
    extends HotelInfoServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetInfoGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[InformationResponse]] = {
    val result: Seq[InformationResponse] = request.propertyIds.map { id =>
      aValidHotelInformationResponse.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class LocalInformationServiceWrapperTest(implicit val injector: Injector)
    extends LocalInformationServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetLocalInformationGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[LocalInformation]] = {
    val result: Seq[LocalInformation] = request.propertyIds.map { id =>
      aValidLocalInformation.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class CheckInOutServiceWrapperTest(implicit val injector: Injector)
    extends CheckInOutServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetCheckInOutGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[CheckInOut]] = {
    val result: Seq[CheckInOut] = request.propertyIds.map { id =>
      aValidCheckInOut.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class EngagementServiceWrapperTest(implicit val injector: Injector)
    extends EngagementServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetPropertyEngagementGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[PropertyEngagement]] = {
    val result: Seq[PropertyEngagement] = request.propertyIds.map { id =>
      aValidEngagement.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class FeatureServiceWrapperTest(implicit val injector: Injector)
    extends FeatureServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetFeaturesGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[FeatureGroupTypes]] = {
    val result: Seq[FeatureGroupTypes] = request.propertyIds.map { id =>
      aValidFeatureGroupTypes.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ImageServiceWrapperTest(implicit val injector: Injector)
    extends ImageServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetImagesGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ImagesResponse]] = {
    val result: Seq[ImagesResponse] = request.propertyIds.map { id =>
      aValidImagesResponse.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ReviewScoreServiceWrapperTest(implicit val injector: Injector)
    extends ReviewScoreServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewScoreGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewScores]] = {
    val result: Seq[ReviewScores] = request.propertyIds.map { id =>
      aValidReviewScores.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ReviewSnippetServiceWrapperTest(implicit val injector: Injector)
    extends ReviewSnippetServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewSnippetGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewSnippets]] = {
    val result: Seq[ReviewSnippets] = request.propertyIds.map { id =>
      aValidReviewSnippets.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ReviewTotalServiceWrapperTest(implicit val injector: Injector)
    extends ReviewTotalServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewTotalsGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewTotals]] = {
    val result: Seq[ReviewTotals] = request.propertyIds.map { id =>
      aValidReviewTotals.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ReviewFiltersServiceWrapperTest(implicit val injector: Injector)
    extends ReviewFiltersServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetReviewFiltersGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[ReviewFiltersAvailability]] = {
    val result: Seq[ReviewFiltersAvailability] = request.propertyIds.map { id =>
      aValidReviewFiltersAvailability.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class ContentSummaryServiceWrapperTest(implicit val injector: Injector)
    extends ContentSummaryServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future]         = inject[HttpClient[Future]]
  override val languageRepository: LanguageRepository = inject[LanguageRepository]

  override def serviceCallWithRetries(request: ContentPropertiesRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[PropertyResponse]] = {
    val result: Seq[PropertyResponse] = request.propertyIds.map { id =>
      aValidContentPropertyResponse.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class HotelInformationSummaryServiceWrapperTest(implicit val injector: Injector)
    extends HotelInformationSummaryServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetSummaryGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[InformationSummaryResponse]] = {
    val result: Seq[InformationSummaryResponse] = request.propertyIds.map { id =>
      aValidInformationSummaryResponse.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class NonHotelAccommodationServiceWrapperTest(implicit val injector: Injector)
    extends NonHotelAccommodationServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetNonHotelAccommodationGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[NonHotelAccommodation]] = {
    val result: Seq[NonHotelAccommodation] = request.propertyIds.map { id =>
      aValidNonHotelAccommodationResponse.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class SynopsisServiceWrapperTest(implicit val injector: Injector)
    extends SynopsisServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetSynopsisGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[Synopsis]] = {
    val result: Seq[Synopsis] = request.propertyIds.map { id =>
      aValidSynopsis.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class HighlightServiceWrapperTest(implicit val injector: Injector)
    extends HighlightServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetHighlightsRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[Highlights]] = {
    val result: Seq[Highlights] = request.propertyIds.map { id =>
      aValidHighlights.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class PersonalizedInformationServiceWrapperTest(implicit val injector: Injector)
    extends PersonalizedInformationServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetPersonalizedInformationGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[PersonalizedInformation]] = {
    val result: Seq[PersonalizedInformation] = request.propertyIds.map { id =>
      aValidPersonalizedInformation.withHotelId(id).build()
    }(collection.breakOut)
    Future.successful(result)
  }

}

class RateCategoriesServiceWrapperTest(implicit val injector: Injector)
    extends RateCategoriesServiceWrapperImpl with FakeChunkableWrapper {

  private val dataMutator = new DataMutator[(Long, Long), RateCategoryResponse]()

  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetRateCategoriesGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[RateCategoriesResponse]] = {
    val result: Seq[RateCategoriesResponse] = request.propertyIds.flatMap { id =>
      createMutatedData(id)
    }(collection.breakOut)
    Future.successful(result)
  }

  def addMutator(hotelId: Long, rateCategoryId: Long, mutator: RateCategoryResponse => RateCategoryResponse): Unit =
    dataMutator.addMutator(hotelId -> rateCategoryId, mutator.asJava)

  private def createMutatedData(hotelId: Long): Option[RateCategoriesResponse] =
    dataMutator.keys().asScala.groupBy(_._1).get(hotelId).map { rateCategoryIds =>
      val rateCategories = rateCategoryIds.map { case (_, rateCategoryId) =>
        val rateCategory = aValidSingleRateCategoryResponse.copy(
          rateCategoryId = rateCategoryId
        )
        dataMutator.applyMutators(hotelId -> rateCategoryId, rateCategory)
      }
      aValidRateCategoriesResponse.copy(
        propertyId = hotelId,
        rateCategories = rateCategories.toVector
      )
    }

}

class RateCategoryServiceWrapperTest(implicit val injector: Injector)
    extends RateCategoryServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetRateCategoryGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[RateCategoriesResponse] =
    Future.successful(aValidRateCategoryDetailResponse)

}

class BathInformationServiceWrapperTest(implicit val injector: Injector)
    extends BathInformationServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetBathInformationGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[BathInformationResponse]] = {
    val result: Seq[BathInformationResponse] = request.propertyIds.map { id =>
      aValidBathInformationResponse
    }(collection.breakOut)
    Future.successful(result)
  }

}

class TransportationInformationServiceWrapperTest(implicit val injector: Injector)
    extends TransportationInformationServiceWrapperImpl with FakeChunkableWrapper {
  override def httpClient: HttpClient[Future] = inject[HttpClient[Future]]

  override def serviceCallWithRetries(request: GetTransportationInformationGroupRequest, mandatory: Boolean)(implicit
      contextHolder: ContextHolder
  ): Future[Seq[TransportationInformationResponse]] = {
    val result: Seq[TransportationInformationResponse] = request.propertyIds.map { id =>
      aValidTransportationInformationResponse
    }(collection.breakOut)
    Future.successful(result)
  }

}
