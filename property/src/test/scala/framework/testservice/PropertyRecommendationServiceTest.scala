package framework.testservice

import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.service.fs.FeatureStoreService
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import scaldi.{ Injectable, Injector }
import com.agoda.papi.search.property.service.ClusterService.{
  ContentSummaryServiceWrapper,
  DFRecommendationServiceWrapper
}
import com.agoda.papi.search.property.service.jarvis.JarvisService
import com.agoda.papi.search.property.service.{
  BookingHistoryService,
  CityObjectInfoService,
  PAPICMSService,
  PropertyRecommendationServiceImpl,
  RequestBuilderService,
  ZenithService
}

import scala.concurrent.ExecutionContext

class PropertyRecommendationServiceTest(implicit injector: Injector)
    extends PropertyRecommendationServiceImpl with NopContextMetrics with Injectable {
  implicit override val ec: ExecutionContext                           = ExecutionContexts.defaultContext
  implicit override val cmsService: PAPICMSService                     = inject[PAPICMSService]
  implicit override val languageRepository: LanguageRepository         = inject[LanguageRepository]
  implicit override val bookingHistoryService: BookingHistoryService   = inject[BookingHistoryService]
  override val zenithService: ZenithService                            = inject[ZenithService]
  override val summaryService: ContentSummaryServiceWrapper            = inject[ContentSummaryServiceWrapper]
  override val requestBuilderService: RequestBuilderService            = inject[RequestBuilderService]
  override val dfRecommendationService: DFRecommendationServiceWrapper = inject[DFRecommendationServiceWrapper]
  override val cityObjectInfoService: CityObjectInfoService            = inject[CityObjectInfoService]
  override val jarvisService: JarvisService                            = inject[JarvisService]
  override val featureStoreService: FeatureStoreService                = inject[FeatureStoreService]
}
