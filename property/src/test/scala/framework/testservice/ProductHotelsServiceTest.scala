package framework.testservice

import com.agoda.papi.search.common.externaldependency.repository.ProductHotelRepositoryAgsql
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.property.service.ProductHotelsServiceImpl
import models.db.HotelId
import scaldi.{ Injectable, Injector }
import com.agoda.testing.fakeapi.framework.TestIdAwareResourceAccessor

import scala.concurrent.Future
import scala.collection.concurrent
import scala.concurrent.ExecutionContext.Implicits.global

class ProductHotelsServiceTest(implicit injector: Injector) extends ProductHotelsServiceImpl with Injectable {
  override val productHotelRepositoryAgsql: ProductHotelRepositoryAgsql = inject[ProductHotelRepositoryAgsql]
}

// Inner class for fake override, used only in fake API tests
class FakeProductHotelsService(implicit injector: Injector) extends ProductHotelsServiceImpl with Injectable {
  override val productHotelRepositoryAgsql: ProductHotelRepositoryAgsql = inject[ProductHotelRepositoryAgsql]

  override def getProductHotelsByHotel(
      hotels: Seq[HotelId]
  )(implicit contextHolder: ContextHolder): Future[Map[HotelId, ProductHotels]] = {
    // First, try to get data from test setup cache
    val cacheMap = new TestIdAwareResourceAccessor[concurrent.Map[Long, ProductHotels]]()
      .getOrElseUpdate(() => new concurrent.TrieMap[Long, ProductHotels]())

    val cachedHotels = hotels.flatMap { hotelId =>
      cacheMap.get(hotelId).map(hotelId -> _)
    }.toMap

    // If all hotels are in cache, return them
    if (cachedHotels.size == hotels.size) {
      Future.successful(cachedHotels)
    } else {
      // Otherwise, fall back to the default repository for missing hotels
      val missingHotels = hotels.filterNot(cachedHotels.contains)
      super.getProductHotelsByHotel(missingHotels).map { repositoryHotels =>
        cachedHotels ++ repositoryHotels
      }
    }
  }

}
