package framework.testservice

import com.agoda.commons.observability.api.tracing.FeatureTracer
import com.agoda.supplier.common.services.SupplierMetadataService
import com.agoda.commons.tracing.noop.WithTracerNoop
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.service.WhiteLabelService
import com.agoda.papi.search.common.service.cancellationpolicy.BNPLService
import com.agoda.papi.search.common.service.fs.FeatureStoreService
import com.agoda.papi.search.common.util.context.ExecutionContexts
import com.agoda.papi.search.common.util.hotelshutdown.HotelShutdownSetting
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import com.agoda.papi.search.property.externaldependency.repository.ExtraBedAgeRepository
import scaldi.{ Injectable, Injector }
import com.agoda.papi.search.property.service.{ LimitMasterRoomSetting, SoldOutRoomsSetting, _ }
import com.agoda.papi.search.property.service.enrich.{
  EnrichContentForChild<PERSON><PERSON><PERSON>,
  EnrichMultiRoomSuggestionService,
  EnrichRoomBundleService,
  EnrichRoomHelper
}
import com.agoda.papi.search.property.service.ClusterService.{
  BathInformationServiceWrapper,
  CheckInOutServiceWrapper,
  ContentSummaryServiceWrapper,
  ContentVersionService,
  DFPropertyWithCacheTokenServiceWrapper,
  DFRecommendationServiceWrapper,
  DFVersionService,
  EngagementServiceWrapper,
  ExperienceServiceWrapper,
  ExternalService,
  FeatureServiceWrapper,
  HighlightServiceWrapper,
  HotelInformationSummaryServiceWrapper,
  HotelInfoServiceWrapper,
  ImageServiceWrapper,
  LocalInformationServiceWrapper,
  NonHotelAccommodationServiceWrapper,
  PersonalizedInformationServiceWrapper,
  RateCategoriesServiceWrapper,
  RateCategoryServiceWrapper,
  RecommendedRoomsService,
  ReviewCommentServiceWrapper,
  ReviewCountersServiceWrapper,
  ReviewFiltersServiceWrapper,
  ReviewPositiveMentionsServiceWrapper,
  ReviewScoreServiceWrapper,
  ReviewSnippetServiceWrapper,
  ReviewTotalServiceWrapper,
  RoomDetailServiceWrapper,
  SynopsisServiceWrapper,
  TransportationInformationServiceWrapper
}
import com.agoda.papi.search.property.service.ClusterService.propertyfilter.PropertyFilterSortingService
import com.agoda.papi.search.property.service.jarvis.JarvisService
import com.agoda.papi.search.property.service.{
  AreaPopularityService,
  BookingFormHistoryService,
  BookingHistoryService,
  BookingPredictionService,
  BoutiqueHotelService,
  ContentEnrichHelper,
  ContentEnrichService,
  DisplayPaymentTypesService,
  EnrichBookingInformationService,
  HotelPaymentOptionService,
  PAPICMSService,
  PriceMessagingService,
  PriceSelectionService,
  ProductHotelsService,
  RequestBuilderService,
  SoldOutRoomPricesService,
  StarfruitServiceImpl,
  UserLocaleService,
  ZenithService
}
import com.agoda.papi.search.property.service.pricestream.PriceStreamService
import com.agoda.papi.search.property.service.recommendedUpgrade.RecommendedUpgradeService
import com.agoda.papi.search.property.service.roomSorting.RoomSortingHelperService

import scala.concurrent.ExecutionContext

class StarfruitServiceTest(implicit injector: Injector)
    extends StarfruitServiceImpl with NopContextMetrics with Injectable with WithTracerNoop {
  implicit override val ec: ExecutionContext                        = ExecutionContexts.defaultContext
  implicit override val cmsService: PAPICMSService                  = inject[PAPICMSService]
  implicit override val languageRepository: LanguageRepository      = inject[LanguageRepository]
  override val externalService: ExternalService                     = inject[ExternalService]
  override val requestBuilderService: RequestBuilderService         = inject[RequestBuilderService]
  override val priceMessagingService: PriceMessagingService         = inject[PriceMessagingService]
  override val supplierMetadataService: SupplierMetadataService     = inject[SupplierMetadataService]
  override val contentEnrichService: ContentEnrichService           = inject[ContentEnrichService]
  override val priceSelectionService: PriceSelectionService         = inject[PriceSelectionService]
  override val bnplService: BNPLService                             = inject[BNPLService]
  override val extraBedAgeRepository: ExtraBedAgeRepository         = inject[ExtraBedAgeRepository]
  override val productHotelsService: ProductHotelsService           = inject[ProductHotelsService]
  override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
  override val bookingFormBookingHistory: BookingFormHistoryService = inject[BookingFormHistoryService]
  override val bookingHistoryService: BookingHistoryService         = inject[BookingHistoryService]
  override val areaPopularityService: AreaPopularityService         = inject[AreaPopularityService]
  override val enrichBookingInformationService: EnrichBookingInformationService =
    inject[EnrichBookingInformationService]
  override val enrichRoomService: EnrichRoomHelper  = inject[EnrichRoomHelper]
  override val userLocaleService: UserLocaleService = inject[UserLocaleService]
  override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
    inject[EnrichMultiRoomSuggestionService]
  override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
  override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
  override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
  override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
  override val featureTracer: FeatureTracer                       = inject[FeatureTracer]
  override val whiteLabelService: WhiteLabelService               = inject[WhiteLabelService]
}

class ExternalServiceTest(implicit injector: Injector) extends ExternalService with NopContextMetrics with Injectable {
  implicit override val cmsService: PAPICMSService             = inject[PAPICMSService]
  implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
  implicit override val ec: ExecutionContext                   = ExecutionContexts.defaultContext
  override val dfPropertyWithCacheToken: DFPropertyWithCacheTokenServiceWrapper =
    inject[DFPropertyWithCacheTokenServiceWrapper]
  override val dfVersionService: DFVersionService                = inject[DFVersionService]
  override val contentVersionService: ContentVersionService      = inject[ContentVersionService]
  override val reviewCommentService: ReviewCommentServiceWrapper = inject[ReviewCommentServiceWrapper]
  override val reviewPositiveMentionsService: ReviewPositiveMentionsServiceWrapper =
    inject[ReviewPositiveMentionsServiceWrapper]
  override val featureService: FeatureServiceWrapper                   = inject[FeatureServiceWrapper]
  override val hotelInfoService: HotelInfoServiceWrapper               = inject[HotelInfoServiceWrapper]
  override val imageService: ImageServiceWrapper                       = inject[ImageServiceWrapper]
  override val reviewScoreService: ReviewScoreServiceWrapper           = inject[ReviewScoreServiceWrapper]
  override val reviewSnippetService: ReviewSnippetServiceWrapper       = inject[ReviewSnippetServiceWrapper]
  override val reviewTotalService: ReviewTotalServiceWrapper           = inject[ReviewTotalServiceWrapper]
  override val reviewFiltersService: ReviewFiltersServiceWrapper       = inject[ReviewFiltersServiceWrapper]
  override val reviewCountersService: ReviewCountersServiceWrapper     = inject[ReviewCountersServiceWrapper]
  override val roomDetailService: RoomDetailServiceWrapper             = inject[RoomDetailServiceWrapper]
  override val dfRecommendationService: DFRecommendationServiceWrapper = inject[DFRecommendationServiceWrapper]
  override val zenithService: ZenithService                            = inject[ZenithService]
  override val summaryService: ContentSummaryServiceWrapper            = inject[ContentSummaryServiceWrapper]
  override val localInformationService: LocalInformationServiceWrapper = inject[LocalInformationServiceWrapper]
  override val checkInOutService: CheckInOutServiceWrapper             = inject[CheckInOutServiceWrapper]
  override val synopsisService: SynopsisServiceWrapper                 = inject[SynopsisServiceWrapper]
  override val experienceService: ExperienceServiceWrapper             = inject[ExperienceServiceWrapper]
  override val engagementService: EngagementServiceWrapper             = inject[EngagementServiceWrapper]
  override val hotelInformationSummaryService: HotelInformationSummaryServiceWrapper =
    inject[HotelInformationSummaryServiceWrapper]
  override val highlightService: HighlightServiceWrapper    = inject[HighlightServiceWrapper]
  override val requestBuilderService: RequestBuilderService = inject[RequestBuilderService]
  override val nonHotelAccommodationService: NonHotelAccommodationServiceWrapper =
    inject[NonHotelAccommodationServiceWrapper]
  override val supplierMetadataService: SupplierMetadataService = inject[SupplierMetadataService]
  override val personalizedInformationService: PersonalizedInformationServiceWrapper =
    inject[PersonalizedInformationServiceWrapper]
  override val enrichFilterTagsForRoomHelper: EnrichContentForChildRoomHelper = inject[EnrichContentForChildRoomHelper]
  override val soldOutRoomsSetting: SoldOutRoomsSetting                       = inject[SoldOutRoomsSetting]
  override val limitMasterRoomSetting: LimitMasterRoomSetting                 = inject[LimitMasterRoomSetting]
  override val soldOutPricesService: SoldOutRoomPricesService                 = inject[SoldOutRoomPricesService]
  override val propertyFilterSortingService: PropertyFilterSortingService     = inject[PropertyFilterSortingService]
  override val priceStreamService: PriceStreamService                         = inject[PriceStreamService]
  override val rateCategoriesService: RateCategoriesServiceWrapper            = inject[RateCategoriesServiceWrapper]
  override val rateCategoryService: RateCategoryServiceWrapper                = inject[RateCategoryServiceWrapper]
  override val bathInformationService: BathInformationServiceWrapper          = inject[BathInformationServiceWrapper]
  override val transportationInformationService: TransportationInformationServiceWrapper =
    inject[TransportationInformationServiceWrapper]
  override val enrichRoomBundleService: EnrichRoomBundleService     = inject[EnrichRoomBundleService]
  override val paymentTypesService: DisplayPaymentTypesService      = inject[DisplayPaymentTypesService]
  override val hotelShutdownSetting: HotelShutdownSetting           = inject[HotelShutdownSetting]
  override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
  override val roomSortingService: RoomSortingHelperService         = inject[RoomSortingHelperService]
  override val recommendedUpgradeService: RecommendedUpgradeService = inject[RecommendedUpgradeService]
  override val recommendedRoomService: RecommendedRoomsService      = inject[RecommendedRoomsService]
  override val jarvisService: JarvisService                         = inject[JarvisService]
  override val featureStoreService: FeatureStoreService             = inject[FeatureStoreService]
}

class ContentEnrichHelperTest(implicit injector: Injector)
    extends ContentEnrichHelper with Injectable with WithTracerNoop {
  override val contentEnrichService: ContentEnrichService           = inject[ContentEnrichService]
  override val priceSelectionService: PriceSelectionService         = inject[PriceSelectionService]
  override val bnplService: BNPLService                             = inject[BNPLService]
  override val extraBedAgeRepository: ExtraBedAgeRepository         = inject[ExtraBedAgeRepository]
  override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
  implicit override val languageRepository: LanguageRepository      = inject[LanguageRepository]
  implicit override val ec: ExecutionContext                        = inject[ExecutionContext]
  override val productHotelsService: ProductHotelsService           = inject[ProductHotelsService]
  override val enrichBookingInformationService: EnrichBookingInformationService =
    inject[EnrichBookingInformationService]
  override val enrichRoomService: EnrichRoomHelper  = inject[EnrichRoomHelper]
  override val userLocaleService: UserLocaleService = inject[UserLocaleService]
  override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
    inject[EnrichMultiRoomSuggestionService]
  override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
  override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
  override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
  override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
  override val whiteLabelService: WhiteLabelService               = inject[WhiteLabelService]
}
