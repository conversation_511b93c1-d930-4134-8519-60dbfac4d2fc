package framework

import request._
import com.agoda.content.models.{ PropertyId, PropertyResponse }
import com.agoda.content.models.db.information.RoomInformationResponse
import com.agoda.content.models.db.rateCategories.RateCategoriesResponse
import com.agoda.content.models.request.sf._
import com.agoda.papi.search.types._
import com.agoda.papi.search.common.util.context.{ ContextHolder, WithCorrelationId }
import com.agoda.ml.recommendation.zenith.recommendation.{ HotelRecoResponse, RecommendationResponse, RoomRecoResponse }
import com.agoda.pricestream.models.response.{
  CheapestPriceByCheckIn,
  CityMetaInfoResponse,
  EngagementResponse,
  PropertyLongStayPromotionResponse,
  PropertyMetaInfoResponse,
  PropertyPriceTrend
}
import com.agoda.supplier.common.services.{ OverrideCriteria, SupplierMetadata, SupplierMetadataService }
import com.agoda.supplier.models.SupplierId
import framework.testservice._
import models.starfruit.{ Hotel => DFHotel, _ }
import org.joda.time.{ DateTime, LocalDate }
import com.agoda.everest.model.HotelId
import com.agoda.commons.http.client.v2.{ HttpClient, RequestSettings }
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.content.models.db.image.ImagesResponse
import com.agoda.core.search.models.{
  CancellationFee,
  CancellationPoliciesResult => PAPICancellationPoliciesResult,
  CancellationType,
  CancellationTypes
}
import com.agoda.core.search.models.enumeration.TravellerType
import com.agoda.core.search.models.request.CancellationRequestEntry
import com.agoda.feast.api.AnyEntity
import com.agoda.jarvis.interfacing.client.model.{ HotelMatchScore, SimilarPropertiesResponse }
import com.agoda.papi.search.types.{
  AvailMap => _,
  CityIdType => _,
  CountryIdType => _,
  FilterTagType => _,
  HotelIdType => _,
  MatrixIdType => _,
  MemberIdType => _,
  PropertyId => _,
  ProviderIdType => _,
  _
}
import com.agoda.ml.recommendation.zenith.common.RecommendationType
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.externaldependency.repository.{
  BNPLHotelRepository,
  BookingHistoryByPropertyRepositoryAgsql,
  CancellationPolicyRepository,
  LanguageRepository,
  ObjectInfoRepositoryAgsql,
  SupplierBNPLEligibilityRepository
}
import com.agoda.papi.search.common.framework.CommonMockModule
import com.agoda.papi.search.common.framework.mock.{
  CancellationPolicyServiceTest,
  CAPIServiceTest,
  SupplierMetadataServiceTest
}
import com.agoda.papi.search.common.service.{ CMSService, RegulatoryBlockingService }
import com.agoda.papi.search.common.service.cancellationpolicy.CancellationPolicyService
import com.agoda.papi.search.common.util.measurement.NopContextMetrics
import com.agoda.papi.search.internalmodel.database.{
  BookingHistoryForPropertyInfo,
  BookingHistoryInfo,
  CancellationPolicyInfo,
  Language,
  ObjectExtraInfoDB,
  ProductHotels
}
import com.agoda.papi.search.property.externaldependency.repository.{
  OverlapBookingCountIOSource,
  OverlapBookingCountResult
}
import com.agoda.pricestream.models.request.{
  CityMetaInfoRequest,
  PropertyLongStayPromotionRequest,
  PropertyPriceTrendRequest
}
import org.mockito.Mockito
import scaldi.{ BoundHelper, Injector, Module }
import com.agoda.papi.search.property.service.ClusterService.propertyfilter.{
  PropertyFilterSortingService,
  PropertyFilterSortingServiceImpl
}
import com.agoda.papi.search.property.service.enrich.{ EnrichChargeService, EnrichPackagePricingService }
import com.agoda.papi.search.property.service.ClusterService.{
  ContentSummaryServiceWrapper,
  ContentSummaryServiceWrapperImpl,
  DFPropertyWithCacheTokenServiceWrapper,
  DFPropertyWithCacheTokenServiceWrapperImpl,
  DFRecommendationServiceWrapper,
  DFRecommendationServiceWrapperImpl,
  ImageServiceWrapper,
  ImageServiceWrapperImpl,
  RateCategoriesServiceWrapper,
  RateCategoriesServiceWrapperImpl,
  RateCategoryServiceWrapper,
  RateCategoryServiceWrapperImpl,
  RecommendedRoomsService,
  RoomDetailServiceWrapper,
  RoomDetailServiceWrapperImpl
}
import com.agoda.papi.search.property.service.jarvis.JarvisService
import com.agoda.papi.search.property.service.{
  MseRoomHelperService,
  PAPICMSService,
  ProductHotelsService,
  ZenithService
}
import com.agoda.papi.search.property.service.pricestream.PriceStreamService
import com.agoda.platform.service.context.GlobalContext
import com.agoda.pricestream.client.PriceStreamHttpClient
import com.agoda.winterfell.output.ExternalLoyaltyUserProfileResponse
import framework.repository.CancellationPolicyRepositoryTest
import transformers.{ EnrichedHotelWithOutContent, RecommendationHotel, WithPropertyToken }
import types.PropertyId
import util.builders.TestDataBuilders._

import scala.collection.mutable
import scala.concurrent.duration.{ Duration, FiniteDuration }
import scala.concurrent.{ ExecutionContext, Future, TimeoutException }
import com.agoda.papi.search.common.service.fs.FeatureStoreService
import com.agoda.papi.search.common.service.fs.DefaultFeatureStoreServiceUtils.{ FeatureSets, FeatureStoreEntities }
import com.agoda.feast.proto.types.{ Int64List, Value }

//scalastyle:off
//This is for helper bindings for most commonly used components, ex. dragonfruit, content, availabilities, elastic, hotelmeta.
trait MockModule extends Module with CommonMockModule {

  def mockDFResponse(dfResponse: Future[Option[Properties]]): BoundHelper[DFPropertyWithCacheTokenServiceWrapper] = {
    sealed class CustomDFPropertyWithCacheTokenServiceWrapperTest(implicit val injector: Injector)
        extends DFPropertyWithCacheTokenServiceWrapperImpl
          with BaseDFServiceWrapperTest[WithPropertyToken[types.WithHotelIdMap[EnrichedHotelWithOutContent]]] {
      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])

      override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Option[Properties]] = {
        val requestHotels = request.propertyIds.toSet
        dfResponse.map(_.map { result =>
          val dfResponseHotels = result.hotels.map(_.hotelId).toList
          val allHotelExist    = dfResponseHotels.forall(requestHotels.contains)
          val hotelsMissing    = (requestHotels.diff(dfResponseHotels.toSet)).nonEmpty
          if (hotelsMissing) {
            throw new RuntimeException(
              s"Some hotels in the request ${requestHotels.diff(dfResponseHotels.toSet)} not present in the response, Return Empty DF Room. This might not be what you want!!!"
            )
          } else if (!allHotelExist) {
            throw new RuntimeException(
              s"Some hotels in mock responses are not in request ${dfResponseHotels.toSet.diff(requestHotels)}, This might not be what you want!!!"
            )
          } else result
        })
      }

      override val priceStreamService: PriceStreamService = inject[PriceStreamService]
    }
    bind[DFPropertyWithCacheTokenServiceWrapper].toProvider(new CustomDFPropertyWithCacheTokenServiceWrapperTest)
  }

  def mockDFResponseWithCheckIn(
      dfResponse: Map[HotelIdType, Map[DateTime, DFHotel]]
  ): BoundHelper[DFPropertyWithCacheTokenServiceWrapper] = {
    sealed class CustomDFPropertyWithCacheTokenServiceWrapperTest(implicit val injector: Injector)
        extends DFPropertyWithCacheTokenServiceWrapperImpl
          with BaseDFServiceWrapperTest[WithPropertyToken[types.WithHotelIdMap[EnrichedHotelWithOutContent]]] {

      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])

      override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Option[Properties]] = {

        val hotels = request.propertyIds.flatMap { hotelId =>
          val dfHotelPerCheckIn = dfResponse.get(hotelId)
          dfHotelPerCheckIn.flatMap(_.get(request.pricing.checkIn))
        }
        Future.successful(Some(Properties(hotels, None)))
      }
      override val priceStreamService: PriceStreamService = inject[PriceStreamService]
    }
    bind[DFPropertyWithCacheTokenServiceWrapper].toProvider(new CustomDFPropertyWithCacheTokenServiceWrapperTest)
  }

  def mockDFResponse(dfResponse: Map[HotelIdType, DFHotel]): BoundHelper[DFPropertyWithCacheTokenServiceWrapper] = {
    sealed class CustomDFPropertyWithCacheTokenServiceWrapperTest(implicit val injector: Injector)
        extends DFPropertyWithCacheTokenServiceWrapperImpl
          with BaseDFServiceWrapperTest[WithPropertyToken[types.WithHotelIdMap[EnrichedHotelWithOutContent]]] {

      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])

      override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Option[Properties]] = {

        val hotels = request.propertyIds.flatMap(hotelId => dfResponse.get(hotelId))
        Future.successful(Some(Properties(hotels, None)))
      }

      override val priceStreamService: PriceStreamService = inject[PriceStreamService]
    }
    bind[DFPropertyWithCacheTokenServiceWrapper].toProvider(new CustomDFPropertyWithCacheTokenServiceWrapperTest)
  }

  def mockDFResponseRecorder(): BoundHelper[DFPropertyWithCacheTokenServiceWrapper] =
    bind[DFPropertyWithCacheTokenServiceWrapper].to {
      lazy val wrapper = new DFPropertyWithCacheTokenServiceWrapperTest
      Mockito.spy(wrapper)
    }

  def mockDFRecommendation(dfResponse: Map[HotelIdType, DFHotel]): BoundHelper[DFRecommendationServiceWrapper] = {
    sealed class CustomDFRecommendationServiceWrapperTest(implicit val injector: Injector)
        extends DFRecommendationServiceWrapperImpl with BaseDFServiceWrapperTest[mutable.Map[Long, RecommendationHotel]]
          with MseRoomHelperService {
      override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Option[Properties]] = {
        val hotels = request.propertyIds.flatMap(hotelId => dfResponse.get(hotelId))
        Future.successful(Some(Properties(hotels, None)))
      }
      override def httpClient: HttpClient[Future]                      = Mockito.mock(classOf[HttpClient[Future]])
      override val priceStreamService: PriceStreamService              = inject[PriceStreamService]
      override val enrichPackagingService: EnrichPackagePricingService = inject[EnrichPackagePricingService]
      override val enrichChargeService: EnrichChargeService            = inject[EnrichChargeService]
    }
    bind[DFRecommendationServiceWrapper].toProvider(new CustomDFRecommendationServiceWrapperTest)
  }

  def mockDFRecommendation(zenithHotelList: List[RecommendationHotel]): BoundHelper[DFRecommendationServiceWrapper] = {
    sealed class CustomDFRecommendationServiceWrapperTest(implicit val injector: Injector)
        extends DFRecommendationServiceWrapperImpl with BaseDFServiceWrapperTest[mutable.Map[Long, RecommendationHotel]]
          with MseRoomHelperService {
      override def serviceCallWithRetries(request: PropertySearchRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Option[Properties]] = {
        val dfHotelSeq = zenithHotelList.map(zh =>
          aValidDFHotel.copy(
            hotelId = zh.h.hotelId
          )
        )
        val properties = aValidDFProperties.copy(hotels = dfHotelSeq)
        Future.successful(Some(properties))
      }
      override val priceStreamService: PriceStreamService              = inject[PriceStreamService]
      override def httpClient: HttpClient[Future]                      = Mockito.mock(classOf[HttpClient[Future]])
      override val enrichPackagingService: EnrichPackagePricingService = inject[EnrichPackagePricingService]
      override val enrichChargeService: EnrichChargeService            = inject[EnrichChargeService]
    }
    bind[DFRecommendationServiceWrapper].toProvider(new CustomDFRecommendationServiceWrapperTest)
  }

  def mockCMSService(mockCmsService: Map[(Long, Long), String]): BoundHelper[PAPICMSService] = {
    sealed class CustomPAPICMSService(implicit val injector: Injector) extends PAPICMSService {
      override def toCmsTextFuture(cmsId: Long, languageId: Long)(implicit
          ec: ExecutionContext,
          contextHolder: ContextHolder
      ): Future[Option[String]] =
        Future.successful(mockCmsService.get(cmsId, languageId))

      override def toCmsText(cmsId: Long, languageId: Long)(implicit contextHolder: ContextHolder): Option[String] =
        mockCmsService.get(cmsId, languageId)

    }
    bind[PAPICMSService].toProvider(new CustomPAPICMSService)
  }

  def mockCoreCMSService(mockCmsService: Map[(Long, Long), String]): BoundHelper[CMSService] = {
    sealed class CustomCMSService(implicit val injector: Injector) extends CMSService {
      override def toCmsTextFuture(cmsId: Long, languageId: Long)(implicit
          ec: ExecutionContext,
          contextHolder: ContextHolder
      ): Future[Option[String]] =
        Future.successful(mockCmsService.get(cmsId, languageId))

      override def toCmsText(cmsId: Long, languageId: Long)(implicit
          contextHolder: ContextHolder
      ): Option[String] = mockCmsService.get(cmsId, languageId)

    }
    bind[CMSService].toProvider(new CustomCMSService)
  }

  def mockContentResponse(contentResponse: Future[Seq[PropertyResponse]]): BoundHelper[ContentSummaryServiceWrapper] = {
    sealed class CustomContentSummaryServiceWrapper(implicit val injector: Injector)
        extends ContentSummaryServiceWrapperImpl with NopContextMetrics with FakeChunkableWrapper {
      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])
      override def serviceCallWithRetries(request: ContentPropertiesRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Seq[PropertyResponse]] = contentResponse

      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
    }
    bind[ContentSummaryServiceWrapper].toProvider(new CustomContentSummaryServiceWrapper)
  }

  def mockOverlapBookingCountIOSource(
      mockOverlapBookingCount: Map[PropertyId, Vector[OverlapBookingCountResult]]
  ): BoundHelper[OverlapBookingCountIOSource] = {
    sealed class CustomOverlapBookingCountIOSource(implicit val injector: Injector)
        extends OverlapBookingCountIOSource {
      override def getOverlapBookingCount(propertyIds: Seq[PropertyId], checkin: DateTime, checkout: DateTime)(implicit
          withCorrelationId: WithCorrelationId
      ): Future[Map[PropertyId, Vector[OverlapBookingCountResult]]] = Future.successful(mockOverlapBookingCount)
    }
    bind[OverlapBookingCountIOSource].toProvider(new CustomOverlapBookingCountIOSource)

  }

  def mockLanguageRepository(
      mockLanguageRepository: mutable.Map[String, Language],
      mockLanguageRepositoryById: mutable.Map[Int, Language]
  ): BoundHelper[LanguageRepository] = {
    sealed class CustomLanguageRepository(implicit val injector: Injector) extends LanguageRepository {
      override def allLanguages(): mutable.Map[String, Language] = mockLanguageRepository

      override def allLanguagesById(): mutable.Map[Int, Language] = mockLanguageRepositoryById
    }
    bind[LanguageRepository].toProvider(new CustomLanguageRepository)
  }

  def mockProductHotelsService(
      mockProductHotelsService: Future[Map[HotelId, ProductHotels]]
  ): BoundHelper[ProductHotelsService] = {
    sealed class CustomProductHotelsService(implicit val injector: Injector) extends ProductHotelsService {
      override def getProductHotelsByHotel(hotels: Seq[HotelId])(implicit
          contextHolder: ContextHolder
      ): Future[Map[HotelId, ProductHotels]] = mockProductHotelsService
    }
    bind[ProductHotelsService].toProvider(new CustomProductHotelsService)
  }

  def mockCancellationPolicyRepository(): BoundHelper[CancellationPolicyRepository] =
    bind[CancellationPolicyRepository].toProvider(new CancellationPolicyRepositoryTest)

  def mockRoomInformationService(
      roomInformationResponse: Future[Seq[RoomInformationResponse]]
  ): BoundHelper[RoomDetailServiceWrapper] = {
    sealed class CustomRoomDetailServiceWrapper(implicit val injector: Injector)
        extends RoomDetailServiceWrapperImpl with FakeChunkableWrapper with NopContextMetrics {
      override val languageRepository = inject[LanguageRepository]

      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])
      override def serviceCallWithRetries(request: GetRoomDetailsGroupRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Seq[RoomInformationResponse]] =
        roomInformationResponse
    }
    bind[RoomDetailServiceWrapper].toProvider(new CustomRoomDetailServiceWrapper)
  }

  def mockImageService(
      imageResponse: Future[Seq[ImagesResponse]]
  ): BoundHelper[ImageServiceWrapper] = {
    sealed class CustomImageServiceWrapper(implicit val injector: Injector)
        extends ImageServiceWrapperImpl with FakeChunkableWrapper with NopContextMetrics {
      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])

      override def serviceCallWithRetries(request: GetImagesGroupRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Seq[ImagesResponse]] = imageResponse
    }
    bind[ImageServiceWrapper].toProvider(new CustomImageServiceWrapper)
  }

  def mockPropertyFilterSortingService(
      mockFilterScoreMap: Map[TravellerType, Map[String, Int]]
  ): BoundHelper[PropertyFilterSortingServiceImpl] = {
    sealed class PropertyFilterSortingServiceWrapperTest(implicit val injector: Injector)
        extends PropertyFilterSortingServiceImpl {
      override def getFilterForTravellerType(travellerType: TravellerType)(implicit ctx: ContextHolder) =
        mockFilterScoreMap.getOrElse(travellerType, Map.empty)
    }
    bind[PropertyFilterSortingService] to new PropertyFilterSortingServiceWrapperTest()
  }

  def mockBNPLHotelRepository(meta: Map[HotelIdType, Boolean]): BoundHelper[BNPLHotelRepository] = {
    sealed class BNPLHotelRepositoryWrapperTest(implicit val injector: Injector)
        extends BNPLHotelRepository with FakeChunkableWrapper {
      override def getBNPLHotel(
          propertyId: HotelIdType
      )(implicit contextHolder: ContextHolder): Future[Vector[Boolean]] = {
        val result = meta.getOrElse(propertyId, false)
        Future(Vector(result))
      }
    }
    bind[BNPLHotelRepository] to new BNPLHotelRepositoryWrapperTest
  }

  def mockSupplierBNPLEligibilityRepository(
      meta: Map[ProviderIdType, Boolean]
  ): BoundHelper[SupplierBNPLEligibilityRepository] = {
    sealed class SupplierBNPLEligibilityRepositoryWrapperTest(implicit val injector: Injector)
        extends SupplierBNPLEligibilityRepository with FakeChunkableWrapper {
      override def getBNPLEligibility(implicit contextHolder: ContextHolder): Future[Vector[(Long, Boolean)]] = {
        val result = meta.map(m => (m._1.toLong, m._2)).toVector
        Future.successful(result)
      }
    }
    bind[SupplierBNPLEligibilityRepository] to new SupplierBNPLEligibilityRepositoryWrapperTest
  }

  def mockZenithService(
      propertyRequest: PropertyRequest,
      sortedFilter: Seq[FilterTagType] = Seq.empty
  ): BoundHelper[ZenithService] = {
    sealed class ZenithServiceTest extends ZenithService {
      override def getRecommendations(
          propertyId: PropertyId,
          request: PropertyRequest,
          recommendationRequest: Recommendation
      )(implicit
          ec: ExecutionContext,
          languageRepository: LanguageRepository,
          contextHolder: ContextHolder
      ): Future[Option[HotelRecoResponse]] =
        Future.successful(Some(HotelRecoResponse.defaultInstance.copy(hotelId = propertyRequest.context.propertyIds)))

      override def getCityRecommendations(
          cityId: CityIdType,
          request: PropertyRequest,
          recommendationRequest: Recommendation,
          recommendationType: RecommendationType
      )(implicit
          ec: ExecutionContext,
          languageRepository: LanguageRepository,
          contextHolder: ContextHolder
      ): Future[Option[RecommendationResponse]] =
        Future.successful({
          Some(
            RecommendationResponse.defaultInstance.copy(
              scores = propertyRequest.context.propertyIds
                .map(id => com.agoda.ml.recommendation.zenith.recommendation.HotelResponse(id, 0))
            )
          )
        })

      override def getRecommendedRooms(
          propertyId: PropertyId,
          request: PropertyRequest
      )(implicit
          ec: ExecutionContext,
          languageRepository: LanguageRepository,
          contextHolder: ContextHolder
      ): Future[RoomRecoResponse] =
        if (propertyId == aValidHotelId) {
          Future.successful(RoomRecoResponse(Seq(110L, 112L, 113L, 111L, 12345L)))
        } else if (propertyId == aValidHotelIdForWhichZenithRoomRecoDoesNotIntersect) {
          Future.successful(RoomRecoResponse(Seq(2L, 4L, 5L)))
        } else if (propertyId == aValidHotelIdForWhichZenithRoomRecoCallFailed) {
          Future.failed(new TimeoutException("Timeout calling zenith"))
        } else {
          Future.successful(RoomRecoResponse(Seq.empty))
        }
    }

    bind[ZenithService].toProvider(new ZenithServiceTest)
  }

  def mockJarvisService(
      propertyRequest: PropertyRequest
  ): BoundHelper[JarvisService] = {
    sealed class JarvisServiceTest extends JarvisService {
      override def getSimilarPropertiesResponse(
          propertyId: PropertyId,
          request: PropertyRequest,
          recommendationRequest: Recommendation,
          recommendationType: RecommendationType
      )(implicit
          ec: ExecutionContext,
          languageRepository: LanguageRepository,
          contextHolder: ContextHolder
      ): Future[Option[SimilarPropertiesResponse]] =
        if (propertyRequest.context.propertyIds.isEmpty) {
          Future.successful(None)
        } else {
          Future.successful(
            Some(
              SimilarPropertiesResponse(
                hotelId = Option(propertyRequest.context.propertyIds).filter(_.nonEmpty),
                matchScores = Some(
                  Seq(HotelMatchScore(hotelId = propertyRequest.context.propertyIds.head, score = 10))
                )
              )
            )
          )
        }
    }

    bind[JarvisService].toProvider(new JarvisServiceTest)
  }

  def mockFeatureStoreService(
      recommendations: Map[HotelIdType, List[HotelIdType]]
  ): BoundHelper[FeatureStoreService] = {
    sealed class FeatureStoreServiceTest extends FeatureStoreService {
      override def getSingleEntityFeatures(
          featureSet: Origin,
          entity: AnyEntity,
          features: Seq[Origin]
      ): Future[Map[Origin, Value]] = Future.successful(Map.empty)

      def getCompositeEntitiesFeatures(
          featureSet: String,
          entities: Seq[AnyEntity],
          features: Seq[String],
          maxAge: Option[Duration] = None
      ): Future[Seq[Map[String, Value]]] = {
        val result = entities.map {
          case FeatureStoreEntities.HotelIdEntity(hotelId) =>
            val recommendedIds = Int64List(recommendations.getOrElse(hotelId, List.empty))
            Map("potential_hotel_ids" -> Value(Value.Val.Int64ListVal(recommendedIds)))
          case _ => Map.empty[String, Value]
        }
        Future.successful(result)
      }
    }

    bind[FeatureStoreService].toProvider(new FeatureStoreServiceTest)
  }

  def mockRecommendedRoomsService(
      roomIds: Seq[Long] = Seq.empty
  ): BoundHelper[RecommendedRoomsService] = {
    sealed class RecommendedRoomsServiceTest extends RecommendedRoomsService {

      override def getRecommendedRooms(request: PropertyRequest, globalContext: GlobalContext)(implicit
          ec: ExecutionContext
      ): Future[List[(Long, RoomRecoResponse)]] =
        Future.traverse(request.context.propertyIds)(propertyId => mockRooms(propertyId).map(propertyId -> _))

      private def mockRooms(propertyId: Long): Future[RoomRecoResponse] =
        if (propertyId != aValidHotelIdForWhichZenithRoomRecoCallFailed) {
          Future.successful(RoomRecoResponse(roomIds))
        } else {
          Future.failed(new TimeoutException("Timeout calling zenith"))
        }
    }
    bind[RecommendedRoomsService].toProvider(new RecommendedRoomsServiceTest)
  }

  def mockBookinghistoryByPropertyRepository(
      bookingHistory: Vector[BookingHistoryForPropertyInfo]
  ): BoundHelper[BookingHistoryByPropertyRepositoryAgsql] = {
    sealed class BookingHistoryByPropertyRepositoryMock extends BookingHistoryByPropertyRepositoryAgsql {
      override def fetchBookingHistory(propertyIds: Seq[HotelIdType])(implicit
          withCorrelationId: WithCorrelationId
      ): Future[Map[HotelIdType, Option[BookingHistoryInfo]]] =
        Future.successful(bookingHistory.map(b => b.propertyId -> Some(b.bookingHistoryInfo)).toMap)
    }
    bind[BookingHistoryByPropertyRepositoryAgsql] to new BookingHistoryByPropertyRepositoryMock()
  }

  def mockSupplierMetadataService(
      supplierMetaDataCache: Map[SupplierId, SupplierMetadata]
  ): BoundHelper[SupplierMetadataService] = {
    sealed class supplierMetadataServiceMock(implicit val injector: Injector) extends SupplierMetadataServiceTest {
      override def getAllSupplierMetaDataCache(
          overrideCriteria: Option[OverrideCriteria] = None
      ): Map[SupplierId, SupplierMetadata] = supplierMetaDataCache
    }
    bind[SupplierMetadataService] to new supplierMetadataServiceMock()
  }

  def mockCancellationPolicyService(
      cancellationPoliciesResult: PAPICancellationPoliciesResult
  ): BoundHelper[CancellationPolicyService] = {
    sealed class CancellationPolicyServiceMock(implicit val injector: Injector) extends CancellationPolicyServiceTest {
      override def getCancellationPolicies(
          cancellationRequests: List[CancellationRequestEntry],
          locale: String,
          useCancellationPhase: Boolean = false,
          cancellationFeeList: List[CancellationFee] = List.empty,
          cancellationType: CancellationType = CancellationTypes.None,
          useCancellationInfoFromDF: Boolean = false
      )(implicit contextHolder: ContextHolder): Future[PAPICancellationPoliciesResult] =
        Future.successful(cancellationPoliciesResult)
    }
    bind[CancellationPolicyService].toProvider(new CancellationPolicyServiceMock())
  }

  def mockMetaEngagement(engagement: EngagementResponse): BoundHelper[PriceStreamService] = {
    sealed class PriceStreamServiceMock(implicit val injector: Injector) extends PriceStreamServiceTest {
      override def getEngagement(
          propertyIds: List[PropertyId],
          checkIn: Option[DateTime],
          los: Option[Int]
      ): Future[EngagementResponse] =
        Future.successful(engagement)
    }
    bind[PriceStreamService].toProvider(new PriceStreamServiceMock())
  }

  def mockPropertyMetaInfo(propertyMetaInfo: PropertyMetaInfoResponse): BoundHelper[PriceStreamService] = {
    sealed class PriceStreamServiceMock(implicit val injector: Injector) extends PriceStreamServiceTest {
      override def getPropertyMetaInfo(sfpp: PropertyRequest)(implicit
          contextHolder: ContextHolder
      ): Future[PropertyMetaInfoResponse] =
        Future.successful(propertyMetaInfo)
    }
    bind[PriceStreamService].toProvider(new PriceStreamServiceMock())
  }

  def mockCityMetaInfo(cityMetaInfo: CityMetaInfoResponse): BoundHelper[PriceStreamService] = {
    sealed class PriceStreamServiceMock(implicit val injector: Injector) extends PriceStreamServiceTest {
      override def getCityMetaInfo(cityMetaInfoRequest: CityMetaInfoRequest): Future[CityMetaInfoResponse] =
        Future.successful(cityMetaInfo)
    }
    bind[PriceStreamService].toProvider(new PriceStreamServiceMock())
  }

  def mockRateCategoryService(
      rateCategoryResponse: Future[Seq[RateCategoriesResponse]]
  ): BoundHelper[RateCategoriesServiceWrapper] = {
    sealed class CustomRateCategoriesServiceWrapper(implicit val injector: Injector)
        extends RateCategoriesServiceWrapperImpl with FakeChunkableWrapper with NopContextMetrics {
      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])
      override def serviceCallWithRetries(request: GetRateCategoriesGroupRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[Seq[RateCategoriesResponse]] =
        rateCategoryResponse
    }
    bind[RateCategoriesServiceWrapper].toProvider(new CustomRateCategoriesServiceWrapper())
  }

  def mockRateCategoryDetailResponse(
      rateCategoryDetailResponse: Future[RateCategoriesResponse]
  ): BoundHelper[RateCategoryServiceWrapper] = {
    sealed class CustomRateCategoryServiceWrapper(implicit val injector: Injector)
        extends RateCategoryServiceWrapperImpl with FakeChunkableWrapper with NopContextMetrics {
      override def httpClient: HttpClient[Future] = Mockito.mock(classOf[HttpClient[Future]])
      override def serviceCallWithRetries(request: GetRateCategoryGroupRequest, mandatory: Boolean)(implicit
          contextHolder: ContextHolder
      ): Future[RateCategoriesResponse] =
        rateCategoryDetailResponse
    }
    bind[RateCategoryServiceWrapper].toProvider(new CustomRateCategoryServiceWrapper())
  }

  def mockRegulatoryBlockingService(
      propertyCompliantResponse: Future[HotelIdType => Boolean]
  ): BoundHelper[RegulatoryBlockingService] = {
    sealed class RegulatoryBlockingServiceTesting extends RegulatoryBlockingService {

      override def isPropertyCompliant(
          hotelIds: List[HotelIdType],
          userOrigin: Option[Origin],
          requestId: Option[Origin]
      )(implicit ec: ExecutionContext): Future[HotelIdType => Boolean] =
        propertyCompliantResponse
    }

    bind[RegulatoryBlockingService].toProvider(new RegulatoryBlockingServiceTesting)
  }

  def mockMetricsReporter(metricReporter: MetricsReporter): BoundHelper[MetricsReporter] =
    bind[MetricsReporter] to metricReporter

  def mockPriceStreamPropertyTrend(
      propertyToPriceTrendMap: Map[PropertyId, PropertyPriceTrend]
  ): BoundHelper[PriceStreamHttpClient[Future]] = {
    val client = new PriceStreamHttpClientTest() {
      override def getPropertyTrend(
          propertyPriceRequest: PropertyPriceTrendRequest,
          settings: RequestSettings
      ): Future[PropertyPriceTrend] =
        Future.successful(
          propertyToPriceTrendMap
            .get(propertyPriceRequest.propertyId)
            .getOrElse(
              PropertyPriceTrend(
                propertyPriceRequest.propertyId,
                propertyPriceRequest.los,
                Seq.empty
              )
            )
        )
    }

    bind[PriceStreamHttpClient[Future]].toProvider(client)
  }

  def mockPriceStreamPropertyLongStayPromotion(
      propertyToResponseMap: Map[PropertyId, PropertyLongStayPromotionResponse]
  ): BoundHelper[PriceStreamHttpClient[Future]] = {
    val client = new PriceStreamHttpClientTest() {
      override def getPropertyLongStayPromotions(
          propertyLongStayPromotionRequest: PropertyLongStayPromotionRequest,
          settings: RequestSettings
      ): Future[PropertyLongStayPromotionResponse] =
        Future.successful(
          propertyToResponseMap
            .get(propertyLongStayPromotionRequest.propertyId)
            .getOrElse(
              PropertyLongStayPromotionResponse(Vector.empty)
            )
        )
    }

    bind[PriceStreamHttpClient[Future]].toProvider(client)
  }

  def mockCapiService(resultResponse: Future[Option[ExternalLoyaltyUserProfileResponse]]): BoundHelper[CAPIService] = {
    val mockCAPIService = new CAPIServiceTest {
      override def getExternalLoyaltyUserProfile(
          whitelabelToken: Option[String],
          capiToken: Option[String],
          partnerClaim: Option[String],
          searchId: String,
          currency: Option[String],
          getLiveData: Option[Boolean],
          ccImageSize: Option[Int]
      )(implicit ctx: ContextHolder): Future[Option[ExternalLoyaltyUserProfileResponse]] =
        resultResponse
    }
    bind[CAPIService] to mockCAPIService
  }

}
