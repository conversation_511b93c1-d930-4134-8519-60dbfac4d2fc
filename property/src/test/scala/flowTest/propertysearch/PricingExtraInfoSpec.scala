package flowTest.propertysearch

import com.agoda.papi.search.property.service._
import framework.{ MockF<PERSON><PERSON>or<PERSON><PERSON><PERSON><PERSON>, MockModule }
import models.starfruit._
import org.scalatest.wordspec.AnyWordSpec
import util.builders.TestDataBuilders._
import scala.concurrent.Future
import framework.FlowTestExecutor

class PricingExtraInfoSpec extends AnyWordSpec with MockFrameworkHelper {

  "PricingExtraInfoSpec Should" should {
    val context = createContextHolderFromRequest(aValidPropertyRequest)
    val extraInfo =
      ExtraInformation(10.0, 20.0, 50.00, 15.00, 15.00, 15.00, 30, 45, None, None, None, None, None, Option(20.00))
    val dfPricing             = aValidDFPricing.copy(extraInfo = Some(extraInfo))
    val roomWithPricing: Room = aValidDFRoom.withPricing(Map("USD" -> dfPricing))
    val result: Seq[Hotel] = Seq(
      aValidDFHotel.withRooms(Seq(roomWithPricing)).build()
    )
    val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = result)))
    val customInjector: FrameworkInjector = composeInjector(new MockModule {
      mockDFResponse(dfResult)
    })

    injectionTest(customInjector) { implicit injector =>
      "Return correct PricingExtraInfo" in {
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest.withHotelIds(List(aValidHotelId)).withPricing(aValidPricingRequest),
          context
        )
        whenReady(result) { r =>
          val extraInfo = r.room(0).getUSDPrice.extraInfo.get
          r.room(0).requestedCurrencyCode.get should be("USD")
          extraInfo.roomTaxAndFeePRPN should be(10.0)
          extraInfo.roomTaxAndFeePN should be(20.0)
          extraInfo.roomTaxAndFeeWithExcludedPB should be(50.0)
          extraInfo.marginPerNightUSD should be(15.0)
          extraInfo.roomWithExtraBeds should be(30.0)
          extraInfo.totalPriceWithoutTaxAndFee should be(45.0)
          extraInfo.totalPriceMandatoryWithoutTaxAndFee.getOrElse(0) should be(20.0)
        }
      }
    }
  }

}
