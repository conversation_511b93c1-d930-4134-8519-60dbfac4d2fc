package flowTest.propertysearch

import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.features.{ Feature, FeatureGroup }
import com.agoda.content.models.db.information.{
  FilterTags,
  MasterRoomResponse,
  RoomFeatureResponse,
  RoomInformationResponse
}
import constant._
import enumerations.RoomAmenities
import framework.{ MockModule, TestBinding }
import models.starfruit._
import request.{ FeatureFlagKey, FeatureFlagRequest, SearchTypes }
import transformers.{ FilterTag, Properties => _, _ }
import util.BaseUnitSpec
import util.builders.TestDataBuilders._

import scala.concurrent.Future
import _root_.framework.FlowTestExecutor
import com.agoda.papi.enums.hotel.StayPackageTypes.Escapes
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextGenerator }

class PropertyFiltersSpec extends BaseUnitSpec with TestBinding with ContextGenerator {

  val specialInsiderDealCID = 1442530

  "Property Filters" should {

    val normalRoomId   = "7ec7e104-f93b-7845-db06-9cb73acbf799"
    val insiderRoomId  = "7ec7e104-f93b-7845-db06-9cb73acbf798"
    val noCCRoomId     = "7ec7e104-f93b-7845-db06-9cb73acbf797"
    val giftCardRoomId = "7ec7e104-f93b-7845-db06-9cb73acbf796"
    val discountRoomId = "7ec7e104-f93b-7845-db06-9cb73acbf795"

    val normalRoom: Room = aValidDFRoom
      .withRoomTypeId(1)
      .withSupplierID(3038)
      .withUID(normalRoomId)
      .withDMCUD("a4b4ac0c-1792-bdab-d424-227f202a0f0e")
    val insiderDealRoom: Room = aValidDFRoom.withRoomTypeId(2).withChannelID(Channel(2)).withUID(insiderRoomId)
    val noCCRoom: Room = aValidDFRoom
      .withRoomTypeId(3)
      .withUID(noCCRoomId)
      .copy(
        payment = aValidDFPayment.copy(noCreditCardRequired = Some(NoCreditCardRequired(isEligible = true))),
        stayPackageType = Some(Escapes)
      )
      .withHourlyAvailableSlots(
        Some(
          Seq(
            TimeInterval(1, "18:00"),
            TimeInterval(1, "20:00")
          )
        )
      )
    val giftCardRoom: Room = aValidDFRoom.withRoomTypeId(4).withUID(giftCardRoomId).withGiftCard(aValidGiftCard)
    val discountRoom: Room =
      aValidDFRoom.withUID(discountRoomId).withPricing(Map("USD" -> aValidDFPricing.withTotalDiscount(30).build()))

    val result: Seq[Hotel] = Seq(
      aValidDFHotel
        .withRooms(Seq(insiderDealRoom, normalRoom, noCCRoom, giftCardRoom, discountRoom))
        .withCheapestRoomID(normalRoomId)
        .withSuggestRoom(List(normalRoom))
        .build()
    )
    val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = result)))

    val normalContentRoom                          = aValidChildRoomMeta.withRoomId(1)
    val insiderDealContentRoom                     = aValidChildRoomMeta.withRoomId(2)
    val giftCardContentRoom                        = aValidChildRoomMeta.withRoomId(3)
    val noCCContentRoom                            = aValidChildRoomMeta.withRoomId(4)
    val nonSmokingRoomFeature: RoomFeatureResponse = aValidFeatureResponse.withName(Symbols.nonSmokingFilter)

    val kitchenetteRoomFeature: RoomFeatureResponse    = aValidFeatureResponse.withName(Symbols.kitchenAvailableIcon)
    val balconyTerraceRoomFeature: RoomFeatureResponse = aValidFeatureResponse.withName(Symbols.balconyTerraceIcon)
    val babyCotFeature              = aValidFeatureResponse.withName(RoomFeaturesConstants.InfantCotAvailable)
    val feature: Feature            = aValidFeature.withId(109)
    val facilityGroup: FeatureGroup = aValidFeatureGroup.withFeatures(Vector(feature)).withId(26)
    val facilityGroup2              = aValidFeatureGroup.withId(103)

    val configuration = aValidBedDetailResponse.withBedRooms(
      Vector(aValidBedroomConfigurationResponse, aValidBedroomConfigurationResponse)
    )
    val bedConfiguration = aValidBedConfigurationResponse.withConfiguration(Some(configuration))

    val familyRelatedfeature        = aValidFeature.withId(103).build()
    val familyRelatedfeatures       = Vector(familyRelatedfeature)
    val familyRelatedFacilityGroups = aValidFeatureGroup.withFeatures(familyRelatedfeatures)

    val masterRoomResponse = aValidMasterRoomResponse
      .withChildrenRoomMetaData(Vector(normalContentRoom, insiderDealContentRoom, giftCardContentRoom, noCCContentRoom))
      .withFeatures(Vector(nonSmokingRoomFeature, kitchenetteRoomFeature, balconyTerraceRoomFeature, babyCotFeature))
      .withFacilityGroups(Vector(facilityGroup, familyRelatedFacilityGroups))
      .withFilterTags(Some(aValidFilterTags))
      .withSuitabilityType(Some(FamilyConstant.SuitabilityTypeFamily))
      .withBedConfiguration(Some(bedConfiguration))
      .withMasterRoomMetadata(normalContentRoom.copy(numberOfBedRooms = Some(2)))
    val roomInfos: Vector[MasterRoomResponse] = Vector[MasterRoomResponse](masterRoomResponse)
    val rooms: RoomInformationResponse        = aValidRoomInformationResponse.withMasterRoomResponse(roomInfos)
    val roomInformationResponse               = Future.successful(Seq[RoomInformationResponse](rooms))

    val masterRoomResponse2 = aValidMasterRoomResponse
      .withChildrenRoomMetaData(Vector(normalContentRoom, insiderDealContentRoom, giftCardContentRoom, noCCContentRoom))
      .withFeatures(Vector(kitchenetteRoomFeature, balconyTerraceRoomFeature))
      .withFacilityGroups(Vector(facilityGroup2))
    val roomInfos2: Vector[MasterRoomResponse] = Vector[MasterRoomResponse](masterRoomResponse2)
    val rooms2: RoomInformationResponse        = aValidRoomInformationResponse.withMasterRoomResponse(roomInfos2)
    val roomInformationResponse2               = Future.successful(Seq[RoomInformationResponse](rooms2))

    val propertyResponse                       = aValidContentPropertyResponse.withRooms(Some(rooms))
    val contentResponse: Seq[PropertyResponse] = Seq(propertyResponse)

    val propertyResponse2                       = aValidContentPropertyResponse.withRooms(Some(rooms2))
    val contentResponse2: Seq[PropertyResponse] = Seq(propertyResponse2)

    val queenBedMasterRoomResponse = aValidMasterRoomResponse
      .withBedConfiguration(Some(aValidQueenBedConfigurationResponse))
      .withChildrenRoomMetaData(Vector(normalContentRoom, insiderDealContentRoom, giftCardContentRoom, noCCContentRoom))
      .withFeatures(Vector(balconyTerraceRoomFeature))
      .withFacilities(Vector(aValidFeature.copy(id = Some(RoomAmenities.balconyTerraceAmenityId))))
      .withFilterTags(Some(FilterTags(Some(false), Some("Pool"))))
      .build()

    val roomInformationResponse3 = Future.successful(
      Seq[RoomInformationResponse](
        aValidRoomInformationResponse.withMasterRoomResponse(Vector(queenBedMasterRoomResponse))
      )
    )

    def getChildrenRoomAndVerifyAvailableFilters(
        result: Future[transformers.Properties],
        expectedPropertyFilters: PropertyFilters,
        isMse: Boolean = false
    ): List[EnrichedChildRoom] = {
      val propertyResult = await(result)
      propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
      propertyResult.property.head.isMseProperty shouldBe Some(isMse)
      propertyResult.property.head.masterRooms.head.childrenRooms
    }
    "return no-cc filter" in {
      val contextHolder = createContextHolderFromRequest(
        aValidPropertyRequest
          .withFeatureFlag(
            aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
          )
          .build()
      )

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolder
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        val childRooms            = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.noCCPropertyPage,
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "return no non-smoking filter)" in {
      val contextHolder = createContextHolderFromRequest(
        aValidPropertyRequest
          .withFeatureFlag(
            aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
          )
          .build()
      )

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolder
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        val childRooms            = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.noCCPropertyPage
          ) ++ extraFamilyFilterTags
      }
    }

    "Agoda Special Offers filter" should {
      "should not return Agoda Special Offers when enableEscapesPackage feature flag is true" in {
        val contextHolder = createContextHolderFromRequest(
          aValidPropertyRequest
            .withFeatureFlag(
              FeatureFlagRequest(enableEscapesPackage = Some(true))
            )
            .build()
        )

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse))
          mockRoomInformationService(roomInformationResponse)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(enableEscapesPackage = Some(true))
              )
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.NonSmoking,
                "CMSId:65152",
                Symbols.nonSmokingFilter,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.TwinBed,
                "CMSId:84511",
                Symbols.twinBedIcon,
                None,
                Some(FilterGroupID.BeddingConfiguration)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.NonSmoking,
              FilterID.KitchenAvailable,
              FilterID.TwinBed,
              FilterID.PayAtHotel,
              FilterID.noCCPropertyPage
            )
        }
      }
      "should not return Agoda Special Offers when both enableEscapesPackage feature flag is false, regardless of disableEscapesPackage feature flag" in {
        val contextHolder = createContextHolderFromRequest(
          aValidPropertyRequest
            .withFeatureFlag(
              FeatureFlagRequest(enableEscapesPackage = Some(false), disableEscapesPackage = Some(false))
            )
            .build()
        )

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse))
          mockRoomInformationService(roomInformationResponse)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(enableEscapesPackage = Some(false), disableEscapesPackage = Some(false))
              )
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.NonSmoking,
                "CMSId:65152",
                Symbols.nonSmokingFilter,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.TwinBed,
                "CMSId:84511",
                Symbols.twinBedIcon,
                None,
                Some(FilterGroupID.BeddingConfiguration)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.NonSmoking,
              FilterID.KitchenAvailable,
              FilterID.TwinBed,
              FilterID.PayAtHotel,
              FilterID.noCCPropertyPage
            )
        }
      }
      "should not return Agoda Special Offers when no feature flag" in {
        val contextHolder = createContextHolderFromRequest(
          aValidPropertyRequest
            .build()
        )

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse))
          mockRoomInformationService(roomInformationResponse)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(enableEscapesPackage = Some(true), disableEscapesPackage = Some(true))
              )
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.NonSmoking,
                "CMSId:65152",
                Symbols.nonSmokingFilter,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.TwinBed,
                "CMSId:84511",
                Symbols.twinBedIcon,
                None,
                Some(FilterGroupID.BeddingConfiguration)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.NonSmoking,
              FilterID.KitchenAvailable,
              FilterID.TwinBed,
              FilterID.PayAtHotel,
              FilterID.noCCPropertyPage
            )
        }
      }

      "ROOMIE-1311 is B" should {
        val forceBExperimentContext = new ForceBExperimentContext(
          Option(
            List(ActiveABTests.experiments.asoFilterMigration)
          )
        )

        "should return Agoda Special Offers when stayPackageType is Escapes and disableEscapesPackage feature flag is false" in {
          val contextHolder = createContextHolderFromRequest(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(disableEscapesPackage = Some(false))
              )
              .withWhiteLabelKey(Some("AGODA"))
              .build(),
            forceBExperimentContext
          )

          val customInjector: FrameworkInjector = composeInjector(new MockModule {
            mockDFResponse(dfResult)
            mockContentResponse(Future.successful(contentResponse))
            mockRoomInformationService(roomInformationResponse)
          })

          injectionTest(customInjector) { implicit injector =>
            val result = FlowTestExecutor.executePropertySearch(
              aValidPropertyRequest
                .withFeatureFlag(
                  FeatureFlagRequest(disableEscapesPackage = Some(false))
                )
                .withSearchType(SearchTypes.Property)
                .withHotelIds(List(aValidHotelId))
                .withPricing(aValidPricingRequest)
                .withWhiteLabelKey(Some("AGODA")),
              contextHolder
            )
            val expectedPropertyFilters = PropertyFilters(
              "CMSId:64940",
              Seq(
                FilterTag(
                  FilterID.noCCPropertyPage,
                  "CMSId:80031",
                  Symbols.noCCPropertyPageFilter,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.PayAtHotel,
                  "CMSId:56649",
                  Symbols.PayAtHotel,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.NonSmoking,
                  "CMSId:65152",
                  Symbols.nonSmokingFilter,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.KitchenAvailable,
                  "CMSId:83947",
                  Symbols.kitchenAvailableIcon,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.TwinBed,
                  "CMSId:84511",
                  Symbols.twinBedIcon,
                  None,
                  Some(FilterGroupID.BeddingConfiguration)
                ),
                FilterTag(
                  FilterID.AgodaSpecialOffers,
                  "CMSId:121372",
                  Symbols.ASOIcon,
                  None,
                  Some(FilterGroupID.SpecialOffers)
                )
              ),
              Some(
                Seq(
                  FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                  FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                  FilterGroup(FilterGroupID.SpecialOffers, "CMSId:171401"),
                  FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
                )
              )
            )

            val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

            childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
              Seq(
                FilterID.NonSmoking,
                FilterID.KitchenAvailable,
                FilterID.TwinBed,
                FilterID.PayAtHotel,
                FilterID.noCCPropertyPage,
                FilterID.AgodaSpecialOffers
              )
          }
        }

        "should return Agoda Special Offers when no disableEscapesPackage" in {
          val contextHolder = createContextHolderFromRequest(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(disableEscapesPackage = None)
              )
              .withWhiteLabelKey(Some("AGODA"))
              .build(),
            forceBExperimentContext
          )

          val customInjector: FrameworkInjector = composeInjector(new MockModule {
            mockDFResponse(dfResult)
            mockContentResponse(Future.successful(contentResponse))
            mockRoomInformationService(roomInformationResponse)
          })

          injectionTest(customInjector) { implicit injector =>
            val result = FlowTestExecutor.executePropertySearch(
              aValidPropertyRequest
                .withFeatureFlag(
                  FeatureFlagRequest(disableEscapesPackage = None)
                )
                .withSearchType(SearchTypes.Property)
                .withHotelIds(List(aValidHotelId))
                .withPricing(aValidPricingRequest)
                .withWhiteLabelKey(Some("AGODA")),
              contextHolder
            )
            val expectedPropertyFilters = PropertyFilters(
              "CMSId:64940",
              Seq(
                FilterTag(
                  FilterID.noCCPropertyPage,
                  "CMSId:80031",
                  Symbols.noCCPropertyPageFilter,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.PayAtHotel,
                  "CMSId:56649",
                  Symbols.PayAtHotel,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.NonSmoking,
                  "CMSId:65152",
                  Symbols.nonSmokingFilter,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.KitchenAvailable,
                  "CMSId:83947",
                  Symbols.kitchenAvailableIcon,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.TwinBed,
                  "CMSId:84511",
                  Symbols.twinBedIcon,
                  None,
                  Some(FilterGroupID.BeddingConfiguration)
                ),
                FilterTag(
                  FilterID.AgodaSpecialOffers,
                  "CMSId:121372",
                  Symbols.ASOIcon,
                  None,
                  Some(FilterGroupID.SpecialOffers)
                )
              ),
              Some(
                Seq(
                  FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                  FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                  FilterGroup(FilterGroupID.SpecialOffers, "CMSId:171401"),
                  FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
                )
              )
            )

            val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

            childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
              Seq(
                FilterID.NonSmoking,
                FilterID.KitchenAvailable,
                FilterID.TwinBed,
                FilterID.PayAtHotel,
                FilterID.noCCPropertyPage,
                FilterID.AgodaSpecialOffers
              )
          }
        }

        "should return Agoda Special Offers when no feature flag" in {
          val contextHolder = createContextHolderFromRequest(
            aValidPropertyRequest
              .withWhiteLabelKey(Some("AGODA"))
              .build(),
            forceBExperimentContext
          )

          val customInjector: FrameworkInjector = composeInjector(new MockModule {
            mockDFResponse(dfResult)
            mockContentResponse(Future.successful(contentResponse))
            mockRoomInformationService(roomInformationResponse)
          })

          injectionTest(customInjector) { implicit injector =>
            val result = FlowTestExecutor.executePropertySearch(
              aValidPropertyRequest
                .withSearchType(SearchTypes.Property)
                .withHotelIds(List(aValidHotelId))
                .withPricing(aValidPricingRequest)
                .withWhiteLabelKey(Some("AGODA")),
              contextHolder
            )
            val expectedPropertyFilters = PropertyFilters(
              "CMSId:64940",
              Seq(
                FilterTag(
                  FilterID.noCCPropertyPage,
                  "CMSId:80031",
                  Symbols.noCCPropertyPageFilter,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.PayAtHotel,
                  "CMSId:56649",
                  Symbols.PayAtHotel,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.NonSmoking,
                  "CMSId:65152",
                  Symbols.nonSmokingFilter,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.KitchenAvailable,
                  "CMSId:83947",
                  Symbols.kitchenAvailableIcon,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.TwinBed,
                  "CMSId:84511",
                  Symbols.twinBedIcon,
                  None,
                  Some(FilterGroupID.BeddingConfiguration)
                ),
                FilterTag(
                  FilterID.AgodaSpecialOffers,
                  "CMSId:121372",
                  Symbols.ASOIcon,
                  None,
                  Some(FilterGroupID.SpecialOffers)
                )
              ),
              Some(
                Seq(
                  FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                  FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                  FilterGroup(FilterGroupID.SpecialOffers, "CMSId:171401"),
                  FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
                )
              )
            )

            val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

            childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
              Seq(
                FilterID.NonSmoking,
                FilterID.KitchenAvailable,
                FilterID.TwinBed,
                FilterID.PayAtHotel,
                FilterID.noCCPropertyPage,
                FilterID.AgodaSpecialOffers
              )
          }
        }

        "should not return Agoda Special Offers when whitelabel key is not AGODA" in {
          val contextHolder = createContextHolderFromRequest(
            aValidPropertyRequest
              .withWhiteLabelKey(Some("CITI"))
              .build(),
            forceBExperimentContext
          )

          val customInjector: FrameworkInjector = composeInjector(new MockModule {
            mockDFResponse(dfResult)
            mockContentResponse(Future.successful(contentResponse))
            mockRoomInformationService(roomInformationResponse)
          })

          injectionTest(customInjector) { implicit injector =>
            val result = FlowTestExecutor.executePropertySearch(
              aValidPropertyRequest
                .withSearchType(SearchTypes.Property)
                .withHotelIds(List(aValidHotelId))
                .withPricing(aValidPricingRequest)
                .withWhiteLabelKey(Some("CITI")),
              contextHolder
            )
            val expectedPropertyFilters = PropertyFilters(
              "CMSId:64940",
              Seq(
                FilterTag(
                  FilterID.noCCPropertyPage,
                  "CMSId:80031",
                  Symbols.noCCPropertyPageFilter,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.PayAtHotel,
                  "CMSId:56649",
                  Symbols.PayAtHotel,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.NonSmoking,
                  "CMSId:65152",
                  Symbols.nonSmokingFilter,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.KitchenAvailable,
                  "CMSId:83947",
                  Symbols.kitchenAvailableIcon,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.TwinBed,
                  "CMSId:84511",
                  Symbols.twinBedIcon,
                  None,
                  Some(FilterGroupID.BeddingConfiguration)
                )
              ),
              Some(
                Seq(
                  FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                  FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                  FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
                )
              )
            )

            val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

            childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
              Seq(
                FilterID.NonSmoking,
                FilterID.KitchenAvailable,
                FilterID.TwinBed,
                FilterID.PayAtHotel,
                FilterID.noCCPropertyPage
              )
          }
        }

        "should not return Agoda Special Offers when disableEscapesPackage feature flag is true" in {
          val contextHolder = createContextHolderFromRequest(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(disableEscapesPackage = Some(true))
              )
              .build(),
            forceBExperimentContext
          )

          val customInjector: FrameworkInjector = composeInjector(new MockModule {
            mockDFResponse(dfResult)
            mockContentResponse(Future.successful(contentResponse))
            mockRoomInformationService(roomInformationResponse)
          })

          injectionTest(customInjector) { implicit injector =>
            val result = FlowTestExecutor.executePropertySearch(
              aValidPropertyRequest
                .withFeatureFlag(
                  FeatureFlagRequest(disableEscapesPackage = Some(true))
                )
                .withSearchType(SearchTypes.Property)
                .withHotelIds(List(aValidHotelId))
                .withPricing(aValidPricingRequest),
              contextHolder
            )
            val expectedPropertyFilters = PropertyFilters(
              "CMSId:64940",
              Seq(
                FilterTag(
                  FilterID.noCCPropertyPage,
                  "CMSId:80031",
                  Symbols.noCCPropertyPageFilter,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.PayAtHotel,
                  "CMSId:56649",
                  Symbols.PayAtHotel,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.NonSmoking,
                  "CMSId:65152",
                  Symbols.nonSmokingFilter,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.KitchenAvailable,
                  "CMSId:83947",
                  Symbols.kitchenAvailableIcon,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.TwinBed,
                  "CMSId:84511",
                  Symbols.twinBedIcon,
                  None,
                  Some(FilterGroupID.BeddingConfiguration)
                )
              ),
              Some(
                Seq(
                  FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                  FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                  FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
                )
              )
            )

            val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

            childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
              Seq(
                FilterID.NonSmoking,
                FilterID.KitchenAvailable,
                FilterID.TwinBed,
                FilterID.PayAtHotel,
                FilterID.noCCPropertyPage
              )
          }
        }

        "should not return Agoda Special Offers when disableEscapesPackage feature flag is true, regardless of enableEscapesPackage feature flag" in {
          val contextHolder = createContextHolderFromRequest(
            aValidPropertyRequest
              .withFeatureFlag(
                FeatureFlagRequest(enableEscapesPackage = Some(true), disableEscapesPackage = Some(true))
              )
              .build(),
            forceBExperimentContext
          )

          val customInjector: FrameworkInjector = composeInjector(new MockModule {
            mockDFResponse(dfResult)
            mockContentResponse(Future.successful(contentResponse))
            mockRoomInformationService(roomInformationResponse)
          })

          injectionTest(customInjector) { implicit injector =>
            val result = FlowTestExecutor.executePropertySearch(
              aValidPropertyRequest
                .withFeatureFlag(
                  FeatureFlagRequest(enableEscapesPackage = None)
                )
                .withSearchType(SearchTypes.Property)
                .withHotelIds(List(aValidHotelId))
                .withPricing(aValidPricingRequest),
              contextHolder
            )
            val expectedPropertyFilters = PropertyFilters(
              "CMSId:64940",
              Seq(
                FilterTag(
                  FilterID.noCCPropertyPage,
                  "CMSId:80031",
                  Symbols.noCCPropertyPageFilter,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.PayAtHotel,
                  "CMSId:56649",
                  Symbols.PayAtHotel,
                  None,
                  Some(FilterGroupID.PaymentOptions)
                ),
                FilterTag(
                  FilterID.NonSmoking,
                  "CMSId:65152",
                  Symbols.nonSmokingFilter,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.KitchenAvailable,
                  "CMSId:83947",
                  Symbols.kitchenAvailableIcon,
                  None,
                  Some(FilterGroupID.RoomFeatures)
                ),
                FilterTag(
                  FilterID.TwinBed,
                  "CMSId:84511",
                  Symbols.twinBedIcon,
                  None,
                  Some(FilterGroupID.BeddingConfiguration)
                )
              ),
              Some(
                Seq(
                  FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                  FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                  FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
                )
              )
            )

            val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

            childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
              Seq(
                FilterID.NonSmoking,
                FilterID.KitchenAvailable,
                FilterID.TwinBed,
                FilterID.PayAtHotel,
                FilterID.noCCPropertyPage
              )
          }
        }
      }
    }

    "return kitchen available filter" in {
      val contextHolder = createContextHolderFromRequest(
        aValidPropertyRequest
          .withFeatureFlag(
            aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
          )
          .build()
      )

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolder
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        val childRooms            = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable,
            FilterID.noCCPropertyPage,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "return discount filter" in {
      val contextHolder = createContextHolderFromRequest(aValidPropertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest.withHotelIds(List(aValidHotelId)).withPricing(aValidPricingRequest),
          contextHolder
        )
        val expectedPropertyFilters =
          PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              )
            ),
            Some(Seq(FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877")))
          )
        val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)
        childRooms.find(_.uid.contains(discountRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel
        )
      }
    }

    "return all filter tags for MSE" in {
      val propertyRequest = (aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withContext(aValidContext.withIsMSE(Option(true)))
        .withSearchType(SearchTypes.Property))
        .build()
      val contextHolderB = createContextHolderFromRequest(
        aValidPropertyRequest
          .withFeatureFlag(
            aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
          )
          .build()
      )

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          propertyRequest.withHotelIds(List(aValidHotelId)).withPricing(aValidPricingRequest),
          contextHolderB
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        val childRooms            = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters, true)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.noCCPropertyPage,
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(giftCardRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags
      }
    }

    "return no free wifi filter (SIFT-895 - A)" in {
      val contextHolderA = createContextHolderFromRequest(
        aValidPropertyRequest
          .withFeatureFlag(
            aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
          )
          .build()
      )

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolderA
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        val childRooms            = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "return recommended for family filter for rooms when suitability type family " +
      "and 2+ filter for rooms when room have more than 1 bedroom" in {
        val contextHolder = createContextHolderFromRequest(
          aValidPropertyRequest
            .withFeatureFlag(
              aValidFeatureFlagRequest
                .copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
            )
            .build()
        )

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse))
          mockRoomInformationService(roomInformationResponse)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.NonSmoking,
                "CMSId:65152",
                Symbols.nonSmokingFilter,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.RecommendedForFamilies,
                "CMSId:82163",
                Symbols.recommendedForFamiliesIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.MoreThanOneBedRooms,
                "CMSId:83540",
                Symbols.bedroomIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.TwinBed,
                "CMSId:84511",
                Symbols.twinBedIcon,
                None,
                Some(FilterGroupID.BeddingConfiguration)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val propertyResult = await(result)
          propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
          val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

          childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.PayAtHotel,
              FilterID.RecommendedForFamilies,
              FilterID.MoreThanOneBedRooms,
              FilterID.KitchenAvailable,
              FilterID.NonSmoking,
              FilterID.TwinBed
            )

          childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.PayAtHotel,
              FilterID.RecommendedForFamilies,
              FilterID.MoreThanOneBedRooms,
              FilterID.KitchenAvailable,
              FilterID.NonSmoking,
              FilterID.TwinBed
            )

          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.PayAtHotel,
              FilterID.RecommendedForFamilies,
              FilterID.MoreThanOneBedRooms,
              FilterID.noCCPropertyPage,
              FilterID.KitchenAvailable,
              FilterID.NonSmoking,
              FilterID.TwinBed
            )
        }
      }

    "not return recommended for family filter for rooms when suitability type family " +
      "and not return 2+ filter for rooms when room have more than 1 bedroom" +
      "and experiment is A" in {
        val contextHolder = createContextHolderFromRequest(
          aValidPropertyRequest
            .withFeatureFlag(
              aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
            )
            .build()
        )

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse))
          mockRoomInformationService(roomInformationResponse)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.NonSmoking,
                "CMSId:65152",
                Symbols.nonSmokingFilter,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.RecommendedForFamilies,
                "CMSId:82163",
                Symbols.recommendedForFamiliesIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.MoreThanOneBedRooms,
                "CMSId:83540",
                Symbols.bedroomIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              ),
              FilterTag(
                FilterID.TwinBed,
                "CMSId:84511",
                Symbols.twinBedIcon,
                None,
                Some(FilterGroupID.BeddingConfiguration)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val propertyResult = await(result)
          propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
          val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

          val extraFamilyFilterTags =
            Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

          childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
            Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

          childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
            Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
            Seq(
              FilterID.PayAtHotel,
              FilterID.noCCPropertyPage,
              FilterID.KitchenAvailable,
              FilterID.NonSmoking
            ) ++ extraFamilyFilterTags
        }
      }

    "Not return baby cot filter when number of children is not 1" in {
      val pricing = aValidPricingRequest.withChildrens(5)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()
      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "Not return baby cot filter when number of childAge length is not 1" in {
      val pricing = aValidPricingRequest.withChildrens(1)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "Not return baby cot filter when number of childAge length is 1 but the age is more than 3" in {
      val pricing = aValidPricingRequest.withChildrens(1).withChildAges(Some(List(Some(12))))
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "Not return baby cot filter when experiment is A" in {
      val pricing = aValidPricingRequest.withChildrens(1).withChildAges(Some(List(Some(2))))
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "return interconnecting rooms filter when all condition are met with room more than 1" in {
      val pricing = aValidPricingRequest.withRoom(2)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest, new ForceBExperimentContext())

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.InterConnectingRooms,
              "CMSId:83541",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.InterConnectingRooms,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.InterConnectingRooms,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.InterConnectingRooms,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "Not return interconnecting rooms filter when room is less than or equal to 1" in {
      val pricing = aValidPricingRequest.withRoom(1)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "Not return interconnecting rooms filter when adult is less than 3" in {
      val pricing = aValidPricingRequest.withAdult(2)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        val childRooms            = propertyResult.property.head.masterRooms.head.childrenRooms

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "return interconnecting rooms filter when all condition are met with adult more than 3" in {
      val pricing = aValidPricingRequest.withAdult(7)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
        )
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.InterConnectingRooms,
              "CMSId:83541",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms
        val extraFamilyFilterTags = Seq(
          FilterID.RecommendedForFamilies,
          FilterID.MoreThanOneBedRooms,
          FilterID.InterConnectingRooms,
          FilterID.TwinBed
        )

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(FilterID.PayAtHotel, FilterID.KitchenAvailable, FilterID.NonSmoking) ++ extraFamilyFilterTags

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable,
            FilterID.NonSmoking
          ) ++ extraFamilyFilterTags
      }
    }

    "Should return interconnecting rooms filter when room is interconnecting room and feature flag list has `InterconnectingRoomAllRoomSearches`" in {
      val pricing = aValidPricingRequest.withRoom(1)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .withFeatureFlag(
          aValidFeatureFlagRequest.copy(flags =
            Some(
              Map(
                FeatureFlagKey.InterconnectingRoomAllRoomSearches -> true,
                FeatureFlagKey.FamilyFiltersOnPropertyPage        -> true
              )
            )
          )
        )
        .build()
      val contextHolder = createContextHolderFromRequest(propertyRequest, new ForceBExperimentContext())

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })
      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.MoreThanOneBedRooms,
              "CMSId:83540",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.InterConnectingRooms,
              "CMSId:83541",
              Symbols.bedroomIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )
        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms            = propertyResult.property.head.masterRooms.head.childrenRooms
        val extraFamilyFilterTags = Seq(FilterID.RecommendedForFamilies, FilterID.MoreThanOneBedRooms, FilterID.TwinBed)
        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.InterConnectingRooms,
          FilterID.KitchenAvailable,
          FilterID.NonSmoking
        ) ++ extraFamilyFilterTags
        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.InterConnectingRooms,
          FilterID.KitchenAvailable,
          FilterID.NonSmoking
        ) ++ extraFamilyFilterTags
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.InterConnectingRooms,
          FilterID.noCCPropertyPage,
          FilterID.KitchenAvailable,
          FilterID.NonSmoking
        ) ++ extraFamilyFilterTags
      }
    }

    "return recommended for family filter for rooms without suitability type family when is suggestion and GreatForFamiliesMRS flag is sent" in {
      val featureFlagRequest = aValidFeatureFlagRequest
        .withFlags(
          Some(Map(FeatureFlagKey.GreatForFamiliesMRS -> true, FeatureFlagKey.FamilyFiltersOnPropertyPage -> true))
        )
        .build()
      val request = aValidPropertyRequest
        .withSearchType(SearchTypes.Property)
        .withHotelIds(List(aValidHotelId))
        .withPricing(aValidPricingRequest)
        .withFeatureFlag(featureFlagRequest)
        .build()
      val contextHolder = createContextHolderFromRequest(request)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse2))
        mockRoomInformationService(roomInformationResponse2)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(request, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RecommendedForFamilies,
              "CMSId:82163",
              Symbols.recommendedForFamiliesIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val suggestRooms = propertyResult.property.head.suggestedRooms.head
        suggestRooms.suitabilityType.get should be(FamilyConstant.SuitabilityTypeFamily)

        val suggestedChildRooms = suggestRooms.childrenRooms
        suggestedChildRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable,
          FilterID.RecommendedForFamilies
        )

      }
    }

    "not return recommended for family filter for rooms with suitability type not family " +
      "and not return 2+ filter for rooms when room have less than 2 bedroom" in {
        val contextHolder = createContextHolderFromRequest(aValidPropertyRequest)

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse2))
          mockRoomInformationService(roomInformationResponse2)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val propertyResult = await(result)
          propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
          val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

          childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable
          )
          childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs Seq(
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable
          )
          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable
          )
        }
      }

    "not return recommended for family filter for rooms with suitability type not family " +
      "and not return 2+ filter for rooms when room have less than 2 bedroom" +
      "and experiment is A" in {
        val contextHolder = createContextHolderFromRequest(aValidPropertyRequest)

        val customInjector: FrameworkInjector = composeInjector(new MockModule {
          mockDFResponse(dfResult)
          mockContentResponse(Future.successful(contentResponse2))
          mockRoomInformationService(roomInformationResponse2)
        })

        injectionTest(customInjector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withSearchType(SearchTypes.Property)
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest),
            contextHolder
          )
          val expectedPropertyFilters = PropertyFilters(
            "CMSId:64940",
            Seq(
              FilterTag(
                FilterID.noCCPropertyPage,
                "CMSId:80031",
                Symbols.noCCPropertyPageFilter,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.PayAtHotel,
                "CMSId:56649",
                Symbols.PayAtHotel,
                None,
                Some(FilterGroupID.PaymentOptions)
              ),
              FilterTag(
                FilterID.KitchenAvailable,
                "CMSId:83947",
                Symbols.kitchenAvailableIcon,
                None,
                Some(FilterGroupID.RoomFeatures)
              )
            ),
            Some(
              Seq(
                FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
                FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
              )
            )
          )

          val propertyResult = await(result)
          propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
          val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

          childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable
          )
          childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs Seq(
            FilterID.PayAtHotel,
            FilterID.KitchenAvailable
          )
          childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs Seq(
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage,
            FilterID.KitchenAvailable
          )
        }
      }

    "Not return baby cot filter when room feature not contain infant cot" in {
      val pricing = aValidPricingRequest.withChildrens(1).withChildAges(Some(List(Some(2))))
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse2))
        mockRoomInformationService(roomInformationResponse2)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable
        )
        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable
        )
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.noCCPropertyPage,
          FilterID.KitchenAvailable
        )
      }
    }

    "Not return interconnecting rooms filter when room is not interconnecting room" in {
      val pricing = aValidPricingRequest.withAdult(4)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse2))
        mockRoomInformationService(roomInformationResponse2)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable
        )
        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable
        )
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.noCCPropertyPage,
          FilterID.KitchenAvailable
        )
      }
    }

    "Return voucher filter tag when voucher rate plan exists" in {
      val pricing = aValidPricingRequest.withAdult(4)
      val propertyRequest = aValidPropertyRequest
        .withPricing(pricing)
        .withHotelIds(List(aValidHotelId))
        .withSearchType(SearchTypes.Property)
        .build()

      val contextHolder = createContextHolderFromRequest(propertyRequest)
      val dfResultWithRatePlan =
        result.map(hotel => hotel.copy(rooms = hotel.rooms.map(room => room.copy(channel = Channel(716L)))))

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(Future.successful(Some(aValidDFProperties.copy(hotels = dfResultWithRatePlan))))
        mockContentResponse(Future.successful(contentResponse2))
        mockRoomInformationService(roomInformationResponse2)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(propertyRequest, contextHolder)
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.RoomVoucher,
              "CMSId:113243",
              Symbols.VoucherIcon,
              None,
              Some(FilterGroupID.SpecialOffers)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.SpecialOffers, "CMSId:171401"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)
        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable,
          FilterID.RoomVoucher
        )
        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.KitchenAvailable,
          FilterID.RoomVoucher
        )
        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs Seq(
          FilterID.PayAtHotel,
          FilterID.noCCPropertyPage,
          FilterID.KitchenAvailable,
          FilterID.RoomVoucher
        )
      }
    }

    "return Additional Recommended Filters" in {
      val contextHolder = createContextHolderFromRequest(
        aValidPropertyRequest
          .withFeatureFlag(
            aValidFeatureFlagRequest.copy(flags = Some(Map(FeatureFlagKey.FamilyFiltersOnPropertyPage -> true)))
          )
          .build()
      )

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse3)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          aValidPropertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolder
        )

        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.BalconyTerrace,
              "CMSId:65180",
              Symbols.balconyTerraceIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.QueenBed,
              "CMSId:59503",
              Symbols.queenBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            ),
            FilterTag(FilterID.PoolView, "CMSId:85564", Symbols.poolViewIcon, None, Some(FilterGroupID.RoomViews))
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomViews, "CMSId:388180"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val propertyResult = await(result)
        propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

        val childRooms = propertyResult.property.head.masterRooms.head.childrenRooms

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.QueenBed,
            FilterID.PoolView,
            FilterID.BalconyTerrace,
            FilterID.PayAtHotel
          )

        childRooms.find(_.uid.contains(insiderRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.QueenBed,
            FilterID.PoolView,
            FilterID.BalconyTerrace,
            FilterID.PayAtHotel
          )

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.QueenBed,
            FilterID.PoolView,
            FilterID.BalconyTerrace,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage
          )
      }
    }

    "return Dayuse and overnight filter when mergeDayUseOvernight flag is true" in {
      val propertyRequest = aValidPropertyRequest.withFeatureFlag(
        FeatureFlagRequest(mergeDayUseOffersWithOvernight = Some(true))
      )
      val contextHolder = createContextHolderFromRequest(propertyRequest.build())

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          propertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolder
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(FilterID.DayUse, "CMSId:372538", Symbols.DayUseIcon, None, Some(FilterGroupID.Others)),
            FilterTag(FilterID.Overnight, "CMSId:372539", Symbols.OvernightIcon, None, Some(FilterGroupID.Others)),
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886"),
              FilterGroup(FilterGroupID.Others, "CMSId:56918")
            )
          )
        )

        val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.DayUse,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage
          )

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.Overnight,
            FilterID.PayAtHotel
          )
      }
    }

    "should not return Dayuse and overnight filter when mergeDayUseOvernight flag is false" in {
      val propertyRequest = aValidPropertyRequest.withFeatureFlag(
        FeatureFlagRequest(mergeDayUseOffersWithOvernight = Some(false))
      )
      val contextHolder = createContextHolderFromRequest(propertyRequest.build())

      val customInjector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        mockContentResponse(Future.successful(contentResponse))
        mockRoomInformationService(roomInformationResponse)
      })

      injectionTest(customInjector) { implicit injector =>
        val result = FlowTestExecutor.executePropertySearch(
          propertyRequest
            .withSearchType(SearchTypes.Property)
            .withHotelIds(List(aValidHotelId))
            .withPricing(aValidPricingRequest),
          contextHolder
        )
        val expectedPropertyFilters = PropertyFilters(
          "CMSId:64940",
          Seq(
            FilterTag(
              FilterID.noCCPropertyPage,
              "CMSId:80031",
              Symbols.noCCPropertyPageFilter,
              None,
              Some(FilterGroupID.PaymentOptions)
            ),
            FilterTag(FilterID.PayAtHotel, "CMSId:56649", Symbols.PayAtHotel, None, Some(FilterGroupID.PaymentOptions)),
            FilterTag(
              FilterID.NonSmoking,
              "CMSId:65152",
              Symbols.nonSmokingFilter,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.KitchenAvailable,
              "CMSId:83947",
              Symbols.kitchenAvailableIcon,
              None,
              Some(FilterGroupID.RoomFeatures)
            ),
            FilterTag(
              FilterID.TwinBed,
              "CMSId:84511",
              Symbols.twinBedIcon,
              None,
              Some(FilterGroupID.BeddingConfiguration)
            )
          ),
          Some(
            Seq(
              FilterGroup(FilterGroupID.PaymentOptions, "CMSId:387877"),
              FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161"),
              FilterGroup(FilterGroupID.RoomFeatures, "CMSId:173886")
            )
          )
        )

        val childRooms = getChildrenRoomAndVerifyAvailableFilters(result, expectedPropertyFilters)

        childRooms.find(_.uid.contains(noCCRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.PayAtHotel,
            FilterID.noCCPropertyPage
          )

        childRooms.find(_.uid.contains(normalRoomId)).get.filterTags should contain theSameElementsAs
          Seq(
            FilterID.NonSmoking,
            FilterID.KitchenAvailable,
            FilterID.TwinBed,
            FilterID.PayAtHotel
          )
      }
    }
  }

}
