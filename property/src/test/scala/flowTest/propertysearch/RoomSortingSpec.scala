package flowTest.propertysearch

import com.agoda.core.search.models.ProviderIds
import com.agoda.mockito.helper.MockitoHelper
import com.agoda.papi.enums.room.{ CancellationGroup, ExternalLoyaltyItem, ExternalLoyaltyItemType }
import com.agoda.papi.search.common.framework.mock.WhiteLabelServiceRmWhitelabelKeyTest
import com.agoda.papi.search.common.service.WhiteLabelService
import com.agoda.papi.search.common.util.CoreDataExamples.aValidSearchContext
import com.agoda.papi.search.common.util.context.{ ActiveABTests, ContextHolder }
import framework.{ FlowTestExecutor, MockFrameworkHelper, MockModule }
import models.db.RoomUID
import models.pricing.enums.CorTypeFlags
import models.starfruit._
import org.mockito.Mockito
import org.scalatest.BeforeAndAfterEach
import org.scalatest.wordspec.AnyWordSpec
import org.specs2.mock.mockito.ArgumentCapture
import types.HotelIdType
import util.builders.TestDataBuilders._

import scala.concurrent.Future

class RoomSortingSpec extends AnyWordSpec with MockFrameworkHelper with MockitoHelper with BeforeAndAfterEach {

  val activeABTest = new ActiveABTests()

  val rocketmileWhiteLabelKey = "RocketMile"

  val wlService = Mockito.spy(new WhiteLabelServiceRmWhitelabelKeyTest(rocketmileWhiteLabelKey))

  override def beforeEach(): Unit =
    Mockito.clearInvocations(wlService)

  "RoomSortingSpec Should" should {
    val context = createContextHolderFromRequest(aValidPropertyRequest)

    /** roomIdentifier, price, tier, isFit, priceAfterCashback, breakfastIncluded, freeCancellation, AgodaDirect */
    val mockCollectionRoom1 =
      mockRoom("mockCollectionRoom1", (90, 100), ExternalLoyaltyItem.TierZero, isFitCapacity = true)
    val mockCollectionRoom2 =
      mockRoom("mockCollectionRoom2", (110, 120), ExternalLoyaltyItem.TierOne, isFitCapacity = true)
    val mockCollectionRoom3 =
      mockRoom("mockCollectionRoom3", (110, 120), ExternalLoyaltyItem.TierOne, isFitCapacity = true, Option(90, 100))
    val mockCollectionRoom4 =
      mockRoom(
        "mockCollectionRoom4",
        (110, 120),
        ExternalLoyaltyItem.TierOne,
        isFitCapacity = true,
        Option(90, 100),
        isBreakfastIncluded = true
      )
    val mockCollectionRoom5 =
      mockRoom(
        "mockCollectionRoom5",
        (110, 120),
        ExternalLoyaltyItem.TierOne,
        isFitCapacity = true,
        Option(90, 100),
        isBreakfastIncluded = true,
        isFreeCancellation = true
      )
    val mockCollectionRoom6 =
      mockRoom(
        "mockCollectionRoom6",
        (110, 120),
        ExternalLoyaltyItem.TierOne,
        isFitCapacity = true,
        Option(90, 100),
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
    val mockCollectionRoom7 = mockRoom("mockCollectionRoom7", (110, 120), ExternalLoyaltyItem.TierZero)
    val mockCollectionRoom8 = mockRoom("mockCollectionRoom8", (100, 110), ExternalLoyaltyItem.TierZero)
    val mockCollectionRoom9 =
      mockRoom(
        "mockCollectionRoom9",
        (100, 120),
        ExternalLoyaltyItem.TierOne,
        isFitCapacity = true,
        None,
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
    val mockCollectionRoom10 =
      mockRoom(
        "mockCollectionRoom10",
        (110, 120),
        ExternalLoyaltyItem.TierOne,
        isFitCapacity = true,
        Option(80, 90),
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )

    val mockNonCollectionRoom1 = mockRoom("mockNonCollectionRoom1", (200, 210))
    val mockNonCollectionRoom2 =
      mockRoom("mockNonCollectionRoom2", (210, 220), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val mockNonCollectionRoom3 =
      mockRoom(
        "mockNonCollectionRoom3",
        (210, 220),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        Option(180, 190),
        isFreeCancellation = true
      )
    val mockNonCollectionRoom4 =
      mockRoom(
        "mockNonCollectionRoom4",
        (210, 220),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        Option(180, 190),
        isBreakfastIncluded = true
      )
    val mockNonCollectionRoom5 =
      mockRoom(
        "mockNonCollectionRoom5",
        (210, 220),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        Option(180, 190),
        isBreakfastIncluded = true,
        isFreeCancellation = true
      )
    val mockNonCollectionRoom6 = mockRoom(
      "mockNonCollectionRoom6",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(180, 190),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true
    )
    val mockNonCollectionRoom7 = mockRoom("mockNonCollectionRoom7", (220, 230))
    val mockNonCollectionRoom8 =
      mockRoom(
        "mockNonCollectionRoom8",
        (220, 230),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        None,
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
    val mockNonCollectionRoom9 =
      mockRoom("mockNonCollectionRoom9", (200, 210), ExternalLoyaltyItem.Unknown, isFitCapacity = true)
    val mockNonCollectionRoom10 = mockRoom(
      "mockNonCollectionRoom10",
      (210, 220),
      ExternalLoyaltyItem.Unknown,
      isFitCapacity = true,
      Option(170, 180),
      isBreakfastIncluded = true,
      isFreeCancellation = true,
      isAgodaDirectSupply = true
    )

    val rooms = List(
      /** Without collection */
      mockNonCollectionRoom1,
      mockNonCollectionRoom2,
      mockNonCollectionRoom3,
      mockNonCollectionRoom4,
      mockNonCollectionRoom5,
      mockNonCollectionRoom6,
      mockNonCollectionRoom7,
      mockNonCollectionRoom8,
      mockNonCollectionRoom9,
      mockNonCollectionRoom10,
      /** With collection */
      mockCollectionRoom1,
      mockCollectionRoom2,
      mockCollectionRoom3,
      mockCollectionRoom4,
      mockCollectionRoom5,
      mockCollectionRoom6,
      mockCollectionRoom7,
      mockCollectionRoom8,
      mockCollectionRoom9,
      mockCollectionRoom10
    )
    val roomsWithoutShowCashBack = rooms.map(notShowCashBack)

    "Normal cashback" should {
      val hotelResult: Seq[Hotel] = Seq(
        aValidDFHotel
          .withHotelId(aValidHotelId)
          .withRooms(rooms)
          .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
          .build(),
        aValidDFHotel
          .withHotelId(aValidHotelIdWithInclusiveSuggestPrice)
          .withRooms(rooms)
          .withSuggestPriceType(Some(aValidAllInclusiveSuggestPriceType))
          .build()
      )
      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))
      val injector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
      })
      val context = createContextHolderFromRequest(aValidPropertyRequest)

      "Return correct rooms with sorted order for Exclusive price" in {
        injectionTest(injector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId, aValidHotelIdWithInclusiveSuggestPrice))
              .withPricing(aValidPricingRequest),
            context
          )
          whenReady(result) { r =>
            val expectedRoomIdentifiers = List(
              /** Fit with collection */
              "mockCollectionRoom10",
              "mockCollectionRoom3",
              "mockCollectionRoom4",
              "mockCollectionRoom5",
              "mockCollectionRoom6",
              "mockCollectionRoom1",
              "mockCollectionRoom9",
              "mockCollectionRoom2",
              /** Fit without collection */
              "mockNonCollectionRoom10",
              "mockNonCollectionRoom6",
              "mockNonCollectionRoom5",
              "mockNonCollectionRoom4",
              "mockNonCollectionRoom8",
              "mockNonCollectionRoom3",
              "mockNonCollectionRoom9",
              "mockNonCollectionRoom2",
              /** Unfit with collection */
              "mockCollectionRoom8",
              "mockCollectionRoom7",
              /** Unfit without collection */
              "mockNonCollectionRoom1",
              "mockNonCollectionRoom7"
            )
            testRoomSorting(context, aValidHotelId, expectedRoomIdentifiers)
          }
        }
      }

      "Return correct rooms with sorted order for Inclusive price" in {
        injectionTest(injector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId, aValidHotelIdWithInclusiveSuggestPrice))
              .withPricing(aValidPricingRequest),
            context
          )
          whenReady(result) { r =>
            val expectedRoomIdentifiers = List(
              /** Fit with collection */
              "mockCollectionRoom10",
              "mockCollectionRoom3",
              "mockCollectionRoom4",
              "mockCollectionRoom5",
              "mockCollectionRoom6",
              "mockCollectionRoom1",
              "mockCollectionRoom2",
              "mockCollectionRoom9",
              /** Fit without collection */
              "mockNonCollectionRoom10",
              "mockNonCollectionRoom6",
              "mockNonCollectionRoom5",
              "mockNonCollectionRoom4",
              "mockNonCollectionRoom8",
              "mockNonCollectionRoom3",
              "mockNonCollectionRoom9",
              "mockNonCollectionRoom2",
              /** Unfit with collection */
              "mockCollectionRoom8",
              "mockCollectionRoom7",
              /** Unfit without collection */
              "mockNonCollectionRoom1",
              "mockNonCollectionRoom7"
            )
            testRoomSorting(context, aValidHotelIdWithInclusiveSuggestPrice, expectedRoomIdentifiers)
          }
        }
      }
    }

    "Without show cashback" should {
      val hotelResult: Seq[Hotel] = Seq(
        aValidDFHotel
          .withHotelId(aValidHotelId)
          .withRooms(roomsWithoutShowCashBack)
          .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
          .build(),
        aValidDFHotel
          .withHotelId(aValidHotelIdWithInclusiveSuggestPrice)
          .withRooms(roomsWithoutShowCashBack)
          .withSuggestPriceType(Some(aValidAllInclusiveSuggestPriceType))
          .build()
      )
      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))
      val injector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
      })
      val context = createContextHolderFromRequest(aValidPropertyRequest)

      "Return correct rooms with sorted order for Inclusive price" in {
        injectionTest(injector) { implicit injector =>
          val expectedRoomIdentifiers = List(
            /** Fit with collection and always sorted by display price */
            "mockCollectionRoom1",
            "mockCollectionRoom2",
            "mockCollectionRoom3",
            "mockCollectionRoom4",
            "mockCollectionRoom5",
            "mockCollectionRoom6",
            "mockCollectionRoom9",
            "mockCollectionRoom10",
            /** Fit without collection */
            "mockNonCollectionRoom9",
            "mockNonCollectionRoom6",
            "mockNonCollectionRoom10",
            "mockNonCollectionRoom5",
            "mockNonCollectionRoom4",
            "mockNonCollectionRoom3",
            "mockNonCollectionRoom2",
            "mockNonCollectionRoom8",
            /** Unfit with collection */
            "mockCollectionRoom8",
            "mockCollectionRoom7",
            /** Unfit without collection */
            "mockNonCollectionRoom1",
            "mockNonCollectionRoom7"
          )
          testRoomSorting(context, aValidHotelIdWithInclusiveSuggestPrice, expectedRoomIdentifiers)
        }
      }
    }

    // Room Identifier        Price(Excl, Incl) Tier       Fit    BreakfastIncluded   FreeCancellation   DirectSupply
    // ---------------------------------------------------------------------------------------------------------------
    // mockCollectionRoom1     ( 90, 100 )      TierZero    true         false                false         false
    // mockCollectionRoom2     ( 110, 120 )     TierOne     true         false                false         false
    // mockCollectionRoom3     ( 110, 120 )     TierOne     true         false                false         false
    // mockCollectionRoom4     ( 110, 120 )     TierOne     true         true                 false         false
    // mockCollectionRoom5     ( 110, 120 )     TierOne     true         true                 true          false
    // mockCollectionRoom6     ( 110, 120 )     TierOne     true         true                 true          true
    // mockCollectionRoom7     ( 110, 120 )     TierZero    false        false                false         false
    // mockCollectionRoom8     ( 100, 110 )     TierZero    false        false                false         false
    // mockCollectionRoom9     ( 100, 120 )     TierOne     true         true                 true          true
    // mockCollectionRoom10    ( 110, 120 )     TierOne     true         true                 true          true
    // mockNonCollectionRoom1  ( 250, 260 )     Unknown     true         true                 true          true
    // mockNonCollectionRoom2  ( 240, 250 )     Unknown     true         true                 true          true
    // mockNonCollectionRoom3  ( 200, 210 )     Unknown     true         true                 false         true
    // mockNonCollectionRoom4  ( 190, 200 )     Unknown     true         true                 false         true
    // mockNonCollectionRoom5  ( 150, 160 )     Unknown     true         false                true          true
    // mockNonCollectionRoom6  ( 140, 150 )     Unknown     true         false                true          true
    // mockNonCollectionRoom7  ( 120, 130 )     Unknown     true         false                false         true
    // mockNonCollectionRoom8  ( 100, 110 )     Unknown     true         false                false         true
    // mockNonCollectionRoom9  ( 200, 210 )     Unknown     true         true                 true          true
    "Meaningful top 4 offers" should {
      val mockNonCollectionRoom1 = mockRoom(
        "mockNonCollectionRoom1",
        (250, 260),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom2 = mockRoom(
        "mockNonCollectionRoom2",
        (240, 250),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom3 = mockRoom(
        "mockNonCollectionRoom3",
        (200, 210),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isBreakfastIncluded = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom4 = mockRoom(
        "mockNonCollectionRoom4",
        (190, 200),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isBreakfastIncluded = true,
        isAgodaDirectSupply = true
      )

      val mockNonCollectionRoom5 = mockRoom(
        "mockNonCollectionRoom5",
        (150, 160),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom6 = mockRoom(
        "mockNonCollectionRoom6",
        (140, 150),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom7 = mockRoom(
        "mockNonCollectionRoom7",
        (120, 130),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom8 = mockRoom(
        "mockNonCollectionRoom8",
        (100, 110),
        ExternalLoyaltyItem.Unknown,
        isFitCapacity = true,
        isAgodaDirectSupply = true
      )
      val mockNonCollectionRoom9 = mockRoom(
        "mockNonCollectionRoom9",
        (200, 210),
        ExternalLoyaltyItem.Unknown,
        isBreakfastIncluded = true,
        isFreeCancellation = true,
        isAgodaDirectSupply = true
      )

      val rooms = List(
        /** Without collection */
        mockNonCollectionRoom1,
        mockNonCollectionRoom2,
        mockNonCollectionRoom3,
        mockNonCollectionRoom4,
        mockNonCollectionRoom5,
        mockNonCollectionRoom6,
        mockNonCollectionRoom7,
        mockNonCollectionRoom8,
        mockNonCollectionRoom9,

        /** With collection */
        mockCollectionRoom1,
        mockCollectionRoom2,
        mockCollectionRoom3,
        mockCollectionRoom4,
        mockCollectionRoom5,
        mockCollectionRoom6,
        mockCollectionRoom7,
        mockCollectionRoom8,
        mockCollectionRoom9,
        mockCollectionRoom10
      )
      val hotelResult: Seq[Hotel] = Seq(
        aValidDFHotel
          .withHotelId(aValidHotelId)
          .withRooms(rooms)
          .withSuggestPriceType(Some(aValidExclusiveSuggestPriceType))
          .build(),
        aValidDFHotel
          .withHotelId(aValidHotelIdWithInclusiveSuggestPrice)
          .withRooms(rooms)
          .withSuggestPriceType(Some(aValidAllInclusiveSuggestPriceType))
          .build()
      )
      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))
      val injector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
      })

      "Return correct rooms with sorted order for Exclusive price" in {
        injectionTest(injector) { implicit injector =>
          val context = createContextHolderFromRequest(aValidPropertyRequest)
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId, aValidHotelIdWithInclusiveSuggestPrice))
              .withPricing(aValidPricingRequest),
            context
          )
          whenReady(result) { r =>
            val expectedRoomIdentifiers = List(
              /** Fit with collection */
              "mockCollectionRoom10",
              "mockCollectionRoom3",
              "mockCollectionRoom4",
              "mockCollectionRoom5",
              "mockCollectionRoom6",
              "mockCollectionRoom1",
              "mockCollectionRoom9",
              "mockCollectionRoom2",
              /** Fit without collection */
              "mockNonCollectionRoom8", // cheapest
              "mockNonCollectionRoom7", // cheapest with pay at hotel
              "mockNonCollectionRoom6", // cheapest with free cancellation
              "mockNonCollectionRoom4", // cheapest with breakfast
              "mockNonCollectionRoom2", // cheapest with free cancellation and breakfast
              "mockNonCollectionRoom5",
              "mockNonCollectionRoom3",
              "mockNonCollectionRoom1",
              /** Unfit with collection */
              "mockCollectionRoom8",
              "mockCollectionRoom7",
              /** Unfit without collection */
              "mockNonCollectionRoom9"
            )
            testRoomSorting(context, aValidHotelId, expectedRoomIdentifiers)
          }
        }
      }

      "Return correct rooms with sorted order for Inclusive price" in {
        injectionTest(injector) { implicit injector =>
          val context = createContextHolderFromRequest(aValidPropertyRequest)
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId, aValidHotelIdWithInclusiveSuggestPrice))
              .withPricing(aValidPricingRequest),
            context
          )
          whenReady(result) { r =>
            val expectedRoomIdentifiers = List(
              /** Fit with collection */
              "mockCollectionRoom10",
              "mockCollectionRoom3",
              "mockCollectionRoom4",
              "mockCollectionRoom5",
              "mockCollectionRoom6",
              "mockCollectionRoom1",
              "mockCollectionRoom2",
              "mockCollectionRoom9",
              /** Fit without collection */
              "mockNonCollectionRoom8", // cheapest
              "mockNonCollectionRoom7", // cheapest with pay at hotel
              "mockNonCollectionRoom6", // cheapest with free cancellation
              "mockNonCollectionRoom4", // cheapest with breakfast
              "mockNonCollectionRoom2", // cheapest with free cancellation and breakfast
              "mockNonCollectionRoom5",
              "mockNonCollectionRoom3",
              "mockNonCollectionRoom1",
              /** Unfit with collection */
              "mockCollectionRoom8",
              "mockCollectionRoom7",
              /** Unfit without collection */
              "mockNonCollectionRoom9"
            )
            testRoomSorting(context, aValidHotelIdWithInclusiveSuggestPrice, expectedRoomIdentifiers)
          }
        }
      }
    }

    "Normal cashback and SearchContext setup" should {

      val hotelResult: Seq[Hotel] = Seq(
        aValidDFHotel.withRooms(rooms).build()
      )
      val dfResult = Future.successful(Some(aValidDFProperties.copy(hotels = hotelResult)))
      val injector: FrameworkInjector = composeInjector(new MockModule {
        mockDFResponse(dfResult)
        bind[WhiteLabelService] to wlService
      })

      val roomieContext = new ForceBExperimentContext(
        Some(
          Seq(
            "ROOMIE-364"
          )
        )
      )

      val expectedSortedRoomIdentifiers = List(
        /** Fit with collection */
        "mockCollectionRoom10",
        "mockCollectionRoom3",
        "mockCollectionRoom4",
        "mockCollectionRoom5",
        "mockCollectionRoom6",
        "mockCollectionRoom1",
        "mockCollectionRoom9",
        "mockCollectionRoom2",
        /** Fit without collection */
        "mockNonCollectionRoom10",
        "mockNonCollectionRoom6",
        "mockNonCollectionRoom5",
        "mockNonCollectionRoom4",
        "mockNonCollectionRoom8",
        "mockNonCollectionRoom3",
        "mockNonCollectionRoom9",
        "mockNonCollectionRoom2",
        /** Unfit with collection */
        "mockCollectionRoom8",
        "mockCollectionRoom7",
        /** Unfit without collection */
        "mockNonCollectionRoom1",
        "mockNonCollectionRoom7"
      )

      val expectedNonSortedRoomIdentifiers = List(
        "mockNonCollectionRoom1",
        "mockNonCollectionRoom2",
        "mockNonCollectionRoom3",
        "mockNonCollectionRoom4",
        "mockNonCollectionRoom5",
        "mockNonCollectionRoom6",
        "mockNonCollectionRoom7",
        "mockNonCollectionRoom8",
        "mockNonCollectionRoom9",
        "mockNonCollectionRoom10",
        "mockCollectionRoom1",
        "mockCollectionRoom2",
        "mockCollectionRoom3",
        "mockCollectionRoom4",
        "mockCollectionRoom5",
        "mockCollectionRoom6",
        "mockCollectionRoom7",
        "mockCollectionRoom8",
        "mockCollectionRoom9",
        "mockCollectionRoom10"
      )

      s"Return correct rooms with sorted order when SearchContext is None" in {
        injectionTest(injector) { implicit injector =>
          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest.copy(requiredPrice = Some(SuggestedPrice.Exclusive))),
            context.copy(experimentContext = roomieContext, searchContext = None)
          )
          whenReady(result) { r =>
            val roomIdentifiers = r.rooms.map(room => room.roomIdentifiers.get)
            roomIdentifiers shouldBe expectedSortedRoomIdentifiers

            val captor = new ArgumentCapture[ContextHolder]()
            Mockito.verify(wlService, Mockito.times(1)).isRoomSortingEnabled(captor.capture)
            captor.value.searchContext shouldBe empty
          }
        }
      }

      s"Return correct rooms with sorted order and pass SearchContext to WhitelabelService when WL Key is not Rocketmile" in {
        injectionTest(injector) { implicit injector =>
          val searchContext = aValidSearchContext.copy(whiteLabelKey = Some("agoda"))

          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest.copy(requiredPrice = Some(SuggestedPrice.Exclusive))),
            context.copy(experimentContext = roomieContext, searchContext = Some(searchContext))
          )
          whenReady(result) { r =>
            val roomIdentifiers = r.rooms.map(room => room.roomIdentifiers.get)
            roomIdentifiers shouldBe expectedSortedRoomIdentifiers

            val captor = new ArgumentCapture[ContextHolder]()
            Mockito.verify(wlService, Mockito.times(1)).isRoomSortingEnabled(captor.capture)
            captor.value.searchContext shouldBe defined
          }
        }
      }

      s"Return correct rooms with non-sorted order and pass SearchContext to WhitelabelService when WL Key is Rocketmile" in {
        injectionTest(injector) { implicit injector =>
          val searchContext = aValidSearchContext.copy(whiteLabelKey = Some(rocketmileWhiteLabelKey))

          val result = FlowTestExecutor.executePropertySearch(
            aValidPropertyRequest
              .withHotelIds(List(aValidHotelId))
              .withPricing(aValidPricingRequest.copy(requiredPrice = Some(SuggestedPrice.Exclusive))),
            context.copy(experimentContext = roomieContext, searchContext = Some(searchContext))
          )
          whenReady(result) { r =>
            val roomIdentifiers = r.rooms.map(room => room.roomIdentifiers.get)
            roomIdentifiers shouldBe expectedNonSortedRoomIdentifiers

            val captor = new ArgumentCapture[ContextHolder]()
            Mockito.verify(wlService, Mockito.times(1)).isRoomSortingEnabled(captor.capture)
            captor.value.searchContext shouldBe defined
          }
        }
      }
    }

  }

  private def testRoomSorting(context: ContextHolder, hotelId: HotelIdType, expectedRoomPrices: List[RoomUID])(implicit
      injector: FrameworkInjector
  ): Unit = {
    val result = FlowTestExecutor.executePropertySearch(
      aValidPropertyRequest
        .withHotelIds(List(aValidHotelId, aValidHotelIdWithInclusiveSuggestPrice))
        .withPricing(aValidPricingRequest),
      context
    )
    whenReady(result) { r =>
      val propertyIndex: Int = r.property.indexWhere(_.propertyId == hotelId)
      val roomIdentifiers    = r.roomsByIndex(propertyIndex).map(room => room.roomIdentifiers.get)
      roomIdentifiers shouldBe expectedRoomPrices
    }
  }

  def notShowCashBack(room: Room): Room =
    room.copy(cashback = room.cashback.map(cb => cb.copy(showPostCashbackPrice = false)))

  private def mockRoom(
      roomIdentifier: String,
      price: (Double, Double),
      loyaltyItem: ExternalLoyaltyItemType = ExternalLoyaltyItem.Unknown,
      isFitCapacity: Boolean = false,
      displayPriceAfterCashback: Option[(Double, Double)] = None,
      isBreakfastIncluded: Boolean = false,
      isFreeCancellation: Boolean = false,
      isAgodaDirectSupply: Boolean = false
  ): Room = {
    val payment = if (isFreeCancellation) {
      aValidDFPayment.copy(cancellation =
        Cancellation(
          cxlCode = "1D100P_100P",
          cmsMappedCancellationGroup = CancellationGroup.FreeCancellation
        )
      )
    } else aValidDFPayment

    val subSupplierId = if (isAgodaDirectSupply) ProviderIds.Ycs else ProviderIds.Unknown
    val externalLoyaltyDisplay = Some(
      ExternalLoyaltyDisplay(
        Seq(ExternalLoyaltyDisplayItem(loyaltyItem, None, None))
      )
    )
    val cashback = aValidCashback.copy(showPostCashbackPrice = displayPriceAfterCashback.isDefined)
    aValidDFRoom3038
      .withRoomIdentifier(roomIdentifier)
      .withPricing(getDfPricing(price._1, price._2, "USD", displayPriceAfterCashback))
      .withCapacity(aValidCapacity.copy(numberOfGuestsWithoutRoom = if (isFitCapacity) 0 else 1))
      .withisBreakfastIncluded(isBreakfastIncluded)
      .withPayment(payment)
      .withSubSupplierID(subSupplierId)
      .withExternalLoyaltyDisplay(externalLoyaltyDisplay)
      .withCashback(cashback)
  }

  private def getDfPricing(
      exclusive: Double,
      allInclusive: Double,
      currency: String,
      displayPriceAfterCashback: Option[(Double, Double)]
  ): Map[String, Pricing] = {
    val displayPrice = DisplayPrice(exclusive, allInclusive)
    val displayBasis = DisplayBasis(displayPrice, displayPrice, displayPrice)
    val priceSummary = aValidDisplaySummary
      .withPerRoomPerNight(
        aValidSummaryElement.copy(
          displayAfterCashback = displayPriceAfterCashback match {
            case Some(price) =>
              Some(
                aValidDisplayPrice
                  .withExclusive(price._1)
                  .withAllInclusive(price._2)
                  .build()
              )
            case _ => None
          }
        )
      )
    val dfPricing = Pricing(displayBasis, displayBasis, Seq(), Seq(), CorTypeFlags.NoCOR, priceSummary)
    Map(currency -> dfPricing)
  }

}
