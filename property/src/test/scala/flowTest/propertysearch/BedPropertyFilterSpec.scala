package flowTest.propertysearch

import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.information._
import com.agoda.papi.search.common.util.context.{ ContextGenerator, ContextHolder }
import com.agoda.papi.search.property.service.StarfruitService
import constant.{ FilterGroupID, FilterID, Symbols }
import framework.{ MockFrameworkHelper, MockModule }
import models.starfruit.{ Hotel, PayAtHotel, Properties, Room }
import request.SearchTypes
import request.PropertyRequest
import transformers.{ FilterGroup, FilterTag, PropertyFilters }
import util.BaseUnitSpec
import util.builders.TestDataBuilders._

import scala.concurrent.Future
import framework.FlowTestExecutor

class BedPropertyFilterSpec extends BaseUnitSpec with MockFrameworkHelper with ContextGenerator {

  val defaultRequest: PropertyRequest = aValidPropertyRequest
    .withHotelIds(List(aValidHotelId))
    .withPricing(aValidPricingRequest)
    .withSearchType(SearchTypes.Property)

  ///// DF Room

  val normalRoomId = "7ec7e104-f93b-7845-db06-9cb73acbf799"
  val customRoomId = "7ec7e104-f93b-7845-db06-9cb73acbf800"

  val dfRoom = aValidDFRoom.withPayment(aValidDFPayment.withPayAtHotel(PayAtHotel(false)))

  val normalRoom: Room = dfRoom.withRoomTypeId(1).withUID(normalRoomId)
  val customRoom: Room = dfRoom.withRoomTypeId(2).withUID(customRoomId)

  ///// Content Room

  val normalContentRoomId = aValidChildRoomMeta.withRoomId(1)
  val customContentRoomId = aValidChildRoomMeta.withRoomId(2)

  val normalMasterRoom = aValidMasterRoomResponse.withChildrenRoomMetaData(Vector(normalContentRoomId))

  val twinBedConfiguration = Some(createBedConfigurationResponse(Vector(aValidBedResponse)))
  val twinBedRoom =
    aValidMasterRoomResponse.withMasterRoomMetadata(customContentRoomId).withBedConfiguration(twinBedConfiguration)

  val doubleBedTypeId   = BedTypes.Double.getClass.getSimpleName.dropRight(1)
  val doubleBedResponse = aValidBedResponse.withTypeId(doubleBedTypeId).withQuantity(1)

  val doubleBedRoom = aValidMasterRoomResponse
    .withMasterRoomMetadata(customContentRoomId)
    .withBedConfiguration(Some(createBedConfigurationResponse(Vector(doubleBedResponse))))

  "Property filter for twin bed" should {
    val dfResponse                                 = DFResult(Seq(normalRoom, customRoom))
    val (contentResponse, roomInformationResponse) = contentResult(Vector(normalMasterRoom, twinBedRoom))
    val customInjector = createInjector(dfResponse, contentResponse, roomInformationResponse)

    "return twin bed filter" in {
      val contextHolder = createContextHolderFromRequest(defaultRequest)
      val expectedFilterTag = Seq(
        FilterTag(FilterID.TwinBed, "CMSId:84511", Symbols.twinBedIcon, None, Some(FilterGroupID.BeddingConfiguration))
      )
      val expectedRoomTag     = Seq((normalRoomId -> Seq.empty), (customRoomId -> Seq(FilterID.TwinBed)))
      val expectedFilterGroup = Some(Seq(FilterGroup(FilterGroupID.BeddingConfiguration, "CMSId:388161")))
      verify(customInjector, expectedPropertyFilters(expectedFilterTag, expectedFilterGroup), expectedRoomTag)(
        defaultRequest,
        contextHolder
      )
    }
  }

  "Property filter for twin bed (cont.)" should {
    val dfRooms                                    = DFResult(Seq(normalRoom, customRoom))
    val (contentResponse, roomInformationResponse) = contentResult(Vector(normalMasterRoom, doubleBedRoom))
    val customInjector                             = createInjector(dfRooms, contentResponse, roomInformationResponse)

    "return no twin bed filter" in {
      val contextHolder       = createContextHolderFromRequest(defaultRequest)
      val expectedFilterTag   = Seq.empty
      val expectedRoomTag     = Seq((normalRoomId -> Seq.empty), (customRoomId -> Seq.empty))
      val expectedFilterGroup = Some(Seq.empty)
      verify(customInjector, expectedPropertyFilters(expectedFilterTag, expectedFilterGroup), expectedRoomTag)(
        defaultRequest,
        contextHolder
      )
    }
  }

  private def verify(
      injector: FrameworkInjector,
      expectedPropertyFilters: PropertyFilters,
      expectedRoomTag: Seq[(String, Seq[String])]
  )(implicit request: PropertyRequest, contextHolder: ContextHolder): Unit =
    injectionTest(injector) { implicit injector =>
      val result         = FlowTestExecutor.executePropertySearch(request, contextHolder)
      val propertyResult = await(result)
      propertyResult.property.head.availableFilters shouldBe Some(expectedPropertyFilters)

      val childRooms = propertyResult.property.head.masterRooms.flatMap(_.childrenRooms)
      expectedRoomTag.map { e =>
        childRooms.groupBy(_.uid.get).get(e._1).get.flatMap(_.filterTags).distinct should contain theSameElementsAs e._2
      }
    }

  private def createInjector(
      dfResult: Future[Option[Properties]],
      contentResponse: Seq[PropertyResponse],
      roomInformationResponse: Future[Seq[RoomInformationResponse]]
  ): FrameworkInjector =
    composeInjector(new MockModule {
      mockDFResponse(dfResult)
      mockContentResponse(Future.successful(contentResponse))
      mockRoomInformationService(roomInformationResponse)
    })

  private def createBedConfigurationResponse(bedResponses: Vector[BedResponse]): BedConfigurationResponse = {
    val bedOptionResponse            = aValidBedOptionResponse.withBeds(bedResponses)
    val bedroomConfigurationResponse = aValidBedroomConfigurationResponse.withOptions(Vector(bedOptionResponse))
    val bedDetailResponse            = aValidBedDetailResponse.withBedRooms(Vector(bedroomConfigurationResponse))
    val bedConfigurationResponse     = aValidBedConfigurationResponse.withConfiguration(Some(bedDetailResponse))
    bedConfigurationResponse
  }

  private def expectedPropertyFilters(
      filterTags: Seq[FilterTag],
      filterGroups: Option[Seq[FilterGroup]]
  ): PropertyFilters =
    PropertyFilters("CMSId:64940", filterTags, filterGroups)

  private def DFResult(dfRooms: Seq[Room]): Future[Option[Properties]] = {
    val result: Seq[Hotel] = Seq(aValidDFHotel.withRooms(dfRooms).withCheapestRoomID(normalRoomId).build())
    Future.successful(Some(aValidDFProperties.copy(hotels = result)))
  }

  private def contentResult(
      roomInfos: Vector[MasterRoomResponse]
  ): (Seq[PropertyResponse], Future[Seq[RoomInformationResponse]]) = {
    val rooms: RoomInformationResponse = aValidRoomInformationResponse.withMasterRoomResponse(roomInfos)
    val roomInformationResponse        = Future.successful(Seq[RoomInformationResponse](rooms))

    val propertyResponse                       = aValidContentPropertyResponse.withRooms(Some(rooms))
    val contentResponse: Seq[PropertyResponse] = Seq(propertyResponse)
    (contentResponse, roomInformationResponse)
  }

}
