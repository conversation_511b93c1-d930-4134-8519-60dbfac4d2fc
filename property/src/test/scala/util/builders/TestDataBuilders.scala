package util.builders

import com.agoda.content.models.db.information.{ MasterRoomResponse, RoomMetadata }
import com.agoda.pricestream.models.response.PropertyPriceData
import request._
import com.agoda.core.search.models.request.ExtraHotelsRequest
import com.agoda.papi.search.common.service.CalculateChannelParams
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.property.externaldependency.repository.OverlapBookingCountResult
import transformers.{ Property, _ }
import util.builders.contentbuilder._
import util.builders.examples.{ DefaultDataExample, DefaultRequestExample }
import util.builders.pricingbuilder._
import util.builders.requestbuilder._
import util.builders.responsebuilder.{ CitySearchResponseBuilder, ZenithResponseBuilder }
import util.builders.searchbuilder._
import util.builders.servicesbuilder.CalculateChannelParamsBuilder
import models.starfruit.{
  AdditionalPriceDisplayBasis => DFAdditionalPriceDisplayBasis,
  PackagePriceAndSaving => DFPackagePriceAndSaving,
  Packaging => _,
  PriceWithChannel => DFPriceWithChannel,
  Properties => _,
  _
}
import metaapi.{ PriceTrendByCityRequest, PriceTrendByPropertyRequest }

/** Inject this trait to get fast correct input data
  */
sealed trait TestDataBuilders extends ImplicitTestDataBuilder with DefaultTestDataExample

object TestDataBuilders extends TestDataBuilders

//scalastyle:off
private[builders] trait ImplicitTestDataBuilder extends ExternalImplicitTestDataBuilder {

  import scala.language.{ higherKinds, implicitConversions }

  /* =================================
   *        IMPLCIT CONVERSIONS
   *        please register all the request to builder conversion here
   * ================================= */
  implicit def builderToRequest[A](builder: Builder[A]): A = builder.build()

  implicit def toCancellationPolicyRequest(
      cancellationPolicyRequest: _root_.request.CancellationPolicyRequest
  ): CancellationPolicyRequestBuilder =
    new CancellationPolicyRequestBuilder(cancellationPolicyRequest)

  implicit def toPropertyRequestBuilder(propertyRequest: PropertyRequest): PropertyRequestBuilder =
    new PropertyRequestBuilder(propertyRequest)

  implicit def toPropertyRecomendationRequestBuilder(
      propertyRecommendationRequest: PropertyRecommendationRequest
  ): PropertyRecommendationRequestBuilder = new PropertyRecommendationRequestBuilder(propertyRecommendationRequest)

  implicit def toPropertyContext(propertyContext: PropertyContext): PropertyContextBuilder = new PropertyContextBuilder(
    propertyContext
  )

  implicit def toPricingRequestBuilder(pricingRequest: PropertyPricing): PropertyPricingBuilder =
    new PropertyPricingBuilder(pricingRequest)

  implicit def toPropertyBuilder(property: Property): PropertyBuilder = new PropertyBuilder(property)

  implicit def toEnrichedHotelWithContentBuilder(hotel: EnrichedHotelWithContent): EnrichedHotelWithContentBuilder =
    new EnrichedHotelWithContentBuilder(hotel)

  implicit def toEnrichedMasterRoomBuilder(masterRoom: EnrichedMasterRoom): EnrichedMasterRoomBuilder =
    new EnrichedMasterRoomBuilder(masterRoom)

  implicit def toEnrichedChildRoomBuilder(childRoom: EnrichedChildRoom): EnrichedChildRoomBuilder =
    new EnrichedChildRoomBuilder(childRoom)

  implicit def toEnrichedPaymentBuilder(payment: EnrichedPayment): EnrichedPaymentBuilder = new EnrichedPaymentBuilder(
    payment
  )

  implicit def toEnrichedCapacityBuilder(capacity: EnrichedCapacity): EnrichedCapacityBuilder =
    new EnrichedCapacityBuilder(capacity)

  implicit def toEnrichedPricingBuilder(enrichedPricing: EnrichedPricing): EnrichedPricingBuilder =
    new EnrichedPricingBuilder(enrichedPricing)

  implicit def toEnrichedBenefitBuilder(enrichedBenefit: EnrichedBenefit): EnrichedBenefitBuilder =
    new EnrichedBenefitBuilder(enrichedBenefit)

  implicit def toDisplayPriceBuilder(displayPrice: DisplayPrice): DisplayPriceBuilder = new DisplayPriceBuilder(
    displayPrice
  )

  implicit def toDisplayBasisBuilder(displayBasis: DisplayBasis): DisplayBasisBuilder = new DisplayBasisBuilder(
    displayBasis
  )

  implicit def toPackageDisplayPriceBuilder(packageDisplayPrice: PackageDisplayPrice): PackageDisplayPriceBuilder =
    new PackageDisplayPriceBuilder(packageDisplayPrice)

  implicit def toPackageDisplayBasisBuilder(packageDisplayBasis: PackageDisplayBasis): PackageDisplayBasisBuilder =
    new PackageDisplayBasisBuilder(packageDisplayBasis)

  implicit def toCityRequestBuilder(citySearchRequest: CitySearchRequest): CitySearchRequestBuilder =
    new CitySearchRequestBuilder(citySearchRequest)

  implicit def toAreaRequestBuilder(areaSearchRequest: AreaSearchRequest): AreaSearchRequestBuilder =
    new AreaSearchRequestBuilder(areaSearchRequest)

  implicit def toEnrichedPayAtHotel(payAtHotel: EnrichedPayAtHotel): EnrichedPayAtHotelBuilder =
    new EnrichedPayAtHotelBuilder(payAtHotel)

  implicit def toEnrichedPayLater(payLater: EnrichedPayLater): EnrichedPayLaterBuilder = new EnrichedPayLaterBuilder(
    payLater
  )

  implicit def toEnrichedPayAtCheckIn(payAtCheckIn: EnrichedPayAtCheckIn): EnrichedPayAtCheckInBuilder =
    new EnrichedPayAtCheckInBuilder(payAtCheckIn)

  implicit def toEnrichedPointMax(pointMax: EnrichedPointMax): EnrichedPointsMaxBuilder = new EnrichedPointsMaxBuilder(
    pointMax
  )

  implicit def toEnrichedCancellation(cancellation: EnrichedCancellation): EnrichedCancellationBuilder =
    new EnrichedCancellationBuilder(cancellation)

  implicit def toEnrichedChannel(channel: EnrichedChannel): EnrichedChannelBuilder = new EnrichedChannelBuilder(channel)

  implicit def toEnrichedNoCreditCard(noCC: EnrichedNoCreditCard): EnrichedNoCreditCardBuilder =
    new EnrichedNoCreditCardBuilder(noCC)

  implicit def toEnrichedPackaging(packaging: EnrichedPackaging): EnrichedPackagingBuilder =
    new EnrichedPackagingBuilder(packaging)

  implicit def toEnrichedPackagingPricing(packagingPricing: EnrichedPackagingPricing): EnrichedPackagingPricingBuilder =
    new EnrichedPackagingPricingBuilder(packagingPricing)

  implicit def toGiftCard(giftCard: GiftCard): GiftCardBuilder = new GiftCardBuilder(giftCard)

  implicit def toCashback(cashback: Cashback): CashbackBuilder = new CashbackBuilder(cashback)

  implicit def toRegionRequestBuilder(regionSearchRequest: RegionSearchRequest): RegionSearchRequestBuilder =
    new RegionSearchRequestBuilder(regionSearchRequest)

  implicit def toRadiusRequestBuilder(radiusSearchRequest: RadiusSearchRequest): RadiusSearchRequestBuilder =
    new RadiusSearchRequestBuilder(radiusSearchRequest)

  implicit def toBoxRequestBuilder(boxSearchRequest: BoxSearchRequest): BoxSearchRequestBuilder =
    new BoxSearchRequestBuilder(boxSearchRequest)

  implicit def toLandmarkRequestBuilder(landmarkSearchRequest: LandmarkSearchRequest): LandmarkSearchRequestBuilder =
    new LandmarkSearchRequestBuilder(landmarkSearchRequest)

  implicit def toTopAreaRequestBuilder(topAreaSearchRequest: TopAreaSearchRequest): TopAreaSearchRequestBuilder =
    new TopAreaSearchRequestBuilder(topAreaSearchRequest)

  implicit def toCityCenterRequestBuilder(
      cityCenterSearchRequest: CityCenterSearchRequest
  ): CityCenterSearchRequestBuilder =
    new CityCenterSearchRequestBuilder(cityCenterSearchRequest)

  implicit def toSearchCriteriaBuilder(searchCriteria: SearchCriteria): SearchCriteriaBuilder =
    new SearchCriteriaBuilder(searchCriteria)

  implicit def toProductHotelBuilder(productHotel: ProductHotels): ProductHotelBuilder = new ProductHotelBuilder(
    productHotel
  )

  implicit def toRoomMetaDataBuilder(roomMetaData: RoomMetadata): RoomMetaDataBuilder = new RoomMetaDataBuilder(
    roomMetaData
  )

  implicit def toMasterRoomResponseBuilder(masterRoomResponse: MasterRoomResponse): MasterRoomResponseBuilder =
    new MasterRoomResponseBuilder(masterRoomResponse)

  implicit def toCitySearchResponseBuilder(citySearchResponse: CitySearchResponse): CitySearchResponseBuilder =
    new CitySearchResponseBuilder(citySearchResponse)

  implicit def toPropertiesBuilder(properties: Properties): PropertiesBuilder = new PropertiesBuilder(properties)

  implicit def toExtraHotelRequestBuilder(extraHotelsRequest: ExtraHotelsRequest): ExtraHotelRequestBuilder =
    new ExtraHotelRequestBuilder(extraHotelsRequest)

  implicit def toDFPricing(pricing: Pricing): DFPricingBuilder = new DFPricingBuilder(pricing)

  implicit def toDFPackagingToken(packagingResponseToken: PackagingResponseToken): DFPackagingResponseTokenBuilder =
    new DFPackagingResponseTokenBuilder(packagingResponseToken)

  implicit def toDFPackaging(packaging: models.starfruit.Packaging): DFPackagingBuilder = new DFPackagingBuilder(
    packaging
  )

  implicit def toDFPackagePricing(packagePricing: PackagePricing): DFPackagePricingBuilder =
    new DFPackagePricingBuilder(packagePricing)

  implicit def toBasePageRequestBuilder(baseRequest: BasePageRequest): BasePageRequestBuilder =
    new BasePageRequestBuilder(baseRequest)

  implicit def toEnrichedChargeBuilder(enrichedCharge: EnrichedCharge): EnrichedChargeBuilder =
    new EnrichedChargeBuilder(enrichedCharge)

  implicit def toEnrichedChargeBreakdownBuilder(breakdown: EnrichedChargeBreakdown): EnrichedChargeBreakdownBuilder =
    new EnrichedChargeBreakdownBuilder(breakdown)

  implicit def toDFDisplayChargeBuilder(displayCharge: DisplayCharge): DFDisplayChargeBuilder =
    new DFDisplayChargeBuilder(displayCharge)

  implicit def toDFChargeBuilder(charge: Charge): DFChargeBuilder = new DFChargeBuilder(charge)

  implicit def toDFCancellationBuilder(cancellation: Cancellation): DFCancellationBuilder = new DFCancellationBuilder(
    cancellation
  )

  implicit def toCalculateChannelParamsBuilder(
      calculateChannelParams: CalculateChannelParams
  ): CalculateChannelParamsBuilder = new CalculateChannelParamsBuilder(calculateChannelParams)

  implicit def toDFHotelAggregatedPaymentBuilder(hotelAggPayment: HotelAggregatedPayment) =
    new DFHotelAggregatedPaymentBuilder(hotelAggPayment)

  implicit def toPricingSummaryBuilder(pricingSummary: PricingSummary) = new PricingSummaryBuilder(pricingSummary)

  implicit def toMseRoomSummaryBuilder(mseRoomSummary: MseRoomSummary) = new MseRoomSummaryBuilder(mseRoomSummary)

  implicit def toHotelResultSummaryBuilder(hotelResultSummary: HotelResultSummary) = new HotelResultSummaryBuilder(
    hotelResultSummary
  )

  implicit def toZenithResponseBuilder(zenithResponse: RecommendationResponse) = new ZenithResponseBuilder(
    zenithResponse
  )

  implicit def toFeatureFlagRequestBuilder(featureFlagRequest: FeatureFlagRequest) = new FeatureFlagRequestBuilder(
    featureFlagRequest
  )

  implicit def toPropertyPriceDataBuilder(propertyPriceData: PropertyPriceData) = new PropertyPriceDataBuilder(
    propertyPriceData
  )

  implicit def toEnrichedSoldOutRoomBuilder(soldoutRoom: EnrichedSoldOutRooms): EnrichedSoldOutRoomBuilder =
    new EnrichedSoldOutRoomBuilder(soldoutRoom)

  implicit def toHotelAggInfoBuilder(hotelAggInfo: HotelAggregationInfo) = new HotelAggregationInfoBuilder(hotelAggInfo)

  implicit def toOverlapBookingCountDBBuilder(overlap: OverlapBookingCountResult) = new OverlapBookingCountBuilder(
    overlap
  )

  implicit def toPriceTrendRequestByCityBuilder(priceTrendByCityRequest: PriceTrendByCityRequest) =
    new PriceTrendByCityRequestBuilder(priceTrendByCityRequest)

  implicit def toDFPackagePriceAndSaving(
      packagePriceAndSaving: DFPackagePriceAndSaving
  ): DFPackagePriceAndSavingBuilder =
    new DFPackagePriceAndSavingBuilder(packagePriceAndSaving)

  implicit def toDFPriceWithChannel(dfPriceWithChannel: DFPriceWithChannel): DFPriceWithChannelBuilder =
    new DFPriceWithChannelBuilder(dfPriceWithChannel)

  implicit def toDFAdditionalDisplayPriceDisplayBasis(
      additionalPriceDisplayBasis: DFAdditionalPriceDisplayBasis
  ): DFAdditionalPriceDisplayBasisBuilder =
    new DFAdditionalPriceDisplayBasisBuilder(additionalPriceDisplayBasis)

  implicit def toDFCampaignBuilder(dfCampaign: Campaign): DFCampaignBuilder = new DFCampaignBuilder(dfCampaign)

  implicit def toEnrichedCampaignBuilder(campaign: EnrichedCampaign): EnrichedCampaignBuilder =
    new EnrichedCampaignBuilder(campaign)

  implicit def toPriceTrendByPropertyRequestBuilder(
      req: PriceTrendByPropertyRequest
  ): PriceTrendByPropertyRequestBuilder =
    new PriceTrendByPropertyRequestBuilder(req)

}

private[builders] trait DefaultTestDataExample extends DefaultRequestExample with DefaultDataExample

object DefaultTestDataExample extends DefaultTestDataExample
