package util.builders.examples

import _root_.request.{ PaymentRequest => PAPIPaymentRequest, PropertyRequest => PAPIPropertyRequest, _ }
import api.request.SimulateRequestData
import com.agoda.commons.traffic.{ TrafficInfo, User }
import com.agoda.content.models._
import com.agoda.content.models.request.Page
import com.agoda.content.models.request.sf.ContentContext
import com.agoda.core.search.models.response.{ SearchCriteria => TransSearchCriteria, SearchStatusAndCriteria }
import com.agoda.experiments.RequestContext
import constant.{ FilterID, Symbols }
import enumerations.RecommendationTypes
import models.pricing.DiscountTypes
import models.starfruit
import models.starfruit.{ FilterRequest => _, _ }
import com.agoda.core.search.models.enumeration.{ SearchStatuses, TravellerType, TravellerTypes }
import com.agoda.core.search.models.request.{
  APORequest,
  CancellationRequestEntry,
  ExtraHotelsRequest,
  GeoPoint,
  Page => PAPIPage
}
import com.agoda.papi.enums.campaign.CampaignDiscountTypes
import com.agoda.papi.enums.request.StackDiscountOption
import org.joda.time.DateTime
import metaapi.{ PriceTrendByCityRequest, PriceTrendByPropertyRequest }
import transformers.{ FilterTag, PropertyFilters }
import types.FilterTagType
import util.builders.TestDataBuilders.aValidContext
import com.agoda.papi.enums.request.StackDiscountOption
import util.builders.DefaultTestDataExample

//request specific default goes here
trait DefaultRequestExample extends BaseDataParameter {

  lazy val aValidContext: PropertyContext = PropertyContext(
    propertyIds = List.empty,
    searchId = "",
    locale = "en-us",
    userId = Some(java.util.UUID.randomUUID().toString),
    cid = 1,
    memberId = 1,
    origin = "",
    platform = 1
  )

  lazy val creditCardPaymentRequest = CreditCardPaymentRequest("", "", None, Some(181))

  lazy val nonUSCreditCardPaymentRequest = CreditCardPaymentRequest("", "", None, Some(171))

  lazy val paymentRequest = PAPIPaymentRequest(None, None, None, Some(creditCardPaymentRequest))

  lazy val nonUSPaymentRequest = PAPIPaymentRequest(None, None, None, Some(nonUSCreditCardPaymentRequest))

  lazy val aValidPricingRequest: PropertyPricing =
    PropertyPricing(
      checkIn = new DateTime("2017-03-01T00:00:00.000+07:00"),
      lengthOfStay = 1,
      bookingDate = new DateTime("2017-02-22T11:00:00.000+07:00"),
      numberOfRooms = 1,
      numberOfAdults = 2,
      numberOfChildren = 0,
      isUserLoggedIn = false,
      enableOpaqueChannel = false,
      allowOverrideOccupancy = false,
      partnerLoyaltyProgramId = 11,
      storeFrontId = None,
      currency = "USD",
      ratePlanId = List(),
      isRPM2Included = false,
      isIncludeUsdAndLocalCurrency = false,
      paymentRequest = Some(paymentRequest),
      requiredPrice = Some(SuggestedPrice.Unknown)
    )

  lazy val aHourlyPricingRequest       = aValidPricingRequest.copy(bookingDurationType = Some(List("hourly")))
  lazy val aValidPartnerPricingRequest = aValidPricingRequest.copy(partner = Some(aValidPartnerRequest))

  lazy val aValidPricingRequestWithNonUSIssuedCard: PropertyPricing =
    PropertyPricing(
      checkIn = new DateTime("2017-03-01T00:00:00.000+07:00"),
      lengthOfStay = 1,
      bookingDate = new DateTime("2017-02-22T11:00:00.000+07:00"),
      numberOfRooms = 1,
      numberOfAdults = 2,
      numberOfChildren = 0,
      isUserLoggedIn = false,
      enableOpaqueChannel = false,
      allowOverrideOccupancy = false,
      partnerLoyaltyProgramId = 11,
      storeFrontId = None,
      currency = "THB",
      ratePlanId = List(),
      isRPM2Included = false,
      isIncludeUsdAndLocalCurrency = false,
      paymentRequest = Some(nonUSPaymentRequest)
    )

  lazy val aValidRequestContext = RequestContext(
    languageId = "1",
    deviceTypeId = "1",
    trafficGroup = "trafficGroup",
    cId = "cityId",
    aId = "areaId",
    serverName = "server",
    origin = "thailand",
    userId = "1001"
  )

  lazy val aValidImageRequest: ImagesRequest = ImagesRequest(page = None, indexOffset = None)

  lazy val aValidFeatureRequest: FeaturesRequest =
    FeaturesRequest(groupType = None, includeMissing = false, tags = None)

  lazy val aValidInformationRequest: InformationRequest =
    InformationRequest(entertainment = None, highlightedFeaturesOrderPriority = None)

  lazy val aValidReviewDemoGraphicRequest: DemographicsRequest = DemographicsRequest(providerIds = None)

  lazy val aValidReviewCumulativeRequest: CumulativeRequest = CumulativeRequest(providerIds = None)

  lazy val aValidReviewSnippetRequest: SummariesRequest = SummariesRequest(providerIds = None, apo = false)

  lazy val aValidReviewCommentaryRequest: CommentaryRequest = CommentaryRequest(
    providerIds = None,
    page = Page(pageSize = 10, pageNumber = 1),
    demographicId = 0,
    isOnlyCurated = 0,
    sorting = 1,
    filters = None,
    topCommentSize = None
  )

  lazy val aValidRoomRequest: RoomRequestPAPI = RoomRequestPAPI(
    images = None,
    existingImages = None,
    isContentOnly = None,
    includeMissing = None,
    featureLimit = None
  )

  lazy val aValidSummaryRequest: SummaryRequest = SummaryRequest(highlightedFeaturesOrderPriority = None)

  lazy val aValidLocalInformationRequest: LocalInformationRequest = LocalInformationRequest()

  lazy val aValidExperienceRequest: ExperienceRequest = ExperienceRequest()

  lazy val aValidFilterRequest: FilterRequest = FilterRequest(demographicId = 0, providerIds = None)

  lazy val aValidHilightRequest: HighlightsRequest = HighlightsRequest(maxNumberOfItems = None)

  lazy val aValidBookingRequest: BookingRequest = BookingRequest()

  lazy val aValidFeatureFlagRequest: FeatureFlagRequest = FeatureFlagRequest()

  lazy val aValidPropertyRequest: PAPIPropertyRequest = PAPIPropertyRequest(aValidContext)

  lazy val aValidPropertyRequestWithExternalUserContext: PAPIPropertyRequest = PAPIPropertyRequest(aValidContext)

  lazy val aValidCancellationPolicyRequest: CancellationPolicyRequest =
    CancellationPolicyRequest(List(aValidCancellationRequestEntry), "en-us")

  lazy val aValidPriceChangeRequest: PriceChangeRequest = PriceChangeRequest(None, Some(true))

  // Pagesearch

  lazy val aValidBasePageRequest: BasePageRequest = BasePageRequest(
    pricing = aValidPricingRequest,
    page = Option(PAPIPage(pageSize = 40, pageNumber = 1))
  )

  lazy val aValidCitySearchRequest: CitySearchRequest = CitySearchRequest(
    context = aValidContext,
    cityId = 9395,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidAreaSearchRequest: AreaSearchRequest = AreaSearchRequest(
    context = aValidContext,
    cityId = 9395,
    areaId = 27713,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidLandmarkSearchRequest: LandmarkSearchRequest = LandmarkSearchRequest(
    context = aValidContext,
    landmarkId = 22172,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidRadiusSearchRequest: RadiusSearchRequest = RadiusSearchRequest(
    context = aValidContext,
    lat = 13.737397,
    lon = 100.560793,
    distance = 5,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidBoxSearchRequest: BoxSearchRequest = BoxSearchRequest(
    context = aValidContext,
    topLeft = GeoPoint(13.747397, 100.560793),
    bottomRight = GeoPoint(12.737397, 101.570793),
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidTopAreaSearchRequest: TopAreaSearchRequest = TopAreaSearchRequest(
    context = aValidContext,
    topAreaId = 27713,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidCityCenterSearchRequest: CityCenterSearchRequest = CityCenterSearchRequest(
    context = aValidContext,
    cityCenterId = aValidCityCenterId,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidRegionSearchRequest: RegionSearchRequest = RegionSearchRequest(
    context = aValidContext,
    regionId = 504,
    baseRequest = aValidBasePageRequest
  )

  lazy val aValidBasePageRequestWithPriceRange = aValidBasePageRequest
    .copy(featureFlagRequest = Some(FeatureFlagRequest(recommendedPriceRange = Some(true))))

  lazy val aValidBasePageRequestWithPriceRangeFalse = aValidBasePageRequest
    .copy(featureFlagRequest = Some(FeatureFlagRequest(recommendedPriceRange = Some(false))))

  lazy val aValidCitySearchRequestWithPriceRange: CitySearchRequest = CitySearchRequest(
    context = aValidContext,
    cityId = 9395,
    baseRequest = aValidBasePageRequestWithPriceRange
  )

  lazy val aValidCitySearchRequestWithPriceRangeFalse: CitySearchRequest = CitySearchRequest(
    context = aValidContext,
    cityId = 9395,
    baseRequest = aValidBasePageRequestWithPriceRangeFalse
  )

  lazy val aValidPAPIPage: PAPIPage = PAPIPage(pageSize = 45, pageNumber = 1)

  lazy val aValidAPORequest: APORequest = APORequest(apoPageSize = Some(9))

  lazy val aValidSearchStatusAndCriteria: SearchStatusAndCriteria =
    SearchStatusAndCriteria(SearchStatuses.Unknown, TransSearchCriteria(aValidCheckIn))

  lazy val aValidSearchCriteria: SearchCriteria = SearchCriteria(None, None, None, None, None, None)

  lazy val aValidTrafficInfo: TrafficInfo = TrafficInfo(trafficType = User, userAgent = None, correlationId = None)

  lazy val aValidCampaignInfo: CampaignInfo = CampaignInfo(1, 1, "Some Promotion", Some(1))

  lazy val aValidCampaignDiscount: CampaignDiscount =
    CampaignDiscount(1.0, "USD", CampaignDiscountTypes.Percentage, Some(1), Some(10.0), Some(20.0))

  lazy val aValidOverrideCampaign: OverrideCampaign = OverrideCampaign(aValidCampaignInfo, aValidCampaignDiscount)

  lazy val aValidDiscountRequest: DiscountRequest = DiscountRequest(
    Some(aValidCampaignInfo),
    Some(aValidOverrideCampaign),
    Some(Set("Some hash")),
    Some("Email"),
    Some(List(aValidCampaignInfo))
  )

  lazy val aValidLoyaltyPaymentRequest: LoyaltyPaymentRequest =
    LoyaltyPaymentRequest(10.0, 20.0, LoyaltyTypes.GiftCard, Some(1))

  lazy val aValidCreditCardPaymentRequest: CreditCardPaymentRequest =
    CreditCardPaymentRequest("USD", "USD", Some(1), None)

  lazy val aValidDFCashbackRedemptionRequest: LoyaltyPaymentRequest =
    LoyaltyPaymentRequest(10.0, 20.0, LoyaltyTypes.Cashback, Some(1))

  lazy val aValidPaymentRequest: PAPIPaymentRequest = PAPIPaymentRequest(
    Some(1),
    Some(2),
    Some(aValidLoyaltyPaymentRequest),
    Some(aValidCreditCardPaymentRequest),
    cashback = Some(aValidDFCashbackRedemptionRequest),
    isCashbackRedemptionEligible = Some(true)
  )

  lazy val aValidSupplierPullMetadataRequest: SupplierPullMetadataRequest = SupplierPullMetadataRequest(1, Some(2))

  lazy val aValidRecommendation = Recommendation(4, List.empty, Some(RecommendationTypes.RecentlyViewed))

  // Property Reco Request

  val aValidPropertyRecoRequest = PropertyRecommendationRequest(
    context = aValidContext,
    pricing = Some(aValidPricingRequest),
    recommendation = aValidRecommendation,
    cityId = Some(9395)
  )

  // DF Request
  lazy val aValidSessionInfo = SessionInfo(0, 0, false)

  lazy val aValidClientInfo: ClientInfo = ClientInfo(
    aValidContext.cid,
    aValidContext.userId.getOrElse(""),
    1,
    1,
    aValidContext.platform,
    aValidContext.origin,
    0,
    aValidContext.searchId
  )

  lazy val aValidContextRequest: ContextRequest = ContextRequest(
    sessionInfo = aValidSessionInfo,
    clientInfo = aValidClientInfo,
    abTests = Nil,
    experiment = Nil
  )

  lazy val aValidDFPricingRequest: PricingRequest = PricingRequest(
    occupancy = starfruit.OccupancyRequest(2, 0, 1, Nil, None),
    filters = models.starfruit.FilterRequest(Nil, Nil, false),
    details = DetailRequest(false, false, false),
    features = FeatureRequest(false, false, false, 0),
    checkIn = aValidPricingRequest.checkIn,
    checkout = aValidPricingRequest.checkIn.plusDays(aValidPricingRequest.lengthOfStay),
    currency = aValidPricingRequest.currency,
    bookingDate = aValidPricingRequest.bookingDate,
    roomRequest = None
  )

  lazy val aValidDFFeatureRequest = FeatureRequest(false, false, false, 0)

  lazy val aValidPropertySearchRequest: PropertySearchRequest = PropertySearchRequest(
    context = aValidContextRequest,
    propertyIds = Nil,
    pricing = aValidDFPricingRequest,
    suggestedPrice = "PB",
    isSSR = None
  )

  lazy val aValidSoldOutRequest = SoldOutAlternateDatesRequest(Some(3), Some(3), Some(1), Some(5))

  lazy val aValidSimulatePromotionStackOptions: api.request.SimulatePromotionStackOptions =
    api.request.SimulatePromotionStackOptions(false, true, Some(StackDiscountOption.Additive))

  lazy val aValidSimulatePromotionConstraintsData: api.request.SimulatePromotionConstraintsData =
    api.request.SimulatePromotionConstraintsData(
      1,
      0,
      "1111111",
      Some(new DateTime("2017-06-08T00:00:00.0000")),
      Some(new DateTime("2017-10-30T00:00:00.0000"))
    )

  lazy val aValidSimulatePromotionCmsData: api.request.SimulatePromotionCmsData =
    api.request.SimulatePromotionCmsData(69820, 47749)

  lazy val aValidSimulatePromotionDiscount: api.request.SimulatePromotionDiscount =
    api.request.SimulatePromotionDiscount(DiscountTypes.PercentDiscount, List(40.0, 0.0, 0.0, 0.0, 0.0, 0.0, 0.0))

  lazy val aValidSimulateData: api.request.SimulatePromotionData = api.request.SimulatePromotionData(
    74715822,
    10,
    Some(aValidSimulatePromotionDiscount),
    Some(aValidSimulatePromotionCmsData),
    0,
    Some(aValidSimulatePromotionConstraintsData),
    "5D100P_100P",
    List(),
    Some(aValidSimulatePromotionStackOptions),
    true
  )

  lazy val aValidSimulatePromotion: api.request.SimulatePromotion =
    api.request.SimulatePromotion(1543, 14069320, Some(aValidSimulateData))

  lazy val aValidSimulateRequestData: SimulateRequestData = SimulateRequestData(Some(aValidSimulatePromotion))

  lazy val aValidExtraHotelRequest: ExtraHotelsRequest = ExtraHotelsRequest(extraHotelIds = List(aValidHotelId))

  lazy val aValidCancellationRequestEntry =
    CancellationRequestEntry(
      "0D100P_100P",
      Some(new DateTime("2017-02-12T00:00:00.0000+07:00")),
      Some(new DateTime("2017-02-14T00:00:00.0000+07:00")),
      Some(new DateTime("2017-02-02T00:00:00.0000+07:00")),
      aValidHotelId,
      Some(new DateTime("2017-02-02T00:00:00.0000+07:00"))
    )

  lazy val aValidExperimentInfo: ExperimentInfo = ExperimentInfo(trafficGroup = "1")

  lazy val aValidRoomSelectionRequest: RoomSelectionRequest =
    RoomSelectionRequest(1, 0, 0, 0, 1, false, false, None, true)

  lazy val aValidPropertyFilters: PropertyFilters = PropertyFilters(
    title = "MockPropertyFilters",
    aValidPropertyFilterTags
  )

  lazy val aValidPropertyWithAgodaSpecialOffersFilterTags: Seq[FilterTag] = Seq(
    FilterTag(FilterID.QueenBed, "QueenBed", Symbols.queenBedIcon),
    FilterTag(FilterID.MountainView, "MountainView", Symbols.mountainViewIcon),
    FilterTag(FilterID.BreakfastInclude, "Breakfast Included", Symbols.noCCPropertyPageFilter),
    FilterTag(FilterID.FreeCancellation, "FreeCancellation", Symbols.PayAtHotel),
    FilterTag(FilterID.PayLater, "PayLater", Symbols.nonSmokingFilter),
    FilterTag(FilterID.NonSmoking, "NonSmoking", Symbols.kitchenAvailableIcon),
    FilterTag(FilterID.PayAtHotel, "PayAtHotel", Symbols.recommendedForFamiliesIcon),
    FilterTag(FilterID.AgodaSpecialOffers, "Agoda Special Offers", Symbols.bedroomIcon),
    FilterTag("NonExistingID", "CMSId:101012", "NonExistingSymbol")
  )

  lazy val aValidPropertyFilterTags: Seq[FilterTag] = Seq(
    FilterTag(FilterID.MountainView, "MountainView", Symbols.mountainViewIcon),
    FilterTag(FilterID.BreakfastInclude, "Breakfast Included", Symbols.noCCPropertyPageFilter),
    FilterTag(FilterID.FreeCancellation, "FreeCancellation", Symbols.PayAtHotel),
    FilterTag(FilterID.PayLater, "PayLater", Symbols.nonSmokingFilter),
    FilterTag(FilterID.QueenBed, "QueenBed", Symbols.queenBedIcon),
    FilterTag(FilterID.NonSmoking, "NonSmoking", Symbols.kitchenAvailableIcon),
    FilterTag(FilterID.PayAtHotel, "PayAtHotel", Symbols.recommendedForFamiliesIcon),
    FilterTag(FilterID.TwinBed, "TwinBed", Symbols.twinBedIcon),
    FilterTag("NonExistingID", "CMSId:101012", "NonExistingSymbol")
  )

  lazy val aValidGmtOffset        = Some(7)
  lazy val aValidGmtOffsetMinutes = Some(0)

  lazy val aValidPriceTrendByCityRequest = PriceTrendByCityRequest(
    context = Some(aValidContext),
    cityId = aValidCityId,
    los = aValidLos,
    occupancy = 2,
    startDate = DateTime.now,
    endDate = DateTime.now,
    supportedChannel = List.empty[Int],
    isUserLoggedIn = false,
    suggestedPrice = 0,
    currency = "USD",
    requiredPrice = Some(SuggestedPrice.AllInclusive),
    hotelStarRating = None,
    cheapest = None
  )

  lazy val aValidPartnerRequest = PartnerRequest(
    partnerRoomRateType = Some(1),
    partnerSurchargeRateType = Some(11),
    ratePartnerSummaries = Some(true)
  )

  lazy val aValidPriceTrendByPropertyRequest = PriceTrendByPropertyRequest(
    context = DefaultTestDataExample.aValidContext,
    cityId = DefaultTestDataExample.aValidCityId,
    los = 2,
    occupancy = 1,
    startDate = DateTime.now().withTimeAtStartOfDay(),
    endDate = DateTime.now().withTimeAtStartOfDay().plusDays(2),
    supportedChannel = List(),
    isUserLoggedIn = false,
    currency = DefaultTestDataExample.aValidCurrency
  )

}
