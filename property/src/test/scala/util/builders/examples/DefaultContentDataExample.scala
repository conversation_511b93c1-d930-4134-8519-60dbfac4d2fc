package util.builders.examples

import com.agoda.content.models.{ ImportantNotes, PropertyResponse }
import com.agoda.content.models.db.bathInformation._
import com.agoda.content.models.db.checkInOut.CheckInOut
import com.agoda.content.models.db.common.Provider
import com.agoda.content.models.db.experiences.{ Experience, Experiences, Landmark }
import com.agoda.content.models.db.features.{ Feature, FeatureGroup, FeatureGroupType, FeatureGroupTypes }
import com.agoda.content.models.db.highlights.{ FavoriteFeatures, Highlight, Highlights }
import com.agoda.content.models.db.image.{ ImageLocalizations, ImageResponse, ImagesResponse, VideoResponse }
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.nonHotelAccommodation.{ ChildRoom, MasterRoom, NonHotelAccommodation }
import com.agoda.content.models.db.personalized.PersonalizedInformation
import com.agoda.content.models.db.policy.HotelPolicies
import com.agoda.content.models.db.positiveMentions.ReviewPositiveMentions
import com.agoda.content.models.db.rateCategories._
import com.agoda.content.models.db.reviews.{ ReviewFiltersAvailability, _ }
import com.agoda.content.models.db.synopsis.{ AggregatedFacility, Airport, AirportTransfer, Synopsis }
import com.agoda.content.models.db.transportationInformation.{
  Connection,
  Places,
  TransportationInformationResponse,
  Waypoint
}
import com.agoda.content.models.propertycontent.{ ContentGeoInfo, ContentLocations }
import org.joda.time.DateTime

import scala.collection.immutable.Vector

private[builders] trait DefaultContentDataExample extends BaseDataParameter {
  lazy val aValidRoomInformationResponse = RoomInformationResponse(aValidHotelId, roomsInformation = Vector())

  lazy val aValidMasterRoomResponse = MasterRoomResponse(
    metadata = aValidMasterRoomMeta,
    children = Vector(aValidChildRoomMeta),
    images = Vector(aValidImageResponse),
    facilities = Vector(aValidFeature),
    bedConfiguration = None,
    features = Vector(aValidFeatureResponse),
    facilityGroups = Vector(aValidFeatureGroup),
    isAllowChildren = Some(true),
    isYcsMaster = None,
    suitabilityType = None,
    filterTags = None,
    localizations = None,
    roomReviewInformation = None,
    suitabilityTypes = Seq.empty,
    topFacilities = None,
    rolledUpProvider = None,
    videos = Vector.empty
  )

  lazy val aValidYCSMasterRoomResponse = MasterRoomResponse(
    metadata = aValidYCSMasterRoomMeta,
    children = Vector(aValidChildRoomMeta),
    images = Vector(aValidImageResponse),
    facilities = Vector(aValidFeature),
    bedConfiguration = None,
    features = Vector(aValidFeatureResponse),
    facilityGroups = Vector(aValidFeatureGroup),
    isAllowChildren = Some(true),
    isYcsMaster = Some(true),
    suitabilityType = None,
    filterTags = None,
    localizations = None,
    roomReviewInformation = None,
    suitabilityTypes = Seq.empty,
    topFacilities = None,
    rolledUpProvider = None,
    videos = Vector.empty
  )

  lazy val aValidFeature = Feature(
    Some(1),
    Some("water"),
    None,
    None,
    Option("AAA"),
    Some(false),
    Some(true),
    Option("hotel, apartment, condo"),
    Some(1),
    Some(true),
    Option(Vector(aValidImageResponse)),
    None,
    None,
    Some(1)
  )

  lazy val aValidFeatureResponse =
    RoomFeatureResponse(name = "infantCotAvailable", text = "Baby cot available", symbol = "baby-cot")
  lazy val aValidImageLocalizations = ImageLocalizations(caption = None)

  lazy val aValidImageResponse = ImageResponse(
    1,
    "pic",
    2,
    Map("12312" -> "1123"),
    None,
    332,
    "all",
    None,
    None,
    1,
    List(ContentLocations("12312", "1123"))
  )

  lazy val aValidVideoResponse = VideoResponse(1, "1.mp4")

  lazy val aValidImagesResponse = ImagesResponse(
    propertyId = aValidHotelId,
    totalHotelImages = 1,
    mainImage = Some(aValidImageResponse),
    hotelImages = Vector(aValidImageResponse),
    cityImage = None,
    ugcImages = None,
    categories = None,
    videos = Some(Vector(aValidVideoResponse)),
    matterports = None,
    ugcMosaicImages = None
  )

  lazy val aValidFeatureGroup =
    FeatureGroup(Some(1), Some("feature"), Some(".ficon-feature"), Some(12), Some(Vector(aValidFeature)))
  lazy val aValidExperiences = Experiences(propertyId = aValidHotelId, experience = Some(Vector(aValidExperience)))

  lazy val aValidExperience = Experience(
    name = Some("great experience"),
    symbol = Some("fantastic symbol"),
    landmarks = Some(Vector(aValidLandmark))
  )

  lazy val aValidReviewScores = ReviewScores(
    propertyId = aValidHotelId,
    providers = Vector(aValidProvider),
    groups = Vector.empty,
    remarks = None,
    combinedReviewProviderIds = Vector.empty,
    combinedReviewScores = Vector.empty,
    roomScores = Vector.empty
  )

  lazy val aValidReviewGroupScore =
    ReviewGroupScore(id = 0, name = "solo", reviewCount = 100L, 100L, grades = Vector.empty, providerId = 332)

  lazy val aValidHotelInformationResponse = InformationResponse(
    aValidHotelId,
    None,
    Option(Vector()),
    Option(Notes(Option(Vector()), Option(Vector()), Option(Vector()), Option(Vector()))),
    Option(Vector()),
    None,
    Option(HotelPolicies(None, Option(Vector()), Option(Vector()), Option(Vector()), Option(Vector()), None)),
    Option(Vector()),
    None,
    None,
    Option(Vector.empty),
    None,
    None,
    None,
    Option(0),
    certificate = None,
    staffVaccinationInfo = None,
    characteristicTopics = None,
    sustainabilityInfo = None,
    nightStayInfo = None,
    companyTraceabilityInfo = None,
    companyTraceabilityData = None,
    dsaComplianceInfo = None
  )

  lazy val aValidContentPropertyResponse = PropertyResponse(
    propertyId = aValidHotelId,
    reviews = Some(aValidReviewResponse),
    images = Some(aValidImagesResponse),
    information = Some(aValidHotelInformationResponse),
    summary = Some(aValidInformationSummaryResponse),
    features = Some(aValidFeatureGroupTypes),
    experiences = Some(aValidExperiences),
    localInformation = Some(aValidLocalInformation),
    engagement = Some(aValidEngagement),
    rooms = Some(aValidRoomInformationResponse),
    highlights = Some(aValidHighlights),
    nonHotelAccommodation = Some(aValidNonHotelAccommodationResponse),
    synopsis = Some(aValidSynopsis),
    checkInOut = Some(aValidCheckInOut),
    uniqueSellingPoints = None,
    personalizedInformation = None,
    rateCategories = None,
    rateCategory = None,
    bathInformation = None,
    transportationInformation = None
  )

  lazy val aValidReviewResponse = ReviewResponse(
    commentary = Some(aValidReviewComments),
    demographics = Some(aValidReviewScores),
    summaries = Some(aValidReviewSnippets),
    cumulative = Some(aValidReviewTotals),
    filters = Some(aValidReviewFiltersAvailability),
    positiveMentions = Some(aValidReviewPositiveMentions),
    counters = None
  )

  lazy val aValidCheckInOut =
    CheckInOut(propertyId = aValidHotelId, title = "title", checkInOutTime = None, features = Vector.empty)

  lazy val aValidLocalInformation = LocalInformation(
    propertyId = aValidHotelId,
    nearbyProperties = Some(Vector.empty),
    nearbyPlaces = Some(Vector.empty),
    topPlaces = Some(Vector.empty),
    cuisines = Some(Vector.empty),
    transportation = Some(Transportation(airports = Some(Vector.empty)))
  )

  lazy val aValidLandmark = Landmark(
    name = Some("landmark"),
    geoInfo = Some(aValidContentGeoInfo),
    distance = Some(5),
    distanceUnit = Some("km"),
    scores = Some(aValidReviewTotal),
    images = Some(Vector(aValidImageResponse)),
    distanceInKm = Some(5)
  )

  lazy val aValidReviewComments = ReviewComments(
    propertyId = aValidHotelId,
    propertyCountryId = None,
    propertyCountryIso = None,
    counts = Vector.empty,
    providers = Vector.empty,
    comments = Vector.empty,
    topComments = Vector.empty
  )

  lazy val aValidReviewPositiveMentions = ReviewPositiveMentions(
    propertyId = aValidHotelId,
    categories = List.empty,
    facilityClasses = Vector.empty,
    reviewSentiments = Vector.empty,
    facilityClassesSentiment = Vector.empty,
    bcomReviewScores = None
  )

  lazy val aValidReviewCounters = ReviewCounters(propertyId = aValidHotelId, facilityCounters = Vector.empty)

  lazy val aValidReviewSnippets = ReviewSnippets(
    propertyId = aValidHotelId,
    snippets = Vector.empty,
    recommendationScores = Vector.empty,
    frequentTravellerRecommendationScore = None,
    snippetTopic = Vector.empty,
    snippetTopics = None,
    recommendedTopics = None,
    hotelReviewSummaryFromAi = None,
    reviewTags = None
  )

  lazy val aValidReviewTotals =
    ReviewTotals(propertyId = aValidHotelId, providers = Vector(aValidProvider), totals = Vector(aValidReviewTotal))

  lazy val aValidReviewFiltersAvailability = ReviewFiltersAvailability(
    propertyId = aValidHotelId,
    demographicId = 1,
    demographicName = "",
    languages = Vector.empty,
    roomTypes = Vector.empty
  )

  lazy val aValidReviewSnippet = ReviewSnippet(
    providerId = 332,
    reviewId = 1000L,
    snippet = "test snippet",
    date = new DateTime,
    sentimentScore = 0.0,
    reviewer = "papi",
    countryId = 1L,
    countryCode = "A1",
    countryName = "Test",
    snippetId = 11L
  )

  lazy val aValidInformationSummaryResponse = InformationSummaryResponse(
    propertyId = aValidHotelId,
    isPreferredPartner = true,
    awardYear = Some("2016"),
    localeName = "great locale",
    defaultName = "great default",
    displayName = "great display",
    singleRoomNonHotelAccommodation = false,
    hasHostExperience = false,
    accommodationType = "Hotel",
    isNonHotelAccommodationType = false,
    remarks = None,
    geoInfo = Some(aValidGeoInfo),
    address = None,
    highlightedFeatures = Vector(aValidFeature),
    rating = aValidStarRating,
    propertyType = "some type",
    sellingPoints = Vector.empty,
    generalRoomInformation = None,
    spokenLanguages = Vector.empty,
    awardsAndAccolades = AwardsAndAccolades("", None, None),
    chainId = 0,
    hasChannelManager = false,
    isHygienePlusOptOut = Some(false),
    nhaSummary = None,
    propertyStatus = None,
    requiredGuestContact = false,
    asqInfos = Vector.empty
  )

  lazy val aValidHighLight =
    Highlight(
      id = Some(1),
      name = Some(""),
      title = Some(""),
      symbol = Some(""),
      category = None,
      order = Some(1),
      topicName = Some("")
    )

  lazy val aValidHighlights = Highlights(
    propertyId = aValidHotelId,
    favoriteFeatures = Some(aValidFavoriteFeatures),
    benefits = None,
    locations = None,
    categories = Some(Vector.empty),
    locationHighlightMessage = None,
    locationHighlights = None
  )

  lazy val aValidSynopsis = Synopsis(
    propertyId = aValidHotelId,
    airportTransfer = aValidAirportTransfer,
    neighborhood = None,
    aggregatedFacilities = Vector.empty
  )

  lazy val aValidAggregatedFacility =
    AggregatedFacility(id = 10, facilityName = "", symbol = None, groupType = "", order = 1)

  lazy val aValidNonHotelAccommodationChildRoom = ChildRoom(1L)

  lazy val aValidNonHotelAccommodationMasterRoom =
    MasterRoom(1L, childrenRooms = Vector.empty, highlightedFacilities = Vector.empty)

  lazy val aValidNonHotelAccommodationResponse = NonHotelAccommodation(
    propertyId = aValidHotelId,
    noOfBedrooms = None,
    noOfBeds = None,
    facilities = Vector.empty,
    houseRules = None,
    masterRooms = Vector.empty
  )

  lazy val aValidReviewTotal = ReviewTotal(
    providerId = Some(332),
    reviewCount = Some(10),
    reviewCommentsCount = Some(10),
    demographic = Some(""),
    score = Some(2.5),
    scoreText = Some("text 2.5->1"),
    maxScore = Some(10),
    countText = None
  )

  lazy val aValidEngagement = PropertyEngagement(
    propertyId = aValidHotelId,
    peopleLooking = Some(""),
    Some(0),
    lastBooking = Some(""),
    todayBooking = Some(""),
    lastBookByRooms = Some(Map.empty),
    lastBookDateByRooms = Some(Map.empty)
  )

  lazy val aValidFeatureGroupType = FeatureGroupType(id = Some(1), name = Some(""), featureGroups = Some(Vector.empty))

  lazy val aValidFeatureGroupTypes = FeatureGroupTypes(
    propertyId = aValidHotelId,
    featureGroupTypes = Some(Vector.empty),
    hotelFacilities = Some(Vector.empty),
    summary = None,
    facilityHighlights = None,
    additionalInfos = None,
    luxuryFacilityHighlights = None
  )

  lazy val aValidProvider = Provider(id = 332, default = true, totalIndex = 1)

  lazy val aValidReviewCount = ReviewCount(providerId = 332, count = 10)

  lazy val aValidMasterRoomMeta =
    RoomMetadata(1000, None, Some("master"), Some("master localized"), Some(2), Some(4), false)

  lazy val aValidYCSMasterRoomMeta = RoomMetadata(
    1000,
    None,
    Some("master"),
    Some("master localized"),
    Some(2),
    Some(4),
    true,
    provider = Some(ProviderInformation(id = Some(332)))
  )

  private val aValidGeoInfo = GeoInfo(latitude = 0, longitude = 0, obfuscatedLat = 0, obfuscatedLong = 0)

  private val aValidContentGeoInfo = ContentGeoInfo(latitude = 0, longitude = 0, obfuscatedLat = 0, obfuscatedLong = 0)

  lazy val aValidChildRoomMeta =
    RoomMetadata(
      1001,
      Some(aValidMasterRoomMeta.roomId),
      Some("child"),
      Some("child localized"),
      Some(3),
      Some(4),
      false
    )

  private val aValidStarRating =
    StarRating(value = 4, symbol = "4-a", text = "some text", color = Color("blue", "#0000ff"))

  lazy val aValidAirport = Airport(abbr = None, distance = None, duration = None)

  lazy val aValidAirportTransfer = AirportTransfer(airports = Vector.empty, transferCost = None, icon = "")

  lazy val aValidFavoriteFeatures = FavoriteFeatures(title = Some("title"), features = Some(Vector.empty))

  lazy val aValidPersonalizedInformation = PersonalizedInformation(
    propertyId = aValidHotelId,
    childAge = None,
    familyFeature = None,
    familyRoomFeature = None,
    isChildStayFreeAllowedForFamily = None,
    isFamilyRoom = None,
    isMoreThanOneBedroom = None,
    childrenFreePolicy = None
  )

  lazy val aValidLocalizations =
    Map("en-us" -> "englishtranslation", "th-th" -> "ภาษาไทยแปล", "ru-ru" -> "русский перевод")

  lazy val aValidUsefulInfo =
    UsefulInfo(id = 1, name = "", nameLocalizations = None, description = None, symbol = None, propertyId = 1)

  lazy val aValidUsefulInfoGroup =
    UsefulInfoGroup(id = Some(1), name = Some(""), order = Some(1), usefulInfo = Some(Vector.empty))

  lazy val aValidCompanyTraceabilityAddress = CompanyTraceabilityAddressInfo(
    addressUnit = Some("addressUnit"),
    addressFloor = Some("addressFloor"),
    addressBuilding = Some("addressBuilding"),
    addressStreet1 = Some("addressStreet1"),
    addressStreet2 = Some("addressStreet2"),
    city = Some("city"),
    stateId = Some(2),
    stateName = Some("stateName"),
    countryId = Some(2),
    countryName = Some("countryName"),
    postalCode = Some("postalCode")
  )

  lazy val aValidCompanyTraceabilityInfo = CompanyTraceabilityInfo(
    tradingName = Some("trading name"),
    address = Some(aValidCompanyTraceabilityAddress),
    email = Some("email"),
    phoneNumber = Some("phone"),
    registrationNo = Some("registration no"),
    chainSpecificComplianceText = Some("chain specific text"),
    representativeName = Some("representative name")
  )

  lazy val aValidCompanyTraceabilityData = CompanyTraceabilityData(
    requiredLayoutType = Some("kr template"),
    traderInformationList = Some(List(aValidCompanyTraceabilityInfo))
  )

  lazy val aValidFilterTags = FilterTags(isLargeRoom = Some(true), viewType = None)

  lazy val aValidBedConfigurationResponse = BedConfigurationResponse(None, Some(aValidBedDetailResponse))

  lazy val aValidBedDetailResponse =
    BedDetailResponse("Sleeping arrangements", "bed-detail", Vector(aValidBedroomConfigurationResponse))

  lazy val aValidBedroomConfigurationResponse =
    BedroomConfigurationResponse("Common space", Vector(aValidBedOptionResponse))

  lazy val aValidBedOptionResponse = BedOptionResponse(Vector(aValidBedResponse))

  lazy val aValidBedResponse = BedResponse("Single", "2 single beds", Some("single-bed"), 2, None)

  lazy val aValidQueenBedConfigurationResponse = {
    val queenBedResponse             = BedResponse("Queen", "Queen Bed", Some("queen-bed"), 1, None)
    val bedOptionResponse            = BedOptionResponse(Vector(queenBedResponse))
    val bedroomConfigurationResponse = BedroomConfigurationResponse("Common space", Vector(bedOptionResponse))
    val bedDetailResponse =
      BedDetailResponse("Sleeping arrangements", "bed-detail", Vector(bedroomConfigurationResponse))
    BedConfigurationResponse(None, Some(bedDetailResponse))
  }

  lazy val aValidInformationLocalizations = InformationLocalizations(
    blockedNationalities = Some(aValidBlockNationalitiesLocalziations),
    notes = Some(aValidNotesLocalizations),
    description = None
  )

  lazy val aValidNotesLocalizations = NotesLocalizations(
    importantNotes = Option(
      Map("en-us" -> List("englishtranslation"), "th-th" -> List("ภาษาไทยแปล"), "ru-ru" -> List("русский перевод"))
    ),
    publicNotes = Option(
      Map("en-us" -> List("englishtranslation"), "th-th" -> List("ภาษาไทยแปล"), "ru-ru" -> List("русский перевод"))
    ),
    importantNotesList = Option(
      List(
        ImportantNotes("en-us", List("englishtranslation")),
        ImportantNotes("th-th", List("ภาษาไทยแปล")),
        ImportantNotes("ru-ru", List("русский перевод"))
      )
    ),
    publicNotesList = Option(
      List(
        ImportantNotes("en-us", List("englishtranslation")),
        ImportantNotes("th-th", List("ภาษาไทยแปล")),
        ImportantNotes("ru-ru", List("русский перевод"))
      )
    )
  )

  lazy val aValidBlockNationalitiesLocalziations = Map(
    "en-us" -> List(Country(id = Some(1), localeName = Some("englishtranslation"))),
    "th-th" -> List(Country(id = Some(2), localeName = Some("ภาษาไทยแปล"))),
    "ru-ru" -> List(Country(id = Some(3), localeName = Some("русский перевод")))
  )

  lazy val aValidInformationSummaryLocalizations = InformationSummaryLocalizations(
    localeName = Some(aValidLocalizations),
    accommodationType = Some(aValidLocalizations)
  )

  lazy val aValidRoomMetadataLocalization = RoomMetadataLocalization(aValidLocalizations)

  lazy val aValidMasterRoomResponseLocalization =
    MasterRoomResponseLocalization(Some(aValidRoomMetadataLocalization), None)
  lazy val aValidBedSummaryResponse = BedSummaryResponse("", "")

  lazy val aValidRoomProviderInformation    = ProviderInformation(Some(332), Some("AA1"))
  lazy val aValidJTBRoomProviderInformation = ProviderInformation(Some(332), Some("AA1"))

  lazy val aValidGeneralRoomInformationResponse =
    GeneralRoomInformationResponse(features = Vector(aValidFeatureResponse))

  lazy val aValidSingleRateCategoryResponse = RateCategoryResponse(
    rateCategoryId = 1,
    localizedRateCategoryName = Some("Rate Plan Name 1"),
    minNightsStay = Some(1),
    checkIn = Some(
      RateCategoryCheckTime(
        startTime = Some("13:00"),
        endTime = Some("23:59")
      )
    ),
    checkOut = Some(
      RateCategoryCheckTime(
        startTime = Some("13:00"),
        endTime = Some("12:00")
      )
    ),
    detail = Some(
      RateCategoryDetail(
        supplierPlanCode = None,
        content = None,
        paymentNotice = None,
        notice = None,
        images = None,
        cancellationPolicies = None,
        rateCategoryContentToken = "123,124,125"
      )
    ),
    questionsToBookers = Some("Any special request?"),
    acceptCustomerRequest = Some(true)
  )

  lazy val aValidSingleRateCategoryWithDetailResponse = RateCategoryResponse(
    rateCategoryId = 1,
    localizedRateCategoryName = Some("Rate Plan Name 1"),
    checkIn = Some(
      RateCategoryCheckTime(
        startTime = Some("13:00"),
        endTime = Some("23:59")
      )
    ),
    checkOut = Some(
      RateCategoryCheckTime(
        startTime = None,
        endTime = Some("12:00")
      )
    ),
    inventoryType = Some("HR2"),
    acceptCustomerRequest = Some(true),
    questionsToBookers = Some("questionsToBookers ?"),
    gender = Some("M"),
    detail = Some(aValidSingleRateCategoryDetail)
  )

  lazy val aValidSingleRateCategoryDetail = RateCategoryDetail(
    supplierPlanCode = Some("Plan Code[XXXXXX] Rate Code[XXX] Room Code[XXX] Reservation Code[XXX]"),
    content = Some("Plan contain detail"),
    paymentNotice = Some("Special notes on charges"),
    notice = Some("Plan notice information"),
    images = Some(List(aValidSingleRateCategoryImage)),
    cancellationPolicies = Some(aValidRateCategoryCancellationPolicy),
    childPolicies = Some(
      RateCategoryChildPolicies(
        acceptChild = Some(true),
        policies = List(
          RateCategoryChildPolicy(
            categoryId = 1,
            isRoomCapacityIncluded = true,
            priceDiscount = RateCategoryPolicyPrice(
              value = 80,
              unit = "FLT"
            ),
            minAge = Some(12),
            maxAge = Some(12),
            description = Some("Description 1")
          ),
          RateCategoryChildPolicy(
            categoryId = 2,
            isRoomCapacityIncluded = false,
            priceDiscount = RateCategoryPolicyPrice(
              code = Some("JYP"),
              value = 60,
              unit = "DAP"
            ),
            minAge = Some(12),
            maxAge = Some(12),
            description = Some("Description 2")
          ),
          RateCategoryChildPolicy(
            categoryId = 3,
            isRoomCapacityIncluded = false,
            priceDiscount = RateCategoryPolicyPrice(
              value = 100,
              unit = "PAP"
            ),
            minAge = Some(12),
            maxAge = Some(12),
            description = Some("Description 3")
          )
        )
      )
    ),
    roomDescriptions = None,
    inclusions = Some("Some inclusions"),
    exclusions = Some("Some exclusions"),
    termsAndConditions = Some("Some terms and conditions")
  )

  lazy val aValidSingleRateCategoryImage = RateCategoryImage(
    id = 1,
    caption = "Free Text Caption",
    locations = Map("main" -> "//pix6.agoda.net/hotelImages/106/10633/10633_14020610070018261941.jpg?s=300x300&ar=1x1")
  )

  lazy val aValidRateCategoryCancellationPolicy = RateCategoryCancellationPolicy(
    feeSettingTypeId = Some(1),
    amendmentPolicyRules = Some(
      RateCategoryAmendmentPolicyRules(
        stayLengthRuleId = Some(1),
        guestSizeRuleId = Some(2)
      )
    ),
    chargeRanges = List("15D", "10D", "7D", "3D", "1D", "0D", "NS"),
    chargeRates = List(aValidRateCategoryCancellationChargeRate, aValidRateCategoryCancellationChargeRate2)
  )

  lazy val aValidRateCategoryCancellationChargeRate = RateCategoryCancellationChargeRate(
    minGuest = Some(1),
    maxGuest = Some(14),
    charges = List(
      RateCategoryPolicyPrice(Some("15D"), 20, "P"),
      RateCategoryPolicyPrice(Some("10D"), 30, "P"),
      RateCategoryPolicyPrice(Some("7D"), 30, "P"),
      RateCategoryPolicyPrice(Some("3D"), 30, "P"),
      RateCategoryPolicyPrice(Some("1D"), 50, "P"),
      RateCategoryPolicyPrice(Some("0D"), 100, "P"),
      RateCategoryPolicyPrice(Some("NS"), 100, "P")
    )
  )

  lazy val aValidRateCategoryCancellationChargeRate2 = RateCategoryCancellationChargeRate(
    minGuest = Some(15),
    maxGuest = None,
    charges = List(
      RateCategoryPolicyPrice(Some("15D"), 0, "P"),
      RateCategoryPolicyPrice(Some("10D"), 20, "P"),
      RateCategoryPolicyPrice(Some("7D"), 30, "P"),
      RateCategoryPolicyPrice(Some("3D"), 30, "P"),
      RateCategoryPolicyPrice(Some("1D"), 50, "P"),
      RateCategoryPolicyPrice(Some("0D"), 100, "P"),
      RateCategoryPolicyPrice(Some("NS"), 100, "P")
    )
  )

  lazy val aValidRateCategoriesResponse: RateCategoriesResponse = RateCategoriesResponse(
    propertyId = 10001,
    rateCategories = Vector(
      aValidSingleRateCategoryResponse,
      RateCategoryResponse(
        2,
        Some("Localized Rate Plan Name")
      ),
      RateCategoryResponse(
        3,
        None
      )
    ),
    escapeRateCategories = Vector(
      aValidSingleRateCategoryResponse,
      RateCategoryResponse(
        2,
        Some("Localized Rate Plan Name")
      ),
      RateCategoryResponse(
        3,
        None
      )
    )
  )

  lazy val aValidRateCategoryResponse: RateCategoriesResponse = RateCategoriesResponse(
    propertyId = 10001,
    rateCategories = Vector(
      aValidSingleRateCategoryWithDetailResponse
    ),
    escapeRateCategories = Vector(
      aValidSingleRateCategoryWithDetailResponse
    )
  )

  lazy val aValidRateCategoryDetailResponse = RateCategoriesResponse(
    propertyId = 1,
    rateCategories = Vector(aValidSingleRateCategoryResponse),
    escapeRateCategories = Vector(aValidSingleRateCategoryResponse)
  )

  lazy val aValidBathInformationResponse = BathInformationResponse(
    propertyId = 10001,
    bathInformation = Some(
      BathInformation(
        description = Some("This is test bath description"),
        bathTypes = List(
          BathFacility(
            facilityId = 1,
            facilityName = "Test Facility 1"
          ),
          BathFacility(
            facilityId = 2,
            facilityName = "Test Facility 2"
          )
        ),
        bathWaterTypes = List(
          BathFacility(
            facilityId = 3,
            facilityName = "Test Facility 3"
          ),
          BathFacility(
            facilityId = 4,
            facilityName = "Test Facility 4"
          )
        ),
        bathWaterEfficacy = List(
          BathFacility(
            facilityId = 5,
            facilityName = "Test Facility 5"
          ),
          BathFacility(
            facilityId = 6,
            facilityName = "Test Facility 6"
          )
        ),
        others = List(
          BathOther(
            bathAvailableCode = "family-bath",
            numberOfBaths = Some(10),
            numberOfOutdoorBaths = Some(8),
            reservationRequired = Some(true)
          )
        ),
        indoorBaths = List(
          BathInOutDoor(
            bathName = "Indoor Bath",
            genderAllowed = Some("M"),
            capacity = Some(10),
            numberOfBathtubs = Some(5),
            bathtubMaterial = Some("Marble")
          )
        ),
        outdoorBaths = List(
          BathInOutDoor(
            bathName = "Outdoor Bath",
            genderAllowed = Some("B"),
            capacity = Some(30),
            isShared = Some(true),
            numberOfBathtubs = Some(5),
            bathtubMaterial = Some("Marble"),
            walkingDuration = Some(555),
            views = Some(
              List("Mountain", "Lake")
            ),
            usagePeriod = Some(
              BathUsagePeriod(
                periodFrom = Some("01/01"),
                periodUntil = Some("12/31")
              )
            )
          )
        )
      )
    )
  )

  lazy val aValidTransportationInformationResponse = TransportationInformationResponse(
    propertyId = 10001,
    waypoints = Vector(
      Waypoint(
        typeName = "airport",
        name = None,
        places = Vector(
          Places(
            name = "HND 羽田",
            duration = None,
            destinationName = None,
            dropOffStation = None,
            propertyConnections = Vector(
              Connection(
                typeName = "taxi",
                name = None,
                duration = 1782
              ),
              Connection(
                typeName = "bus",
                name = None,
                duration = 1782
              )
            )
          )
        )
      )
    ),
    isPickUpServiceAvailable = true,
    isPickUpServiceRequiredPriorContact = Some("Yes")
  )

}
