package util.builders.contentbuilder

import com.agoda.content.models.db.information._
import com.agoda.content.models.db.policy.HotelPolicies
import types.HotelIdType
import util.builders.{ Builder, DefaultTestDataExample }

class HotelInformationResponseBuilder(information: InformationResponse)
    extends Builder[InformationResponse](information) {
  import HotelInformationBuilder._
  def withHotelId(hotelId: HotelIdType): self      = information.copy(propertyId = hotelId)
  def withHotelPolicy(policy: HotelPolicies): self = information.copy(policies = Option(policy))
  def withBlockNationalities(blockedNationalities: Vector[Country]): self =
    information.copy(blockedNationalities = Option(blockedNationalities))
  def withUsefulInfoGroup(usefulInfoGroups: Vector[UsefulInfoGroup]): self =
    information.copy(usefulInfoGroups = Option(usefulInfoGroups))
  def withLocalizations(localizations: Option[InformationLocalizations]): self =
    information.copy(localizations = localizations)
  def withNumberOfRooms(noOfRooms: String): self = information.copy(numberOfRoom = Some(noOfRooms))
  def withCompanyTraceability(info: CompanyTraceabilityInfo): self =
    information.copy(companyTraceabilityInfo = Some(info))
  def withCompanyTraceabilityData(info: CompanyTraceabilityData): self =
    information.copy(companyTraceabilityData = Some(info))
  def withArrivalGuide(arrivalGuide: Option[ArrivalGuide]): self = information.copy(arrivalGuide = arrivalGuide)
}

object HotelInformationBuilder {
  type self = HotelInformationResponseBuilder
  def newBuilder(): self = new HotelInformationResponseBuilder(DefaultTestDataExample.aValidHotelInformationResponse)
}
