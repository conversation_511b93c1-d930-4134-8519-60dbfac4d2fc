package util.builders.requestbuilder

import _root_.request.{ PropertyRequest => PAPIPropertyRequest, _ }
import com.agoda.content.models._
import com.agoda.content.models.request.sf.{ ContentEscapeRateCategoriesRequest, ContentRateCategoriesRequest }
import models.starfruit.{ BookingRequest, RocketmilesPublishPriceRequest }
import types.HotelIdType
import util.builders.{ <PERSON><PERSON>er, DefaultTestDataExample }

class PropertyRequestBuilder(req: PAPIPropertyRequest) extends Builder[PAPIPropertyRequest](req) {
  type self = PropertyRequestBuilder

  def withContext(ctx: PropertyContext): self = req.copy(context = ctx)

  def withHotelIds(hotelIds: List[HotelIdType]): self = {
    val ctx = req.context.copy(propertyIds = hotelIds)
    req.copy(context = ctx)
  }

  def withMemberId(memberId: Long): self = req.copy(context = aValidContext.copy(memberId = memberId))

  def withPricing(pricingRequest: PropertyPricing = DefaultTestDataExample.aValidPricingRequest): self =
    req.copy(pricing = Some(pricingRequest))

  def withoutPricing(): self =
    req.copy(pricing = None)

  def withEngagement(engagementRequest: EmptyRequest = EmptyRequest()): self =
    req.copy(engagementRequest = Some(engagementRequest))

  def withImage(imageRequest: ImagesRequest = DefaultTestDataExample.aValidImageRequest): self =
    req.copy(images = Some(imageRequest))

  def withFeature(featureRequest: FeaturesRequest = DefaultTestDataExample.aValidFeatureRequest): self =
    req.copy(features = Some(featureRequest))

  def withInformation(informationRequest: InformationRequest = DefaultTestDataExample.aValidInformationRequest): self =
    req.copy(information = Some(informationRequest))

  def withReviewDemographic(
      reviewDemographicRequest: DemographicsRequest = DefaultTestDataExample.aValidReviewDemoGraphicRequest
  ): self =
    req.copy(reviewDemographics = Some(reviewDemographicRequest))

  def withReviewCumulative(
      reviewCumulativeRequest: CumulativeRequest = DefaultTestDataExample.aValidReviewCumulativeRequest
  ): self =
    req.copy(reviewCumulative = Some(reviewCumulativeRequest))

  def withReviewSnippet(
      reviewSnippetRequest: SummariesRequest = DefaultTestDataExample.aValidReviewSnippetRequest
  ): self =
    req.copy(reviewSnippet = Some(reviewSnippetRequest))

  def withReviewCommentary(
      reviewCommentaryRequest: CommentaryRequest = DefaultTestDataExample.aValidReviewCommentaryRequest
  ): self =
    req.copy(reviewCommentary = Some(reviewCommentaryRequest))

  def withRoomRequest(roomRequest: RoomRequestPAPI = DefaultTestDataExample.aValidRoomRequest): self =
    req.copy(room = Some(roomRequest))

  def withContentOnlyReqquest(roomRequest: RoomRequestPAPI = DefaultTestDataExample.aValidRoomRequest): self =
    req.copy(room = Some(roomRequest))

  def withRecommendation(
      numberOfRecommendation: Int = 4,
      overrideList: List[Long] = List.empty,
      calculateCheaperThanSimilar: Option[Boolean] = Option(false)
  ): self =
    req.copy(recommendation =
      Some(
        Recommendation(
          numberOfRecommendations = numberOfRecommendation,
          overrideList = overrideList,
          calculateCheaperThanSimilar = calculateCheaperThanSimilar
        )
      )
    )

  def withPersonalizedRecommendation(numberOfRecommendation: Int = 4, overrideList: List[Long] = List.empty): self =
    req.copy(personalizedRecommendation =
      Some(Recommendation(numberOfRecommendations = numberOfRecommendation, overrideList = overrideList))
    )

  def withPastBookingsRecommendation(numberOfRecommendation: Int = 4, overrideList: List[Long] = List.empty): self =
    req.copy(pastBookingsRecommendation =
      Some(Recommendation(numberOfRecommendations = numberOfRecommendation, overrideList = overrideList))
    )

  def withContentSummary(summaryRequest: SummaryRequest = DefaultTestDataExample.aValidSummaryRequest): self =
    req.copy(summaryRequest = Some(summaryRequest))

  def withLocalInformation(
      localInformationRequest: LocalInformationRequest = DefaultTestDataExample.aValidLocalInformationRequest
  ): self =
    req.copy(localInformation = Some(localInformationRequest))

  def withContentCheckInOut(): self =
    req.copy(checkInOut = Some(EmptyRequest()))

  def withSynopsis(): self =
    req.copy(synopsis = Some(EmptyRequest()))

  def withEngagement(): self =
    req.copy(engagementRequest = Some(EmptyRequest()))

  def withExperience(experienceRequest: ExperienceRequest = DefaultTestDataExample.aValidExperienceRequest): self =
    req.copy(experienceRequest = Some(experienceRequest))

  def withContentFilter(filterRequest: FilterRequest = DefaultTestDataExample.aValidFilterRequest): self =
    req.copy(filterRequest = Some(filterRequest))

  def withHilight(hilightRequest: HighlightsRequest = DefaultTestDataExample.aValidHilightRequest): self =
    req.copy(highlights = Some(hilightRequest))

  def withBooking(bookingRequest: BookingRequest = DefaultTestDataExample.aValidBookingRequest): self =
    req.copy(booking = Some(bookingRequest))

  def withNonHotelAccomodation(): self =
    req.copy(nonHotelAccommodation = Some(EmptyRequest()))

  def withLocalization(): self =
    req.copy(localization = Some(EmptyRequest()))

  def withFeatureFlag(featureFlagRequest: FeatureFlagRequest = DefaultTestDataExample.aValidFeatureFlagRequest): self =
    req.copy(featureFlag = Some(featureFlagRequest))

  def withSearchType(searchType: SearchType): self =
    req.copy(searchType = Some(searchType))

  def withIsSSR(isSSRFlag: Boolean): self =
    req.copy(isSSR = Some(isSSRFlag))

  def withSearchCriteria(searchCriteria: Option[SearchCriteria]): self =
    req.copy(searchCriteria = searchCriteria)

  def withTopSellingPoint(topSellingPointRequest: Option[TopSellingPointRequest]): self =
    req.copy(topSellingPointRequest = topSellingPointRequest)

  def withOrigin(origin: String): self = {
    val ctx = req.context.copy(origin = origin)
    req.copy(context = ctx)
  }

  def withSoldOutRequest(soldOutAlternateDatesRequest: Option[SoldOutAlternateDatesRequest]): self =
    req.copy(soldOutAlternateDatesRequest = soldOutAlternateDatesRequest)

  def withRateCategories(): self =
    req.copy(rateCategories = Some(ContentRateCategoriesRequest(None, None)))
  def withEscapeRateCategories(): self =
    req.copy(rateCategories = Some(ContentRateCategoriesRequest(None, Some(ContentEscapeRateCategoriesRequest(None)))))

  def withRateCategoryRequest(
      rateCategoryIds: RateCategoryIds,
      detail: Option[RateCategoryDetailRequest] = None,
      rateCategoryIdWithRooms: Option[List[RateCategoryIdWithRoom]] = None,
      shouldAutoPopulateIds: Option[Boolean] = None
  ): self =
    req.copy(rateCategory =
      Some(PAPIRateCategoryRequest(rateCategoryIds, detail, rateCategoryIdWithRooms, shouldAutoPopulateIds))
    )

  def withBathInformation(): self =
    req.copy(bathInformation = Some(EmptyRequest()))

  def withTransportationInformation(): self =
    req.copy(transportationInformation = Some(EmptyRequest()))

  def withRocketmilesPublishPriceRequest(rocketmilesPublishPriceRequest: Option[RocketmilesPublishPriceRequest]): self =
    req.copy(rocketmilesPublishPriceRequest = rocketmilesPublishPriceRequest)

  def withPartnerClaimToken(partnerClaimToken: Option[String]): self =
    req.withContext(req.context.copy(partnerClaimToken = partnerClaimToken))

  def withCapiToken(capiToken: Option[String]): self = req.withContext(req.context.copy(capiToken = capiToken))

  def withWhiteLabelKey(whiteLabelKey: Option[String]): self =
    req.withContext(req.context.withWhiteLabelKey(whiteLabelKey))

  def withAdditionalExperimentForce(experimentName: String, variant: String): self = {
    val newContext = req.context.withAdditionalExperiments(experimentName, variant)
    req.withContext(newContext)
  }

}

object PropertyRequestBuilder {
  def newBuilder(): PropertyRequestBuilder =
    new PropertyRequestBuilder(PAPIPropertyRequest(context = DefaultTestDataExample.aValidContext))
}
