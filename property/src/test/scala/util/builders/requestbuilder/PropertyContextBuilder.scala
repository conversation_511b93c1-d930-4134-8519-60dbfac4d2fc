package util.builders.requestbuilder

import com.agoda.commons.traffic.TrafficInfo
import com.agoda.core.search.models.enumeration.SearchType
import com.agoda.core.search.models.request.PollingInfoRequest
import com.agoda.upi.models.request.CartBaseRequest
import models.starfruit.ABTest
import request.{ Bot, Experiments, ForcedExperiment, PropertyContext }
import types.{ CidType, HotelIdType }
import util.builders.{ Builder, DefaultTestDataExample }

class PropertyContextBuilder(req: PropertyContext) extends Builder[PropertyContext](req) {
  type self = PropertyContextBuilder

  def withDeviceTypeId(deviceTypeId: Int): self               = req.copy(deviceTypeId = Some(deviceTypeId))
  def withUserId(userId: Option[String]): self                = req.copy(userId = userId)
  def withShowCMS(showCMS: Option[Boolean]): self             = req.copy(showCMS = showCMS)
  def withOrigin(origin: String): self                        = req.copy(origin = origin)
  def withLocale(locale: String): self                        = req.copy(locale = locale)
  def withIsAPO(isAPO: Option[Boolean]): self                 = req.copy(isAPO = isAPO)
  def withTrafficInfo(trafficInfo: Option[TrafficInfo]): self = req.copy(trafficInfo = trafficInfo)
  def withSearchId(searchId: String): self                    = req.copy(searchId = searchId)
  def withHotelIds(propertyIds: List[HotelIdType]): self      = req.copy(propertyIds = propertyIds)
  def withIsMSE(isMSE: Option[Boolean]): self                 = req.copy(isMSE = isMSE)
  def withMemberId(memberId: Long): self                      = req.copy(memberId = memberId)
  def withIsAllowBookOnRequest(isAllowBookOnRequest: Option[Boolean]): self =
    req.copy(isAllowBookOnRequest = isAllowBookOnRequest)
  def withABTest(abTest: Option[List[ABTest]]): self        = req.copy(abTest = abTest)
  def withExperiment(experiment: Option[Experiments]): self = req.copy(experiments = experiment)

  def withAdditionalExperiments(experimentName: String, variant: String): self = {
    val existingExperimentsOpt = req.experiments
    val newForceExperiment     = ForcedExperiment(experimentName, variant)

    val newExperiments = existingExperimentsOpt match {
      case Some(existingExperiments) =>
        existingExperiments.copy(
          forceByExperiment = existingExperiments.forceByExperiment
            .map(_ :+ newForceExperiment)
            .orElse(Some(Vector(newForceExperiment)))
        )
      case None =>
        Experiments(None, Some(Vector(newForceExperiment)))
    }

    req.withExperiment(Some(newExperiments))
  }

  def withCID(cid: CidType): self                                    = req.copy(cid = cid)
  def withPlatform(platform: Int): self                              = req.copy(platform = platform)
  def withBot(bot: Option[Bot]): self                                = req.copy(bot = bot)
  def withUserAgent(userAgent: Option[String]): self                 = req.copy(userAgent = userAgent)
  def withLocalizations(localizations: Option[List[String]]): self   = req.copy(localizations = localizations)
  def withIpAddress(ip: Option[String]): self                        = req.copy(ipAddress = ip)
  def withStoreFrontId(storeFrontId: Option[Int]): self              = req.copy(storeFrontId = storeFrontId)
  def withPropertyIds(pIds: List[Long]): self                        = req.copy(propertyIds = pIds)
  def withSearchType(searchType: Option[SearchType]): self           = req.copy(searchType = searchType)
  def withWhiteLabelKey(whiteLabelKey: Option[String]): self         = req.copy(whiteLabelKey = whiteLabelKey)
  def withRawUserContext(rawUserContext: Option[String]): self       = req.copy(rawUserContext = rawUserContext)
  def withPollingInfo(pollingInfo: Option[PollingInfoRequest]): self = req.copy(pollingInfo = pollingInfo)
  def withCartRequest(cartRequest: Option[CartBaseRequest]): self    = req.copy(cartRequest = cartRequest)
  def withExternalPartnerId(externalPartnerId: Option[String]): self = req.copy(externalPartnerId = externalPartnerId)
}

object PropertyContextBuilder {
  def newBuilder(): PropertyContextBuilder =
    new PropertyContextBuilder(DefaultTestDataExample.aValidContext)
}
