package util.builders.requestbuilder

import metaapi.PriceTrendByPropertyRequest
import models.starfruit.SuggestedPrice
import org.joda.time.DateTime
import request.PropertyContext
import types.{ CityIdType, LengthOfStay }
import util.builders.{ Builder, DefaultTestDataExample }

class PriceTrendByPropertyRequestBuilder(req: PriceTrendByPropertyRequest)
    extends Builder[PriceTrendByPropertyRequest](req) {
  import PriceTrendByPropertyRequestBuilder._

  def withContext(context: PropertyContext): self                    = req.copy(context = context)
  def withCityId(cityId: CityIdType): self                           = req.copy(cityId = cityId)
  def withLos(los: LengthOfStay): self                               = req.copy(los = los)
  def withOccupancy(occupancy: Int): self                            = req.copy(occupancy = occupancy)
  def withStartDate(startDate: DateTime): self                       = req.copy(startDate = startDate)
  def withEndDate(endDate: DateTime): self                           = req.copy(endDate = endDate)
  def withSupportedChannel(supportedChannel: List[Int]): self        = req.copy(supportedChannel = supportedChannel)
  def withIsUserLoggedIn(isUserLoggedIn: Boolean): self              = req.copy(isUserLoggedIn = isUserLoggedIn)
  def withSuggestedPrice(suggestedPrice: Int): self                  = req.copy(suggestedPrice = suggestedPrice)
  def withCurrency(currency: String): self                           = req.copy(currency = currency)
  def withRequiredPrice(requiredPrice: Option[SuggestedPrice]): self = req.copy(requiredPrice = requiredPrice)
}

object PriceTrendByPropertyRequestBuilder {
  type self = PriceTrendByPropertyRequestBuilder

  def newBuilder(): PriceTrendByPropertyRequestBuilder = new PriceTrendByPropertyRequestBuilder(
    DefaultTestDataExample.aValidPriceTrendByPropertyRequest
  )

}
