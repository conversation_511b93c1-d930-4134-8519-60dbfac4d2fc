package com.agoda.papi.search.property.graphql.resolver

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.tracing.api.WithTracer
import com.agoda.content.models.propertycontent.ContentInformationSummary
import com.agoda.content.models.request.sf.ContentImagesRequest
import com.agoda.core.search.models.request.SearchContext
import com.agoda.papi.content.graphql._
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.graphql.resolver.ResolverUtil._
import com.agoda.papi.search.common.model.{ Components, ReportingDataBuilder }
import com.agoda.papi.search.common.util.context.{ CmsExperimentContext, GlobalContextService }
import com.agoda.papi.search.common.util.hotelshutdown.HotelShutdownUtils
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.common.util.measurement
import com.agoda.papi.search.common.util.measurement.{
  HotelShutdownMetrics,
  MeasurementName,
  MeasurementTag,
  MetricsHelper
}
import com.agoda.papi.search.internalmodel.promocode.BannerCampaignGroupRequest
import com.agoda.papi.search.internalmodel.response.HostProfileResponse
import com.agoda.papi.search.internalmodel._
import com.agoda.papi.search.internalmodel.growthprogram.GrowthProgramInfoRequestEnvelope
import com.agoda.papi.search.property.service.datafetcher.PropertyDetailsDataFetcher
import com.agoda.papi.search.types.PropertyId
import com.agoda.styx.botprofile.{ BotProfileParser, BotTypes, TrafficType }
import com.agoda.whitelabel.client.settings.Constants.WhiteLabelId
import io.opentelemetry.api.common.AttributeKey
import io.opentracing.Tracer
import sangria.execution.deferred.Deferred

import scala.concurrent.{ ExecutionContext, Future }

class PropertyDetailResolverLevel1(
    val tracer: Tracer,
    hotelShutdownUtils: HotelShutdownUtils,
    globalContextService: GlobalContextService,
    searchExperimentManagerService: SearchExperimentManagerService,
    propertyDetailsDataFetcher: PropertyDetailsDataFetcher,
    reporter: MetricsReporter
) extends WithTracer with MetricsHelper with PAPIAppLogger {

  override val metricsReporter: MetricsReporter = reporter

  def setSearchContextF(ctx: GraphQLContext)(implicit ec: ExecutionContext): Future[Unit] = {
    val searchContext = SearchContext(
      memberId = 0L,
      locale = "",
      cid = 0,
      origin = "",
      platform = 0
    )
    val newContext = globalContextService.updateSearchContext(searchContext, ctx.globalContext, ctx.piiContext)
    val experimentContextF = searchExperimentManagerService.createExperimentContext(
      newContext,
      ctx.globalContext,
      ctx.requestContext.experiments
    )
    experimentContextF.map { experimentContext =>
      val cmsExperimentContext = CmsExperimentContext(
        experimentContext,
        ctx.globalContext.getOrigin,
        ctx.globalContext.getWhiteLabelId,
        ctx.globalContext.isDebug
      )
      val packagingContext = None
      val searchContextHolder = ctx.getSearchContext.copy(
        experimentContext = experimentContext,
        cmsExperimentContext = cmsExperimentContext,
        globalContext = ctx.globalContext,
        packagingContext = packagingContext,
        commonContextTags = ctx.operationTags
      )
      ctx.setSearchContext(searchContextHolder)
    }
  }

  // scalastyle:off
  def propertyDetailResolveLevel1(
      deferred: Vector[Deferred[Any]],
      ctx: GraphQLContext,
      queryState: Any
  )(implicit ec: ExecutionContext): PartialFunction[Deferred[Any], Future[Any]] = {

    implicit val ctxHolder = ctx.getSearchContext

    val propertyIds = deferred
      .flatMap {
        case d: PropertyDeferred[Any] =>
          if (!d.isInstanceOf[RoomSummaryDeferred]) {
            ctx.reportingDataBuilder.update(ReportingDataBuilder.PropertyDetailsSearchType)
          }
          Some(d.propertyId)
        case _ => None
      }
      .toList
      .distinct

    val propertyDetailsRequest0: Option[List[PropertyId]] = deferred.collectFirst { case d: PropertyDetailsDeferred =>
      d.propertyDetails.propertyIds.map { propertyId =>
        propertyId
      }
    }

    val contentImagesRequestO: Option[Wrapper[ContentImagesRequest]] = deferred.collectFirst {
      case d: ContentImagesDeferred => d.contentImagesRequestWrapper
    }

    val contentReviewScoreRequestO: Option[ContentGroupReviewScoreRequestEnvelope] =
      deferred.collectFirst { case d: ContentReviewScoreDeferred =>
        ContentGroupReviewScoreRequestEnvelope(propertyIds, d.contentReviewScoreRequestWrapper)
      }

    val contentReviewSummariesRequestO: Option[ContentGroupReviewSummariesRequestEnvelope] =
      deferred.collectFirst { case d: ContentReviewSummariesDeferred =>
        ContentGroupReviewSummariesRequestEnvelope(propertyIds, d.contentReviewSummariesRequestWrapper)
      }

    val hostProfileDeferredO: Vector[HostProfileDeferred] = deferred.collect { case differ: HostProfileDeferred =>
      differ
    }

    val contentInformationSummaryRequestO: Option[ContentInformationSummaryGroupRequestEnvelope] =
      deferred
        .collectFirst { case d: ContentInformationSummaryDeferred =>
          ContentInformationSummaryGroupRequestEnvelope(propertyIds, d.contentInformationSummaryRequestWrapper)
        }
        .orElse {

          /** CAPI call need to check is Single Room from Supplier summary */
          if (hostProfileDeferredO.nonEmpty) {
            Some(ContentInformationSummaryGroupRequestEnvelope(propertyIds, None))
          } else {
            None
          }
        }

    val contentInformationRequestO: Option[ContentInformationGroupRequestEnvelope] =
      deferred.collectFirst { case d: ContentInformationDeferred =>
        ContentInformationGroupRequestEnvelope(propertyIds, d.contentInformationRequestWrapper)
      }

    val contentLocalInformationRequestO: Option[ContentGroupLocalInformationRequestEnvelope] =
      deferred.collectFirst { case d: ContentLocalInformationDeferred =>
        ContentGroupLocalInformationRequestEnvelope(propertyIds, d.contentLocalInformationRequestWrapper)
      }

    val contentGroupEngagementRequestO: Option[ContentGroupEngagementRequestEnvelope] =
      deferred.collectFirst { case d: ContentEngagementDeferred =>
        ContentGroupEngagementRequestEnvelope(propertyIds)
      }

    val contentExperiencesRequestO: Option[ContentGroupExperienceRequestEnvelope] =
      deferred.collectFirst { case d: ContentExperienceDeferred =>
        ContentGroupExperienceRequestEnvelope(propertyIds, d.contentExperienceWrapper)
      }

    val contentFeaturesRequestO: Option[ContentGroupFeaturesRequestEnvelope] =
      deferred.collectFirst { case d: ContentFeaturesDeferred =>
        ContentGroupFeaturesRequestEnvelope(propertyIds, d.contentFeaturesRequestWrapper)
      }

    val contentHighlightsRequestO: Option[ContentGroupHighlightsRequestEnvelope] =
      deferred.collectFirst { case d: ContentHighlightsDeferred =>
        ContentGroupHighlightsRequestEnvelope(propertyIds, d.contentHighlightsRequestWrapper)
      }

    val contentQnaRequestO: Option[ContentGroupQnaRequestEnvelope] =
      deferred.collectFirst { case d: ContentQnaDeferred =>
        ContentGroupQnaRequestEnvelope(propertyIds, d.contentQnaRequestWrapper)
      }

    val contentTopicsRequestO: Option[ContentGroupTopicsRequestEnvelope] =
      deferred.collectFirst { case d: ContentTopicsDeferred =>
        ContentGroupTopicsRequestEnvelope(propertyIds, d.contentTopicsRequestWrapper)
      }

    val bannerCampaignRequestO =
      deferred.collectFirst { case d: PropertyBannerCampaignInfoDeferred =>
        BannerCampaignGroupRequest(propertyIds, d.bannerCampaignRequest)
      }

    val contentRateCategoryRequest0 =
      deferred.collectFirst { case d: ContentRateCategoryDeferred =>
        ContentGroupRateCategoryRequestEnvelope(propertyIds, d.contentRateCategoryRequestWrapper)
      }

    val growthProgramInfoRequest0 =
      deferred.collectFirst { case d: GrowthProgramInfoDeferred =>
        GrowthProgramInfoRequestEnvelope(propertyIds, d.growthProgramInfoRequest)
      }

    /** ContentImages * */
    val contentImagesResponseF = optionToFuture(contentImagesRequestO.map { contentImagesRequest =>
      val contentGroupImagesRequest = ContentGroupImagesRequestEnvelope(propertyIds, contentImagesRequest)
      propertyDetailsDataFetcher.fetchContentImagesResponse(
        contentGroupImagesRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    /** ContentReviewScores * */
    val contentReviewScoreResponseF = optionToFuture(contentReviewScoreRequestO.map { contentGroupReviewScoreRequest =>
      propertyDetailsDataFetcher.fetchContentReviewScoreResponse(
        contentGroupReviewScoreRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    /** ContentReviewSummaries * */
    val contentReviewSummariesResponseF = optionToFuture(
      contentReviewSummariesRequestO.map { contentGroupReviewSummariesRequest =>
        propertyDetailsDataFetcher.fetchContentReviewSummariesResponse(
          contentGroupReviewSummariesRequest,
          ctx.globalContext,
          ctx.getSearchContext.experimentContext
        )
      }
    )

    /** ContentInformationSummary * */
    val contentInformationSummaryResponseF = optionToFuture(
      contentInformationSummaryRequestO.map { contentInformationSummaryGroupRequest =>
        propertyDetailsDataFetcher.fetchContentInformationSummaryResponse(
          contentInformationSummaryGroupRequest,
          ctx.globalContext,
          ctx.getSearchContext.experimentContext
        )
      }
    )

    /** ContentInformation * */
    val contentInformationResponseF = optionToFuture(contentInformationRequestO.map { contentInformationRequest =>
      propertyDetailsDataFetcher.fetchContentInformationResponse(
        contentInformationRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    /** ContentLocalInformation * */
    val localInformationF = optionToFuture(contentLocalInformationRequestO.map { contentLocalInformationRequest =>
      propertyDetailsDataFetcher.fetchLocalInformationResponse(
        contentLocalInformationRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    /** ContentEngagement * */
    val contentEngagementF = optionToFuture(contentGroupEngagementRequestO.map { contentGroupEngagementRequest =>
      propertyDetailsDataFetcher.fetchEngagementResponse(
        contentGroupEngagementRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    /** ContentExperiences */
    val contentExperiencesF = optionToFuture(contentExperiencesRequestO.map { contentExperiencesRequest =>
      propertyDetailsDataFetcher.fetchContentExperiencesResponse(
        contentExperiencesRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    /** ContentFeatures */
    val contentFeaturesF = optionToFuture(contentFeaturesRequestO.map { contentFeaturesRequest =>
      propertyDetailsDataFetcher
        .fetchContentFeaturesResponse(contentFeaturesRequest, ctx.globalContext, ctx.getSearchContext.experimentContext)
    })

    /** ContentHighlights */
    val contentHighlightsF = optionToFuture(contentHighlightsRequestO.map { contentHighlightsRequest =>
      propertyDetailsDataFetcher.fetchContentHighlightsResponse(
        contentHighlightsRequest,
        ctx.globalContext,
        ctx.getSearchContext.experimentContext
      )
    })

    val contentQnaF = optionToFuture(contentQnaRequestO.map { contentQnaRequest =>
      propertyDetailsDataFetcher
        .fetchContentQnaResponse(contentQnaRequest, ctx.globalContext, ctx.getSearchContext.experimentContext)
    })

    val contentTopicsF = optionToFuture(contentTopicsRequestO.map { contentTopicsRequest =>
      propertyDetailsDataFetcher
        .fetchContentTopicsResponse(contentTopicsRequest, ctx.globalContext, ctx.getSearchContext.experimentContext)
    })

    val bannerCampaignF = optionToFuture(bannerCampaignRequestO.map { bannerCampaignRequest =>
      propertyDetailsDataFetcher
        .fetchPropertyBannerCampaignResponse(bannerCampaignRequest, ctx.globalContext)
    })

    /** Content RateCategory */
    val contentRateCategoryF = optionToFuture(contentRateCategoryRequest0.map { contentRateCategoryRequest =>
      propertyDetailsDataFetcher
        .fetchContentRateCategoryResponse(
          contentRateCategoryRequest,
          ctx.globalContext,
          ctx.getSearchContext.experimentContext
        )
    })

    val growthProgramInfoF = optionToFuture(growthProgramInfoRequest0.map { growthProgramInfoRequest =>
      propertyDetailsDataFetcher.fetchGrowthProgramInfoResponse(growthProgramInfoRequest)
    })

    val propertyDetailsF = propertyDetailsRequest0
      .map { propertyDetailsRequest =>
        setSearchContextF(ctx)(ec).flatMap { _ =>
          val propertyDetails = propertyDetailsRequest.map(propertyId => PropertyDetail(propertyId))
          Future.successful(propertyDetails)
        }
      }
      .getOrElse(Future.successful(List.empty))

    /** CAPI Host Profile */
    val hostProfileResponseF =
      for {
        contentInformationSummaryResponse <- contentInformationSummaryResponseF
        hostProfile <- {
          val contentInformationSummaryResponseMap: Map[PropertyId, Wrapper[ContentInformationSummary]] =
            contentInformationSummaryResponse.getOrElse(Map.empty)
          Future
            .sequence(hostProfileDeferredO.map { hostProfileDeferred =>
              val result: Future[Option[(PropertyId, HostProfileResponse)]] =
                if (
                  contentInformationSummaryResponseMap.get(hostProfileDeferred.propertyId).exists(_.t.hasHostExperience)
                ) {
                  ctx.requestContext.metrics
                    .setAttribute(AttributeKey.stringKey(MeasurementTag.isFetchHostProfile), "true")

                  propertyDetailsDataFetcher
                    .fetchHostProfile(hostProfileDeferred.propertyId, hostProfileDeferred.hostProfileRequest)
                    .recover { case exception =>
                      error(
                        Components.CustomerAPI,
                        s"Exception while getting Host Profile response",
                        Some(exception)
                      )
                      None
                    }
                    .map(_.map(r => hostProfileDeferred.propertyId -> r))
                } else {
                  ctx.requestContext.metrics
                    .setAttribute(AttributeKey.stringKey(MeasurementTag.isFetchHostProfile), "false")

                  Future.successful(None)
                }
              result
            })
            .map(_.flatten.toMap)
        }
      } yield hostProfile

    val partialFunctionResult: PartialFunction[Deferred[Any], Future[Any]] = {

      case deferred: PropertyDetailsDeferred =>
        propertyDetailsF.map { propertyDetails =>
          val propertyDetailsCount = propertyDetails.size
          metricsReporter.report(
            MeasurementName.PropertyDetailsCount,
            propertyDetailsCount,
            Map(
              MeasurementTag.IsEmptyResult -> (propertyDetailsCount == 0).toString,
              MeasurementTag.PlatformId    -> ctx.getSearchContext.globalContext.getPlatformId.getOrElse(-1).toString,
              MeasurementTag.NewTrafficType -> ctx.getSearchContext.globalContext.getBotInfo
                .map(
                  BotProfileParser(Seq(BotTypes.VOLUME, BotTypes.VOLUME_HIDDEN)).parse(_).trafficType
                )
                .getOrElse(TrafficType.None)
                .toString,
              MeasurementTag.WhiteLabelId -> ctx.getSearchContext.globalContext.getWhiteLabelId
                .getOrElse(WhiteLabelId.AGODA)
                .toString
            ) ++ ctx.operationTags
          )
          propertyDetails
        }

      case deferred: ContentImagesDeferred =>
        contentImagesResponseF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentReviewScoreDeferred =>
        contentReviewScoreResponseF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentReviewSummariesDeferred =>
        contentReviewSummariesResponseF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentInformationSummaryDeferred =>
        contentInformationSummaryResponseF.map(result =>
          result.flatMap(map => map.get(deferred.propertyId).map(_.json))
        )
      case deferred: ContentInformationDeferred =>
        contentInformationResponseF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentLocalInformationDeferred =>
        localInformationF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentEngagementDeferred =>
        contentEngagementF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentExperienceDeferred =>
        contentExperiencesF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentFeaturesDeferred =>
        contentFeaturesF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentHighlightsDeferred =>
        contentHighlightsF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentQnaDeferred =>
        contentQnaF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: ContentTopicsDeferred =>
        contentTopicsF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: HostProfileDeferred =>
        setSearchContextF(ctx)(ec).flatMap(_ => hostProfileResponseF.map(_.get(deferred.propertyId)))
      case deferred: PropertyBannerCampaignInfoDeferred =>
        bannerCampaignF.map(result => result.flatMap(map => map.get(deferred.propertyId)))
      case deferred: ContentRateCategoryDeferred =>
        contentRateCategoryF.map(result => result.flatMap(map => map.get(deferred.propertyId).map(_.json)))
      case deferred: GrowthProgramInfoDeferred =>
        growthProgramInfoF.map(result => result.flatMap(map => map.get(deferred.propertyId)))
    }
    partialFunctionResult
  }

}
