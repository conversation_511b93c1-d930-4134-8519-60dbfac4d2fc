package com.agoda.papi.search.property.api.validation

import request.AreaSearchRequest

/** Created by vpathomsuriy on 3/8/2017 AD.
  */
object AreaSearchValidationRules extends ValidationRule[AreaSearchRequest] {

  override protected def validationRules(request: AreaSearchRequest): Option[ValidationFailure] =
    if (request.areaId > 0) {
      None
    } else {
      Some(ValidationFailure("AreaID must be greater than 0."))
    }

}
