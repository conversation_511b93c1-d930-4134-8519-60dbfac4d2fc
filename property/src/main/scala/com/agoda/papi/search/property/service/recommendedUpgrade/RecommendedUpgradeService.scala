package com.agoda.papi.search.property.service.recommendedUpgrade

import com.agoda.papi.enums.campaign.{ PriceDisplayType, PriceDisplayTypes }
import com.agoda.papi.search.common.util.helper.DoubleConversionUtil
import models.pricing.enums.{ ApplyType, ApplyTypes }
import models.starfruit.SuggestedPrice
import transformers.{ EnrichedChildRoom, EnrichedMasterRoom, RecommendedUpgrade }

case class UpgradeRule(sizeThresholdPercent: Double, priceThresholdPercent: Double)

case class UpgradeContext(
    suggestedBasis: Option[ApplyType],
    suggestedPrice: Option[SuggestedPrice],
    currency: Option[String]
)

trait RecommendedUpgradeService {

  def enrichUpgradeRecommendation(
      masterRooms: List[EnrichedMasterRoom],
      context: UpgradeContext
  ): List[EnrichedMasterRoom]

}

class RecommendedUpgradeServiceImpl extends RecommendedUpgradeService {

  private val RoomSizeFeatureName = "room-size-sqft"

  private val upgradeRules: List[UpgradeRule] = List(
    UpgradeRule(10.0, 5.0),
    UpgradeRule(15.0, 10.0),
    UpgradeRule(20.0, 15.0)
  )

  private val MaxPriceDiffPercentThreshold =
    upgradeRules.map(_.priceThresholdPercent).reduceOption(_ max _).getOrElse(0.0)

  private def getPrice(
      childRoom: EnrichedChildRoom,
      suggestedBasis: Option[ApplyType],
      suggestedPrice: Option[SuggestedPrice],
      currency: Option[String]
  ): Double =
    childRoom.pricing.headOption
      .map { case (cur, pricing) =>
        val priceObj    = childRoom.pricing.getOrElse(currency.getOrElse(cur), pricing)
        val displayType = toPriceDisplayType(suggestedBasis)
        val display     = priceObj.display

        val priceBasis = displayType match {
          case PriceDisplayTypes.PerRoomPerNight => display.perRoomPerNight
          case PriceDisplayTypes.PerNight        => display.perNight
          case PriceDisplayTypes.PerBook         => display.perBook
        }

        if (suggestedPrice.contains(SuggestedPrice.AllInclusive)) priceBasis.allInclusive
        else priceBasis.exclusive
      }
      .getOrElse(0.0)

  private def getRoomSizeSqft(masterRoom: EnrichedMasterRoom): Double =
    masterRoom.features
      .find(_.name == RoomSizeFeatureName)
      .map(_.text.toDouble)
      .getOrElse(0.0)

  override def enrichUpgradeRecommendation(
      masterRooms: List[EnrichedMasterRoom],
      context: UpgradeContext
  ): List[EnrichedMasterRoom] =
    if (masterRooms.length < 2) {
      masterRooms
    } else {
      val baseRoom      = masterRooms.head
      val baseRoomSize  = getRoomSizeSqft(baseRoom)
      val baseRoomPrice = getFirstChildRoomPrice(baseRoom, context)

      enrichCandidates(
        baseRoom,
        baseRoomSize,
        baseRoomPrice,
        masterRooms.tail,
        context
      )
    }

  private def enrichCandidates(
      baseRoom: EnrichedMasterRoom,
      baseRoomSize: Double,
      baseRoomPrice: Double,
      candidateRooms: List[EnrichedMasterRoom],
      context: UpgradeContext
  ): List[EnrichedMasterRoom] = {
    val (_, _, result) = candidateRooms.foldLeft((false, false, List(baseRoom))) {
      case ((found, stopped, acc), candidateRoom) if found || stopped =>
        (found, stopped, acc :+ candidateRoom)
      case ((found, stopped, acc), candidateRoom) =>
        processCandidate(
          baseRoomSize,
          baseRoomPrice,
          candidateRoom,
          context,
          found,
          stopped,
          acc
        )
    }
    result
  }

  private def processCandidate(
      baseRoomSize: Double,
      baseRoomPrice: Double,
      candidateRoom: EnrichedMasterRoom,
      context: UpgradeContext,
      found: Boolean,
      stopped: Boolean,
      acc: List[EnrichedMasterRoom]
  ): (Boolean, Boolean, List[EnrichedMasterRoom]) = {
    val candidateRoomSize  = getRoomSizeSqft(candidateRoom)
    val candidateRoomPrice = getFirstChildRoomPrice(candidateRoom, context)
    val priceDiffPercent   = calculatePriceDiffPercent(baseRoomPrice, candidateRoomPrice)

    if (priceDiffPercent > MaxPriceDiffPercentThreshold) {
      (found, true, acc :+ candidateRoom)
    } else {
      val recommendedUpgrade = calculateRecommendedUpgrade(
        baseRoomPrice,
        candidateRoomPrice,
        baseRoomSize,
        candidateRoomSize
      )
      if (recommendedUpgrade.isDefined) {
        (true, true, acc :+ candidateRoom.copy(recommendedUpgrade = recommendedUpgrade))
      } else {
        (found, stopped, acc :+ candidateRoom)
      }
    }
  }

  private def calculatePriceDiffPercent(basePrice: Double, candidatePrice: Double): Double =
    if (basePrice > 0) {
      (candidatePrice - basePrice) / basePrice * 100
    } else {
      0.0
    }

  private def getFirstChildRoomPrice(
      room: EnrichedMasterRoom,
      context: UpgradeContext
  ): Double =
    room.childrenRooms.headOption
      .map(getPrice(_, context.suggestedBasis, context.suggestedPrice, context.currency))
      .getOrElse(0.0)

  private def calculateRecommendedUpgrade(
      baseRoomPrice: Double,
      candidateRoomPrice: Double,
      baseRoomSize: Double,
      candidateRoomSize: Double
  ): Option[RecommendedUpgrade] = {
    val invalidInputs = Seq(baseRoomPrice, candidateRoomPrice, baseRoomSize, candidateRoomSize).exists(_ <= 0)
    if (invalidInputs || candidateRoomPrice <= baseRoomPrice) {
      None
    } else {
      val sizeDiffPercent  = (candidateRoomSize - baseRoomSize) / baseRoomSize * 100
      val priceDiffPercent = (candidateRoomPrice - baseRoomPrice) / baseRoomPrice * 100

      val isUpgrade = upgradeRules.exists { rule =>
        sizeDiffPercent >= rule.sizeThresholdPercent && priceDiffPercent <= rule.priceThresholdPercent
      }
      if (isUpgrade) {
        Some(
          RecommendedUpgrade(
            priceDiff = DoubleConversionUtil.roundToNDecimal(candidateRoomPrice - baseRoomPrice, 2),
            roomSizeDiffPercent = Some(DoubleConversionUtil.roundToNDecimal(sizeDiffPercent, 2))
          )
        )
      } else {
        None
      }
    }
  }

  private def toPriceDisplayType(requiredBasis: Option[ApplyType]): PriceDisplayType = requiredBasis match {
    case Some(ApplyTypes.PRPN) => PriceDisplayTypes.PerRoomPerNight
    case Some(ApplyTypes.PN)   => PriceDisplayTypes.PerNight
    case _                     => PriceDisplayTypes.PerBook
  }

}
