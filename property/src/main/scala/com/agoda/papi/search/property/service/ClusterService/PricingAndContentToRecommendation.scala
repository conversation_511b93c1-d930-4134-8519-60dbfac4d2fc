package com.agoda.papi.search.property.service.ClusterService

import com.agoda.core.search.models.enumeration.ProductTypeID
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.supplier.common.models.SupplierId
import com.agoda.supplier.common.services.SupplierMetadata
import com.typesafe.scalalogging.LazyLogging
import constant.FeaturesConstants
import mappings.CMSMapping
import models.db.CountryId
import models.starfruit.PropertySearchRequest
import request.PropertyRequest
import serializer.Serialization
import com.agoda.papi.search.property.service.{ LogicHelper, SfModelMapper, SupplierInformationHelperService }
import com.agoda.papi.search.property.service.enrich.{ EnrichPackagePricingServiceImpl, EnrichRoomHelper }
import com.agoda.papi.search.property.service.{
  LogicHelper,
  PAPICMSService,
  SfModelMapper,
  SupplierInformationHelperService
}
import transformers._
import types.HotelIdType

import scala.concurrent.{ ExecutionContext, Future }

/** Created by schoudhary on 8/9/16.
  */
object PricingAndContentToRecommendation extends PAPIAppLogger {

  def convertPricingAndContentToRecommendationResponse(
      result: RecommendationResult,
      matchScore: Option[MatchScore]
  )(implicit
      sfpp: PropertyRequest,
      ec: ExecutionContext,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository
  ): Future[Option[RecommendationResponse]] = {
    val languageId = SfModelMapper.localeToLanguageUse(sfpp.context.locale)

    val enrichedChannelFuture = result.hotel
      .map(s => Future.successful(s.enrichedChannel))
      .getOrElse(PricingAndContentToRecommendation.enrichChannel(0, languageId, None, "deal-gift-card"))
    val breakfastFlagDataFuture = result.hotel
      .map(s => Future.successful(s.breakfastFlag))
      .getOrElse(PricingAndContentToRecommendation.enrichBreakFastFlag(false, languageId))

    val recommendationResponse = for {
      enrichedChannel   <- enrichedChannelFuture
      breakfastFlagData <- breakfastFlagDataFuture
    } yield {
      val totalDiscount              = result.hotel.flatMap(_.totalDiscount)
      val cheapestRoomPrice          = result.hotel.map(_.cheapestPriceCrossedDisplay)
      val cheapestRoomCor            = result.hotel.map(_.cheapestPriceCrossedOut)
      val cheapestRoomPackagingPrice = result.hotel.flatMap(_.cheapestRoomPackaging)
      val cheapestRoomCharges        = result.hotel.map(_.cheapestRoomCharges)
      val cheapestRoomCancellation   = result.hotel.map(_.cheapestRoomCancellation)
      val recommendationMseResults   = result.hotel.map(_.recommendationMseResults)
      val loyaltyDisplay             = result.hotel.flatMap(_.loyaltyDisplay)

      val defaultDmc = result.summaryResponse.reviews.flatMap(_.cumulative.get.providers.find(_.default))
      val (reviewScore, reviewCount, reviewScoreText) = if (defaultDmc.isDefined) {
        (
          result.summaryResponse.reviews.flatMap(
            _.cumulative.get.totals.find(_.providerId.contains(defaultDmc.get.id)).flatMap(_.score)
          ),
          result.summaryResponse.reviews.flatMap(
            _.cumulative.get.totals.find(_.providerId.contains(defaultDmc.get.id)).flatMap(_.reviewCount)
          ),
          result.summaryResponse.reviews.flatMap(
            _.cumulative.get.totals.find(_.providerId.contains(defaultDmc.get.id)).flatMap(_.scoreText)
          )
        )
      } else {
        (
          result.summaryResponse.reviews.flatMap(_.cumulative.get.totals.headOption.flatMap(_.score)),
          result.summaryResponse.reviews.flatMap(_.cumulative.get.totals.headOption.flatMap(_.reviewCount)),
          result.summaryResponse.reviews.flatMap(_.cumulative.get.totals.headOption.flatMap(_.scoreText))
        )
      }

      val reviewDemographics = result.summaryResponse.reviews.flatMap(_.demographics)
      val engagement         = result.summaryResponse.engagement
      val mainImage   = result.summaryResponse.images.flatMap(_.mainImage.flatMap(_.locations.headOption.map(_._2)))
      val name        = result.summaryResponse.summary.map(_.defaultName)
      val localName   = result.summaryResponse.summary.map(_.localeName)
      val displayName = result.summaryResponse.summary.map(_.localeName)
      val starRating  = result.summaryResponse.summary.map(_.rating.value)
      val starRatingSymbol = result.summaryResponse.summary.map(_.rating.symbol)
      val starRatingColor  = result.summaryResponse.summary.map(_.rating.color.name)
      val starRatingText   = result.summaryResponse.summary.map(_.rating.text)
      val locationHighlightMessage = Option(
        result.summaryResponse.highlights.flatMap(_.locationHighlightMessage)
      ).flatten
      val locationHighlights = result.summaryResponse.highlights.flatMap(_.locationHighlights)

      val isFreeWiFi = result.summaryResponse.summary
        .map(_.highlightedFeatures.exists(_.id.contains(FeaturesConstants.FreeWiFiInAllRoom)))
        .getOrElse(false)
      val area                            = result.summaryResponse.summary.map(_.address.flatMap(_.area).getOrElse(""))
      val propertyId                      = result.summaryResponse.summary.map(_.propertyId)
      val singleRoomNonHotelAccommodation = result.summaryResponse.summary.map(_.singleRoomNonHotelAccommodation)
      val isNonHotelAccommodationType     = result.summaryResponse.summary.map(_.isNonHotelAccommodationType)
      val hasHostExperience               = result.summaryResponse.summary.map(_.hasHostExperience)
      val nonHotelRecommendation          = result.summaryResponse.nonHotelAccommodation
      val hostType                        = result.summaryResponse.summary.flatMap(_.nhaSummary.flatMap(_.hostType))

      Some(
        RecommendationResponse(
          cheapestPriceCrossedDisplay = cheapestRoomPrice,
          cheapestPriceCrossedOut = cheapestRoomCor,
          cheapestRoomPackaging = cheapestRoomPackagingPrice,
          cheapestRoomCharges = cheapestRoomCharges,
          cheapestRoomCancellation = cheapestRoomCancellation,
          mainImage = mainImage,
          name = name,
          localName = localName,
          displayName = displayName,
          reviewCount = reviewCount,
          reviewScore = reviewScore,
          reviewScoreText = reviewScoreText,
          starRating = starRating,
          starRatingSymbol = starRatingSymbol,
          starRatingColor = starRatingColor,
          starRatingText = starRatingText,
          isBreakfastIncluded = breakfastFlagData,
          channel = enrichedChannel,
          isFreeWiFi = isFreeWiFi,
          loyaltyDisplay = loyaltyDisplay,
          neighborhood = area,
          propertyId = propertyId,
          reviewDemographics = reviewDemographics,
          recommendationMseResults = recommendationMseResults,
          engagement = engagement,
          singleRoomNonHotelAccommodation = singleRoomNonHotelAccommodation,
          isNonHotelAccommodationType = isNonHotelAccommodationType,
          hasHostExperience = hasHostExperience,
          hostType = hostType,
          productHotels = result.hotel.flatMap(_.productHotels),
          locationHighlightMessage = locationHighlightMessage,
          reviewCumulative = result.summaryResponse.reviews.flatMap(_.cumulative),
          locationHighlights = locationHighlights,
          totalDiscount = totalDiscount,
          nonHotelAccommodation = nonHotelRecommendation,
          filteredBy = None,
          matchScore = matchScore,
          cheapestRoomPayment = result.hotel.flatMap(_.cheapestRoomPayment),
          suggestedPrice = result.hotel.flatMap(_.h.suggestPriceType.map(_.suggestPrice)),
          suggestedBasis = result.hotel.flatMap(_.h.suggestPriceType.map(_.applyType)),
          propertyLinks = result.summaryResponse.summary.flatMap(_.links)
        )
      )
    }
    recommendationResponse.recoverWith { case e =>
      val requestJson = Serialization.jacksonMapper.writeValueAsString(sfpp)
      val msg =
        s"recommendation failed on hotel: ${result.hotel.map(_.h.hotelId)}, ${e.getMessage}, request: $requestJson"
      warn(Components.PAPI, msg, Some(e))
      Future.successful(None)
    }
  }

  def getRecommendationCmsData(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[RecommendationHeader] = {
    val languageUse = SfModelMapper.localeToLanguageUse(sfpp.context.locale)

    val recommendationHeaderFuture = if (sfpp.recommendation.exists(_.productTypeId.contains(ProductTypeID.HOMES))) {
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_Homes_Header, languageUse).map(_.getOrElse(""))
    } else cmsService.toCmsTextFuture(CMSMapping.Recommendation_Header, languageUse).map(_.getOrElse(""))
    val recommendationRoomRateFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_Title_RoomRate, languageUse).map(_.getOrElse(""))
    val recommendationGuestRatingFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_Title_GuestRating, languageUse).map(_.getOrElse(""))
    val recommendationNeighborhoodFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_Title_Neighborhood, languageUse).map(_.getOrElse(""))
    val recommendationBreakfastFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_Title_Breakfast, languageUse).map(_.getOrElse(""))
    val recommendationWifiFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_Title_Wifi, languageUse).map(_.getOrElse(""))
    val recommendationMoreDeal = cmsService.toCmsText(CMSMapping.Recommendation_MoreDeal, languageUse).getOrElse("")

    for {
      recommendationHeader       <- recommendationHeaderFuture
      recommendationRoomRate     <- recommendationRoomRateFuture
      recommendationGuestRating  <- recommendationGuestRatingFuture
      recommendationNeighborhood <- recommendationNeighborhoodFuture
      recommendationBreakfast    <- recommendationBreakfastFuture
      recommendationWifi         <- recommendationWifiFuture
    } yield {
      val recommendationBreakfastCmsData = CmsData(CmsDataGroupTypes.BreakFastInclude, recommendationBreakfast)
      val recommendationWifiCmsData      = CmsData(CmsDataGroupTypes.FreeWifi, recommendationWifi)

      RecommendationHeader(
        CmsData(CmsDataGroupTypes.None, recommendationHeader),
        CmsData(CmsDataGroupTypes.None, recommendationRoomRate),
        CmsData(CmsDataGroupTypes.None, recommendationGuestRating),
        CmsData(CmsDataGroupTypes.None, recommendationNeighborhood),
        List(recommendationBreakfastCmsData, recommendationWifiCmsData),
        CmsData(CmsDataGroupTypes.None, recommendationMoreDeal)
      )
    }

  }

  def convertPricingAndContentToRecommendationResponseWithEnrichList(
      recommendationResponseList: List[RecommendationResponse]
  )(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[List[ZenithResponseEnriched]] = {
    val enrichedRecommendationResponseList: List[Future[ZenithResponseEnriched]] = recommendationResponseList.map {
      recommendationResponse =>
        PricingAndContentToRecommendation.convertPricingAndContentToRecommendationResponseWithEnrich(
          recommendationResponse
        )
    }
    Future.sequence(enrichedRecommendationResponseList)
  }

  def convertPricingAndContentToRecommendationResponseWithEnrich(
      recommendationResponse: RecommendationResponse
  )(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[ZenithResponseEnriched] = {

    val languageUse = SfModelMapper.localeToLanguageUse(sfpp.context.locale)

    val freeWifiFacilityEnrichFuture = enrichFreeWifiFlag(recommendationResponse.isFreeWiFi, languageUse)
    val reviewCountTextFuture = getReviewCountText(recommendationResponse.reviewCount.getOrElse(-1L), languageUse)

    for {
      freeWifiFacilityEnrich <- freeWifiFacilityEnrichFuture
      reviewCountText        <- reviewCountTextFuture
    } yield {
      val recommendationCheapestRoom = ZenithCheapestRoom(
        recommendationResponse.cheapestPriceCrossedDisplay,
        recommendationResponse.cheapestPriceCrossedOut,
        benefits = List(recommendationResponse.isBreakfastIncluded),
        channel = recommendationResponse.channel,
        loyaltyDisplay = recommendationResponse.loyaltyDisplay,
        packaging = recommendationResponse.cheapestRoomPackaging,
        cancellation = recommendationResponse.cheapestRoomCancellation,
        charges = recommendationResponse.cheapestRoomCharges,
        payment = recommendationResponse.cheapestRoomPayment
      )
      ZenithResponseEnriched(
        cheapestPriceBasis = recommendationResponse.cheapestPriceCrossedDisplay,
        cheapestRoom = recommendationCheapestRoom,
        mainImage = recommendationResponse.mainImage,
        name = recommendationResponse.name,
        localName = recommendationResponse.localName,
        displayName = recommendationResponse.displayName,
        reviewCount = recommendationResponse.reviewCount,
        reviewCountText = Option(reviewCountText),
        reviewScore = recommendationResponse.reviewScore,
        reviewScoreText = recommendationResponse.reviewScoreText,
        starRating = recommendationResponse.starRating,
        starRatingSymbol = recommendationResponse.starRatingSymbol,
        starRatingColor = recommendationResponse.starRatingColor,
        starRatingText = recommendationResponse.starRatingText,
        neighborhood = recommendationResponse.neighborhood,
        highlightFacilities = List(freeWifiFacilityEnrich),
        propertyId = recommendationResponse.propertyId,
        reviewDemographics = recommendationResponse.reviewDemographics,
        recommendationMseResults = recommendationResponse.recommendationMseResults,
        engagement = recommendationResponse.engagement,
        singleRoomNonHotelAccommodation = recommendationResponse.singleRoomNonHotelAccommodation,
        isNonHotelAccommodationType = recommendationResponse.isNonHotelAccommodationType,
        hasHostExperience = recommendationResponse.hasHostExperience,
        hostType = recommendationResponse.hostType,
        locationHighlightMessage = recommendationResponse.locationHighlightMessage,
        reviewCumulative = recommendationResponse.reviewCumulative,
        bookingHistory = None,
        topSellingPoints = Nil,
        locationHighlights = recommendationResponse.locationHighlights,
        totalDiscount = recommendationResponse.totalDiscount,
        nonHotelAccommodation = recommendationResponse.nonHotelAccommodation,
        filteredBy = recommendationResponse.filteredBy,
        matchScore = recommendationResponse.matchScore,
        suggestedPrice = recommendationResponse.suggestedPrice,
        suggestedBasis = recommendationResponse.suggestedBasis,
        propertyLinks = recommendationResponse.propertyLinks
      )
    }
  }

  def getReviewCountText(reviewCount: Long, languageId: Long)(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[String] =
    reviewCount match {
      case 1 =>
        cmsService
          .toCmsTextFuture(CMSMapping.Recommendation_Review, languageId)
          .map(_.getOrElse("").replaceAll("\\{0\\}", reviewCount.toString))
      case _ =>
        cmsService
          .toCmsTextFuture(CMSMapping.Recommendation_Reviews, languageId)
          .map(_.getOrElse("").replaceAll("\\{0\\}", reviewCount.toString))
    }

  def enrichBreakFastFlag(isBreakfastIncluded: Boolean, languageId: Long)(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[CmsFlagData] = {

    val descriptionFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_BreakfastInclude, languageId).map(_.getOrElse(""))
    descriptionFuture.map { description =>
      CmsFlagData(CmsDataGroupTypes.BreakFastInclude, description, "breakfast", isBreakfastIncluded)
    }
  }

  def enrichChannel(
      channelId: Long,
      languageId: Long,
      propertySearchRequest: Option[PropertySearchRequest],
      insiderDealBadgeSymbol: String = "deal-gift-card"
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[EnrichedChannel] = {
    def toCmsText(cmsId: Long, languageId: Long)(implicit
        ec: ExecutionContext,
        languageRepository: LanguageRepository,
        cmsService: PAPICMSService
    ): Future[String] =
      cmsService.toCmsTextFuture(cmsId, languageId).map(_.getOrElse(""))
    val isApsEnabled     = propertySearchRequest.map(_.pricing.features.isApsEnabled)
    val requestedChannel = propertySearchRequest.map(_.pricing.filters.ratePlans)
    channelId match {
      case 2L
          if LogicHelper.determineInsiderDeal(
            Option(channelId),
            requestedChannel.getOrElse(List.empty),
            isApsEnabled.getOrElse(false)
          ) =>
        toCmsText(CMSMapping.GiftSouvenirChannel, languageId)
          .map(cmsText => EnrichedChannel(channelId, cmsText, insiderDealBadgeSymbol))
      case 6L =>
        toCmsText(CMSMapping.DomesticRateChannel, languageId)
          .map(cmsText => EnrichedChannel(channelId, cmsText, "domestic-rates"))
      case 7L =>
        toCmsText(CMSMapping.RoomPromotionMobileChannel, languageId)
          .map(cmsText => EnrichedChannel(channelId, cmsText, "room-promotion-for-mobiledeal"))
      case 103L if LogicHelper.isGoLocalInsiderDeal(propertySearchRequest) =>
        toCmsText(CMSMapping.GiftSouvenirChannel, languageId)
          .map(cmsText => EnrichedChannel(channelId, cmsText, insiderDealBadgeSymbol))
      case _ => Future.successful(EnrichedChannel(channelId, "", ""))
    }
  }

  def enrichFreeWifiFlag(isFreeWifi: Boolean, languageId: Long)(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      contextHolder: ContextHolder,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService
  ): Future[CmsFlagData] = {

    val descriptionFuture =
      cmsService.toCmsTextFuture(CMSMapping.Recommendation_FreeWifi, languageId).map(_.getOrElse(""))
    descriptionFuture.map { description =>
      CmsFlagData(CmsDataGroupTypes.FreeWifi, description, "wifi", isFreeWifi)
    }
  }

  def enrichRecommendationMseResult(
      rooms: scala.Seq[models.starfruit.Room],
      request: PropertySearchRequest,
      supplierMetadataCache: Map[SupplierId, SupplierMetadata],
      languageId: Long,
      cmsService: PAPICMSService,
      countryId: CountryId
  )(implicit contextHolder: ContextHolder): Future[List[RecomendationMseResults]] = {
    val shouldMapMseRecommendationResult =
      contextHolder.requestContextO.flatMap(_.context.flatMap(_.isMSE)).contains(true)
    if (rooms.nonEmpty && shouldMapMseRecommendationResult) {
      val result = rooms.map { r =>
        val supplierNameAndImage = supplierMetadataCache.get(r.subSupplierId)
        val name = SupplierInformationHelperService.getSupplierName(supplierNameAndImage, languageId.toInt, countryId)(
          cmsService,
          contextHolder
        )
        RecomendationMseResults(r.subSupplierId, getPriceBasis(r, request.pricing.currency), name, name)
      }.toList
      Future.successful(result)
    } else {
      Future.successful(List.empty)
    }
  }

  def getPriceBasis(room: models.starfruit.Room, currency: models.db.Currency): models.starfruit.DisplayBasis =
    room.pricing.getOrElse(currency, room.pricing.getOrElse(room.localCurrencyCode, room.pricing.head._2)).display
}
