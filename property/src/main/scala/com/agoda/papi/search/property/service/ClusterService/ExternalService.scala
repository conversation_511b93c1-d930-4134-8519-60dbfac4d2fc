package com.agoda.papi.search.property.service.ClusterService

import cats.instances.future._
import com.agoda.commons.tracing.api.WithTracer
import com.agoda.commons.tracing.core.TracerOps._
import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.nonHotelAccommodation.NonHotelAccommodation
import com.agoda.content.models.db.personalized.PersonalizedInformation
import com.agoda.content.models.db.rateCategories.{ RateCategoriesResponse, RateCategoryResponse }
import com.agoda.content.models.db.reviews.ReviewResponse
import com.agoda.content.models.db.synopsis.Synopsis
import com.agoda.core.search.models.GroupMatrixResultMapping
import com.agoda.ml.recommendation.zenith.recommendation.RoomRecoResponse
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.service.WhiteLabelService
import com.agoda.papi.search.common.util.ResultTracking
import com.agoda.papi.search.common.util.ResultTracking.enrichedHotelResponse
import com.agoda.papi.search.common.util.context.{ ContextHolder, DevicePlatforms, PlatformIds, SystemIds }
import com.agoda.papi.search.common.util.debug.ResultTrackerSteps
import com.agoda.papi.search.common.util.debug.ResultTrackerSteps.{
  EnrichedRooms,
  FetchMasterRoomFromContent,
  FetchRoomFromDF
}
import com.agoda.papi.search.common.util.hotelshutdown.HotelShutdownSetting
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.common.util.measurement.{
  APIFlowMetric,
  APIInstrumentedMetric,
  MeasurementKey,
  PageMeasurementStep
}
import com.agoda.papi.search.internalmodel.JMapBuilder._
import com.agoda.papi.search.internalmodel.database.ProductHotels
import com.agoda.papi.search.property.externaldependency.logger.EscapesSummaryHelper
import com.agoda.papi.search.property.service.ClusterService.propertyfilter.{
  PropertyFilter,
  PropertyFilterSortingService
}
import com.agoda.papi.search.property.service._
import com.agoda.papi.search.property.service.enrich.{ EnrichContentForChildRoomHelper, EnrichRoomBundleService }
import com.agoda.papi.search.property.service.pricestream.PriceStreamService
import com.agoda.papi.search.property.service.roomSorting.RoomSortingHelperService
import com.agoda.papi.search.property.service.recommendedUpgrade.{ RecommendedUpgradeService, UpgradeContext }
import com.agoda.pricestream.models.response.{
  LastBookEngagement,
  PropertyLongStayPromotionResponse,
  PropertyMetaInfoResponse
}
import constant._
import mappings.CMSMapping
import metaapi.{ PropertyRanking, RankMetric }
import models.db.MasterRoomTypeId
import models.pricing.enums.ApplyType
import models.starfruit.{ CustomizableRoomGridOptions, DisplayPrice, SuggestedPrice }
import repository.ProviderIds
import request._
import suggestprice.SuggestPrice
import transformers._
import types._
import util._

import scala.collection.compat.toOptionCompanionExtension
import scala.collection.mutable
import scala.concurrent.{ ExecutionContext, Future }
import scala.util.Try

/** Created by vpathomsuriy on 5/25/2016 AD.
  */
//scalastyle:off
trait ExternalService
    extends APIInstrumentedMetric with PAPIAppLogger with MseRoomHelperService with DealOfTheDayService
      with RecommendationEnricherService with HotelResultSummaryHelper with RecommendationComposerService
      with RateCategoriesService with WithTracer with APIFlowMetric {

  val dfPropertyWithCacheToken: DFPropertyWithCacheTokenServiceWrapper
  val dfRecommendationService: DFRecommendationServiceWrapper
  val dfVersionService: DFVersionService
  val contentVersionService: ContentVersionService
  val reviewCommentService: ReviewCommentServiceWrapper
  val reviewPositiveMentionsService: ReviewPositiveMentionsServiceWrapper
  val featureService: FeatureServiceWrapper
  val hotelInfoService: HotelInfoServiceWrapper
  val imageService: ImageServiceWrapper
  val reviewScoreService: ReviewScoreServiceWrapper
  val reviewSnippetService: ReviewSnippetServiceWrapper
  val reviewTotalService: ReviewTotalServiceWrapper
  val reviewFiltersService: ReviewFiltersServiceWrapper
  val reviewCountersService: ReviewCountersServiceWrapper
  val roomDetailService: RoomDetailServiceWrapper
  val zenithService: ZenithService
  val summaryService: ContentSummaryServiceWrapper
  val localInformationService: LocalInformationServiceWrapper
  val experienceService: ExperienceServiceWrapper
  val engagementService: EngagementServiceWrapper
  val hotelInformationSummaryService: HotelInformationSummaryServiceWrapper
  val highlightService: HighlightServiceWrapper
  val nonHotelAccommodationService: NonHotelAccommodationServiceWrapper
  val checkInOutService: CheckInOutServiceWrapper
  val synopsisService: SynopsisServiceWrapper
  val requestBuilderService: RequestBuilderService
  val personalizedInformationService: PersonalizedInformationServiceWrapper
  val enrichFilterTagsForRoomHelper: EnrichContentForChildRoomHelper
  val soldOutRoomsSetting: SoldOutRoomsSetting
  val limitMasterRoomSetting: LimitMasterRoomSetting
  val soldOutPricesService: SoldOutRoomPricesService
  val propertyFilterSortingService: PropertyFilterSortingService
  val priceStreamService: PriceStreamService
  val rateCategoriesService: RateCategoriesServiceWrapper
  val rateCategoryService: RateCategoryServiceWrapper
  val bathInformationService: BathInformationServiceWrapper
  val transportationInformationService: TransportationInformationServiceWrapper
  val enrichRoomBundleService: EnrichRoomBundleService
  val paymentTypesService: DisplayPaymentTypesService
  val hotelShutdownSetting: HotelShutdownSetting
  val whiteLabelService: WhiteLabelService
  val roomSortingService: RoomSortingHelperService
  val recommendedRoomService: RecommendedRoomsService
  val recommendedUpgradeService: RecommendedUpgradeService

  final val RoomImageTypeId = 7

  // Do recommendation search and trigger normal search (searchCommon)
  def searchBasedOnType(
      sfRequest: List[PropertyAPIRequestModel],
      productHotel: mutable.Map[HotelIdType, ProductHotels]
  )(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder,
      bookingHistoryService: BookingHistoryService
  ): Future[WithPropertyToken[Vector[RankingPricingAndContent]]] = {
    val hotelToCityMap: Map[HotelIdType, CityIdType] = productHotel.mapValues(_.cityId).toMap

    val enrichedPersonalizedRecommendationF = tracer.withSpanM("enrichedPersonalizedRecommendation") {
      composeRecentlyViewedRecommendationList(sfpp, hotelToCityMap).flatMap(doEnrichedRecommendation)
    }
    val enrichedPastBookingsRecommendationF = tracer.withSpanM("enrichedPastBookingsRecommendation") {
      composePastBookingsRecommendationList(sfpp, hotelToCityMap).flatMap(doEnrichedRecommendation)
    }

    // Common Search
    val commonResult = tracer.withSpanM("common-search") {
      searchCommon(sfRequest, hotelToCityMap)
    }

    val enrichedRecommendationsF = tracer.withSpanM("enrichedRecommendations") {
      val suggestedPricesF = commonResult.map(
        _.data
          .flatMap(_.hotel.map(h => h.propertyId -> whichPriceToUse(h.suggestedPrice)))
          .toMap
          .withDefaultValue(whichPriceToUse(None))
      )

      composeComparisonRecommendationList(sfpp, suggestedPricesF).flatMap(doEnrichedRecommendation)
    }

    val numberOfRecommendations = getNumberOfRecommendations(sfpp)

    val recommendationCmsDataFuture = tracer.withSpanM("recommendationCmsData") {
      numberOfRecommendations match {
        case Some(number) if number > 0 =>
          PricingAndContentToRecommendation.getRecommendationCmsData.map(Option(_))
        case _ => Future.successful(None)
      }
    }

    // Search properties and recommended properties in parallel
    for {
      result                             <- commonResult
      recommendationCmsData              <- recommendationCmsDataFuture
      enrichedRecommendation             <- enrichedRecommendationsF
      enrichPersonalizedRecommendation   <- enrichedPersonalizedRecommendationF
      enrichedPastBookingsRecommendation <- enrichedPastBookingsRecommendationF
    } yield {
      val newResult = result.data.map { r =>
        val similarPropertyRecommendation = enrichedRecommendation.getOrElse(r.propertyId, List.empty)
        val personalizedRecommendation    = enrichPersonalizedRecommendation.getOrElse(r.propertyId, List.empty)
        val pastBookingsResponse          = enrichedPastBookingsRecommendation.getOrElse(r.propertyId, List.empty)

        RankingPricingAndContent(
          searchId = sfpp.context.searchId,
          propertyId = r.propertyId,
          results = r,
          recommendations = similarPropertyRecommendation,
          personalizedRecommendations = personalizedRecommendation,
          pastBookingsRecommendations = pastBookingsResponse,
          recommendationHeader = recommendationCmsData,
          roomBundle = r.roomBundle,
          breakfastPriceFromHotelSetting = r.breakfastPriceFromHotelSetting
        )
      }
      WithPropertyToken[Vector[RankingPricingAndContent]](result.meta, newResult)
    }
  }

  private def whichPriceToUse: Option[SuggestedPrice] => DisplayPrice => Double = {
    case Some(SuggestedPrice.AllInclusive) => _.allInclusive
    case _                                 => _.exclusive
  }

  def getDFAPIVersion(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[String] =
    dfVersionService.process(None, false)

  def getContentVersion(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[String] =
    contentVersionService.process(None, false)

  def search(
      sfRequest: List[PropertyAPIRequestModel],
      productHotel: mutable.Map[HotelIdType, ProductHotels]
  )(implicit
      ec: ExecutionContext,
      sfpp: PropertyRequest,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder,
      bookingHistoryService: BookingHistoryService
  ): Future[WithPropertyToken[Vector[RankingPricingAndContent]]] =
    tracer.withSpanM("ext-search") {
      searchBasedOnType(sfRequest, productHotel)
    }

  // Group rooms from one hotel
  def getEnrichedMasterRooms(
      rooms: List[EnrichedChildRoom],
      roomDetails: Option[RoomInformationResponse],
      isMse: Boolean,
      hotelId: Long,
      countryId: Long,
      suggestedPrice: Option[SuggestedPrice] = None,
      includeSoldOut: Boolean = false,
      isSuggestedRooms: Boolean = false,
      rateCategoriesMap: Map[RateCategoryId, RateCategoryResponse] = Map.empty,
      rateCategoryIdsWithChildRateEnabled: Vector[RateCategoryId] = Vector.empty,
      customizableRoomGridOptions: Map[MasterRoomTypeId, List[CustomizableRoomGridOptions]] = Map.empty,
      isExternalVipDisplayEnabled: Boolean,
      isRoomFilterEnabled: Boolean = false,
      isValidEscapesFlow: Boolean = false,
      suggestedBasis: Option[ApplyType] = None,
      isValidMergedDayUseOvernightFlow: Boolean = false
  )(implicit request: PropertyRequest, contextHolder: ContextHolder): EnrichedMasterRooms = {
    val isPropertySearch           = request.isSearchType(SearchTypes.Property)
    val isSortSuggestedMasterRooms = isPropertySearch && isSuggestedRooms
    val isGreatForFamiliesMRS = isPropertySearch && isSuggestedRooms && contextHolder.requestContextO
      .flatMap(_.featureFlag)
      .exists(
        _.flags.exists(_.getOrElse(FeatureFlagKey.GreatForFamiliesMRS, false))
      )

    val (roomsWithoutContent, roomsWithContent) = rooms.partition(_.typeId.getOrElse(0L) == 0)
    val masterRoomsWithContent: EnrichedMasterRooms = roomDetails match {
      case Some(roomResponse) =>
        enrichMasterRoomsHavingContent(
          roomResponse,
          roomsWithContent,
          isSortSuggestedMasterRooms,
          includeSoldOut,
          countryId,
          isGreatForFamiliesMRS,
          rateCategoriesMap,
          rateCategoryIdsWithChildRateEnabled,
          customizableRoomGridOptions,
          isExternalVipDisplayEnabled,
          isRoomFilterEnabled,
          isValidEscapesFlow,
          isValidMergedDayUseOvernightFlow
        )
      case _ => EnrichedMasterRooms()
    }

    val allMasterRooms = if (roomsWithoutContent.nonEmpty) {
      timer(MeasurementKey.Rooms, Map("content" -> "false"), 1)
      val masterRoomsWithoutContent: List[EnrichedMasterRoom] = getEnrichedMasterRoomsForRoomsWithoutContent(
        roomsWithoutContent,
        isMse,
        hotelId,
        suggestedPrice,
        rateCategoryIdsWithChildRateEnabled,
        isExternalVipDisplayEnabled,
        isRoomFilterEnabled,
        isValidEscapesFlow,
        isValidMergedDayUseOvernightFlow
      )
      masterRoomsWithContent.regularRooms ++ masterRoomsWithoutContent
    } else {
      timer(MeasurementKey.Rooms, Map("content" -> "true"), 1)
      masterRoomsWithContent.regularRooms
    }

    val isFilterMasterRoom =
      isPropertySearch && !isSuggestedRooms && request.featureFlag.exists(_.allowFilterMasterRoom.contains(true))
    val filteredMasterRooms = if (isFilterMasterRoom) {

      val (excludeFilterCount, maxFilterCount) =
        LimitMasterRoomSetting.getMaxFilterAndExcludeFilterCount(allMasterRooms.length, limitMasterRoomSetting)
      filterMasterRoom(sortMasterRoom(allMasterRooms), maxFilterCount, excludeFilterCount)
    } else {
      allMasterRooms
    }

    val sortedMasterRooms        = sortMasterRoomsIfNeeded(filteredMasterRooms, suggestedPrice)
    val enableRecommendedUpgrade = request.featureFlag.flatMap(_.enableRecommendedUpgrade).getOrElse(false)

    val masterRooms =
      if (enableRecommendedUpgrade) {
        val context = UpgradeContext(suggestedBasis, suggestedPrice, request.pricing.map(_.currency))
        try
          recommendedUpgradeService.enrichUpgradeRecommendation(
            sortedMasterRooms,
            context
          )
        catch {
          case ex: Exception =>
            logger.error("Error during recommended upgrade enrichment", ex)
            sortedMasterRooms
        }
      } else {
        sortedMasterRooms
      }

    masterRoomsWithContent.copy(regularRooms = masterRooms)
  }

  private def sortMasterRoomsIfNeeded(
      masterRooms: List[EnrichedMasterRoom],
      suggestedPrice: Option[SuggestedPrice]
  )(implicit contextHolder: ContextHolder, request: PropertyRequest): List[EnrichedMasterRoom] = stepTimer(
    MeasurementKey.RoomSorting,
    PageMeasurementStep.TotalStep
  ) {
    try
      if (
        masterRooms.nonEmpty &&
        whiteLabelService.isRoomSortingEnabled
      ) {
        roomSortingService.sortRooms(masterRooms, suggestedPrice, request.pricing.map(_.currency))
      } else masterRooms
    catch {
      case e: Exception =>
        logger.error("Error during room sorting", e)
        masterRooms
    }
  }

  private def enrichMasterRoomsHavingContent(
      roomResponse: RoomInformationResponse,
      roomsWithContent: List[EnrichedChildRoom],
      isSortSuggestedMasterRooms: Boolean,
      includeSoldOut: Boolean,
      countryId: Long,
      isGreatForFamiliesMRS: Boolean = false,
      rateCategoriesMap: Map[RateCategoryId, RateCategoryResponse] = Map.empty,
      rateCategoryIdsWithChildRateEnabled: Vector[RateCategoryId] = Vector.empty,
      customizableRoomGridOptions: Map[MasterRoomTypeId, List[CustomizableRoomGridOptions]] = Map.empty,
      isExternalVipDisplayEnabled: Boolean,
      isRoomFilterEnabled: Boolean = false,
      isValidEscapesFlow: Boolean = false,
      isValidMergedDayUseOvernightFlow: Boolean = false
  )(implicit request: PropertyRequest, contextHolder: ContextHolder): EnrichedMasterRooms = {

    val enrichedMasterRooms: List[EnrichedMasterRoom] = roomResponse.roomsInformation.flatMap {
      originalMasterRoomInfo =>
        val masterRoomInfo =
          originalMasterRoomInfo.copy(children = (originalMasterRoomInfo.children :+ originalMasterRoomInfo.metadata))

        val childRooms = contextHolder.papiSearchResultTracker.step {
          roomsWithContent.filter(room => masterRoomInfo.children.exists(c => c.roomId == room.typeId.getOrElse(0L)))
        }(ResultTrackerSteps.FoundContentChildRooms, ResultTracking.enrichedChildRooms(roomResponse.propertyId))

        val childrenRoomsWithFilterTags = enrichFilterTagsForRoomHelper.updateChildRoomWithContent(
          childRooms,
          Option(masterRoomInfo),
          isGreatForFamiliesMRS,
          rateCategoriesMap,
          rateCategoryIdsWithChildRateEnabled,
          isExternalVipDisplayEnabled,
          isRoomFilterEnabled,
          isValidEscapesFlow,
          isValidMergedDayUseOvernightFlow
        )

        childrenRoomsWithFilterTags.size match {
          case 0 =>
            if (
              includeSoldOut && request.pricing.exists(
                _.guestPerRooms <= masterRoomInfo.metadata.maxOccupants.getOrElse(0)
              ) && masterRoomInfo.metadata.isYcs
            ) {
              Option(
                masterRoomToEnrichedMasterRoom(
                  masterRoomInfo,
                  isGreatForFamiliesMRS = isGreatForFamiliesMRS,
                  customizableRoomGridOptions = customizableRoomGridOptions
                )
              )
            } else None
          case _ =>
            Option(
              masterRoomToEnrichedMasterRoom(
                masterRoomInfo,
                childrenRoomsWithFilterTags,
                isGreatForFamiliesMRS,
                customizableRoomGridOptions
              )
            )
        }
    }(collection.breakOut)

    val (regularRooms, soldOutRooms) = enrichedMasterRooms.partition(_.childrenRooms.nonEmpty)

    val roomsWithoutContent =
      roomsWithContent.flatMap(_.typeId).size - enrichedMasterRooms.flatMap(_.childrenRooms.flatMap(_.typeId)).size

    if (roomsWithoutContent > 0) timer(MeasurementKey.Rooms, Map("content" -> "missing"), 1)

    val finalRegularRooms = if (isSortSuggestedMasterRooms) {
      sortSuggestedMasterRoomsByPrice(regularRooms)
    } else {
      regularRooms
    }

    val finalSoldOutRooms =
      if (includeSoldOut) SoldoutRoomHelper.getSoldOutRooms(finalRegularRooms, soldOutRooms)
      else List.empty[EnrichedMasterRoom]

    EnrichedMasterRooms(finalRegularRooms, finalSoldOutRooms)
  }

  private[ClusterService] def sortSuggestedMasterRoomsByPrice(
      masterRooms: List[EnrichedMasterRoom]
  )(implicit request: PropertyRequest): List[EnrichedMasterRoom] = {
    def getPrice: EnrichedMasterRoom => Double = (r: EnrichedMasterRoom) =>
      r.childrenRooms.headOption
        .map(childRoom =>
          if (childRoom.pricing.nonEmpty) {
            childRoom.pricing
              .getOrElse(request.pricing.map(_.currency).getOrElse(""), childRoom.pricing.head._2)
              .display
              .perBook
              .exclusive
          } else Double.MaxValue
        )
        .getOrElse(Double.MaxValue)

    masterRooms.sortBy(getPrice(_))
  }

  private def isFitOverride(roomOcc: Int, searchOcc: Int): Boolean = roomOcc >= searchOcc

  def sortMasterRoom(
      enrichedMasterRooms: List[EnrichedMasterRoom]
  )(implicit request: PropertyRequest): List[EnrichedMasterRoom] = {
    val searchOcc = request.pricing.map(_.guests).getOrElse(0)
    val (masterRoomsWithFitChildren, masterRoomsWithOutFitChildren) =
      enrichedMasterRooms.partition(_.childrenRooms.exists(r => isFitOverride(r.guests, searchOcc)))

    def sellExPRPN: EnrichedChildRoom => Double = (r: EnrichedChildRoom) =>
      if (r.pricing.nonEmpty)
        r.pricing
          .getOrElse(request.pricing.map(_.currency).getOrElse(""), r.pricing.head._2)
          .display
          .perRoomPerNight
          .exclusive
      else Double.MaxValue

    // use only after partition with isFitOverride
    def getChildrenRooms(masterRoom: EnrichedMasterRoom, isFitRoomList: Boolean): List[EnrichedChildRoom] =
      if (isFitRoomList) masterRoom.childrenRooms.filter(r => isFitOverride(r.guests, searchOcc))
      else masterRoom.childrenRooms

    def sortByCheapestPrice(masterRooms: List[EnrichedMasterRoom], isFitRoomList: Boolean): List[EnrichedMasterRoom] =
      masterRooms.sortBy { m =>
        val childrooms   = getChildrenRooms(m, isFitRoomList).minBy(sellExPRPN)
        val isAgodaBrand = supplierMetadataService.isAgodaBrand(childrooms.subSupplierId.getOrElse(ProviderIds.Unknown))
        val typeId       = childrooms.typeId.getOrElse(Long.MinValue)
        (sellExPRPN(childrooms), !isAgodaBrand, -typeId)
      }

    val sortedMasterRoomWithFit    = sortByCheapestPrice(masterRoomsWithFitChildren, true)
    val sortedMasterRoomWithoutFit = sortByCheapestPrice(masterRoomsWithOutFitChildren, false)
    (sortedMasterRoomWithFit ++ sortedMasterRoomWithoutFit)
  }

  def filterMasterRoom(
      enrichedMasterRooms: List[EnrichedMasterRoom],
      maxFilterCount: Int,
      excludeFilterCount: Int
  ): List[EnrichedMasterRoom] = {
    val (excluded, toFilter) = enrichedMasterRooms.splitAt(excludeFilterCount)
    excluded ++ recursiveMasterRoomFilter(toFilter.reverse, maxFilterCount).reverse
  }

  /** Recursive function to filter no image and no room-specific content.
    *
    * @param rooms
    *   as input for filter
    * @param availableFilterCount
    *   is maxmimum room to filter
    * @return
    *   rooms list after remove room without image and room-specific content
    */
  private def recursiveMasterRoomFilter(
      rooms: List[EnrichedMasterRoom],
      availableFilterCount: Int
  ): List[EnrichedMasterRoom] =
    rooms.headOption match {
      case Some(room) if availableFilterCount > 0 =>
        if (room.images.isEmpty && room.facilityGroups.isEmpty)
          recursiveMasterRoomFilter(rooms.tail, availableFilterCount - 1)
        else room :: recursiveMasterRoomFilter(rooms.tail, availableFilterCount)
      case _ => rooms
    }

  def getRoomInfosContentOnly(roomsInformation: Vector[MasterRoomResponse]): List[EnrichedMasterRoom] =
    roomsInformation.map {
      masterRoomToEnrichedMasterRoom(_)
    }(collection.breakOut)

  def getRoomInfosContentOnlyWithChildRoom(
      roomsInformation: Vector[MasterRoomResponse]
  ): List[EnrichedMasterRoom] =
    roomsInformation.map { masterRoom =>
      val masterProvider        = masterRoom.metadata.provider
      val masterDirectConnectId = masterProvider.flatMap(_.directConnectProviderId)
      val master = EnrichedChildRoom(
        typeId = Some(masterRoom.metadata.roomId),
        dmcRoomId = masterProvider.flatMap(_.roomId),
        supplierId = masterProvider.flatMap(_.id.map(_.toLong)),
        directConnectProviderId = masterDirectConnectId
      )
      val childRooms: List[EnrichedChildRoom] = masterRoom.children.map { child =>
        val childProvider        = child.provider
        val childDirectConnectId = childProvider.flatMap(_.directConnectProviderId)
        EnrichedChildRoom(
          typeId = Some(child.roomId),
          dmcRoomId = childProvider.flatMap(_.roomId),
          supplierId = childProvider.flatMap(_.id.map(_.toLong)),
          directConnectProviderId = childDirectConnectId
        )
      }(collection.breakOut)
      masterRoomToEnrichedMasterRoom(
        masterRoom,
        master :: childRooms
      )
    }(collection.breakOut)

  private def getEnrichedMasterRoomsForRoomsWithoutContent(
      rooms: List[EnrichedChildRoom],
      isMse: Boolean,
      hotelId: Long,
      suggestedPrice: Option[SuggestedPrice] = None,
      rateCategoryIdsWithChildRateEnabled: Vector[RateCategoryId] = Vector.empty,
      isExternalVipDisplayEnabled: Boolean,
      isRoomFilterEnabled: Boolean = false,
      isValidEscapesFlow: Boolean = false,
      isValidMergedDayUseOvernightFlow: Boolean = false
  )(implicit request: PropertyRequest, contextHolder: ContextHolder): List[EnrichedMasterRoom] =
    rooms
      .groupBy(_.pricingRoomOption.flatMap(_.dmcPolicyText.map(_.roomTypeName)))
      .collect { case (roomName, roomList) =>
        val roomOcc = roomList.headOption.flatMap(_.capacity.map(_.occupancy)).getOrElse(0)

        val roomListWithTags = enrichFilterTagsForRoomHelper.updateChildRoomWithContent(
          roomList,
          None,
          false,
          Map.empty,
          rateCategoryIdsWithChildRateEnabled,
          isExternalVipDisplayEnabled,
          isRoomFilterEnabled,
          isValidEscapesFlow,
          isValidMergedDayUseOvernightFlow
        )

        EnrichedMasterRoom(
          maxOccupancy = roomOcc,
          maxExtraBeds = 0,
          typeId = 0,
          englishName = None,
          name = roomName,
          images = Vector.empty,
          facilities = Vector.empty,
          bedConfiguration2 = None,
          childrenRooms = roomListWithTags,
          facilityGroups = Vector.empty,
          features = Vector.empty,
          groupMseRooms = Nil,
          isAllowChildren = None,
          isYcsMaster = None,
          suitabilityType = None,
          styleName = None,
          customizableRoomGridOptions = None,
          videos = Vector.empty
        )
      }(collection.breakOut)

  private[ClusterService] def getEnrichedHotel(
      request: PropertyRequest,
      hotel: EnrichedHotelWithOutContent,
      roomDetail: Option[RoomInformationResponse],
      singleRoomNonHotelAccommodation: Option[Boolean],
      synopsis: Option[Synopsis] = None,
      soldOutRoomsPrice: List[SoldOutRoomInfo],
      rateCategoriesMap: Map[RateCategoryId, RateCategoryResponse] = Map.empty,
      roomBundles: List[RoomBundle] = List.empty,
      rateCategoryIdsWithChildRateEnabled: Vector[RateCategoryId] = Vector.empty,
      isExternalVipDisplayEnabled: Boolean,
      isRoomFilterEnabled: Boolean,
      isValidEscapesFlow: Boolean,
      isValidMergedDayUseOvernightFlow: Boolean
  )(implicit contextHolder: ContextHolder, cmsService: PAPICMSService): Option[EnrichedHotelWithContent] = {

    val isMseProperty: Boolean = request.context.isMSE.getOrElse(false)

    // Since we need Synopsis
    val overrideHotel = LogicHelper.overridePointsMaxBadge(request, hotel)

    if (request.searchType.exists(tpe => tpe.equals(SearchTypes.Complex))) {

      val enrichedRoomWithContent = overrideHotel.toWithRoomContent(Seq(), Option(isMseProperty), None)

      if (isMseProperty) {
        val sortedRoomsForMse =
          sortChildRoomsForMse(enrichedRoomWithContent.rooms.toList, hotel.suggestedPrice, hotel.countryId)
        Option(
          enrichedRoomWithContent.copy(
            rooms = sortedRoomsForMse,
            suggestedRooms = List.empty
          )
        )
      } else {
        Option(
          enrichedRoomWithContent.copy(suggestedRooms = List.empty)
        )
      }

    } else {
      val suggestedRoomEnriched = getEnrichedMasterRooms(
        overrideHotel.suggestedRooms,
        roomDetail,
        isMseProperty,
        hotel.propertyId,
        hotel.countryId,
        overrideHotel.suggestedPrice,
        isSuggestedRooms = true,
        rateCategoriesMap = rateCategoriesMap,
        rateCategoryIdsWithChildRateEnabled = rateCategoryIdsWithChildRateEnabled,
        customizableRoomGridOptions = hotel.customizableRoomGridOptions,
        isExternalVipDisplayEnabled = isExternalVipDisplayEnabled,
        isRoomFilterEnabled = isRoomFilterEnabled,
        isValidEscapesFlow = isValidEscapesFlow,
        suggestedBasis = overrideHotel.suggestedBasis,
        isValidMergedDayUseOvernightFlow = isValidMergedDayUseOvernightFlow
      )(request, contextHolder)

      if (request.isContentOnly) {
        roomDetail.map { rd =>
          val roomEnriched = if (request.room.flatMap(_.showChildRoomInContentOnly).getOrElse(false)) {
            getRoomInfosContentOnlyWithChildRoom(rd.roomsInformation)
          } else {
            getRoomInfosContentOnly(rd.roomsInformation)
          }
          EnrichedHotelWithContent(
            overrideHotel.propertyId,
            0,
            0,
            0,
            None,
            None,
            roomEnriched,
            None,
            None,
            None,
            None,
            None,
            Nil,
            overrideHotel.tooltip,
            overrideHotel.ratePlanSelection,
            overrideHotel.enableAPS,
            pseudoCouponMessage = None,
            dfFeatures = None,
            promotionEligible = None,
            taxType = overrideHotel.taxType,
            supplierSummaries = overrideHotel.supplierSummaries,
            isMseProperty = Option(isMseProperty),
            suggestedRooms = suggestedRoomEnriched.regularRooms,
            maxPointsMax = hotel.maxPointsMax,
            maxPointsMaxBadge = hotel.maxPointsMaxBadge,
            activeHotelsInCity = overrideHotel.activeHotelsInCity,
            internalUtilityData =
              InternalUtilityData(internalUtilityDataFromDF = overrideHotel.internalUtilityDataFromDF),
            isOnlyAlternativeRoom = overrideHotel.isOnlyAlternativeRoom
          )
        }
      } else {
        val selectedRoomDetail = request.room.flatMap(_.externalServiceMode) match {
          case Some(externalServiceMode) if request.isPricingOnly =>
            Some(
              RoomInformationResponse(
                1,
                overrideHotel.rooms
                  .flatMap(room => room.typeId)
                  .distinct
                  .map(typeId =>
                    MasterRoomResponse(
                      RoomMetadata(typeId),
                      Vector(RoomMetadata(typeId)),
                      Vector.empty,
                      Vector.empty,
                      None,
                      Vector.empty,
                      Vector.empty,
                      None,
                      None,
                      None,
                      None,
                      None,
                      None,
                      Seq.empty,
                      None,
                      None,
                      Vector.empty
                    )
                  )
                  .toVector
              )
            )
          case _ =>
            roomDetail
        }

        val includeSoldOutRooms = request.featureFlag.flatMap(_.includeSoldOutRooms).getOrElse(false) &&
          !soldOutRoomsSetting.blackListProperties.contains(overrideHotel.propertyId)

        val enrichedMasterRooms = getEnrichedMasterRooms(
          overrideHotel.rooms.toList,
          selectedRoomDetail,
          isMseProperty,
          hotel.propertyId,
          hotel.countryId,
          overrideHotel.suggestedPrice,
          includeSoldOutRooms,
          rateCategoriesMap = rateCategoriesMap,
          rateCategoryIdsWithChildRateEnabled = rateCategoryIdsWithChildRateEnabled,
          customizableRoomGridOptions = hotel.customizableRoomGridOptions,
          isExternalVipDisplayEnabled = isExternalVipDisplayEnabled,
          isRoomFilterEnabled = isRoomFilterEnabled,
          isValidEscapesFlow = isValidEscapesFlow,
          suggestedBasis = overrideHotel.suggestedBasis,
          isValidMergedDayUseOvernightFlow = isValidMergedDayUseOvernightFlow
        )(request, contextHolder)

        val enrichedSoldOutRooms = soldOutPricesService.enrichSoldOutRooms(
          enrichedMasterRooms.soldOutRooms,
          request.context.locale,
          soldOutRoomsPrice
        )

        val cheapestHourlyRoomEnriched = if (request.featureFlag.flatMap(_.showCheapestHourlyRate).getOrElse(false)) {
          Some(
            getEnrichedMasterRooms(
              overrideHotel.cheapestHourlyRoom.toList,
              roomDetail,
              isMseProperty,
              hotel.propertyId,
              hotel.countryId,
              overrideHotel.suggestedPrice,
              isSuggestedRooms = false,
              rateCategoriesMap = rateCategoriesMap,
              rateCategoryIdsWithChildRateEnabled = rateCategoryIdsWithChildRateEnabled,
              customizableRoomGridOptions = hotel.customizableRoomGridOptions,
              isExternalVipDisplayEnabled = isExternalVipDisplayEnabled,
              isRoomFilterEnabled = isRoomFilterEnabled,
              isValidEscapesFlow = isValidEscapesFlow,
              suggestedBasis = overrideHotel.suggestedBasis,
              isValidMergedDayUseOvernightFlow = isValidMergedDayUseOvernightFlow
            )(request, contextHolder)
          )
        } else None

        Option(
          overrideHotel.toWithAllContent(
            enrichedMasterRooms.regularRooms,
            singleRoomNonHotelAccommodation,
            suggestedRoomEnriched.regularRooms,
            Option(isMseProperty),
            enrichedSoldOutRooms,
            roomBundles,
            cheapestHourlyRoomEnriched.flatMap(_.regularRooms.headOption)
          )
        )
      }
    }
  }

  private def masterRoomToEnrichedMasterRoom(
      masterRoomInfo: MasterRoomResponse,
      childRooms: List[EnrichedChildRoom] = Nil,
      isGreatForFamiliesMRS: Boolean = false,
      customizableRoomGridOptions: Map[MasterRoomTypeId, List[CustomizableRoomGridOptions]] = Map.empty
  ): EnrichedMasterRoom = {

    val masterRoomId = masterRoomInfo.metadata.roomId
    EnrichedMasterRoom(
      masterRoomInfo.metadata.maxOccupants.getOrElse(0),
      masterRoomInfo.metadata.maxExtraBeds.getOrElse(0),
      masterRoomId,
      masterRoomInfo.metadata.roomName,
      masterRoomInfo.metadata.localizedRoomName,
      masterRoomInfo.images,
      masterRoomInfo.facilities,
      masterRoomInfo.bedConfiguration,
      childRooms,
      masterRoomInfo.facilityGroups,
      masterRoomInfo.features,
      Nil,
      masterRoomInfo.isAllowChildren,
      masterRoomInfo.isYcsMaster,
      if (isGreatForFamiliesMRS) Some(FamilyConstant.SuitabilityTypeFamily) else masterRoomInfo.suitabilityType,
      masterRoomInfo.metadata.hotelRoomTypeAlternateName,
      masterRoomInfo.metadata.numberOfRooms,
      masterRoomInfo.metadata.roomSizeInclTerrace,
      masterRoomInfo.metadata.extrabedChildAge,
      masterRoomInfo.metadata.maxInfantInRoom,
      masterRoomInfo.localizations,
      masterRoomInfo.metadata.roomName.map(_.toLowerCase.contains(FamilyConstant.Dormitory)),
      masterRoomInfo.roomReviewInformation,
      FamilyFeaturesService.getRoomSuitabilityTypes(masterRoomInfo, isGreatForFamiliesMRS),
      styleName = masterRoomInfo.metadata.roomStyle.map(_.name),
      isRoomDayUsed = masterRoomInfo.metadata.isRoomDayUsed,
      topFacilities = masterRoomInfo.topFacilities,
      roomLicenseId = masterRoomInfo.metadata.roomLicenseId,
      customizableRoomGridOptions = customizableRoomGridOptions.get(masterRoomId.toString),
      roomDescriptionAI = masterRoomInfo.metadata.roomDescriptionAI,
      videos = masterRoomInfo.videos,
      aiRoomShortDescription = masterRoomInfo.aiRoomShortDescription
    )
  }

  private def getReviews(request: PropertyRequest, mandatory: Boolean = false)(implicit
      ec: ExecutionContext,
      rackingContext: ContextHolder
  ): Future[mutable.Map[HotelIdType, ReviewResponse]] = {
    val reviewCommentResponse          = reviewCommentService.process(request, mandatory)
    val reviewScoresResponse           = reviewScoreService.process(request, mandatory)
    val reviewSnippetResponse          = reviewSnippetService.process(request, mandatory)
    val reviewTotalResponse            = reviewTotalService.process(request, mandatory)
    val reviewFiltersResponse          = reviewFiltersService.process(request, mandatory)
    val reviewPositiveMentionsResponse = reviewPositiveMentionsService.process(request, mandatory)
    val reviewCountersResponse         = reviewCountersService.process(request, mandatory)

    for {
      comments               <- reviewCommentResponse
      reviewScores           <- reviewScoresResponse
      reviewSnippet          <- reviewSnippetResponse
      reviewTotal            <- reviewTotalResponse
      reviewFilters          <- reviewFiltersResponse
      reviewPositiveMentions <- reviewPositiveMentionsResponse
      reviewCounters         <- reviewCountersResponse
    } yield {
      val result = JMapBuilder.empty[HotelIdType, ReviewResponse]()
      request.context.propertyIds.foreach { propertyId =>
        val reviewExisted = comments.contains(propertyId) ||
          reviewScores.contains(propertyId) ||
          reviewSnippet.contains(propertyId) ||
          reviewTotal.contains(propertyId) ||
          reviewFilters.contains(propertyId) ||
          reviewPositiveMentions.contains(propertyId) ||
          reviewCounters.contains(propertyId)
        if (reviewExisted) {
          val resp = ReviewResponse(
            comments.get(propertyId),
            reviewScores.get(propertyId),
            reviewSnippet.get(propertyId),
            reviewTotal.get(propertyId),
            reviewFilters.get(propertyId),
            reviewPositiveMentions.get(propertyId),
            reviewCounters.get(propertyId)
          )
          result.put(propertyId, resp)
        }
      }
      result
    }
  }

  private def defaultEnrichedHotelWithOutContent(propertyId: Long): EnrichedHotelWithOutContent =
    EnrichedHotelWithOutContent(
      propertyId,
      cityId = 0,
      countryId = 0,
      areaId = 0,
      cheapestRoom = None,
      needOccupancySearch = None,
      rooms = Seq.empty,
      isReady = Option(true),
      supplierId = None,
      isDisplayAsAmount = None,
      suggestedPrice = None,
      tooltip = PropertyTooltip(None, None),
      ratePlanSelection = List.empty,
      enableAPS = false,
      hotelSummary = None,
      paxSetting = None,
      pseudoCouponMessage = None,
      dfFeatures = None,
      promotionEligible = None,
      activeHotelsInCity = None,
      internalUtilityDataFromDF = None,
      suggestedBasis = None
    )

  /** on PropertyRequest or BookingRequest * */
  private def onPropertyRequest[A](pass: => Future[mutable.Map[HotelIdType, A]], inverse: Boolean = false)(implicit
      request: PropertyRequest
  ): Future[mutable.Map[HotelIdType, A]] =
    if ((request.isSearchType(SearchTypes.Property) || request.isSearchType(SearchTypes.Booking)) ^ inverse) {
      pass
    } else {
      Future.successful(JMapBuilder.empty[HotelIdType, A]())
    }

  private[ClusterService] def getRateCategories()(implicit
      ec: ExecutionContext,
      request: PropertyRequest,
      contextHolder: ContextHolder
  ) =
    if (
      request.rateCategories.exists(_.escapeRateCategories.nonEmpty) && request.isSearchType(SearchTypes.Properties)
    ) {
      rateCategoriesService.process(request, true)
    } else {
      onPropertyRequest(rateCategoriesService.process(request, true))
    }

  // scalastyle:off
  private[ClusterService] def propertyFilters(property: Option[EnrichedHotelWithContent])(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder,
      sfpp: PropertyRequest,
      cmsService: PAPICMSService,
      languageRepository: LanguageRepository
  ): Future[Option[PropertyFilters]] = {

    val language = SfModelMapper.localeToLanguageUse(sfpp.context.locale)

    val shouldUseCombineList = sfpp.isSearchType(SearchTypes.Property) && property.map(_.suggestedRooms).nonEmpty &&
      contextHolder.requestContextO
        .flatMap(_.featureFlag)
        .exists(
          _.flags.exists(_.getOrElse(FeatureFlagKey.GreatForFamiliesMRS, false))
        )

    val combineChildrenRoomList = if (shouldUseCombineList) {
      Some(
        (property.map(_.masterRooms).getOrElse(Seq.empty) ++ property.map(_.suggestedRooms).getOrElse(List.empty))
          .flatMap(_.childrenRooms)
      )
    } else {
      property.map(_.masterRooms.flatMap(_.childrenRooms))
    }

    val enablePropertyFilterSyncId = contextHolder.experimentContext.isBVariant(
      contextHolder.allocationContext.enablePropertyFilterSyncId
    )
    val isReplaceDroneIcon =
      contextHolder.experimentContext.isBVariant(contextHolder.allocationContext.isReplaceDroneIcon)
    combineChildrenRoomList.map(_.flatMap(_.filterTags).distinct) match {
      case Some(availableRoomLevelFilters) =>
        val filterTagsFuture = availableRoomLevelFilters.map { filter =>
          val (cmsId, symbol) =
            if (isReplaceDroneIcon) PropertyFilter.filterToCMSMappingV2(filter)
            else PropertyFilter.filterToCMSMapping(filter)
          val groupId: Option[String] = Some(PropertyFilter.getFilterGroupId(filter))
          val syncId: Option[String] = if (enablePropertyFilterSyncId) {
            GroupMatrixResultMapping.getPropertyFilterSyncId(filter)
          } else None
          cmsService
            .toCmsTextFuture(cmsId, language)
            .map(filterName => FilterTag(filter, filterName.getOrElse(""), symbol, groupId = groupId, syncId = syncId))
        }
        val filterGroupsFuture = filterGroups(availableRoomLevelFilters, language)

        for {
          filter       <- cmsService.toCmsTextFuture(CMSMapping.RoomFilterTitle, language)
          result       <- Future.sequence(filterTagsFuture)
          filterGroups <- Future.sequence(filterGroupsFuture)
        } yield Some(
          PropertyFilters(
            filter.getOrElse(""),
            result.sortWith((filter1, filter2) =>
              PropertyFilter.filterOrder(filter1.id) < PropertyFilter.filterOrder(filter2.id)
            ),
            Some(
              filterGroups.sortWith((group1, group2) =>
                PropertyFilter.getFilterGroupOrder(group1.id) < PropertyFilter.getFilterGroupOrder(group2.id)
              )
            )
          )
        )
      case None => Future.successful(None)
    }
  }

  private def filterGroups(
      availableRoomLevelFilters: Seq[String],
      language: LanguageIdType
  )(implicit cmsService: PAPICMSService, contextHolder: ContextHolder): Seq[Future[FilterGroup]] = {
    val groups = availableRoomLevelFilters
      .map(filter => PropertyFilter.getFilterGroupId(filter))
      .distinct

    groups.map { group =>
      cmsService
        .toCmsTextFuture(PropertyFilter.filterGroupToCMSMapping(group), language)
        .map(filterGroupName => FilterGroup(group, filterGroupName.getOrElse("")))
    }
  }

  // scalastyle:on
  def getCheapestRoomTypeId(
      childRooms: Seq[EnrichedChildRoom],
      hotelEnrichedWithBasisAndContenRoomIds: Option[EnrichedHotelWithContent]
  ): Option[Long] =
    hotelEnrichedWithBasisAndContenRoomIds match {
      case Some(hotelEnrichedInput) =>
        val cheapestRoomUid = hotelEnrichedInput.cheapestRoom.map(_.uid)
        childRooms.find(cr => cr.uid == cheapestRoomUid).flatMap(_.typeId)
      case _ => None
    }

  def updateNhaWithCheapestMasterRoom(
      cheapestRoomTypeId: Long,
      nonHotelAccommodationOpt: Option[NonHotelAccommodation]
  ): Option[NonHotelAccommodation] = {
    val cheapestNonHotelAccommodationMasterRoom = nonHotelAccommodationOpt match {
      case Some(nonHotelAccommodation) if nonHotelAccommodation.masterRooms.nonEmpty =>
        nonHotelAccommodation.masterRooms
          .filter(mRoom =>
            mRoom.roomTypeId == cheapestRoomTypeId
              || mRoom.childrenRooms.exists(childRoom => childRoom.roomTypeId == cheapestRoomTypeId)
          )
          .take(1)
      case _ => Vector.empty
    }
    nonHotelAccommodationOpt.map(nha => nha.copy(masterRooms = cheapestNonHotelAccommodationMasterRoom))
  }

  private def getDFResponseF(
      sfRequest: List[PropertyAPIRequestModel],
      request: PropertyRequest,
      propertyIdList: List[PropertyId],
      transformHotel: Seq[
        WithPropertyToken[WithHotelIdMap[EnrichedHotelWithOutContent]]
      ] => scala.collection.mutable.Map[HotelIdType, EnrichedHotelWithOutContent]
  )(implicit
      contextHolder: ContextHolder
  ): Future[(DFMetaResult, mutable.Map[PropertyId, EnrichedHotelWithOutContent])] =
    if (request.isContentOnly) {
      val enrichedHotelWithOutContentMap: mutable.Map[Long, EnrichedHotelWithOutContent] = propertyIdList
        .map { propertyId =>
          val enrichedHotel = defaultEnrichedHotelWithOutContent(propertyId)
          propertyId -> enrichedHotel
        }
        .toJMap()
      Future.successful((DFMetaResult(), enrichedHotelWithOutContentMap))
    } else {
      val (mandatory, ignoreChunkFailure, ignoreAllChunkFailure) = request.searchType match {
        case Some(SearchTypes.Complex) =>
          val ignoreChunkFailureConfig = dfPropertyWithCacheToken.config.ignoreChunkFailure
          if (request.context.isAPORequest) {
            (false, ignoreChunkFailureConfig, true)
          } else {
            (true, ignoreChunkFailureConfig, false)
          }
        case _ => (true, false, false)
      }
      reportingService.report(
        "normal-count",
        sfRequest.flatMap(_.propertySearchRequest.propertyIds).size,
        request.context.measureTags ++ Map(
          "cheapest" -> sfRequest.exists(_.propertySearchRequest.cheapestOnly).toString
        )
      )
      Future
        .sequence(sfRequest.map { dfRequest =>
          dfPropertyWithCacheToken
            .process(dfRequest.propertySearchRequest, mandatory, ignoreChunkFailure, ignoreAllChunkFailure, true)
        })
        .map { results =>
          val metaResult   = results.map(_.meta).headOption.getOrElse(DFMetaResult())
          val hotelMapping = transformHotel(results)
          (metaResult, hotelMapping)
        }
    }

  private def getPropertiesLongStayPromotion(
      propertyIds: List[PropertyId]
  ): Future[Map[PropertyId, PropertyLongStayPromotionResponse]] =
    Future
      .sequence(propertyIds.map { propertyId =>
        priceStreamService.getPropertyLongStayPromotion(propertyId).map(promos => (propertyId, promos))
      })
      .map(_.toMap)

  private def isRocketMilesSortingFlagsPresent(featureFlagOpt: Option[FeatureFlagRequest]): Boolean =
    featureFlagOpt.exists(_.useTotalNetWithSupplierFinancialsRoomSorting.getOrElse(false)) ||
      featureFlagOpt.exists(_.useBmpFirstSupplierFinancialsWithTotalNetPriceSorting.getOrElse(false))

  // scalastyle:off
  private[ClusterService] def searchCommon(
      sfRequest: List[PropertyAPIRequestModel],
      hotelToCityMap: Map[HotelIdType, CityIdType]
  )(implicit
      ec: ExecutionContext,
      request: PropertyRequest,
      cmsService: PAPICMSService,
      languageRepository: LanguageRepository,
      contextHolder: ContextHolder
  ): Future[WithPropertyToken[Vector[PricingAndContent]]] = {
    val propertyIdList = request.context.propertyIds

    def transformHotel(
        results: Seq[WithPropertyToken[WithHotelIdMap[EnrichedHotelWithOutContent]]]
    ): mutable.Map[HotelIdType, EnrichedHotelWithOutContent] =
      results.map(_.data).foldLeft(JMapBuilder.empty[HotelIdType, EnrichedHotelWithOutContent]())((m, i) => m ++= i)

    val dfResponse = tracer.withSpanM("dfResponse") {
      getDFResponseF(sfRequest, request, propertyIdList, transformHotel _)
    }

    val priceStreamEngagementF = tracer.withSpanM("priceStreamService.getEngagement") {
      if (request.engagementRequest.nonEmpty)
        priceStreamService
          .getEngagement(
            request.context.propertyIds,
            request.pricing.map(_.checkIn),
            request.pricing.map(_.lengthOfStay)
          )
          .map(Some(_))
      else Future.successful(None)
    }

    val roomInformation = tracer.withSpanM("roomInformation")(getRoomInformation(dfResponse))
    val rateCategoriesResponse: Future[mutable.Map[HotelIdType, RateCategoriesResponse]] =
      tracer.withSpanM("rateCategoriesResponse")(getRateCategories())
    val bathInformationResponse = tracer.withSpanM("bathInformationResponse") {
      onPropertyRequest(bathInformationService.process(request, false))
    }
    val transportationInformationResponse = tracer.withSpanM("transportationInformationResponse") {
      onPropertyRequest(transportationInformationService.process(request, false))
    }
    val featuresResponse = tracer.withSpanM("featuresResponse") {
      onPropertyRequest(featureService.process(request, false))
    }
    val hotelInfoResponse = tracer.withSpanM("hotelInfoResponse") {
      onPropertyRequest(hotelInfoService.process(request, true))
    }
    val hotelInfoSummaryResponse = tracer.withSpanM("hotelInfoSummaryResponse") {
      onPropertyRequest(hotelInformationSummaryService.process(request, true))
    }
    val imagesResponse  = tracer.withSpanM("imagesResponse")(onPropertyRequest(imageService.process(request, true)))
    val reviewsResponse = tracer.withSpanM("reviewsResponse")(onPropertyRequest(getReviews(request, false)))
    val localInformationResponse = tracer.withSpanM("localInformationResponse") {
      onPropertyRequest(localInformationService.process(request, false))
    }
    val checkInOutResponse = tracer.withSpanM("checkInOutResponse") {
      onPropertyRequest(checkInOutService.process(request, false))
    }
    val synopsisResponse = tracer.withSpanM("synopsisResponse") {
      onPropertyRequest(synopsisService.process(request, false))
    }
    val engagement = tracer.withSpanM("engagement")(onPropertyRequest(engagementService.process(request, false)))
    val experience = tracer.withSpanM("experience")(onPropertyRequest(experienceService.process(request, false)))
    val highlightsResponse = tracer.withSpanM("highlightsResponse") {
      onPropertyRequest(highlightService.process(request, false))
    }
    val nonHotelAccommodationResponse = tracer.withSpanM("nonHotelAccommodationResponse") {
      onPropertyRequest(nonHotelAccommodationService.process(request, false))
    }
    val contentSummaryResponse = tracer.withSpanM("contentSummaryResponse")(getContentSummary(dfResponse))
    val priceStreamPropertyMetaInfoF =
      if (request.isSearchType(SearchTypes.Property) | request.isSearchType(SearchTypes.Booking))
        priceStreamService.getPropertyMetaInfo(request)
      else Future.successful(PropertyMetaInfoResponse())

    val priceStreamCityMetaInfoF =
      priceStreamService.getCityMetaInfoWithPropertyRequest(request, hotelToCityMap, contextHolder)
    val priceStreamLongStayPromotionF =
      if (
        contextHolder.requestContextO.flatMap(_.featureFlag).exists(_.showLongStayPromotionGrid.contains(true))
        && request.isSearchType(SearchTypes.Property)
      ) {
        contextHolder.experimentContext.runABExperiment(
          contextHolder.allocationContext.pricestreamPropertyLongStayPromotionNegativeTest,
          getPropertiesLongStayPromotion(propertyIdList),
          Future.successful(Map.empty[PropertyId, PropertyLongStayPromotionResponse])
        )
      } else
        Future.successful(Map.empty[PropertyId, PropertyLongStayPromotionResponse])

    val hasPersonalizedFlag = contextHolder.requestContextO
      .flatMap(_.featureFlag)
      .exists(
        _.flags.exists(_.getOrElse(FeatureFlagKey.PersonalizedInformation, false))
      )
    val rateCategoryResponse = tracer.withSpanM("rateCategoryResponse") {
      onRequestForRateCategory(request, rateCategoryService, dfResponse)(ec, contextHolder, reportingService)
    }

    val personalizedInformationResponse = tracer.withSpanM("personalizedInformationResponse") {
      if (hasPersonalizedFlag) onPropertyRequest(personalizedInformationService.process(request, true), true)
      else Future.successful(mutable.Map.empty[types.HotelIdType, PersonalizedInformation])
    }

    val soldOutPricesResponse = tracer.withSpanM("soldOutPricesResponse") {
      soldOutPricesService.getSoldOutRoomsPrice(propertyIdList, request.isSSR, request.pricing)
    }

    val paymentTypesF =
      if (
        isRocketMilesSortingFlagsPresent(contextHolder.requestContextO.flatMap(_.featureFlag)) &&
        contextHolder.requestContextO.flatMap(_.featureFlag).exists(_.showPaymentTypes.getOrElse(false))
      ) {
        val languageId = SfModelMapper.localeToLanguageUse(request.context.locale)
        paymentTypesService.getPaymentCategories(languageId, request.context.origin, request.context.platform)
      } else Future.successful(Seq.empty)

    val roomRecoF = getRecommendedRooms(request)

    val result = for {
      df <- contextHolder.papiSearchResultTracker.stepF(dfResponse)(FetchRoomFromDF, ResultTracking.dfRoomsResponse)
      features         <- featuresResponse
      hotelInfo        <- hotelInfoResponse
      hotelInfoSummary <- hotelInfoSummaryResponse
      images           <- imagesResponse
      reviews          <- reviewsResponse
      roomDetail <- contextHolder.papiSearchResultTracker
        .stepF(roomInformation)(FetchMasterRoomFromContent, ResultTracking.roomInfoResponse(df))
      contentSummary               <- contentSummaryResponse
      localInfo                    <- localInformationResponse
      eng                          <- engagement
      exp                          <- experience
      highlights                   <- highlightsResponse
      nonHotelAccommodations       <- nonHotelAccommodationResponse
      checkInOut                   <- checkInOutResponse
      synopsis                     <- synopsisResponse
      personalizedInformation      <- personalizedInformationResponse
      soldOutPrices                <- soldOutPricesResponse
      rateCategories               <- rateCategoriesResponse
      rateCategory                 <- rateCategoryResponse
      priceStreamEngagement        <- priceStreamEngagementF
      bathInformationMap           <- bathInformationResponse
      transportationInformationMap <- transportationInformationResponse
      priceStreamPropertyMetaInfo  <- priceStreamPropertyMetaInfoF
      priceStreamCityMetaInfo      <- priceStreamCityMetaInfoF
      priceStreamLongStayPromotion <- priceStreamLongStayPromotionF
      paymentTypes                 <- paymentTypesF
      recommendedRooms             <- roomRecoF
    } yield {

      val suggestMapping = sfRequest.flatMap(r => r.propertySearchRequest.propertyIds.map(id => id -> r.suggestPrice))(
        collection.breakOut
      ): Map[HotelIdType, SuggestPrice]

      val recommendedRoomsMap = recommendedRooms.toMap

      val futures = propertyIdList.map { propertyId =>
        val roomDetailResolved =
          request.searchType.flatMap(searchType =>
            searchType match {
              case SearchTypes.Property | SearchTypes.Booking => roomDetail.get(propertyId)
              case SearchTypes.Properties | SearchTypes.ComprehensiveComplex | SearchTypes.Complex =>
                contentSummary.get(propertyId).flatMap(_.rooms)
              case _ => None
            }
          )

        val synopsisData = (synopsis.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.synopsis)).headOption
        val hotelSummaryResult =
          (hotelInfoSummary.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.summary)).headOption
        val h: EnrichedHotelWithOutContent = df._2.getOrElse(propertyId, defaultEnrichedHotelWithOutContent(propertyId))

        val breakfastPriceFromHotelSetting = h.breakfastPriceFromHotelSetting

        val roomDetailsForEnrichment =
          if (request.isSSR.contains(true)) None else roomDetailResolved
        val whitelabelKey = contextHolder.requestContextO.flatMap(_.context).flatMap(_.whiteLabelKey)
        val deviceTypeId  = contextHolder.requestContextO.flatMap(_.context).flatMap(_.deviceTypeId)
        val origin        = contextHolder.requestContextO.flatMap(_.context).map(_.origin)

        val isExternalVipDisplayEnabled =
          whiteLabelService.isExternalVipDisplayEnabled(
            origin,
            deviceTypeId,
            whitelabelKey,
            contextHolder.globalContext.getEnv
          )
        val isRoomFilterEnabled =
          whiteLabelService.isBiggerRoomFilterEnabled(
            origin,
            deviceTypeId,
            whitelabelKey,
            contextHolder.globalContext.getEnv
          )
        val isAsoFilterMigration =
          contextHolder.experimentContext.isBVariant(contextHolder.allocationContext.asoFilterMigration)
        val isValidEscapesFlow = EscapesSummaryHelper.isValidEscapesFlow(
          request,
          isRoomFilterEnabled,
          isAsoFilterMigration
        )
        val isValidMergedDayUseOvernightFlow =
          request.featureFlag.flatMap(_.mergeDayUseOffersWithOvernight).getOrElse(false)
        val roomBundle = enrichRoomBundleService.getRoomBundle(
          h,
          roomDetailResolved,
          isExternalVipDisplayEnabled,
          getEnrichedMasterRooms
        )

        val rateCategoriesResponse              = rateCategories.get(propertyId)
        val rateCategoriesMap                   = getRateCategoriesLookUp(rateCategoriesResponse)
        val rateCategoryIdsWithChildRateEnabled = getRateCategoryIdsWithChildRateEnabled(rateCategoriesResponse)

        val hotelEnriched = getEnrichedHotel(
          request,
          h,
          roomDetailsForEnrichment,
          hotelSummaryResult.map(_.singleRoomNonHotelAccommodation),
          synopsisData,
          soldOutPrices.getOrElse(propertyId, List.empty),
          rateCategoriesMap,
          roomBundle,
          rateCategoryIdsWithChildRateEnabled,
          isExternalVipDisplayEnabled,
          isRoomFilterEnabled,
          isValidEscapesFlow,
          isValidMergedDayUseOvernightFlow
        )

        val hotelEnrichedWithEscapesInfo = hotelEnriched.map(
          _.copy(
            cheapestStayPackageRatePlans = h.cheapestStayPackageRatePlans,
            cheapestEscapesRoom = h.cheapestEscapesRoom
          )
        )

        contextHolder.papiSearchResultTracker.step(hotelEnrichedWithEscapesInfo)(EnrichedRooms, enrichedHotelResponse)

        // TODO: This hard code(PRPN) follow SFA-562 -> Real Logic Option(suggest._2),
        val hotelEnrichedWithBasisAndContenRoomIds = hotelEnrichedWithEscapesInfo.map { h =>
          val contentRoomIds =
            roomDetailResolved.map(_.roomsInformation.map(_.metadata.roomId).toSet).getOrElse(Set.empty)
          val contentYCSRoomIds = roomDetailResolved
            .map(
              _.roomsInformation
                .filter(r =>
                  r.isYcsMaster.getOrElse(false) && request.pricing
                    .exists(_.guestPerRooms <= r.metadata.maxOccupants.getOrElse(0))
                )
                .map(_.metadata.roomId)
                .toSet
            )
            .getOrElse(Set.empty)
          val contentRoomIdRoomNames = roomDetailResolved
            .map(_.roomsInformation.map(r => r.metadata.roomId -> r.metadata.roomName.getOrElse("")))
            .getOrElse(Vector.empty)
          val contentRoomIdRoomNameMap = contentRoomIdRoomNames.filter(_._2.nonEmpty).toMap[Long, String]

          h.copy(
            suggestedBasis = h.suggestedBasis,
            suggestedPrice = h.suggestedPrice,
            internalUtilityData = h.internalUtilityData.copy(internalUtilityDataFromContent =
              Some(
                InternalUtilityDataFromContent(
                  contentRoomIds = contentRoomIds,
                  contentYCSRoomIds = contentYCSRoomIds,
                  roomNameMap = contentRoomIdRoomNameMap
                )
              )
            ),
            displayPaymentTypes = Some(paymentTypes)
          )
        }
        val hotelImages = (images.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.images)).headOption

        val propertyReviews = (reviews.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.reviews)).headOption

        val hotelWithTopSellingPoints = setTopSellingPointsData(
          tspRequest = request.topSellingPointRequest,
          hotel = hotelEnrichedWithBasisAndContenRoomIds,
          reviews = propertyReviews
        )

        val cheapestRoomTypeId = getCheapestRoomTypeId(h.rooms, hotelEnrichedWithBasisAndContenRoomIds).getOrElse(0L)

        val hotelWithSupportChildRateData =
          setSupportChildRateData(rateCategoriesResponse, hotelWithTopSellingPoints)

        val hotelWithRecommendationMarked =
          markRecommendedRoom(recommendedRoomsMap, hotelWithSupportChildRateData)

        // fixme :: try content from summary and other bits
        propertyFilters(hotelEnrichedWithBasisAndContenRoomIds).map { filters =>
          PricingAndContent(
            propertyId = propertyId,
            reviews = propertyReviews,
            hotel = hotelWithRecommendationMarked,
            hotelInfo = (hotelInfo.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.information)).headOption,
            images = hotelImages,
            highlights =
              (highlights.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.highlights)).headOption,
            features = (features.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.features)).headOption,
            hotelSummary = hotelSummaryResult,
            localInformation =
              (localInfo.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.localInformation)).headOption,
            checkInOut =
              (checkInOut.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.checkInOut)).headOption,
            synopsis = synopsisData,
            engagement = (eng.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.engagement)).headOption
              .map(engResult =>
                toEngagement(engResult, priceStreamEngagement.flatMap(_.lastBook.find(_.propertyId == propertyId)))
              ),
            experience = (exp.get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.experiences)).headOption,
            nonHotelAccommodation = updateNhaWithCheapestMasterRoom(
              cheapestRoomTypeId,
              (nonHotelAccommodations
                .get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.nonHotelAccommodation)).headOption
            ),
            availableFilters = propertyFilterSortingService.getSortedPropertyFilters(filters, request),
            uniqueSellingPoints = contentSummary.get(propertyId).flatMap(_.uniqueSellingPoints),
            personalizedInformation = personalizedInformation.get(propertyId),
            rateCategory = rateCategory,
            bathInformation = (bathInformationMap
              .get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.bathInformation)).headOption,
            roomBundle = roomBundle,
            transportationInformation = (transportationInformationMap
              .get(propertyId) ++ contentSummary.get(propertyId).flatMap(_.transportationInformation)).headOption,
            breakfastPriceFromHotelSetting = breakfastPriceFromHotelSetting,
            propertyMetaInfoResponse = priceStreamPropertyMetaInfo.response.find(_.propertyId == propertyId),
            lastYearBookingCount =
              priceStreamPropertyMetaInfo.response.find(_.propertyId == propertyId).flatMap(_.lastYearBookingCount),
            propertyMetaRanking =
              priceStreamPropertyMetaInfo.response.find(_.propertyId == propertyId).map { metaInfo =>
                PropertyRanking(
                  propertyId = propertyId.toInt,
                  numberOfHotels = metaInfo.hotelMetaRanking.map(_.numberOfHotels).getOrElse(0),
                  metrics = metaInfo.hotelMetaRanking
                    .map(_.metrics)
                    .map(ranks => ranks.map(rank => RankMetric(rank.metric_name, rank.rank, rank.absoluteValue)))
                    .getOrElse(Vector.empty)
                )
              },
            uniqueVisitorPerCity = priceStreamCityMetaInfo.cityMetaInfo.flatMap(
              _.find(_.cityId == hotelToCityMap(propertyId)).flatMap(_.uniqueVisitor)
            ),
            rateCategories =
              if (request.isSSR.contains(true)) contentSummary.get(propertyId).flatMap(_.rateCategories) else None,
            longStayRoomId = h.longStayRoomId,
            longStayPromotions =
              priceStreamLongStayPromotion.get(propertyId).map(_.longStayPromotions).getOrElse(Vector.empty),
            propertySearchToken = h.propertySearchToken
          )
        }
      }(collection.breakOut)
      Future.sequence(futures).map(r => WithPropertyToken[Vector[PricingAndContent]](df._1, r.toVector))
    }
    result.flatMap(identity)
  }

  private def getContentSummaryResponseSeqF(
      df: (DFMetaResult, scala.collection.Map[types.HotelIdType, EnrichedHotelWithOutContent])
  )(implicit
      ec: ExecutionContext,
      request: PropertyRequest,
      contextHolder: ContextHolder
  ): Future[mutable.Map[types.HotelIdType, PropertyResponse]] =
    onPropertyRequest(summaryService.process(updateRequestWithRoomFilterCriteria(df), true), true)

  private def getContentSummary(
      dfResponse: Future[(DFMetaResult, scala.collection.Map[types.HotelIdType, EnrichedHotelWithOutContent])]
  )(implicit ec: ExecutionContext, request: PropertyRequest, contextHolder: ContextHolder) = {
    val isContentCallSequence = request.searchType.exists { searchType =>
      val nonComplexSequential = searchType != SearchTypes.Complex
      nonComplexSequential
    } && request.pricing.isDefined

    if (isContentCallSequence) {
      dfResponse.flatMap(getContentSummaryResponseSeqF)
    } else {
      tracer.withSpanM("process") {
        onPropertyRequest(summaryService.process(request, true), true)
      }
    }
  }

  private def getRoomInformation(
      dfResponse: Future[(DFMetaResult, scala.collection.Map[types.HotelIdType, EnrichedHotelWithOutContent])]
  )(implicit ec: ExecutionContext, request: PropertyRequest, contextHolder: ContextHolder) =
    onPropertyRequest {
      val isContentCallSequence =
        request.searchType.exists(tpe => tpe == SearchTypes.Property || tpe == SearchTypes.Booking)

      if (isContentCallSequence) {
        dfResponse.flatMap(getRoomInformationSeqF)
      } else {
        timer(MeasurementKey.ContentRoomDetails, Map("isSequential" -> "false"), 1)
        roomDetailService.process(request, true)
      }
    }

  private def getRoomInformationSeqF(
      df: (DFMetaResult, scala.collection.Map[types.HotelIdType, EnrichedHotelWithOutContent])
  )(implicit
      ec: ExecutionContext,
      request: PropertyRequest,
      contextHolder: ContextHolder
  ): Future[mutable.Map[HotelIdType, RoomInformationResponse]] = {
    val updateRequestWithRoomFilters: PropertyRequest = updateRequestWithRoomFilterCriteria(df)

    val roomIds =
      updateRequestWithRoomFilters.room.flatMap(_.filterCriteria.map(_.flatMap(_.roomIds))).getOrElse(List.empty[Long])
    timer(
      MeasurementKey.ContentRoomDetails,
      Map("isSequential" -> "true", "hasRoomFilter" -> roomIds.nonEmpty.toString),
      1
    )
    if (request.isContentOnly || roomIds.nonEmpty) {
      roomDetailService.process(updateRequestWithRoomFilters, true)
    } else {
      Future.successful(mutable.Map.empty)
    }
  }

  import cats.implicits._

  private[service] def updateRequestWithRoomFilterCriteria(
      df: (DFMetaResult, collection.Map[HotelIdType, EnrichedHotelWithOutContent])
  )(implicit request: PropertyRequest, contextHolder: ContextHolder): PropertyRequest = {
    val roomFilterCriteria: List[RoomFilterCriteria] = df._2.values.map { h =>
      // DF always sends masterTypeId, if its 0 or NULL in DB, then sends typeId as masterTypeId
      val roomTypeId                = h.rooms.flatMap(_.typeId) ++ h.cheapestHourlyRoom.flatMap(_.typeId)
      val masterRoomTypeId          = h.rooms.flatMap(_.masterTypeId) ++ h.cheapestHourlyRoom.flatMap(_.masterTypeId)
      val suggestedRoomTypeId       = h.suggestedRooms.flatMap(_.typeId)
      val suggestedMasterRoomTypeId = h.suggestedRooms.flatMap(_.masterTypeId)
      val roomBundleTypeId          = h.roomBundle.flatMap(_.segments.flatMap(_.childRooms.flatMap(_.typeId)))
      val roomBundleMasterTypeId    = h.roomBundle.flatMap(_.segments.flatMap(_.childRooms.flatMap(_.masterTypeId)))
      val masterRoomIds             = (masterRoomTypeId ++ suggestedMasterRoomTypeId ++ roomBundleMasterTypeId).toSet
      RoomFilterCriteria(h.propertyId, masterRoomIds, Try(masterRoomIds.map(_.toInt)).toOption)
    }.toList

    val dynamicallyMappedRooms = Option(df._2.values.flatMap { h =>
      h.rooms.flatMap(r => r.pricingRoomOption.flatMap(_.dynamicRoomMappingToken))
    }.toList).filter(_.nonEmpty)

    if (request.room.isDefined) {
      request.copy(room =
        request.room.map(r =>
          r.copy(
            filterCriteria = Some(
              r.filterCriteria.fold(roomFilterCriteria)(reqFilter =>
                (reqFilter ++ roomFilterCriteria)
                  .groupBy(_.propertyId)
                  .map { hotels =>
                    RoomFilterCriteria(
                      hotels._1,
                      hotels._2.flatMap(room =>
                        room.roomIds ++ room.masterRoomIds.map(_.map(_.toLong)).getOrElse(Set.empty[Long])
                      )(collection.breakOut),
                      hotels._2.map(_.masterRoomIds).traverse(identity).map(_.flatten.toSet)
                    )
                  }
                  .toList
              )
            ),
            dynamicallyMappedRooms = dynamicallyMappedRooms
          )
        )
      )
    } else {
      request.copy(room =
        Option(
          RoomRequestPAPI(
            isContentOnly = Some(request.isContentOnly),
            filterCriteria = Some(roomFilterCriteria),
            dynamicallyMappedRooms = dynamicallyMappedRooms,
            showRoomDescriptionAI = request.room.flatMap(_.showRoomDescriptionAI)
          )
        )
      )
    }
  }

  private[ClusterService] def setTopSellingPointsData(
      tspRequest: Option[TopSellingPointRequest],
      hotel: Option[EnrichedHotelWithContent],
      reviews: Option[ReviewResponse]
  )(implicit
      contextHolder: ContextHolder,
      request: PropertyRequest,
      cmsService: PAPICMSService
  ): Option[EnrichedHotelWithContent] =
    tspRequest match {
      case Some(tsp) =>
        val topSellingPoints = tsp.ids.flatMap { id =>
          id match {
            case TopSellingPointIds.DealOfTheDay         => Some(getDODTopSellingPoint(request.context.locale))
            case TopSellingPointIds.FiveStarDealOfTheDay => Some(getFiveStarDODTopSellingPoint(request.context.locale))
            case TopSellingPointIds.Bestseller           => Some(getBestsellerTopSellingPoint(request.context.locale))
            case _                                       => getTSPBadge(id, reviews, request.context.locale)
          }

        }
        hotel.map(h => h.copy(topSellingPoints = h.topSellingPoints ++ topSellingPoints))

      case None => hotel
    }

  private[ClusterService] def setSupportChildRateData(
      rateCategoriesResponse: Option[RateCategoriesResponse],
      hotel: Option[EnrichedHotelWithContent]
  ): Option[EnrichedHotelWithContent] = {
    val supportChildRate = rateCategoriesResponse.map(_.rateCategoryIdsWithChildRateEnabled.nonEmpty)
    hotel.map(_.copy(isSupportChildRate = supportChildRate))
  }

  private[ClusterService] def getRecommendedRooms(
      request: PropertyRequest
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      contextHolder: ContextHolder
  ): Future[List[(Long, RoomRecoResponse)]] =
    if (request.room.exists(roomReq => roomReq.includeRoomRecommendation.getOrElse(false))) {
      if (
        contextHolder.experimentContext.isBVariant(
          contextHolder.allocationContext.migrateRoomRecoFromZenithToFs
        )
      ) {
        val result = recommendedRoomService
          .getRecommendedRooms(
            request,
            contextHolder.globalContext
          )
          .recover { case ex: Exception =>
            logger.error(s"Failed to get recommended rooms from FS ${request.context.propertyIds}", ex)
            request.context.propertyIds.map(_ -> RoomRecoResponse.defaultInstance)
          }
        result
      } else {
        getRecommendedRoomsFromZenithIfRequested(request)
      }
    } else {
      Future.successful(
        request.context.propertyIds.map(propertyId => (propertyId, RoomRecoResponse.defaultInstance))
      )
    }

  private def getRecommendedRoomsFromZenithIfRequested(
      request: PropertyRequest
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      contextHolder: ContextHolder
  ): Future[List[(Long, RoomRecoResponse)]] =
    if (!contextHolder.experimentContext.isBVariant(contextHolder.allocationContext.negativeTestRoomReco)) {
      Future.sequence(
        request.context.propertyIds.map(propertyId =>
          zenithService
            .getRecommendedRooms(propertyId, request)
            .map(roomRecoResponse => (propertyId, roomRecoResponse))
            .recoverWith { case t: Throwable =>
              logger.error(s"Failed to get recommended rooms from zenith for propertyId: $propertyId", t)
              Future.successful((propertyId, RoomRecoResponse.defaultInstance))
            }
        )
      )
    } else {
      Future.successful(
        request.context.propertyIds.map(propertyId => (propertyId, RoomRecoResponse.defaultInstance))
      )
    }

  /** Marks the first available room from zenith list as recommended in master rooms of the hotel
    *
    * @param recommendedRoomsMap
    *   \- Map of propertyId -> RoomRecoResponse from zenith
    * @param hotel
    *   \- Hotel Info containing master rooms
    * @return
    */
  private def markRecommendedRoom(
      recommendedRoomsMap: Map[HotelIdType, RoomRecoResponse],
      hotel: Option[EnrichedHotelWithContent]
  ): Option[EnrichedHotelWithContent] =
    hotel.map { enrichedHotel =>
      val recommendedRoomId =
        recommendedRoomsMap.getOrElse(enrichedHotel.propertyId, RoomRecoResponse.defaultInstance).rooms
      val availableMasterRoomIds = enrichedHotel.masterRooms.map(_.typeId).toSet
      val recommendedMarked = recommendedRoomId
        .find(availableMasterRoomIds.contains)
        .map(roomIdToMark =>
          enrichedHotel.masterRooms.map {
            case masterRoom if masterRoom.typeId == roomIdToMark =>
              masterRoom.copy(isRecommended = Some(true))
            case masterRoom => masterRoom
          }
        )
        .getOrElse(enrichedHotel.masterRooms)

      enrichedHotel.copy(
        masterRooms = recommendedMarked
      )
    }

  private def toEngagement(
      contentEngagement: PropertyEngagement,
      psEngagement: Option[LastBookEngagement]
  ): EngagementContainer =
    EngagementContainer(
      contentEngagement,
      MetaEngagement(psEngagement.map(_.lastBookByCheckIn).getOrElse(Map.empty))
    )

  private def getRateCategoryIdsWithChildRateEnabled(
      rateCategoriesResponse: Option[RateCategoriesResponse]
  ): Vector[RateCategoryId] =
    rateCategoriesResponse match {
      case Some(rateCategory) =>
        rateCategory.rateCategoryIdsWithChildRateEnabled
      case None => Vector.empty
    }

}

case class EnrichedMasterRooms(
    regularRooms: List[EnrichedMasterRoom] = Nil,
    soldOutRooms: List[EnrichedMasterRoom] = Nil
)

case class GroupedRoomBundles(
    mergedChildRoom: List[EnrichedChildRoom],
    roomBundleEnrichChildRoomSegmentAs: List[EnrichChildRoomBundleSegment],
    roomBundlecEnrichChildRoomSegmentBs: List[EnrichChildRoomBundleSegment]
)
