package com.agoda.papi.search.property.binding

import akka.actor.ActorSystem
import akka.stream.{ ActorMaterializer, Materializer }
import cats.effect.IO
import com.agoda.acm.traffic.forwarder.kafka.KafkaRequestForwarderSettingsBuilder
import com.agoda.acm.traffic.forwarder.{ KafkaRe<PERSON><PERSON><PERSON><PERSON><PERSON>, RequestForwarder, RequestForwarderSettings }
import com.agoda.cache.RemoteCacheSettings
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.http.client.v2.settings.HttpClientSettingsBuilder
import com.agoda.commons.http.client.v2.{ ClientSettings, HttpClient, RequestSettings }
import com.agoda.commons.http.mesh.v2.ServiceMesh
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.commons.http.server.bootstrap.BootstrapSettings
import com.agoda.commons.logging.metrics.{ AdpMetrics<PERSON>eporter, MetricsReporter }
import com.agoda.commons.observability.api.tracing.FeatureTracer
import com.agoda.commons.sql.hikari.load.balancing.{ DataSourcePoolLoadBalancer, HikariDataSourceResource }
import com.agoda.commons.sql.{ Sql, SqlConnection }
import com.agoda.commons.tracing.api.WithTracer
import com.agoda.configuration.dynamic.DynamicConfigurationService
import com.agoda.content.models.PropertyId
import com.agoda.core.search.models.request.filterops.optimizer.{ ElasticAggFilterOptimizer, ElasticFilterOptimizer }
import com.agoda.jarvis.interfacing.client.api.DefaultApi
import com.agoda.papi.search.common.externaldependency.cache.{ AgCacheProvider, CacheConfig }
import com.agoda.papi.search.common.externaldependency.customer.{ CAPIService, CAPIServiceImpl }
import com.agoda.papi.search.common.externaldependency.growthprogram.{ GrowthProgramService, GrowthProgramServiceImpl }
import com.agoda.papi.search.common.externaldependency.repository._
import com.agoda.papi.search.common.externaldependency.vault.VaultAuthenticationSettings
import com.agoda.papi.search.common.model.{ Components, KillSwitchConfig }
import com.agoda.papi.search.common.service.cancellationpolicy.{ BNPLService, CancellationPolicyService }
import com.agoda.papi.search.common.service.fs.FeatureStoreService
import com.agoda.papi.search.common.service.{
  CalculateChannelService,
  ExchangeDataService,
  ExchangeDataServiceImpl,
  LocaleService,
  PropertyMetaService,
  StatefulCmsServiceDefault => _,
  WhiteLabelService
}
import com.agoda.papi.search.common.util.circuitbreaker._
import com.agoda.papi.search.common.util.configuration.{ CustomLocalCacheSettings, CustomRemoteCacheSettings }
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExecutionContexts, WithCorrelationId }
import com.agoda.papi.search.common.util.hotelshutdown.HotelShutdownSetting
import com.agoda.papi.search.common.util.logger.{ HadoopLogger => PapiHadoopLogger, PAPIAppLogger }
import com.agoda.papi.search.common.util.measurement.{ ContextScheduledMetrics, MeasurementName, MeasurementTag }
import com.agoda.papi.search.internalmodel.database._
import com.agoda.papi.search.property.api.configuration.{ FireDrillServiceSettings, PriceSnapshotSettings }
import com.agoda.papi.search.property.api.metrics.RetryGauge
import com.agoda.papi.search.property.externaldependency.logger.HadoopLogger
import com.agoda.papi.search.property.externaldependency.repository._
import com.agoda.papi.search.property.externaldependency.whitelabel.WhiteLabelSetting
import com.agoda.papi.search.property.service.ClusterService._
import com.agoda.papi.search.property.service.ClusterService.propertyfilter.{
  PropertyFilterSortingService,
  PropertyFilterSortingServiceImpl
}
import com.agoda.papi.search.property.service._
import com.agoda.papi.search.property.service.complexresult.{
  HotelResultMappingHelperService,
  HotelResultMappingHelperServiceImpl
}
import com.agoda.papi.search.property.service.datafetcher.{
  DefaultPropertyDetailsDataFetcher,
  DefaultPropertySummaryDataFetcher,
  PropertyDetailsDataFetcher,
  PropertySummaryDataFetcher
}
import com.agoda.papi.search.property.service.enrich._
import com.agoda.papi.search.property.service.jarvis.{
  JarvisClient,
  JarvisClientDefault,
  JarvisService,
  JarvisServiceDefault,
  JarvisSettings
}
import com.agoda.papi.search.property.service.pricestream._
import com.agoda.papi.search.property.service.recommendedUpgrade.{
  RecommendedUpgradeService,
  RecommendedUpgradeServiceImpl
}
import com.agoda.papi.search.property.service.roomSorting.{ RoomSortingHelperService, RoomSortingHelperServiceImpl }
import com.agoda.payment.business.{ InstallmentDetailsBuilder, InstallmentDetailsBuilderDefault }
import com.agoda.payment.repository.{ InstallmentRepository, InstallmentRepositoryDefault }
import com.agoda.pricestream.client.{ PriceStreamClientSettings, PriceStreamHttpClient }
import com.agoda.supplier.client.{ SupplierClient, SupplierClientImpl }
import com.agoda.supplier.common.services.SupplierMetadataService
import com.agoda.supplier.core._
import com.agoda.supplier.services.{
  PreAllocationExperimentService,
  PreAllocationExperimentServiceImpl,
  SupplierExperimentService,
  SupplierExperimentServiceImpl
}
import com.agoda.supply.growth.program.client.{
  GrowthProgramClient,
  GrowthProgramClientImpl,
  GrowthProgramClientSettings
}
import com.agoda.universal.cms.client.context.{ SQLContext => CmsSqlContext }
import com.agoda.universal.cms.client.service.{ UniversalCmsService, UniversalCmsServiceDefault }
import com.agoda.whitelabel.client.WhiteLabelClientMultiEnv
import com.agoda.zenith.client.ZenithHttpClient
import com.typesafe.config.{ Config, ConfigFactory }
import io.opentelemetry.api.OpenTelemetry
import io.opentracing.Tracer
import request.{ PropertyRequest, Recommendation }
import scalacache.AbstractCache
import scaldi.Module
import sttp.client3.SttpBackend
import types.PromotionIdType

import java.sql.Connection
import java.util.concurrent.TimeUnit
import scala.concurrent.duration.FiniteDuration
import scala.concurrent.{ ExecutionContext, Future }
import scala.util.Try

/** Created by psingh on 5/28/15.
  */
object Bindings extends PAPIAppLogger {

  class ServiceModule extends Module {

    // Init file poller
    DynamicConfigurationService.init()

    val config = ConfigFactory.load()
    bind[Config] to config

    bind[HadoopLogger] to new HadoopLogger
    bind[RequestForwarderSettings] to RequestForwarderSettings()

    bind[PropertyDetailsDataFetcher] to injected[DefaultPropertyDetailsDataFetcher]
    bind[PropertySummaryDataFetcher] to injected[DefaultPropertySummaryDataFetcher]

    bind[RequestForwarder] to new KafkaRequestForwarder(
      KafkaRequestForwarderSettingsBuilder()
        .withRequestForwarderSettings(inject[RequestForwarderSettings])
        .withMetricsReporter(inject[MetricsReporter])
        .build()
    )

    // scalastyle:off
    bind[PAPICMSService] to new BaseReportingModule with StatefulCmsServiceDefault {
      private val AppName = "PAPI"

      private val cmsSqlContext: CmsSqlContext = new CmsSqlContext {

        override def SQL[T](metricTag: String)(block: Connection => T): T =
          inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](connection =>
            timerWithoutSampling(
              MeasurementName.SqlContext,
              Map(
                "step"                    -> metricTag,
                MeasurementTag.ClientName -> "cms-client"
              )
            )(block(connection))
          )

      }

      override val cmsService: UniversalCmsService = new UniversalCmsServiceDefault(cmsSqlContext, AppName)
    }
    // scalastyle:on

    // scalastyle:off
    bind[SupplierClient] to {
      val sqlContext = new BaseReportingModule with SQLContext {

        override def SQL[T](metricTag: String)(block: Connection => T): T =
          inject[DataSourcePoolLoadBalancer[HikariDataSourceResource]].withConnection[T](c =>
            timerWithoutSampling(
              MeasurementName.SqlContext,
              Map(
                "step"                    -> metricTag,
                MeasurementTag.ClientName -> "supplier-client"
              )
            )(block(c))
          )

      }

      new SupplierClientImpl(config, sqlContext)
    }

    // scalastyle:on

    implicit val defaultEC = ExecutionContexts.defaultContext

    private val vaultCredentialsOpt =
      if (config.getBoolean("ag-vault.enable")) Some(VaultAuthenticationSettings(config)) else None

    val agCacheProvider =
      new AgCacheProvider(
        AdpMetricsReporter,
        config,
        vaultCredentialsOpt.map(_.getCouchbaseAuthenticationSettings),
        vaultCredentialsOpt.flatMap(_.getCouchbaseCBDATAAuthenticationSettigs)
      )

    // AGSQL
    bind[ExtraBedAgeRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with ExtraBedAgeRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[ExtraBedChildrenAge]] =
        agCacheProvider.getL1L2Cache("ExtraBedAgeRepositoryAgsql")
    }

    bind[HotelBookingCountByCheckinRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with HotelBookingCountByCheckinRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[BookingCountByCheckIn]] =
        agCacheProvider.getL1L2Cache("HotelBookingCountByCheckinRepositoryAgsql")
    }

    bind[PaymentTypesRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with PaymentTypesRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[PaymentTypeResult]] =
        agCacheProvider.getL1L2Cache("PaymentTypesRepositoryAgsql")
    }

    bind[PromotionRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with PromotionRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[(PromotionIdType, PromotionData)]] =
        agCacheProvider.getL1L2Cache("PromotionRepositoryAgsql")
    }

    bind[RatePlanInfoRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with RatePlanInfoRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[RatePlanInfoModel]] =
        agCacheProvider.getL1L2Cache("RatePlanInfoRepositoryAgsql1")
    }

    bind[SurchargeDescriptionRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with SurchargeDescriptionRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[SurchargeDescription]] =
        agCacheProvider.getL1L2Cache("SurchargeDescriptionRepositoryAgsql")
    }

    bind[PeopleLookingPerCitySourceIO] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with PeopleLookingPerCitySourceIOImpl {

      implicit override protected val agCache: AbstractCache[Option[UserViewCountPerCity]] =
        agCacheProvider.getL1L2Cache(
          "PeopleLookingPerCitySourceIO",
          customLocalCacheExpiry = Some(FiniteDuration(1, TimeUnit.HOURS))
        )

    }

    bind[BookingFrequencyRateIOSource] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with BookingFrequencyRateIOSourceImpl {
      implicit override protected val agCache: AbstractCache[BookingFrequencyRateResult] =
        agCacheProvider.getL1L2Cache("BookingFrequencyRateIOSource", localCacheCapacity = Some(30000))
    }

    bind[CityCentrePolygonRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with CityCentrePolygonRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[CityPolygonData]] =
        agCacheProvider.getL1L2Cache("CityCentrePolygonRepositoryAgsql")
    }

    bind[SoldOutRoomsPriceRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with SoldOutRoomsPriceRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[SoldOutPricesWithDateTime]] =
        agCacheProvider.getL1L2Cache("SoldOutRoomsPriceRepositoryAgsql")
    }

    bind[BookingFormHistoryRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with BookingFormHistoryRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[BookingHistoryData] =
        agCacheProvider.getL1L2Cache("BookingFormHistoryRepositoryAgsql")
    }

    bind[SpecialRequestsRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with SpecialRequestsRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[SpecialRequestData] =
        agCacheProvider.getL1L2Cache("SpecialRequestsRepositoryAgsql", localCacheCapacity = Some(15000))
    }

    bind[BookingHistoryByCityRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with BookingHistoryByCityRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[CityBookingHistorySummaryRow]] =
        agCacheProvider.getL1L2Cache("BookingHistoryByCityRepositoryAgsql")
    }

    bind[BookingHistoryByPropertyRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with BookingHistoryByPropertyRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[BookingHistoryInfo]] =
        agCacheProvider.getL1L2Cache("BookingHistoryByPropertyRepositoryAgsql", localCacheCapacity = Some(50000))
    }

    bind[CCCampaignInformationRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with CCCampaignInformationRepositoryAgsqlImpl {
      override protected val remoteCacheSettings = CustomRemoteCacheSettings("cc-campaign")
      val customLocalCacheSettings               = Some(CustomLocalCacheSettings("cc-campaign"))

      implicit override protected val agCache: AbstractCache[Vector[CampaignFetchedInformation]] =
        agCacheProvider.getL1L2Cache(
          "CCCampaignInformationRepositoryAgsql",
          customLocalCacheSettings = customLocalCacheSettings
        )

    }

    bind[HotelAvgBookingByMonthRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with HotelAvgBookingByMonthRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[StatisticBookingDataPerDayAggregatedMonth]] =
        agCacheProvider.getL1L2Cache("HotelAvgBookingByMonthRepositoryAgsql")
    }

    bind[CurrencyDisplayRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with CurrencyDisplayRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[String]] =
        agCacheProvider.getL1L2Cache("CurrencyDisplayRepositoryAgsql")
    }

    bind[PointsMaxDescriptionRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with PointsMaxDescriptionRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Vector[PointsMaxDescription]] =
        agCacheProvider.getL1L2Cache("PointsMaxDescriptionRepositoryAgsql")
    }

    bind[TaxDescriptionRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with TaxDescriptionRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[TaxDescription] =
        agCacheProvider.getL1L2Cache("TaxDescriptionRepositoryAgsql")
    }

    bind[CityIdsFetcherRepositoryAgsql] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with CityIdsFetcherRepositoryAgsqlImpl

    bind[AreaPopularityService] to injected[AreaPopularityServiceImpl]

    bind[PMCCampaignInformationRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with PMCCampaignInformationRepositoryAgsqlImpl {
      override protected val remoteCacheSettings = CustomRemoteCacheSettings("pmc-campaign")
      val customLocalCacheSettings               = Some(CustomLocalCacheSettings("pmc-campaign"))

      implicit override protected val agCache: AbstractCache[Vector[CampaignFetchedInformation]] =
        agCacheProvider.getL1L2Cache(
          "PMCCampaignInformationRepositoryAgsql",
          customLocalCacheSettings = customLocalCacheSettings
        )

    }

    bind[PricingParityAggregationsRepository] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with PricingParityAggregationsRepositoryAgsqlImpl {
      implicit override protected val agCache: AbstractCache[Option[PricingParityAggregations]] =
        agCacheProvider.getL1L2Cache("PricingParityAggregationsRepositoryAgsql")
    }

    bind[OverlapBookingCountIOSource] to new BaseServiceModuleSqlIOCached(
      inject[IO[Sql[IO, SqlConnection[IO]]]],
      inject[PAPICMSService]
    ) with OverlapBookingCountIOSourceImpl

    bind[BookingHistoryService] to injected[BookingHistoryServiceImpl]

    bind[BookingFormHistoryService] to injected[BookingFormHistoryServiceImpl]

    bind[ContentEnrichService] to injected[ContentEnrichServiceImpl]

    bind[BootstrapSettings] to BootstrapSettings(config)

    // Whitelabel client settings
    lazy val whiteLabelSetting = WhiteLabelSetting(config)

    def createCircuitBreakerWrapper(
        circuitBreakerSettings: CircuitBreakerSettings,
        tagMap: Map[String, String]
    ): CircuitBreakerWrapper =
      CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
        circuitBreakerSettings,
        inject[ActorSystem].scheduler,
        defaultEC,
        inject[MetricsReporter],
        tagMap
      )

    def createCircuitBreakerWrapperContent(
        clusterSettings: ClusterServiceSettings
    ): CircuitBreakerWrapper =
      createCircuitBreakerWrapper(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        Map("endpoint" -> clusterSettings.endpointPath)
      )

    def createCircuitBreakerWrapperDF(
        clusterSettings: ClusterServiceSettings,
        usecase: Option[String] = None
    ): CircuitBreakerWrapper =
      createCircuitBreakerWrapper(
        inject[ExternalServiceCircuitBreakerSettings].dragonfruitCircuitBreakerSettings,
        Map("endpoint" -> clusterSettings.endpointPath) ++ usecase
          .map(u => Map("usecase" -> u))
          .getOrElse(Map.empty[String, String])
      )

    bind[WhiteLabelClientMultiEnv] to WhiteLabelClientMultiEnv(
      whiteLabelSetting.host,
      whiteLabelSetting.appName,
      whiteLabelSetting.appName,
      maxAttempts = whiteLabelSetting.maxAttempts
    )

    lazy val priceStreamHttpClientSettings =
      HttpClientSettingsBuilder(inject[PriceStreamClientSettings].service, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

    import cats.instances.future.catsStdInstancesForFuture

    lazy val serviceMeshSettings = ServiceMeshSettingsBuilder(
      inject[PriceStreamClientSettings].service,
      config
    ).build()

    (bind[RequestSettings] to RequestSettings(priceStreamHttpClientSettings.clientSettings, Map.empty))
      .identifiedBy('PriceStreamRequestSetting)

    bind[PriceStreamHttpClient[Future]] to new PriceStreamHttpClient[Future](
      ServiceMesh(serviceMeshSettings),
      priceStreamHttpClientSettings
    )(
      inject[SttpBackend[Future, Any]]("sttpBackendPricestream"),
      catsStdInstancesForFuture
    )

    bind[PriceMessagingService] to new BaseServiceModuleCached(inject[PAPICMSService])
      with PriceMessagingServiceDefault {
      override val pricingParityAggregationsRepository: PricingParityAggregationsRepository =
        inject[PricingParityAggregationsRepository]
      override val objectInfoRepositoryAgsql: ObjectInfoRepositoryAgsql = inject[ObjectInfoRepositoryAgsql]
    }

    bind[HighlightServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with HighlightServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh =
        ServiceMesh(ServiceMeshSettingsBuilder(config.serviceName, ConfigFactory.load()).build())

      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapper(
        inject[ExternalServiceCircuitBreakerSettings].contentCircuitBreakerSettings,
        Map("endpoint" -> config.endpointPath)
      )

    }

    bind[PointsMaxEnrichHelper] to new BaseServiceModule(inject[PAPICMSService]) with PointsMaxEnrichHelper {
      implicit override val languageRepository: LanguageRepository    = inject[LanguageRepository]
      override val taxDescriptionRepository: TaxDescriptionRepository = inject[TaxDescriptionRepository]
      override val surchargeDescriptionRepository: SurchargeDescriptionRepository =
        inject[SurchargeDescriptionRepository]
      override val contentEnrichService: ContentEnrichService = inject[ContentEnrichService]
      override val pointsMaxDescriptionRepositoryAgsql: PointsMaxDescriptionRepositoryAgsql =
        inject[PointsMaxDescriptionRepositoryAgsql]
    }

    bind[EnrichPaymentHelper] to new BaseServiceModule(inject[PAPICMSService]) with EnrichPaymentHelper {
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
      override val bnplService: BNPLService                        = inject[BNPLService]
      override val contentEnrichService: ContentEnrichService      = inject[ContentEnrichService]
    }

    bind[PricingMessageEnrichHelper] to new BaseServiceModule(inject[PAPICMSService])
      with PricingMessageEnrichHelperImpl {
      override val pricingMessageSettings: PricingMessageSettings = PricingMessageSettings()
      override val currencyDisplayRepositoryAgsql: CurrencyDisplayRepositoryAgsql =
        inject[CurrencyDisplayRepositoryAgsql]
    }

    bind[EnrichCampaignService] to new BaseServiceModule(inject[PAPICMSService]) with EnrichCampaignServiceImpl {
      override val campaignInfoService: CampaignInformationService = inject[CampaignInformationService]
    }

    bind[EnrichPricingService] to new BaseServiceModule(inject[PAPICMSService]) with EnrichPricingServiceImpl {
      override val taxDescriptionRepository: TaxDescriptionRepository = inject[TaxDescriptionRepository]
      override val surchargeDescriptionRepository: SurchargeDescriptionRepository =
        inject[SurchargeDescriptionRepository]
      override val contentEnrichService: ContentEnrichService   = inject[ContentEnrichService]
      override val enrichCampaignService: EnrichCampaignService = inject[EnrichCampaignService]
    }

    bind[EnrichChargeService] to new EnrichChargeServiceImpl {
      override val contentEnrichService: ContentEnrichService         = inject[ContentEnrichService]
      override val enrichCampaignService: EnrichCampaignService       = inject[EnrichCampaignService]
      override val taxDescriptionRepository: TaxDescriptionRepository = inject[TaxDescriptionRepository]
      override val surchargeDescriptionRepository: SurchargeDescriptionRepository =
        inject[SurchargeDescriptionRepository]
      implicit override val ec: ExecutionContext = ExecutionContexts.defaultContext
    }

    bind[EnrichPackagePricingService] to new BaseServiceModule(inject[PAPICMSService])
      with EnrichPackagePricingServiceImpl {
      override val taxDescriptionRepository: TaxDescriptionRepository = inject[TaxDescriptionRepository]
      override val surchargeDescriptionRepository: SurchargeDescriptionRepository =
        inject[SurchargeDescriptionRepository]
      override val contentEnrichService: ContentEnrichService   = inject[ContentEnrichService]
      override val enrichCampaignService: EnrichCampaignService = inject[EnrichCampaignService]
    }

    bind[SpecialRequestsService] to new SpecialRequestsServiceImpl {
      override protected val specialRequestsRepositoryAgsql: SpecialRequestsRepositoryAgsql =
        inject[SpecialRequestsRepositoryAgsql]
    }

    bind[EnrichRoomBundleService] to new EnrichRoomBundleServiceImpl {}
    bind[OriginalRoomDetailHelper] to new OriginalRoomDetailHelper {}

    bind[EnrichRoomHelper] to new BaseServiceModule(inject[PAPICMSService]) with EnrichRoomHelper {

      override val coreCancellationService: CancellationPolicyService =
        inject[CancellationPolicyService]
      override val ratePlanInfoRepository: RatePlanInfoRepository           = inject[RatePlanInfoRepository]
      override val enrichCampaignService: EnrichCampaignService             = inject[EnrichCampaignService]
      implicit override val languageRepository: LanguageRepository          = inject[LanguageRepository]
      override val contentEnrichService: ContentEnrichService               = inject[ContentEnrichService]
      override val pointsMaxEnricher: PointsMaxEnrichHelper                 = inject[PointsMaxEnrichHelper]
      override val paymentEnricher: EnrichPaymentHelper                     = inject[EnrichPaymentHelper]
      override val pricingMessageEnricher: PricingMessageEnrichHelper       = inject[PricingMessageEnrichHelper]
      override val enrichPricingService: EnrichPricingService               = inject[EnrichPricingService]
      override val exchangeDataService: ExchangeDataService                 = inject[ExchangeDataService]
      override val installmentService: InstallmentService                   = inject[InstallmentService]
      override val enrichPackagePricingService: EnrichPackagePricingService = inject[EnrichPackagePricingService]
      override val whiteLabelService: WhiteLabelService                     = inject[WhiteLabelService]
      override val originalRoomDetailHelper: OriginalRoomDetailHelper       = inject[OriginalRoomDetailHelper]
    }

    bind[EnrichBookingInformationService] to new BaseServiceModule(inject[PAPICMSService])
      with EnrichBookingInformationServiceImpl {
      override val enrichRoomService: EnrichRoomHelper            = inject[EnrichRoomHelper]
      override val exchangeDataService: ExchangeDataService       = inject[ExchangeDataService]
      override val enrichCampaignService: EnrichCampaignService   = inject[EnrichCampaignService]
      override val specialRequestsService: SpecialRequestsService = inject[SpecialRequestsService]
    }

    bind[ContentEnrichHelper] to new BaseServiceModule(inject[PAPICMSService]) with ContentEnrichHelper {
      override val productHotelsService: ProductHotelsService      = inject[ProductHotelsService]
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
      override val extraBedAgeRepository                           = inject[ExtraBedAgeRepository]
      override val enrichBookingInformationService: EnrichBookingInformationService =
        inject[EnrichBookingInformationService]
      override val enrichRoomService: EnrichRoomHelper                = inject[EnrichRoomHelper]
      override val userLocaleService: UserLocaleService               = inject[UserLocaleService]
      override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
      override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
      override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
      override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
      override val bnplService: BNPLService                           = inject[BNPLService]
      override val contentEnrichService: ContentEnrichService         = inject[ContentEnrichService]
      override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
        inject[EnrichMultiRoomSuggestionService]
      override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
      override val priceSelectionService: PriceSelectionService         = inject[PriceSelectionService]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
    }

    bind[HotelPaymentOptionService] to new HotelPaymentOptionServiceImpl {

      implicit override val cancellationPolicyService: CancellationPolicyService =
        inject[CancellationPolicyService]

    }

    bind[DisplayPaymentTypesService] to new DisplayPaymentTypesServiceImpl {
      override protected val paymentTypesRepository: PaymentTypesRepository = inject[PaymentTypesRepository]
    }

    lazy val serviceMeshes: Map[String, ServiceMesh] = List("content", "dragonfruit").map { serviceName =>
      val meshSettings = ServiceMeshSettingsBuilder(serviceName, config)
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()
      (serviceName, ServiceMesh(meshSettings))
    }.toMap

    (bind[Map[String, ServiceMesh]] to serviceMeshes).identifiedBy('consulServiceMeshes)

    bind[DFPropertyWithCacheTokenServiceWrapper] to new BaseServiceModuleWithLanguage(
      inject[PAPICMSService]
    ) with DFPropertyWithCacheTokenServiceWrapperImpl with ContentEnrichHelper {
      val languageRepository: LanguageRepository              = inject[LanguageRepository]
      val clusterSettingsConfig                               = ClusterServiceSettings(configName)
      override val serviceMesh: ServiceMesh                   = serviceMeshes(clusterSettingsConfig.serviceName)
      override val productHotelsService: ProductHotelsService = inject[ProductHotelsService]
      override val config: ClusterServiceSettings             = clusterSettingsConfig

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedDF"))
      implicit override val system: ActorSystem               = inject[ActorSystem]
      override val retryGauge: RetryGauge                     = inject[RetryGauge]
      override val requestFilterConfig: DFRequestFilterConfig = inject[DFRequestFilterConfig]
      override val supplierMetadataService                    = inject[SupplierMetadataService]
      override val enrichBookingInformationService: EnrichBookingInformationService =
        inject[EnrichBookingInformationService]
      override val enrichRoomService: EnrichRoomHelper                = inject[EnrichRoomHelper]
      override val userLocaleService: UserLocaleService               = inject[UserLocaleService]
      override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
      override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
      override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
      override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
      override val circuitBreakerWrapper: CircuitBreakerWrapper       = createCircuitBreakerWrapperDF(config)
      override val bnplService: BNPLService                           = inject[BNPLService]
      override val contentEnrichService: ContentEnrichService         = inject[ContentEnrichService]
      override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
        inject[EnrichMultiRoomSuggestionService]
      override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
      override val priceSelectionService: PriceSelectionService         = inject[PriceSelectionService]
      override val extraBedAgeRepository: ExtraBedAgeRepository         = inject[ExtraBedAgeRepository]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
    }

    bind[DFRecommendationServiceWrapper] to new BaseServiceModuleWithLanguage(
      inject[PAPICMSService]
    ) with DFRecommendationServiceWrapperImpl with ContentEnrichHelper {
      val languageRepository: LanguageRepository  = inject[LanguageRepository]
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      override val serviceMesh: ServiceMesh       = serviceMeshes(config.serviceName)

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedDF"))
      implicit override val system: ActorSystem                     = inject[ActorSystem]
      override val retryGauge: RetryGauge                           = inject[RetryGauge]
      override val priceSelectionService: PriceSelectionService     = inject[PriceSelectionService]
      override val productHotelsService: ProductHotelsService       = inject[ProductHotelsService]
      override val requestFilterConfig: DFRequestFilterConfig       = inject[DFRequestFilterConfig]
      override val extraBedAgeRepository                            = inject[ExtraBedAgeRepository]
      override val supplierMetadataService: SupplierMetadataService = inject[SupplierMetadataService]
      override val enrichBookingInformationService: EnrichBookingInformationService =
        inject[EnrichBookingInformationService]
      override val enrichRoomService: EnrichRoomHelper                = inject[EnrichRoomHelper]
      override val userLocaleService: UserLocaleService               = inject[UserLocaleService]
      override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
      override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
      override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
      override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
      override val circuitBreakerWrapper: CircuitBreakerWrapper =
        createCircuitBreakerWrapperDF(config, Some("recommendations"))
      override val enrichPackagingService: EnrichPackagePricingService = inject[EnrichPackagePricingService]
      override val enrichChargeService: EnrichChargeService            = inject[EnrichChargeService]
      override val bnplService: BNPLService                            = inject[BNPLService]
      override val contentEnrichService: ContentEnrichService          = inject[ContentEnrichService]
      override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
        inject[EnrichMultiRoomSuggestionService]
      override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
    }

    bind[DFVersionService] to new BaseServiceModule(inject[PAPICMSService]) with DFVersionServiceImpl {
      val clusterSettingsConfig                   = ClusterServiceSettings(configName)
      override val config: ClusterServiceSettings = clusterSettingsConfig
      override val serviceMesh: ServiceMesh       = serviceMeshes(clusterSettingsConfig.serviceName)

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedDF"))
      implicit override val system: ActorSystem                 = inject[ActorSystem]
      override val retryGauge: RetryGauge                       = inject[RetryGauge]
      override val circuitBreakerWrapper: CircuitBreakerWrapper = NoOpCircuitBreakerWrapper
    }

    bind[ReviewCommentServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewCommentServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ReviewPositiveMentionsServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewPositiveMentionsServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ReviewCountersServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewCountersServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[HotelInfoServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with HotelInfoServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[LocalInformationServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with LocalInformationServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[CheckInOutServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with CheckInOutServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[EngagementServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with EngagementServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ExperienceServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with ExperienceServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[FeatureServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with FeatureServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ImageServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with ImageServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ReviewScoreServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewScoreServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ReviewSnippetServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewSnippetServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ReviewTotalServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewTotalServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ReviewFiltersServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ReviewFiltersServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[RoomDetailServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with RoomDetailServiceWrapperImpl {
      override val config: ClusterServiceSettings         = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem           = inject[ActorSystem]
      override val retryGauge: RetryGauge                 = inject[RetryGauge]
      override val languageRepository: LanguageRepository = inject[LanguageRepository]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[PersonalizedInformationServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with PersonalizedInformationServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[ContentSummaryServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with ContentSummaryServiceWrapperImpl {
      override val config: ClusterServiceSettings                  = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem                    = inject[ActorSystem]
      override val retryGauge: RetryGauge                          = inject[RetryGauge]
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[HotelInformationSummaryServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with HotelInformationSummaryServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[EnrichContentForChildRoomHelper] to new BaseServiceModule(inject[PAPICMSService])
      with EnrichContentForChildRoomHelperImpl {
      override val whiteLabelService: WhiteLabelService = inject[WhiteLabelService]
    }

    bind[ContentVersionService] to new BaseServiceModule(inject[PAPICMSService]) with ContentVersionServiceImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val serviceMesh: ServiceMesh       = serviceMeshes(config.serviceName)

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val retryGauge: RetryGauge                       = inject[RetryGauge]
      override val circuitBreakerWrapper: CircuitBreakerWrapper = NoOpCircuitBreakerWrapper
    }

    bind[NonHotelAccommodationServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with NonHotelAccommodationServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[SynopsisServiceWrapper] to new BaseServiceModule(inject[PAPICMSService]) with SynopsisServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[RateCategoriesServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with RateCategoriesServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[RateCategoryServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with RateCategoryServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[BathInformationServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with BathInformationServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[TransportationInformationServiceWrapper] to new BaseServiceModule(inject[PAPICMSService])
      with TransportationInformationServiceWrapperImpl {
      override val config: ClusterServiceSettings = ClusterServiceSettings(configName)
      implicit override val system: ActorSystem   = inject[ActorSystem]
      override val retryGauge: RetryGauge         = inject[RetryGauge]

      lazy val httpClientSettings = HttpClientSettingsBuilder(config.serviceName, inject[Config])
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

      override val httpClient: HttpClient[Future] =
        HttpClient[Future](httpClientSettings)(inject[SttpBackend[Future, Any]]("sttpBackedContent"))
      override val serviceMesh: ServiceMesh                     = serviceMeshes(config.serviceName)
      override val circuitBreakerWrapper: CircuitBreakerWrapper = createCircuitBreakerWrapperContent(config)
    }

    bind[SupplierExperimentService] to injected[SupplierExperimentServiceImpl]
    bind[PreAllocationExperimentService] to injected[PreAllocationExperimentServiceImpl]

    bind[PreAllocationExperimentReportHelper] to new PreAllocationExperimentReportHelper with WithTracer {
      implicit override val languageRepository: LanguageRepository =
        inject[LanguageRepository]
      override val preAllocationExperimentService: PreAllocationExperimentService =
        inject[PreAllocationExperimentService]
      implicit override val cqlContext: CQLContext = inject[CQLContext]
      override val tracer: Tracer                  = inject[Tracer]
    }

    bind[ExternalService] to new BaseServiceModule(inject[PAPICMSService]) with ExternalService {
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
      override val dfPropertyWithCacheToken: DFPropertyWithCacheTokenServiceWrapper =
        inject[DFPropertyWithCacheTokenServiceWrapper]
      override val dfVersionService: DFVersionService                = inject[DFVersionService]
      override val contentVersionService: ContentVersionService      = inject[ContentVersionService]
      override val reviewCommentService: ReviewCommentServiceWrapper = inject[ReviewCommentServiceWrapper]
      override val reviewPositiveMentionsService: ReviewPositiveMentionsServiceWrapper =
        inject[ReviewPositiveMentionsServiceWrapper]
      override val featureService: FeatureServiceWrapper                   = inject[FeatureServiceWrapper]
      override val hotelInfoService: HotelInfoServiceWrapper               = inject[HotelInfoServiceWrapper]
      override val imageService: ImageServiceWrapper                       = inject[ImageServiceWrapper]
      override val reviewScoreService: ReviewScoreServiceWrapper           = inject[ReviewScoreServiceWrapper]
      override val reviewSnippetService: ReviewSnippetServiceWrapper       = inject[ReviewSnippetServiceWrapper]
      override val reviewTotalService: ReviewTotalServiceWrapper           = inject[ReviewTotalServiceWrapper]
      override val reviewFiltersService: ReviewFiltersServiceWrapper       = inject[ReviewFiltersServiceWrapper]
      override val reviewCountersService: ReviewCountersServiceWrapper     = inject[ReviewCountersServiceWrapper]
      override val roomDetailService: RoomDetailServiceWrapper             = inject[RoomDetailServiceWrapper]
      override val dfRecommendationService: DFRecommendationServiceWrapper = inject[DFRecommendationServiceWrapper]
      override val zenithService: ZenithService                            = inject[ZenithService]
      override val summaryService: ContentSummaryServiceWrapper            = inject[ContentSummaryServiceWrapper]
      override val localInformationService: LocalInformationServiceWrapper = inject[LocalInformationServiceWrapper]
      override val checkInOutService: CheckInOutServiceWrapper             = inject[CheckInOutServiceWrapper]
      override val synopsisService: SynopsisServiceWrapper                 = inject[SynopsisServiceWrapper]
      override val experienceService: ExperienceServiceWrapper             = inject[ExperienceServiceWrapper]
      override val engagementService: EngagementServiceWrapper             = inject[EngagementServiceWrapper]
      override val hotelInformationSummaryService: HotelInformationSummaryServiceWrapper =
        inject[HotelInformationSummaryServiceWrapper]
      override val highlightService: HighlightServiceWrapper    = inject[HighlightServiceWrapper]
      override val requestBuilderService: RequestBuilderService = inject[RequestBuilderService]
      override val nonHotelAccommodationService: NonHotelAccommodationServiceWrapper =
        inject[NonHotelAccommodationServiceWrapper]
      override val supplierMetadataService: SupplierMetadataService = inject[SupplierMetadataService]
      override val personalizedInformationService: PersonalizedInformationServiceWrapper =
        inject[PersonalizedInformationServiceWrapper]
      override val enrichFilterTagsForRoomHelper: EnrichContentForChildRoomHelper =
        inject[EnrichContentForChildRoomHelper]
      override val soldOutRoomsSetting: SoldOutRoomsSetting                   = inject[SoldOutRoomsSetting]
      override val limitMasterRoomSetting: LimitMasterRoomSetting             = inject[LimitMasterRoomSetting]
      override val hotelShutdownSetting: HotelShutdownSetting                 = inject[HotelShutdownSetting]
      override val soldOutPricesService: SoldOutRoomPricesService             = inject[SoldOutRoomPricesService]
      override val propertyFilterSortingService: PropertyFilterSortingService = inject[PropertyFilterSortingService]
      override val priceStreamService: PriceStreamService                     = inject[PriceStreamService]
      override val rateCategoriesService: RateCategoriesServiceWrapper        = inject[RateCategoriesServiceWrapper]
      override val rateCategoryService: RateCategoryServiceWrapper            = inject[RateCategoryServiceWrapper]
      override val bathInformationService: BathInformationServiceWrapper      = inject[BathInformationServiceWrapper]
      override val transportationInformationService: TransportationInformationServiceWrapper =
        inject[TransportationInformationServiceWrapper]
      override val enrichRoomBundleService: EnrichRoomBundleService     = inject[EnrichRoomBundleService]
      override val paymentTypesService: DisplayPaymentTypesService      = inject[DisplayPaymentTypesService]
      override val tracer: Tracer                                       = inject[Tracer]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]
      override val roomSortingService: RoomSortingHelperService         = inject[RoomSortingHelperService]
      override val recommendedUpgradeService: RecommendedUpgradeService = inject[RecommendedUpgradeService]

      override val recommendedRoomService: RecommendedRoomsService = inject[RecommendedRoomsService]
      override val jarvisService: JarvisService                    = inject[JarvisService]
      override val featureStoreService: FeatureStoreService        = inject[FeatureStoreService]
    }

    // For locally running without consul
    val appProfile = Option(Try(config.getString("app.profile")).getOrElse("live"))

    appProfile match {
      case AppProfile.Devs | AppProfile.CI | AppProfile.Affiliate | AppProfile.UserPrivateCloud =>
        warn(Components.PAPI, s"Start PAPI in $appProfile Mode (Get External Service from url)")(new WithCorrelationId {
          override def getCorrelationId: String = "boot_correlation_id"
        })
      case _ =>
        warn(Components.PAPI, s"Start PAPI in Consul Mode")(new WithCorrelationId {
          override def getCorrelationId: String = "boot_correlation_id"
        })
    }

    bind[RoomSortingHelperService] to injected[RoomSortingHelperServiceImpl]
    bind[RecommendedUpgradeService] to injected[RecommendedUpgradeServiceImpl]

    bind[PriceSelectionService] to new PriceSelectionServiceDefault with Ec {
      override val whiteLabelService: WhiteLabelService                = inject[WhiteLabelService]
      override val priceConfigServiceAgsql: PriceConfigRepositoryAgsql = inject[PriceConfigRepositoryAgsql]
    }

    bind[ProductHotelsService] to new ProductHotelsServiceImpl {
      override val productHotelRepositoryAgsql: ProductHotelRepositoryAgsql = inject[ProductHotelRepositoryAgsql]
    }

    bind[PropertySearchTokenService] to new PropertySearchTokenServiceImpl with PAPIAppLogger {}

    bind[PriceSnapshotService] to new BaseServiceModuleCached(inject[PAPICMSService]) with PriceSnapshotServiceImpl {
      override val priceSnapshotSettings: PriceSnapshotSettings = inject[PriceSnapshotSettings]
    }

    bind[PriceStreamService] to new BaseServiceModuleCached(inject[PAPICMSService]) with PriceStreamServiceImpl {
      override val client: PriceStreamHttpClient[Future] = inject[PriceStreamHttpClient[Future]]
      override val ec: ExecutionContext                  = ExecutionContexts.defaultContext
      override val requestSettings: RequestSettings = inject[RequestSettings](identified by 'PriceStreamRequestSetting)
      override val whiteLabelService: WhiteLabelService   = inject[WhiteLabelService]
      override val localeService: LocaleService           = inject[LocaleService]
      override val languageRepository: LanguageRepository = inject[LanguageRepository]
    }

    bind[ExchangeDataService] to new BaseServiceModuleCached(inject[PAPICMSService]) with ExchangeDataServiceImpl {
      override val exchangeDataRepository: ExchangeDataRepository = inject[ExchangeDataRepository]
    }

    bind[ZenithService] to new BaseServiceModule(inject[PAPICMSService]) with ZenithServiceDefault {
      override val zenithClient: ZenithHttpClient[Future] = inject[ZenithHttpClient[Future]]
    }

    bind[BookingCountService] to injected[BookingCountServiceImpl]

    bind[CampaignInformationService] to injected[CampaignInformationServiceImpl]

    bind[DateService] to injected[DateServiceImpl]

    bind[UserLocaleService] to new BaseServiceModuleCached(inject[PAPICMSService]) with UserLocaleServiceImpl {
      implicit override val languageRepository: LanguageRepository = inject[LanguageRepository]
    }

    bind[PriceStreamUrgencyService] to new BaseServiceModuleCached(inject[PAPICMSService])
      with PriceStreamUrgencyServiceImpl {
      override val priceStreamClientSettings: PriceStreamClientSettings = inject[PriceStreamClientSettings]
      override val priceStreamService: PriceStreamService               = inject[PriceStreamService]
    }

    bind[ComplexSearchPriceStreamEnrichService] to new BaseServiceModuleCached(
      inject[PAPICMSService]
    ) with ComplexSearchPriceStreamEnrichServiceImpl {
      override val priceStreamUrgencyService: PriceStreamUrgencyService = inject[PriceStreamUrgencyService]
    }

    bind[BookingFrequencyRateService] to injected[BookingFrequencyRateServiceImpl]

    bind[HotelResultMappingHelperService] to injected[HotelResultMappingHelperServiceImpl]

    bind[RequestBuilderService] to new BaseServiceModuleCached(inject[PAPICMSService])
      with RequestBuilderServiceDefault {
      implicit val languageRepository: LanguageRepository        = inject[LanguageRepository]
      val priceSelectionService: PriceSelectionService           = inject[PriceSelectionService]
      val productHotelsService: ProductHotelsService             = inject[ProductHotelsService]
      val calculateChannelService: CalculateChannelService       = inject[CalculateChannelService]
      val propertySearchTokenService: PropertySearchTokenService = inject[PropertySearchTokenService]
      val whiteLabelService: WhiteLabelService                   = inject[WhiteLabelService]
      val capiService: CAPIService                               = inject[CAPIService]
      val propertyMetaService: PropertyMetaService               = inject[PropertyMetaService]
      val hadoopLogger: PapiHadoopLogger                         = inject[PapiHadoopLogger]

      override val blockDSANonCompliantHotelRegisteredBeforeFeb2024: State[Boolean] =
        inject[State[KillSwitchConfig]]
          .map(_.blockDSANonCompliantHotelRegisteredBeforeFeb2024)

    }

    bind[StarfruitService] to new BaseServiceModuleCached(inject[PAPICMSService])
      with StarfruitServiceImpl with ContentEnrichHelper {
      val externalService: ExternalService                              = inject[ExternalService]
      implicit val languageRepository: LanguageRepository               = inject[LanguageRepository]
      val priceMessagingService: PriceMessagingService                  = inject[PriceMessagingService]
      val requestBuilderService: RequestBuilderService                  = inject[RequestBuilderService]
      override val productHotelsService: ProductHotelsService           = inject[ProductHotelsService]
      override val supplierMetadataService                              = inject[SupplierMetadataService]
      override val extraBedAgeRepository                                = inject[ExtraBedAgeRepository]
      override val bookingFormBookingHistory: BookingFormHistoryService = inject[BookingFormHistoryService]
      override val bookingHistoryService: BookingHistoryService         = inject[BookingHistoryService]
      override val areaPopularityService: AreaPopularityService         = inject[AreaPopularityService]
      override val enrichBookingInformationService: EnrichBookingInformationService =
        inject[EnrichBookingInformationService]
      override val enrichRoomService: EnrichRoomHelper                = inject[EnrichRoomHelper]
      override val userLocaleService: UserLocaleService               = inject[UserLocaleService]
      override val tracer: Tracer                                     = inject[Tracer]
      override val featureTracer: FeatureTracer                       = inject[FeatureTracer]
      override val bookingPredictionService: BookingPredictionService = inject[BookingPredictionService]
      override val boutiqueHotelService: BoutiqueHotelService         = inject[BoutiqueHotelService]
      override val priceStreamService: PriceStreamService             = inject[PriceStreamService]
      override val enrichRoomBundleService: EnrichRoomBundleService   = inject[EnrichRoomBundleService]
      override val bnplService: BNPLService                           = inject[BNPLService]
      override val contentEnrichService: ContentEnrichService         = inject[ContentEnrichService]
      override val enrichMultiRoomSuggestionService: EnrichMultiRoomSuggestionService =
        inject[EnrichMultiRoomSuggestionService]
      override val hotelPaymentOptionService: HotelPaymentOptionService = inject[HotelPaymentOptionService]
      override val priceSelectionService: PriceSelectionService         = inject[PriceSelectionService]
      override val whiteLabelService: WhiteLabelService                 = inject[WhiteLabelService]

    }

    bind[BookingPredictionService] to injected[BookingPredictionServiceImpl]
    bind[BoutiqueHotelService] to injected[BoutiqueHotelServiceImpl]

    bind[CityObjectInfoService] to injected[CityObjectInfoServiceImpl]

    bind[PropertyRecommendationService] to new BaseServiceModuleCached(inject[PAPICMSService])
      with PropertyRecommendationServiceImpl {
      implicit override val ec: ExecutionContext                           = defaultEC
      implicit override val cmsService: PAPICMSService                     = inject[PAPICMSService]
      implicit override val languageRepository: LanguageRepository         = inject[LanguageRepository]
      implicit override val bookingHistoryService: BookingHistoryService   = inject[BookingHistoryService]
      override val zenithService: ZenithService                            = inject[ZenithService]
      override val summaryService: ContentSummaryServiceWrapper            = inject[ContentSummaryServiceWrapper]
      override val requestBuilderService: RequestBuilderService            = inject[RequestBuilderService]
      override val dfRecommendationService: DFRecommendationServiceWrapper = inject[DFRecommendationServiceWrapper]
      override val reportingService: MetricsReporter                       = inject[MetricsReporter]
      override val cityObjectInfoService: CityObjectInfoService            = inject[CityObjectInfoService]
      override val jarvisService: JarvisService                            = inject[JarvisService]
      override val featureStoreService: FeatureStoreService                = inject[FeatureStoreService]
    }

    bind[RetryGauge] to new RetryGauge(
      new BaseCounterMetric().metrics.meter("retry"),
      new BaseCounterMetric().metrics.meter("content_retry"),
      new BaseCounterMetric().metrics.meter("df_retry")
    )

    bind[DFRequestFilterConfig] to DFRequestFilterConfig()
    bind[SoldOutRoomsSetting] to SoldOutRoomsSetting()
    bind[LimitMasterRoomSetting] to LimitMasterRoomSetting()
    bind[HotelShutdownSetting] to HotelShutdownSetting()
    bind[PriceSnapshotSettings] to PriceSnapshotSettings()
    bind[FireDrillServiceSettings] to FireDrillServiceSettings()

    bind[RecommendedRoomsService] to new RecommendedRoomsServiceImpl(inject[FeatureStoreService])

    bind[ElasticFilterOptimizer] to new ElasticFilterOptimizer
    bind[ElasticAggFilterOptimizer] to new ElasticAggFilterOptimizer
    bind[PriceStreamClientSettings] to PriceStreamClientSettings()

    bind[SoldOutRoomPricesService] to new SoldOutRoomPricesServiceImpl {
      override protected val exchangeDataService: ExchangeDataService = inject[ExchangeDataService]
      override protected val soldOutRoomsPriceRepositoryAgsql: SoldOutRoomsPriceRepositoryAgsql =
        inject[SoldOutRoomsPriceRepositoryAgsql]
    }

    bind[CircuitBreakerWrapper]
      .identifiedBy('CAPICircuitBreakerForProperty) to CircuitBreakerWrapperBuilder
      .createCircuitBreakerWrapperForClient(
        inject[ExternalServiceCircuitBreakerSettings].capiCircuitBreakerSettings,
        inject[ActorSystem].scheduler,
        ExecutionContexts.defaultContext,
        inject[MetricsReporter]
      )

    bind[CAPIService] to new BaseServiceModuleCached(inject[PAPICMSService]) with CAPIServiceImpl {
      implicit override val actorSystem: ActorSystem   = inject[ActorSystem]
      implicit override val materializer: Materializer = inject[ActorMaterializer]
      implicit override val capiEC: ExecutionContext   = ExecutionContexts.defaultContext
      override val tracer: Tracer                      = inject[Tracer]
      override val circuitBreaker: CircuitBreakerWrapper =
        inject[CircuitBreakerWrapper](identified by 'CAPICircuitBreakerForProperty)
    }

    bind[PropertyFilterSortingService] to new PropertyFilterSortingServiceImpl {}

    private val paymentApiConfig = config.getConfig("payment-api-utility")

    bind[InstallmentRepository] to new InstallmentRepositoryDefault(
      agCacheProvider.getL1L2Cache("InstallmentRepository", cacheConfig = CacheConfig.configForService),
      RemoteCacheSettings().expiration,
      defaultEC,
      paymentApiConfig,
      inject[MetricsReporter]
    )

    bind[InstallmentDetailsBuilder] to new InstallmentDetailsBuilderDefault(
      defaultEC,
      inject[InstallmentRepository],
      paymentApiConfig
    )

    bind[InstallmentService] to new InstallmentServiceDefault {
      override val installmentBuilder: InstallmentDetailsBuilder = inject[InstallmentDetailsBuilder]
    }

    bind[EnrichMultiRoomSuggestionService] to new EnrichMultiRoomSuggestionServiceImpl {
      override val enrichPricingService: EnrichPricingService             = inject[EnrichPricingService]
      override val pointsMaxEnricher: PointsMaxEnrichHelper               = inject[PointsMaxEnrichHelper]
      override val corBreakdownEnrichHelper: CorBreakdownEnrichHelper     = inject[CorBreakdownEnrichHelper]
      override val pricingMessageEnrichHelper: PricingMessageEnrichHelper = inject[PricingMessageEnrichHelper]
      implicit override val ec: ExecutionContext                          = ExecutionContexts.defaultContext
    }

    bind[GrowthProgramClient] to new GrowthProgramClientImpl(
      GrowthProgramClientSettings.apply.withOpenTelemetry(inject[OpenTelemetry])
    )

    bind[CircuitBreakerWrapper]
      .identifiedBy('GrowthProgramCircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].growthProgramCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[GrowthProgramService] to new GrowthProgramServiceImpl {
      override val gpClient: GrowthProgramClient = inject[GrowthProgramClient]
      override val circuitBreaker: CircuitBreakerWrapper =
        inject[CircuitBreakerWrapper](identified by 'GrowthProgramCircuitBreaker)

      override def reportingService: MetricsReporter = inject[MetricsReporter]

      override def tracer: Tracer = inject[Tracer]
    }

    private val jarvisSettings = JarvisSettings()

    private lazy val jarvisHttpClientSettings =
      HttpClientSettingsBuilder(serviceName = jarvisSettings.service, config)
        .withClientSettings(ClientSettings(Some(jarvisSettings.totalTimeout)))
        .withOpenTelemetry(inject[OpenTelemetry])
        .build()

    bind[CircuitBreakerWrapper]
      .identifiedBy('JarvisCircuitBreaker) to CircuitBreakerWrapperBuilder.createCircuitBreakerWrapperForClient(
      inject[ExternalServiceCircuitBreakerSettings].jarvisCircuitBreakerSettings,
      inject[ActorSystem].scheduler,
      ExecutionContexts.defaultContext,
      inject[MetricsReporter]
    )

    bind[DefaultApi[Future]] to new DefaultApi[Future](
      HttpClient[Future](jarvisHttpClientSettings)(
        inject[SttpBackend[Future, Any]]("sttpBackendJarvis")
      ),
      jarvisSettings.urls.headOption.getOrElse("")
    )(catsStdInstancesForFuture(ExecutionContexts.defaultContext))

    bind[JarvisClient] to new JarvisClientDefault(
      inject[DefaultApi[Future]],
      inject[CircuitBreakerWrapper](identified by 'JarvisCircuitBreaker)
    )(
      ExecutionContexts.defaultContext
    )

    bind[JarvisService] to new BaseReportingModule with JarvisServiceDefault {
      val jarvisClient: JarvisClient = inject[JarvisClient]
    }

    abstract class BaseReportingModule extends ContextScheduledMetricsWithTracing {
      val reportingService: MetricsReporter = AdpMetricsReporter
    }

    class BaseCounterMetric extends BaseReportingModule

    class BaseServiceModule(
        val cmsService: PAPICMSService,
        val ec: ExecutionContext = ExecutionContexts.defaultContext
    ) extends BaseReportingModule

    class BaseServiceModuleWithLanguage(
        val cmsService: PAPICMSService,
        val ec: ExecutionContext = ExecutionContexts.defaultContext
    ) extends BaseReportingModule

    class BaseServiceModuleSqlIOCached(
        val ioSql: IO[Sql[IO, SqlConnection[IO]]],
        val cmsService: PAPICMSService,
        val ec: ExecutionContext = ExecutionContexts.defaultContext,
        val dbEc: ExecutionContext = ExecutionContexts.defaultContext
    ) extends BaseReportingModule

    class BaseServiceModuleCached(
        val cmsService: PAPICMSService,
        val ec: ExecutionContext = ExecutionContexts.defaultContext,
        val dbEc: ExecutionContext = ExecutionContexts.defaultContext
    ) extends BaseReportingModule

    trait ContextScheduledMetricsWithTracing extends ContextScheduledMetrics with WithTracer {
      override val tracer: Tracer = inject[Tracer]
    }

  }

  trait Ec {
    implicit val ec: ExecutionContext = ExecutionContexts.defaultContext
  }

}
