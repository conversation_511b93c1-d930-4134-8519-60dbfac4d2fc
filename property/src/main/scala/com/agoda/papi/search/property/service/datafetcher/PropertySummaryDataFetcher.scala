package com.agoda.papi.search.property.service.datafetcher

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.content.models.circe.Serialization.{ ContentSummaryEncoder, ContentSummaryRequestEncoder }
import com.agoda.content.models.propertycontent.ContentSummary
import com.agoda.content.models.request.sf.ContentSummaryRequest
import com.agoda.papi.content.graphql.ContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.pricing.graphql.PricingPropertiesRequestEnvelope
import com.agoda.papi.search.common.externaldependency.chunkable.{ ContentService, DFService }
import com.agoda.papi.search.common.externaldependency.customer.{ CAPIContext, CAPIService }
import com.agoda.papi.search.common.util.concurrent.FZip
import com.agoda.papi.search.common.util.context.{ Con<PERSON><PERSON><PERSON><PERSON>, ExperimentContext, TrafficUtil }
import com.agoda.papi.search.common.util.measurement.MetricsHelper
import com.agoda.papi.search.internalmodel.content.PAPIContentSummary
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultTypes
import com.agoda.papi.search.internalmodel.pricing.PAPIHotelPricing
import com.agoda.papi.search.internalmodel.request.PricingPropertiesRequestWrapper
import com.agoda.papi.search.internalmodel.{ PropertySponsoredDetail, PropertySummaryEnvelope }
import com.agoda.papi.search.types.{ HotelIdType, PropertySummaryEnvelopeList }
import com.agoda.papi.search.utils.PAPIContentSummaryHelper._
import com.agoda.platform.service.context.GlobalContext
import com.agoda.pricing.models.circe.ResponseEncoder
import io.circe.syntax._
import io.opentracing.Tracer
import models.starfruit.PricingRequestParameters
import api.request.FeatureFlag

import scala.concurrent.{ ExecutionContext, Future }

trait PropertySummaryDataFetcher {

  def fetchPropertiesSummary(
      propertyIds: List[Long],
      pricingRequestParametersOpt: Option[PricingRequestParameters],
      contentSummaryRequestOpt: Option[ContentSummaryRequest],
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[PropertySummaryEnvelopeList]

}

class DefaultPropertySummaryDataFetcher(
    dfService: DFService,
    contentService: ContentService,
    capiService: CAPIService,
    reporter: MetricsReporter,
    tracing: Tracer
) extends PropertySummaryDataFetcher with MetricsHelper with ResponseEncoder {

  override def fetchPropertiesSummary(
      propertyIds: List[Long],
      pricingRequestParametersOpt: Option[PricingRequestParameters],
      contentSummaryRequest: Option[ContentSummaryRequest],
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[PropertySummaryEnvelopeList] =
    if (propertyIds.isEmpty || (pricingRequestParametersOpt.isEmpty && contentSummaryRequest.isEmpty)) {
      Future.successful(List.empty)
    } else {
      val capiContext = CAPIContext(
        globalContext.getMemberId.getOrElse(0L),
        TrafficUtil.fetchTrafficInfo(globalContext),
        globalContext.getWhiteLabelKey,
        globalContext.getCorrelationId.getOrElse("no-search-id"),
        globalContext.getPlatformId.getOrElse(-1),
        globalContext.getOrigin.getOrElse("no-origin"),
        globalContext.getRequestAttempt.forall(_ > 1),
        "propertysummarysearch",
        isGQL = true,
        globalContext.getEnv
      )

      val capiRequestContextF: Future[Option[String]] = pricingRequestParametersOpt
        .map { pricingRequestParameters =>
          capiService.getMemberUserContext(capiContext)
        }
        .getOrElse(Future.successful(None))

      val capiEmailDomainF: Future[String] = pricingRequestParametersOpt
        .map { pricingRequestParameters =>
          capiService.getEmailDomain(capiContext)
        }
        .getOrElse(Future.successful(""))

      val contentSummaryListFOpt: Option[Future[List[Wrapper[PAPIContentSummary]]]] = contentSummaryRequest.map {
        contentSummaryReq =>
          val envelope = ContentPropertiesSummaryRequestEnvelope(
            propertyIds,
            Wrapper(contentSummaryReq, contentSummaryReq.asJson),
            None
          )
          contentService.process(envelope, globalContext, experimentContext)
      }

      val pricingListFOpt: Option[Future[List[Wrapper[PAPIHotelPricing]]]] = pricingRequestParametersOpt.map {
        pricingRequestParameters =>
          // FUSION-6248: Filter out ReturnHotelNotReadyIfPullNotReady flag on B side

          val filteredPricingRequestParameters = {
            val shouldFilterFlag = contextHolder.experimentContext.isBVariant(
              contextHolder.allocationContext.ignoreReturnHotelNotReadyIfPullNotReady
            )
            if (shouldFilterFlag) {
              val originalFeatureFlags = pricingRequestParameters.pricing.featureFlag
              val filteredFeatureFlags =
                originalFeatureFlags.filterNot(_ == FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
              val updatedPricing = pricingRequestParameters.pricing.copy(featureFlag = filteredFeatureFlags)
              pricingRequestParameters.copy(pricing = updatedPricing)
            } else {
              pricingRequestParameters
            }
          }

          val envelope = PricingPropertiesRequestEnvelope(
            propertyIds,
            Wrapper(filteredPricingRequestParameters, filteredPricingRequestParameters.asJson)
          )
          for {
            capiRequestContext <- capiRequestContextF
            capiEmailDomain    <- capiEmailDomainF
            dfResult <- dfService.process(
              PricingPropertiesRequestWrapper(
                envelope,
                capiRequestContext = capiRequestContext,
                capiEmailDomain = capiEmailDomain
              ),
              globalContext,
              experimentContext
            )
          } yield dfResult
      }

      val contentSummaryMapF: Future[Map[HotelIdType, Wrapper[PAPIContentSummary]]] = contentSummaryListFOpt
        .getOrElse(Future.successful(List.empty))
        .map(_.map(contentSummary => contentSummary.t.propertyId -> contentSummary)(collection.breakOut))
      val pricingMapF: Future[Map[HotelIdType, Wrapper[PAPIHotelPricing]]] = pricingListFOpt
        .getOrElse(Future.successful(List.empty))
        .map(_.map(pricing => pricing.t.hotelId -> pricing)(collection.breakOut))

      FZip.apply(pricingMapF, contentSummaryMapF).map { case (pricingMap, contentSummaryMap) =>
        propertyIds.map { propertyId =>
          PropertySummaryEnvelope(
            propertyId,
            contentSummaryMap
              .get(propertyId)
              .getOrElse(PropertySummaryDataFetcher.emptyContentSummaryWrapper(propertyId)),
            pricingMap.get(propertyId),
            None,
            None,
            None,
            PropertySponsoredDetail(),
            PropertyResultTypes.NormalProperty
          )
        }
      }
    }

  override val metricsReporter: MetricsReporter = reporter

  override def tracer: Tracer = tracing
}

object PropertySummaryDataFetcher {

  def emptyContentSummaryWrapper(propertyId: HotelIdType): Wrapper[PAPIContentSummary] = {
    val emptyContentSummary = ContentSummary(propertyId, None, None, None, None, Vector.empty, None, None, None, None)
    Wrapper(emptyContentSummary, emptyContentSummary.asJson)
  }

}
