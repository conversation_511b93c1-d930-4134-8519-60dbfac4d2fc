package com.agoda.papi.search.property.externaldependency.logger

import com.agoda.core.search.models.enumeration.FunnelType
import com.agoda.papi.search.property.externaldependency.logger.papisearchmessage.EscapesPackageSummary
import com.agoda.supply.calc.proto.StayPackageType
import request.PropertyRequest
import transformers.{ EnrichedChildRoom, Properties }

object EscapesSummaryHelper {
  def isEscapesPackage(room: EnrichedChildRoom): Boolean =
    room.stayPackageType.map(_ == StayPackageType.Escapes.value).getOrElse(false)

  def getEscapesPackageSummary(request: PropertyRequest, response: Properties): EscapesPackageSummary = {
    val enableEscapesFlag                         = request.featureFlag.flatMap(_.enableEscapesPackage).getOrElse(false)
    val (hotelsWithEscapes, hotelsWithoutEscapes) = response.property.partition(_.rooms.exists(isEscapesPackage(_)))
    val totalEscapesPackages                      = hotelsWithEscapes.flatMap(_.rooms.filter(isEscapesPackage(_))).size
    new EscapesPackageSummary(
      enableEscapesFlag,
      totalEscapesPackages,
      hotelsWithoutEscapes.size
    )
  }

  def isValidEscapesFlow(
      request: PropertyRequest,
      isRoomFilterEnabled: Boolean,
      isAsoFilterMigration: Boolean
  ): Boolean = {
    val enableEscapesFlag =
      if (isAsoFilterMigration) !request.featureFlag.flatMap(_.disableEscapesPackage).getOrElse(false)
      else request.featureFlag.flatMap(_.enableEscapesPackage).getOrElse(false)
    val funnelType    = request.getFunnel
    val isValidFunnel = funnelType != FunnelType.Packages && funnelType != FunnelType.MultiHotel

    isRoomFilterEnabled && enableEscapesFlag && isValidFunnel
  }

}
