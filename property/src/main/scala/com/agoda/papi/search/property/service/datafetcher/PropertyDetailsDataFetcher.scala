package com.agoda.papi.search.property.service.datafetcher

import com.agoda.common.graphql.model.Wrapper
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.information.{ InformationResponse, LocalInformation }
import com.agoda.content.models.propertycontent.{
  ContentImagesResponse,
  ContentInformationSummary,
  ContentReviewScoreResponse,
  ContentReviewSummariesResponse
}
import com.agoda.papi.content.graphql._
import com.agoda.papi.search.common.externaldependency.content._
import com.agoda.papi.search.common.externaldependency.customer.CAPIService
import com.agoda.papi.search.common.externaldependency.promocode.GandalfService
import com.agoda.papi.search.common.externaldependency.growthprogram.GrowthProgramService
import com.agoda.papi.search.common.model.content.ContentWithPropertyId
import com.agoda.papi.search.common.util.context.{ Con<PERSON><PERSON><PERSON><PERSON>, ExperimentContext, WithCorrelationId }
import com.agoda.papi.search.common.util.measurement.{ MeasurementName, MetricsHelper }
import com.agoda.papi.search.internalmodel.growthprogram.{ GrowthProgramInfoRequestEnvelope, PAPIGrowthProgramInfo }
import com.agoda.papi.search.internalmodel.promocode.{ BannerCampaignGroupRequest, BannerCampaignInfo }
import com.agoda.papi.search.internalmodel.response.{ HostProfileResponse, HostPropertyResponse }
import com.agoda.papi.search.types.PropertyId
import com.agoda.platform.service.context.GlobalContext
import com.agoda.winterfell.output.TrustedHostInfo
import io.opentracing.Tracer
import com.agoda.papi.search.internalmodel.request.HostProfileRequest

import scala.concurrent.{ ExecutionContext, Future }
import scala.util.Try

trait PropertyDetailsDataFetcher {

  def fetchContentImagesResponse(
      contentGroupImagesRequest: ContentGroupImagesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentImagesResponse]]]

  def fetchContentReviewScoreResponse(
      contentReviewScoreRequest: ContentGroupReviewScoreRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentReviewScoreResponse]]]

  def fetchContentReviewSummariesResponse(
      contentReviewSummariesRequest: ContentGroupReviewSummariesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentReviewSummariesResponse]]]

  def fetchContentInformationSummaryResponse(
      contentInformationSummaryRequest: ContentInformationSummaryGroupRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentInformationSummary]]]

  def fetchContentInformationResponse(
      contentInformationRequest: ContentInformationGroupRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[InformationResponse]]]

  def fetchContentExperiencesResponse(
      contentExperienceRequest: ContentGroupExperienceRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[Experiences]]]

  def fetchLocalInformationResponse(
      contentLocalInformationRequest: ContentGroupLocalInformationRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[LocalInformation]]]

  def fetchEngagementResponse(
      contentGroupEngagementRequest: ContentGroupEngagementRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchContentFeaturesResponse(
      contentGroupFeaturesRequest: ContentGroupFeaturesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchContentHighlightsResponse(
      contentGroupHighlightsRequestEnvelope: ContentGroupHighlightsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchContentQnaResponse(
      contentGroupQnaRequestEnvelope: ContentGroupQnaRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchContentTopicsResponse(
      contentGroupTopicsRequestEnvelope: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchContentRateCategoryResponse(
      contentGroupRateCategoryRequestEnvelope: ContentGroupRateCategoryRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]]

  def fetchHostProfile(
      propertyId: PropertyId,
      hostProfileRequest: Option[HostProfileRequest]
  )(implicit
      ec: ExecutionContext,
      ctx: ContextHolder
  ): Future[Option[HostProfileResponse]]

  def fetchPropertyBannerCampaignResponse(
      bannerCampaignGroupRequest: BannerCampaignGroupRequest,
      globalContext: GlobalContext
  )(implicit
      ec: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Map[PropertyId, List[BannerCampaignInfo]]]

  def fetchGrowthProgramInfoResponse(
      growthProgramInfoRequestEnvelope: GrowthProgramInfoRequestEnvelope
  )(implicit
      ec: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Map[PropertyId, PAPIGrowthProgramInfo]]

}

class DefaultPropertyDetailsDataFetcher(
    imageService: ImageService,
    reviewScoreService: ReviewScoreService,
    reviewSummariesService: ReviewSummariesService,
    informationSummaryService: InformationSummaryService,
    informationService: InformationService,
    localInformationService: LocalInformationService,
    engagementService: EngagementService,
    experiencesService: ExperiencesService,
    featuresService: FeaturesService,
    highlightsService: HighlightsService,
    qnaService: QnaService,
    topicsService: TopicsService,
    rateCategoryService: RateCategoryService,
    capiService: CAPIService,
    reporter: MetricsReporter,
    tracing: Tracer,
    gandalfService: GandalfService,
    growthProgramService: GrowthProgramService
) extends PropertyDetailsDataFetcher with MetricsHelper {
  override val metricsReporter: MetricsReporter = reporter
  override val tracer: Tracer                   = tracing
  private val avatarPrefix: String              = "https://pix7.agoda.net/hotelImages"

  override def fetchHostProfile(
      propertyId: PropertyId,
      hostProfileRequest: Option[HostProfileRequest]
  )(implicit
      ec: ExecutionContext,
      ctx: ContextHolder
  ): Future[Option[HostProfileResponse]] = {
    val isHostPropertyEnabled = hostProfileRequest.flatMap(_.isHostPropertyEnabled)
    val trustedHostF: Future[TrustedHostInfo] =
      if (isHostPropertyEnabled.contains(true)) {
        capiService.getHostPropertiesInfo(propertyId)
      } else {
        capiService.getTrustedHostInfo(propertyId)
      }
    trustedHostF
      .map { trustedHost =>
        Try {
          val hostProperties: List[HostPropertyResponse] = trustedHost.properties.map { hProp =>
            HostPropertyResponse(
              id = hProp.id,
              bookings = Option(hProp.bookings),
              reviewCount = Option(hProp.reviewCount),
              reviewAvg = Option(hProp.reviewAvg)
            )
          }(collection.breakOut)
          HostProfileResponse(
            userId = Option(trustedHost.userId),
            picture = trustedHost.picture.map(p => s"$avatarPrefix$p"),
            properties = Option(hostProperties),
            responseRate = Option(trustedHost.responseRate),
            responseTimeSeconds = trustedHost.responseTimeSeconds,
            displayName = trustedHost.displayName,
            firstName = Option(trustedHost.firstName),
            lastName = Option(trustedHost.lastName),
            hostLevel = Option(trustedHost.getLevelInt),
            joined = trustedHost.joined.map(_.toDate),
            averageReviewScore = trustedHost.getAverageReview,
            totalBookings = Option(trustedHost.getTotalBookings),
            totalReviews = Option(trustedHost.getTotalReviews),
            cityName = trustedHost.cityName,
            countryName = trustedHost.countryName,
            stateName = trustedHost.stateName,
            hostType = trustedHost.tprmHostType,
            userDescription = trustedHost.userDescription
          )
        }.toOption
      }
      .recover { case _ => None }
  }

  def fetchContentImagesResponse(
      contentGroupImagesRequest: ContentGroupImagesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentImagesResponse]]] =
    measure(MeasurementName.DataFetch.Images) {
      val contentImagesResponseF =
        imageService.getContentImagesResponse(contentGroupImagesRequest, globalContext, experimentContext)
      for {
        contentImagesResponse <- contentImagesResponseF
      } yield contentImagesResponse.map { result =>
        result.t.propertyId -> result
      }(collection.breakOut)
    }

  def fetchContentReviewScoreResponse(
      contentReviewScoreRequest: ContentGroupReviewScoreRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentReviewScoreResponse]]] =
    measure(MeasurementName.DataFetch.ReviewScore) {
      val contentReviewScoreResponseF =
        reviewScoreService.getContentReviewScoreResponse(contentReviewScoreRequest, globalContext, experimentContext)
      for {
        contentReviewScoreResponse <- contentReviewScoreResponseF
      } yield contentReviewScoreResponse.map { result =>
        result.t.propertyId -> result
      }(collection.breakOut)
    }

  def fetchContentReviewSummariesResponse(
      contentReviewSummariesRequest: ContentGroupReviewSummariesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentReviewSummariesResponse]]] =
    measure(MeasurementName.DataFetch.ReviewSummaries) {
      val contentReviewSummariesResponseF = reviewSummariesService.getContentReviewSummariesResponse(
        contentReviewSummariesRequest,
        globalContext,
        experimentContext
      )
      for {
        contentReviewSummariesResponse <- contentReviewSummariesResponseF
      } yield contentReviewSummariesResponse.map { result =>
        result.t.propertyId -> result
      }(collection.breakOut)
    }

  override def fetchContentInformationSummaryResponse(
      contentInformationSummaryRequest: ContentInformationSummaryGroupRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentInformationSummary]]] =
    measure(MeasurementName.DataFetch.InformationSummary) {
      val contentInformationSummaryResponseF = informationSummaryService.getInformationSummaryResponse(
        contentInformationSummaryRequest,
        globalContext,
        experimentContext
      )
      for {
        contentInformationSummary <- contentInformationSummaryResponseF
      } yield contentInformationSummary.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchContentInformationResponse(
      contentInformationRequest: ContentInformationGroupRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[InformationResponse]]] =
    measure(MeasurementName.DataFetch.Information) {
      val contentInformationResponseF =
        informationService.getInformationResponse(contentInformationRequest, globalContext, experimentContext)
      for {
        contentInformation <- contentInformationResponseF
      } yield contentInformation.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchContentExperiencesResponse(
      contentExperienceRequest: ContentGroupExperienceRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[Experiences]]] =
    measure(MeasurementName.DataFetch.Experiences) {
      val experiencesF = experiencesService.getExperiences(contentExperienceRequest, globalContext, experimentContext)
      for {
        experiences <- experiencesF
      } yield experiences.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  def fetchLocalInformationResponse(
      contentLocalInformationRequestEnvelope: ContentGroupLocalInformationRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): Future[Map[PropertyId, Wrapper[LocalInformation]]] =
    measure(MeasurementName.DataFetch.LocalInformation) {
      val localInformationF = localInformationService.getLocalInformation(
        contentLocalInformationRequestEnvelope,
        globalContext,
        experimentContext
      )
      for {
        localInformation <- localInformationF
      } yield localInformation.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchEngagementResponse(
      contentGroupEngagementRequest: ContentGroupEngagementRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.Engagement) {
      val contentEngagementResponseF =
        engagementService.getEngagementResponse(contentGroupEngagementRequest, globalContext, experimentContext)
      for {
        contentEngagement <- contentEngagementResponseF
      } yield contentEngagement.map { result =>
        result.t.propertyId.toLong -> result

      }(collection.breakOut)
    }

  override def fetchContentFeaturesResponse(
      contentGroupFeaturesRequest: ContentGroupFeaturesRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.Features) {
      val contentFeaturesResponseF =
        featuresService.getFeatures(contentGroupFeaturesRequest, globalContext, experimentContext)
      for {
        contentFeaturesResponse <- contentFeaturesResponseF
      } yield contentFeaturesResponse.map { result =>
        result.t.propertyId.toLong -> result

      }(collection.breakOut)
    }

  override def fetchContentHighlightsResponse(
      contentGroupHighlightsRequestEnvelope: ContentGroupHighlightsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.Highlights) {
      val contentHighlightsResponseF =
        highlightsService.getHighlights(contentGroupHighlightsRequestEnvelope, globalContext, experimentContext)
      for {
        contentHighlightsResponse <- contentHighlightsResponseF
      } yield contentHighlightsResponse.map { result =>
        result.t.propertyId.toLong -> result

      }(collection.breakOut)
    }

  override def fetchContentQnaResponse(
      contentGroupQnaRequestEnvelope: ContentGroupQnaRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.Qna) {
      val contentQnaResponseF = qnaService.getQna(contentGroupQnaRequestEnvelope, globalContext, experimentContext)
      for {
        contentQnaResponse <- contentQnaResponseF
      } yield contentQnaResponse.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchContentTopicsResponse(
      contentGroupTopicsRequestEnvelope: ContentGroupTopicsRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.Topics) {
      val contentTopicsResponseF =
        topicsService.getTopics(contentGroupTopicsRequestEnvelope, globalContext, experimentContext)
      for {
        contentTopicsResponse <- contentTopicsResponseF
      } yield contentTopicsResponse.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchContentRateCategoryResponse(
      contentGroupRateCategoryRequestEnvelope: ContentGroupRateCategoryRequestEnvelope,
      globalContext: GlobalContext,
      experimentContext: ExperimentContext
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[Map[PropertyId, Wrapper[ContentWithPropertyId]]] =
    measure(MeasurementName.DataFetch.RateCategory) {
      val contentRateCategoryResponseF =
        rateCategoryService.getRateCategory(contentGroupRateCategoryRequestEnvelope, globalContext, experimentContext)
      for {
        contentQnaResponse <- contentRateCategoryResponseF
      } yield contentQnaResponse.map { result =>
        result.t.propertyId.toLong -> result
      }(collection.breakOut)
    }

  override def fetchPropertyBannerCampaignResponse(
      bannerCampaignGroupRequest: BannerCampaignGroupRequest,
      globalContext: GlobalContext
  )(implicit
      ec: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Map[PropertyId, List[BannerCampaignInfo]]] = {
    val campaignListF = bannerCampaignGroupRequest.propertyIds.map { propertyId =>
      val resultF = gandalfService.getBannerCampaignForProperty(
        propertyId,
        bannerCampaignGroupRequest.bannerCampaignRequest.checkIn,
        globalContext
      )
      resultF.map(result => (propertyId, result))
    }
    Future.sequence(campaignListF).map { campaignList =>
      val campaignMap: Map[PropertyId, List[BannerCampaignInfo]] =
        campaignList.map(campaign => (campaign._1, campaign._2))(collection.breakOut)
      campaignMap
    }
  }

  override def fetchGrowthProgramInfoResponse(
      growthProgramInfoRequestEnvelope: GrowthProgramInfoRequestEnvelope
  )(implicit
      ec: ExecutionContext,
      searchContextHolder: ContextHolder
  ): Future[Map[PropertyId, PAPIGrowthProgramInfo]] = growthProgramService.getGrowthProgramInfo(
    growthProgramInfoRequestEnvelope.propertyIds,
    growthProgramInfoRequestEnvelope.request
  )(ec, searchContextHolder.globalContext)

}
