package com.agoda.papi.search.property.service

import _root_.request.{ PropertyRequest, Recommendation, RecommendationFilter, SearchTypes }
import com.agoda.content.models.{ CityIdType => _, _ }
import com.agoda.feast.proto.types.Value
import com.agoda.ml.recommendation.zenith.recommendation.{ HotelRecoResponse, RecommendationResponse => ZenithResponse }
import com.agoda.papi.search.internalmodel.DegradeFeatures.RecommendationProperties
import com.agoda.jarvis.interfacing.client.model.SimilarPropertiesResponse
import com.agoda.ml.recommendation.zenith.common.RecommendationType
import com.agoda.ml.recommendation.zenith.common.RecommendationType.{
  PastBookingsRecommendation,
  RecentlyViewRecommendation
}
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.model.Components
import com.agoda.papi.search.common.service.fs.DefaultFeatureStoreServiceUtils.{ valToAnyRef, FeatureSets }
import com.agoda.papi.search.common.service.fs.DefaultFeatureStoreServiceUtils.FeatureStoreEntities.HotelIdEntity
import com.agoda.papi.search.common.service.fs.FeatureStoreService
import com.agoda.papi.search.common.util.context.ContextHolder
import com.agoda.papi.search.common.util.logger.PAPIAppLogger
import com.agoda.papi.search.common.util.measurement.APIInstrumentedMetric
import enumerations.RecommendationFilterComparisonTypes.{ Equal, Less, More }
import enumerations.{ RecommendationFilterComparisonType, RecommendationFilterFieldType }
import com.agoda.papi.search.property.service.ClusterService.{
  ContentSummaryServiceWrapper,
  DFRecommendationServiceWrapper,
  PricingAndContentToRecommendation
}
import com.agoda.papi.search.property.service.ClusterService.{
  ContentSummaryServiceWrapper,
  DFRecommendationServiceWrapper,
  PricingAndContentToRecommendation
}
import transformers.{ MatchScore, RecommendationHotel, RecommendationResponse, RecommendationResult }
import types.{ CityIdType, HotelIdType }
import util.JMapBuilder
import enumerations.RecommendationFilterFieldTypes.{ Price, ReviewScore, StarRating }
import com.agoda.papi.search.property.service.jarvis.JarvisService
import models.starfruit.DisplayPrice

import scala.collection.mutable
import scala.concurrent.{ ExecutionContext, Future }
import scala.util.{ Failure, Success, Try }

trait RecommendationComposerService extends APIInstrumentedMetric with PAPIAppLogger {
  private class OriginData(val recommendationResponse: RecommendationResponse, val getPrice: DisplayPrice => Double)
  val zenithService: ZenithService
  val jarvisService: JarvisService
  val summaryService: ContentSummaryServiceWrapper
  val requestBuilderService: RequestBuilderService
  val dfRecommendationService: DFRecommendationServiceWrapper
  val featureStoreService: FeatureStoreService

  def composeComparisonRecommendationList(
      request: PropertyRequest,
      suggestedPricesF: Future[Map[HotelIdType, DisplayPrice => Double]] = Future.successful(Map.empty)
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[HotelIdType, List[RecommendationResponse]]] = {
    implicit val req: PropertyRequest = request
    val useJarvisService: Boolean = contextHolder.experimentContext.isBVariant(
      contextHolder.allocationContext.migrateSimilarPropertiesToJarvis
    )

    if (useJarvisService) {
      composeAndFormatRecommendation[HotelIdType, SimilarPropertiesResponse](
        idsToRecommend = request.context.propertyIds,
        request = request,
        recommendationRequestOption = request.recommendation,
        recommendationType = RecommendationType.RecentlyViewRecommendation,
        getRecommendationFromService = jarvisService.getSimilarPropertiesResponse,
        extractRecommendedIdsFromResponse = _.hotelId.getOrElse(Seq.empty),
        suggestedPricesF = suggestedPricesF,
        extractMatchScoresFromResponse = _.matchScores
          .map(_.map(x => x.hotelId -> MatchScore(x.score, x.categoryId.getOrElse(Seq.empty))).toMap)
          .getOrElse(Map.empty)
      )
    } else {
      composeAndFormatRecommendation[HotelIdType, HotelRecoResponse](
        idsToRecommend = request.context.propertyIds,
        request = request,
        recommendationRequestOption = request.recommendation,
        recommendationType = RecommendationType.RecentlyViewRecommendation,
        getRecommendationFromService = zenithService.getRecommendations,
        extractRecommendedIdsFromResponse = _.hotelId,
        suggestedPricesF = suggestedPricesF,
        extractMatchScoresFromResponse = _.matchScores.map(x => x.hotelId -> MatchScore(x.score, x.categoryId)).toMap
      )
    }
  }

  def composeRecentlyViewedRecommendationList(
      request: PropertyRequest,
      hotelToCityMap: Map[HotelIdType, CityIdType]
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[HotelIdType, List[RecommendationResponse]]] =
    composeZenithRecommendationList(
      request,
      hotelToCityMap,
      request.personalizedRecommendation,
      RecentlyViewRecommendation
    )

  def composeZenithRecommendationListFromCityId(
      request: PropertyRequest,
      cityId: Long,
      recommendation: Recommendation,
      recommendationType: RecommendationType
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[HotelIdType, List[RecommendationResponse]]] =
    composeAndFormatRecommendation[CityIdType, ZenithResponse](
      Seq(cityId),
      request,
      Some(recommendation),
      recommendationType,
      zenithService.getCityRecommendations,
      (zenithResponse: ZenithResponse) => zenithResponse.scores.map(_.hotelId)
    )

  def composePastBookingsRecommendationList(
      request: PropertyRequest,
      hotelToCityMap: Map[HotelIdType, CityIdType]
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[HotelIdType, List[RecommendationResponse]]] =
    composeZenithRecommendationList(
      request,
      hotelToCityMap,
      request.pastBookingsRecommendation,
      PastBookingsRecommendation
    )

  def composeZenithRecommendationList(
      request: PropertyRequest,
      hotelToCityMap: Map[HotelIdType, CityIdType],
      recommendationRequestOption: Option[Recommendation],
      recommendationType: RecommendationType = RecentlyViewRecommendation
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[HotelIdType, List[RecommendationResponse]]] = {
    implicit val req: PropertyRequest = request

    val propertyIdsToRecommend            = request.context.propertyIds
    val distinctCityIds: List[CityIdType] = propertyIdsToRecommend.flatMap(hotelToCityMap.get).distinct

    val cityIdToRecommendationMapF: Future[Map[CityIdType, List[RecommendationResponse]]] =
      composeAndFormatRecommendation[CityIdType, ZenithResponse](
        distinctCityIds,
        request,
        recommendationRequestOption,
        recommendationType,
        zenithService.getCityRecommendations,
        (zenithResponse: ZenithResponse) => zenithResponse.scores.map(_.hotelId)
      )

    val propertyIdToRecommendationMapF: Future[Map[HotelIdType, List[RecommendationResponse]]] = for {
      cityIdToRecommendationMap <- cityIdToRecommendationMapF
    } yield propertyIdsToRecommend.map { propertyId =>
      val cityId = hotelToCityMap.getOrElse(propertyId, 0L)
      propertyId -> cityIdToRecommendationMap.getOrElse(cityId, List.empty)
    }.toMap

    propertyIdToRecommendationMapF
  }

  def composeAndFormatRecommendation[IN_ID <: Long, OUT](
      idsToRecommend: Seq[IN_ID],
      request: PropertyRequest,
      recommendationRequestOption: Option[Recommendation],
      recommendationType: RecommendationType,
      getRecommendationFromService: (IN_ID, PropertyRequest, Recommendation, RecommendationType) => Future[Option[OUT]],
      extractRecommendedIdsFromResponse: OUT => Seq[IN_ID],
      suggestedPricesF: Future[Map[HotelIdType, DisplayPrice => Double]] = Future.successful(Map.empty),
      extractMatchScoresFromResponse: OUT => Map[Long, MatchScore] = (_: OUT) => Map.empty[Long, MatchScore]
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[IN_ID, List[RecommendationResponse]]] =
    if (
      !recommendationRequestOption.exists(r => ZenithServiceDefault.getNumberOfRecommendation(r) > 0) || contextHolder
        .isFeatureDegraded(RecommendationProperties)
    ) {
      Future.successful(Map.empty)
    } else {
      recommendationRequestOption
        .map { recommendationRequest =>
          val resultFutures = idsToRecommend.map(id =>
            {
              val responseF = getRecommendationFromService(id, request, recommendationRequest, recommendationType)
                .recover { case e: Throwable =>
                  warn(Components.PAPI, "get recommendations from service failed", Some(e))
                  None
                }
              responseF.flatMap { responseO =>
                responseO
                  .map { response =>
                    getFilteredRecommendationResponse(
                      request,
                      recommendationRequest,
                      extractRecommendedIdsFromResponse(response),
                      suggestedPricesF,
                      extractMatchScoresFromResponse(response)
                    )
                  }
                  .getOrElse(Future.successful(List.empty))
              }
            }.map(id -> _)
          )
          Future.sequence(resultFutures).map(_.toMap)
        }
        .getOrElse(Future.successful(Map.empty))
    }

  private[service] def filterValidUpTierPropertyRecommendation(
      hotelOpt: Option[RecommendationHotel],
      referencePriceOpt: Option[Double],
      priceCompareLowerBound: Double = 0.9,
      priceCompareUpperBound: Double = 1.2
  )(implicit sfpp: PropertyRequest): Boolean =
    (hotelOpt, referencePriceOpt) match {
      case (Some(propertyPricing), Some(referencePrice)) if referencePrice > 0 =>
        val recommendPrice   = propertyPricing.cheapestPriceCrossedDisplay.perBook.allInclusive
        val toReferenceRatio = recommendPrice / referencePrice
        val isTooExpensive   = toReferenceRatio > priceCompareUpperBound
        val isTooCheap       = toReferenceRatio < priceCompareLowerBound
        val isInRange        = !isTooExpensive && !isTooCheap
        reportingService.report(
          "up-tier-recommendation-property-filter",
          1,
          sfpp.context.measureTags ++ Map(
            "hasPrice"       -> "true",
            "isTooExpensive" -> isTooExpensive.toString,
            "isTooCheap"     -> isTooCheap.toString
          )
        )
        isInRange
      case _ =>
        reportingService.report(
          "up-tier-recommendation-property-filter",
          1,
          sfpp.context.measureTags ++ Map("hasPrice" -> "false")
        )
        false
    }

  private def buildUpTierPropertyIdRecommendationResponseMap(
      request: PropertyRequest,
      propertyEntities: Map[HotelIdType, Iterable[HotelIdType]],
      recommendationResults: List[RecommendationResult]
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Iterable[(HotelIdType, List[RecommendationResponse])]] = {
    val propertyIdRecommendationResultMap = recommendationResults.flatMap { recommendationResult =>
      recommendationResult.hotel.map(_.h.hotelId).map(hotelId => hotelId -> recommendationResult)
    }.toMap
    implicit val sfpp: PropertyRequest = request
    val recommendationResponsePairFutureList = propertyEntities.collect { case (propertyId, recommendedPropertyIds) =>
      val referencePriceOpt = propertyIdRecommendationResultMap
        .get(propertyId)
        .flatMap(propertyResult =>
          propertyResult.hotel.map { propertyPricing =>
            propertyPricing.cheapestPriceCrossedDisplay.perBook.allInclusive
          }
        )
      val propertyRecommendationResponseOptFutureList = recommendedPropertyIds.toList
        .flatMap(propertyIdRecommendationResultMap.get)
        .filter { property =>
          filterValidUpTierPropertyRecommendation(property.hotel, referencePriceOpt)
        }
        .sortBy(_.hotel.map(_.cheapestPriceCrossedDisplay.perBook.allInclusive).getOrElse(Double.MaxValue))
        .map(recommendationResult =>
          PricingAndContentToRecommendation
            .convertPricingAndContentToRecommendationResponse(recommendationResult, None)
        )
      Future
        .sequence(propertyRecommendationResponseOptFutureList)
        .map(recommendedProperties => (propertyId, recommendedProperties.flatten))
    }
    Future.sequence(recommendationResponsePairFutureList)
  }

  def composeUpTierRecommendationList(request: PropertyRequest)(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[Map[HotelIdType, List[RecommendationResponse]]] = {
    val propertyIds = request.context.propertyIds.distinct
    val propertyRecommendationResponseMapF = featureStoreService
      .getCompositeEntitiesFeatures(
        featureSet = FeatureSets.UPTIER_PROPERTY_RECOMMENDATION_V1,
        entities = propertyIds.map(propertyId => HotelIdEntity(propertyId)),
        features = Seq("potential_hotel_ids")
      )
      .map { entities =>
        propertyIds
          .zip(entities)
          .map { case (propertyId, entity) =>
            propertyId -> entity.flatMap {
              case (_, v) if v.`val`.isDefined => Try(valToAnyRef(v).asInstanceOf[Seq[Long]]).getOrElse(Seq.empty)
              case _                           => Seq.empty
            }
          }
          .toMap
      }
      .flatMap { propertyEntities =>
        val allPropertyIds                = (propertyIds ++ propertyEntities.values.flatten).distinct
        val recommendationPropertyRequest = buildPropertyRequest(request, allPropertyIds)
        searchRecommendationResult(recommendationPropertyRequest).flatMap { recommendationResults =>
          buildUpTierPropertyIdRecommendationResponseMap(request, propertyEntities, recommendationResults)
        }
      }
      .recover { case err: Throwable =>
        error(Components.Recommendations, "Failed to compose up-tier recommendation list", Some(err))
        Map.empty
      }
    propertyRecommendationResponseMapF.map(_.toMap)
  }

  private def getFilteredRecommendationResponse(
      originalRequest: PropertyRequest,
      recommendationRequest: Recommendation,
      recommendedIds: Seq[HotelIdType],
      suggestedPricesF: Future[Map[HotelIdType, DisplayPrice => Double]],
      matchScore: Map[HotelIdType, MatchScore]
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[List[RecommendationResponse]] = {
    implicit val req  = originalRequest
    val overridingIds = RecommendationService.getOverrideRecommendationIds(recommendationRequest)
    val idsToShow     = overridingIds ++ recommendedIds
    val originIds     = recommendationRequest.filters.getOrElse(Nil).map(_.originHotelId)

    val recommendedPropertyRequest = buildPropertyRequest(
      originalRequest,
      (originIds ++ idsToShow).distinct
    )

    val recommendedRecommendationResponseF = getRecommendationResponse(recommendedPropertyRequest, matchScore)
    for {
      recommendedRecommendationResponse <- recommendedRecommendationResponseF
      suggestedPrices                   <- suggestedPricesF
    } yield {
      val recs    = recommendedRecommendationResponse.flatten.filter(_.propertyId.exists(idsToShow.toSet))
      val origins = recommendedRecommendationResponse.flatten.filter(_.propertyId.exists(originIds.toSet))

      val ors = for {
        o  <- origins
        id <- o.propertyId
      } yield id -> new OriginData(o, suggestedPrices.getOrElse(id, _.exclusive))

      filterRecommendation(recs, recommendationRequest, ors.toMap)
    }
  }

  protected def getRecommendationResponse(
      propertyRequest: PropertyRequest,
      matchScores: Map[HotelIdType, MatchScore]
  )(implicit
      ec: ExecutionContext,
      languageRepository: LanguageRepository,
      cmsService: PAPICMSService,
      contextHolder: ContextHolder
  ): Future[List[Option[RecommendationResponse]]] = {
    implicit val req: PropertyRequest = propertyRequest
    val recommendationResult          = searchRecommendationResult(propertyRequest)

    recommendationResult.flatMap { recommendationResultList =>
      Future.sequence(
        recommendationResultList.map(recommendationResult =>
          PricingAndContentToRecommendation
            .convertPricingAndContentToRecommendationResponse(
              recommendationResult,
              recommendationResult.hotel.flatMap(h => matchScores.get(h.h.hotelId))
            )
        )
      )
    }
  }

  protected[service] def searchRecommendationResult(
      request: PropertyRequest
  )(implicit
      ec: ExecutionContext,
      contextHolder: ContextHolder
  ): Future[List[RecommendationResult]] =
    timing("recommendation", request.context.measureTags) {
      // Get DF requests for recommended properties
      val mandatory          = false
      val papirequestFutures = requestBuilderService.getPAPIRequest(request, contextHolder)
      val dfResponse = papirequestFutures.flatMap { papirequests =>
        reportingService.report(
          "recommendation-count",
          papirequests.propertyRequestList.flatMap(_.propertySearchRequest.propertyIds).size,
          request.context.measureTags ++ Map(
            "cheapest" -> papirequests.propertyRequestList.exists(_.propertySearchRequest.cheapestOnly).toString
          )
        )
        val enrichRecommendationDetail = papirequests.propertyRequestList
          .map(req => dfRecommendationService.process(req.propertySearchRequest, mandatory = mandatory))

        Future
          .sequence(enrichRecommendationDetail)
          .map(results => results.foldLeft(JMapBuilder.empty[HotelIdType, RecommendationHotel]())((m, i) => m ++= i))
      }

      val summaryResponse = summaryService.process(request, mandatory = mandatory)
      for {
        df      <- dfResponse
        summary <- summaryResponse
      } yield request.context.propertyIds.flatMap { propertyId =>
        getRecommendationResult(df, summary, propertyId)
      }(collection.breakOut)
    }

  private def getRecommendationResult(
      df: mutable.Map[HotelIdType, RecommendationHotel],
      summary: mutable.Map[Long, PropertyResponse],
      propertyId: Long
  )(implicit contextHolder: ContextHolder): Option[RecommendationResult] = {
    val responseTry = Try {
      val hotel           = df.get(propertyId)
      val summaryResponse = summary.get(propertyId)
      summaryResponse.map(s => RecommendationResult(hotel, s))
    }
    responseTry match {
      case Success(result) => result
      case Failure(e) =>
        error(Components.Recommendations, "", Some(e))
        None
    }
  }

  private[service] def filterRecommendation(
      availableRecommendations: List[RecommendationResponse],
      recommendationRequest: Recommendation,
      origins: Map[HotelIdType, OriginData]
  )(implicit
      request: PropertyRequest,
      contextHolder: ContextHolder
  ): List[RecommendationResponse] = {
    val requiredNumberOfRecommendations = recommendationRequest.numberOfRecommendations.toInt

    val sortedRecommendations = applyRecommendationFilters(
      availableRecommendations,
      recommendationRequest.filters.getOrElse(Nil),
      origins
    )

    val filteredRecommendation =
      if (contextHolder.requestContextO.flatMap(_.featureFlag).exists(_.soldOutSimilarProperties.contains(true))) {
        request.pricing.fold(sortedRecommendations) { _ =>
          val (recommendationWithRates, soldOutRecommendations) =
            sortedRecommendations.partition(_.cheapestPriceCrossedDisplay.isDefined)

          recommendationWithRates.take(requiredNumberOfRecommendations) ++ soldOutRecommendations
        }
      } else {
        request.pricing
          .fold(sortedRecommendations)(_ =>
            sortedRecommendations
              .filter(r => r.cheapestPriceCrossedDisplay.isDefined)
          )
          .take(requiredNumberOfRecommendations)
      }

    filteredRecommendation
  }

  private def applyRecommendationFilters(
      responses: List[RecommendationResponse],
      filters: List[RecommendationFilter],
      origins: Map[HotelIdType, OriginData]
  ) = filters.foldLeft(responses)(applyRecommendationFilter(origins))

  private def applyRecommendationFilter(
      origins: Map[HotelIdType, OriginData]
  )(res: List[RecommendationResponse], f: RecommendationFilter) =
    origins.get(f.originHotelId) match {
      case None => res
      case Some(origin) =>
        val flt = (x: RecommendationResponse) =>
          filtering(
            f.comparision,
            getValue(f.field, origin.recommendationResponse, origin.getPrice),
            getValue(f.field, x, origin.getPrice)
          )

        val sortedRes        = res.sortBy(flt)
        val (passed, failed) = sortedRes.span(flt(_) < 0)
        passed.map(_.copy(filteredBy = Some(f))) ++ failed
    }

  private def getValue(
      f: RecommendationFilterFieldType,
      r: RecommendationResponse,
      getPrice: DisplayPrice => Double
  ): Option[Double] = f match {
    case Price       => r.cheapestPriceCrossedDisplay.map(cpcd => getPrice(cpcd.perRoomPerNight))
    case ReviewScore => r.reviewScore
    case StarRating  => r.starRating
  }

  /** @param c
    *   less|more|equal
    * @param originValue
    *   value of the field of the original hotel
    * @param value
    *   value of the field of the filtered hotel
    * @return
    *   negative Double for @value passing the filter. The lower the answer the more satisfying `value` is, so we can
    *   use the returned value for sorting
    */
  private def filtering(
      c: RecommendationFilterComparisonType,
      originValue: Option[Double],
      value: Option[Double]
  ): Double = value
    .flatMap { v =>
      originValue
        .map { ov =>
          c match {
            case Less  => v - ov
            case Equal => (v - ov).abs - ov * 0.2 // negative for ov+-20%
            case More  => ov - v
            case _     => 0d
          }
        }
        .orElse(Some(0d))
    }
    .getOrElse(Double.MaxValue)

  private[service] def buildPropertyRequest(
      request: PropertyRequest,
      recommendedIds: List[Long]
  ): PropertyRequest = PropertyRequest(
    context = request.context.copy(propertyIds = recommendedIds),
    summaryRequest = Some(SummaryRequest()),
    pricing = request.pricing.map(_.copy(cheapestOnly = Some(true))),
    images = Some(request.images.getOrElse(ImagesRequest(None, None))),
    reviewCumulative = Some(request.reviewCumulative.getOrElse(CumulativeRequest(None))),
    reviewDemographics = Some(request.reviewDemographics.getOrElse(DemographicsRequest(None))),
    searchType = Some(SearchTypes.Recommendations),
    nonHotelAccommodation = request.nonHotelAccommodation,
    checkInOut = request.checkInOut,
    highlights = request.highlights,
    synopsis = request.synopsis,
    engagementRequest = Some(request.engagementRequest.getOrElse(EmptyRequest()))
  )

  private[service] def getNumberOfRecommendations(sfpp: PropertyRequest): Option[HotelIdType] =
    sfpp.recommendation
      .orElse(sfpp.personalizedRecommendation)
      .orElse(sfpp.pastBookingsRecommendation)
      .map(_.numberOfRecommendations)

}
