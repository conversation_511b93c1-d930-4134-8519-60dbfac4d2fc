package com.agoda.papi.search.property.graphql.resolver

import api.request.FeatureFlag
import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.commons.tracing.api.WithTracer
import com.agoda.core.search.models.request.SearchContext
import com.agoda.core.search.models.response.PollingInfoResponse
import com.agoda.papi.search.common.externaldependency.experiment.SearchExperimentManagerService
import com.agoda.papi.search.common.graphql.GraphQLContext
import com.agoda.papi.search.common.model.ReportingDataBuilder
import com.agoda.papi.search.common.util.context.{ CmsExperimentContext, GlobalContextService }
import com.agoda.papi.search.common.util.measurement.{ MeasurementKey, MeasurementTag }
import com.agoda.papi.search.internalmodel.{ PollingInfoDeferred, PropertySummarySearchDeferred }
import com.agoda.papi.search.property.service.datafetcher.PropertySummaryDataFetcher
import com.agoda.papi.search.types.PropertySummaryEnvelopeList
import com.typesafe.scalalogging.LazyLogging
import io.opentracing.Tracer
import sangria.execution.deferred.Deferred

import scala.concurrent.{ ExecutionContext, Future }

class PropertySummaryResolverLevel1(
    val tracer: Tracer,
    globalContextService: GlobalContextService,
    searchExperimentManagerService: SearchExperimentManagerService,
    propertySummaryDataFetcher: PropertySummaryDataFetcher,
    metricsReporter: MetricsReporter
) extends WithTracer with LazyLogging {

  def setSearchContextF(ctx: GraphQLContext)(implicit ec: ExecutionContext): Future[Unit] = {
    val globalContext = ctx.globalContext
    val updateSearchContext = SearchContext(
      memberId = ctx.globalContext.getMemberId.getOrElse(0),
      locale = globalContext.getLanguageLocale.getOrElse(""),
      cid = globalContext.getCid.getOrElse(-1),
      origin = globalContext.getOrigin.getOrElse(""),
      platform = globalContext.getPlatformId.getOrElse(0)
    )
    val newContext = globalContextService.updateSearchContext(updateSearchContext, ctx.globalContext, ctx.piiContext)
    val experimentContextF = searchExperimentManagerService.createExperimentContext(
      newContext,
      ctx.globalContext,
      ctx.requestContext.experiments
    )
    experimentContextF.map { experimentContext =>
      val cmsExperimentContext = CmsExperimentContext(
        experimentContext,
        ctx.globalContext.getOrigin,
        ctx.globalContext.getWhiteLabelId,
        ctx.globalContext.isDebug
      )
      val packagingContext = None
      val searchContextHolder = ctx.getSearchContext.copy(
        experimentContext = experimentContext,
        cmsExperimentContext = cmsExperimentContext,
        globalContext = ctx.globalContext,
        packagingContext = packagingContext,
        commonContextTags = ctx.operationTags
      )
      ctx.setSearchContext(searchContextHolder)
    }
  }

  def pricingPropertiesResolveLevel1(
      deferred: Vector[Deferred[Any]],
      ctx: GraphQLContext,
      queryState: Any
  )(implicit ec: ExecutionContext): PartialFunction[Deferred[Any], Future[Any]] = {

    val propertySummarySearchDeferred = deferred.collectFirst {
      case PropertySummarySearchDeferred(propertyIds, pricingRequestParametersOpt, contentSummaryRequestOpt) =>
        ctx.reportingDataBuilder.update(ReportingDataBuilder.PropertySummarySearchType)
        PropertySummarySearchDeferred(propertyIds, pricingRequestParametersOpt, contentSummaryRequestOpt)
    }
    val propertySummariesF = propertySummarySearchDeferred.map { request =>
      setSearchContextF(ctx)(ec).flatMap { _ =>
        val searchContextHolder = ctx.getSearchContext.copy(
          globalContext = ctx.globalContext,
          commonContextTags = ctx.operationTags
        )
        val result = propertySummaryDataFetcher.fetchPropertiesSummary(
          request.propertyIds,
          request.pricingRequestParameters,
          request.contentSummaryRequest,
          ctx.globalContext,
          ctx.getSearchContext.experimentContext
        )(ec, searchContextHolder)
        result.map { properties =>
          reportNotReadyPropertiesForPropertySummarySearch(
            properties,
            ctx.globalContext.getPlatformId.getOrElse(-1),
            metricsReporter,
            request.pricingRequestParameters.exists(
              _.pricing.featureFlag.contains(FeatureFlag.ReturnHotelNotReadyIfPullNotReady)
            )
          )
          properties
        }

      }
    }

    val pollingInfo = deferred
      .collectFirst { case PollingInfoDeferred(Some(pollingInfoRequest)) =>
        PollingInfoDeferred(Some(pollingInfoRequest))
      }
      .flatMap(request => PollingInfoResponse(request.pollingInfoRequest))

    val partialFunctionResult: PartialFunction[Deferred[Any], Future[Any]] = {
      case PollingInfoDeferred(_)                 => Future(pollingInfo)
      case PropertySummarySearchDeferred(_, _, _) => propertySummariesF.getOrElse(Future(List.empty))
    }
    partialFunctionResult
  }

  /** Reports property readiness metrics for Property Summary Search
    */
  private def reportNotReadyPropertiesForPropertySummarySearch(
      properties: PropertySummaryEnvelopeList,
      platformId: Int,
      metricsReporter: MetricsReporter,
      isReturnHotelNotReadyIfPullNotReady: Boolean
  ): Unit = {
    val totalSize     = properties.size
    val notReadyCount = properties.count(property => !property.pricing.exists(_.t.isReady))

    val summarySearchSpecificTags = Map(
      MeasurementTag.PlatformId                          -> platformId.toString,
      MeasurementTag.SearchType                          -> "PropertySummarySearch",
      MeasurementTag.AllPropertiesIsReady                -> (notReadyCount == 0).toString,
      MeasurementTag.AllPropertiesNotReady               -> (notReadyCount == totalSize).toString,
      MeasurementTag.TotalSize                           -> totalSize.toString,
      MeasurementTag.IsReturnHotelNotReadyIfPullNotReady -> isReturnHotelNotReadyIfPullNotReady.toString
    )

    metricsReporter.report(
      MeasurementKey.NotReadyPropertiesSummarySearch,
      notReadyCount,
      summarySearchSpecificTags
    )
  }

}
