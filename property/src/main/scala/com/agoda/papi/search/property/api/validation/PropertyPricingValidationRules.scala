package com.agoda.papi.search.property.api.validation

import com.agoda.papi.search.common.util.constant.Constants
import request.{ PropertyPricing, PropertyPricingWithFeature }

/** Created by s<PERSON><PERSON><PERSON><PERSON> on 12/21/2016 AD.
  */
object PropertyPricingValidationRules extends ValidationRule[PropertyPricingWithFeature] {
  // scalastyle:off cyclomatic.complexity

  private val MAX_ADULTS         = 72
  private val MAX_CHILDREN       = 24
  private val MAX_ROOMS          = 72
  private val MIN_LOS            = 0
  private val EXTENDED_MAX_LOS   = Constants.EXTENDED_MAX_LOS
  private val EXTENDED_MAX_ADULT = 100

  override protected def validationRules(
      propertyPricingWithFeature: PropertyPricingWithFeature
  ): Option[ValidationFailure] = {

    val extendedMaxAdult: Boolean = propertyPricingWithFeature.featureFlagRequest
      .exists(f => f.extendedNumberOfAdults.getOrElse(false))
    val isPastCheckInDateAllowed: Boolean = propertyPricingWithFeature.featureFlagRequest
      .exists(_.allowPastCheckInDate.getOrElse(false))
    val propertyPricing = propertyPricingWithFeature.pricing
    val yesterday       = propertyPricing.bookingDate.withTimeAtStartOfDay().minusDays(1)
    val twoYearAhead    = propertyPricing.bookingDate.withTimeAtStartOfDay().plusYears(2)
    val maxAdults       = if (extendedMaxAdult) EXTENDED_MAX_ADULT else MAX_ADULTS

    propertyPricing match {
      case priceRequest if priceRequest.lengthOfStay < MIN_LOS || priceRequest.lengthOfStay > EXTENDED_MAX_LOS =>
        Some(ValidationFailure(s"los should be greater than 0 and less than or equal to $EXTENDED_MAX_LOS"))
      case priceRequest if priceRequest.numberOfAdults < 0 || priceRequest.numberOfAdults > maxAdults =>
        Some(ValidationFailure(s"adults must be greater or equal to zero and less than or equal to $maxAdults"))
      case priceRequest if priceRequest.numberOfChildren < 0 || priceRequest.numberOfChildren > MAX_CHILDREN =>
        Some(ValidationFailure(s"children must be greater or equal to zero and less than or equal to $MAX_CHILDREN"))
      case priceRequest if priceRequest.childAges.exists(x => x.flatten.exists(_ < 0)) =>
        Some(ValidationFailure(s"child age must be greater or equal to zero"))
      case priceRequest if priceRequest.numberOfRooms < 0 || priceRequest.numberOfRooms > MAX_ROOMS =>
        Some(ValidationFailure(s"rooms must be greater or equal to zero and less than $MAX_ROOMS"))
      // case priceRequest
      //     if (!isPastCheckInDateAllowed && priceRequest.checkIn.isBefore(yesterday)) || priceRequest.checkIn.isAfter(
      //       twoYearAhead
      //     ) =>
      //   Some(ValidationFailure("CheckIn must be greater than yesterday and before two years ahead from now"))
      case priceRequest if !(priceRequest.currency.isEmpty || priceRequest.currency.matches("[a-zA-Z]{3}")) =>
        Some(ValidationFailure("Currency code must be english alphabets with 3 characters"))
      case priceRequest if priceRequest.partnerLoyaltyProgramId < 0 =>
        Some(ValidationFailure("partner loyalty programId must be equal or greater to 0"))
      case priceRequest if !isPaymentCurrencyValid(priceRequest) =>
        Some(ValidationFailure("If payment currency exists, it should be either display or credit card."))
      case _ => None
    }
  }

  // scalastyle:on cyclomatic.complexity

  // Payment currency suppose to be display or credit card currency
  private def isPaymentCurrencyValid(priceRequest: PropertyPricing): Boolean = {
    val isCreditcardRequestExisted = priceRequest.paymentRequest.flatMap(_.creditCardPayment).isDefined
    val paymentCurrency            = priceRequest.paymentRequest.flatMap(_.creditCardPayment.map(_.paymentCurrency))
    val creditCardCurrency         = priceRequest.paymentRequest.flatMap(_.creditCardPayment.map(_.creditCardCurrency))
    val displayCurrency            = priceRequest.currency
    if (isCreditcardRequestExisted)
      paymentCurrency.exists(c => creditCardCurrency.exists(_.equals(c)) || c.equals(displayCurrency))
    else true
  }

}
