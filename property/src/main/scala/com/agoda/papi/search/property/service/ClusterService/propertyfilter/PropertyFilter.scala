package com.agoda.papi.search.property.service.ClusterService.propertyfilter

import com.agoda.papi.search.common.util.context.ContextHolder
import constant.{ FilterGroupID, FilterID, Symbols }
import mappings.CMSMapping
import types.{ FilterGroupType, FilterTagType }

import scala.collection.compat.toOptionCompanionExtension

object PropertyFilter {

  lazy val filterOrder: Map[String, Int] = List(
    FilterID.DayUse,
    FilterID.Overnight,
    FilterID.LuxuryCollection,
    FilterID.HotelCollection,
    FilterID.BreakfastAndDinnerInclude,
    FilterID.JapaneseStyleRoom,
    FilterID.JapaneseWesternStyleRoom,
    FilterID.noCCPropertyPage,
    FilterID.BreakfastInclude,
    FilterID.FreeCancellation,
    FilterID.MoreThanOrEqual20Sqm,
    FilterID.MoreThanOrEqual40Sqm,
    FilterID.MoreThanOrEqual60Sqm,
    FilterID.PayLater,
    FilterID.PayAtCheckIn,
    FilterID.PayAtHotel,
    FilterID.PointsMax,
    FilterID.NonSmoking,
    FilterID.KitchenAvailable,
    FilterID.BalconyTerrace,
    FilterID.RecommendedForFamilies,
    FilterID.MoreThanOneBedRooms,
    FilterID.BabyCotAvailable,
    FilterID.InterConnectingRooms,
    FilterID.TwinBed,
    FilterID.DoubleBed,
    FilterID.QueenBed,
    FilterID.KingBed,
    FilterID.GardenView,
    FilterID.CityView,
    FilterID.SeaView,
    FilterID.OceanView,
    FilterID.MountainView,
    FilterID.LakeView,
    FilterID.PoolView,
    FilterGroupID.FamilyFilters,
    FilterID.RoomVoucher,
    FilterID.DinnerIncluded,
    FilterID.EarlyCheckIn,
    FilterID.LateCheckOut,
    FilterID.AirportTransfer,
    FilterID.AgodaSpecialOffers
  ).zipWithIndex.toMap

  val filterToCMSMapping: Map[FilterTagType, (Int, String)] = Map(
    FilterID.BreakfastInclude       -> (CMSMapping.BreakfastRoomFilterTile, Symbols.BreakfastIncludePropertySymbol),
    FilterID.FreeCancellation       -> (CMSMapping.FreeCancellationRoomFilterTile, Symbols.FreeCancellation),
    FilterID.MoreThanOrEqual20Sqm   -> (CMSMapping.MoreThanOrEqual20SqmFilter, Symbols.roomSize),
    FilterID.MoreThanOrEqual40Sqm   -> (CMSMapping.MoreThanOrEqual40SqmFilter, Symbols.roomSize),
    FilterID.MoreThanOrEqual60Sqm   -> (CMSMapping.MoreThanOrEqual60SqmFilter, Symbols.roomSize),
    FilterID.PayLater               -> (CMSMapping.PayLaterRoomFilterTile, Symbols.PayLater),
    FilterID.PayAtHotel             -> (CMSMapping.PayAtHotelRoomFilterTile, Symbols.PayAtHotel),
    FilterID.PayAtCheckIn           -> (CMSMapping.PayAtCheckInRoomFilterTile, Symbols.PayAtCheckIn),
    FilterID.PointsMax              -> (CMSMapping.PointsMaxRoomFilterTile, Symbols.PointsMaxPropertySymbol),
    FilterID.noCCPropertyPage       -> (CMSMapping.noCCPropertyPageFilter, Symbols.noCCPropertyPageFilter),
    FilterID.NonSmoking             -> (CMSMapping.nonSmokingFilter, Symbols.nonSmokingFilter),
    FilterID.KitchenAvailable       -> (CMSMapping.kitchenAvailableFilter, Symbols.kitchenAvailableIcon),
    FilterID.BalconyTerrace         -> (CMSMapping.balconyTerraceFilter, Symbols.balconyTerraceIcon),
    FilterID.RecommendedForFamilies -> (CMSMapping.recommendedForFamilies, Symbols.recommendedForFamiliesIcon),
    FilterID.MoreThanOneBedRooms    -> (CMSMapping.moreThanOneBedRooms, Symbols.bedroomIcon),
    FilterID.BabyCotAvailable       -> (CMSMapping.babyCotAvailable, Symbols.babyCotIcon),
    FilterID.InterConnectingRooms   -> (CMSMapping.interConnectingRooms, Symbols.bedroomIcon),
    FilterID.TwinBed                -> (CMSMapping.twinBedFilter, Symbols.twinBedIcon),
    FilterID.DoubleBed              -> (CMSMapping.doubleBedFilter, Symbols.doubleBedIcon),
    FilterID.QueenBed               -> (CMSMapping.queenBedFilter, Symbols.queenBedIcon),
    FilterID.KingBed                -> (CMSMapping.kingBedFilter, Symbols.kingBedIcon),
    FilterID.GardenView             -> (CMSMapping.gardenViewFilter, Symbols.gardenViewIcon),
    FilterID.CityView               -> (CMSMapping.cityViewFilter, Symbols.cityViewIcon),
    FilterID.SeaView                -> (CMSMapping.seaViewFilter, Symbols.seaViewIcon),
    FilterID.OceanView              -> (CMSMapping.oceanViewFilter, Symbols.oceanViewIcon),
    FilterID.MountainView           -> (CMSMapping.mountainViewFilter, Symbols.mountainViewIcon),
    FilterID.LakeView               -> (CMSMapping.lakeViewFilter, Symbols.lakeViewIcon),
    FilterID.PoolView               -> (CMSMapping.poolViewFilter, Symbols.poolViewIcon),
    FilterID.BreakfastAndDinnerInclude -> (CMSMapping.BreakfastAndDinnerIncludeTitle, Symbols.BreakfastAndDinnerInclude),
    FilterID.JapaneseStyleRoom        -> (CMSMapping.JapaneseStyleRoom, Symbols.JapaneseStyleRoomIcon),
    FilterID.JapaneseWesternStyleRoom -> (CMSMapping.JapaneseWesternStyleRoom, Symbols.JapaneseWesternStyleRoomIcon),
    FilterID.RoomVoucher              -> (CMSMapping.VoucherFilter, Symbols.VoucherIcon),
    FilterID.AgodaSpecialOffers       -> (CMSMapping.AgodaSpecialOffers, Symbols.ASOIcon),
    FilterID.HotelCollection          -> (CMSMapping.HotelCollection, Symbols.PinHeartIcon),
    FilterID.LuxuryCollection         -> (CMSMapping.LuxuryCollection, Symbols.LuxuryIcon),
    FilterID.DinnerIncluded           -> (CMSMapping.DinnerIncludedMatrix, Symbols.DinnerIcon),
    FilterID.EarlyCheckIn             -> (CMSMapping.EarlyCheckInMatrix, Symbols.CheckInIcon),
    FilterID.LateCheckOut             -> (CMSMapping.LateCheckoutMatrix, Symbols.CheckOutIcon),
    FilterID.AirportTransfer          -> (CMSMapping.AirportTransfer, Symbols.AirportTransferIcon),
    FilterID.DayUse                   -> (CMSMapping.DayUseFilter, Symbols.DayUseIcon),
    FilterID.Overnight                -> (CMSMapping.OvernightFilter, Symbols.OvernightIcon)
  )

  val filterToCMSMappingV2: Map[FilterTagType, (Int, String)] = Map(
    FilterID.BreakfastInclude       -> (CMSMapping.BreakfastRoomFilterTile, Symbols.BreakfastIncludeDroneIcon),
    FilterID.FreeCancellation       -> (CMSMapping.FreeCancellationRoomFilterTile, Symbols.FreeCancellationDroneIcon),
    FilterID.MoreThanOrEqual20Sqm   -> (CMSMapping.MoreThanOrEqual20SqmFilter, Symbols.roomSizeDroneIcon),
    FilterID.MoreThanOrEqual40Sqm   -> (CMSMapping.MoreThanOrEqual40SqmFilter, Symbols.roomSizeDroneIcon),
    FilterID.MoreThanOrEqual60Sqm   -> (CMSMapping.MoreThanOrEqual60SqmFilter, Symbols.roomSizeDroneIcon),
    FilterID.PayLater               -> (CMSMapping.PayLaterRoomFilterTile, Symbols.PayLaterDroneIcon),
    FilterID.PayAtHotel             -> (CMSMapping.PayAtHotelRoomFilterTile, Symbols.PayAtHotelDroneIcon),
    FilterID.PayAtCheckIn           -> (CMSMapping.PayAtCheckInRoomFilterTile, Symbols.PayAtCheckInDroneIcon),
    FilterID.PointsMax              -> (CMSMapping.PointsMaxRoomFilterTile, Symbols.PointsMaxDroneIcon),
    FilterID.noCCPropertyPage       -> (CMSMapping.noCCPropertyPageFilter, Symbols.noCCPropertyPageFilterDroneIcon),
    FilterID.NonSmoking             -> (CMSMapping.nonSmokingFilter, Symbols.nonSmokingFilterDroneIcon),
    FilterID.KitchenAvailable       -> (CMSMapping.kitchenAvailableFilter, Symbols.kitchenAvailableDroneIcon),
    FilterID.BalconyTerrace         -> (CMSMapping.balconyTerraceFilter, Symbols.balconyTerraceDroneIcon),
    FilterID.RecommendedForFamilies -> (CMSMapping.recommendedForFamilies, Symbols.recommendedForFamiliesDroneIcon),
    FilterID.MoreThanOneBedRooms    -> (CMSMapping.moreThanOneBedRooms, Symbols.bedroomDroneIcon),
    FilterID.BabyCotAvailable       -> (CMSMapping.babyCotAvailable, Symbols.babyCotDroneIcon),
    FilterID.InterConnectingRooms   -> (CMSMapping.interConnectingRooms, Symbols.bedroomDroneIcon),
    FilterID.TwinBed                -> (CMSMapping.twinBedFilter, Symbols.twinBedDroneIcon),
    FilterID.DoubleBed              -> (CMSMapping.doubleBedFilter, Symbols.doubleBedDroneIcon),
    FilterID.QueenBed               -> (CMSMapping.queenBedFilter, Symbols.queenBedDroneIcon),
    FilterID.KingBed                -> (CMSMapping.kingBedFilter, Symbols.kingBedDroneIcon),
    FilterID.GardenView             -> (CMSMapping.gardenViewFilter, Symbols.gardenViewDroneIcon),
    FilterID.CityView               -> (CMSMapping.cityViewFilter, Symbols.cityViewDroneIcon),
    FilterID.SeaView                -> (CMSMapping.seaViewFilter, Symbols.seaViewDroneIcon),
    FilterID.OceanView              -> (CMSMapping.oceanViewFilter, Symbols.oceanViewDroneIcon),
    FilterID.MountainView           -> (CMSMapping.mountainViewFilter, Symbols.mountainViewDroneIcon),
    FilterID.LakeView               -> (CMSMapping.lakeViewFilter, Symbols.lakeViewDroneIcon),
    FilterID.PoolView               -> (CMSMapping.poolViewFilter, Symbols.poolViewDroneIcon),
    FilterID.BreakfastAndDinnerInclude -> (CMSMapping.BreakfastAndDinnerIncludeTitle, Symbols.BreakfastAndDinnerIncludeDroneIcon),
    FilterID.JapaneseStyleRoom -> (CMSMapping.JapaneseStyleRoom, Symbols.JapaneseStyleRoomDroneIcon),
    FilterID.JapaneseWesternStyleRoom -> (CMSMapping.JapaneseWesternStyleRoom, Symbols.JapaneseWesternStyleRoomDroneIcon),
    FilterID.RoomVoucher        -> (CMSMapping.VoucherFilter, Symbols.VoucherDroneIcon),
    FilterID.AgodaSpecialOffers -> (CMSMapping.AgodaSpecialOffers, Symbols.ASODroneIcon),
    FilterID.HotelCollection    -> (CMSMapping.HotelCollection, Symbols.PinHeartDroneIcon),
    FilterID.LuxuryCollection   -> (CMSMapping.LuxuryCollection, Symbols.LuxuryDroneIcon),
    FilterID.DinnerIncluded     -> (CMSMapping.DinnerIncludedMatrix, Symbols.DinnerDroneIcon),
    FilterID.EarlyCheckIn       -> (CMSMapping.EarlyCheckInMatrix, Symbols.CheckInDroneIcon),
    FilterID.LateCheckOut       -> (CMSMapping.LateCheckoutMatrix, Symbols.CheckOutDroneIcon),
    FilterID.AirportTransfer    -> (CMSMapping.AirportTransfer, Symbols.AirportTransferDroneIcon),
    FilterID.DayUse             -> (CMSMapping.DayUseFilter, Symbols.DayUseDroneIcon),
    FilterID.Overnight          -> (CMSMapping.OvernightFilter, Symbols.OvernightDroneIcon)
  )

  private lazy val filterGroupOrder: Map[String, Int] = List(
    FilterGroupID.Meals,
    FilterGroupID.CancellationPolicies,
    FilterGroupID.PaymentOptions,
    FilterGroupID.BeddingConfiguration,
    FilterGroupID.RoomSize,
    FilterGroupID.RoomViews,
    FilterGroupID.SpecialOffers,
    FilterGroupID.RoomFeatures,
    FilterGroupID.PropertyFeatures,
    FilterGroupID.Others
  ).zipWithIndex.toMap

  val filterGroups: Map[FilterGroupType, Map[FilterTagType, Int]] = Map(
    FilterGroupID.FamilyFilters -> Seq(
      FilterID.RecommendedForFamilies,
      FilterID.InterConnectingRooms,
      FilterID.MoreThanOneBedRooms,
      FilterID.BabyCotAvailable
    ).zipWithIndex.toMap
  )

  val filterGroupToCMSMapping: Map[FilterGroupType, Int] = Map(
    FilterGroupID.Meals                -> CMSMapping.meals,
    FilterGroupID.CancellationPolicies -> CMSMapping.cancellationPolicies,
    FilterGroupID.PaymentOptions       -> CMSMapping.paymentOptions,
    FilterGroupID.BeddingConfiguration -> CMSMapping.beddingConfiguration,
    FilterGroupID.RoomSize             -> CMSMapping.roomSize,
    FilterGroupID.RoomViews            -> CMSMapping.roomViews,
    FilterGroupID.SpecialOffers        -> CMSMapping.specialOffers,
    FilterGroupID.RoomFeatures         -> CMSMapping.roomFeatures,
    FilterGroupID.PropertyFeatures     -> CMSMapping.propertyFeatures,
    FilterGroupID.Others               -> CMSMapping.others
  )

  private val filterGroupIdMapping: Map[FilterTagType, String] = Map(
    FilterID.BreakfastInclude          -> FilterGroupID.Meals,
    FilterID.FreeCancellation          -> FilterGroupID.CancellationPolicies,
    FilterID.PayAtHotel                -> FilterGroupID.PaymentOptions,
    FilterID.PayLater                  -> FilterGroupID.PaymentOptions,
    FilterID.PointsMax                 -> FilterGroupID.PropertyFeatures,
    FilterID.PayAtCheckIn              -> FilterGroupID.PaymentOptions,
    FilterID.noCCPropertyPage          -> FilterGroupID.PaymentOptions,
    FilterID.NonSmoking                -> FilterGroupID.RoomFeatures,
    FilterID.KitchenAvailable          -> FilterGroupID.RoomFeatures,
    FilterID.BalconyTerrace            -> FilterGroupID.RoomFeatures,
    FilterID.RecommendedForFamilies    -> FilterGroupID.RoomFeatures,
    FilterID.MoreThanOneBedRooms       -> FilterGroupID.RoomFeatures,
    FilterID.BabyCotAvailable          -> FilterGroupID.RoomFeatures,
    FilterID.InterConnectingRooms      -> FilterGroupID.RoomFeatures,
    FilterID.TwinBed                   -> FilterGroupID.BeddingConfiguration,
    FilterID.DoubleBed                 -> FilterGroupID.BeddingConfiguration,
    FilterID.QueenBed                  -> FilterGroupID.BeddingConfiguration,
    FilterID.KingBed                   -> FilterGroupID.BeddingConfiguration,
    FilterID.GardenView                -> FilterGroupID.RoomViews,
    FilterID.CityView                  -> FilterGroupID.RoomViews,
    FilterID.SeaView                   -> FilterGroupID.RoomViews,
    FilterID.OceanView                 -> FilterGroupID.RoomViews,
    FilterID.MountainView              -> FilterGroupID.RoomViews,
    FilterID.LakeView                  -> FilterGroupID.RoomViews,
    FilterID.PoolView                  -> FilterGroupID.RoomViews,
    FilterID.BreakfastAndDinnerInclude -> FilterGroupID.Meals,
    FilterID.JapaneseStyleRoom         -> FilterGroupID.RoomFeatures,
    FilterID.JapaneseWesternStyleRoom  -> FilterGroupID.RoomFeatures,
    FilterID.RoomVoucher               -> FilterGroupID.SpecialOffers,
    FilterID.AgodaSpecialOffers        -> FilterGroupID.SpecialOffers,
    FilterID.HotelCollection           -> FilterGroupID.PropertyFeatures,
    FilterID.LuxuryCollection          -> FilterGroupID.PropertyFeatures,
    FilterID.MoreThanOrEqual20Sqm      -> FilterGroupID.RoomSize,
    FilterID.MoreThanOrEqual40Sqm      -> FilterGroupID.RoomSize,
    FilterID.MoreThanOrEqual60Sqm      -> FilterGroupID.RoomSize,
    FilterID.DinnerIncluded            -> FilterGroupID.Meals,
    FilterID.EarlyCheckIn              -> FilterGroupID.Others,
    FilterID.LateCheckOut              -> FilterGroupID.Others,
    FilterID.AirportTransfer           -> FilterGroupID.Others,
    FilterID.DayUse                    -> FilterGroupID.Others,
    FilterID.Overnight                 -> FilterGroupID.Others
  )

  def getFilterGroupId(filter: FilterTagType): String =
    filterGroupIdMapping.getOrElse(filter, FilterGroupID.Others)

  def getFilterGroupOrder(group: FilterGroupType): Int = filterGroupOrder.getOrElse(group, Int.MaxValue)
}
