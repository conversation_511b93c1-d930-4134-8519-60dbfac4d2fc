package com.agoda.papi.search.property.service

import enumerations.RecommendationTypes
import request.{ PropertyRecommendationRequest, PropertyRequest }
import transformers.{ PropertiesRecommendationResponse, RecommendationResponse }
import cats.implicits._
import com.agoda.ml.recommendation.zenith.common.RecommendationType.{
  PastBookingsRecommendation,
  RecentlyViewRecommendation
}
import com.agoda.papi.search.common.externaldependency.repository.LanguageRepository
import com.agoda.papi.search.common.util.context.ContextHolder

import scala.concurrent.{ ExecutionContext, Future }

trait PropertyRecommendationService {

  def searchRecommendation(
      request: PropertyRecommendationRequest,
      contextHolder: ContextHolder
  ): Future[PropertiesRecommendationResponse]

}

trait PropertyRecommendationServiceImpl
    extends PropertyRecommendationService with RecommendationComposerService with RecommendationEnricherService {

  implicit val ec: ExecutionContext
  implicit val cmsService: PAPICMSService
  implicit val languageRepository: LanguageRepository
  implicit val bookingHistoryService: BookingHistoryService
  val cityObjectInfoService: CityObjectInfoService

  override def searchRecommendation(
      request: PropertyRecommendationRequest,
      contextHolder: ContextHolder
  ): Future[PropertiesRecommendationResponse] = {

    implicit val ctx: ContextHolder    = contextHolder
    implicit val sfpp: PropertyRequest = convertToPropertyRequest(request)

    val recommendationF: Future[Map[Long, List[RecommendationResponse]]] =
      request.recommendation.recommendationType match {
        case Some(RecommendationTypes.Comparison) => composeComparisonRecommendationList(sfpp)
        case Some(RecommendationTypes.RecentlyViewed) =>
          composeZenithRecommendationListFromCityId(
            sfpp,
            request.cityId.get,
            request.recommendation,
            RecentlyViewRecommendation
          )
        case Some(RecommendationTypes.PastBookings) =>
          composeZenithRecommendationListFromCityId(
            sfpp,
            request.cityId.get,
            request.recommendation,
            PastBookingsRecommendation
          )
        case Some(RecommendationTypes.UpTier) =>
          composeUpTierRecommendationList(sfpp)
        case _ => Future.successful(Map.empty)
      }
    val objectInfoF = request.cityId.traverse(cityId =>
      cityObjectInfoService.getCityObjectInfo(cityId = cityId, request.context.locale)(ctx, ec)
    )
    for {
      recommendation <- recommendationF
      enriched       <- doEnrichedRecommendation(recommendation)
      objectInfo     <- objectInfoF
    } yield {

      val recommendedList = request.recommendation.recommendationType match {
        case Some(RecommendationTypes.Comparison) => enriched.getOrElse(request.context.propertyIds.head, List.empty)
        case Some(RecommendationTypes.UpTier) =>
          request.context.propertyIds.flatMap(propertyId => enriched.getOrElse(propertyId, List.empty))
        case _ => enriched.getOrElse(request.cityId.head, List.empty)
      }
      PropertiesRecommendationResponse(recommendedList, objectInfo)
    }
  }

  private[service] def convertToPropertyRequest(
      request: PropertyRecommendationRequest
  ): PropertyRequest = {

    val (recommendation, personalizedRecommendation, pastBookingsRecommendation) =
      request.recommendation.recommendationType match {
        case Some(RecommendationTypes.Comparison)     => (Some(request.recommendation), None, None)
        case Some(RecommendationTypes.RecentlyViewed) => (None, Some(request.recommendation), None)
        case Some(RecommendationTypes.PastBookings)   => (None, None, Some(request.recommendation))
        case _                                        => (None, None, None)
      }

    PropertyRequest(
      context = request.context,
      pricing = request.pricing,
      recommendation = recommendation,
      personalizedRecommendation = personalizedRecommendation,
      pastBookingsRecommendation = pastBookingsRecommendation
    )
  }

}
