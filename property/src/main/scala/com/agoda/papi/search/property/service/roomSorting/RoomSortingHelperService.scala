package com.agoda.papi.search.property.service.roomSorting

import com.agoda.commons.logging.metrics.MetricsReporter
import com.agoda.core.search.models.CancellationTypes
import com.agoda.papi.enums.room.{ ExternalLoyaltyItem, ExternalLoyaltyItemType }
import com.agoda.papi.search.common.util.ContextProvider.getDefaultContext
import com.agoda.papi.search.common.util.context.{ ContextHolder, ExecutionContexts }
import com.agoda.papi.search.common.util.measurement.{
  APIFlowMetric,
  ContextScheduledMetrics,
  MeasurementKey,
  PageStep
}
import com.agoda.platform.service.context.GlobalContext
import com.agoda.supplier.common.services.SupplierMetadataService
import com.typesafe.scalalogging.LazyLogging
import constant.StayPackageType
import io.opentracing.Tracer
import models.starfruit.SuggestedPrice
import repository.ProviderIds
import transformers.{ EnrichedChildRoom, EnrichedMasterRoom }

import scala.concurrent.ExecutionContext

trait RoomSortingHelperService {

  def sortRooms(
      masterRooms: List[EnrichedMasterRoom],
      suggestPrice: Option[SuggestedPrice],
      currency: Option[String]
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): List[EnrichedMasterRoom]

}

class RoomSortingHelperServiceImpl(
    val supplierMetadataService: SupplierMetadataService,
    val reportingService: MetricsReporter,
    val tracer: Tracer,
    implicit val contextHolder: ContextHolder = getDefaultContext(GlobalContext(Seq.empty)),
    implicit val ec: ExecutionContext = ExecutionContexts.defaultContext
) extends RoomSortingHelperService with APIFlowMetric with ContextScheduledMetrics with LazyLogging {

  private val DEFAULT_EXPAND_OFFER: Int = 4
  private val MIN_EXPANDED_OFFERS: Int  = 2
  private val TOP_PRIORITY: Double      = 1.0
  private val NONE: Double              = 0

  import RoomSortingManager._

  /** @param masterRooms
    * @param suggestPrice
    * @return
    *   List of masterRooms
    */
  override def sortRooms(
      masterRooms: List[EnrichedMasterRoom],
      suggestPrice: Option[SuggestedPrice],
      currency: Option[String]
  )(implicit ec: ExecutionContext, contextHolder: ContextHolder): List[EnrichedMasterRoom] = stepTimer(
    MeasurementKey.RoomSorting,
    PageStep.Sorting
  ) {
    try
      sortMasterRooms(masterRooms, suggestPrice, currency)
    catch {
      case e: Exception =>
        logger.error("Error during room sorting", e)
        masterRooms
    }
  }

  private def sortMasterRooms(
      masterRooms: List[EnrichedMasterRoom],
      suggestPrice: Option[SuggestedPrice],
      currency: Option[String]
  )(implicit contextHolder: ContextHolder): List[EnrichedMasterRoom] =
    MasterRoomsManager(
      masterRooms.map(sortChildRooms(_, suggestPrice, currency)),
      suggestPrice,
      currency
    ).sortMasterRooms

  private def sortChildRooms(
      masterRoom: EnrichedMasterRoom,
      suggestPrice: Option[SuggestedPrice],
      currency: Option[String]
  )(implicit contextHolder: ContextHolder): EnrichedMasterRoom = {
    val childRoomsManager = ChildrenRoomsManager(masterRoom.childrenRooms, suggestPrice, currency)
    masterRoom.copy(childrenRooms = childRoomsManager.sortChildrenRooms)
  }

  private object RoomSortingManager {

    case class MasterRoomsManager(
        masterRooms: List[EnrichedMasterRoom],
        suggestPrice: Option[SuggestedPrice],
        currency: Option[String]
    ) {

      def sortMasterRooms: List[EnrichedMasterRoom] = {
        val (collectionRooms, nonCollectionRooms)            = groupMasterRoomsWithCollection(masterRooms)
        val (fitNonCollectionRooms, unfitNonCollectionRooms) = groupMasterRoomsWithFitCapacity(nonCollectionRooms)
        sortCollectionRooms(collectionRooms) ++
          sortNonCollectionRooms(fitNonCollectionRooms) ++
          sortNonCollectionRooms(unfitNonCollectionRooms)
      }

      /** @return
        *   List of collection and non-collection rooms
        */
      private def groupMasterRoomsWithCollection(
          masterRooms: List[EnrichedMasterRoom]
      ): (List[EnrichedMasterRoom], List[EnrichedMasterRoom]) =
        masterRooms.partition(_.childrenRooms.exists(ChildRoomManager.isCollectionRoom))

      /** @return
        *   List of fit and unfit rooms
        */
      private def groupMasterRoomsWithFitCapacity(
          masterRooms: List[EnrichedMasterRoom]
      ): (List[EnrichedMasterRoom], List[EnrichedMasterRoom]) =
        masterRooms.partition(MasterRoomManager.hasFitOffer)

      /** @return
        *   Sorted list of rooms with collectionOrdering
        */
      private def sortCollectionRooms(masterRooms: List[EnrichedMasterRoom]): List[EnrichedMasterRoom] =
        masterRooms.sorted(collectionOrdering)

      /** @return
        *   Sorted list of rooms with nonCollectionOrdering
        */
      private def sortNonCollectionRooms(masterRooms: List[EnrichedMasterRoom]): List[EnrichedMasterRoom] =
        masterRooms.sorted(nonCollectionOrdering)

      /** Tie-breaker 1) Price 2) isFit 3) Agoda direct */
      private def collectionOrdering =
        Ordering.by[EnrichedMasterRoom, (Double, Boolean, Boolean)](room =>
          (
            MasterRoomManager.getChildRoomPrice(room, suggestPrice, currency),
            !MasterRoomManager.hasFitOffer(room),
            !MasterRoomManager.hasAgodaDirect(room)
          )
        )

      /** Tie-breaker 1) Price 2) Agoda direct */
      private def nonCollectionOrdering =
        Ordering.by[EnrichedMasterRoom, (Double, Boolean)](room =>
          (
            MasterRoomManager.getChildRoomPrice(room, suggestPrice, currency),
            !MasterRoomManager.hasAgodaDirect(room)
          )
        )

    }

    case class ChildrenRoomsManager(
        rooms: List[EnrichedChildRoom],
        suggestPrice: Option[SuggestedPrice],
        currency: Option[String]
    ) {

      def sortChildrenRooms()(implicit contextHolder: ContextHolder): List[EnrichedChildRoom] = {

        /** Group with fit capacity */
        val (fitRooms, unfitRooms) = groupRoomsWithFitCapacity

        /** Group with collection */
        val (fitCollectionRooms, fitNonCollectionRooms)     = groupRoomsWithCollection(fitRooms)
        val (unfitCollectionRooms, unfitNonCollectionRooms) = groupRoomsWithCollection(unfitRooms)

        val result = sortCollectionRooms(fitCollectionRooms) ++
          sortFitNonCollectionRooms(fitNonCollectionRooms) ++
          sortCollectionRooms(unfitCollectionRooms) ++
          sortNonCollectionRooms(unfitNonCollectionRooms)

        setOfferPriority(result)
      }

      /** @return
        *   List of fit and unfit rooms
        */
      private def groupRoomsWithFitCapacity: (List[EnrichedChildRoom], List[EnrichedChildRoom]) =
        rooms.partition(ChildRoomManager.isFitCapacity)

      /** @return
        *   List of rooms with collection and non collection
        */
      private def groupRoomsWithCollection(
          rooms: List[EnrichedChildRoom]
      ): (List[EnrichedChildRoom], List[EnrichedChildRoom]) = rooms.partition(ChildRoomManager.isCollectionRoom)

      /** @param suggestPrice
        * @return
        *   Sorted list of rooms with collectionComparator
        */
      private def sortCollectionRooms(rooms: List[EnrichedChildRoom]): List[EnrichedChildRoom] =
        rooms.sorted(collectionOrdering)

      /** @param suggestPrice
        * @return
        *   Sorted list of rooms with nonCollectionComparator
        */
      private def sortNonCollectionRooms(rooms: List[EnrichedChildRoom]): List[EnrichedChildRoom] =
        rooms.sorted(nonCollectionOrdering)

      private def sortFitNonCollectionRooms(
          rooms: List[EnrichedChildRoom]
      )(implicit contextHolder: ContextHolder): List[EnrichedChildRoom] = {
        val sortedRooms = sortNonCollectionRooms(rooms)

        sortWithMeaningfulTopRooms(sortedRooms)
      }

      /** @param sortedRooms
        *   Fit Non Collection Rooms sorted
        * @return
        *   Sorted List of rooms with meaningful top offers
        */
      private def sortWithMeaningfulTopRooms(sortedRooms: List[EnrichedChildRoom]): List[EnrichedChildRoom] =
        /** Early return for less than 3 rooms */
        if (sortedRooms.length < 3) {
          sortedRooms
        } else {

          val (cheapestOffer, remainingAfterCheapest) = (sortedRooms.headOption, sortedRooms.tail)
          val (cheapestWithBreakfast, remainingAfterBreakfast) =
            extractFirstMatchingRoom(remainingAfterCheapest, ChildRoomManager.isIncludeBreakfast)
          val (cheapestWithFreeCancellation, remainingAfterCancellation) =
            extractFirstMatchingRoom(remainingAfterBreakfast, ChildRoomManager.isFreeCancellation)
          val (cheapestWithBreakfastAndFreeCancellation, remainingAfterBreakfastAndFreeCxl) =
            extractFirstMatchingRoom(
              remainingAfterCancellation,
              room => ChildRoomManager.isIncludeBreakfast(room) && ChildRoomManager.isFreeCancellation(room)
            )
          val (cheapestWithPayAtHotel, remainingAfterPayAtHotel) =
            extractFirstMatchingRoom(remainingAfterBreakfastAndFreeCxl, ChildRoomManager.isPayAtHotel)
          val (cheapestASOOffer, remainingOffers) =
            extractFirstMatchingRoom(remainingAfterPayAtHotel, ChildRoomManager.isAgodaSpecialOffer)

          val meaningfulOffers = List(
            cheapestOffer,
            cheapestWithBreakfast,
            cheapestWithFreeCancellation,
            cheapestWithBreakfastAndFreeCancellation,
            cheapestWithPayAtHotel,
            cheapestASOOffer
          ).flatten
            .sortBy(room => ChildRoomManager.getPrice(room, suggestPrice, currency))
            .map(room => room.copy(priority = Some(TOP_PRIORITY)))
          val collapsedOffers = remainingOffers.map(room => room.copy(priority = Some(NONE)))

          meaningfulOffers ++ collapsedOffers
        }

      // Do not remove: use this for negative experiment for Desktop/MSPA
      private def sortWithMeaningfulTop4(sortedRooms: List[EnrichedChildRoom]): List[EnrichedChildRoom] =
        /** Early return less than 3 rooms */
        if (sortedRooms.length < 3) {
          sortedRooms
        } else {

          /** Extract the top four meaningful offers */
          val (cheapestFit, remainingAfterCheapest) = (sortedRooms.headOption, sortedRooms.tail)
          val (cheapestWithBreakfast, remainingAfterBreakfast) =
            extractFirstMatchingRoom(remainingAfterCheapest, ChildRoomManager.isIncludeBreakfast)
          val (cheapestWithFreeCancellation, remainingAfterCancellation) =
            extractFirstMatchingRoom(remainingAfterBreakfast, ChildRoomManager.isFreeCancellation)
          val (cheapestWithBreakfastAndFreeCancellation, remainingRooms) = extractFirstMatchingRoom(
            remainingAfterCancellation,
            room => ChildRoomManager.isIncludeBreakfast(room) && ChildRoomManager.isFreeCancellation(room)
          )

          /** Combine meaningful offers and the remaining sorted rooms */
          val meaningfulOffers = List(
            cheapestFit,
            cheapestWithBreakfast,
            cheapestWithFreeCancellation,
            cheapestWithBreakfastAndFreeCancellation
          ).flatten
            .sortBy(room => ChildRoomManager.getPrice(room, suggestPrice, currency))

          meaningfulOffers ++ remainingRooms
        }

      private def setOfferPriority(
          sortedOffers: List[EnrichedChildRoom]
      )(implicit contextHolder: ContextHolder): List[EnrichedChildRoom] = {
        val hasMeaningfulOffers = sortedOffers.headOption.exists(_.priority.contains(TOP_PRIORITY))

        if (hasMeaningfulOffers) {
          if (contextHolder.experimentContext.isBVariant(contextHolder.allocationContext.offerRankingV4)) {
            fillMinimumOffers(sortedOffers)
          } else {
            sortedOffers
          }
        } else {
          val expandedOffers = sortedOffers
            .take(DEFAULT_EXPAND_OFFER)
            .map(item => item.copy(priority = Some(TOP_PRIORITY)))
          val collapsedOffers = sortedOffers
            .drop(DEFAULT_EXPAND_OFFER)
            .map(item => item.copy(priority = Some(NONE)))

          expandedOffers ++ collapsedOffers
        }
      }

      private def fillMinimumOffers(sortedOffers: List[EnrichedChildRoom]): List[EnrichedChildRoom] = {
        val expandedCount = sortedOffers.count(_.priority.contains(TOP_PRIORITY))

        if (expandedCount >= MIN_EXPANDED_OFFERS) {
          sortedOffers
        } else {
          val toFill = MIN_EXPANDED_OFFERS - expandedCount
          val additionalOffers = sortedOffers.zipWithIndex
            .collect { case (room, idx) if !room.priority.contains(TOP_PRIORITY) => idx }
            .take(toFill)
            .toSet

          sortedOffers.zipWithIndex.map { case (room, idx) =>
            if (additionalOffers.contains(idx)) {
              room.copy(priority = Some(TOP_PRIORITY))
            } else {
              room
            }
          }
        }
      }

      /** Extracts the first room that matches a given predicate.
        *
        * @param rooms
        *   A list of enriched child rooms.
        * @param predicate
        *   A function that defines the matching criteria.
        * @return
        *   A tuple containing an optional matching room and the list of remaining rooms.
        */
      private def extractFirstMatchingRoom(
          rooms: List[EnrichedChildRoom],
          predicate: EnrichedChildRoom => Boolean
      ): (Option[EnrichedChildRoom], List[EnrichedChildRoom]) =
        rooms.indexWhere(predicate) match {
          case idx if idx >= 0 => (Some(rooms(idx)), rooms.take(idx) ++ rooms.drop(idx + 1))
          case _               => (None, rooms)
        }

      /** Tie-breaker 1) Price 2) Breakfast included 3) Free cancellation 4) Agoda direct
        */
      private def nonCollectionOrdering() =
        Ordering.by[EnrichedChildRoom, (Double, Boolean, Boolean, Boolean)](room =>
          (
            ChildRoomManager.getPrice(room, suggestPrice, currency),
            !ChildRoomManager.isIncludeBreakfast(room),
            !ChildRoomManager.isFreeCancellation(room),
            !ChildRoomManager.isAgodaDirectSupply(room)
          )
        )

      /** Tie-breaker 1) Price 2) Tier1
        */
      private def collectionOrdering() =
        Ordering.by[EnrichedChildRoom, (Double, Boolean)](room =>
          (ChildRoomManager.getPrice(room, suggestPrice, currency), !ChildRoomManager.isLuxuryCollectionRoom(room))
        )

    }

    private object ChildRoomManager {

      def getPrice(room: EnrichedChildRoom, suggestPrice: Option[SuggestedPrice], currency: Option[String]): Double =
        room.pricing.headOption
          .map { pricing =>
            val roomPrice          = room.pricing.getOrElse(currency.getOrElse(pricing._1), pricing._2)
            val priceAfterCashback = roomPrice.displaySummary.perRoomPerNight.displayAfterCashback
            val displayPrice       = roomPrice.display.perRoomPerNight

            priceAfterCashback match {
              case Some(cashback) if room.cashback.exists(_.showPostCashbackPrice) =>
                if (suggestPrice.contains(SuggestedPrice.AllInclusive)) cashback.allInclusive else cashback.exclusive
              case _ =>
                if (suggestPrice.contains(SuggestedPrice.AllInclusive)) displayPrice.allInclusive
                else displayPrice.exclusive
            }
          }
          .getOrElse(Double.MaxValue)

      def isFitCapacity(room: EnrichedChildRoom): Boolean =
        room.capacity.exists(value => value.numberOfGuestsWithoutRoom <= 0)

      def isFreeCancellation(room: EnrichedChildRoom): Boolean = room.payment match {
        case Some(payment) => payment.cancellation.cancellationType == CancellationTypes.FreeCancellation
        case _             => false
      }

      def isIncludeBreakfast(room: EnrichedChildRoom): Boolean = room.isBreakfastIncluded.getOrElse(false)

      def isAgodaDirectSupply(room: EnrichedChildRoom): Boolean = supplierMetadataService
        .isAgodaBrand(room.subSupplierId.getOrElse(ProviderIds.Unknown))

      def isPayAtHotel(room: EnrichedChildRoom): Boolean = room.payment match {
        case Some(payment) => payment.payAtHotel.isEligible
        case None          => false
      }

      def isAgodaSpecialOffer(room: EnrichedChildRoom): Boolean =
        room.stayPackageType.contains(StayPackageType.agodaSpecialOffer)

      private def getLoyaltyItemType(room: EnrichedChildRoom): ExternalLoyaltyItemType =
        room.externalLoyaltyDisplay.flatMap(_.items.headOption).map(_.tierId).getOrElse(ExternalLoyaltyItem.Unknown)

      def isCollectionRoom(room: EnrichedChildRoom): Boolean = getLoyaltyItemType(room) != ExternalLoyaltyItem.Unknown

      def isLuxuryCollectionRoom(room: EnrichedChildRoom): Boolean =
        getLoyaltyItemType(room) == ExternalLoyaltyItem.TierOne
    }

    private object MasterRoomManager {

      def getChildRoomPrice(
          masterRoom: EnrichedMasterRoom,
          suggestedPrice: Option[SuggestedPrice],
          currency: Option[String]
      ): Double =
        masterRoom.childrenRooms.headOption match {
          case Some(room) => ChildRoomManager.getPrice(room, suggestedPrice, currency)
          case _          => Double.MaxValue
        }

      def hasFitOffer(masterRooms: EnrichedMasterRoom): Boolean =
        masterRooms.childrenRooms.headOption.exists(ChildRoomManager.isFitCapacity)

      def hasAgodaDirect(masterRoom: EnrichedMasterRoom): Boolean =
        masterRoom.childrenRooms.headOption.exists(ChildRoomManager.isAgodaDirectSupply)
    }

  }

}
