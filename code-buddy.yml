# repo-standards: Links to the repository's standards. These links can point to files within the current repository or external sources.
# supported sources: gitlab.md file, confluence e.g., https://gitlab.agodadev.io/devops/developer-experience-aiops/aiops/code-buddy/-/blob/agoda-main/ag-docs/index.md
# Example:
#
# repo-standards:
#   - "https://gitlab.agodadev.io/devops/developer-experience-aiops/aiops/code-buddy/-/blob/agoda-main/ag-docs/index.md"
#   - "https://agoda.atlassian.net/wiki/spaces/DEV/pages/766347705/CodeBuddy+development+standards"
#
repo-standards:
  - "https://gitlab.agodadev.io/IT-Platform/propertyapi/-/blob/develop/CONTRIBUTING.md"

# customized-prompt: Add a custom prompt here. This will be appended to the main prompt and will not replace it.
# Example:
# customized-prompt: "Focus only on identifying critical logic bugs in the code. Avoid commenting on formatting or style issues."
#
# Example for multi-lines customized-prompt:
#
#  customized-prompt: |
#  Emphasize the following aspects:
#  - Focus only on identifying critical logic bugs
#  - Avoid commenting on formatting
customized-prompt: |
  Emphasize the following aspects:
  - SfRequest.scala and Result.scala included Properties, Property, EnrichedCheapestRoom ETC. model must always maintain backward compatibility for JSON serialization and deserialization.
  - No white-label-specific logic (e.g., if (whiteLabelId == WhiteLabel.X) doA else doB) should be introduced in the MR.
  - No DMC ID specific logic (e.g., if (request.dmcId == DmcId.BCOM.id) doA else doB) should be introduced in the MR.
  - No Platform ID specific logic (e.g., if (request.platformId == 1007) doA else doB) should be introduced in the MR.
  - MR that change ActiveABTests.scala must add a functional test in the functional-test module



