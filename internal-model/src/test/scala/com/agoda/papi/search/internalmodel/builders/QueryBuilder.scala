package com.agoda.papi.search.internalmodel.builders

import com.agoda.common.graphql.model.Query
import com.agoda.commons.serialization.v1.instances.circe.circeSerializer
import com.agoda.content.models.request.sf._
import com.agoda.papi.search.internalmodel.builders.request._
import com.agoda.papi.search.internalmodel.request.encoder.RequestEncodersForWarmUp
import com.agoda.papi.search.internalmodel.request._
import io.circe.derivation.deriveEncoder
import io.circe.parser.parse
import io.circe.{ Encoder, J<PERSON>, Printer }
import models.starfruit.{ PricingPropertiesRequest, PricingRequestParameters }
import com.agoda.content.models.circe.Serialization._
import com.agoda.core.search.models.request.{
  AreaSearchRequest,
  BaseRequest,
  BoxSearchRequest,
  CityCenterSearchRequest,
  CitySearchRequest,
  ComplexSearchRequest,
  LandmarkSearchRequest,
  PollingInfoRequest,
  RadiusSearchRequest,
  RegionSearchRequest,
  StateSearchRequest,
  TopAreaSearchRequest
}
import com.agoda.pricing.models.circe.Encoders.{ PricingPropertiesSummaryRequestEncoder, PricingSummaryRequestEncoder }
import io.circe.syntax.EncoderOps
import com.agoda.papi.search.internalmodel.request.encoder.RequestEncodersForWarmUp.{
  growthProgramInfoRequestEncoder,
  pollingInfoRequestEncoder,
  propertyAllotmentSearchRequestEncoder,
  propertyDetailsRequestEncoder,
  propertySummarySearchRequestEncoder
}
import com.agoda.papi.search.internalmodel.builders.request._
import com.agoda.papi.search.internalmodel.growthprogram.GrowthProgramInfoRequest
import com.agoda.papi.search.internalmodel.serializer.encoder.CustomEncoders._

/** <AUTHOR>   mkhan
  */

object QueryBuilder {

  implicit val PropertyDetailsRequestItTestType: Encoder[PropertyDetailsRequestItTest] =
    deriveEncoder[PropertyDetailsRequestItTest]
  implicit val PriceStreamMetaLabRequestType: Encoder[PriceStreamMetaLabRequest] =
    deriveEncoder[PriceStreamMetaLabRequest]
  implicit val BannerCampaignRequestItTestType: Encoder[BannerCampaignRequestItTest] =
    deriveEncoder[BannerCampaignRequestItTest]

  def build(
      query: String,
      complexSearchRequest: BaseRequest[ComplexSearchRequest],
      pricingRequestParameters: PricingRequestParameters,
      contentSummaryRequest: ContentSummaryRequest,
      priceStreamMetaLabRequest: PriceStreamMetaLabRequest = PriceStreamMetaLabRequest(),
      bannerCampaignRequest: BannerCampaignRequestItTest = BannerCampaignRequestItTest(),
      contentExtraRoomSummaryRequest: ContentExtraRoomSummaryRequest = ContentExtraRoomSummaryRequest(Some(false))
  ): Query =
    build(
      query,
      build(
        complexSearchRequest,
        pricingRequestParameters,
        contentSummaryRequest,
        priceStreamMetaLabRequest,
        bannerCampaignRequest,
        contentExtraRoomSummaryRequest
      )
    )

  def build(query: String, variables: Json): Query = Query(query, None, variables)

  def build(
      complexSearchRequest: BaseRequest[ComplexSearchRequest],
      pricingRequestParameters: PricingRequestParameters,
      contentSummaryRequest: ContentSummaryRequest,
      priceStreamMetaLabRequest: PriceStreamMetaLabRequest,
      bannerCampaignRequest: BannerCampaignRequestItTest,
      contentExtraRoomSummaryRequest: ContentExtraRoomSummaryRequest
  ): Json = {

    import com.agoda.pricing.models.circe.Encoders._

    val jsonContentRequest = dropNullValues(
      parse(circeSerializer[ContentSummaryRequest].serialize(contentSummaryRequest)).right.get
    )
    val jsonDFRequest = dropNullValues(
      parse(circeSerializer[PricingRequestParameters].serialize(pricingRequestParameters)).right.get
    )
    val jsonPriceStreamMetaLabRequest = dropNullValues(
      parse(circeSerializer[PriceStreamMetaLabRequest].serialize(priceStreamMetaLabRequest)).right.get
    )
    val jsonBannerCampaignRequest = dropNullValues(
      parse(circeSerializer[BannerCampaignRequestItTest].serialize(bannerCampaignRequest)).right.get
    )
    val jsonContentExtraRoomSummaryRequest = dropNullValues(
      parse(circeSerializer[ContentExtraRoomSummaryRequest].serialize(contentExtraRoomSummaryRequest)).right.get
    )
    val jsonCoreSearch = complexSearchRequest match {
      case request: CitySearchRequest =>
        toCoreSearchJson(request, CitySearchParametersBuilder)(RequestEncodersForWarmUp.citySearchRequestEncoder)
      case request: AreaSearchRequest =>
        toCoreSearchJson(request, AreaSearchParametersBuilder)(RequestEncodersForWarmUp.areaSearchRequestEncoder)
      case request: RegionSearchRequest =>
        toCoreSearchJson(request, RegionSearchParametersBuilder)(RequestEncodersForWarmUp.regionSearchRequestEncoder)
      case request: TopAreaSearchRequest =>
        toCoreSearchJson(request, TopAreaSearchParametersBuilder)(RequestEncodersForWarmUp.topAreaSearchRequestEncoder)
      case request: BoxSearchRequest =>
        toCoreSearchJson(request, BoxSearchParametersBuilder)(RequestEncodersForWarmUp.boxSearchRequestEncoder)
      case request: RadiusSearchRequest =>
        toCoreSearchJson(request, RadiusSearchParametersBuilder)(RequestEncodersForWarmUp.radiusSearchRequestEncoder)
      case request: LandmarkSearchRequest =>
        toCoreSearchJson(request, LandmarkSearchParametersBuilder)(
          RequestEncodersForWarmUp.landmarkSearchRequestEncoder
        )
      case request: CityCenterSearchRequest =>
        toCoreSearchJson(request, CityCenterSearchParametersBuilder)(
          RequestEncodersForWarmUp.cityCenterSearchRequestEncoder
        )
      case request: StateSearchRequest =>
        toCoreSearchJson(request, StateSearchParametersBuilder)(RequestEncodersForWarmUp.stateSearchRequestEncoder)
    }

    val jsonMap =
      jsonCoreSearch + ("ContentSummaryRequest" -> jsonContentRequest) + ("PricingSummaryRequest" -> jsonDFRequest) +
        ("PriceStreamMetaLabRequest" -> jsonPriceStreamMetaLabRequest) + ("BannerCampaignRequest" -> jsonBannerCampaignRequest) +
        ("ContentExtraRoomSummaryRequest" -> jsonContentExtraRoomSummaryRequest)

    // Need to perform additonal steps due to null values.
    val result = parse(Printer.noSpaces.copy(dropNullValues = true).print(jsonMap.asJson)).right.get
    result
  }

  def build(query: String, pricingPropertiesRequest: PricingPropertiesRequest): Query = {
    val jsonMap = Map(
      "PricingPropertiesSummaryRequest" -> dropNullValues(
        parse(circeSerializer[PricingPropertiesRequest].serialize(pricingPropertiesRequest)).right.get
      )
    )
    val variables = parse(Printer.noSpaces.copy(dropNullValues = true).print(jsonMap.asJson)).right.get
    Query(query, None, variables)
  }

  def buildPropertySummarySearchQuery(
      query: String,
      summarySearchRequest: PropertySummarySearchRequest,
      pricingRequestParametersOpt: Option[PricingRequestParameters],
      contentSummaryRequestOpt: Option[ContentSummaryRequest],
      contentExtraRoomSummaryRequestOpt: Option[ContentExtraRoomSummaryRequest],
      pollingInfoRequestOpt: Option[PollingInfoRequest]
  ): Query = {
    val jsonMap = Map(
      "PropertySummarySearchRequest" -> dropNullValues(
        parse(circeSerializer[PropertySummarySearchRequest].serialize(summarySearchRequest)).right.get
      ),
      "PricingSummaryRequest" -> {
        pricingRequestParametersOpt match {
          case Some(pricingRequestParameters) =>
            dropNullValues(
              parse(circeSerializer[PricingRequestParameters].serialize(pricingRequestParameters)).right.get
            )
          case None => Json.Null
        }
      },
      "ContentSummaryRequest" -> {
        contentSummaryRequestOpt match {
          case Some(contentSummaryRequest) =>
            dropNullValues(parse(circeSerializer[ContentSummaryRequest].serialize(contentSummaryRequest)).right.get)
          case None => Json.Null
        }
      },
      "ContentExtraRoomSummaryRequest" -> {
        contentExtraRoomSummaryRequestOpt match {
          case Some(contentExtraRoomSummaryRequest) =>
            dropNullValues(
              parse(circeSerializer[ContentExtraRoomSummaryRequest].serialize(contentExtraRoomSummaryRequest)).right.get
            )
          case None => Json.Null
        }
      },
      "PollingInfoRequest" -> {
        pollingInfoRequestOpt match {
          case Some(pollingInfoRequest) =>
            dropNullValues(parse(circeSerializer[PollingInfoRequest].serialize(pollingInfoRequest)).right.get)
          case None => Json.Null
        }
      }
    )
    val variables = parse(Printer.noSpaces.copy(dropNullValues = true).print(jsonMap.asJson)).right.get
    Query(query, None, variables)
  }

  def buildPropertyAllotmentSearchQuery(
      query: String,
      propertyAllotmentSearchRequest: PropertyAllotmentSearchRequest
  ): Query = {
    val jsonMap = Map(
      "PropertyAllotmentSearchRequest" -> dropNullValues(
        parse(circeSerializer[PropertyAllotmentSearchRequest].serialize(propertyAllotmentSearchRequest)).right.get
      )
    )
    val variables = parse(Printer.noSpaces.copy(dropNullValues = true).print(jsonMap.asJson)).right.get
    Query(query, None, variables)
  }

  def buildPropertyDetailSearchQuery(
      query: String,
      propertyDetailsRequest: PropertyDetailsRequest,
      contentReviewScoreRequest: ContentReviewScoreRequest
  ): Query = {
    val jsonMap = Map(
      "PropertyDetailsRequest" -> dropNullValues(
        parse(circeSerializer[PropertyDetailsRequest].serialize(propertyDetailsRequest)).right.get
      ),
      "ContentReviewScoreRequest" -> dropNullValues(
        parse(circeSerializer[ContentReviewScoreRequest].serialize(contentReviewScoreRequest)).right.get
      )
    )
    val variables = parse(Printer.noSpaces.copy(dropNullValues = true).print(jsonMap.asJson)).right.get
    Query(query, None, variables)
  }

  def build(
      query: String,
      propertyDetailsRequest: PropertyDetailsRequestItTest,
      contentImagesRequest: ContentImagesRequest,
      contentReviewScoreRequest: ContentReviewScoreRequest,
      contentLocalInformationRequest: ContentLocalInformationRequest,
      contentExperienceRequest: ContentExperienceRequest,
      contentFeaturesRequest: ContentFeaturesRequest,
      contentHighlightsRequest: ContentHighlightsRequest,
      contentQnaRequest: ContentQnaRequest,
      contentTopicsRequest: TopicsRequest,
      priceStreamMetaLabRequest: PriceStreamMetaLabRequest,
      bannerCampaignRequest: BannerCampaignRequestItTest,
      contentRateCategoryRequest: ContentRateCategoryRequest,
      growthProgramInfoRequest: GrowthProgramInfoRequest
  ): Query = {
    val jsonPropertyDetailsRequest = dropNullValues(
      parse(circeSerializer[PropertyDetailsRequestItTest].serialize(propertyDetailsRequest)).right.get
    )
    val jsonContentImagesRequest = dropNullValues(
      parse(circeSerializer[ContentImagesRequest].serialize(contentImagesRequest)).right.get
    )
    val jsonContentReviewScoreRequest = dropNullValues(
      parse(circeSerializer[ContentReviewScoreRequest].serialize(contentReviewScoreRequest)).right.get
    )
    val jsonContentLocalInformationRequest = dropNullValues(
      parse(circeSerializer[ContentLocalInformationRequest].serialize(contentLocalInformationRequest)).right.get
    )
    val jsonContentExperienceRequest = dropNullValues(
      parse(circeSerializer[ContentExperienceRequest].serialize(contentExperienceRequest)).right.get
    )
    val jsonContentFeaturesRequest = dropNullValues(
      parse(circeSerializer[ContentFeaturesRequest].serialize(contentFeaturesRequest)).right.get
    )
    val jsonContentHighlightsRequest = dropNullValues(
      parse(circeSerializer[ContentHighlightsRequest].serialize(contentHighlightsRequest)).right.get
    )
    val jsonContentQnaRequest = dropNullValues(
      parse(circeSerializer[ContentQnaRequest].serialize(contentQnaRequest)).right.get
    )
    val jsonContentTopicsRequest = dropNullValues(
      parse(circeSerializer[TopicsRequest].serialize(contentTopicsRequest)).right.get
    )
    val jsonPriceStreamMetaLabRequest = dropNullValues(
      parse(circeSerializer[PriceStreamMetaLabRequest].serialize(priceStreamMetaLabRequest)).right.get
    )
    val jsonBannerCampaignRequest = dropNullValues(
      parse(circeSerializer[BannerCampaignRequestItTest].serialize(bannerCampaignRequest)).right.get
    )
    val jsonContentRateCategoryRequest = dropNullValues(
      parse(circeSerializer[ContentRateCategoryRequest].serialize(contentRateCategoryRequest)).right.get
    )
    val jsonGrowthProgramInfoRequest = dropNullValues(
      parse(circeSerializer[GrowthProgramInfoRequest].serialize(growthProgramInfoRequest)).right.get
    )
    val jsonMap: Map[String, Json] = Map(
      "PropertyDetailsRequest"         -> jsonPropertyDetailsRequest,
      "ContentImagesRequest"           -> jsonContentImagesRequest,
      "ContentReviewScoreRequest"      -> jsonContentReviewScoreRequest,
      "ContentLocalInformationRequest" -> jsonContentLocalInformationRequest,
      "ContentExperienceRequest"       -> jsonContentExperienceRequest,
      "ContentFeaturesRequest"         -> jsonContentFeaturesRequest,
      "ContentHighlightsRequest"       -> jsonContentHighlightsRequest,
      "ContentQnaRequest"              -> jsonContentQnaRequest,
      "ContentTopicsRequest"           -> jsonContentTopicsRequest,
      "PriceStreamMetaLabRequest"      -> jsonPriceStreamMetaLabRequest,
      "BannerCampaignRequest"          -> jsonBannerCampaignRequest,
      "ContentRateCategoryRequest"     -> jsonContentRateCategoryRequest,
      "GrowthProgramInfoRequest"       -> jsonGrowthProgramInfoRequest
    )
    val variables = parse(Printer.noSpaces.copy(dropNullValues = true).print(jsonMap.asJson)).right.get

    Query(query, None, variables)
  }

  private def toCoreSearchJson[T <: BaseRequest[ComplexSearchRequest]](
      request: T,
      parameterBuilder: ComplexSearchParametersBuilder[T]
  )(implicit encoder: Encoder[T]) =
    Map(parameterBuilder.parameterName -> dropNullValues(parse(circeSerializer[T].serialize(request)).right.get))

  /** circe has been downgraded back to a stable release of 0.11.x series by dependencies, this method comes from 0.12
    * which hasn't been released for Scala 2.11
    * https://github.com/circe/circe/pull/983/commits/0c66b4ff87071666b5d3c3be91dfbdde8b49d2f8
    */
  private def dropNullValues(json: Json): Json =
    json.mapObject(_.filter { case (_, v) => !v.isNull })
}
