package com.agoda.papi.search.validation

import com.agoda.core.search.models.request.{
  BaseRequest,
  CampaignFilterDefinition,
  ComplexSearchRequest,
  ExtraAgodaHomesRequest,
  FeaturedLuxuryHotelsRequest,
  FeatureFlagRequest,
  HighlyRatedAgodaHomesRequest,
  ItineraryCriteria,
  MatrixGroupRequest,
  Page,
  SearchContext,
  SearchCriteria,
  SearchDetailRequest
}
import com.agoda.core.search.models.enumeration.SearchTypes.CitySearch
import com.agoda.core.search.models.enumeration.matrix.{ FilterKeys, MatrixGroupTypes }
import com.agoda.core.search.models.{ request, CustomFilterRequest, FilterRequest, IdsFilterRequest }
import com.agoda.core.search.models.enumeration.matrix.FilterKeys.CampaignId
import FilterKeys.ProductType
import com.agoda.core.search.utils.ItineraryCriteriaHelper
import com.agoda.core.search.validation.{ ComplexSearchRequestValidationRules, ValidationFailure }
import com.agoda.papi.search.validation.constant.ValidationFailures.{
  ExtraAgodaHomesRangeValidationFailure,
  ExtraHotelsValidationFailure,
  FeaturedLuxuryHotelsRangeValidationFailure,
  HighlyRatedAgodaHomesRangeValidationFailure,
  HistogramBinsValidationFailure,
  IdsFilterValidationFailure,
  MatrixGroupSizeValidationFailure,
  PageNumberValidationFailure,
  PageSizeValidationFailure
}
import org.joda.time.DateTime
import org.scalatest.matchers.should.Matchers
import org.scalatest.wordspec.AnyWordSpec

class ComplexSearchRequestValidationRulesSpec extends AnyWordSpec with Matchers {
  private val aValidCurrency: String = "USD"
  private val aValidItineraryCriteria =
    ItineraryCriteria(Some(DateTime.now), ItineraryCriteriaHelper.getCheckInDate(DateTime.now), 1, 1, 2, 0, None)
  private val aValidFeatureFlagRequest = FeatureFlagRequest()

  private val aValidSearchCriteria = SearchCriteria(
    ratePlans = None,
    isUserLoggedIn = false,
    currency = aValidCurrency,
    itineraryCriteria = Some(aValidItineraryCriteria),
    featureFlagRequest = Some(aValidFeatureFlagRequest)
  )

  private val aValidSearchContext: SearchContext = request.SearchContext(
    searchId = Some(""),
    locale = "en-us",
    userId = Some(java.util.UUID.randomUUID().toString),
    cid = 1,
    memberId = 1,
    origin = "",
    platform = 1,
    endpointSearchType = Some(CitySearch)
  )

  private val aValidPage = Some(Page(10, 1))
  private val aValidSearchRequest =
    request.ComplexSearchRequest(aValidSearchCriteria, aValidSearchContext, None, None, None, None, aValidPage)

  private def getComplexSearchRequestWithCustomFilter(campaignId: Long): ComplexSearchRequest = {
    val filterRequest = Some(
      FilterRequest(Some(CustomFilterRequest(campaign = Some(CampaignFilterDefinition(campaignId)))))
    )
    aValidSearchRequest.copy(filterRequest = filterRequest)
  }

  private def getComplexSearchRequestWithIdsFilter(campaignId: Long): ComplexSearchRequest = {
    val filterRequest = Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(CampaignId, List(campaignId))))))
    aValidSearchRequest.copy(filterRequest = filterRequest)
  }

  private def validateComplexSearchRequest(campaignId: Long, isValid: Boolean): Unit = {
    ComplexSearchRequestValidationRules
      .validate(
        getComplexSearchRequestWithCustomFilter(campaignId)
      )
      .isEmpty shouldBe isValid
    ComplexSearchRequestValidationRules
      .validate(
        getComplexSearchRequestWithIdsFilter(campaignId)
      )
      .isEmpty shouldBe isValid
  }

  "idsFilters validation" when {
    "idsFilterRequests type is ProductType" should {
      "return success when ids = -1" in {
        val filterRequest        = Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(ProductType, List(-1))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe None
      }
      "return success when ids = Int.MaxValue" in {
        val filterRequest =
          Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(ProductType, List(Int.MaxValue))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe None
      }
      "return success when ids is empty list" in {
        val filterRequest = Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(ProductType, List.empty)))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe None
      }
      "return failure when ids is less than -1" in {
        val filterRequest        = Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(ProductType, List(-2))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe Some(
          ValidationFailure(IdsFilterValidationFailure)
        )
      }
      "return failure when ids is more than Int.MaxValue" in {
        val filterRequest =
          Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(ProductType, List(Int.MaxValue.toLong + 1L))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe Some(
          ValidationFailure(IdsFilterValidationFailure)
        )
      }
    }
    "idsFilterRequest type is not ProductType" should {
      "return success when id is exactly 0" in {
        val filterRequest =
          Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(FilterKeys.RoomBenefits, List(0))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe None
      }
      "return success when id is exactly Int.MaxValue" in {
        val filterRequest =
          Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(FilterKeys.RoomBenefits, List(Int.MaxValue))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe None
      }
      "return success when id is empty list" in {
        val filterRequest =
          Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(FilterKeys.RoomBenefits, List.empty)))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe None
      }
      "return failure when id is less than 0" in {
        val filterRequest =
          Some(FilterRequest(idsFilters = Some(List(IdsFilterRequest(FilterKeys.RoomBenefits, List(-1))))))
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe Some(
          ValidationFailure(IdsFilterValidationFailure)
        )
      }
      "return failure when id is more than Int.MaxValue" in {
        val filterRequest = Some(
          FilterRequest(idsFilters =
            Some(List(IdsFilterRequest(FilterKeys.RoomBenefits, List(Int.MaxValue.toLong + 1L))))
          )
        )
        val complexSearchRequest = aValidSearchRequest.copy(filterRequest = filterRequest)
        ComplexSearchRequestValidationRules.validate(complexSearchRequest) shouldBe Some(
          ValidationFailure(IdsFilterValidationFailure)
        )
      }
    }
    "customfilters.campaign is not empty" should {
      "return success when campaign id is a positive integer" in {
        val campaignId: Long = Int.MaxValue
        validateComplexSearchRequest(campaignId, isValid = true)
      }
      "return success when campaign id = 0" in {
        val campaignId: Long = 0L
        validateComplexSearchRequest(campaignId, isValid = true)
      }
      "return failure when campaign id is negative" in {
        val campaignId: Long = -1L
        validateComplexSearchRequest(campaignId, isValid = false)
      }
      "return failure when campaign id exceeds Int" in {
        val campaignId: Long = Int.MaxValue + 1
        validateComplexSearchRequest(campaignId, isValid = false)
      }
    }
  }

  "page validation" should {
    def aValidPage: Page = Page(pageSize = 0, pageNumber = 0)
    "return success if page.pageSize is exactly 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(page = Some(aValidPage.copy(pageSize = 0)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return success if page.pageSize is exactly 100" in {
      val complexSearchRequest = aValidSearchRequest.copy(page = Some(aValidPage.copy(pageSize = 100)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if page.pageSize is less than 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(page = Some(aValidPage.copy(pageSize = -1)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(PageSizeValidationFailure)
      )
    }
    "return failure if page.pageSize is more than 100" in {
      val complexSearchRequest = aValidSearchRequest.copy(page = Some(aValidPage.copy(pageSize = 101)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(PageSizeValidationFailure)
      )
    }
    "return success if page.pageNumber is exactly 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(page = Some(aValidPage.copy(pageNumber = 0)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if page.pageNumber is less than 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(page = Some(aValidPage.copy(pageNumber = -1)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(PageNumberValidationFailure)
      )
    }
  }

  "highlyRatedAgodaHomesRequest validation" should {
    def aValidHighlyRateAgodaHomesRequest: HighlyRatedAgodaHomesRequest =
      HighlyRatedAgodaHomesRequest(numberOfAgodaHomes = Some(0))
    "return success if highlyRatedAgodaHomesRequest is exactly BaseRequest.MaxNumberOfHighlyRatedAgodaHomes" in {
      val complexSearchRequest = aValidSearchRequest.copy(highlyRatedAgodaHomesRequest =
        Some(
          aValidHighlyRateAgodaHomesRequest.copy(numberOfAgodaHomes =
            Some(BaseRequest.MaxNumberOfHighlyRatedAgodaHomes)
          )
        )
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return success if highlyRatedAgodaHomesRequest is exactly 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(highlyRatedAgodaHomesRequest =
        Some(aValidHighlyRateAgodaHomesRequest.copy(numberOfAgodaHomes = Some(0)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if highlyRatedAgodaHomesRequest.numberOfAgodaHomes is less than 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(highlyRatedAgodaHomesRequest =
        Some(aValidHighlyRateAgodaHomesRequest.copy(numberOfAgodaHomes = Some(-1)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe a[Some[_]]
    }
    "return failure if highlyRatedAgodaHomesRequest.numberOfAgodaHomes is higher than BaseRequest.MaxNumberOfHighlyRatedAgodaHomes" in {
      val complexSearchRequest = aValidSearchRequest.copy(highlyRatedAgodaHomesRequest =
        Some(
          aValidHighlyRateAgodaHomesRequest.copy(numberOfAgodaHomes =
            Some(BaseRequest.MaxNumberOfHighlyRatedAgodaHomes + 1)
          )
        )
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(HighlyRatedAgodaHomesRangeValidationFailure(BaseRequest.MaxNumberOfHighlyRatedAgodaHomes))
      )
    }
  }

  "extraAgodaHomesValidation validation" should {
    def aValidExtraAgodaHomeRequest: ExtraAgodaHomesRequest = ExtraAgodaHomesRequest(numberOfAgodaHomes = Some(0))
    "return success if highlyRatedAgodaHomesRequest is exactly BaseRequest.MaxNumberOfExtraAgodaHomes" in {
      val complexSearchRequest = aValidSearchRequest.copy(extraAgodaHomesRequest =
        Some(aValidExtraAgodaHomeRequest.copy(numberOfAgodaHomes = Some(BaseRequest.MaxNumberOfExtraAgodaHomes)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return success if extraAgodaHomesRequest is exactly 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(extraAgodaHomesRequest =
        Some(aValidExtraAgodaHomeRequest.copy(numberOfAgodaHomes = Some(0)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if extraAgodaHomesRequest.numberOfAgodaHomes is less than 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(extraAgodaHomesRequest =
        Some(aValidExtraAgodaHomeRequest.copy(numberOfAgodaHomes = Some(-1)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe a[Some[_]]
    }
    "return failure if extraAgodaHomesRequest.numberOfAgodaHomes is higher than BaseRequest.MaxNumberOfExtraAgodaHomes" in {
      val complexSearchRequest = aValidSearchRequest.copy(extraAgodaHomesRequest =
        Some(aValidExtraAgodaHomeRequest.copy(numberOfAgodaHomes = Some(BaseRequest.MaxNumberOfExtraAgodaHomes + 1)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(ExtraAgodaHomesRangeValidationFailure(BaseRequest.MaxNumberOfExtraAgodaHomes))
      )
    }
  }

  "featuredLuxuryHotelsRequest validation" should {
    def aValidFeaturedLuxuryHotelRequest: FeaturedLuxuryHotelsRequest =
      FeaturedLuxuryHotelsRequest(numberOfHotels = Some(0))
    "return success if highlyRatedAgodaHomesRequest is exactly BaseRequest.MaxNumberOfExtraAgodaHomes" in {
      val complexSearchRequest = aValidSearchRequest.copy(featuredLuxuryHotelsRequest =
        Some(aValidFeaturedLuxuryHotelRequest.copy(numberOfHotels = Some(BaseRequest.MaxNumberOfFeaturedLuxuryHotels)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return success if featuredLuxuryHotelsRequest is exactly 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(featuredLuxuryHotelsRequest =
        Some(aValidFeaturedLuxuryHotelRequest.copy(numberOfHotels = Some(0)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if featuredLuxuryHotelsRequest.numberOfHotels is less than 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(featuredLuxuryHotelsRequest =
        Some(aValidFeaturedLuxuryHotelRequest.copy(numberOfHotels = Some(-1)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe a[Some[_]]
    }
    "return failure if featuredLuxuryHotelsRequest.numberOfHotels is higher than BaseRequest.MaxNumberOfExtraAgodaHomes" in {
      val complexSearchRequest = aValidSearchRequest.copy(featuredLuxuryHotelsRequest =
        Some(
          aValidFeaturedLuxuryHotelRequest.copy(numberOfHotels = Some(BaseRequest.MaxNumberOfFeaturedLuxuryHotels + 1))
        )
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(FeaturedLuxuryHotelsRangeValidationFailure(BaseRequest.MaxNumberOfFeaturedLuxuryHotels))
      )
    }
  }

  "searchDetailRequest.priceHistogramBins validation" should {
    "return success if searchDetailRequest.priceHistogramBins is exactly 0" in {
      val complexSearchRequest =
        aValidSearchRequest.copy(searchDetailRequest = Some(SearchDetailRequest(priceHistogramBins = Some(0))))
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if searchDetailRequest.priceHistogramBins is less than 0" in {
      val complexSearchRequest =
        aValidSearchRequest.copy(searchDetailRequest = Some(SearchDetailRequest(priceHistogramBins = Some(-1))))
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(
        ValidationFailure(HistogramBinsValidationFailure)
      )
    }
  }

  "matrixGroup validation" should {
    def aValidMatrixGroupRequest: MatrixGroupRequest =
      request.MatrixGroupRequest(matrixGroup = MatrixGroupTypes.Segment, size = Some(0))
    "return success if all matrixGroup's sizes are exactly 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(matrixGroup =
        Some(
          List(
            aValidMatrixGroupRequest.copy(size = Some(0)),
            aValidMatrixGroupRequest.copy(size = Some(0))
          )
        )
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return success if all of matrixGroup's size is None" in {
      val complexSearchRequest = aValidSearchRequest.copy(matrixGroup =
        Some(
          List(
            aValidMatrixGroupRequest.copy(size = None),
            aValidMatrixGroupRequest.copy(size = None)
          )
        )
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if some of matrixGroup's size is less than 0" in {
      val complexSearchRequest = aValidSearchRequest.copy(matrixGroup =
        Some(
          List(
            aValidMatrixGroupRequest.copy(size = Some(-1)),
            aValidMatrixGroupRequest.copy(size = Some(0))
          )
        )
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(ValidationFailure(MatrixGroupSizeValidationFailure))
    }
  }

  "extraHotels validation" should {
    "return success if extraHotelIds is empty" in {
      val complexSearchRequest = aValidSearchRequest.copy(extraHotels = Some(request.ExtraHotelsRequest(List.empty)))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }

    "return success if extraHotelIds has one element" in {
      val complexSearchRequest = aValidSearchRequest.copy(extraHotels = Some(request.ExtraHotelsRequest(List(1L))))
      val validationResult     = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult.isDefined shouldBe false
    }
    "return failure if extraHotelsRequest.extraHotelIds has more than 1 element" in {
      val complexSearchRequest = aValidSearchRequest.copy(
        extraHotels = Some(request.ExtraHotelsRequest(List(1L, 2L)))
      )
      val validationResult = ComplexSearchRequestValidationRules.validate(complexSearchRequest)
      validationResult shouldBe Some(ValidationFailure(ExtraHotelsValidationFailure))
    }
  }

}
