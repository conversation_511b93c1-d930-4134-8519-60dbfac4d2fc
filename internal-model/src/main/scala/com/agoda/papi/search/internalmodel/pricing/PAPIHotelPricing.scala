package com.agoda.papi.search.internalmodel.pricing

import com.agoda.papi.enums.room.CancellationGroup
import com.agoda.papi.pricing.supply.models.GrowthProgramInfo
import com.agoda.upi.models.cart.Cart
import constant.ConsolidatedAppliedDiscountType
import models._
import models.pricing.enums.{ DFBundleType, DFBundleTypes, PaymentModel }
import models.starfruit.{ ConsolidatedAppliedDiscount, DisplayBasis, ExternalLoyaltyDisplay }
import models.starfruit.PropertyPricingJsonProtocol.SupplierFinancialData

case class PAPIHotelPricing(
    hotelId: Long,
    isReady: Boolean,
    isAvailable: Boolean,
    payment: Option[PAPIDFPayment] = None,
    benefits: List[Long] = List.empty,
    cheapestRoomOffer: Option[PAPICheapestRoomOffer] = None,
    suppliersSummary: Option[DFSuppliersSummary],
    suppliersSummaries: List[DFSupplierSummary] = Nil,
    supplierInfo: Option[List[PAPIDFSupplierInfo]],
    roomLevelPayLaterData: Option[Seq[PAPIPayLaterData]] = None,
    hotelAggregatedOptions: Option[PAPIHotelAggregatedOptions] = None,
    availableRoomIds: Set[Long] = Set.empty[Long],
    suggestedRoomQuantity: Int,
    suggestPriceType: DFSuggestPriceType,
    isEasyCancel: Option[Boolean] = None,
    offers: Seq[PAPIDFBundleOffer] = Nil,
    isCheapestOnly: Boolean = true,
    rocketmilesPublishedPrices: Option[DFRocketMilesPublishedPricing] = None,
    rocketmilesCheapestRoomPricing: Option[List[DFRocketmilesCheapestRoomPricing]] = None,
    cheapestStayPackageRatePlans: Option[Seq[CheapestStayPackageRatePlans]] = None,
    pulseCampaignMetadata: Option[PAPIPulseCampaignMetadata] = None,
    externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay] = None,
    dayuseInfo: Option[PAPIDayuseInfo] = None,
    growthProgramInfo: Option[GrowthProgramInfo] = None
) {

  lazy val cheapestBundle: Option[PAPIDFBundleOffer] =
    if (this.isCheapestOnly) {
      offers.find(_.bundleType.contains(DFBundleTypes.FlexibleMultiRoom)).orElse(offers.headOption)
    } else {
      None
    }

}

case class PAPIHotelAggregatedOptions(benefits: Seq[PAPIBenefit], channelIds: Set[Long])

case class PAPIBenefit(id: Long)

case class PAPICheapestRoomOffer(supplierId: Long, pricing: List[PAPIDFCurrencyPrice])

case class PAPIDFBundleOffer(roomOffers: Seq[PAPIRoomBundleOffer], bundleType: Option[DFBundleType])

case class PAPIDFPrices(display: Double, crossedOutPrice: Double, displayAfterCashback: Option[Double] = None)

case class PAPIDFPriceType(exclusive: PAPIDFPrices, inclusive: PAPIDFPrices)

case class PAPIDFRoomPricing(
    perBook: PAPIDFPriceType,
    perRoomPerNight: PAPIDFPriceType,
    perNight: PAPIDFPriceType,
    totalDiscount: Int = 0,
    display: DisplayBasis
)

case class PAPIDFCurrencyPrice(
    currency: String,
    price: PAPIDFRoomPricing,
    packagePriceAndSaving: Option[PAPICartProductPropertyDisplay]
)

case class PAPICartProductPropertyDisplay()

case class PAPIRoomBundleOffer(room: PAPIRoomOffer)

case class PAPIRoomOffer(
    uid: String,
    benefits: List[PAPIDFBenefitDetails],
    payment: PAPIDFPayment,
    supplierId: Long,
    availableRooms: Int,
    pricing: List[PAPIDFCurrencyPrice],
    packaging: Option[DFPackaging],
    mseRoomSummaries: List[PAPIMseRoomSummary],
    isMseProperty: Boolean,
    typeId: Long,
    masterTypeId: Option[Long] = None,
    channel: PAPIChannel,
    isSuggested: Boolean = false,
    promotions: Option[DFPromotion],
    extraPriceInfo: Option[DFExtraPriceInformation],
    supplierFinancialData: Option[SupplierFinancialData] = None,
    loyaltyDisplay: Option[PAPILoyaltyDisplay] = None,
    ratePlan: Option[PAPIRatePlan] = None,
    capacity: DFCapacity,
    cart: Option[Cart],
    consolidatedAppliedDiscount: Option[ConsolidatedAppliedDiscount] = None
) {

  def isConsolidatedAppliedEligible: Boolean =
    consolidatedAppliedDiscount.exists(
      _.breakdowns.exists(item =>
        item.amount > 0d && (
          item.discountType == ConsolidatedAppliedDiscountType.promoDiscount
            || item.discountType == ConsolidatedAppliedDiscountType.downliftDiscount
        )
      )
    )

}

case class PAPIRatePlan(
    id: Long
)

case class PAPIChannel(id: Long)

case class PAPIPayLaterData(paymentModel: PaymentModel)

case class PAPIMseRoomSummary(supplierId: Long, pricingSummaries: List[PAPIDFCurrencyPrice])

case class PAPIDFBenefitDetails(id: Long)

case class PAPIDFSupplierInfo(id: Long, name: String)

case class PAPIDFPayLater(
    isEligible: Boolean = false
)

case class PAPIDFCancellation(
    code: String,
    cancellationType: CancellationGroup
)

object PAPIDFCancellation {
  val unknown = PAPIDFCancellation(code = "", cancellationType = CancellationGroup.Unknown)
}

case class PAPIPaymentOption(isEligible: Boolean)

case class PAPIDFTaxReceipt(isEligible: Boolean)

case class PAPIDFPayment(
    payLater: PAPIDFPayLater,
    cancellation: PAPIDFCancellation,
    noCreditCard: PAPIPaymentOption,
    payAtHotel: PAPIPaymentOption,
    taxReceipt: PAPIDFTaxReceipt
)

case class PAPIPulseCampaignMetadata()

case class PAPILoyaltyDisplay(items: Seq[String])

case class PAPIDayuseCheckInTime(hourOfDay: Int, minuteOfHour: Int = 0)
case class PAPIDayuseInfo(dayuseCheckInTimes: Seq[PAPIDayuseCheckInTime] = Seq.empty)
