package com.agoda.papi.search.internalmodel.request.encoder

import com.agoda.commons.traffic.TrafficInfo
import com.agoda.core.search.models.{
  CompositeFilterRequest,
  CustomFilterRequest,
  DynamicFilterRequest,
  ElasticFilterRequest,
  FilterRequest,
  IdsFilterRequest,
  RangeFilterItem,
  RangeFilterRequest,
  TextFilterRequest
}
import com.agoda.core.search.models.enumeration.{ HourlySortParams, Sorting, SortParams }
import com.agoda.core.search.models.request.{
  AdditionalClientInfo,
  APORequest,
  AreaSearchRequest,
  BoxSearchRequest,
  CityCenterSearchRequest,
  CitySearchRequest,
  ComplexSearchRequest,
  DoubleEndedRangeFilterDefinition,
  Experiments,
  ExtraAgodaHomesRequest,
  ExtraHotelsRequest,
  FeaturedAgodaHomesRequest,
  FeaturedLuxuryHotelsRequest,
  FeaturedPulsePropertiesRequest,
  FeatureFlagRequest,
  FeatureSwitch,
  FilterDefinition,
  FlexibleSearchRequest,
  ForcedExperiment,
  GeoPoint,
  HighlyRatedAgodaHomesRequest,
  IdsFilterDefinition,
  ItineraryCriteria,
  LandmarkSearchRequest,
  LoyaltyRequestCriteria,
  MatrixField,
  MatrixGroupRequest,
  Packaging,
  PackagingToken,
  Page,
  PersonalizedPropertiesRequest,
  PollingInfoRequest,
  PropertyMatchResultRequest,
  RadiusSearchRequest,
  RankingRequest,
  RankingWeightsRequest,
  RegionSearchRequest,
  RocketmilesRequestV2,
  SearchContext,
  SearchCriteria,
  SearchDetailRequest,
  SearchHistory,
  SingleEndedRangeFilterDefinition,
  StateSearchRequest,
  SupplierPullMetadataRequest,
  TopAreaSearchRequest,
  TrackSteps
}
import com.agoda.core.search.models._
import com.agoda.core.search.models.request._
import com.agoda.core.search.models.request.occ.{ CoreChildType, CoreOccFilter, CoreRoomAssignment }
import com.agoda.papi.search.internalmodel.growthprogram.GrowthProgramInfoRequest
import com.agoda.papi.search.internalmodel.request.{
  HostProfileRequest,
  PropertyAllotmentSearchRequest,
  PropertyDetailsRequest,
  PropertySummarySearchRequest
}
import com.agoda.papi.search.internalmodel.serializer.encoder.{ CustomEncoders, EnumEncoders }
import com.github.dwickern.macros.NameOf.nameOf
import io.circe.{ Encoder, Json }
import io.circe.derivation.deriveEncoder
import com.agoda.ml.ranking.jarvis.FreeTextSortRequest

object RequestEncodersForWarmUp {

  import EnumEncoders._
  import CustomEncoders._
  import com.agoda.upi.models.serializer.encoder.Encoders.cartBaseRequestEncoder

  /** SearchCriteria* */
  implicit private val coreChildTypeEncoder: Encoder[CoreChildType]           = deriveEncoder[CoreChildType]
  implicit private val coreRoomAssignmentEncoder: Encoder[CoreRoomAssignment] = deriveEncoder[CoreRoomAssignment]
  implicit private val CoreOccFilterEncoder: Encoder[CoreOccFilter]           = deriveEncoder[CoreOccFilter]
  implicit private val searchCriteriaEncoder: Encoder[SearchCriteria]         = deriveEncoder[SearchCriteria]
  implicit private val itineraryCriteriaEncoder: Encoder[ItineraryCriteria]   = deriveEncoder[ItineraryCriteria]
  implicit private val supplierPullMetadataRequestEncoder: Encoder[SupplierPullMetadataRequest] =
    deriveEncoder[SupplierPullMetadataRequest]
  implicit private val SortingEncoder: Encoder[Sorting]                   = deriveEncoder[Sorting]
  implicit private val SortParamsEncoder: Encoder[SortParams]             = deriveEncoder[SortParams]
  implicit private val HourlySortParamsEncoder: Encoder[HourlySortParams] = deriveEncoder[HourlySortParams]
  implicit private val RankingWeightsRequestEncoder: Encoder[RankingWeightsRequest] =
    deriveEncoder[RankingWeightsRequest]
  implicit private val loyaltyRequestCriteriaEncoder: Encoder[LoyaltyRequestCriteria] =
    deriveEncoder[LoyaltyRequestCriteria]

  /** SearchContext* */
  implicit private val packagingTokenEncoder: Encoder[PackagingToken]             = deriveEncoder[PackagingToken]
  implicit private val packagingEncoder: Encoder[Packaging]                       = deriveEncoder[Packaging]
  implicit private val searchContextEncoder: Encoder[SearchContext]               = deriveEncoder[SearchContext]
  implicit private val ExperimentsEncoder: Encoder[Experiments]                   = deriveEncoder[Experiments]
  implicit private val ForcedExperimentEncoder: Encoder[ForcedExperiment]         = deriveEncoder[ForcedExperiment]
  implicit private val TrafficInfoEncoder: Encoder[TrafficInfo]                   = deriveEncoder[TrafficInfo]
  implicit private val TrackStepsEncoder: Encoder[TrackSteps]                     = deriveEncoder[TrackSteps]
  implicit private val FeatureSwitchEncoder: Encoder[FeatureSwitch]               = deriveEncoder[FeatureSwitch]
  implicit private val FeatureFlagRequestEncoder: Encoder[FeatureFlagRequest]     = deriveEncoder[FeatureFlagRequest]
  implicit private val additionalClientInfoEncoder: Encoder[AdditionalClientInfo] = deriveEncoder[AdditionalClientInfo]

  /** FilterRequest * */
  implicit private val PriceFilterEncoder         = deriveEncoder[PriceFilterDefinition]
  implicit private val hourlyFilterEncoder        = deriveEncoder[BookingDurationFilterDefinition]
  implicit private val NameFilterEncoder          = deriveEncoder[NameFilterDefinition]
  implicit private val BenefitsFilterEncoder      = deriveEncoder[BenefitsFilterDefinition]
  implicit private val PaymentModelFilterEncoder  = deriveEncoder[PaymentModeFilterDefinition]
  implicit private val CampaignFilterEncoder      = deriveEncoder[CampaignFilterDefinition]
  implicit private val BannerFilterEncoder        = deriveEncoder[BannerFilterDefinition]
  implicit private val RoomBenefitsFilterEncoder  = deriveEncoder[RoomBenefitsFilterDefinition]
  implicit private val DealsFilterEncoder         = deriveEncoder[DealsFilterDefinition]
  implicit private val PaymentOptionFilterEncoder = deriveEncoder[PaymentOptionsFilterDefinition]
  implicit private val RoomIdFilterEncoder        = deriveEncoder[RoomIdFilterDefinition]
  implicit private val TupleStringDoubleEncoder   = deriveEncoder[TupleStringDouble]
  implicit private val RangeFilterEncoder         = deriveEncoder[RangeFilterDefinition]
  implicit private val VoucherFilterDefinition    = deriveEncoder[VoucherFilterDefinition]
  implicit private val CollectionsFilterEncoder   = deriveEncoder[CollectionsFilterDefinition]

  implicit private val TermFilterEncoder = new Encoder[TermFilterDefinition] {

    final def apply(p: TermFilterDefinition): Json =
      Json.obj(
        (nameOf[TermFilterDefinition](_.col), Json.fromString(p.col)),
        (nameOf[TermFilterDefinition](_.value), Json.fromInt(Json.asInstanceOf[Int]))
      )

  }

  implicit private val TermsFilterEncoder = new Encoder[TermsFilterDefinition] {

    final def apply(p: TermsFilterDefinition): Json = {
      val values = p.values.map(m => Json.fromLong(m.asInstanceOf[Int]))
      Json.obj(
        (nameOf[TermsFilterDefinition](_.col), Json.fromString(p.col)),
        (nameOf[TermsFilterDefinition](_.values), Json.fromValues(values))
      )
    }

  }

  implicit private val AndTermsFilterEncoder = new Encoder[AndTermsFilterDefinition] {

    final def apply(p: AndTermsFilterDefinition): Json = {
      val values = p.values.map(m => Json.fromLong(m.asInstanceOf[Int]))
      Json.obj(
        (nameOf[AndTermsFilterDefinition](_.col), Json.fromString(p.col)),
        (nameOf[AndTermsFilterDefinition](_.values), Json.fromValues(values))
      )
    }

  }

  implicit private val IdsFilterEncoder = new Encoder[IdsFilterDefinition] {

    final def apply(p: IdsFilterDefinition): Json = {
      val values = p.values.map(m => Json.fromLong(m))
      Json.obj(
        (nameOf[IdsFilterDefinition](_.values), Json.fromValues(values))
      )
    }

  }

  implicit private val AndFilterEncoder  = deriveEncoder[AndFilterDefinition]
  implicit private val OrFilterEncoder   = deriveEncoder[OrFilterDefinition]
  implicit private val TextFilterEncoder = deriveEncoder[TextFilterDefinition]

  implicit private val OneOrMoreFilterEncoder = new Encoder[OneOrMoreFilterDefinition] {

    final def apply(p: OneOrMoreFilterDefinition): Json = {
      val values = p.values.map(m => Json.fromLong(m.asInstanceOf[Int]))
      Json.obj(
        (nameOf[OneOrMoreFilterDefinition](_.key), Json.fromString(p.key)),
        (nameOf[OneOrMoreFilterDefinition](_.values), Json.fromValues(values))
      )
    }

  }

  implicit private val SingleRangeFilterEncoder      = deriveEncoder[SingleEndedRangeFilterDefinition]
  implicit private val DoubleRangeFilterEncoder      = deriveEncoder[DoubleEndedRangeFilterDefinition]
  implicit private val CustomFilterEncoder           = deriveEncoder[CustomFilterRequest]
  implicit private val CompositeFilterRequestEncoder = deriveEncoder[CompositeFilterRequest]
  implicit private val ElasticFilterEncoder          = deriveEncoder[ElasticFilterRequest]
  implicit private val DynamicFilterEncoder          = deriveEncoder[DynamicFilterRequest]
  implicit private val IdsFilterRequestEncoder       = deriveEncoder[IdsFilterRequest]
  implicit private val RangeFilterItemEncoder        = deriveEncoder[RangeFilterItem]
  implicit private val RangeFilterRequestEncoder     = deriveEncoder[RangeFilterRequest]
  implicit private val TextFilterRequestEncoder      = deriveEncoder[TextFilterRequest]
  implicit private val FilterRequestEncoder          = deriveEncoder[FilterRequest]

  implicit private val FilterDefinitionEncoder: Encoder[FilterDefinition]       = deriveEncoder[FilterDefinition]
  implicit private val MatrixFieldEncoder: Encoder[MatrixField]                 = deriveEncoder[MatrixField]
  implicit private val MatrixGroupRequestEncoder: Encoder[MatrixGroupRequest]   = deriveEncoder[MatrixGroupRequest]
  implicit private val PageEncoder: Encoder[Page]                               = deriveEncoder[Page]
  implicit private val APORequestEncoder: Encoder[APORequest]                   = deriveEncoder[APORequest]
  implicit private val SearchHistoryEncoder: Encoder[SearchHistory]             = deriveEncoder[SearchHistory]
  implicit private val SearchDetailRequestEncoder: Encoder[SearchDetailRequest] = deriveEncoder[SearchDetailRequest]
  implicit private val ExtraHotelsRequestEncoder: Encoder[ExtraHotelsRequest]   = deriveEncoder[ExtraHotelsRequest]
  implicit private val ExtraAgodaHomesRequest: Encoder[ExtraAgodaHomesRequest]  = deriveEncoder[ExtraAgodaHomesRequest]
  implicit private val HighlyRatedAgodaHomesRequest: Encoder[HighlyRatedAgodaHomesRequest] =
    deriveEncoder[HighlyRatedAgodaHomesRequest]
  implicit private val FeaturedAgodaHomesRequest: Encoder[FeaturedAgodaHomesRequest] =
    deriveEncoder[FeaturedAgodaHomesRequest]
  implicit private val FeaturedLuxuryHotelsRequest: Encoder[FeaturedLuxuryHotelsRequest] =
    deriveEncoder[FeaturedLuxuryHotelsRequest]
  implicit private val FeaturedPulsePropertiesRequest: Encoder[FeaturedPulsePropertiesRequest] =
    deriveEncoder[FeaturedPulsePropertiesRequest]
  implicit private val PersonalizedPropertiesRequest: Encoder[PersonalizedPropertiesRequest] =
    deriveEncoder[PersonalizedPropertiesRequest]
  implicit private val PropertyMatchResultRequest: Encoder[PropertyMatchResultRequest] =
    deriveEncoder[PropertyMatchResultRequest]
  implicit private val RocketmilesRequestV2: Encoder[RocketmilesRequestV2] = deriveEncoder[RocketmilesRequestV2]
  implicit private val FlexibleSearchRequestEncoder: Encoder[FlexibleSearchRequest] =
    deriveEncoder[FlexibleSearchRequest]
  implicit private val RankingRequest: Encoder[RankingRequest]                    = deriveEncoder[RankingRequest]
  implicit private val FreeTextSortRequest: Encoder[FreeTextSortRequest]          = deriveEncoder[FreeTextSortRequest]
  implicit private val complexSearchRequestEncoder: Encoder[ComplexSearchRequest] = deriveEncoder[ComplexSearchRequest]
  implicit private val geoPointEncoder: Encoder[GeoPoint]                         = deriveEncoder[GeoPoint]
  implicit val citySearchRequestEncoder: Encoder[CitySearchRequest]               = deriveEncoder[CitySearchRequest]
  implicit val areaSearchRequestEncoder: Encoder[AreaSearchRequest]               = deriveEncoder[AreaSearchRequest]
  implicit val landmarkSearchRequestEncoder: Encoder[LandmarkSearchRequest]       = deriveEncoder[LandmarkSearchRequest]
  implicit val radiusSearchRequestEncoder: Encoder[RadiusSearchRequest]           = deriveEncoder[RadiusSearchRequest]
  implicit val boxSearchRequestEncoder: Encoder[BoxSearchRequest]                 = deriveEncoder[BoxSearchRequest]
  implicit val regionSearchRequestEncoder: Encoder[RegionSearchRequest]           = deriveEncoder[RegionSearchRequest]
  implicit val topAreaSearchRequestEncoder: Encoder[TopAreaSearchRequest]         = deriveEncoder[TopAreaSearchRequest]
  implicit val cityCenterSearchRequestEncoder: Encoder[CityCenterSearchRequest] = deriveEncoder[CityCenterSearchRequest]
  implicit val pollingInfoRequestEncoder: Encoder[PollingInfoRequest]           = deriveEncoder[PollingInfoRequest]
  implicit val propertySummarySearchRequestEncoder: Encoder[PropertySummarySearchRequest] =
    deriveEncoder[PropertySummarySearchRequest]
  implicit val propertyAllotmentSearchRequestEncoder: Encoder[PropertyAllotmentSearchRequest] =
    deriveEncoder[PropertyAllotmentSearchRequest]
  implicit val stateSearchRequestEncoder: Encoder[StateSearchRequest]         = deriveEncoder[StateSearchRequest]
  implicit val propertyDetailsRequestEncoder: Encoder[PropertyDetailsRequest] = deriveEncoder[PropertyDetailsRequest]

  implicit val growthProgramInfoRequestEncoder: Encoder[GrowthProgramInfoRequest] =
    deriveEncoder[GrowthProgramInfoRequest]
  implicit val hostProfileRequestEncoder: Encoder[HostProfileRequest] =
    deriveEncoder[HostProfileRequest]
}
