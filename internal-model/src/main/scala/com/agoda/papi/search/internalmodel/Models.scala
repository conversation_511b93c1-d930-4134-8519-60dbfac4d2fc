package com.agoda.papi.search.internalmodel

import api.request.ExternalLoyaltyProfile
import com.agoda.common.graphql.model.Wrapper
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.information.{ LocalInformation, PropertyEngagement }
import com.agoda.content.models.db.roomsummary.RoomSummaryResponse
import com.agoda.content.models.propertycontent._
import com.agoda.content.models.request.sf._
import com.agoda.core.search.models.request.{ BaseRequest, ComplexSearchRequest, PollingInfoRequest, SearchContext }
import com.agoda.core.search.models.response._
import com.agoda.everest.model.SponsoredType
import com.agoda.everest.model.enum.SponsoredType
import com.agoda.ml.ranking.jarvis.RankingSource
import com.agoda.papi.content.graphql.ContentPropertiesSummaryRequestEnvelope
import com.agoda.papi.search.internalmodel.content.PAPIContentSummary
import com.agoda.papi.search.internalmodel.enumeration.PropertyResultType
import com.agoda.papi.search.internalmodel.growthprogram.{ GrowthProgramInfoRequest, PAPIGrowthProgramInfo }
import pricing.PAPIHotelPricing
import com.agoda.papi.search.internalmodel.promocode.{ BannerCampaignInfo, BannerCampaignRequest }
import com.agoda.papi.search.internalmodel.request._
import com.agoda.papi.search.internalmodel.response._
import com.agoda.papi.search.types.{
  ContentSummaryWrapper,
  HotelIdType,
  HotelPricingWrapper,
  PropertyId,
  PropertyRankingWrapper,
  PropertySummaryEnvelopeList
}
import com.agoda.platform.service.context.GlobalContext
import com.agoda.pricestream.models.response.PropertyAttributes
import models.starfruit.{ OverridePricingContext, OverridePricingRequest, PricingRequestParameters }
import org.joda.time.DateTime
import sangria.execution.deferred.Deferred
import types.CityIdType
import com.agoda.papi.search.internalmodel.request.HostProfileRequest

// scalastyle:off
case class PriceBreakerSummaryEnvelope(
    propertyId: Long,
    content: ContentSummaryWrapper,
    pricing: Option[HotelPricingWrapper],
    propertyResultType: PropertyResultType
)

/** <AUTHOR>   mkhan Property Result Envelope for GQL model to derive an actual value to return
  */
case class PropertySummaryEnvelope(
    propertyId: Long,
    content: ContentSummaryWrapper,
    pricing: Option[HotelPricingWrapper],
    soldOut: Option[SoldOutSummary],
    dateless: Option[DatelessSummary],
    propertyRanking: Option[PropertyRankingWrapper],
    sponsoredDetail: PropertySponsoredDetail = PropertySponsoredDetail(),
    propertyResultType: PropertyResultType,
    metaLab: Option[PropertyAttributes] = None,
    @deprecated priceBreakerProperties: List[PriceBreakerSummaryEnvelope] = List.empty,
    overrideCheckInOutDate: Option[CheapestCheckInCheckOut] = None,
    topSellingPointInfo: Option[Seq[TopSellingPointInfo]] = None,
    uniqueSellingPointInfo: Option[Seq[UniqueSellingPointInfo]] = None
)

/** Internal use only
  */
case class SearchEnrichmentResponse(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    cumulativePropertiesResponse: CumulativePropertiesResponse
)

case class PropertySponsoredDetail(
    isShowSponsoredFlag: Boolean = false,
    sponsoredType: SponsoredType = SponsoredType.NoSponsored,
    trackingData: Option[String] = None,
    rankingType: Int = RankingSource.None.value
)

/** Container for request in Wrapper format
  * @param contentRequestWrapper
  */
case class PropertySearchRequestWrapper(
    contentRequestWrapper: Wrapper[ContentSummaryRequest],
    pricingRequestWrapper: Option[Wrapper[models.starfruit.PricingRequestParameters]],
    overrideContentRequest: Option[OverrideContentRequest] = None,
    overridePricingRequest: Option[OverridePricingRequest] = None,
    searchRequest: BaseRequest[ComplexSearchRequest],
    globalContext: GlobalContext,
    datelessRequestContainer: Option[DatelessRequestContainer] = None,
    overridePricingContext: Option[OverridePricingContext] = None,
    metaLabRequest: Option[PriceStreamMetaLabRequest] = None,
    capiRequestContext: Option[String] = None,
    capiEmailDomain: String = "",
    externalUserContext: Option[String] = None,
    externalUserProfileInfoContext: Option[ExternalLoyaltyProfile] = None
)

case class PricingRequestContainer(
    enabledOpaqueChannel: Option[Boolean] = None,
    isEnabledPartnerChannelSelection: Option[Boolean] = None,
    isPackageDeal: Boolean = false
)

/** Fields required to perform dateless search
  * @param cityId
  */
case class DatelessRequestContainer(cityId: CityIdType, currency: String)

/** @param contentRequestWrapper
  * @param pricingRequestWrapper
  * @param pricingRequestContainer
  *   this field contains extra field that does not send to DF. but need them for pre-processing
  * @param globalContext
  *   [[GlobalContext]] that is propagated within http headers
  */
case class PropertiesSearchRequestWrapper(
    contentRequestWrapper: ContentPropertiesSummaryRequestEnvelope,
    pricingRequestWrapper: Option[com.agoda.papi.pricing.graphql.PricingPropertiesRequestEnvelope],
    searchRequest: BaseRequest[ComplexSearchRequest],
    globalContext: GlobalContext,
    pricingRequestContainer: PricingRequestContainer,
    datelessRequestContainer: Option[DatelessRequestContainer] = None,
    metaLabRequest: Option[MetaLabPropertiesRequest] = None,
    capiRequestContext: Option[String] = None,
    capiEmailDomain: String,
    hotelSizeHints: Map[HotelIdType, Int] = Map.empty,
    hotelOverrideCheckInDate: Map[HotelIdType, CheapestCheckInCheckOut] = Map.empty,
    externalUserContext: Option[String] = None,
    hasHourlyRatesMap: Map[HotelIdType, Boolean] = Map.empty,
    externalUserProfileInfoContext: Option[ExternalLoyaltyProfile] = None
)

case class PropertiesWithEnrichment(
    normalProperties: List[PropertySummaryEnvelope],
    topSellingPointInfoMap: Map[HotelIdType, Seq[TopSellingPointInfo]],
    uniqueSellingPointInfoMap: Map[HotelIdType, Seq[UniqueSellingPointInfo]]
)

/** Deferred object which will be provided to Deferred Resolver for resolution
  * @param propertySearchRequestWrapper
  */
case class PropertyRequestDeferred(
    val propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[PropertySummaryEnvelopeList]

case class PropertySummaryModel(
    propertyId: HotelIdType,
    content: Option[PAPIContentSummary],
    pricing: Option[PAPIHotelPricing],
    propertyResultType: PropertyResultType,
    overrideCheckInDate: Option[CheapestCheckInCheckOut],
    topSellingPointInfo: Option[Seq[TopSellingPointInfo]],
    uniqueSellingPointInfo: Option[Seq[UniqueSellingPointInfo]]
)

case class PropertySummaryEnrichment(
    topSellingPoint: List[TopSellingPointInfo] = List.empty,
    uniqueSellingPoint: List[UniqueSellingPointInfo] = List.empty,
    pricingBadges: Option[PricingBadges] = None,
    showReviewSnippet: Option[Boolean] = None,
    propertySummaryModel: Option[PropertySummaryModel] = None,
    isFavorite: Option[Boolean] = None,
    distance: Option[DistanceEnrichInfo] = None,
    flexibleDate: Option[PropertyFlexibleDate] = None
)

case class EnrichmentDeferred(propertySummaryModel: PropertySummaryModel) extends Deferred[PropertySummaryEnrichment]

case class PriceStreamCitySearchMetaLabResponseDeferred(metaLabRequest: Option[PriceStreamMetaLabRequest])
    extends Deferred[PropertyAttributes]

case class BookingHistoryDeferred(propertySummaryModel: Option[PropertySummaryModel])
    extends Deferred[BookingHistoryInfo]

case class IsPopularDeferred(propertyId: Option[HotelIdType]) extends Deferred[Option[Boolean]]

case class HotelRoomInformationDeferred(propertySummaryModel: Option[PropertySummaryModel])
    extends Deferred[HotelRoomInformation]

case class PropertySummarySearchDeferred(
    propertyIds: List[Long],
    pricingRequestParameters: Option[PricingRequestParameters],
    contentSummaryRequest: Option[ContentSummaryRequest]
) extends Deferred[PropertySummaryEnvelopeList]

/** Deferred that will be used for enrichment
  * @param propertyId
  */
case class PropertySummaryEnrichmentDeferred(propertyId: Long) extends Deferred[PropertySummaryEnrichment]

/** Deferred object for Aggregation response
  * @param searchRequest
  * @param searchResult
  */
case class AggregationRequestDeferred(searchRequest: BaseRequest[ComplexSearchRequest], searchResult: SearchResult)
    extends Deferred[AggregationResponse]

/** GQL deferred object for resolving city search call to core search.
  * @param searchType
  * @param wrapper
  * @tparam T
  */
case class ComplexSearchDeferred[T <: BaseRequest[ComplexSearchRequest]](searchType: String, wrapper: Wrapper[T])
    extends Deferred[PropertySmartSearchResponse]

/** Cumulative response in PagedPropertyService
  * @param propertySummaryWrapper
  * @param inValidHotels
  * @param calledProperties
  *   All properties that already call to DF and Content (included buffer property)
  */
case class CumulativePropertiesResponse(
    propertySummaryWrapper: PropertySummaryEnvelopeList,
    inValidHotels: Vector[HotelIdType] = Vector.empty,
    calledProperties: Vector[HotelIdType] = Vector.empty,
    inValidApoHotels: Vector[HotelIdType] = Vector.empty,
    pageToken: Option[String] = None,
    pagingFlowMetadata: Option[PagingFlowMetadata]
)

case class PagingFlowMetadata(
    iterationCountByPropertyResultType: Map[PropertyResultType, Int]
)

/** Deferred object for Featured Agoda Homes
  * @param propertySearchRequestWrapper
  * @param searchResult
  */
case class FeaturedAgodaHomesRequestDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[PropertySummaryEnvelopeList]

/** Deferred object for Highly Rated Agoda Homes
  * @param propertySearchRequestWrapper
  * @param searchResult
  */
case class HighlyRatedAgodaHomesRequestDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[PropertySummaryEnvelopeList]

/** Deferred object for Extra Agoda Homes
  * @param propertySearchRequestWrapper
  * @param searchResult
  */
case class ExtraAgodaHomesRequestDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[PropertySummaryEnvelopeList]

/** Deferred object for Featured Luxury Hotels
  * @param propertySearchRequestWrapper
  * @param searchResult
  */
case class FeaturedLuxuryHotelsRequestDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[PropertySummaryEnvelopeList]

/** Deferred object for Pulse Properties
  * @param propertySearchRequestWrapper
  * @param searchResult
  */
case class FeaturedPulsePropertiesRequestDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[PropertySummaryEnvelopeList]

case class SearchEnrichmentDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    searchResult: SearchResult
) extends Deferred[SearchEnrichmentResponse]

case class SearchResultDeferred(searchResult: SearchResult) extends Deferred[GqlSearchResult]

case class GeoPlacesDeferred(objectInfo: ObjectExtraInfo, searchContext: SearchContext) extends Deferred[Seq[GeoPlace]]

/** Deferred that will be used for price trend requests
  * @param priceTrendSearchRequest
  */

case class PriceTrendSearchDetailsDeferred(priceTrendSearchRequest: PriceTrendSearchRequest)
    extends Deferred[PriceTrendSearchDetails]

case class PropertyAllotmentSearchDetailsDeferred(propertyAllotmentSearchRequest: PropertyAllotmentSearchRequest)
    extends Deferred[PropertyAllotmentSearchDetails]
case class PropertyDetailsDeferred(propertyDetails: PropertyDetailsRequest) extends Deferred[List[PropertyDetail]]

/** **===== Search Enrich 3rd Level=====***
  */
case class SupplierInformationDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    cumulativePropertiesResponse: CumulativePropertiesResponse
) extends Deferred[List[SupplierInformation]]

case class PointMaxInformationDeferred(propertySearchRequestWrapper: PropertySearchRequestWrapper)
    extends Deferred[Option[PointMaxInformation]]

case class BenefitInformationDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    cumulativePropertiesResponse: CumulativePropertiesResponse
) extends Deferred[List[BenefitInformation]]

case class CreditCardCampaignInfoDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    cumulativePropertiesResponse: CumulativePropertiesResponse
) extends Deferred[Option[CreditCardCampaignInfo]]

case class CreditCardCampaignInfosDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    cumulativePropertiesResponse: CumulativePropertiesResponse
) extends Deferred[Seq[CreditCardCampaignInfo]]

case class IsConnectedTripApplicableDeferred(
    propertySearchRequestWrapper: PropertySearchRequestWrapper,
    cumulativePropertiesResponse: CumulativePropertiesResponse
) extends Deferred[Option[Boolean]]

case class RoomSummaryDeferred(
    propertyId: PropertyId,
    contentRoomSummaryRequest: Wrapper[ContentExtraRoomSummaryRequest],
    pricing: Option[HotelPricingWrapper]
) extends PropertyDeferred[RoomSummaryResponse]

case class AlternativeDatePricingOverride(
    newCheckIn: DateTime,
    newCheckOut: DateTime,
    pricingOverride: OverridePricingRequest
)

case class SearchBannerCampaignInfoDeferred(
    searchRequest: BaseRequest[ComplexSearchRequest],
    bannerCampaignRequest: BannerCampaignRequest
) extends Deferred[List[BannerCampaignInfo]]

trait PropertyDeferred[+T] extends Deferred[T] {
  val propertyId: PropertyId
}

case class ContentDetailDeferred(propertyId: PropertyId) extends PropertyDeferred[ContentDetail]

case class ContentImagesDeferred(propertyId: PropertyId, contentImagesRequestWrapper: Wrapper[ContentImagesRequest])
    extends PropertyDeferred[ContentImagesResponse]

case class ContentReviewScoreDeferred(
    propertyId: PropertyId,
    contentReviewScoreRequestWrapper: Option[Wrapper[ContentReviewScoreRequest]]
) extends PropertyDeferred[ContentReviewScoreResponse]

case class ContentReviewSummariesDeferred(
    propertyId: PropertyId,
    contentReviewSummariesRequestWrapper: Option[Wrapper[ContentReviewSummariesRequest]]
) extends PropertyDeferred[ContentReviewSummariesResponse]

case class ContentInformationSummaryDeferred(
    propertyId: PropertyId,
    contentInformationSummaryRequestWrapper: Option[Wrapper[ContentInformationSummaryRequest]]
) extends PropertyDeferred[ContentInformationSummary]

case class ContentExperienceDeferred(
    propertyId: PropertyId,
    contentExperienceWrapper: Option[Wrapper[ContentExperienceRequest]]
) extends PropertyDeferred[Experiences]

case class ContentInformationDeferred(
    propertyId: PropertyId,
    contentInformationRequestWrapper: Option[Wrapper[ContentInformationRequest]]
) extends PropertyDeferred[ContentInformationSummary]

case class ContentLocalInformationDeferred(
    propertyId: PropertyId,
    contentLocalInformationRequestWrapper: Option[Wrapper[ContentLocalInformationRequest]]
) extends PropertyDeferred[LocalInformation]

case class ContentEngagementDeferred(propertyId: PropertyId) extends PropertyDeferred[PropertyEngagement]

case class ContentFeaturesDeferred(
    propertyId: PropertyId,
    contentFeaturesRequestWrapper: Option[Wrapper[ContentFeaturesRequest]]
) extends PropertyDeferred[ContentFeaturesResponse]

case class ContentHighlightsDeferred(
    propertyId: PropertyId,
    contentHighlightsRequestWrapper: Option[Wrapper[ContentHighlightsRequest]]
) extends PropertyDeferred[ContentHighlightsResponse]

case class ContentQnaDeferred(propertyId: PropertyId, contentQnaRequestWrapper: Option[Wrapper[ContentQnaRequest]])
    extends PropertyDeferred[ContentQnaResponse]

case class ContentTopicsDeferred(propertyId: PropertyId, contentTopicsRequestWrapper: Option[Wrapper[TopicsRequest]])
    extends PropertyDeferred[ContentTopicsResponse]

case class ContentRateCategoryDeferred(
    propertyId: PropertyId,
    contentRateCategoryRequestWrapper: Option[Wrapper[ContentRateCategoryRequest]]
) extends PropertyDeferred[ContentRateCategoryResponse]

case class PriceStreamFetchPropertyMetaInfoResponseDeferred(
    propertyId: PropertyId,
    propertyMetaInformationRequest: Option[PropertyMetaInformationRequest]
) extends Deferred[PriceStreamPropertyMetaInfoResponse]

case class PriceStreamFetchMetaLabResponseDeferred(
    propertyId: PropertyId,
    metaLabRequest: Option[PriceStreamMetaLabRequest]
) extends PropertyDeferred[PriceStreamPropertyAttributesResponse]

case class PollingInfoDeferred(pollingInfoRequest: Option[PollingInfoRequest]) extends Deferred[PollingInfoResponse]

case class HostProfileDeferred(propertyId: PropertyId, hostProfileRequest: Option[HostProfileRequest])
    extends PropertyDeferred[HostProfileResponse]

case class PropertyBannerCampaignInfoDeferred(propertyId: PropertyId, bannerCampaignRequest: BannerCampaignRequest)
    extends PropertyDeferred[List[BannerCampaignInfo]]

case class GrowthProgramInfoDeferred(propertyId: PropertyId, growthProgramInfoRequest: GrowthProgramInfoRequest)
    extends PropertyDeferred[PAPIGrowthProgramInfo]

// scalastyle:on
