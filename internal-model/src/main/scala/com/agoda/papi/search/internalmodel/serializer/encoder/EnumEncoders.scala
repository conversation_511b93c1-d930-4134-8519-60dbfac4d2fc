package com.agoda.papi.search.internalmodel.serializer.encoder

import api.request.{ FeatureFlag => DFFeatureFlag, PaymentChargeType }
import com.agoda.commons.traffic.TrafficType
import com.agoda.core.search.models.{ CancellationFeeType, CancellationType }
import com.agoda.core.search.models.enumeration.matrix.{
  FilterKey,
  FilterRequestType,
  MatrixExtraDataType,
  MatrixGroupType
}
import com.agoda.core.search.models.enumeration._
import com.agoda.core.search.models.request.LoyaltyRequestType
import com.agoda.everest.model.enum.SponsoredType
import com.agoda.papi.enums.campaign.{ CampaignStatusType, CampaignType, PromotionCodeType }
import com.agoda.papi.enums.hotel.{ PaymentModel, StayPackageType }
import com.agoda.papi.enums.request.RequestedPrice
import com.agoda.papi.enums.room.{
  ApplyType,
  ChildRateType,
  GeoType,
  ValueCalculationMethodType,
  ValueMethodType,
  WhomToPayType
}
import com.agoda.papi.enums.user.NhaProbability
import com.agoda.upi.models.enums.{ ArrangementEntry, ItemEntry, ProductEntry }
import enumerations.{ RecommendationFilterComparisonType, RecommendationFilterFieldType, RecommendationType }
import io.circe.{ Encoder, Json }
import models.pricing.enums.{
  ApplyType => DfApplyType,
  M150TransparencyVersionType,
  PaymentModel => DfPaymentModel,
  RequestedPrice => DFRequestedPrice,
  SwapRoomType
}
import models.starfruit.{ LoyaltyReason, LoyaltyType, SuggestedPrice => DFSuggestedPrice }
import request.ExternalServiceMode

object EnumEncoders {

  private val stringEncoder = implicitly[Encoder[String]]

  implicit val SortFieldEncoder: Encoder[SortField] = new Encoder[SortField] {
    override def apply(sortField: SortField): Json =
      stringEncoder.apply(sortField.entryName)
  }

  implicit val SortOrderEncoder: Encoder[SortOrder] = new Encoder[SortOrder] {
    override def apply(sortOrder: SortOrder): Json =
      stringEncoder.apply(sortOrder.entryName)
  }

  implicit val PaymentModelEncoder: Encoder[PaymentModel] = new Encoder[PaymentModel] {
    override def apply(paymentModel: PaymentModel): Json =
      stringEncoder.apply(paymentModel.entryName)
  }

  implicit val M150TransparencyVersionTypeEncoder: Encoder[M150TransparencyVersionType] =
    new Encoder[M150TransparencyVersionType] {
      override def apply(m150TransparencyVersion: M150TransparencyVersionType): Json =
        stringEncoder.apply(m150TransparencyVersion.entryName)
    }

  implicit val SponsoredTypeEncoder: Encoder[SponsoredType] = new Encoder[SponsoredType] {
    override def apply(sponsoredType: SponsoredType): Json =
      stringEncoder.apply(sponsoredType.id.toString)
  }

  implicit val TravellerTypeEncoder: Encoder[TravellerType] = new Encoder[TravellerType] {
    override def apply(a: TravellerType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val ApplyTypeEncoder: Encoder[ApplyType] = new Encoder[ApplyType] {
    override def apply(a: ApplyType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val SuggestedPriceEncoder: Encoder[RequestedPrice] = new Encoder[RequestedPrice] {
    override def apply(a: RequestedPrice): Json = stringEncoder.apply(a.entryName)
  }

  implicit val DFRequestedPriceEncoder: Encoder[DFRequestedPrice] = new Encoder[DFRequestedPrice] {
    override def apply(a: DFRequestedPrice): Json = stringEncoder.apply(a.entryName)
  }

  implicit val PrecheckAccuracyEncoder: Encoder[PrecheckAccuracy] = new Encoder[PrecheckAccuracy] {
    override def apply(a: PrecheckAccuracy): Json = stringEncoder.apply(a.entryName)
  }

  implicit val CancellationTypeEncoder: Encoder[CancellationType] = new Encoder[CancellationType] {
    override def apply(a: CancellationType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val CancellationFeeTypeEncoder: Encoder[CancellationFeeType] = new Encoder[CancellationFeeType] {
    override def apply(a: CancellationFeeType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val EnumSearchTypeEncoder: Encoder[SearchType] = new Encoder[SearchType] {
    override def apply(a: SearchType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val DFFeatureFlagEncoder: Encoder[DFFeatureFlag] = new Encoder[DFFeatureFlag] {
    override def apply(a: DFFeatureFlag): Json = stringEncoder.apply(a.entryName)
  }

  implicit val SearchStatusEncoder: Encoder[SearchStatus] = new Encoder[SearchStatus] {
    override def apply(a: SearchStatus): Json = stringEncoder.apply(a.entryName)
  }

  implicit val FriendlinessTypeEncoder: Encoder[FriendlinessType] = new Encoder[FriendlinessType] {
    override def apply(a: FriendlinessType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val AgencyBookingTypeEncoder: Encoder[AgencyBookingType] = new Encoder[AgencyBookingType] {
    override def apply(agencyBookingType: AgencyBookingType): Json =
      stringEncoder.apply(agencyBookingType.entryName)
  }

  implicit val personalizationTypeEncoder: Encoder[PersonalizationType] = new Encoder[PersonalizationType] {
    override def apply(a: PersonalizationType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val AttributeEncoder: Encoder[Attribute] = new Encoder[Attribute] {
    override def apply(a: Attribute): Json = stringEncoder.apply(a.entryName)
  }

  implicit val MatrixExtraDataTypeEncoder: Encoder[MatrixExtraDataType] = new Encoder[MatrixExtraDataType] {
    override def apply(a: MatrixExtraDataType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val FilterKeyEncoder: Encoder[FilterKey] = new Encoder[FilterKey] {
    override def apply(a: FilterKey): Json = stringEncoder.apply(a.entryName)
  }

  implicit val FilterRequestTypeEncoder: Encoder[FilterRequestType] = new Encoder[FilterRequestType] {
    override def apply(a: FilterRequestType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val MatrixGroupTypeEncoder: Encoder[MatrixGroupType] = new Encoder[MatrixGroupType] {
    override def apply(a: MatrixGroupType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val BookingDurationFilterTypeEncoder: Encoder[BookingDurationFilterType] =
    new Encoder[BookingDurationFilterType] {
      override def apply(a: BookingDurationFilterType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val RocketmilesSearchTypeEncoder: Encoder[RocketmilesSearchType] = new Encoder[RocketmilesSearchType] {
    override def apply(a: RocketmilesSearchType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val RocketmilesPublishPriceLogicTypeEncoder: Encoder[RocketmilesPublishPriceLogicType] =
    new Encoder[RocketmilesPublishPriceLogicType] {
      override def apply(a: RocketmilesPublishPriceLogicType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val RocketmilesSortByFieldEncoder: Encoder[RocketmilesSortByField] = new Encoder[RocketmilesSortByField] {
    override def apply(a: RocketmilesSortByField): Json = stringEncoder.apply(a.entryName)
  }

  implicit val StayPackageTypeEncoder: Encoder[StayPackageType] = new Encoder[StayPackageType] {
    override def apply(a: StayPackageType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val TrafficTypeEncoder: Encoder[TrafficType] = new Encoder[TrafficType] {
    override def apply(a: TrafficType): Json = stringEncoder.apply(a.trafficType)
  }

  implicit val NhaProbabilityEncoder: Encoder[NhaProbability] = new Encoder[NhaProbability] {
    override def apply(a: NhaProbability): Json = stringEncoder.apply(a.entryName)
  }

  implicit val LoyaltyRequestTypeEncoder: Encoder[LoyaltyRequestType] = new Encoder[LoyaltyRequestType] {
    override def apply(a: LoyaltyRequestType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val ChildRateTypeEncoder: Encoder[ChildRateType] = new Encoder[ChildRateType] {
    override def apply(a: ChildRateType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val ArrangementEntryEncoder: Encoder[ArrangementEntry] = new Encoder[ArrangementEntry] {
    override def apply(a: ArrangementEntry): Json = stringEncoder.apply(a.entryName)
  }

  implicit val ItemEntryEncoder: Encoder[ItemEntry] = new Encoder[ItemEntry] {
    override def apply(a: ItemEntry): Json = stringEncoder.apply(a.entryName)
  }

  implicit val ProductEntryEncoder: Encoder[ProductEntry] = new Encoder[ProductEntry] {
    override def apply(a: ProductEntry): Json = stringEncoder.apply(a.entryName)
  }

  implicit val DfApplyTypeEncoder: Encoder[DfApplyType] = new Encoder[DfApplyType] {
    override def apply(a: DfApplyType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val DFSuggestedPriceEncoder: Encoder[DFSuggestedPrice] = new Encoder[DFSuggestedPrice] {
    override def apply(a: DFSuggestedPrice): Json = stringEncoder.apply(a.entryName)
  }

  implicit val CommonCampaignTypeEncoder: Encoder[CampaignType] = new Encoder[CampaignType] {
    override def apply(a: CampaignType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val CommonCampaignStatusTypeEncoder: Encoder[com.agoda.papi.enums.campaign.CampaignStatusType] =
    new Encoder[com.agoda.papi.enums.campaign.CampaignStatusType] {
      override def apply(a: com.agoda.papi.enums.campaign.CampaignStatusType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val PromotionCodeTypeEncoder: Encoder[PromotionCodeType] = new Encoder[PromotionCodeType] {
    override def apply(a: PromotionCodeType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val SwapRoomTypeEncoder: Encoder[SwapRoomType] = new Encoder[SwapRoomType] {
    override def apply(a: SwapRoomType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val LoyaltyReasonEncoder: Encoder[LoyaltyReason] = new Encoder[LoyaltyReason] {
    override def apply(a: LoyaltyReason): Json = stringEncoder.apply(a.entryName)
  }

  implicit val LoyaltyTypeEncoder: Encoder[LoyaltyType] = new Encoder[LoyaltyType] {
    override def apply(a: LoyaltyType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val PaymentChargeTypeEncoder: Encoder[PaymentChargeType] = new Encoder[PaymentChargeType] {
    override def apply(a: PaymentChargeType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val dfPaymentModelEncoder: Encoder[DfPaymentModel] = new Encoder[DfPaymentModel] {
    override def apply(a: DfPaymentModel): Json = stringEncoder.apply(a.entryName)
  }

  implicit val ExternalServiceModeEncoder: Encoder[ExternalServiceMode] = new Encoder[ExternalServiceMode] {
    override def apply(a: ExternalServiceMode): Json = stringEncoder.apply(a.entryName)
  }

  implicit val RecommendationTypeEncoder: Encoder[Option[RecommendationType]] =
    new Encoder[Option[RecommendationType]] {
      override def apply(a: Option[RecommendationType]): Json = stringEncoder.apply(a.map(_.entryName).getOrElse(""))
    }

  implicit val RecommendationFilterFieldTypeEncoder: Encoder[RecommendationFilterFieldType] =
    new Encoder[RecommendationFilterFieldType] {
      override def apply(a: RecommendationFilterFieldType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val RecommendationFilterComparisonTypeEncoder: Encoder[RecommendationFilterComparisonType] =
    new Encoder[RecommendationFilterComparisonType] {
      override def apply(a: RecommendationFilterComparisonType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val RequestSearchTypeEncoder: Encoder[request.SearchType] = new Encoder[request.SearchType] {
    override def apply(a: request.SearchType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val CmsDataGroupTypeEncoder: Encoder[transformers.CmsDataGroupType] =
    new Encoder[transformers.CmsDataGroupType] {
      override def apply(a: transformers.CmsDataGroupType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val ChargeTypeEncoder: Encoder[models.pricing.enums.ChargeType] =
    new Encoder[models.pricing.enums.ChargeType] {
      override def apply(a: models.pricing.enums.ChargeType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val PayTypeEncoder: Encoder[models.starfruit.PayType] = new Encoder[models.starfruit.PayType] {
    override def apply(a: models.starfruit.PayType): Json = stringEncoder.apply(a.entryName)
  }

  implicit val PackageProductTypeEncoder: Encoder[models.pricing.enums.PackageProductType] =
    new Encoder[models.pricing.enums.PackageProductType] {
      override def apply(a: models.pricing.enums.PackageProductType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val BenefitUnitEncoder: Encoder[models.pricing.enums.BenefitUnit] =
    new Encoder[models.pricing.enums.BenefitUnit] {
      override def apply(a: models.pricing.enums.BenefitUnit): Json = stringEncoder.apply(a.entryName)
    }

  implicit val BenefitTargetTypeEncoder: Encoder[com.agoda.papi.enums.room.BenefitTargetType] =
    new Encoder[com.agoda.papi.enums.room.BenefitTargetType] {
      override def apply(a: com.agoda.papi.enums.room.BenefitTargetType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val ChargeOptionEncoder: Encoder[models.pricing.enums.ChargeOption] =
    new Encoder[models.pricing.enums.ChargeOption] {
      override def apply(a: models.pricing.enums.ChargeOption): Json = stringEncoder.apply(a.entryName)
    }

  implicit val WhomToPayTypeEncoder: Encoder[Option[WhomToPayType]] =
    new Encoder[Option[WhomToPayType]] {
      override def apply(a: Option[WhomToPayType]): Json =
        stringEncoder.apply(a.map(_.entryName).getOrElse(""))
    }

  implicit val ValueCalculationMethodTypeEncoder: Encoder[Option[ValueCalculationMethodType]] =
    new Encoder[Option[ValueCalculationMethodType]] {
      override def apply(a: Option[ValueCalculationMethodType]): Json =
        stringEncoder.apply(a.map(_.entryName).getOrElse(""))
    }

  implicit val GeoTypeEncoder: Encoder[Option[GeoType]] =
    new Encoder[Option[GeoType]] {
      override def apply(a: Option[GeoType]): Json =
        stringEncoder.apply(a.map(_.entryName).getOrElse(""))
    }

  implicit val ValueMethodTypeEncoder: Encoder[Option[ValueMethodType]] =
    new Encoder[Option[ValueMethodType]] {
      override def apply(a: Option[ValueMethodType]): Json =
        stringEncoder.apply(a.map(_.entryName).getOrElse(""))
    }

  implicit val DiscountTypeEncoder: Encoder[models.pricing.DiscountType] =
    new Encoder[models.pricing.DiscountType] {
      override def apply(a: models.pricing.DiscountType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val DownliftSourceEncoder: Encoder[models.pricing.enums.DownliftSource] =
    new Encoder[models.pricing.enums.DownliftSource] {
      override def apply(a: models.pricing.enums.DownliftSource): Json = stringEncoder.apply(a.entryName)
    }

  implicit val CorTypeFlagEncoder: Encoder[models.pricing.enums.CorTypeFlag] =
    new Encoder[models.pricing.enums.CorTypeFlag] {
      override def apply(a: models.pricing.enums.CorTypeFlag): Json = stringEncoder.apply(a.entryName)
    }

  implicit val CampaignTypeEncoder: Encoder[enumerations.CampaignType] =
    new Encoder[enumerations.CampaignType] {
      override def apply(a: enumerations.CampaignType): Json = stringEncoder.apply(a.entryName)
    }

  implicit val IneligiblePromotionReasonEncoder: Encoder[com.agoda.papi.enums.campaign.IneligiblePromotionReason] =
    new Encoder[com.agoda.papi.enums.campaign.IneligiblePromotionReason] {
      override def apply(a: com.agoda.papi.enums.campaign.IneligiblePromotionReason): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val CampaignDiscountTypeEncoder: Encoder[com.agoda.papi.enums.campaign.CampaignDiscountType] =
    new Encoder[com.agoda.papi.enums.campaign.CampaignDiscountType] {
      override def apply(a: com.agoda.papi.enums.campaign.CampaignDiscountType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val CampaignValidDateTypeEncoder: Encoder[com.agoda.papi.enums.campaign.CampaignValidDateType] =
    new Encoder[com.agoda.papi.enums.campaign.CampaignValidDateType] {
      override def apply(a: com.agoda.papi.enums.campaign.CampaignValidDateType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val RatePlanStatusTypeEncoder: Encoder[models.starfruit.RatePlanStatusType] =
    new Encoder[models.starfruit.RatePlanStatusType] {
      override def apply(a: models.starfruit.RatePlanStatusType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val CartEntryEncoder: Encoder[com.agoda.upi.models.enums.CartEntry] =
    new Encoder[com.agoda.upi.models.enums.CartEntry] {
      override def apply(a: com.agoda.upi.models.enums.CartEntry): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val PassengerEntryEncoder: Encoder[com.agoda.upi.models.enums.PassengerEntry] =
    new Encoder[com.agoda.upi.models.enums.PassengerEntry] {
      override def apply(a: com.agoda.upi.models.enums.PassengerEntry): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val RoomCategoryChangeTypeEncoder: Encoder[models.pricing.enums.RoomCategoryChangeType] =
    new Encoder[models.pricing.enums.RoomCategoryChangeType] {
      override def apply(a: models.pricing.enums.RoomCategoryChangeType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val RoomOfferNameTypeEncoder: Encoder[models.pricing.enums.RoomOfferNameType] =
    new Encoder[models.pricing.enums.RoomOfferNameType] {
      override def apply(a: models.pricing.enums.RoomOfferNameType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val PaxTypeEncoder: Encoder[models.pricing.enums.PaxType] =
    new Encoder[models.pricing.enums.PaxType] {
      override def apply(a: models.pricing.enums.PaxType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val AvailabilityTypeEncoder: Encoder[enumerations.AvailabilityType] =
    new Encoder[enumerations.AvailabilityType] {
      override def apply(a: enumerations.AvailabilityType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val CancellationChargeSettingTypeEncoder: Encoder[com.agoda.papi.enums.room.CancellationChargeSettingType] =
    new Encoder[com.agoda.papi.enums.room.CancellationChargeSettingType] {
      override def apply(a: com.agoda.papi.enums.room.CancellationChargeSettingType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val FireDrillContractTypeEncoder: Encoder[com.agoda.papi.enums.hotel.FireDrillContractType] =
    new Encoder[com.agoda.papi.enums.hotel.FireDrillContractType] {
      override def apply(a: com.agoda.papi.enums.hotel.FireDrillContractType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val SpecialRequestIdEncoder: Encoder[enumerations.SpecialRequestId] =
    new Encoder[enumerations.SpecialRequestId] {
      override def apply(a: enumerations.SpecialRequestId): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val StayTypeEncoder: Encoder[com.agoda.papi.enums.hotel.StayType] =
    new Encoder[com.agoda.papi.enums.hotel.StayType] {
      override def apply(a: com.agoda.papi.enums.hotel.StayType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val TaxTypeEncoder: Encoder[models.pricing.enums.TaxType] =
    new Encoder[models.pricing.enums.TaxType] {
      override def apply(a: models.pricing.enums.TaxType): Json =
        stringEncoder.apply(a.entryName)
    }

  implicit val TaxRegistrationTypeEncoder: Encoder[com.agoda.papi.enums.hotel.TaxRegistrationType] =
    new Encoder[com.agoda.papi.enums.hotel.TaxRegistrationType] {
      override def apply(a: com.agoda.papi.enums.hotel.TaxRegistrationType): Json =
        stringEncoder.apply(a.entryName)
    }

}
