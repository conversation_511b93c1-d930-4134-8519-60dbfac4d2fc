package com.agoda.papi.search.validation.constant

object ValidationFailures {
  val CampaignIdValidationFailure: String = "Campaign ids must be 0 or positive integers"

  val IdsFilterValidationFailure: String =
    "ids for idsFilter must be 0 or positive integers"

  val PageSizeValidationFailure: String =
    "Page size must be between 0 and 100"

  val PageNumberValidationFailure: String =
    "Page number must be greater than zero"

  val ExtraHotelsValidationFailure: String =
    "sending more than 1 extra property is not allowed"

  val MatrixGroupSizeValidationFailure: String =
    "Matrix Group size must be positive"

  val HistogramBinsValidationFailure: String =
    "Number of bins must be positive"

  val HighlyRatedAgodaHomesRangeValidationFailure: Int => String = (maxNumberOfHighlyRatedAgodaHomes: Int) =>
    s"highlyRatedAgodaHomesRequest.numberOfAgodaHomes must be between 0 and $maxNumberOfHighlyRatedAgodaHomes"

  val ExtraAgodaHomesRangeValidationFailure: Int => String = (maxNumberOfExtraAgodaHomes: Int) =>
    s"extraAgodaHomesRequest.numberOfAgodaHomes must be between 0 and $maxNumberOfExtraAgodaHomes"

  val FeaturedLuxuryHotelsRangeValidationFailure: Int => String = (maxNumberOfFeaturedLuxuryHotels: Int) =>
    s"featuredLuxuryHotelsRequest.numberOfHotels must be between 0 and $maxNumberOfFeaturedLuxuryHotels"
}
