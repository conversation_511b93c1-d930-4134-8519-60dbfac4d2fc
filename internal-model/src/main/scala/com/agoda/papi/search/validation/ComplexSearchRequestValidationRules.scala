package com.agoda.core.search.validation

import com.agoda.core.search.models.{ FilterRequest, IdsFilterRequest }
import com.agoda.core.search.models.request.{ BaseRequest, ComplexSearchRequest }
import com.agoda.core.search.models.enumeration.matrix.FilterKeys.ProductType
import com.agoda.papi.search.validation.constant.ValidationFailures.{
  CampaignIdValidationFailure,
  ExtraAgodaHomesRangeValidationFailure,
  ExtraHotelsValidationFailure,
  FeaturedLuxuryHotelsRangeValidationFailure,
  HighlyRatedAgodaHomesRangeValidationFailure,
  HistogramBinsValidationFailure,
  IdsFilterValidationFailure,
  MatrixGroupSizeValidationFailure,
  PageNumberValidationFailure,
  PageSizeValidationFailure
}

object ComplexSearchRequestValidationRules extends ValidationRule[ComplexSearchRequest] {

  override protected def validationRules(complexSearchRequest: ComplexSearchRequest): Option[ValidationFailure] = {

    val basePageValidations = complexSearchRequest.page match {
      case Some(page) if page.pageSize < 0 || page.pageSize > 100 =>
        Some(ValidationFailure(PageSizeValidationFailure))
      case Some(page) if page.pageNumber < 0 =>
        Some(ValidationFailure(PageNumberValidationFailure))
      case _ => None
    }
    val highlyRatedAgodaHomesValidation = complexSearchRequest.highlyRatedAgodaHomesRequest match {
      case Some(highlyRatedRequest) =>
        highlyRatedRequest.numberOfAgodaHomes match {
          case Some(noOfAgodaHomes)
              if noOfAgodaHomes < 0 || noOfAgodaHomes > BaseRequest.MaxNumberOfHighlyRatedAgodaHomes =>
            Some(
              ValidationFailure(
                HighlyRatedAgodaHomesRangeValidationFailure(BaseRequest.MaxNumberOfHighlyRatedAgodaHomes)
              )
            )
          case _ => None
        }
      case _ => None
    }

    val extraAgodaHomesValidation = complexSearchRequest.extraAgodaHomesRequest match {
      case Some(extraHomesRequest) =>
        extraHomesRequest.numberOfAgodaHomes match {
          case Some(noOfAgodaHomes) if noOfAgodaHomes < 0 || noOfAgodaHomes > BaseRequest.MaxNumberOfExtraAgodaHomes =>
            Some(
              ValidationFailure(
                ExtraAgodaHomesRangeValidationFailure(BaseRequest.MaxNumberOfExtraAgodaHomes)
              )
            )
          case _ => None
        }
      case _ => None
    }

    val featuredLuxuryHotelsValidation =
      complexSearchRequest.featuredLuxuryHotelsRequest.flatMap(_.numberOfHotels) match {
        case Some(numberOfHotels)
            if numberOfHotels < 0 || numberOfHotels > BaseRequest.MaxNumberOfFeaturedLuxuryHotels =>
          Some(
            ValidationFailure(
              FeaturedLuxuryHotelsRangeValidationFailure(BaseRequest.MaxNumberOfFeaturedLuxuryHotels)
            )
          )
        case _ => None
      }

    val histogramBins = complexSearchRequest.searchDetailRequest.flatMap(_.priceHistogramBins)
    val histogramValidation = histogramBins.flatMap { h =>
      if (h < 0) {
        Some(ValidationFailure(HistogramBinsValidationFailure))
      } else None
    }

    val matrixGroupRequests = complexSearchRequest.matrixGroup.getOrElse(List.empty)
    val matrixGroupsValidation = matrixGroupRequests.flatMap { m =>
      if (m.size.exists(size => size < 0)) {
        Some(ValidationFailure(MatrixGroupSizeValidationFailure))
      } else None
    }.headOption

    val extraHotelsValidation = complexSearchRequest.extraHotels
      .filter(_.extraHotelIds.size > 1)
      .map(_ => ValidationFailure(ExtraHotelsValidationFailure))
    basePageValidations
      .andThen(extraHotelsValidation)
      .andThen(highlyRatedAgodaHomesValidation)
      .andThen(extraAgodaHomesValidation)
      .andThen(featuredLuxuryHotelsValidation)
      .andThen(histogramValidation)
      .andThen(matrixGroupsValidation)
      .andThen(validateFilters(complexSearchRequest.filterRequest))
  }

  private def validateFilters(filterRequest: Option[FilterRequest]): Option[ValidationFailure] = {
    val idsFilterRequests = filterRequest.flatMap(_.idsFilters).getOrElse(List.empty)
    val idFilterValidations = idsFilterRequests.flatMap { idsFilterRequest =>
      idsFilterRequest match {
        case IdsFilterRequest(ProductType, ids) =>
          if (ids.exists(id => id < -1 || id > Int.MaxValue)) Some(ValidationFailure(IdsFilterValidationFailure))
          else None
        case IdsFilterRequest(_, ids) if ids.exists(id => id < 0 || id > Int.MaxValue) =>
          Some(ValidationFailure(IdsFilterValidationFailure))
        case _ => None
      }
    }.headOption
    val campaignCustomFilters = filterRequest.flatMap(_.customFilters.flatMap(_.campaign))
    val campaignCustomFilterValidation = campaignCustomFilters.flatMap { campaignFilter =>
      if (campaignFilter.campaignId < 0 || campaignFilter.campaignId > Int.MaxValue) {
        Some(ValidationFailure(CampaignIdValidationFailure))
      } else None
    }
    idFilterValidations.andThen(campaignCustomFilterValidation)
  }

}
