package com.agoda.papi.search.internalmodel.serializer.decoder

import cats.syntax.either._
import com.agoda.commons.traffic.TrafficType
import com.agoda.core.search.models.enumeration.matrix.{
  FilterKey,
  Filter<PERSON>eys,
  FilterRequestType,
  FilterRequestTypes,
  MatrixExtraDataType,
  MatrixExtraDataTypes,
  MatrixGroupType,
  MatrixGroupTypes
}
import com.agoda.core.search.models.{ CancellationFeeType, CancellationFeeTypes, CancellationType, CancellationTypes }
import com.agoda.core.search.models.enumeration.{
  AgencyBookingType,
  AgencyBookingTypes,
  Attribute,
  Attributes,
  BookingDurationFilterType,
  BookingDurationFilterTypes,
  FriendlinessType,
  PersonalizationType,
  PersonalizationTypes,
  PrecheckAccuracies,
  PrecheckAccuracy,
  RocketmilesPublishPriceLogicType,
  RocketmilesPublishPriceLogicTypes,
  RocketmilesSearchType,
  RocketmilesSearchTypes,
  RocketmilesSortByField,
  RocketmilesSortBy<PERSON>ields,
  SearchStatus,
  SearchStatuses,
  SearchType,
  SearchTypes,
  SortField,
  SortFields,
  SortOrder,
  SortOrders,
  TravellerType,
  TravellerTypes
}
import com.agoda.core.search.models.request.{ LoyaltyRequestType, LoyaltyRequestTypes }
import com.agoda.papi.search.internalmodel.enumeration._
import com.agoda.papi.search.internalmodel.serializer.encoder.EnumEncoders.stringEncoder
import com.agoda.everest.model.SponsoredType
import com.agoda.everest.model.enum.SponsoredType
import com.agoda.papi.enums.user.{ NhaProbabilities, NhaProbability }
import com.agoda.papi.enums.hotel.{ PaymentModel, StayPackageType, StayPackageTypes }
import com.agoda.papi.enums.request.{ FeatureFlag, RequestedPrice }
import com.agoda.papi.enums.room.{
  ApplyType,
  ChildRateType,
  GeoType,
  ValueCalculationMethodType,
  ValueMethodType,
  WhomToPayType
}
import com.agoda.upi.models.enums.{
  ArrangementEntries,
  ArrangementEntry,
  ItemEntries,
  ItemEntry,
  ProductEntries,
  ProductEntry
}
import io.circe.Decoder.Result
import io.circe.{ Decoder, DecodingFailure, Encoder, HCursor, Json }
import api.request.{ FeatureFlag => DFFeatureFlag, PaymentChargeTypes }
import com.agoda.papi.enums.campaign.{
  CampaignStatusType,
  CampaignStatusTypes,
  CampaignType,
  CampaignTypes,
  PromotionCodeType
}
import enumerations.{
  RecommendationFilterComparisonType,
  RecommendationFilterComparisonTypes,
  RecommendationFilterFieldType,
  RecommendationFilterFieldTypes,
  RecommendationType,
  RecommendationTypes
}
import models.pricing.enums.{
  ApplyType => DfApplyType,
  ApplyTypes => DfApplyTypes,
  M150TransparencyVersionType,
  PaymentModel => DfPaymentModel,
  PaymentModels => DfPaymentModels,
  RequestedPrice => DFRequestedPrice,
  SwapRoomType,
  SwapRoomTypes
}
import models.starfruit.{ LoyaltyReason, LoyaltyReasons, LoyaltyType, LoyaltyTypes, SuggestedPrice => DFSuggestedPrice }
import request.{ ExternalServiceMode, ExternalServiceModes }

import scala.util.Try

object EnumDecoders {

  import KeyDecoders._

  private val stringDecoder  = implicitly[Decoder[String]]
  private val integerDecoder = implicitly[Decoder[Int]]

  implicit val TravellerTypeDecoder: Decoder[TravellerType] = new Decoder[TravellerType] {

    final def apply(c: HCursor): Result[TravellerType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = TravellerTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $TravellerTypes", c.history))
      }
    }

  }

  implicit val SortFieldDecoder: Decoder[SortField] = new Decoder[SortField] {

    final def apply(c: HCursor): Result[SortField] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = SortFields.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $SortFields", c.history))
      }
    }

  }

  implicit val SortOrderDecoder: Decoder[SortOrder] = new Decoder[SortOrder] {

    final def apply(c: HCursor): Result[SortOrder] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = SortOrders.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $SortOrders", c.history))
      }
    }

  }

  implicit val ApplyTypeDecoder: Decoder[ApplyType] = new Decoder[ApplyType] {

    final def apply(c: HCursor): Result[ApplyType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = ApplyType.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $ApplyType", c.history))
      }
    }

  }

  implicit val RequestedPriceDecoder: Decoder[RequestedPrice] = new Decoder[RequestedPrice] {

    final def apply(c: HCursor): Result[RequestedPrice] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = RequestedPrice.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $RequestedPrice", c.history))
      }
    }

  }

  implicit val NhaProbabilityDecoder: Decoder[NhaProbability] = new Decoder[NhaProbability] {

    final def apply(c: HCursor): Result[NhaProbability] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = NhaProbabilities.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $NhaProbabilities", c.history))
      }
    }

  }

  implicit val PrecheckAccuracyDecoder: Decoder[PrecheckAccuracy] = new Decoder[PrecheckAccuracy] {

    final def apply(c: HCursor): Result[PrecheckAccuracy] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = PrecheckAccuracies.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $PrecheckAccuracies", c.history))
      }
    }

  }

  implicit val DFFeatureFlagDecoder: Decoder[DFFeatureFlag] = new Decoder[DFFeatureFlag] {

    final def apply(c: HCursor): Result[DFFeatureFlag] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = DFFeatureFlag.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $DFFeatureFlag", c.history))
      }
    }

  }

  implicit val FeatureFlagDecoder: Decoder[FeatureFlag] = new Decoder[FeatureFlag] {

    final def apply(c: HCursor): Result[FeatureFlag] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = FeatureFlag.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $FeatureFlag", c.history))
      }
    }

  }

  implicit val SearchTypeDecoder: Decoder[SearchType] = new Decoder[SearchType] {

    final def apply(c: HCursor): Result[SearchType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = SearchTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $SearchTypes", c.history))
      }
    }

  }

  implicit val CancellationTypeDecoder: Decoder[CancellationType] = new Decoder[CancellationType] {

    final def apply(c: HCursor): Result[CancellationType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = CancellationTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $CancellationTypes", c.history))
      }
    }

  }

  implicit val CancellationFeeTypeDecoder: Decoder[CancellationFeeType] = new Decoder[CancellationFeeType] {

    final def apply(c: HCursor): Result[CancellationFeeType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = CancellationFeeTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $CancellationFeeTypes", c.history))
      }
    }

  }

  implicit val PaymentModelDecoder: Decoder[PaymentModel] = new Decoder[PaymentModel] {

    final def apply(c: HCursor): Result[PaymentModel] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = PaymentModel.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $PaymentModel", c.history))
      }
    }

  }

  implicit val M150TransparencyVersionDecoder: Decoder[M150TransparencyVersionType] =
    new Decoder[M150TransparencyVersionType] {

      final def apply(c: HCursor): Result[M150TransparencyVersionType] = stringDecoder.apply(c).flatMap { s =>
        val maybeMember = M150TransparencyVersionType.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _ => Left(DecodingFailure(s"$s' is not a member of enum $M150TransparencyVersionType", c.history))
        }
      }

    }

  implicit val SponsoredTypeDecoder: Decoder[SponsoredType] = new Decoder[SponsoredType] {

    final def apply(c: HCursor): Result[SponsoredType] = integerDecoder.apply(c).flatMap { i =>
      val sponsoredType = Option(SponsoredType(i))

      sponsoredType match {
        case Some(sponsoredType) => Right(sponsoredType)
        case _                   => Left(DecodingFailure(s"$i' is not a member of enum $SponsoredType", c.history))
      }
    }

  }

  implicit val SearchStatusDecoder: Decoder[SearchStatus] = new Decoder[SearchStatus] {

    final def apply(c: HCursor): Result[SearchStatus] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = SearchStatuses.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum SearchStatus", c.history))
      }
    }

  }

  implicit val FriendlinessTypeDecoder: Decoder[FriendlinessType] = new Decoder[FriendlinessType] {

    final def apply(c: HCursor): Result[FriendlinessType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = FriendlinessType.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum FriendlinessType", c.history))
      }
    }

  }

  implicit val AgencyBookingTypeDecoder: Decoder[AgencyBookingType] = new Decoder[AgencyBookingType] {

    final def apply(c: HCursor): Result[AgencyBookingType] = stringDecoder.apply(c).flatMap { s =>
      val agencyBookingType = AgencyBookingTypes.withNameOption(s)
      agencyBookingType match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum AgencyBookingType", c.history))
      }
    }

  }

  implicit val ChildRateTypeDecoder: Decoder[ChildRateType] = new Decoder[ChildRateType] {

    final def apply(c: HCursor): Result[ChildRateType] = stringDecoder.apply(c).flatMap { s =>
      val childRateType = ChildRateType.withNameOption(s)
      childRateType match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum AgencyBookingType", c.history))
      }
    }

  }

  implicit val PersonalizationTypeDecoder: Decoder[PersonalizationType] = new Decoder[PersonalizationType] {

    final def apply(c: HCursor): Result[PersonalizationType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = PersonalizationTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $PersonalizationTypes", c.history))
      }
    }

  }

  implicit val AttributeDecoder: Decoder[Attribute] = new Decoder[Attribute] {

    final def apply(c: HCursor): Result[Attribute] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = Attributes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $Attributes", c.history))
      }
    }

  }

  implicit val FilterKeyDecoder: Decoder[FilterKey] = new Decoder[FilterKey] {

    final def apply(c: HCursor): Result[FilterKey] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = FilterKeys.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $Attributes", c.history))
      }
    }

  }

  implicit val MatrixGroupTypeDecoder: Decoder[MatrixGroupType] = new Decoder[MatrixGroupType] {

    final def apply(c: HCursor): Result[MatrixGroupType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = MatrixGroupTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $Attributes", c.history))
      }
    }

  }

  implicit val BookingDurationFilterTypeDecoder: Decoder[BookingDurationFilterType] =
    new Decoder[BookingDurationFilterType] {

      final def apply(c: HCursor): Result[BookingDurationFilterType] = stringDecoder.apply(c).flatMap { s =>
        val maybeMember = BookingDurationFilterTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _ => Left(DecodingFailure(s"$s' is not a member of enum $BookingDurationFilterTypes", c.history))
        }
      }

    }

  implicit val RocketmilesSearchTypeDecoder: Decoder[RocketmilesSearchType] = new Decoder[RocketmilesSearchType] {

    final def apply(c: HCursor): Result[RocketmilesSearchType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = RocketmilesSearchTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $RocketmilesSearchTypes", c.history))
      }
    }

  }

  implicit val RocketmilesPublishPriceLogicTypeDecoder: Decoder[RocketmilesPublishPriceLogicType] =
    new Decoder[RocketmilesPublishPriceLogicType] {

      final def apply(c: HCursor): Result[RocketmilesPublishPriceLogicType] = stringDecoder.apply(c).flatMap { s =>
        val maybeMember = RocketmilesPublishPriceLogicTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _ => Left(DecodingFailure(s"$s' is not a member of enum $RocketmilesPublishPriceLogicTypes", c.history))
        }
      }

    }

  implicit val RocketmilesSortByFieldDecoder: Decoder[RocketmilesSortByField] = new Decoder[RocketmilesSortByField] {

    final def apply(c: HCursor): Result[RocketmilesSortByField] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = RocketmilesSortByFields.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $RocketmilesSortByFields", c.history))
      }
    }

  }

  implicit val StayPackageTypeDecoder: Decoder[StayPackageType] = new Decoder[StayPackageType] {

    final def apply(c: HCursor): Result[StayPackageType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = StayPackageTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $StayPackageTypes", c.history))
      }
    }

  }

  implicit val TrafficTypeDecoder: Decoder[TrafficType] = new Decoder[TrafficType] {

    final def apply(c: HCursor): Result[TrafficType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = Try(TrafficType.getByType(s)).toOption
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $StayPackageTypes", c.history))
      }
    }

  }

  implicit val MatrixExtraDataTypeDecoder: Decoder[MatrixExtraDataType] = new Decoder[MatrixExtraDataType] {

    final def apply(c: HCursor): Result[MatrixExtraDataType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = MatrixExtraDataTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $MatrixExtraDataTypes", c.history))
      }
    }

  }

  implicit val FilterRequestTypeDecoder: Decoder[FilterRequestType] = new Decoder[FilterRequestType] {

    final def apply(c: HCursor): Result[FilterRequestType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = FilterRequestTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $FilterRequestTypes", c.history))
      }
    }

  }

  implicit val LoyaltySearchTypeDecoder: Decoder[LoyaltyRequestType] = new Decoder[LoyaltyRequestType] {

    final def apply(c: HCursor): Result[LoyaltyRequestType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = LoyaltyRequestTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s' is not a member of enum $LoyaltyRequestTypes", c.history))
      }
    }

  }

  implicit val ArrangementEntryDecoder: Decoder[ArrangementEntry] = new Decoder[ArrangementEntry] {

    final def apply(c: HCursor): Result[ArrangementEntry] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = ArrangementEntries.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $ArrangementEntries", c.history))
      }
    }

  }

  implicit val ItemEntryEntryDecoder: Decoder[ItemEntry] = new Decoder[ItemEntry] {

    final def apply(c: HCursor): Result[ItemEntry] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = ItemEntries.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $ItemEntries", c.history))
      }
    }

  }

  implicit val ProductEntryEntryDecoder: Decoder[ProductEntry] = new Decoder[ProductEntry] {

    final def apply(c: HCursor): Result[ProductEntry] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = ProductEntries.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $ProductEntries", c.history))
      }
    }

  }

  implicit val DfApplyTypeDecoder: Decoder[DfApplyType] = new Decoder[DfApplyType] {

    final def apply(c: HCursor): Result[DfApplyType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = DfApplyTypes.withNameInsensitiveOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $DfApplyTypes", c.history))
      }
    }

  }

  implicit val DFSuggestedPriceDecoder: Decoder[DFSuggestedPrice] = new Decoder[DFSuggestedPrice] {

    final def apply(c: HCursor): Result[DFSuggestedPrice] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = DFSuggestedPrice.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $DFSuggestedPrice", c.history))
      }
    }

  }

  implicit val DFRequestedPriceDecoder: Decoder[DFRequestedPrice] = new Decoder[DFRequestedPrice] {

    final def apply(c: HCursor): Result[DFRequestedPrice] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = DFRequestedPrice.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $DFRequestedPrice", c.history))
      }
    }

  }

  implicit val CommonCampaignTypeDecoder: Decoder[CampaignType] = new Decoder[CampaignType] {

    final def apply(c: HCursor): Result[CampaignType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = CampaignTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $CampaignTypes", c.history))
      }
    }

  }

  implicit val CommonCampaignStatusTypeDecoder: Decoder[CampaignStatusType] = new Decoder[CampaignStatusType] {

    final def apply(c: HCursor): Result[CampaignStatusType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = CampaignStatusTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $CampaignStatusTypes", c.history))
      }
    }

  }

  implicit val PromotionCodeTypeDecoder: Decoder[PromotionCodeType] = new Decoder[PromotionCodeType] {

    final def apply(c: HCursor): Result[PromotionCodeType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = PromotionCodeType.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $PromotionCodeType", c.history))
      }
    }

  }

  implicit val SwapRoomTypeDecoder: Decoder[SwapRoomType] = new Decoder[SwapRoomType] {

    final def apply(c: HCursor): Result[SwapRoomType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = SwapRoomTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $SwapRoomTypes", c.history))
      }
    }

  }

  implicit val LoyaltyReasonDecoder: Decoder[LoyaltyReason] = new Decoder[LoyaltyReason] {

    final def apply(c: HCursor): Result[LoyaltyReason] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = LoyaltyReasons.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $LoyaltyReasons", c.history))
      }
    }

  }

  implicit val LoyaltyTypeDecoder: Decoder[LoyaltyType] = new Decoder[LoyaltyType] {

    final def apply(c: HCursor): Result[LoyaltyType] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = LoyaltyTypes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $LoyaltyTypes", c.history))
      }
    }

  }

  implicit val PaymentChargeTypeDecoder: Decoder[api.request.PaymentChargeType] =
    new Decoder[api.request.PaymentChargeType] {

      final def apply(c: HCursor): Result[api.request.PaymentChargeType] = stringDecoder.apply(c).flatMap { s =>
        val maybeMember = PaymentChargeTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _            => Left(DecodingFailure(s"$s is not a member of enum $PaymentChargeTypes", c.history))
        }
      }

    }

  implicit val DFPaymentModelsDecoder: Decoder[DfPaymentModel] = new Decoder[DfPaymentModel] {

    final def apply(c: HCursor): Result[DfPaymentModel] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = DfPaymentModels.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $DfPaymentModels", c.history))
      }
    }

  }

  implicit val ExternalServiceModeDecoder: Decoder[ExternalServiceMode] = new Decoder[ExternalServiceMode] {

    final def apply(c: HCursor): Result[ExternalServiceMode] = stringDecoder.apply(c).flatMap { s =>
      val maybeMember = ExternalServiceModes.withNameOption(s)
      maybeMember match {
        case Some(member) => Right(member)
        case _            => Left(DecodingFailure(s"$s is not a member of enum $ExternalServiceModes", c.history))
      }
    }

  }

  implicit val RecommendationTypeDecoder: Decoder[RecommendationType] =
    new Decoder[RecommendationType] {

      final def apply(c: HCursor): Result[RecommendationType] = stringDecoder.apply(c).flatMap { s =>
        val maybeMember = RecommendationTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _            => Left(DecodingFailure(s"$s is not a member of enum $RecommendationTypes", c.history))
        }
      }

    }

  implicit val RecommendationFilterFieldTypeDecoder: Decoder[RecommendationFilterFieldType] =
    new Decoder[RecommendationFilterFieldType] {

      final def apply(c: HCursor): Result[RecommendationFilterFieldType] = stringDecoder.apply(c).flatMap { s =>
        val maybeMember = RecommendationFilterFieldTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _ => Left(DecodingFailure(s"$s is not a member of enum $RecommendationFilterFieldTypes", c.history))
        }
      }

    }

  implicit val RecommendationFilterComparisonTypeDecoder: Decoder[RecommendationFilterComparisonType] =
    new Decoder[RecommendationFilterComparisonType] {

      final def apply(c: HCursor): Result[RecommendationFilterComparisonType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = RecommendationFilterComparisonTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(DecodingFailure(s"$s is not a member of enum $RecommendationFilterComparisonTypes", c.history))
          }
        }

    }

  implicit val RequestSearchTypeDecoder: Decoder[request.SearchType] = new Decoder[request.SearchType] {

    final def apply(c: HCursor): Result[request.SearchType] =
      stringDecoder.apply(c).flatMap { s =>
        val maybeMember = request.SearchTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _ =>
            Left(DecodingFailure(s"$s is not a member of enum ${request.SearchTypes}", c.history))
        }
      }

  }

  implicit val CmsDataGroupTypeDecoder: Decoder[transformers.CmsDataGroupType] =
    new Decoder[transformers.CmsDataGroupType] {

      final def apply(c: HCursor): Result[transformers.CmsDataGroupType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = transformers.CmsDataGroupTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(DecodingFailure(s"$s is not a member of enum ${transformers.CmsDataGroupTypes}", c.history))
          }
        }

    }

  implicit val ChargeTypeDecoder: Decoder[models.pricing.enums.ChargeType] =
    new Decoder[models.pricing.enums.ChargeType] {

      final def apply(c: HCursor): Result[models.pricing.enums.ChargeType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.ChargeTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(DecodingFailure(s"$s is not a member of enum ${models.pricing.enums.ChargeTypes}", c.history))
          }
        }

    }

  implicit val PayTypeDecoder: Decoder[models.starfruit.PayType] = new Decoder[models.starfruit.PayType] {

    final def apply(c: HCursor): Result[models.starfruit.PayType] =
      stringDecoder.apply(c).flatMap { s =>
        val maybeMember = models.starfruit.PayTypes.withNameOption(s)
        maybeMember match {
          case Some(member) => Right(member)
          case _ =>
            Left(DecodingFailure(s"$s is not a member of enum ${models.starfruit.PayTypes}", c.history))
        }
      }

  }

  implicit val PackageProductTypeDecoder: Decoder[models.pricing.enums.PackageProductType] =
    new Decoder[models.pricing.enums.PackageProductType] {

      final def apply(c: HCursor): Result[models.pricing.enums.PackageProductType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.PackageProductTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.pricing.enums.PackageProductTypes}", c.history)
              )
          }
        }

    }

  implicit val BenefitUnitDecoder: Decoder[models.pricing.enums.BenefitUnit] =
    new Decoder[models.pricing.enums.BenefitUnit] {

      final def apply(c: HCursor): Result[models.pricing.enums.BenefitUnit] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.BenefitUnits.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.pricing.enums.BenefitUnits}", c.history)
              )
          }
        }

    }

  implicit val BenefitTargetTypeDecoder: Decoder[com.agoda.papi.enums.room.BenefitTargetType] =
    new Decoder[com.agoda.papi.enums.room.BenefitTargetType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.room.BenefitTargetType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.room.BenefitTargetTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.room.BenefitTargetTypes}",
                  c.history
                )
              )
          }
        }

    }

  implicit val ChargeOptionDecoder: Decoder[models.pricing.enums.ChargeOption] =
    new Decoder[models.pricing.enums.ChargeOption] {

      final def apply(c: HCursor): Result[models.pricing.enums.ChargeOption] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.ChargeOptions.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.pricing.enums.ChargeOptions}", c.history)
              )
          }
        }

    }

  implicit val WhomToPayTypeDecoder: Decoder[WhomToPayType] =
    new Decoder[WhomToPayType] {

      final def apply(c: HCursor): Result[WhomToPayType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = WhomToPayType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum $WhomToPayType", c.history)
              )
          }
        }

    }

  implicit val GeoTypeDecoder: Decoder[GeoType] =
    new Decoder[GeoType] {

      final def apply(c: HCursor): Result[GeoType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = GeoType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum $GeoType", c.history)
              )
          }
        }

    }

  implicit val ValueCalculationMethodTypeDecoder: Decoder[ValueCalculationMethodType] =
    new Decoder[ValueCalculationMethodType] {

      final def apply(c: HCursor): Result[ValueCalculationMethodType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = ValueCalculationMethodType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum $ValueCalculationMethodType", c.history)
              )
          }
        }

    }

  implicit val ValueMethodTypeDecoder: Decoder[ValueMethodType] =
    new Decoder[ValueMethodType] {

      final def apply(c: HCursor): Result[ValueMethodType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = ValueMethodType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum $ValueMethodType", c.history)
              )
          }
        }

    }

  implicit val DiscountTypeDecoder: Decoder[models.pricing.DiscountType] =
    new Decoder[models.pricing.DiscountType] {

      final def apply(c: HCursor): Result[models.pricing.DiscountType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.DiscountTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.pricing.DiscountTypes}", c.history)
              )
          }
        }

    }

  implicit val DownliftSourceDecoder: Decoder[models.pricing.enums.DownliftSource] =
    new Decoder[models.pricing.enums.DownliftSource] {

      final def apply(c: HCursor): Result[models.pricing.enums.DownliftSource] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.DownliftSources.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.pricing.enums.DownliftSources}", c.history)
              )
          }
        }

    }

  implicit val CorTypeFlagDecoder: Decoder[models.pricing.enums.CorTypeFlag] =
    new Decoder[models.pricing.enums.CorTypeFlag] {

      final def apply(c: HCursor): Result[models.pricing.enums.CorTypeFlag] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.CorTypeFlags.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.pricing.enums.CorTypeFlags}", c.history)
              )
          }
        }

    }

  implicit val CampaignTypeDecoder: Decoder[enumerations.CampaignType] =
    new Decoder[enumerations.CampaignType] {

      final def apply(c: HCursor): Result[enumerations.CampaignType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = enumerations.CampaignTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${enumerations.CampaignTypes}", c.history)
              )
          }
        }

    }

  implicit val IneligiblePromotionReasonDecoder: Decoder[com.agoda.papi.enums.campaign.IneligiblePromotionReason] =
    new Decoder[com.agoda.papi.enums.campaign.IneligiblePromotionReason] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.campaign.IneligiblePromotionReason] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.campaign.InelegiblePromotionReasons.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.campaign.InelegiblePromotionReasons}",
                  c.history
                )
              )
          }
        }

    }

  implicit val CampaignDiscountTypeDecoder: Decoder[com.agoda.papi.enums.campaign.CampaignDiscountType] =
    new Decoder[com.agoda.papi.enums.campaign.CampaignDiscountType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.campaign.CampaignDiscountType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.campaign.CampaignDiscountTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.campaign.CampaignDiscountTypes}",
                  c.history
                )
              )
          }
        }

    }

  implicit val CampaignValidDateTypeDecoder: Decoder[com.agoda.papi.enums.campaign.CampaignValidDateType] =
    new Decoder[com.agoda.papi.enums.campaign.CampaignValidDateType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.campaign.CampaignValidDateType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.campaign.CampaignValidDateType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.campaign.CampaignValidDateType}",
                  c.history
                )
              )
          }
        }

    }

  implicit val RatePlanStatusTypeDecoder: Decoder[models.starfruit.RatePlanStatusType] =
    new Decoder[models.starfruit.RatePlanStatusType] {

      final def apply(c: HCursor): Result[models.starfruit.RatePlanStatusType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.starfruit.RatePlanStatusTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(s"$s is not a member of enum ${models.starfruit.RatePlanStatusTypes}", c.history)
              )
          }
        }

    }

  implicit val CartEntryDecoder: Decoder[com.agoda.upi.models.enums.CartEntry] =
    new Decoder[com.agoda.upi.models.enums.CartEntry] {

      final def apply(c: HCursor): Result[com.agoda.upi.models.enums.CartEntry] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.upi.models.enums.CartEntries.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.upi.models.enums.CartEntries}",
                  c.history
                )
              )
          }
        }

    }

  implicit val PassengerEntryDecoder: Decoder[com.agoda.upi.models.enums.PassengerEntry] =
    new Decoder[com.agoda.upi.models.enums.PassengerEntry] {

      final def apply(c: HCursor): Result[com.agoda.upi.models.enums.PassengerEntry] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.upi.models.enums.PassengerEntries.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.upi.models.enums.PassengerEntries}",
                  c.history
                )
              )
          }
        }

    }

  implicit val RoomCategoryChangeTypeDecoder: Decoder[models.pricing.enums.RoomCategoryChangeType] =
    new Decoder[models.pricing.enums.RoomCategoryChangeType] {

      final def apply(c: HCursor): Result[models.pricing.enums.RoomCategoryChangeType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.RoomCategoryChangeType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${models.pricing.enums.RoomCategoryChangeType}",
                  c.history
                )
              )
          }
        }

    }

  implicit val RoomOfferNameTypeDecoder: Decoder[models.pricing.enums.RoomOfferNameType] =
    new Decoder[models.pricing.enums.RoomOfferNameType] {

      final def apply(c: HCursor): Result[models.pricing.enums.RoomOfferNameType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.RoomOfferNameType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${models.pricing.enums.RoomOfferNameType}",
                  c.history
                )
              )
          }
        }

    }

  implicit val PaxTypeDecoder: Decoder[models.pricing.enums.PaxType] =
    new Decoder[models.pricing.enums.PaxType] {

      final def apply(c: HCursor): Result[models.pricing.enums.PaxType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.PaxTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${models.pricing.enums.PaxTypes}",
                  c.history
                )
              )
          }
        }

    }

  implicit val AvailabilityTypeDecoder: Decoder[enumerations.AvailabilityType] =
    new Decoder[enumerations.AvailabilityType] {

      final def apply(c: HCursor): Result[enumerations.AvailabilityType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = enumerations.AvailabilityTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${enumerations.AvailabilityTypes}",
                  c.history
                )
              )
          }
        }

    }

  implicit val CancellationChargeSettingTypeDecoder: Decoder[com.agoda.papi.enums.room.CancellationChargeSettingType] =
    new Decoder[com.agoda.papi.enums.room.CancellationChargeSettingType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.room.CancellationChargeSettingType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.room.CancellationChargeSettingType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.room.CancellationChargeSettingType}",
                  c.history
                )
              )
          }
        }

    }

  implicit val FireDrillContractTypeDecoder: Decoder[com.agoda.papi.enums.hotel.FireDrillContractType] =
    new Decoder[com.agoda.papi.enums.hotel.FireDrillContractType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.hotel.FireDrillContractType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.hotel.FireDrillContractType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.hotel.FireDrillContractType}",
                  c.history
                )
              )
          }
        }

    }

  implicit val SpecialRequestIdDecoder: Decoder[enumerations.SpecialRequestId] =
    new Decoder[enumerations.SpecialRequestId] {

      final def apply(c: HCursor): Result[enumerations.SpecialRequestId] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = enumerations.SpecialRequestIds.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${enumerations.SpecialRequestIds}",
                  c.history
                )
              )
          }
        }

    }

  implicit val StayTypeDecoder: Decoder[com.agoda.papi.enums.hotel.StayType] =
    new Decoder[com.agoda.papi.enums.hotel.StayType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.hotel.StayType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.hotel.StayTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.hotel.StayTypes}",
                  c.history
                )
              )
          }
        }

    }

  implicit val TaxTypeDecoder: Decoder[models.pricing.enums.TaxType] =
    new Decoder[models.pricing.enums.TaxType] {

      final def apply(c: HCursor): Result[models.pricing.enums.TaxType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = models.pricing.enums.TaxTypes.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${models.pricing.enums.TaxTypes}",
                  c.history
                )
              )
          }
        }

    }

  implicit val TaxRegistrationTypeDecoder: Decoder[com.agoda.papi.enums.hotel.TaxRegistrationType] =
    new Decoder[com.agoda.papi.enums.hotel.TaxRegistrationType] {

      final def apply(c: HCursor): Result[com.agoda.papi.enums.hotel.TaxRegistrationType] =
        stringDecoder.apply(c).flatMap { s =>
          val maybeMember = com.agoda.papi.enums.hotel.TaxRegistrationType.withNameOption(s)
          maybeMember match {
            case Some(member) => Right(member)
            case _ =>
              Left(
                DecodingFailure(
                  s"$s is not a member of enum ${com.agoda.papi.enums.hotel.TaxRegistrationType}",
                  c.history
                )
              )
          }
        }

    }

}
