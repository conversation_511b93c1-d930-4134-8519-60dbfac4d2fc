stryker4s {
    base-dir: "internal-model"
    reporters: ["console", "html", "json"]
    excluded-mutations: ["StringLiteral"]
    mutate: [
        "!src/main/scala/com/agoda/core/search/models/CancellationPoliciesResult.scala",
        "!src/main/scala/com/agoda/core/search/models/serializer/decoder/EnumDecoders.scala",
        "!src/main/scala/com/agoda/core/search/models/serializer/encoder/EnumEncoders.scala",
        "!src/main/scala/com/agoda/core/search/models/request/LoyaltyRequestType.scala",
        "!src/main/scala/com/agoda/core/search/models/request/BaseSearchRequest.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/PropertyInformationForEnrichment.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/serializer/decoder/RequestDecoders.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/serializer/PAPIHotelPricingSerializer.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/serializer/decoder/ResponseDecoders.scala",
        "!src/main/scala/com/agoda/papi/search/validation/RectangleSearchValidationRules.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/JMapBuilder.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/configuration/BNPLSettings.scala",
        "!src/main/scala/com/agoda/papi/search/internalmodel/pricing/PAPIHotelPricing.scala"
        "src/main/**"
    ]
}