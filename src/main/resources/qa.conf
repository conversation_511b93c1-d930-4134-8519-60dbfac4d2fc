include "qa-debug-common.conf"
include "qa-debug-cluster.conf" //override some value from dev
include "config/application/graphql-settings.conf"
include "config/application/circuit-breaker-settings.conf"
include "config/external-settings/shard/qa-shard.conf"
include "config/application/warmup.conf"
include "config/application/ag-jdbc-cache-settings.conf"

akka.http.server {
  idle-timeout = 60 s
  request-timeout = 20200 milliseconds
}

ag-logging.metrics {
  enabled = false
  application = "search"
  prefix = "propertyapi"
}

reporting {
  enabled = false
  time.interval = 1s
  falcon.url = "http://sgp-gc-fe.agoda.local/measurements"
  jvm.enabled = true
  keyspace = "search"
  prefix = "propertyapi"
  percentile = true
}

hadoop {
  enabled = true
  url = "http://sgp-gc-fe.agoda.local/messaging"
  interval = 30 seconds
  maxQueue = 20
}

elastic-filter-size {
  maxNumberOfRecords = 50
  bestsellerResultSize = 100
}

clusterService{
  dragonfruitVersion {
    router = routerDFVersion
    timeout = 1 second
    retries = 2
  }
  contentVersion {
    router = routerContentVersion
    timeout = 1 second
    retries = 2
  }
  dragonfruitV2{
    router = routerDFv2
    timeout = 10 second
    retries = 2
  }
  features{
    router = routerFeature
    timeout = 5 second
    retries = 2
  }
  images{
    router = routerImage
    timeout = 5 second
    retries = 2
  }
  hotelinfo{
    router = routerHotelInfo
    timeout = 5 second
    retries = 2
  }
  reviewScore{
    router = routerReviewScore
    timeout = 5 second
    retries = 2
  }
  reviewTotal{
    router = routerReviewTotal
    timeout = 5 second
    retries = 2
  }
  reviewSnippet{
    router = routerReviewSnippet
    timeout = 5 second
    retries = 2
  }
  reviewComment{
    router = routerReviewComment
    timeout = 5 second
    retries = 2
  }
  reviewPositiveMentions{
    router = routerReviewPositiveMentions
    timeout = 5 second
    retries = 2
  }
  roomDetail{
    router = routerRoomDetail
    timeout = 1 second
    retries = 2
  }
  personalizedInformation{
    router = routerPersonalizedInformation
    timeout = 1 second
    retries = 2
  }
  nonHotelAccommodation{
    router = routerNonHotelAccommodation
    timeout = 5 second
    retries = 2
  }
  checkInOut{
    router = routerCheckInOut
    timeout = 5 second
    retries = 2
  }
  highlight{
    router = routerHighlight
    timeout = 5 second
    retries = 2
  }
  synopsis{
    router = routerSynopsis
    timeout = 5 second
    retries = 2
  }
  rateCategories {
    router = routerRateCategories
    timeout = 5 second
    retries = 2
  }
  rateCategory {
    router = routerRateCategory
    timeout = 5 second
    retries = 2
  }
  bathInformation {
    router = routerBathInformation
    timeout = 5 second
    retries = 2
  }
  transportationInformation {
    router = routerTransportationInformation
    timeout = 5 second
    retries = 2
  }
}

externalServicesRequestFilter {
  dragonfruit {
    lengthOfStayLimit = 31
  }
}

experiment {
  url = "http://bk-qatvm-1049:8080/messaging"
  platform_id = 3
}

com.couchbase.timeout {
  connect=10s
}

couchbase-nodes.a = ["hk-qaagmcc-2b01", "hk-qaagmcc-2b02", "hk-qaagmcc-2b03", "hk-qaagmcc-2b04"]

ag-cache.couchbase {
  multicache{
    weighted-roundrobin{
      max-weight = 10000
      increment = 2
      decrement = 2
    }
    clusters = [
      {
        id = "A"
        settings = {
          include "/config/external-settings/couchbase/couchbase-bucket-common.conf"
          nodes = ${couchbase-nodes.a}
          bucketName = "property_api"
          user = "property_api"
          password = "property_api"  // pragma: allowlist secret
          prefix = "papi"
        } ${?couchbase-overwrite-envsetting}
      }
    ]
  }
}

cache {
  remote {
    expiration = 360m
  }
  local {
    capacity = 100000
    expiration = 120m
    idle = 60m
  }
}

cache {
  timeout = 30 ms
  hosts=["http://************:8091/pools"]
  bucket = "dragonfruit_scala"
  password = "dragonfruit_scala" // pragma: allowlist secret
  default-expiration = 20 m
}

max-min-setting {
  max-buffer = 0.2
  min-buffer = 0.1
}

zenith {
  enabled = true
  retries = 1
  timeout = 100
}

sl-service {
  breaker {
    failure-count = 5
    disable-duration = 3000 milliseconds
  }
  total-timeout = 100 milliseconds
  retries = 2
}

everest-client {
  urls: [
    "qa.everest-docker.svc.cluster.local"
  ]
  port: 8090
}

page{
  bufferratio = 1.2
  degradebufferratio = 1.0
}

large-cities {
  apply-cities = [9395, 17193]
}

price-filter {
  min-price-buffer = 0.1
  max-price-buffer = 0.25
}

manual{
  connectionTimeout = 2000 millis
  readTimeout = 5000 millis
  grpSize = 50
  failureCount = 5
  disableTime = 30 seconds
  retries = 3
  cityLimit = 12
  urls = ["http://sg-dfcal-6a01.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a02.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a03.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a04.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a05.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a06.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a07.agprod1.agoda.local:9000/manual",
    "http://sg-dfcal-6a08.agprod1.agoda.local:9000/manual"]
}

manual-calculate.env = "sgp-auth"

content-call-sequential {
  "affiliate-enabled" = false
}

supplier-overrider-criteria {
  criteria = [{
    country-id = 45
    origin = ["TR"]
    original-suppliers = [3038]
    logical-supplier = 332
  },
    {
      country-id = -1
      origin = ["ALL"]
      original-suppliers = [3038]
      logical-supplier = 332
    }]
}


price-push {
  enabled = false
  max-room-prices-limit = 100
}


app.profile="devs"

is-ci-env = true

graphql {
  schema-reload-time = 1800s
  timeout-for-fetching-schema = 20s
}

jarvis {
  urls = ["jarvis-stable-prelive.privatecloud.hk.agoda.is"]
  total-timeout = 2 seconds
  enabled = false
  retries = 1
  failure-count = 50
  disable-duration = 60 seconds
  consulEnabled = false

  httpclient {
    name = "jarvis"
    request-timeout = 1000 milliseconds
    bufferSize = 2048
    overflowStrategy = "dropNew"
    refresh-scheduler = 30000
  }
}

ag-http-client {
  mesh {
    default {}
    services {
     local-papi {
        individual-timeout = 2000 milliseconds
        discovery {
          method = "static"
          static {
            hosts = ["localhost:"${ag-http-server.endpoint.http-port}]
          }
        }
        round-robin {
          method = "weighted"
          weighted {
            total-weight = 100
            decrement = 10
            increment = 10
          }
        }
        request {
          max-attempts = 5
        }
      }
      zenith {
        discovery {
          method = "static"
          static {
            hosts = [
              "zenith-ci.privatecloud.hk.agoda.is:80"
            ]
          }
        }
        roundRobin {
          method = "simple"
        }

        request {
          maxAttempts = 1
        }
      }
      jarvis {
        discovery {
          method = "static"
          static {
            hosts = ["jarvis-stable-prelive.privatecloud.hk.agoda.is"]
          }
        }
      }
      priceStreamPool {
        discovery {
          method = "static"
          static {
            hosts = ["bk-psapi-1001:80"]
          }
        }
      }
      npcapi {
        discovery {
          static {
            hosts = ["aiab.qa.agoda.is:30983"]
          }
        }
      }
      content {
        discovery {
          static {
            hosts = ["localhost:80"]
          }
        }
      }
      dragonfruit {
        discovery {
          static {
            hosts = ["localhost:9000"]
          }
        }
      }
      promoapi {
        discovery {
          method = "static"
          static {
            hosts = ["promo-api-qa.qa.agoda.is"]
          }
        }
      }
    }
  }
}

zenith {
  enabled = true
  retries = 1
  timeout = 100
}

ag-observability.otel.exporter.host = ${ag-observability.otel.exporter.env.qa.hk.host}

sl-service {
  enabled = false
  commission-adjustments-enabled = true
}


couchbase-nodes.a = ["hk-qaagmcc-2b01", "hk-qaagmcc-2b02", "hk-qaagmcc-2b03", "hk-qaagmcc-2b04"]

reporting {
  enabled = false
  jvm.enabled = false
  messagingAPIToken = "propertyapi_search"
}

traffic.forwarder{
  forward-request{
    enabled = false
  }
  error-request {
    enabled = false
  }
}

constants {
  overAllBookingAvailabilityThreshold = 5
  numberOfPropertiesForRecommendations = 5
}

hikari {
  databases = [
    {
      id = "bk-qacdb-1000"
      url = "**********************************************************************************************************************************************"
      username = "propertyapi_user"
      password = "+AbUQdN2+Wun8=ypDR*"
      connectionTimeout = 1 seconds
      connectionTestQuery = "SELECT 1"
      maximumPoolSize = 4
    },
    {
      id = "bk-qacdb-1001"
      url = "**********************************************************************************************************************************************"
      username = "propertyapi_user"
      password = "+AbUQdN2+Wun8=ypDR*"
      connectionTimeout = 1 seconds
      connectionTestQuery = "SELECT 1"
      maximumPoolSize = 4
    }
  ]
}
//experiment settings
experimentsPlatform {

  applicationName = "papi_search"

  # Messaging API Token (requested from ADP Team)
  messagingAPIToken = "propertyapi_search"

  # Prelive server pattern use to identify prelive server
  preliveServerRegex = "(bk|BK).*"

  # Your client <NAME_EMAIL>
  clientId = 9

  applyZForDefaultUser = ""

  experiments-service {
    timeout = 5000
    host = "experiments-service-dev.privatecloud.qa.agoda.is"
  }

}

elastic{
  urls = "http://bk-ciprop-1004:9200"
  clustername = "qaelastic"
  index = "hotels"
  indextype = "hotels"
  city-result-size = 1000
  tcp-timeout-ms = 3000
  async-timeout-ms = 3050
  request-timeout = 20000 milliseconds
  batch-size = 4000
  retries = 1
  fallback-enabled = true
}

propertyinfolistcachereader {
    populate-cache-in-case-of-miss-sp-timeout = 10000 milliseconds
}

load-test {
  delay = 0
}

circuit.breaker.papi {
  maxFailures = 20
  callTimeout = 30 seconds
  resetTimeout = 5 seconds
  delay = 5 minutes
  successCount = -1
}

manual{
  connectionTimeout = 2000 millis
  readTimeout = 5000 millis
  grpSize = 50
  failureCount = 5
  disableTime = 30 seconds
  retries = 3
  cityLimit = 12
  urls = ["http://bk-qadfcal-1001:9000/manual"]
}

npc {
  requestTimeout = 30000ms
}

manual-calculate.env = "qa"

clusterService{
  contentTimeout = 10000 milliseconds
  dragonfruitTimeout = 20000 milliseconds

  reviewCounters{
    retries = 2
  }
  reviewFilters{
    retries = 2
  }
  contentSummary{
    retries = 2
  }
  locationInformation{
    retries = 2
  }
  engagement{
    retries = 2
  }
  experience{
    retries = 2
  }
  hotelInformationSummary{
    retries = 2
  }

}

pricing-chunkable-wrapper-settings {
  endpoint-settings {
    max-number-of-attempts = 3
    individual-timeout = ${clusterService.dragonfruitTimeout}
    enable-circuit-breaker = ${clusterService.dfCircuitBreakerEnabled}
  }
}

content-chunkable-wrapper-settings {
  endpoint-settings {
    max-number-of-attempts = 3
    individual-timeout = ${clusterService.contentTimeout}
    enable-circuit-breaker = ${clusterService.contentCircuitBreakerEnabled}
  }
}

site {
  interface = "0.0.0.0"
  port = 8081
  port = ${?http.port}
  metricsPort = 8995
  dev-mode = on
  application-secret-key = "8610d2f41ef1b4a8ade067f578be51d7" # data encryption key
  receive-timeout = 30000 milliseconds
  cache-warm-up = true
  gql-warm-up = false
  populate-cache = false
  populate-cache = ${?cache.populate}
}

capi {
  urls = ["http://customerapi-qa.privatecloud.qa.agoda.is/V1/RewardsApiService"]
  urlsV2Authed = ["http://customerapi-qa.privatecloud.qa.agoda.is/v2/authed"]
  retry-count = 1
  timeout = 200
  membercontext-timeout = 100
  hostprofile-timeout = 5000
  api-key = "mXl9VDj8k2LavTkI3lQgPelUqaxPKonW"
  loyaltyProfile-timeout = 2000
  hostPropertiesTimeout = 1500
  urlTransitV2 =["http://customerapi-main.privatecloud.hk.agoda.is/v2/transit"]
}

no-cc-matrix-option {
  min-properties = 1
}

sold-out-rooms {
  blacklist-hotelids = [185945, 3809, 335020]
  max-rooms = 6
}

limit-master-rooms {
  max-filter-count = 12
  exclude-filter-count = 8
  rooms-config = [
    {
      min-rooms = 0
      max-rooms = 9
      max-filter-count = 3
      exclude-filter-count = 6
    },
    {
      min-rooms = 10
      max-rooms = 24
      max-filter-count = 10
      exclude-filter-count = 6
    },
    {
      min-rooms = 25
      max-rooms = 50
      max-filter-count = 18
      exclude-filter-count = 6
    },
    {
      min-rooms = 50
      max-rooms = 20000
      max-filter-count = 30
      exclude-filter-count = 6
    }]
}

price-snapshot {
  lookback-day = 7
  base-date-offset = -1
  min-percent-diff = 5
}

supplier-data-client {
  #  In implementation this is a timeout that's used for limit reading time (f.e. for Cassandra)
  read-timeout = 1000 ms
}

price-stream {
  total-timeout = 1500 milliseconds
  failure-count = 50
  disable-duration = 60 seconds

  httpclient {
    name = "priceStreamPool"
  }
}

pricing-message {
  enabled = true
}

combine-lists {
  enabled = false
}

limit-properties-number {
  limit = 200
}

payment-api-utility {
  getInstallmentDetails = true
  baseUrl = "https://qa-pay-el2.agoda.local/paymentapi/v2"
  exponentialBackOff = true
  maxRetries = 6
}

payment-api-utility {
  timezone = "Asia/Bangkok"
  dateTimeFormat = "yyyy-MM-dd'T'HH:mm:ss"
  dateFormat = "yyyy-MM-dd"
  apiTimeout = 500
}

wl-client {
  hostsSettings = ["http://qa.wlapi.agoda.local"]
  appName = "propertyapi"
}

ag-http-client.client {
  services {
    dragonfruit{
      individual-timeout = ${clusterService.dragonfruitTimeout}
    }
    content {
      individual-timeout = ${clusterService.contentTimeout}
    }

    rocketmilesRanking {
      individual-timeout = 10000 milliseconds
    }
  }

  default {
    metrics {
      report {
        response-time = true
        request-size = false
        response-size = true
        request-serialization-time = false
        response-deserialization-time = false
      }
    }
  }
}

ag-http-client.mesh.services {
  local-papi {
    discovery {
      method = "static"
      static {
        hosts = ["localhost:"${ag-http-server.endpoint.http-port}]
      }
    }
    round-robin {
      method = "weighted"
      weighted {
        total-weight = 100
        decrement = 10
        increment = 10
      }
    }
    request {
      max-attempts = 5
    }
  }
}

rocketmilesRanking {
  urls = [
     "http://rocketmiles-pagination-qa.privatecloud.hk.agoda.is"
  ]
  apiKey = ""
  maxConnectionsPerHost = 200
}

traffic.forwarder {
  error-request {
    enabled = false
  }
}

secret-deal-setting {
  rate-channels = [7, 8, 9, 14, 27, 111, 105, 104]
  default-fetch-count = 20
  fetch-count = 50
}

promo {
  maxAttempts = 2
  requestTimeout = 5000ms
}

ag-vault {
  enable = true
  http.url = "https://hk.qa-vault.agoda.local:8200/v1"
  cache {
    cryptkey = "Y2tlbDguIQ=="  // Configure unique crypt key
    dir = "/var/tmp/vault" // Enable file cache. this directory should be mounted to a physical machine or persistent storage
  }
}

com.agoda.commons.jdbc.cache {
  layers {
    couchbase {
      enabled = true
      bucket = "mssql-caching"
      hosts = ["hk-cbcache-2q11", "hk-cbcache-2q12", "hk-cbcache-2q13", "hk-cbcache-2q14"]
    }
  }

  vault {
    enabled = true
    path = "it-dbops-nosql/db-cbcache/mssql-caching/mssql-caching/rw"
  }

  consul {
    enabled = true
    //In order of priority
    paths = [
                "local/it-dbops-sql/ag-jdbc-cache/propertyapisearchdocker/qa/"
             ]
  }
}