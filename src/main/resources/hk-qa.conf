include "qa.conf"

ag-http-client {
  mesh {
    default {}
    services {
      zenith {
        discovery {
          method = "static"
          static {
            hosts = [
              "zenith-ci.privatecloud.hk.agoda.is"
            ]
          }
        }
        roundRobin {
          method = "simple"
        }

        request {
          maxAttempts = 1
        }
      }
      jarvis {
        discovery {
          method = "static"
          static {
            hosts = ["jarvis-stable-prelive.privatecloud.hk.agoda.is"]
          }
        }
      }
      priceStreamPool {
        discovery {
          method = "static"
          static {
            hosts = ["bk-psapi-1001:80"]
          }
        }
      }
      npcapi {
        discovery {
          method = "static"
          static {
            hosts = ["npces-masterqa-master.privatecloud.qa.agoda.is"]
          }
        }
      }
      priceStreamPool {
        discovery {
          method = "static"
          static {
            hosts = ["pricestreamapidocker-qa.privatecloud.qa.agoda.is"]
          }
        }
      }
      content {
        discovery {
          method = "static"
          static {
            hosts = ["propertyapicontentdocker-qa.privatecloud.qa.agoda.is"]
          }
        }
      }
      dragonfruit {
        discovery {
          method = "static"
          static {
            hosts = ["qa.propertyapipricingdocker.svc"]
          }
        }
      }
      promoapi {
        discovery {
          method = "static"
          static {
            hosts = ["promo-api-qa.qa.agoda.is"]
          }
        }
      }
    }
  }
}

elastic {
  urls = "http://qa.papi-es-docker.svc.cluster.local:9200"
}

site.cache-warm-up = false
site.gql-warm-up = false
zenith.enabled = false
ag-vault.enable = false

ag-logging.metrics {
  enabled = true
  messaging-api-token = propertyapi_search
  application = search
  prefix = propertyapi
}
availability {
  timeout = 4000 milliseconds
}
hikari.default.queryTimeout = 10
shard-availabilities-settings.resiliency.total-timeout-ms = 2000
shard-availabilities-settings.resiliency.read-timeout-ms = 2000
shard-availabilities-settings.hikari.connectionTimeout = 600
akka.http.server.request-timeout = 20 seconds


hikari {
  databases = [
    {
      id = "hkqacdb2001"
      url = "********************************************************************************************************************************************"
      username = "papi_qa_test_user"
      password = "+AbUQdN2+Wun8=ypDR*" # pragma: allowlist secret
      connectionTimeout = 1 seconds
      connectionTestQuery = "SELECT 1"
      maximumPoolSize = 4
    },
    {
      id = "hkqacdb2002"
      url = "********************************************************************************************************************************************"
      username = "papi_qa_test_user"
      password = "+AbUQdN2+Wun8=ypDR*" # pragma: allowlist secret
      connectionTimeout = 1 seconds
      connectionTestQuery = "SELECT 1"
      maximumPoolSize = 4
    }
  ]
}

ag-cache.couchbase {
  multicache {
    clusters = [
      {
        id = "X"
        settings = {
          bucketName = "propertyapi-search"
          user = "it-propertyapi"
          password = "it-propertyapi" # pragma: allowlist secret
          writeTimeout = 300ms
          readTimeout = 100ms
          keysPrefix = "papi"
          environment {
            socketTimeout = 500ms
            connectTimeout = 5s
            kvTimeout = 100ms
            maxRequestTimeout = 300ms
            kvEndpoints = 1
            computationPoolSize = 8
            operationTracingEnabled = false
          }
          nodes = ["hk-cbcache-2q01", "hk-cbcache-2q02", "hk-cbcache-2q03", "hk-cbcache-2q04"]
        }
      }
    ]
  }
}

ag-sql {
  default {
    query-timeout = 15
    query-retry = 4
  }
  hikari {
    pools {
      cdb {
        database = "App_Papi_Search_RO"
        maximum-pool-size = 5
        connection-timeout = 12 seconds
        datasource-properties = {
          appName = propertyapi
          # https://learn.microsoft.com/en-us/sql/connect/jdbc/understand-timeouts?view=sql-server-ver16
          queryTimeout = 15        # unit is second
          cancelQueryTimeout = 1  # unit is second
          socketTimeout = 30000    # unit is millisecond
        }
        auth {
          provider = "static"
          static {
            username = "papi_qa_test_user"
            password = "+AbUQdN2+Wun8=ypDR*" # pragma: allowlist secret
          }
        }
        discovery {
          provider = "static"
          static {
            instances = ["hkqacdb2001:1433", "hkqacdb2002:1433"]
          }
        }
      }
    }
  }
}

circuit-breaker {
  content {
    callTimeout = 2 seconds
  }
  dragonfruit {
    callTimeout = 5 seconds
  }
}
ag-http-server {
  endpoint {
    defaults.ttl = 24000 milliseconds
    endpoints = [
      {
        path-regex = "/api/property"
        ttl = 20000 milliseconds
      },
      {
        path-regex = "/graphql/search"
        ttl = 30000 milliseconds
      }
      {
        path-regex = "/graphql.*"
        ttl = 15000 milliseconds
      }
    ]
  }
}
