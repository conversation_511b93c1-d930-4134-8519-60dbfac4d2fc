include "docker.conf"

ag-http-server.endpoint.http-port = 8080

ag-sql.hikari.pools.cdb.connection-timeout = 10 seconds
ag-sql.hikari.pools.cdb.discovery.static.instances = ["aiab.qa.agoda.is:31625"]

db-legacy.maximumPoolSize = 2


shard-availabilities-settings {
  socket-timeout = 10000
  clusters = [
    {
      name = "qa1"
      instances = [
        {
          name = "shard01"
          host = "aiab.qa.agoda.is"
          port = 30327
          allocations = ["01"]
        }
      ]
    }
  ]
  hikari {
    connectionTimeout = 10000
    jdbcUrl = "jdbc:cache:sqlserver://${settings.host}:${settings.port};integratedSecurity=false;encrypt=false"
    dataSourceClassName = ""
    driverClassName = "com.agoda.commons.sql.jdbc.cache.JdbcCacheDriver"
  }
  resiliency {
    retry = 1
    read-timeout-ms = 3000
    total-timeout-ms = 5000
    speculative{
      timeout-ms = 2000
      retry = 1
      disable-on-same-cluster = true
    }
  }
}

remote {
  services:[],
  schema :[
    {
      service: "content"
      connectionTimeout : 80000
      pathPrefix: "graphql"
      retry : 1
    },
    {
      service: "dragonfruit"
      connectionTimeout : 80000
      pathPrefix: "graphql"
      retry : 1
    }
  ]
}

couchbase-nodes.a = ["aiab.qa.agoda.is"]

ag-cache.couchbase {
  multicache{
    weighted-roundrobin{
      max-weight = 10000
      increment = 2
      decrement = 2
    }
    clusters = [
     {
       id = "A"
       settings = {
        include "/config/external-settings/couchbase/couchbase-bucket-common.conf"
        nodes = ${couchbase-nodes.a}
        bucketName = "property_api"
        user = "property_api"
        password = "property_api"
        environment {
          bootstrapHttpPort = 30758
          bootstrapCarrierPort = 31003
        }
       } ${?couchbase-overwrite-envsetting}
     }
    ]
  }
}

elastic{
  clustername = "elasticsearch"
  urls = "http://aiab.qa.agoda.is:32567"
  request-timeout = 10000 milliseconds
}

npc {
  requestTimeout = 10000ms
}

capi {
  urls = ["http://aiab.qa.agoda.is:31723/V1/RewardsApiService"]
  urlsV2Authed = ["http://aiab.qa.agoda.is:31723/v2/authed"]
  timeout = 10000
}

price-stream {
  total-timeout = 10000 milliseconds
}

zenith {
  urls = ["zenith-ci.privatecloud.hk.agoda.is:80"]
  port = 2901
  total-timeout = 10000 milliseconds
  enabled = true
  consulEnabled = false
}

everest-client {
  urls: [
    "aiab.qa.agoda.is"
  ]
  port: 30817
  retry-max-attempt = 2 # todo move aiab to use `use-ag-grpc-setting=true` to align with production PVC
  timeout-getagxcommission-ms = 5000
  timeout-getsponsoredlistings-ms = 5000
}

sl-service {
  breaker {
    failure-count = 500000
    disable-duration = 60 seconds
  }
  total-timeout = 10000 milliseconds
  enabled = true
  retries = 2
  commission-adjustments-enabled = true
}

defaultConnectionTimeout = 5000

pii-encryption {
    key = "vdvwk1heF//pifdQ8W1nFKTtav0avfrMwjZLm7IBWFs="
}

ag-http-client {
  mesh {
    default {}
    services {
      zenith {
        discovery {
          method = "static"
          static {
            hosts = ["zenith-ci.privatecloud.hk.agoda.is:80"]
          }
        }
      }
      jarvis {
        discovery {
          method = "static"
          static {
            hosts = [""]
          }
        }
      }
      priceStreamPool {
        discovery {
          method = "static"
          static {
            hosts = ["aiab.qa.agoda.is:30963"]
          }
        }
      }
      dragonfruit {
        discovery {
          method = "static"
          static {
            hosts = ["aiab.qa.agoda.is:31872"]
          }
        }
      }
      content {
        discovery {
          method = "static"
          static {
            hosts = ["aiab.qa.agoda.is:32079"]
          }
        }
      }
      npcapi {
        discovery {
          method = "static"
          static {
            hosts = ["aiab.qa.agoda.is:30983"]
          }
        }
      }
    }
  }
}

ag-grpc.client {
  services {
    growth-program {
      get-commissions.individual-timeout = 500 milliseconds
      discovery {
        method = "static"
        static {
          hosts = ["gp-commission-api-integration.qa.agoda.is:80"]
          authority = "gp-commission-api-integration.qa.agoda.is:80"
        }
      }
    }
  }
}

ag-vault {
  enable = false
}

featurestore {
  clientId : papi
}

feature-store.online {
  required-freshness-default = 1 hour
  rpc.default-timeout = 200 milliseconds
}