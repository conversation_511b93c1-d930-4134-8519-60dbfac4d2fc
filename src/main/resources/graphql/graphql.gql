extend type PropertySummary{
    content:ContentSummary @loadContent
    pricing:HotelPricing @loadPricing
    roomSummary(ContentExtraRoomSummaryRequest: ContentExtraRoomSummaryRequest!):RoomSummaryResponse @loadContentRoomSummary
}

extend type PriceBreakerSummary{
    content:ContentSummary @loadPBContent
    pricing:HotelPricing @loadPBPricing
}

extend type PropertySearchResponse{
  properties(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters, PriceStreamMetaLabRequest: PriceStreamMetaLabRequest):[PropertySummary!]! @loadProperty
  featuredAgodaHome(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters):[PropertySummary!] @featuredAgodaHome
  highlyRatedAgodaHomes(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters):[PropertySummary!] @highlyRatedAgodaHomes
  extraAgodaHomes(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters):[PropertySummary!] @extraAgodaHomes
  featuredLuxuryHotels(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters):[PropertySummary!] @featuredLuxuryHotels
  featuredPulseProperties(ContentSummaryRequest: ContentSummaryRequest!,PricingSummaryRequest: PricingRequestParameters):[PropertySummary!] @featuredPulseProperties
}
extend type AlternateDatePricing{
    pricing:HotelPricing @loadAlternatePricing
}

extend type ContentDetail {
    contentImages(ContentImagesRequest: ContentImagesRequest!): ContentImagesResponse @loadContentImages
    contentReviewScore(ContentReviewScoreRequest: ContentReviewScoreRequest): ContentReviewScoreResponse @loadContentReviewScore
    contentReviewSummaries(ContentReviewSummariesRequest: ContentReviewSummariesRequest): ContentReviewSummariesResponse @loadContentReviewSummaries
    contentSummary(ContentInformationSummaryRequest: ContentInformationSummaryRequest): ContentInformationSummary @loadContentInformationSummary
    contentInformation(ContentInformationRequest: ContentInformationRequest): ContentInformationResponse @loadContentInformation
    contentLocalInformation(ContentLocalInformationRequest: ContentLocalInformationRequest): ContentLocalInformationResponse @loadContentLocalInformation
    contentEngagement: ContentPropertyEngagementResponse @loadContentEngagement
    contentExperiences(ContentExperienceRequest: ContentExperienceRequest): ContentExperiences @loadContentExperiences
    contentFeatures(ContentFeaturesRequest: ContentFeaturesRequest): ContentFeaturesResponse @loadContentFeatures
    contentHighlights(ContentHighlightsRequest: ContentHighlightsRequest) : ContentHighlightsResponse @loadContentHighlights
    contentQna(ContentQnaRequest: ContentQnaRequest): ContentQnaResponse @loadContentQna
    contentTopics(ContentTopicsRequest: TopicsRequest): ContentTopicsResponse @loadContentTopics
    contentRateCategory(ContentRateCategoryRequest: ContentRateCategoryRequest): ContentRateCategoryResponse @loadContentRateCategory
}

extend type ContentNonHotelAccommodation{
    masterRooms:[ContentMasterRoom] @masterRooms
}

extend type PropertySummarySearchResponse {
    properties(PricingSummaryRequest: PricingRequestParameters, ContentSummaryRequest: ContentSummaryRequest): [PropertySummary!] @loadPropertySummarySearch
}

extend type RateCategoryDetail {
    contentRateCategory(ContentRateCategoryRequest: ContentRateCategoryRequest): ContentRateCategoryResponse @loadContentRateCategory
}
