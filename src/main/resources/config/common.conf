include "application/ag-cache-settings.conf"
include "application/ag-concurrency-settings.conf"
include "application/ag-http-client-settings.conf"
include "application/ag-http-server-settings.conf"
include "application/ag-sql-settings.conf"
include "application/ag-vault-settings.conf"
include "application/akka-settings.conf"
include "application/circuit-breaker-settings.conf"
include "application/experiment-settings.conf"
include "application/graphql-settings.conf"
include "application/ag-observability-settings.conf"
include "application/register-consul.conf"
include "application/ag-logging-settings.conf"
include "application/shutdown-hotels-common.conf"
include "application/warmup.conf"
include "application/supplier-financial-data.conf"
include "application/ag-jdbc-cache-settings.conf"

traffic.forwarder.traffic-type = "papi.search.user"
consul-client.cache-dir = "/var/propertyapi/consul-cache/"

ag-consul {
  cache-dir = "/var/propertyapi/consul-cache/"
}

# connection timeout for optional service (priceStream & npc) in millis
defaultConnectionTimeout = 100
defaultReadTimeout = 100

disableMaxConnectionsPerHost = true
webgateApiKey=63ee62a3-69a1-4c5f-9921-ab599bfbb19b

core-search{
  aggregation.enabled = false
}

combine-lists {
  enabled = false
}

constants {
  overAllBookingAvailabilityThreshold = 150
  numberOfPropertiesForRecommendations = 30
}

limit-master-rooms {
  max-filter-count = 12
  exclude-filter-count = 8
  rooms-config = [
    {
      min-rooms = 0
      max-rooms = 9
      max-filter-count = 3
      exclude-filter-count = 6
    },
    {
      min-rooms = 10
      max-rooms = 24
      max-filter-count = 10
      exclude-filter-count = 6
    },
    {
      min-rooms = 25
      max-rooms = 50
      max-filter-count = 18
      exclude-filter-count = 6
    },
    {
      min-rooms = 50
      max-rooms = 20000
      max-filter-count = 30
      exclude-filter-count = 6
    }]
}

sold-out-rooms {
  blacklist-hotelids = [185945, 3809, 335020]
  max-rooms = 6
}

large-cities {
  apply-cities = [10757,16594,17193,15470,7395,233,16716,16534,16580,10647,18007,5085,14932,16056,6325,9590,16364,15977,16573,16585,9395,2002,1569,3987,8584,1063,14552,12772,72200,11304,647,17164,11350,4839,17379,7401,9442,10112,5531,14690,19020,14524,1784]
}

cities {
  top-booked= [9395, 16808, 4951, 16901, 14524, 7401, 8584, 14690, 12080, 5085, 17072, 4064, 8691, 12772, 756, 18943, 18347, 88773, 16056, 9590, 16087, 17019, 15121, 23127, 1808, 17172, 8683, 3987, 2994, 10757, 1622, 13170, 18346, 1784, 717899, 14018, 10461, 17787, 1178, 4740, 2045, 16527, 17005, 3241, 13899, 11977, 2758, 318, 1399, 17236, 4234, 3435, 17193, 2467, 10779, 14865, 3068, 8453, 19041, 10112, 18343, 15523, 13801, 17234, 14523, 16223, 11579, 1569, 4590, 13740, 7233, 217, 9312, 4001, 15780, 5414, 4542, 19359, 15932, 16115, 9698, 17790, 12711, 16928, 12113, 10182, 14932, 12546, 21740, 10787, 702243, 11304, 2374, 17179, 8422, 2703, 17190, 211364, 233, 14552, 102488, 16443, 12333, 14370, 3322, 17099, 512948, 5235, 15470, 21397, 17191, 15936, 2570, 15289, 205026, 213193, 11863, 17198, 18218, 3687, 2018, 19433, 8052, 615, 9254, 16611, 5435, 7321, 2961, 9442, 21284, 1102, 17023, 10680, 12374, 5349, 11980, 6343, 17188, 102464, 13689, 78905, 7219, 17759, 17095, 4816, 2679, 16440, 8881, 3054, 10554, 106017, 2366, 9466, 18720, 12384, 17235, 17487, 4684, 5070, 17232, 17704, 15643, 11531, 17753, 17139, 105619, 4278, 2217, 17022, 19392, 6771, 7875, 16850, 10665, 2111, 105965, 18826, 161657, 17222, 2919, 5954, 15032, 17254, 287008, 12393, 17233, 10137, 4472, 78901, 3936, 15308, 9777, 23732, 105938, 13259, 5152, 4682, 10358, 7266, 11488, 1063, 9506, 8559, 17831, 106031, 4923, 513407, 17160, 13630, 17383, 17137, 10345, 16594, 5663, 19623, 18732, 13596, 105645, 15947, 3965, 17791, 105987, 14892, 107, 19042, 2394, 16264, 4729, 3881, 4556, 18341, 2299, 11459, 7395, 106028, 212469, 6958, 162093, 8163, 16580, 11356, 11658, 105625, 17269, 7165, 6861, 5818, 17127, 14094, 79672, 16079, 18352, 6931, 14566, 6263, 17237, 23028, 6214, 19625, 15903, 7692, 13602, 19479, 16585, 10921, 8845, 105971, 11743, 13796, 1249, 16017, 3818, 1568, 9047, 17080, 11780, 12289, 10313, 3750, 4359, 106352, 15612, 5368, 105656, 4689, 78591, 106032, 19704, 106064, 17036, 6579, 10022, 1686, 4635, 4276, 14544, 88776, 18875, 7786]
}

secret-deal-setting {
  rate-channels = [7, 8, 9, 14, 27, 111, 105, 104]
  default-fetch-count = 20
  fetch-count = 50
}

site {
  interface = "0.0.0.0"
  port = 8081
  port = ${?http.port}
  metricsPort = 8999
  metricsPort = ${?metrics.port}
  dev-mode = on
  application-secret-key = "8610d2f41ef1b4a8ade067f578be51d6" # data encryption key
  receive-timeout = 8000 milliseconds
  cache-warm-up = true
  populate-cache = false
  populate-cache = ${?cache.populate}
  precache-machine = false
  precache-machine = ${?cache.precachemachine}
  cache-warm-up-timeout = 60 seconds
  gql-warm-up = true
  cache-population-initial-delay = 5 hours
  to-strict-entity-timeout = 1000 ms
}

augmentation {
  radius = 10
  enabled = true
  radius-multiplier = 1.2
}

augmentsearch{
  maxcities = 10
  wl-city-limit = [
    { whitelabel-id = 3, limit = 50 },
    {  whitelabel-id = 4, limit = 50 }
  ]
}

manual{
  connectionTimeout = 2000 millis
  readTimeout = 5000 millis
  grpSize = 50
  failureCount = 5
  disableTime = 30 seconds
  retries = 3
  cityLimit = 12
  urls = []
}
no-cc-matrix-option {
  min-properties = 100
}

override.experiment{
    force-a-dependency-service = false
}

page{
  bufferratio = 1.2
  degradebufferratio = 1.0
  maxpagebuffer = 45
  pricebufferratio = 3
  recobufferratio = 1
  pricebufferpage = 5
  maxPriceBufferCount = 225
  maxPriceBufferCountDegraded = 90
  newmaxpricebufferenable = true
  apobufferFetchSize = 75
  roomAmenityFetchSize = 100
  thirtyPercentDealsFetchSize = 270
  maxFetchCount = 4
  maxFetchCountDegraded = 1
}

supplier-overrider-criteria {
  criteria = [{
    country-id = 45
    origin = ["TR"]
    original-suppliers = [3038]
    logical-supplier = 332
  },
  {
    country-id = -1
    origin = ["ALL"]
    original-suppliers = [3038]
    logical-supplier = 332
  }]
}

meta-pricing {
  min-availability-threshold = 0
  max-availability-threshold = 25
  limit = 10
}

geosearch {
  default-radius = 30
}


price-snapshot {
  lookback-day = 14
  base-date-offset = -1
  min-percent-diff = 5
}

price-push {
  enabled = false
  max-room-prices-limit = 20
}

special-attributes{
  cheapest.dfbatch.size = 4
}


otel {
  // Batch Span Processor
  bsp {
    // Reduce batch size to avoid hitting message size limit on the collectors when enabling result-tracking
    max.export.batch.size = 32
  }
}


reporting {
  enabled = true
  time.interval = 1s
  messagingAPIToken = "propertyapi_search"
  jvm.enabled = true
  keyspace = "search"
  prefix = "propertyapi"
  percentile = true
}

traffic.forwarder {
  traffic-type = "papi.search.user"
  forward-request {
    enabled = false
    encryption {
      enabled = true
      encryption-key {
        provider = "vault"
      }
    }
  }
  error-request {
    enabled = true
  }

  kafka {
    adp.apikey = "propertyapi_search"
    security.protocol = "SASL_PLAINTEXT"
    sasl.mechanism = "SCRAM-SHA-512"
  }
}


// number of cdb connection for cms-service, supplier metadata
db-legacy.maximumPoolSize = 2

papi-message {
  raw-request-enabled = true
}
price-filter {
  buffer = 0.10
}

filter-matrix-order {
  facility-order = [93, 90, 80, 17, 92, 116, 89, 5754, 15, 91, 11, 83, 24, 10, 16, 88, 56]
  things-to-do-facility-order = [51, 55, 57, 58, 59, 60, 61, 63, 106, 136, 258, 265, 268, 269]
  transportation-filter-order = [2,3,4,5,6]
  room-amenities-order = [50, 43, 42, 25, 35, 86, 31, 33, 87, 1, 37, 143, 47, 342, 2025, 2026, 2350, 2351]
  beach-access-order = [2,1]
}

dealsfilter {
    min-percent = 50
}


externalServicesRequestFilter {
  dragonfruit {
    lengthOfStayLimit = 31
  }
}

#type array size should equal to id array size
segment-filter-matrix {
  family {
    type = [11, 11, 11, 11, 11, 26]
    id = [89, 15, 17, 90, 93, 4]
  }
  solo {
    type = [11, 22, 23, 26, 11, 4]
    id = [90, 78322, 56649, 4, 92, 33]
  }
  double {
    type = [22, 11, 23, 11, 11, 23]
    id = [78322, 90, 49499, 91, 93, 56649]
  }
  group {
    type = [22, 11, 23, 11, 26, 11]
    id = [78322, 90, 56649, 93, 4, 17]
  }
}


hadoop {
  enabled = true
  messagingAPIToken = "propertyapi_search"
}


propertyinfo.cache{
  initsize = 2097152 #2<<20
  maxsize = 2097152 #2<<20
  concurrencylevel = 32 #2 << 4
}

cache {
  remote {
    enableReporting = true
    expiration = 360m
  }
  local {
    capacity = 300000
    expiration = 120m
    idle = 60m
  }
}

# to override cache setting for specific stored proc
customcache {
  pmc-campaign {
    cache {
      remote {
        enableReporting = true
        expiration = 5m
      }
      local {
        capacity = 300000
        expiration = 5m
        idle = 60m
      }
    }
  }

  cc-campaign {
    cache {
      remote {
        enableReporting = true
        expiration = 5m
      }
      local {
        capacity = 300000
        expiration = 5m
        idle = 60m
      }
    }
  }
}


nha {
  # Quick Fix for NHA Star Rating
  nha-accommodation-types = [28, 29, 103, 131]
}

bnpl {
  blacklist-origins = []
  cache {
    initsize = 2097152 #2<<20
    maxsize = 2097152 #2<<20
    concurrencylevel = 32 #2 << 4
  }
}

sql.non-mandatory-sp {
  timeout = 500 millisecond
}

propertyinfolistcachereader {
    populate-cache-in-case-of-miss-sp-timeout = 200 milliseconds
    couchbase-read-timeout = 200 milliseconds
}

producthotelsettings {
  couchbase-read-timeout = 40 milliseconds
}

com.agoda.commons.jdbc.cache {
  layers{
    couchbase {
      enabled = true
      bucket = "mssql-caching"
    }
  }

  vault {
    enabled = true
    path = "it-dbops-nosql/db-cbcache/mssql-caching/mssql-caching/rw"
  }

  consul {
    enabled = true
    //In order of priority
    paths = [
        "local/it-dbops-sql/ag-jdbc-cache/propertyapisearchdocker/prod/"
    ]
  }
}