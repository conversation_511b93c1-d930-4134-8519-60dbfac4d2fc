shard-availabilities-settings{
  include "/config/external-settings/shard/shard-db-common.conf"
  include "/config/external-settings/shard/shard-db-consul.conf"
  consul-setting{
    dc = "sg"
    discovery-setting.tags = ${shard-availabilities-settings.prod-tags}
  }
  clusters = []
}

ag-sql.hikari.pools.cdb.discovery.consul.dc = "sg"

manual-calculate.env = "sgp-auth"

traffic.forwarder.dc = "sgp"

elastic{
  clustername = "propertyapi-sg-a"
}

# couchbase
couchbase-nodes.a = ["sg-cbcache-6a01", "sg-cbcache-6a02", "sg-cbcache-6a03", "sg-cbcache-6a04"]

site {
  cache-population-initial-delay = 5 hours
}

ag-vault.http.url = "https://sg.vault.agoda.local:8200/v1"
ag-vault.http.url = ${?VAULT_ADDR}

regulatoryblocking {
  couchbase.nodes = [
      "sg-cbcache-6b01:11210",
      "sg-cbcache-6b02:11210",
      "sg-cbcache-6b03:11210",
      "sg-cbcache-6b04:11210",
      "sg-cbcache-6b05:11210",
      "sg-cbcache-6b06:11210"
  ]
  base-url = "http://prod.regulatory-blocking.svc.cluster.local:8080"
 }

com.agoda.commons.jdbc.cache.layers.couchbase {
  hosts = ["sg-cbcache-6b01", "sg-cbcache-6b02", "sg-cbcache-6b03", "sg-cbcache-6b04","sg-cbcache-6b05"]
}