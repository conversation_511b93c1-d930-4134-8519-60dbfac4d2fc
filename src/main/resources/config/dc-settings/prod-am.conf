ag-http-server.discovery.enabled = false
shard-availabilities-settings{
  include "/config/external-settings/shard/shard-db-common.conf"
  include "/config/external-settings/shard/shard-db-consul.conf"
  consul-setting{
    dc = "am"
    discovery-setting.tags = ${shard-availabilities-settings.prod-tags}
  }
  clusters = []
}

ag-sql.hikari.pools.cdb.discovery.consul.dc = "am"


manual-calculate.env = "ams-auth"

traffic.forwarder.dc = "ams"

elastic.clustername = "propertyapi-am-a"

availability {
  timeout = 600 milliseconds
}

# couchbase
couchbase-nodes.a = ["am-cbcache-4a11", "am-cbcache-4a12", "am-cbcache-4a13", "am-cbcache-4a14", "am-cbcache-4a15"]

site {
  cache-population-initial-delay = 4 hours
}

ag-vault.http.url = "https://am.vault.agoda.local:8200/v1"
ag-vault.http.url = ${?VAULT_ADDR}

com.agoda.commons.jdbc.cache.layers.couchbase {
  hosts = ["am-cbcache-4b11", "am-cbcache-4b12", "am-cbcache-4b13", "am-cbcache-4b14","am-cbcache-4b15"]
}

traffic.forwarder {
  forward-request {
    enabled = true
    // At 2024-03-06, we're having ~8MB/min on PROD, increasing from 0.02 to 0.08 to make the number match with SGP (~30MB/min)
    sampling-rate = 0.08
  }
}

regulatoryblocking {
  couchbase.nodes = [
      "am-cbcache-4b11:11210",
      "am-cbcache-4b12:11210",
      "am-cbcache-4b13:11210",
      "am-cbcache-4b14:11210",
      "am-cbcache-4b15:11210"
  ]
  base-url = "http://prod.regulatory-blocking.svc.cluster.local:8080"
 }
