shard-availabilities-settings{
  include "/config/external-settings/shard/shard-db-common.conf"
  include "/config/external-settings/shard/shard-db-consul.conf"
  consul-setting{
    dc = "as"
    discovery-setting.tags = ${shard-availabilities-settings.prod-tags}
  }
  clusters = []
}

ag-sql.hikari.pools.cdb.discovery.consul.dc = "as"

traffic.forwarder.dc = "ash"

manual-calculate.env = "ash-auth"

elastic{
  clustername = "propertyapi-as-a"
}

# couchbase
couchbase-nodes.a = ["as-cbcache-3a01", "as-cbcache-3a02", "as-cbcache-3a03", "as-cbcache-3a04"]

site {
  cache-population-initial-delay = 2 hours
}

ag-vault.http.url = "https://as.vault.agoda.local:8200/v1"
ag-vault.http.url = ${?VAULT_ADDR}
traffic.forwarder.forward-request.sampling-rate = 0.08

regulatoryblocking {
  couchbase.nodes = [
      "as-cbcache-3b01:11210",
      "as-cbcache-3b02:11210",
      "as-cbcache-3b03:11210",
      "as-cbcache-3b04:11210",
      "as-cbcache-3b05:11210"
  ]
  base-url = "http://prod.regulatory-blocking.svc.cluster.local:8080"
 }

com.agoda.commons.jdbc.cache.layers.couchbase {
  hosts = ["as-cbcache-3b11", "as-cbcache-3b12", "as-cbcache-3b13", "as-cbcache-3b14","as-cbcache-3b15"]
}
