include "/config/external-settings/shard/prelive-hk-shard.conf"

elastic {
  clustername = "propertyapi-hk-t-v8"
  urls = "http://hk-papies-2t05:9200,hk-papies-2t06:9200,hk-papies-2t07:9200,hk-papies-2t08:9200"
}
capi {
  urls = ["http://customerapi-main.privatecloud.hk.agoda.is/V1/RewardsApiService"]
  urlsV2Authed = ["http://customerapi-main.privatecloud.hk.agoda.is/v2/authed"]
  urlTransitV2 =["http://customerapi-main.privatecloud.hk.agoda.is/v2/transit"]
}

jarvis {
    urls = ["http://jarvis-stable-prelive.privatecloud.hk.agoda.is"]
}

payment-api-utility.baseUrl = "http://local.ebe-payment-api-netcore.svc.cluster.local:5000/paymentapi/v2"

wl-client.hostsSettings = ["http://whitelabel-service-api-prod.hk.agoda.is"]

ag-http-client.mesh.services {

  promoapi {
    discovery {
      method = "static"
      static {
        hosts = ["promo-api-main.privatecloud.hk.agoda.is"]
      }
    }
  }
  npcapi {
    discovery {
      method = "static"
      static {
        hosts = ["npc-api-docker-user.hk.agoda.is"]
      }
    }
  }
  priceStreamPool {
    discovery {
      method = "static"
      static {
        hosts = ["pricestreamapidocker-prelive.privatecloud.hk.agoda.is"]
      }
    }
  }
  jarvis {
    discovery {
      method = "static"
      static {
        hosts = ["jarvis-stable-prelive.privatecloud.hk.agoda.is"]
      }
    }
  }
}
# couchbase
couchbase-nodes.a = ["hk-cbcache-2a01", "hk-cbcache-2a02", "hk-cbcache-2a03", "hk-cbcache-2a04"]
couchbase-nodes.b = ["hk-cbcache-2b01", "hk-cbcache-2b02", "hk-cbcache-2b03", "hk-cbcache-2b04"]
traffic.forwarder.dc = "hkg"
ag-sql.hikari.pools.cdb.discovery.consul.dc = "hk"

ag-http-client.mesh.services {
  dragonfruit {
    discovery {
      method = "static"
      static {
        hosts = ["propertyapipricingdocker-acm-papi.privatecloud.hk.agoda.is:80"]
      }
    }
  }
  content {
    discovery {
      method = "static"
      static {
        hosts = ["propertyapicontentdocker-acmtest.privatecloud.hk.agoda.is:80"]
      }
    }
  }
}
ag-cache.couchbase.multicache.clusters = [
  {
    id = "X"
    settings = {
      include "/config/external-settings/couchbase/couchbase-bucket-common.conf"
      nodes = ["hk-cbcache-2w01", "hk-cbcache-2w02", "hk-cbcache-2w03", "hk-cbcache-2w04", "hk-cbcache-2w05"]
    } ${?couchbase-overwrite-envsetting}
  }
]
site.cache-population-initial-delay = 3 hours

regulatoryblocking {
  couchbase {
    nodes = [
      "hk-cbcache-2w01:11210",
      "hk-cbcache-2w02:11210",
      "hk-cbcache-2w03:11210",
      "hk-cbcache-2w04:11210",
      "hk-cbcache-2w05:11210",
      "hk-cbcache-2w06:11210",
      "hk-cbcache-2w07:11210",
      "hk-cbcache-2w08:11210",
      "hk-cbcache-2w09:11210",
      "hk-cbcache-2w10:11210",
      "hk-cbcache-2w11:11210"
    ]
    use-vault = false
    user = "it-legal-tech"
    password = "wdahflsdrk0ds20a"  # pragma: allowlist secret
  }
 }

com.agoda.commons.jdbc.cache {
  vault {
    enabled = true
    path = "it-dbops-nosql/db-cbcache/poc-mssql-caching/poc-mssql-caching/rw"
  }
  layers {
    couchbase {
      bucket = "poc-mssql-caching"
      hosts = ["hk-cbcache-2b01", "hk-cbcache-2b02", "hk-cbcache-2b03", "hk-cbcache-2b04","hk-cbcache-2b05"]
    }
  }
  consul {
    enabled = true
    //In order of priority
    paths = [
        "local/it-dbops-sql/ag-jdbc-cache/propertyapisearchdocker/prelive/"
    ]
  }
}