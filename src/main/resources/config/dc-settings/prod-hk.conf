shard-availabilities-settings{

  include "/config/external-settings/shard/shard-db-common.conf"
  include "/config/external-settings/shard/shard-db-consul.conf"
  consul-setting{
    dc = "hk"
    discovery-setting.tags = ${shard-availabilities-settings.prod-tags}
  }
  clusters = []
}

site {
  cache-population-initial-delay = 3 hours
}

ag-sql.hikari.pools.cdb.discovery.consul.dc = "hk"


traffic.forwarder.dc = "hkg"

elastic{
  clustername = "propertyapi-hk-a"
}


manual-calculate.env = "hkg-auth"


# couchbase
couchbase-nodes.a = ["hk-cbcache-2a01", "hk-cbcache-2a02", "hk-cbcache-2a03", "hk-cbcache-2a04"]
couchbase-nodes.b = ["hk-cbcache-2b01", "hk-cbcache-2b02", "hk-cbcache-2b03", "hk-cbcache-2b04"]


ag-vault.http.url = "https://hk.vault.agoda.local:8200/v1"
ag-vault.http.url = ${?VAULT_ADDR}

regulatoryblocking {
  couchbase.nodes = [
      "hk-cbcache-2b01:11210",
      "hk-cbcache-2b02:11210",
      "hk-cbcache-2b03:11210",
      "hk-cbcache-2b04:11210",
      "hk-cbcache-2b05:11210"
  ]
  base-url = "http://prod.regulatory-blocking.svc.cluster.local:8080"
 }

com.agoda.commons.jdbc.cache.layers.couchbase {
  hosts = ["hk-cbcache-2b01", "hk-cbcache-2b02", "hk-cbcache-2b03", "hk-cbcache-2b04","hk-cbcache-2b05"]
}