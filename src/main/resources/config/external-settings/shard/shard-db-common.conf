application-name = "papisearch"
db-name-prefix = "agoda_city_avail_"
socket-timeout = 5000
username = ""
password = ""
circuit-breaker {
  failure-rate-threshold = 50
  open-state-timeout = 90.seconds
  num-samples-closed-state = 30
  num-samples-half-open-state = 5
  startup-delay = 1 minute
}
hikari {
  connectionTimeout = 250 #ms
  connectionTestQuery = "SELECT 1"
  maximumPoolSize = 24
  minimumIdle = 16
  idleTimeout = 120000
  leakDetectionThreshold = 60000
  initializationFailTimeout = -1 #we don't want the app to fail starting if it cannot connect to db, as it's possible that we enable/disable a cluster during maintenance
  # Terminate the connection 1 second after query timeout.
  # This reduces long running queries which should have been cancelled on queryTimeout by the driver on client side but failed to do so.
  "dataSource.cancelQueryTimeout" = 1
  # Expose Active / Idle connections through mbeans
  registerMbeans = true
  "dataSource.encrypt" = "false"
  jdbcUrl = "jdbc:cache:sqlserver://${settings.host}:${settings.port};integratedSecurity=false;encrypt=false"
  dataSourceClassName = ""
  driverClassName = "com.agoda.commons.sql.jdbc.cache.JdbcCacheDriver"
}
pool-config {
  # We have maximum number of connections as 24. So, using min-thread = 24 + 1 here to prevent any potential deadlock
  min-thread = 25
  # max-thread can be any number higher than 25. Since the buffer-size is very high, this thread-pool will never scale its number of threads up.
  max-thread = 25
  buffer-size = 1024
  # Improve resiliency by creating thread pool per sql instance
  isolate-per-instance = true
}
resiliency {
  retry = 0
  read-timeout-ms = 300
  total-timeout-ms = 500
  speculative {
    timeout-ms = 200
    retry = 1
    disable-on-same-cluster = true
  }
}


