com.agoda.commons.jdbc.cache {
  appName = "propertyapisearch"
  analytics {
    adp {
      enabled = true
      key = "propertyapisearch"
    }
  }
  filtering {
    shadow {
      allowlist {
        values = []
        regexes = ["agoda_city_avail_\\d{2}\\.dbo\\.select_available_city_avail"]
      }
      blocklist {
        values = []
        regexes = []
      }
    }
    cache {
      allowlist {
        values = []
        regexes = ["agoda_city_avail_\\d{2}\\.dbo\\.select_available_city_avail"]
      }
      blocklist {
        values = []
        regexes = []
      }
    }
  }
}