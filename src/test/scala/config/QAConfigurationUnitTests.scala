package config

import akka.http.scaladsl.settings.ServerSettings
import com.agoda.commons.concurrency.v1.threadpool.settings.{
  ThreadLimit,
  ThreadLimitType,
  ThreadPoolSettings,
  ThreadPoolType
}
import com.agoda.commons.dynamic.state.State
import com.agoda.commons.http.mesh.v2.ServiceMesh.Host
import com.agoda.commons.http.mesh.v2.discovery.StaticListDiscoveryService
import com.agoda.commons.http.mesh.v2.settings.ServiceMeshSettingsBuilder
import com.agoda.commons.http.server.bootstrap.BootstrapSettings
import com.agoda.papi.search.common.externaldependency.cache.AgCacheSettings
import com.agoda.papi.search.common.util.circuitbreaker.ExternalServiceCircuitBreakerSettings
import com.agoda.papi.search.complexsearch.configuration.PageSearchSettings
import com.agoda.papi.search.complexsearch.filter.elastic.config.ElasticSettings
import com.agoda.papi.search.property.service.ClusterService.ClusterServiceSettings
import com.typesafe.config.{ Config, ConfigFactory }
import org.scalatest.matchers.should.Matchers
import org.scalatest.prop.TableDrivenPropertyChecks.forAll
import org.scalatest.prop.Tables.Table
import org.scalatest.wordspec.AnyWordSpec
import sttp.client3.UriContext

import scala.concurrent.duration._
import scala.jdk.CollectionConverters._

class QAConfigurationUnitTests extends AnyWordSpec with Matchers {

  "hk-qa.conf" when {
    val testConfig = ConfigFactory.load("hk-qa.conf")
    "Ag-sql" should {
      "parse the config string correctly" in {
        val agSqlConfig   = testConfig.getConfig("ag-sql")
        val defaultConfig = agSqlConfig.getConfig("default")
        // Assertions for default config
        defaultConfig.getInt("await-connection-thread-pool-size") shouldBe 81
        defaultConfig.getInt("query-retry") shouldBe 4
        defaultConfig.getInt("query-timeout") shouldBe 15
        defaultConfig.getConfig("thread-pools").getInt("transaction-max-size-x-cpu") shouldBe 32

        // Assertions for hikari default config
        val hikariConfig        = agSqlConfig.getConfig("hikari")
        val hikariDefaultConfig = hikariConfig.getConfig("default")
        hikariDefaultConfig.getString("connection-timeout") shouldBe "3 seconds"
        //            hikariDefaultConfig.getConfig("datasource-properties").getConfig("appName") shouldBe "propertyapisearchdocker"
        hikariDefaultConfig.getConfig("datasource-properties").getBoolean("disableStatementPooling") shouldBe false
        hikariDefaultConfig.getConfig("datasource-properties").getString("maxResultBuffer") shouldBe "5p"
        hikariDefaultConfig.getConfig("datasource-properties").getInt("socketTimeout") shouldBe 10000
        hikariDefaultConfig.getConfig("datasource-properties").getInt("statementPoolingCacheSize") shouldBe 100
        hikariDefaultConfig.getString("idle-timeout") shouldBe "600 seconds"
        hikariDefaultConfig.getConfig("initialization").getString("auth-timeout") shouldBe "10 seconds"
        hikariDefaultConfig.getConfig("initialization").getString("discovery-timeout") shouldBe "10 seconds"
        hikariDefaultConfig.getConfig("initialization").getString("fail-timeout") shouldBe "1 second"
        hikariDefaultConfig.getString(
          "jdbc-url-template"
        ) shouldBe "jdbc:otel:sqlserver://{server};databaseName={database};integratedSecurity=false;encrypt=false"
        hikariDefaultConfig
          .getConfig("jdbc-url-templates")
          .getString(
            "failover"
          ) shouldBe "jdbc:otel:sqlserver://{server};databaseName={database};integratedSecurity=false;encrypt=false;failoverPartner={failoverPartner}"
        hikariDefaultConfig
          .getConfig("jdbc-url-templates")
          .getString(
            "regular"
          ) shouldBe "jdbc:otel:sqlserver://{server};databaseName={database};integratedSecurity=false;encrypt=false"
        hikariDefaultConfig.getString("max-lifetime") shouldBe "1800 seconds"
        hikariDefaultConfig.getInt("maximum-pool-size") shouldBe 4
        hikariDefaultConfig.getInt("minimum-idle") shouldBe 4

        // Assertions for hikari pools
        val poolsConfig = hikariConfig.getConfig("pools")
        val cdbConfig   = poolsConfig.getConfig("cdb")
        cdbConfig.getConfig("auth").getString("provider") shouldBe "static"
        cdbConfig
          .getConfig("auth")
          .getConfig("vault")
          .getString("path") shouldBe "it-dbops-sql/sql/credential/propertyapi_user/cdb"
        cdbConfig.getString("connection-timeout") shouldBe "12 seconds"
        cdbConfig.getString("database") shouldBe "App_Papi_Search_RO"
        cdbConfig.getConfig("datasource-properties").getInt("cancelQueryTimeout") shouldBe 1
        cdbConfig.getConfig("datasource-properties").getInt("queryTimeout") shouldBe 15
        cdbConfig.getConfig("datasource-properties").getInt("socketTimeout") shouldBe 30000
        cdbConfig.getConfig("discovery").getConfig("consul").getString("dc") shouldBe ""
        cdbConfig.getConfig("discovery").getConfig("consul").getString("service") shouldBe "cdb"
        cdbConfig.getConfig("discovery").getConfig("consul").getStringList("tags").asScala.toList shouldBe List(
          "prod",
          "err"
        )
        cdbConfig.getConfig("discovery").getString("provider") shouldBe "static"
        cdbConfig.getInt("maximum-pool-size") shouldBe 5
        cdbConfig.getInt("minimum-idle") shouldBe 4
      }
    }

    "ag-cache" should {
      "parse the config string correctly" in {

        val config: AgCacheSettings = AgCacheSettings.apply(testConfig)

        config.localCacheCapacity shouldBe 10000
        config.productHotelsLocalCacheCapacity shouldBe 100000

      }
    }
    "ag-concurrency" should {
      "parse the config string correctly" in {

        val databaseConfig = ThreadPoolSettings
          .of(
            "database",
            ConfigFactory
              .load(testConfig)
              .getConfig("ag-concurrency.threadpools")
              .getConfig("database")
          )
          .build()

        val mainConfig = ThreadPoolSettings
          .of(
            "main",
            ConfigFactory
              .load(testConfig)
              .getConfig("ag-concurrency.threadpools")
              .getConfig("main")
          )
          .build()

        databaseConfig.getType shouldBe ThreadPoolType.FIXED
        databaseConfig.getSize.getAbsoluteThreadNumber shouldBe ThreadLimit
          .of(2, ThreadLimitType.PER_CORE)
          .getAbsoluteThreadNumber

        mainConfig.getType shouldBe ThreadPoolType.FJP
        mainConfig.getSize.getAbsoluteThreadNumber shouldBe ThreadLimit
          .of(1, ThreadLimitType.PER_CORE)
          .getAbsoluteThreadNumber

      }
    }

    "Ag-http-server" should {
      "parse the config string correctly" in {

        val config = BootstrapSettings(testConfig)
        config.serviceName shouldBe ("papi-search")
        config.endpoint.defaultTTL shouldBe Some(24000 milliseconds)
        config.endpoint.endpoints.filter(_.path == "/api/property").head.ttl shouldBe Some(20000 milliseconds)
        config.endpoint.endpoints.filter(_.path == "/graphql/search").head.ttl shouldBe Some(30000 milliseconds)
        config.endpoint.endpoints.filter(_.path == "/graphql.*").head.ttl shouldBe Some(15000 milliseconds)

      }
    }

    "akka" should {
      "parse the config string correctly" in {

        val config = ServerSettings(testConfig)
        config.timeouts.idleTimeout shouldBe 60.seconds
        config.timeouts.bindTimeout shouldBe 1.seconds
        config.timeouts.lingerTimeout shouldBe 1.minute
        config.timeouts.requestTimeout shouldBe 20.seconds
        config.maxConnections shouldBe 1024
        config.parserSettings.maxHeaderCount shouldBe 128

      }
    }
    "circuit-breaker" should {
      "parse the config string correctly" in {
        val mockState = State.of[Config](ConfigFactory.empty())

        val config =
          ExternalServiceCircuitBreakerSettings.fromConfigAndConsul(testConfig, mockState)
        config.contentCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.contentCircuitBreakerSettings.maxFailures shouldBe 40
        config.contentCircuitBreakerSettings.callTimeout shouldBe 2.seconds
        config.contentCircuitBreakerSettings.resetTimeout shouldBe 5.seconds
        config.contentCircuitBreakerSettings.service shouldBe "content"

        config.dragonfruitCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.dragonfruitCircuitBreakerSettings.maxFailures shouldBe 40
        config.dragonfruitCircuitBreakerSettings.callTimeout shouldBe 5.seconds
        config.dragonfruitCircuitBreakerSettings.resetTimeout shouldBe 5.seconds
        config.dragonfruitCircuitBreakerSettings.service shouldBe "dragonfruit"

        config.gandalfCircuitBreakerSettings.enabled.get.get() shouldBe false
        config.gandalfCircuitBreakerSettings.maxFailures shouldBe 10
        config.gandalfCircuitBreakerSettings.callTimeout shouldBe 30.milliseconds
        config.gandalfCircuitBreakerSettings.resetTimeout shouldBe 8.seconds
        config.gandalfCircuitBreakerSettings.service shouldBe "gandalf"

        config.mandatoryESCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.mandatoryESCircuitBreakerSettings.maxFailures shouldBe 40
        config.mandatoryESCircuitBreakerSettings.callTimeout shouldBe 825.milliseconds
        config.mandatoryESCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.mandatoryESCircuitBreakerSettings.service shouldBe "mandatory-elasticsearch"

        config.optionalESCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.optionalESCircuitBreakerSettings.maxFailures shouldBe 40
        config.optionalESCircuitBreakerSettings.callTimeout shouldBe 825.milliseconds
        config.optionalESCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.optionalESCircuitBreakerSettings.service shouldBe "optional-elasticsearch"

        config.rocketmilesRankingCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.rocketmilesRankingCircuitBreakerSettings.maxFailures shouldBe 15
        config.rocketmilesRankingCircuitBreakerSettings.callTimeout shouldBe 500.milliseconds
        config.rocketmilesRankingCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.rocketmilesRankingCircuitBreakerSettings.service shouldBe "rocketmiles-ranking"

        config.npcCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.npcCircuitBreakerSettings.maxFailures shouldBe 40
        config.npcCircuitBreakerSettings.callTimeout shouldBe 125.milliseconds
        config.npcCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.npcCircuitBreakerSettings.service shouldBe "npc"

        config.jarvisCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.jarvisCircuitBreakerSettings.maxFailures shouldBe 40
        config.jarvisCircuitBreakerSettings.callTimeout shouldBe 150.milliseconds
        config.jarvisCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.jarvisCircuitBreakerSettings.service shouldBe "jarvis"

        config.capiCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.capiCircuitBreakerSettings.maxFailures shouldBe 40
        config.capiCircuitBreakerSettings.callTimeout shouldBe 3025.milliseconds
        config.capiCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.capiCircuitBreakerSettings.service shouldBe "capi"

        config.shardCircuitBreakerSettings.enabled.get.get() shouldBe false
        config.shardCircuitBreakerSettings.maxFailures shouldBe 55
        config.shardCircuitBreakerSettings.callTimeout shouldBe 700.milliseconds
        config.shardCircuitBreakerSettings.resetTimeout shouldBe 45.seconds
        config.shardCircuitBreakerSettings.service shouldBe "SqlShard"

        config.growthProgramCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.growthProgramCircuitBreakerSettings.maxFailures shouldBe 60
        config.growthProgramCircuitBreakerSettings.callTimeout shouldBe 20.milliseconds
        config.growthProgramCircuitBreakerSettings.resetTimeout shouldBe 5.seconds
        config.growthProgramCircuitBreakerSettings.service shouldBe "gp-grpc-client"

        config.fsCircuitBreakerSettings.enabled.get.get() shouldBe true
        config.fsCircuitBreakerSettings.maxFailures shouldBe 15
        config.fsCircuitBreakerSettings.callTimeout shouldBe 200.milliseconds
        config.fsCircuitBreakerSettings.resetTimeout shouldBe 60.seconds
        config.fsCircuitBreakerSettings.service shouldBe "feature-store"
      }
    }
    "page" should {
      "parse the config string correctly" in {

        val config = PageSearchSettings(testConfig)
        config.apoBufferFetchSize shouldBe 75
        config.bufferRatio shouldBe 1.2
        config.degradeBufferRatio shouldBe 1.0
        config.maxFetchCount shouldBe 4
        config.maxFetchCountDegraded shouldBe 1
        config.maxPriceBufferCount shouldBe 225
        config.maxPriceBufferCountDegraded shouldBe 90
        config.maxPageBuffer shouldBe 45
        config.priceBufferRatio shouldBe 3
        config.priceBufferPage shouldBe 5
        config.roomAmenityFetchSize shouldBe 100
        config.fiftyPercentDealsFetchSize shouldBe 270

      }
    }
    "elastic-setting" should {
      "parse the config string correctly" in {

        val config = ElasticSettings(testConfig)
        config.batchSize shouldBe 4000
        config.cityResultSize shouldBe 1000
        config.clusterName shouldBe "qaelastic"
        config.connectionString shouldBe "http://qa.papi-es-docker.svc.cluster.local:9200"
        config.fallbackEnable shouldBe true
        config.hotelIdsPerDistanceFetching shouldBe 1000
        config.hotelIdsPerReviewFetching shouldBe 1000
        config.index shouldBe "hotels"
        config.indexB shouldBe "bhotels"
        config.indextype shouldBe "hotels"
        config.polygonResultSize shouldBe 1000
        config.retries shouldBe 1
        config.retryBackoff shouldBe 50.milliseconds
        config.requestTimeout shouldBe 20000.milliseconds

      }
    }

    "clusterService.bathInformation" should {
      "parse the config string correctly" in {

        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "bathInformation"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false // set as false in cluster.conf though overriden by consul
        config.httpEndpoint shouldBe uri"http://host:80/api/property/bathInformation"
        config.serviceName shouldBe "content"

        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerBathInformation"
        config.timeout shouldBe 5000.milliseconds

      }
    }
    "clusterService.checkInOut" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "checkInOut")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/checkInOut"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerCheckInOut"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.contentSummary" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "contentSummary")
        config.chunkSize shouldBe 16
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/properties"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerContentSummary"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.contentVersion" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "contentVersion")
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/version"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerContentVersion"
        config.timeout shouldBe 1000.milliseconds
      }
    }
    "clusterService.dragonfruitV2" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "dragonfruitV2")
        config.chunkSize shouldBe 4
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/Api/GetProperty"
        config.serviceName shouldBe "dragonfruit"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerDFv2"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.dragonfruitVersion" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "dragonfruitVersion"
        )
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/version"
        config.serviceName shouldBe "dragonfruit"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerDFVersion"
        config.timeout shouldBe 1000.milliseconds
      }
    }
    "clusterService.engagement" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "engagement")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/engagement"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerEngagement"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.experience" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "experience")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/experiences"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerExperience"
        config.timeout shouldBe 10000.milliseconds
      }
    }

    "clusterService.features" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "features")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/features"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerFeature"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.highlight" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "highlight")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/highlights"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerHighlight"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.hotelInformationSummary" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "hotelInformationSummary"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/summary"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerHotelInformationSummary"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.hotelinfo" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "hotelinfo")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/information"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerHotelInfo"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.images" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "images")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/images"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerImage"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.locationInformation" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "locationInformation"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/localInformation"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerLocalInformation"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.nonHotelAccommodation" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "nonHotelAccommodation"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/nonHotelAccommodation"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerNonHotelAccommodation"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.personalizedInformation" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "personalizedInformation"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/personalized"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerPersonalizedInformation"
        config.timeout shouldBe 1000.milliseconds
      }
    }
    "clusterService.rateCategories" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "rateCategories")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/rateCategories"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerRateCategories"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.rateCategory" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "rateCategory")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/rateCategory"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerRateCategory"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.reviewComment" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "reviewComment")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/commentary"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewComment"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.reviewCounters" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "reviewCounters")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/counters"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewCounters"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.reviewFilters" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "reviewFilters")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/filters"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewFilters"
        config.timeout shouldBe 10000.milliseconds
      }
    }
    "clusterService.reviewPositiveMentions" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "reviewPositiveMentions"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/positiveMentions"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewPositiveMentions"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.reviewScore" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "reviewScore")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/demographics"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewScore"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.reviewSnippet" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "reviewSnippet")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/summaries"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewSnippet"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.reviewTotal" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "reviewTotal")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/reviews/cumulative"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerReviewTotal"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.roomDetail" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "roomDetail")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/rooms"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerRoomDetail"
        config.timeout shouldBe 1000.milliseconds
      }
    }
    "clusterService.synopsis" should {
      "parse the config string correctly" in {
        val config =
          ClusterServiceSettings.apply(testConfig.getConfig("clusterService"), "synopsis")
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/synopsis"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerSynopsis"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "clusterService.transportationInformation" should {
      "parse the config string correctly" in {
        val config = ClusterServiceSettings.apply(
          testConfig.getConfig("clusterService"),
          "transportationInformation"
        )
        config.chunkSize shouldBe 8
        config.isCircuitBreakerEnabled shouldBe false
        config.httpEndpoint shouldBe uri"http://host:80/api/property/transportationInformation"
        config.serviceName shouldBe "content"
        config.recoverOptionalCall shouldBe true
        config.retries shouldBe 2
        config.routerName shouldBe "routerTransportationInformation"
        config.timeout shouldBe 5000.milliseconds
      }
    }
    "ag-http-client.content" should {
      "parse the config string correctly" in {
        val config = ServiceMeshSettingsBuilder("content", testConfig).build()
        config.serviceName shouldBe "content"
        config.discoveryService shouldBe StaticListDiscoveryService(
          Seq(Host("propertyapicontentdocker-qa.privatecloud.qa.agoda.is", 80))
        )
        config.roundRobinStrategy.totalWeight shouldBe 100
        config.roundRobinStrategy.incrementOnSuccess shouldBe Some(10)
        testConfig.getConfig("ag-http-client.mesh.services.content.request").getInt("maxAttempts") shouldBe 1

      }
    }
    "ag-http-client.dragonfruit" should {
      "parse the config string correctly" in {
        val config = ServiceMeshSettingsBuilder("dragonfruit", testConfig).build()
        config.serviceName shouldBe "dragonfruit"
        config.discoveryService shouldBe StaticListDiscoveryService(
          Seq(Host("qa.propertyapipricingdocker.svc", 80))
        )
        config.roundRobinStrategy.totalWeight shouldBe 100
        config.roundRobinStrategy.incrementOnSuccess shouldBe Some(10)
        testConfig.getConfig("ag-http-client.mesh.services.dragonfruit.request").getInt("maxAttempts") shouldBe 1

      }
    }
    "ag-http-client.jarvis" should {
      "parse the config string correctly" in {
        val config = ServiceMeshSettingsBuilder("jarvis", testConfig).build()
        config.serviceName shouldBe "jarvis"
        config.discoveryService shouldBe StaticListDiscoveryService(
          Seq(Host("jarvis-stable-prelive.privatecloud.hk.agoda.is", 80))
        )
        config.roundRobinStrategy.totalWeight shouldBe 100
        config.roundRobinStrategy.incrementOnSuccess shouldBe Some(10)
        testConfig.getConfig("ag-http-client.mesh.services.jarvis.request").getInt("maxAttempts") shouldBe 1

      }
    }
    "ag-http-client.npcapi" should {
      "parse the config string correctly" in {
        val config = ServiceMeshSettingsBuilder("npcapi", testConfig).build()
        config.discoveryService shouldBe StaticListDiscoveryService(
          Seq(Host("npces-masterqa-master.privatecloud.qa.agoda.is", 80))
        )
        config.roundRobinStrategy.incrementOnSuccess shouldBe Some(10)
        config.roundRobinStrategy.totalWeight shouldBe 100
        testConfig.getConfig("ag-http-client.mesh.services.npcapi.request").getInt("maxAttempts") shouldBe 1

      }
    }
    "ag-http-client.priceStreamPool" should {
      "parse the config string correctly" in {
        val config = ServiceMeshSettingsBuilder("priceStreamPool", testConfig).build()
        config.serviceName shouldBe "priceStreamPool"
        config.discoveryService shouldBe StaticListDiscoveryService(
          Seq(Host("pricestreamapidocker-qa.privatecloud.qa.agoda.is", 80))
        )
        config.roundRobinStrategy.incrementOnSuccess shouldBe Some(10)
        config.roundRobinStrategy.totalWeight shouldBe 100
        testConfig
          .getConfig("ag-http-client.mesh.services.priceStreamPool.request")
          .getInt("maxAttempts") shouldBe 1

      }
    }
  }

}
