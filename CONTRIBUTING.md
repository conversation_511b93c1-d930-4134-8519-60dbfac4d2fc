# Contributing to PAPI Search

We welcome changes from anyone to PAPI, and we encourage **small incremental changes**. To ensure fast and efficient review, please respect the guidelines outlined below.

## Table of Contents

- [Prerequisites](#prerequisites)
- [Development Guidelines](#development-guidelines)
- [Making Changes](#making-changes)
- [Submitting Your Changes](#submitting-your-changes)
- [Code Review Process](#code-review-process)
- [Approval and Merge Process](#approval-and-merge-process)

## Prerequisites

Before you start contributing, ensure you have:

- **JIRA ID**: Required on every branch and merge request title
- **JIRA Story**: A detailed story describing the issue you are solving
- **Design Review**: Involve a PAPI Developer in design review and test design (strongly encouraged for all MRs, mandatory for first-time contributors)
- **Test Cases**: Include test cases in your JIRA acceptance criteria
- **Understanding**: Read our [README](README.md) to understand how to run and debug PAPI

## Development Guidelines

### Coding Style

Follow the latest [Scala Style Guide](https://agoda.atlassian.net/wiki/spaces/Platform/pages/305990011/Scala+Style).

If you want to add something to the style guide, let's have a discussion with the team in #it-propertyapi-search. When possible, update the `scalastyle-config` to match the guidelines.

### Best Practices

Follow these guidelines for high-quality contributions:

- **[MR Authoring Guidelines](https://agoda.atlassian.net/wiki/spaces/Platform/pages/305969470/MR+authoring)**: Make your MR shine!
- **[Reviewer Guidelines](https://agoda.atlassian.net/wiki/spaces/Platform/pages/305998374/MR+reviewing)**: Understand the review process

## Making Changes

### Branch Creation

1. Clone this repository
2. Create your branch from `develop` (our default development branch)
3. Use format: `[JIRA-ID]-meaningful-branch-name`

### Development Process

1. Make your changes following the coding guidelines
2. Ensure all unit tests pass (check [this section](https://gitlab.agodadev.io/IT-Platform/propertyapi#unit-test))
3. Add unit tests for your changes
4. Test your changes locally before submitting

## Submitting Your Changes

### Creating a Merge Request

When ready to submit your changes:

1. **Title Format**: Use `[JIRA-ID] Meaningful Title` or `[JIRA-ID] [EXP] Meaningful Title`
2. **Fill MR Template**: Add all descriptions the team needs to know
3. **Ensure CI Passes**: Make sure all CI checks are green before requesting review
4. **Label for Review**: Mark with `Ready for Review` when ready

### MR Requirements

Your MR must include:

- **Unit Tests**: If your change doesn't have unit test coverage, please add it
- **Functional Tests**: Add at least 1 [functional test](functional-test) if applicable
- **System Integration Tests**: Include if your change requires them
- **Proper Logging**: 
  - Info for normal flow
  - Debug for debugging
  - Warning/Error/Fatal when necessary
- **Metrics**: Add metrics for any behavior worth monitoring

## Code Review Process

### What We Check During Review

**[Complete Review Guidelines](https://agoda.atlassian.net/wiki/spaces/Platform/pages/305998374/PR+reviewing)**

#### Code Quality
- **Readability**: Can you and your teammates understand the code after 1 year?
- **Efficiency**: Can it be written in a better/shorter way?
- **Performance**: Could this code cause performance degradation?
- **Logic**: Have all use cases been handled?

#### Technical Standards
- **Coding Conventions**:
  - S.O.L.I.D. principles
  - D.R.Y. principle
  - Decouple logic from main flow using services
  - Functional style (no side effects, minimal mutable state)
  - Proper namespaces and structure
  - Consistent naming conventions
  - Appropriate function length
  - No hardcoded config parameters
  - No magic strings

#### Experiment Considerations
- **Allocation**: Is it allocated correctly?
- **Bias**: Can it be biased by another experiment?

### Reviewer Guidelines

#### DO ✅

- **Timely Response**: Comment or approve within 8–16 working hours when assigned after add `Ready to Review`
- **Understand Scope**: Assess change criticality before review
- **Allocate Time**: Spend more time on critical MRs than scripts or test changes
- **Seek Clarity**: Request additional descriptions when needed
- **Hands-on Review**: Pull MR branches to your IDE when clarification needed when you need better inspection
- **Verify Metrics**: Ensure new measurements have appropriate tags and sampling rates
- **Check Configuration**: Confirm new configs are documented with dev/prod settings and unit tests
- **Validate Testing**: Confirm flow changes are supported by functional and correct test names and testing covering all scenarios

#### DON'T ❌

- **Whitelabel Features**: Don't accept MRs with new whitelabel features without ACM test verification
- **Field Changes**: Don't approve MRs with DF/content field passing without functional tests
- **Mandatory Fields**: Don't accept MRs upgrading DF/Content with mandatory fields - request optional fields downstream
- **Enum Changes**: Don't approve enum library changes without proper `this(0)` constructor
- **Elastic Filters**: Don't accept elastic filters without elasticsearch implementation first
- **Filter Mapping**: Don't approve new filters without proper mapping per [specification](https://agoda.atlassian.net/wiki/spaces/Platform/pages/305959632)
- **Experimental Features**: Don't accept experimental features without proper AB testing and experiment allocation
- **External Dependencies**: Don't approve external service dependencies without circuit breakers, timeouts, and fallback mechanisms

## Approval and Merge Process

### Requirements for Merge

Your MR must meet these criteria:

1. **CI Status**: All CI checks must pass (green checks on GitLab)
2. **Review Label**: Must be labeled `Ready for Review`
3. **Approval**: At least 1 PAPI developer approval via GitLab
4. **Database Changes**: DB team approval required for Stored Procedure changes

### Merge Process

Once all requirements are met, contibutor can trigger the merge train by adding the `Ready to Merge` tag. Your MR will be automatically merged and included in the next deployment.

**Important**: If you have dependencies that prevent immediate deployment, mark your MR as `Do Not Merge`.