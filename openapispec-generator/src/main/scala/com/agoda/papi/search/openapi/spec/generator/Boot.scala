package com.agoda.papi.search.openapi.spec.generator

import com.agoda.papi.search.api.http.rest.openapi.endpointdocs.MainDocs
import com.typesafe.scalalogging.LazyLogging
import scaldi.Injectable
import sttp.apispec.openapi.OpenAPI
import sttp.apispec.openapi.circe.yaml.RichOpenAPI

import java.io.{ File, FileWriter }

object Boot extends App with Injectable with LazyLogging {
  implicit val injector = new com.agoda.papi.search.api.binding.RestApiModule

  val mainDocs = inject[MainDocs]

  logger.info("Generating PAPI OpenAPI Doc")

  val docs: OpenAPI = mainDocs.docs

  val version = sys.env.get("VERSION").filter(_.nonEmpty).getOrElse("1.0")
  val docsYaml = docs.info(docs.info.version(version)).toYaml3_0_3

  logger.info(docsYaml)

  val fileWriter = new FileWriter(new File("openapi_spec.yml"))
  fileWriter.write(docsYaml)
  fileWriter.close()
}
