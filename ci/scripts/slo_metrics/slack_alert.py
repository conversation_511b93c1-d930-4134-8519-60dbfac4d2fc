import requests
import json
import os

# Slack Bot Token and Channel ID
slack_token = os.environ.get("SLACK_TOKEN")
channel_id = os.environ.get("SLACK_CHANNEL_ID", "C2AG5MFT7")
slack_payload = {}

# Prepare Slack payload
def slack_alert(message):
    print(f"[SLACK] 📤 Sending alert to channel {channel_id}")

    slack_payload = {
        "channel": channel_id,
        "text": message
    }

    try:
        response = requests.post(
            "https://slack.com/api/chat.postMessage",
            headers={
                "Authorization": f"Bearer {slack_token}",
                "Content-Type": "application/json; charset=utf-8"
            },
            data=json.dumps(slack_payload)
        )

        # Check if the Slack message was sent successfully
        if response.status_code == 200:
            response_data = response.json()
            if response_data.get('ok'):
                print(f"[SLACK] ✅ Slack API confirmed delivery")
            else:
                print(f"[SLACK] ❌ Slack API error: {response_data.get('error', 'Unknown error')}")
        else:
            print(f"[SLACK] ❌ Failed to send alert. HTTP Status: {response.status_code}")
            print(f"[SLACK] Response: {response.text}")
    except Exception as e:
        print(f"[SLACK] ❌ Exception occurred while sending alert: {str(e)}")
