from urllib.parse import urljoin

import requests
import datetime
import os

import slack_alert

from datetime import datetime, timedelta

components = os.getenv('COMPONENTS', 'propertyapisearchdocker').replace(' ','').split(',')

slo_host = os.getenv('SLO_HOST', 'http://slo-api-main.privatecloud.hk.agoda.is/grafana/v2').rstrip('/') + '/'

# Team to tag in Slack alerts
slack_team_tag = os.getenv('SLACK_TEAM_TAG', 'SKDEC21RR')

# Endpoints
slo_details = 'info/slo'
slo_filter = 'filter/slo'
timeseries_outage = 'timeseries/outage'

print(f"[SLO] 🚀 Starting SLO monitoring for components: {components}")
print(f"[SLO] 🌐 Using SLO host: {slo_host}")

def get_slo_metrics():
    slo_components = {}
    for component in components:
        params = {
            'components': component
        }
        request_url = urljoin(slo_host, slo_filter)
        response = requests.get(request_url, params=params)
        if response.status_code == 200:
            slo_components[component] = response.json()
            print(f"[SLO] ✅ Successfully retrieved SLO metrics for {component}")
        else:
            print(f"[SLO] ❌ Failed to get SLO metrics for {component}")

    return slo_components

def get_slo_details(slo_name):
    # Get the SLO details for the given SLO
    request_url = urljoin(slo_host, slo_details)
    body = {
        "slo_full_name": slo_name,
        "quarter_id": get_current_quarter(),
        "interval_ms": 1,
        "included_whitelist": True
    }
    response = requests.post(request_url, json=body)
    if response.status_code == 200:
        return response.json()
    else:
        print(f"[SLO] ❌ Failed to get SLO details for {slo_name}")
        return None

def get_adjusted_error_budget(slo_name):
    slo_data = get_slo_details(slo_name)
    error_budget_remaining = slo_data[0]['qt_error_budget_mins']
    days_remaining_in_quarter = remaining_days_in_quarter()
    return error_budget_remaining / days_remaining_in_quarter

def get_outage_on_previous_day(slo_name):
    # Timestamp 25 hours ago in milliseconds
    timestamp_25_hours_ago_ms = int((datetime.now() - timedelta(hours=25)).timestamp() * 1000)

    # Get outage data
    request_url = urljoin(slo_host, timeseries_outage)
    body = {
        "slo_full_name": slo_name,
        "quarter_id": get_current_quarter(),
        "interval_ms": 30000,
    }
    response = requests.post(request_url, json=body)
    outage_sum_25_hours_ago = 0.0
    outage_data = {}
    if response.status_code == 200:
        outage_data = response.json()
    else:
        print(f"[SLO] ❌ Failed to get outage data for {slo_name}")
    # Filter null values and older data-points
    outage_data = list(filter(lambda d: d['outage_sum'] is not None and d['timestamp'] > timestamp_25_hours_ago_ms, outage_data))
    if len(outage_data)<2:
        return -1
    sorted(outage_data, key = lambda d: d['timestamp'])
    # from outage_data, get outage value just after 25 hours value
    outage_sum_25_hours_ago = outage_data[0]['outage_sum']
    latest_outage_sum = outage_data[-1]['outage_sum']
    return latest_outage_sum - outage_sum_25_hours_ago

def get_error_budget_consumed(slo_name):
    # Get the error budget consumed till now
    request_url = urljoin(slo_host, timeseries_outage)
    body = {
        "slo_full_name": slo_name,
        "quarter_id": get_current_quarter(),
        "interval_ms": 60000, # not impactful
        "included_whitelist": True
    }
    response = requests.post(request_url, json=body)
    if response.status_code == 200:
        return response.json()[-1]['outage_sum']
    else:
        print(f"[SLO] ❌ Failed to get error budget consumed")
        return None

def remaining_days_in_quarter():
    # Get the current date (ignoring the time portion)
    today = datetime.today().date()

    # Determine the current quarter and its ending date
    if today.month in (1, 2, 3):
        end_of_quarter = datetime(today.year, 3, 31).date()
    elif today.month in (4, 5, 6):
        end_of_quarter = datetime(today.year, 6, 30).date()
    elif today.month in (7, 8, 9):
        end_of_quarter = datetime(today.year, 9, 30).date()
    else:
        end_of_quarter = datetime(today.year, 12, 31).date()

    # Calculate the difference (only in days)
    days_remaining = (end_of_quarter - today).days
    # Return at least 1 to avoid division by zero in downstream calculations
    return max(days_remaining, 1)

def get_remaining_budget(slo_name):
    slo_data = get_slo_details(slo_name)
    return slo_data[0]['qt_error_budget_mins']

def get_current_quarter():
    current_date = datetime.now()
    current_month = current_date.month
    current_year = current_date.year

    quarter = (current_month - 1) // 3 + 1
    if quarter == 1:
        return f'{current_year}Q1'
    elif quarter == 2:
        return f'{current_year}Q2'
    elif quarter == 3:
        return f'{current_year}Q3'
    else:
        return f'{current_year}Q4'

def get_current_slo(slo_name):
    slo_details_data = get_slo_details(slo_name)
    return slo_details_data[0]['slo_pct_uptoend']

print(f"[SLO] 📊 Current quarter: {get_current_quarter()}")
print(f"[SLO] 📅 Days remaining in quarter: {remaining_days_in_quarter()}")

slo_components = get_slo_metrics()
total_slos_checked = 0
breach_detected = False

# Collect all breaches for consolidated reporting
already_breached_slos = []
daily_budget_breaches = []

for slo_component in slo_components:
    slo_items = slo_components[slo_component]

    print(f"\n[SLO] 🔍 Checking component: {slo_component}")
    print(f"[SLO] 📋 Found {len(slo_items)} SLO(s) to check")

    for slo in slo_items:
        total_slos_checked += 1
        print(f"\n[SLO] 🔎 Analyzing SLO: {slo}")

        remaining_budget = get_remaining_budget(slo)
        error_budget_consumed = get_error_budget_consumed(slo)
        current_slo_performance = get_current_slo(slo)

        print(f"[SLO] 📈 Current SLO Performance: {current_slo_performance:.2f}%")
        print(f"[SLO] 💰 Remaining Error Budget: {remaining_budget} minutes")
        print(f"[SLO] 📊 Total Error Budget Consumed: {error_budget_consumed} minutes")

        # Check if SLO is already breached (negative remaining budget)
        if remaining_budget < 0:
            breach_detected = True
            print(f"[SLO] 🚨 ALREADY BREACHED! {slo} has exceeded its error budget!")
            print(f"[SLO] ⚠️  Remaining budget: {remaining_budget} minutes (NEGATIVE)")

            # Collect breach info for consolidated message
            already_breached_slos.append({
                'slo': slo,
                'remaining_budget': remaining_budget,
                'error_budget_consumed': error_budget_consumed,
                'current_slo_performance': current_slo_performance
            })
            continue

        # For non-breached SLOs, check daily budget consumption
        adjusted_error_budget = get_adjusted_error_budget(slo)
        budget_consumed_in_last_24_hours = get_outage_on_previous_day(slo)

        print(f"[SLO] 📉 Budget consumed in last 24h: {budget_consumed_in_last_24_hours} minutes")
        print(f"[SLO] 🎯 Adjusted daily error budget: {adjusted_error_budget:.2f} minutes")

        if budget_consumed_in_last_24_hours >= adjusted_error_budget:
            breach_detected = True
            print(f"[SLO] 🚨 DAILY BUDGET BREACH! {slo} exceeds daily budget threshold!")
            print(f"[SLO] ⚠️  Last 24h consumption ({budget_consumed_in_last_24_hours} min) >= Daily budget ({adjusted_error_budget:.2f} min)")

            # Collect breach info for consolidated message
            daily_budget_breaches.append({
                'slo': slo,
                'budget_consumed_in_last_24_hours': budget_consumed_in_last_24_hours,
                'adjusted_error_budget': adjusted_error_budget,
                'remaining_budget': remaining_budget,
                'error_budget_consumed': error_budget_consumed,
                'current_slo_performance': current_slo_performance
            })
        else:
            print(f"[SLO] ✅ {slo} is within acceptable limits")

print(f"\n[SLO] 📋 Summary:")
print(f"[SLO] 🔢 Total SLOs checked: {total_slos_checked}")
if breach_detected:
    print(f"[SLO] 🚨 BREACH STATUS: ❌ One or more SLOs have breaches!")
else:
    print(f"[SLO] 🚨 BREACH STATUS: ✅ All SLOs are within acceptable limits")

# Send consolidated breach alert if any breaches detected
if breach_detected:
    print(f"\n[SLO] 📤 Preparing consolidated breach alert...")

    consolidated_message = f"<!subteam^{slack_team_tag}> 🚨 *SLO MONITORING ALERT - {get_current_quarter()}* 🚨\n\n"

    # Section 1: Already Breached SLOs
    if already_breached_slos:
        consolidated_message += "🔥 *CRITICAL: SLOs ALREADY BREACHED THIS QUARTER* 🔥\n"
        consolidated_message += f"The following {len(already_breached_slos)} SLO(s) have already exceeded their error budget:\n\n"

        for breach in already_breached_slos:
            consolidated_message += f"• *{breach['slo']}*\n"
            consolidated_message += f"  - Status: *ALREADY BREACHED*\n"
            consolidated_message += f"  - Remaining Budget: *{breach['remaining_budget']} minutes* (NEGATIVE)\n"
            consolidated_message += f"  - Total Consumed: {breach['error_budget_consumed']} minutes\n"
            consolidated_message += f"  - Current Performance: {breach['current_slo_performance']:.2f}%\n\n"

        consolidated_message += "🔥 *IMMEDIATE ACTION REQUIRED* - These SLOs have already failed for the current quarter!\n\n"
        consolidated_message += "---\n\n"

    # Section 2: Daily Budget Breaches
    if daily_budget_breaches:
        consolidated_message += "⚠️ *WARNING: DAILY BUDGET BREACHES DETECTED* ⚠️\n"
        consolidated_message += f"The following {len(daily_budget_breaches)} SLO(s) exceeded their daily error budget and are on track to fail:\n\n"

        for breach in daily_budget_breaches:
            consolidated_message += f"• *{breach['slo']}*\n"
            consolidated_message += f"  - Last 24h Consumption: {breach['budget_consumed_in_last_24_hours']} minutes\n"
            consolidated_message += f"  - Daily Budget Limit: {breach['adjusted_error_budget']:.2f} minutes\n"
            consolidated_message += f"  - Remaining Quarter Budget: {breach['remaining_budget']} minutes\n"
            consolidated_message += f"  - Total Consumed: {breach['error_budget_consumed']} minutes\n"
            consolidated_message += f"  - Current Performance: {breach['current_slo_performance']:.2f}%\n\n"

        consolidated_message += "📈 *REVIEW RECOMMENDED* - Immediate corrective actions needed to prevent SLO failure.\n\n"

    # Footer
    consolidated_message += f"📊 *Summary:* {len(already_breached_slos)} already breached, {len(daily_budget_breaches)} daily budget breaches\n"
    consolidated_message += f"📅 *Quarter:* {get_current_quarter()} ({remaining_days_in_quarter()} days remaining)"

    print(f"[SLO] 📤 Sending consolidated alert with {len(already_breached_slos)} already breached and {len(daily_budget_breaches)} daily budget breaches")
    slack_alert.slack_alert(consolidated_message)
else:
    print(f"[SLO] ✅ No breaches detected - no alert needed")

print(f"\n[SLO] 🏁 SLO monitoring completed!")
