include:
  - local: '/ci/templates/common.gitlab-ci.yml'
  - local: '/ci/templates/workflow.gitlab-ci.yml'

stages:
  - monitor

variables:
  COMPONENTS: "propertyapisearchdocker"

# Common variables for SLO monitoring jobs
.slo_monitoring_common:
  extends: [.python]
  stage: monitor
  variables:
    # Default values - can be overridden via CI/CD variables
    COMPONENTS: $COMPONENTS
    SLO_HOST: "http://slo-api-main.privatecloud.hk.agoda.is/grafana/v2"
    # Team to notify in Slack alerts - Slack user group ID
    SLACK_TEAM_TAG: "SKDEC21RR"
  before_script:
    - pip install requests
    - export PYTHONPATH=$PYTHONPATH:$CI_PROJECT_DIR/ci/scripts/slo_metrics
    - cd $CI_PROJECT_DIR/ci/scripts/slo_metrics
  rules:
    - if: '$CI_PIPELINE_SOURCE == "schedule" && $SCHEDULE_SLO_MONITORING == "true"'

slo_error_budget_monitoring:
  extends: .slo_monitoring_common
  script:
    - |
      echo "Starting SLO error budget monitoring for components: $COMPONENTS"
      echo "Using SLO host: $SLO_HOST"
      python outage_data_parser.py
      echo "SLO error budget monitoring completed"
  allow_failure: true
  artifacts:
    when: always
    expire_in: 1 week
  retry:
    max: 2
    when: script_failure
