service: propertyapisearchdocker
monitoring:
  slackChannel: dob_papi_search
  monitoringPeriodSeconds: {{DEPLOY_MONITOR_SECONDS}}
orderPriority:
  affiliate: 1
  bot: 1
  fe: 1
  fe-search: 1
  internal: 1
environments:
  affiliate:
    resourcePool: mesh
    tier: prod
    artifact:
      image:
        repository: papi/search/propertyapi/release
        tag: {{PC_IMAGE_TAG}}
    healthCheck:
      startup:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 60
        timeoutSeconds: 1
        periodSeconds: 5
        failureThreshold: 40
        successThreshold: 1
      readiness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
      liveness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
    resources:
      requests:
        cpu: "10"
        memory: 22Gi
      limits:
        memory: 26Gi
    envs:
      DC: ${AG_DC}
      DEPLOYMENT: kubernetes
      ENVIRONMENT: affiliate
      TZ: Asia/Bangkok
      VAULT_ADDR: ${VAULT_ADDR}
      VAULT_TOKEN: ${VAULT_TOKEN}
    ports:
      - number: 80
        protocol: http
    mountPaths:
      - path: /var/log/agoda/
      - path: /opt/propertyapi/sflogs
      - path: /var/propertyapi/consul-cache
      - path: /var/tmp/vault
    terminationDurationSeconds: 30
    consulAgent: true
    vaultAgent: true
    telegraf:
      enabled: false
    opa:
      enabled: false
      bundlePaths: null
    gatekeeper:
      enabled: false
    okta:
      enabled: false
    observability:
      enabled: false
    pyroscope:
      enabled: false
    ingresses:
      - port: 80
        hosts:
          - name: propertyapisearchdocker-affiliate.${AG_DC}.agoda.is
        consulRegistry:
          enabled: true
          consulService: papi-search
          tags:
            - X
            - affiliate
    rollout:
      strategy:
        canarySteps:
          - 0%
          - 20%
          - 100%
      targets:
        am:
          orderPriority: 1
          replicas: 16
        as:
          orderPriority: 2
          replicas: 16
        hk:
          orderPriority: 1
          replicas: 16
        sg:
          orderPriority: 2
          replicas: 16
  bot:
    resourcePool: mesh
    tier: prod
    artifact:
      image:
        repository: papi/search/propertyapi/release
        tag: {{PC_IMAGE_TAG}}
    healthCheck:
      startup:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 60
        timeoutSeconds: 1
        periodSeconds: 5
        failureThreshold: 40
        successThreshold: 1
      readiness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
      liveness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
    resources:
      requests:
        cpu: "10"
        memory: 22Gi
      limits:
        memory: 26Gi
    envs:
      DC: ${AG_DC}
      DEPLOYMENT: kubernetes
      ENVIRONMENT: bot
      TZ: Asia/Bangkok
      VAULT_ADDR: ${VAULT_ADDR}
      VAULT_TOKEN: ${VAULT_TOKEN}
    ports:
      - number: 80
        protocol: http
    mountPaths:
      - path: /var/log/agoda/
      - path: /opt/propertyapi/sflogs
      - path: /var/propertyapi/consul-cache
      - path: /var/tmp/vault
    terminationDurationSeconds: 30
    consulAgent: true
    vaultAgent: true
    telegraf:
      enabled: false
    opa:
      enabled: false
      bundlePaths: null
    gatekeeper:
      enabled: false
    okta:
      enabled: false
    observability:
      enabled: false
    pyroscope:
      enabled: false
    ingresses:
      - port: 80
        hosts:
          - name: propertyapisearchdocker-bot.${AG_DC}.agoda.is
        consulRegistry:
          enabled: true
          consulService: papi-search
          tags:
            - Z
            - bot
    rollout:
      strategy:
        canarySteps:
          - 0%
          - 20%
          - 100%
      targets:
        am:
          orderPriority: 1
          replicas: 0
        as:
          orderPriority: 2
          replicas: 12
        hk:
          orderPriority: 1
          replicas: 16
        sg:
          orderPriority: 2
          replicas: 0
  fe:
    resourcePool: mesh
    tier: prod
    artifact:
      image:
        repository: papi/search/propertyapi/release
        tag: {{PC_IMAGE_TAG}}
    healthCheck:
      startup:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 60
        timeoutSeconds: 1
        periodSeconds: 5
        failureThreshold: 40
        successThreshold: 1
      readiness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
      liveness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
    resources:
      requests:
        cpu: "10"
        memory: 22Gi
      limits:
        memory: 26Gi
    envs:
      DC: ${AG_DC}
      DEPLOYMENT: kubernetes
      ENVIRONMENT: fe
      TZ: Asia/Bangkok
      VAULT_ADDR: ${VAULT_ADDR}
      VAULT_TOKEN: ${VAULT_TOKEN}
    ports:
      - number: 80
        protocol: http
    mountPaths:
      - path: /var/log/agoda/
      - path: /opt/propertyapi/sflogs
      - path: /var/propertyapi/consul-cache
      - path: /var/tmp/vault
    terminationDurationSeconds: 30
    consulAgent: true
    vaultAgent: true
    telegraf:
      enabled: false
    opa:
      enabled: false
      bundlePaths: null
    gatekeeper:
      enabled: true
    okta:
      enabled: false
    observability:
      enabled: false
    pyroscope:
      enabled: false
    ingresses:
      - port: 80
        hosts:
          - name: propertyapisearchdocker-fe.${AG_DC}.agoda.is
        consulRegistry:
          enabled: true
          consulService: papi-search
          tags:
            - A
            - fe
    rollout:
      strategy:
        canarySteps:
          - 0%
          - 20%
          - 100%
      targets:
        am:
          orderPriority: 1
          replicas: 35
        as:
          orderPriority: 2
          replicas: 35
        hk:
          orderPriority: 1
          replicas: 50
        sg:
          orderPriority: 2
          replicas: 50
  fe-search:
    resourcePool: mesh
    tier: prod
    artifact:
      image:
        repository: papi/search/propertyapi/release
        tag: {{PC_IMAGE_TAG}}
    healthCheck:
      startup:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 60
        timeoutSeconds: 1
        periodSeconds: 5
        failureThreshold: 40
        successThreshold: 1
      readiness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
      liveness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
    resources:
      requests:
        cpu: "10"
        memory: 22Gi
      limits:
        memory: 26Gi
    envs:
      DC: ${AG_DC}
      DEPLOYMENT: kubernetes
      ENVIRONMENT: fe-search
      TZ: Asia/Bangkok
      VAULT_ADDR: ${VAULT_ADDR}
      VAULT_TOKEN: ${VAULT_TOKEN}
    ports:
      - number: 80
        protocol: http
    mountPaths:
      - path: /var/log/agoda/
      - path: /opt/propertyapi/sflogs
      - path: /var/propertyapi/consul-cache
      - path: /var/agoda/dump/
      - path: /var/tmp/vault
    terminationDurationSeconds: 30
    consulAgent: true
    vaultAgent: true
    telegraf:
      enabled: false
    opa:
      enabled: false
      bundlePaths: null
    gatekeeper:
      enabled: true
    okta:
      enabled: false
    observability:
      enabled: false
    pyroscope:
      enabled: false
    ingresses:
      - port: 80
    rollout:
      strategy:
        canarySteps:
          - 0%
          - 20%
          - 100%
      targets:
        am:
          orderPriority: 1
          replicas: 70
        as:
          orderPriority: 2
          replicas: 70
        hk:
          orderPriority: 1
          replicas: 105
        sg:
          orderPriority: 2
          replicas: 105
  internal:
    resourcePool: stream
    tier: prod
    artifact:
      image:
        repository: papi/search/propertyapi/release
        tag: {{PC_IMAGE_TAG}}
    healthCheck:
      startup:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 60
        timeoutSeconds: 1
        periodSeconds: 5
        failureThreshold: 40
        successThreshold: 1
      readiness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
      liveness:
        httpGet:
          port: 80
          path: /ag-http-server/status
        initialDelaySeconds: 5
        timeoutSeconds: 5
        periodSeconds: 5
        failureThreshold: 10
        successThreshold: 1
    resources:
      requests:
        cpu: "10"
        memory: 22Gi
      limits:
        memory: 26Gi
    envs:
      DC: ${AG_DC}
      DEPLOYMENT: kubernetes
      ENVIRONMENT: internal
      TZ: Asia/Bangkok
      VAULT_ADDR: ${VAULT_ADDR}
      VAULT_TOKEN: ${VAULT_TOKEN}
    ports:
      - number: 80
        protocol: http
    mountPaths:
      - path: /var/log/agoda/
      - path: /opt/propertyapi/sflogs
      - path: /var/propertyapi/consul-cache
      - path: /var/agoda/dump/
      - path: /var/tmp/vault
    terminationDurationSeconds: 30
    consulAgent: true
    vaultAgent: true
    telegraf:
      enabled: false
    opa:
      enabled: false
      bundlePaths: null
    gatekeeper:
      enabled: false
    okta:
      enabled: false
    observability:
      enabled: false
    pyroscope:
      enabled: false
    ingresses:
      - port: 80
        hosts:
          - name: papi-vc.${AG_DC}.agoda.local
        consulRegistry:
          enabled: true
          consulService: papi-search
          tags:
            - VC
            - internal
    rollout:
      strategy:
        canarySteps:
          - 0%
          - 20%
          - 100%
      targets:
        am:
          orderPriority: 1
          replicas: 4
        as:
          orderPriority: 2
          replicas: 4
        hk:
          orderPriority: 1
          replicas: 55
        sg:
          orderPriority: 2
          replicas: 49