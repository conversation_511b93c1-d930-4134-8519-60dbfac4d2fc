package request

import java.util.UUID
import api.request.{ ExternalLoyaltyProfile, FeatureFlag, SimulateRequestData }
import com.agoda.commons.traffic.TrafficInfo
import com.agoda.content.models.db.image.ImageSizeRequest
import com.agoda.localization.Localization
import com.agoda.styx.botprofile.{ <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, BotProfileParser, BotTypes, TrafficType }
import com.fasterxml.jackson.annotation.{ JsonIgnore, JsonIgnoreProperties, JsonProperty }
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import enumerations.{ RecommendationFilterComparisonType, RecommendationFilterFieldType, RecommendationType }
import enumeratum.{ Enum, EnumEntry }
import logger.Image
import models.db.SupplierId
import models.pricing.enums.{ ApplyType, PaymentModel }
import models.starfruit.{ FilterRequest => _, _ }
import org.joda.time.{ DateTime, Days }
import serializer.Serialization
import suggestprice.SuggestPrice
import types.{ CidType, CityIdType, HotelAreaIdType }
import com.agoda.content.models._
import com.agoda.content.models.request.sf.{
  ContentEscapeRateCategoriesRequest,
  ContentRateCategoriesRequest,
  TopicsRequest
}
import com.agoda.core.search.models.Asq
import com.agoda.core.search.models.enumeration.{ FunnelType, SearchStatuses, Sorting, TravellerType }
import com.agoda.core.search.models.request.{
  FilterDefinition,
  GeoPoint,
  Packaging,
  PollingInfoRequest,
  SearchHistory,
  TrackSteps
}
import com.agoda.core.search.models.response.SearchStatusAndCriteria
import types.{ CityIdType => _, LandmarkIdType, _ }
import com.agoda.core.search.models.enumeration
import com.agoda.core.search.utils.{ CartHelper, LosGroup }
import com.agoda.upi.models.request.CartBaseRequest
import models.consts.PlatformID
import util.RequestUtils

import scala.util.Left

/** Created by srattanakana on 5/25/2016 AD.
  */

/** * Property request section
  */

@SerialVersionUID(1L)
sealed abstract class SearchType(val i: String) extends EnumEntry {
  override def hashCode: Int = i.hashCode

  override def equals(obj: Any): Boolean = obj match {
    case c: SearchType => c.i == i
    case _             => false
  }

  def this() = this(hashCode.toString)
}

@SerialVersionUID(1L)
object SearchTypes extends Enum[SearchType] {
  val values                                   = findValues.toIndexedSeq
  val fields                                   = values.map(x => (x.i, x)).toMap
  lazy val getConditions: String => SearchType = fields.getOrElse(_, SearchTypes.None)
  case object Property             extends SearchType("Property")
  case object Properties           extends SearchType("Properties")
  case object SmallProperties      extends SearchType("SmallProperties")
  case object Complex              extends SearchType("Complex")
  case object ComprehensiveComplex extends SearchType("ComprehensiveComplex")
  case object Booking              extends SearchType("Booking")
  case object Recommendations      extends SearchType("Recommendations")
  case object None                 extends SearchType("None")
}

case class ForcedExperiment(id: String, variant: String)

@SerialVersionUID(1L)
case class Experiments(forceByVariant: Option[String], forceByExperiment: Option[Vector[ForcedExperiment]])

@deprecated
@SerialVersionUID(1L)
case class Bot(botType: String, isKnownBot: Boolean)

/** Mandatory request context
  *
  * @param isMSE
  *   New flag for MSE display
  * @param storeFrontId
  *   Moved from [[PropertyPricing.storeFrontId]]
  * @param tracingContext
  *   Serialized tracing context provided by client, used to continue tracing with provided TraceID.
  */
@JsonIgnoreProperties(value = Array("ignoreValidation"), ignoreUnknown = true)
@SerialVersionUID(1L)
case class PropertyContext(
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    @JsonProperty("propertyIds")
    propertyIds: List[Long] = List.empty,
    searchId: String = UUID.randomUUID().toString,
    locale: String,
    userId: Option[String] = None,
    showCMS: Option[Boolean] = Some(false),
    cid: CidType,
    memberId: Long,
    origin: String,
    platform: Int,
    encryptedGeolocation: Option[String] = None,
    debug: Option[Boolean] = None,
    abTest: Option[List[ABTest]] = None,
    experiments: Option[Experiments] = None,
    @JsonProperty("ignoreValidation")
    ignoreValidation: Boolean = false,
    isAPO: Option[Boolean] = None,
    isNeedPriceMessaging: Option[Boolean] = None,
    isAllowBookOnRequest: Option[Boolean] = None,
    @deprecated
    correlationId: Option[String] = None,
    @deprecated
    userAgent: Option[String] = None,
    @deprecated
    bot: Option[Bot] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    pageTypeId: Option[Long] = None,
    isPriusExcludedPointsMax: Option[Boolean] = None,
    isMSE: Option[Boolean] = Some(false),
    searchType: Option[enumeration.SearchType] = None,
    searchHistory: Option[Seq[SearchHistory]] = Some(Vector.empty),
    trafficInfo: Option[TrafficInfo] = None,
    stepsResultTrackingEnabled: Option[Boolean] = None,
    stepsResultFilterProperties: Option[List[Long]] = None,
    trackSteps: Option[TrackSteps] = None,
    clientVersion: Option[String] = None,
    clientEndpoint: Option[Int] = Some(-1),
    deviceTypeId: Option[Int] = None,
    @deprecated("no longer used by FE")
    isMseWithoutGroup: Option[Boolean] = None,
    isRetry: Option[Boolean] = None,
    localizations: Option[List[String]] = None,
    ipAddress: Option[String] = None,
    storeFrontId: Option[Int] = None, // Move from pricing.storeFrontId
    mseHotelIds: Option[List[Int]] = None,
    rawUserContext: Option[String] = None,
    whiteLabelKey: Option[String] = None,
    tracingContext: Option[String] = None,
    ppLandingHotelIds: Option[List[Int]] = None,
    searchedHotelIds: Option[List[Int]] = None,
    isGeneratedSearchIdFromEmptyString: Option[Boolean] = Some(false),
    // used for FE SSR polling
    pollingInfo: Option[PollingInfoRequest] = None,
    cartRequest: Option[CartBaseRequest] = None,
    pushId: Option[String] = None,
    partnerClaimToken: Option[String] = None,
    capiToken: Option[String] = None,
    externalPartnerId: Option[String] = None
) {

  val isAPORequest = isAPO.getOrElse(false)

  @JsonIgnore
  val botProfile: Option[BotProfile] =
    try
      trafficInfo.flatMap(
        _.rawBotProfile
          .map(BotProfileParser(Seq(BotTypes.VOLUME, BotTypes.VOLUME_HIDDEN)).parse)
      )
    catch {
      case _ =>
        Option(
          BotProfile(TrafficType.None, Map(), None)
        ) // should never reach this as styx must have covered negative cases
    }

  @JsonIgnore
  val isBot: Boolean = botProfile.exists(_.trafficType != TrafficType.User)

  @JsonIgnore
  val trafficType: String = botProfile.map(_.trafficType.toString).getOrElse(TrafficType.None.toString)

  @JsonIgnore
  val localization: Localization = Localization(locale)

  val measureTags: Map[String, String] = Map(
    "platform"       -> platform.toString,
    "newTrafficType" -> trafficType,
    "isRetry"        -> isRetry.contains(true).toString
  )

}

trait TrackingRequest {
  @JsonIgnore
  def tags: Map[String, String] = Map.empty
}

trait PAPIRequest {

  @JsonIgnore
  def formatted(): String =
    Serialization.jacksonMapper.writeValueAsString(this)

}

trait PAPITrackingRequest extends TrackingRequest with PAPIRequest

@SerialVersionUID(1L)
case class SearchCriteria(
    cityId: Option[CityIdType],
    areaId: Option[HotelAreaIdType],
    landmarkId: Option[LandmarkIdType],
    radiusRequest: Option[RadiusRequest],
    boxRequest: Option[BoxRequest],
    filters: Option[List[FilterDefinition]] = Option(List.empty),
    sorting: Option[Sorting] = Option(Sorting.default)
)

@SerialVersionUID(1L)
final case class RadiusRequest(geoPoint: GeoPoint, distance: Option[Int])

@SerialVersionUID(1L)
final case class BoxRequest(topLeft: GeoPoint, bottomRight: GeoPoint)

@JsonIgnoreProperties(Array("isSSR"))
@SerialVersionUID(1L)
case class PropertyRequest(
    context: PropertyContext,
    searchCriteria: Option[SearchCriteria] = None,
    information: Option[InformationRequest] = None,
    features: Option[FeaturesRequest] = None,
    images: Option[ImagesRequest] = None,
    reviewDemographics: Option[DemographicsRequest] = None,
    reviewCumulative: Option[CumulativeRequest] = None,
    reviewSnippet: Option[SummariesRequest] = None,
    reviewCommentary: Option[CommentaryRequest] = None,
    reviewPositiveMentions: Option[EmptyRequest] = None,
    pricing: Option[PropertyPricing] = None,
    room: Option[RoomRequestPAPI] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    numberOfRecommendations: Option[Long] = None,
    recommendation: Option[Recommendation] = None,
    personalizedRecommendation: Option[Recommendation] = None,
    pastBookingsRecommendation: Option[Recommendation] = None,
    summaryRequest: Option[SummaryRequest] = None,
    localInformation: Option[LocalInformationRequest] = None,
    checkInOut: Option[EmptyRequest] = None,
    synopsis: Option[EmptyRequest] = None,
    engagementRequest: Option[EmptyRequest] = None,
    experienceRequest: Option[ExperienceRequest] = None,
    filterRequest: Option[FilterRequest] = None,
    highlights: Option[HighlightsRequest] = None,
    searchType: Option[SearchType] = Option(SearchTypes.None),
    @JsonProperty("isSSR")
    isSSR: Option[Boolean] = None,
    isNeedPriceMessaging: Option[Boolean] = None,
    booking: Option[BookingRequest] = None,
    @deprecated("weather endpoint removed in content")
    weather: Option[EmptyRequest] = None,
    nonHotelAccommodation: Option[EmptyRequest] = None,
    localization: Option[EmptyRequest] = None,
    featureFlag: Option[FeatureFlagRequest] = None,
    uspRequest: Option[USPRequest] = None,
    topSellingPointRequest: Option[TopSellingPointRequest] = None,
    personalizedInformation: Option[EmptyRequest] = None,
    reviewCounters: Option[EmptyRequest] = None,
    soldOutAlternateDatesRequest: Option[SoldOutAlternateDatesRequest] = None,
    rateCategories: Option[ContentRateCategoriesRequest] = None,
    rateCategory: Option[PAPIRateCategoryRequest] = None,
    personalizedFacilities: Option[Boolean] = None,
    bathInformation: Option[EmptyRequest] = None,
    transportationInformation: Option[EmptyRequest] = None,
    rocketmilesPublishPriceRequest: Option[RocketmilesPublishPriceRequest] = None,
    propertySearchToken: Option[String] = None,
    perOccupancyBreakdownType: Option[ApplyType] = None
) extends PAPITrackingRequest {

  val MaxPropertyIds: Int = 64

  def isCheapestHourlyRoomRequest: Boolean = this.featureFlag.flatMap(_.showCheapestHourlyRate).getOrElse(false)

  def updateCheapestHourlyRoomFlag(hasHourlyRate: Boolean): PropertyRequest =
    if (isCheapestHourlyRoomRequest) {
      val updatedFeatureFlags =
        this.featureFlag.map(featureFlag => featureFlag.copy(showCheapestHourlyRate = Some(hasHourlyRate)))
      this.copy(featureFlag = updatedFeatureFlags)
    } else this

  def getFunnel: FunnelType =
    RequestUtils.getFunnel(Left(featureFlag), pricing.flatMap(_.packaging), context.cartRequest)

  /** Return true when only content needs to be called
    */
  def isContentOnly: Boolean =
    this.room.flatMap(_.isContentOnly).getOrElse(false) || this.room.exists(
      _.externalServiceMode.exists(_ == ExternalServiceModes.ContentOnly)
    )

  /** Return true when only pricing needs to be called
    */
  def isPricingOnly: Boolean =
    !this.isContentOnly && this.room.exists(_.externalServiceMode.exists(_ == ExternalServiceModes.PricingOnly))

  def isSearchType(checkType: SearchType): Boolean = searchType.contains(checkType)

  def toOverrideIsMse(isMse: Boolean): PropertyRequest = {
    val featuresFlag =
      FeatureFlag.DuplicateRoomForPriceline :: this.pricing.flatMap(_.featureFlag).getOrElse(List.empty)
    this.copy(
      context = this.context.copy(isMSE = Option(isMse)),
      pricing = this.pricing.map(_.copy(featureFlag = Option(featuresFlag)))
    )
  }

  def toExcludeRoomForNonCompliant(isNonCompliant: Boolean): PropertyRequest =
    if (isNonCompliant) {
      this.copy(
        room = None,
        pricing = None
      )
    } else {
      this
    }

  def toOverrideRecomendationWithMse(): PropertyRequest = {
    val featuresFlag =
      FeatureFlag.DuplicateRoomForPriceline :: this.pricing.flatMap(_.featureFlag).getOrElse(List.empty)
    this.copy(
      context = this.context.copy(isMSE = Option(true)),
      pricing = this.pricing.map(_.copy(featureFlag = Option(featuresFlag))),
      isSSR = Option(true)
    )
  }

  def toOverrideCheckIn(searchStatus: SearchStatusAndCriteria): PropertyRequest =
    searchStatus.searchStatus match {
      case SearchStatuses.CheckInChanged =>
        this.copy(pricing = this.pricing.map(_.copy(checkIn = searchStatus.searchCriteria.checkIn)))
      case _ => this
    }

  def toOverrideFeatureFlagRequestForAsq(): PropertyRequest =
    if (Asq.Cids.contains(context.cid)) {
      val updatedFeatureFlagRequest = this.featureFlag.map { featureFlag =>
        featureFlag.copy(
          includeSoldOutRooms = Some(false),
          soldOutSimilarProperties = Some(false),
          showSoldOutRooms = Some(false),
          showPredictionForLikelySoldoutRoom = Some(false),
          showUnAvailable = Some(false)
        )
      }
      this.copy(featureFlag = updatedFeatureFlagRequest)
    } else this

  def toRequestWithTrimmedPropertyIds(): PropertyRequest =
    if (
      this.context.propertyIds.size > MaxPropertyIds && List(
        PlatformID.DesktopWeb,
        PlatformID.TabletWeb,
        PlatformID.MobileWeb,
        PlatformID.MobileAPI
      ).contains(this.context.platform)
    ) {
      val trimmedPropertyIds = this.context.propertyIds.take(MaxPropertyIds)
      val updatedContext     = this.context.copy(propertyIds = trimmedPropertyIds)
      this.copy(context = updatedContext)
    } else this

  def isPackagingSearch: Boolean =
    pricing.flatMap(_.packaging).nonEmpty || CartHelper.isPackagingFunnel(context.cartRequest)
}

case class BookingPropertyRequest(
    context: PropertyContext,
    booking: Option[BookingRequest] = None,
    pricing: Option[PropertyPricing] = None,
    searchType: Option[SearchType] = Option(SearchTypes.None)
) {
  def toPropertyRequest(): PropertyRequest =
    _root_.request.PropertyRequest(context = context, booking = booking, searchType = searchType, pricing = pricing)
}

@SerialVersionUID(1L)
case class PAPIRateCategoryRequest(
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    @JsonProperty(required = false)
    @deprecated("Use rateCategoryIdWithRooms. The room id is optional and will be used for filtering child policies")
    rateCategoryIds: Vector[RateCategoryId],
    detail: Option[RateCategoryDetailRequest],
    rateCategoryIdWithRooms: Option[List[RateCategoryIdWithRoom]] = None,
    shouldAutoPopulateIds: Option[Boolean] = None
) {

  def toContentRateCategoryRequest: RateCategoryRequest =
    RateCategoryRequest(
      rateCategoryIds,
      detail,
      rateCategoryIdWithRooms
    )

}

@SerialVersionUID(1L)
case class Recommendation(
    numberOfRecommendations: Long,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    overrideList: List[Long],
    recommendationType: Option[RecommendationType] = None,
    productTypeId: Option[ProductTypeIdType] = None,
    calculateCheaperThanSimilar: Option[Boolean] = None,
    filters: Option[List[RecommendationFilter]] = None,
    matchScore: Option[Boolean] = None
)

@SerialVersionUID(1L)
final case class RecommendationFilter(
    field: RecommendationFilterFieldType,
    comparision: RecommendationFilterComparisonType,
    originHotelId: HotelIdType
)

@SerialVersionUID(1L)
case class RoomRequestPAPI(
    images: Option[ImageSizeRequest] = None,
    existingImages: Option[Vector[Image]] = None,
    @deprecated("deprecated, please use externalServiceMode instead")
    isContentOnly: Option[Boolean],
    includeMissing: Option[Boolean] = None,
    includeDmcRoomId: Option[Boolean] = None,
    featureLimit: Option[Int] = None,
    externalServiceMode: Option[ExternalServiceMode] = Some(ExternalServiceModes.All),
    showChildRoomInContentOnly: Option[Boolean] = None,
    filterCriteria: Option[List[RoomFilterCriteria]] = None,
    excludeDuplicates: Option[Boolean] = None,
    dynamicallyMappedRooms: Option[List[String]] = None,
    includeRoomRecommendation: Option[Boolean] = None,
    showRoomDescriptionAI: Option[Boolean] = None,
    videoType: Option[String] = None,
    showRoomVideo: Option[Boolean] = None
)

@SerialVersionUID(1L)
final case class RoomFilterCriteria(
    propertyId: Long,
    @JsonDeserialize(contentAs = classOf[java.lang.Long]) roomIds: Set[Long],
    masterRoomIds: Option[Set[Int]]
)

case class PropertyAPIRequestModel(
    propertySearchRequest: PropertySearchRequest,
    suggestPrice: SuggestPrice,
    soldOutAlternateDatesRequest: Option[SoldOutAlternateDatesRequest] = None
)

@SerialVersionUID(1L)
case class CreditCardPaymentRequest(
    paymentCurrency: String,
    creditCardCurrency: String,
    chargedDateOption: Option[Int],
    countryIdOfIssuingBank: Option[Int] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    ccOf: Option[Long] = None,
    ccToken: Option[String] = None
)

@SerialVersionUID(1L)
case class PaymentRequest(
    paymentId: Option[Int] = None,
    paymentOption: Option[Int] = None,
    loyalty: Option[LoyaltyPaymentRequest] = None,
    creditCardPayment: Option[CreditCardPaymentRequest] = None,
    unsupportedPaymentModels: Option[List[PaymentModel]] = None,
    installmentInfo: Option[InstallmentInfo] = None,
    customerTaxCountryCode: Option[String] = None,
    cashback: Option[LoyaltyPaymentRequest] = None,
    isCashbackRedemptionEligible: Option[Boolean] = None
)

case class CCBinInfo(hashed: String, numberOfDigit: Int)

@SerialVersionUID(1L)
case class TopSellingPointRequest(ids: List[Int])

/** Request to DF to get room prices ToDO: please add comments for rest of the fields
  *
  * @param checkIn
  * @param lengthOfStay
  * @param bookingDate
  * @param numberOfRooms
  * @param numberOfAdults
  * @param numberOfChildren
  * @param isUserLoggedIn
  * @param enableOpaqueChannel
  * @param allowOverrideOccupancy
  * @param partnerLoyaltyProgramId
  * @param storeFrontId
  * @param currency
  * @param ratePlanId
  * @param isRPM2Included
  * @param isIncludeUsdAndLocalCurrency
  * @param isAPSPeek
  * @param cheapestOnly
  * @param isSmall
  * @param partner
  * @param refId
  * @param paymentId
  * @param featureFlag
  * @param synchronous
  * @param maxRooms
  * @param childAges
  * @param isIncludedPriceInfos
  * @param isAPSSwap
  * @param isAllowRoomTypeNotGuarantee
  * @param requiredBasis
  * @param requiredPrice
  * @param supplierIds
  * @param discountRequest
  * @param paymentRequest
  * @param simulateRequestData
  * @param supplierPullMetadataRequest
  * @param suggestionLimit
  * @param isAllOcc
  * @param occFilter
  *   for all-occupancy-search (no adults / children restriction) you can optionally specify min and max occupancy
  */
@JsonIgnoreProperties(
  value = Array("twoYearAhead", "yesterday", "getIsUserLoggedIn", "guests", "guestPerRooms", "checkOut", "leadDays"),
  ignoreUnknown = true
)
@SerialVersionUID(1L)
case class PropertyPricing(
    checkIn: DateTime,
    lengthOfStay: Int,
    bookingDate: DateTime,
    numberOfRooms: Int,
    numberOfAdults: Int,
    numberOfChildren: Int,
    isUserLoggedIn: Boolean,
    enableOpaqueChannel: Boolean,
    allowOverrideOccupancy: Boolean,
    partnerLoyaltyProgramId: Int,
    @deprecated("move to context.storeFrontId")
    storeFrontId: Option[Int] = None,
    currency: String,
    ratePlanId: List[Int],
    isRPM2Included: Boolean,
    isIncludeUsdAndLocalCurrency: Boolean,
    isUsingHotelCurrency: Option[Boolean] = None,
    isAPSPeek: Option[Boolean] = None,
    cheapestOnly: Option[Boolean] = None,
    isSmall: Option[Boolean] = None,
    partner: Option[PartnerRequest] = None,
    refId: Option[Int] = None,
    paymentId: Option[Int] = None,
    featureFlag: Option[List[FeatureFlag]] = None,
    synchronous: Option[Boolean] = None,
    maxRooms: Option[Int] = None,
    childAges: Option[List[Option[Int]]] = None,
    isIncludedPriceInfos: Option[Boolean] = None,
    isAPSSwap: Option[Boolean] = None,
    isAllowRoomTypeNotGuarantee: Option[Boolean] = None,
    requiredBasis: Option[ApplyType] = None,
    requiredPrice: Option[SuggestedPrice] = None,
    supplierIds: Option[List[SupplierId]] = None,
    discountRequest: Option[DiscountRequest] = None,
    paymentRequest: Option[PaymentRequest] = None,
    simulateRequestData: Option[SimulateRequestData] = None,
    supplierPullMetadataRequest: Option[SupplierPullMetadataRequest] = None,
    suggestionLimit: Option[Int] = None,
    isAllOcc: Option[Boolean] = None,
    occFilter: Option[OccFilter] = None,
    isEnabledPartnerChannelSelection: Option[Boolean] = None,
    mseHotelIds: Option[List[Int]] = None,
    mseClicked: Option[String] = None,
    fencedRate: Option[FencedRate] = None,
    travellerType: Option[TravellerType] = None,
    roomSelection: Option[RoomSelectionRequest] = None,
    packaging: Option[Packaging] = None,
    ppLandingHotelIds: Option[List[Int]] = None,
    searchedHotelIds: Option[List[Int]] = None,
    roomsAssignment: Option[List[RoomAssignment]] = None,
    childrenTypes: Option[List[ChildType]] = None,
    bookingDurationType: Option[List[String]] = None,
    roomBundleHints: Option[List[RoomBundleRequest]] = None,
    clientCampaignInfos: Option[List[CampaignInfo]] = None,
    simplifiedRoomSelectionRequest: Option[SimplifiedRoomSelectionRequest] = None,
    roomIdentifierFilter: Option[RoomIdentifierFilter] = None,
    symmetricUidFilterOut: Option[SymmetricUidFilter] = None,
    packagingFilterContext: Option[PackagingFilterContext] = None,
    priceAdjustmentRequest: Option[List[PriceAdjustment]] = None,
    features: Option[FeatureRequest] = None,
    externalLoyalty: Option[ExternalLoyaltyRequest] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    externalLoyaltyRemainingBenefitCredit: Option[Double] = None,
    selectedRewardOption: Option[String] = None,
    stateId: Option[Int] = None,
    selectedCheckInTime: Option[String] = None
) {
  @JsonProperty("yesterday")
  val yesterday = DateTime.now().withTimeAtStartOfDay().minusDays(2)
  @JsonProperty("twoYearAhead")
  val twoYearAhead = DateTime.now().withTimeAtStartOfDay().plusYears(2).plusDays(1)

  @JsonProperty("getIsUserLoggedIn")
  lazy val getIsUserLoggedIn = isUserLoggedIn || isAPSPeek.exists(identity)

  def getNumberOfChildren(): Int = {
    val childrenWithAges = childAges.map(_.size).getOrElse(0)
    if (childrenWithAges > 0) childrenWithAges else numberOfChildren
  }

  @JsonProperty("guests")
  lazy val guests: Int = numberOfAdults + getNumberOfChildren()

  @JsonProperty("guestPerRooms")
  lazy val guestPerRooms: Int =
    // Handle some cases that only one of them is ZERO
    if (guests == 0 || numberOfRooms == 0) {
      1
    } else if (guests % numberOfRooms == 0) {
      guests / numberOfRooms
    } else {
      (guests / numberOfRooms) + 1
    }

  @JsonProperty("adultsPerRoom")
  lazy val adultsPerRoom: Int =
    if (numberOfAdults == 0 || numberOfRooms == 0) {
      1
    } else if (numberOfAdults % numberOfRooms == 0) {
      numberOfAdults / numberOfRooms
    } else {
      (numberOfAdults / numberOfRooms) + 1
    }

  final private val maxLengthOfStay = 14
  final private val maxCheckInDate  = 365

  def isManual: Boolean = {
    val isNotSync = !synchronous.getOrElse(false)
    (lengthOfStay > maxLengthOfStay || Days.daysBetween(bookingDate, checkIn).getDays + 1 > maxCheckInDate) && isNotSync
  }

  @JsonProperty("checkOut")
  lazy val checkOut: DateTime = checkIn.plusDays(lengthOfStay)

  @JsonProperty("leadDays")
  lazy val leadDays = Days.daysBetween(bookingDate.withTimeAtStartOfDay(), checkIn).getDays

  def hasFlag(featureFlagToCheck: FeatureFlag): Boolean =
    featureFlag.exists(_.contains(featureFlagToCheck))

  def removeFlags(featureFlagsToRemove: Seq[FeatureFlag]): PropertyPricing = {
    val flags = featureFlag.map(_.filterNot(featureFlagsToRemove.contains(_)))
    this.copy(featureFlag = flags)
  }

  def addFlags(featureFlagsToAdd: List[FeatureFlag]): PropertyPricing = {
    val flagsOpt: Option[List[FeatureFlag]] = featureFlag.map { flags =>
      (flags ::: featureFlagsToAdd).distinct
    }
    this.copy(featureFlag = flagsOpt)
  }

  def isFamilySearch: Boolean = numberOfAdults > 2 || getNumberOfChildren > 0

  def isPackagingRequest: Boolean = packaging.nonEmpty

  @JsonIgnore
  lazy val measureTags: Map[String, String] = Map(
    "isSync" -> synchronous.getOrElse(false).toString,
    "los"    -> LosGroup.losGroup(lengthOfStay)
  )

}
