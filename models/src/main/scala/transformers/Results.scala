package transformers

import com.agoda.content.models.PropertyResponse
import com.agoda.content.models.db.bathInformation.BathInformationResponse
import com.agoda.content.models.db.checkInOut.CheckInOut
import com.agoda.content.models.db.experiences.Experiences
import com.agoda.content.models.db.features._
import com.agoda.content.models.db.highlights.{ HighlightDistanceInformation, HighlightInformation, Highlights }
import com.agoda.content.models.db.image._
import com.agoda.papi.enums.hotel.TaxRegistrationType
import com.agoda.content.models.db.information._
import com.agoda.content.models.db.nonHotelAccommodation.NonHotelAccommodation
import com.agoda.content.models.db.personalized.PersonalizedInformation
import com.agoda.content.models.db.rateCategories.{ RateCategoriesResponse, RateCategoryCheckTime }
import com.agoda.content.models.db.reviews.{ ReviewResponse, ReviewTotals }
import com.agoda.content.models.db.synopsis.Synopsis
import com.agoda.content.models.db.transportationInformation.TransportationInformationResponse
import com.agoda.content.models.response.USPResponse
import com.agoda.core.search.models.request.PolygonCoordinates
import com.agoda.core.search.models.response.{
  FriendsInfoType,
  MatrixResult,
  ObjectExtraInfo,
  PollingInfoResponse,
  PopularFilterResult,
  Price => _,
  SearchStatusAndCriteria,
  SortMatrixResult,
  StepResult,
  StructuredMaxMinPrice,
  UrgencyDetail,
  _
}
import com.agoda.core.search.models.{
  CancellationPoliciesResultEntry,
  CancellationType,
  CancellationTypes,
  PropertyData
}
import com.agoda.localization.Localization
import com.agoda.papi.enums.campaign.{
  CampaignDiscountType,
  CampaignStatusType,
  CampaignValidDateType,
  IneligiblePromotionReason,
  PromotionCodeType
}
import com.agoda.papi.enums.hotel.{ FireDrillContractType, StayType }
import com.agoda.papi.enums.room.{ BenefitTargetType, BenefitTargetTypes, CancellationChargeSettingType, WhomToPayType }
import com.agoda.papi.pricing.supply.models.GrowthProgramInfo
import models.starfruit.TaxMetaData
import com.agoda.payment.model.InstallmentDetails
import com.agoda.pricestream.models.response.{
  PropertyLastYearBookingCount,
  PropertyLongStayPromotion,
  PropertyMetaInfo,
  PropertyPriceSchema
}
import com.agoda.upi.models.cart.Cart
import com.fasterxml.jackson.annotation.{ JsonIgnoreProperties, JsonProperty }
import com.fasterxml.jackson.databind.annotation.JsonDeserialize
import constant.TopSellingPointIds
import enumerations.{ Benefit => _, CampaignType, _ }
import filters.DynamicFilter
import meta.CheapestRoomWithSuggestion
import metaapi.{ PropertyRanking, YesterdayBookingsUrgency }
import metapricing.{ PriceChange, PriceSnapshot }
import metrics.PriceHistogram
import models.CheapestStayPackageRatePlans
import models.db._
import models.pricing.enums._
import models.pricing.{
  AccountingEntity,
  AgodaRefundInfo,
  Benefit => DFBenefit,
  Cashback => DFCashback,
  CrossOutRates,
  DiscountType,
  DiscountTypes,
  DmcData => DFDmcData,
  ExchangeRate,
  GiftCard => DFGiftCard,
  PriceChange => DFPriceChange,
  PseudoCoupon => DFPseudoCoupon,
  RateCategory,
  RateRepurposeInfo,
  Rebate,
  RoomOfferBookingHistory
}
import models.starfruit.PropertyPricingJsonProtocol.{
  DFPrice,
  DFPriceSummary,
  ResponseStateToken,
  SupplierFinancialData
}
import models.starfruit.{
  CheckInInformation => PricingCheckInInformation,
  CorBreakdown => _,
  Discount,
  HotelSummary => PriceHotelSummary,
  PartnersPriceBreakdown,
  Price => PriceModel,
  PromotionCap,
  Properties => _,
  RateModel,
  RoomAllocationInfo,
  _
}
import org.joda.time.{ DateTime, LocalDate }
import payment.DisplayPaymentType
import ranking.RankingExplanationMessage
import request.{ PropertyRequest, RecommendationFilter }
import response.ObjectInfo
import serializer.OptionalEnrichedCampaignMessagesMapDeserializer
import types._
import util.JMapBuilder

import scala.collection.mutable
import sttp.tapir.Schema.annotations.encodedName

/** Created by schoudhary on 6/12/16.
  */

@SerialVersionUID(1L)
case class RankingPricingAndContent(
    searchId: String,
    propertyId: Long,
    results: PricingAndContent,
    recommendations: List[ZenithResponseEnriched] = List.empty,
    personalizedRecommendations: List[ZenithResponseEnriched] = List.empty,
    pastBookingsRecommendations: List[ZenithResponseEnriched] = List.empty,
    recommendationHeader: Option[RecommendationHeader],
    roomBundle: List[RoomBundle] = Nil,
    breakfastPriceFromHotelSetting: Option[Double] = None
)

@SerialVersionUID(2L)
case class BookingPricingAndContent(searchId: String, propertyId: Long, results: PricingAndContent)

@SerialVersionUID(3L)
case class RecommendationResult(hotel: Option[RecommendationHotel], summaryResponse: PropertyResponse)

/** -Keep EnrichedHotelWithContent (DF Hotel with enriched and mapped master Room with content API)
  * -Keep All Response from content API per Property
  */
@SerialVersionUID(4L)
case class PricingAndContent(
    propertyId: Long,
    hotel: Option[EnrichedHotelWithContent],
    hotelInfo: Option[InformationResponse],
    reviews: Option[ReviewResponse],
    images: Option[ImagesResponse],
    availableFilters: Option[PropertyFilters],
    features: Option[FeatureGroupTypes],
    hotelSummary: Option[InformationSummaryResponse] = None,
    localInformation: Option[LocalInformation] = None,
    checkInOut: Option[CheckInOut] = None,
    synopsis: Option[Synopsis] = None,
    engagement: Option[EngagementContainer] = None,
    experience: Option[Experiences] = None,
    highlights: Option[Highlights] = None,
    nonHotelAccommodation: Option[NonHotelAccommodation] = None,
    uniqueSellingPoints: Option[USPResponse] = None,
    personalizedInformation: Option[PersonalizedInformation] = None,
    rateCategory: Option[RateCategoriesResponse] = None,
    bathInformation: Option[BathInformationResponse] = None,
    roomBundle: List[RoomBundle] = Nil,
    transportationInformation: Option[TransportationInformationResponse] = None,
    breakfastPriceFromHotelSetting: Option[Double] = None,
    propertyMetaInfoResponse: Option[PropertyMetaInfo] = None,
    lastYearBookingCount: Option[PropertyLastYearBookingCount] = None,
    propertyMetaRanking: Option[PropertyRanking] = None,
    uniqueVisitorPerCity: Option[Int] = None,
    rateCategories: Option[RateCategoriesResponse] = None,
    longStayRoomId: Option[String] = None,
    longStayPromotions: Vector[PropertyLongStayPromotion] = Vector.empty,
    propertySearchToken: Option[String] = None
)

/** reflect from PropertyEngagement existed in content-platform, purpose is to handle new field from meta-api should use
  * as intermediate object to avoid carrying around two different engagement related object
  * @param lastBookByCheckIn
  *   new field coming from meta-api
  */
final case class EngagementContainer(contentEngagement: PropertyEngagement, metaEngagement: MetaEngagement)

@SerialVersionUID(5L)
case class ZenithCheapestRoom(
    display: Option[DisplayBasis],
    crossedOut: Option[DisplayBasis],
    benefits: List[CmsFlagData],
    channel: EnrichedChannel,
    loyaltyDisplay: Option[LoyaltyDisplay] = None,
    charges: Option[Seq[EnrichedCharge]],
    packaging: Option[EnrichedPackaging],
    cancellation: Option[EnrichedCancellation],
    payment: Option[EnrichedPayment] = None
)

/** ZenithResponse with Enriched Recommendation card
  */
@SerialVersionUID(6L)
case class RecommendationResponse(
    cheapestPriceCrossedDisplay: Option[DisplayBasis],
    cheapestPriceCrossedOut: Option[DisplayBasis],
    cheapestRoomPackaging: Option[EnrichedPackaging],
    cheapestRoomCancellation: Option[EnrichedCancellation],
    cheapestRoomCharges: Option[Seq[EnrichedCharge]],
    mainImage: Option[String],
    name: Option[String],
    localName: Option[String],
    displayName: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    reviewCount: Option[Long],
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    reviewScore: Option[Double],
    reviewScoreText: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    starRating: Option[Double],
    starRatingSymbol: Option[String],
    starRatingColor: Option[String],
    starRatingText: Option[String],
    isBreakfastIncluded: CmsFlagData,
    channel: EnrichedChannel,
    isFreeWiFi: Boolean,
    loyaltyDisplay: Option[LoyaltyDisplay] = None,
    neighborhood: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    propertyId: Option[Long],
    reviewDemographics: Option[com.agoda.content.models.db.reviews.ReviewScores],
    recommendationMseResults: Option[List[RecomendationMseResults]],
    engagement: Option[com.agoda.content.models.db.information.PropertyEngagement],
    singleRoomNonHotelAccommodation: Option[Boolean],
    isNonHotelAccommodationType: Option[Boolean],
    @deprecated("hasHostExperience is no longer in use instead use isNhaEnabled or propertyType.NonHotel")
    hasHostExperience: Option[Boolean],
    hostType: Option[Int],
    productHotels: Option[ZenithProductHotels] = None,
    locationHighlightMessage: Option[HighlightInformation] = None,
    reviewCumulative: Option[ReviewTotals] = None,
    locationHighlights: Option[Vector[HighlightDistanceInformation]] = None,
    totalDiscount: Option[Int] = None,
    nonHotelAccommodation: Option[NonHotelAccommodation] = None,
    filteredBy: Option[RecommendationFilter] = None,
    matchScore: Option[MatchScore] = None,
    cheapestRoomPayment: Option[EnrichedPayment] = None,
    suggestedPrice: Option[RequestedPrice] = None,
    suggestedBasis: Option[ApplyType] = None,
    propertyLinks: Option[PropertyLinks] = None
)

/** ZenithResponse with Enriched Recommendation card (Free wifi and reviewCountText)
  */
@SerialVersionUID(7L)
case class ZenithResponseEnriched(
    cheapestPriceBasis: Option[DisplayBasis],
    cheapestRoom: ZenithCheapestRoom,
    mainImage: Option[String],
    name: Option[String],
    localName: Option[String],
    displayName: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    reviewCount: Option[Long],
    reviewCountText: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    reviewScore: Option[Double],
    reviewScoreText: Option[String],
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    starRating: Option[Double],
    starRatingSymbol: Option[String],
    starRatingColor: Option[String],
    starRatingText: Option[String],
    neighborhood: Option[String],
    highlightFacilities: List[CmsFlagData],
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    propertyId: Option[Long],
    reviewDemographics: Option[com.agoda.content.models.db.reviews.ReviewScores],
    recommendationMseResults: Option[List[RecomendationMseResults]],
    engagement: Option[com.agoda.content.models.db.information.PropertyEngagement],
    singleRoomNonHotelAccommodation: Option[Boolean],
    isNonHotelAccommodationType: Option[Boolean],
    @deprecated("hasHostExperience is no longer in use instead use isNhaEnabled or propertyType.NonHotel")
    hasHostExperience: Option[Boolean],
    hostType: Option[Int],
    locationHighlightMessage: Option[HighlightInformation] = None,
    reviewCumulative: Option[ReviewTotals] = None,
    bookingHistory: Option[BookingHistory] = None,
    topSellingPoints: List[TopSellingPoint] = Nil,
    locationHighlights: Option[Vector[HighlightDistanceInformation]] = None,
    totalDiscount: Option[Int] = None,
    nonHotelAccommodation: Option[NonHotelAccommodation] = None,
    filteredBy: Option[RecommendationFilter] = None,
    matchScore: Option[MatchScore] = None,
    suggestedPrice: Option[RequestedPrice] = None,
    suggestedBasis: Option[ApplyType] = None,
    propertyLinks: Option[PropertyLinks] = None
)

@SerialVersionUID(1L)
final case class MatchScore(score: Int, categories: Seq[Int])

@SerialVersionUID(8L)
case class Properties(
    property: Seq[Property],
    debug: Option[Debug],
    suppliersInformation: Map[Long, SupplierInformation] = Map.empty,
    @deprecated("use suppliersInformation instead")
    suppliersInformationForWhitelistHotel: Map[Long, SupplierInformation] = Map.empty,
    localization: Option[LocalizationResponse] = None,

    /** Use By FE to Tags Mse Allocation Request
      */
    isMseAllocation: Option[Boolean] = None,
    dfMetaResult: DFMetaResult = DFMetaResult(),
    searchStatusAndCriteria: Option[SearchStatusAndCriteria] = None,
    // used for FE SSR polling
    pollingInfo: Option[PollingInfoResponse] = None
) {

  lazy val pseudoCouponMessage: Option[EnrichedPseudoCouponMessage] =
    if (property.exists(_.pseudoCouponMessage.nonEmpty)) {
      Some(property.flatMap(_.pseudoCouponMessage).maxBy(_.downlift))
    } else None

}

@SerialVersionUID(3L)
case class PropertiesRecommendationResponse(
    recommendations: List[ZenithResponseEnriched] = List.empty,
    objectInfo: Option[ObjectExtraInfo]
)

@SerialVersionUID(1L)
case class PagingFlowResultCumulative(
    availableProperties: Properties,
    invalidHotelIds: Vector[HotelIdType],
    usedHotelIds: Vector[HotelIdType],
    numberOfIterations: Int
)

@SerialVersionUID(1L)
case class PagingFlowResult(availableProperties: Properties, invalidHotelIds: Set[HotelIdType], numberOfIterations: Int)

@SerialVersionUID(1L)
trait ComplexSearchResponse

@SerialVersionUID(12L)
case class CitySearchResponse(
    property: Properties,
    matrix: Option[MatrixResult],
    hasInsiderDeal: Boolean = false,
    hasSecretDeal: Boolean = false,
    hasFavorites: Boolean = false,
    hasEscapesPackage: Boolean = false,
    apoProperty: Option[Properties] = None,
    extraProperty: Option[Properties] = None,
    unAvailProperty: Option[Properties] = None,
    featuredAgodaHomes: Option[Properties] = None,
    highlyRatedAgodaHomes: Option[Properties] = None,
    personalizedProperties: Option[Properties] = None,
    extraAgodaHomes: Option[Properties] = None,
    maxMinPrice: Option[StructuredMaxMinPrice] = None,
    objectInfo: ObjectInfo = ObjectInfo("", 0, 0),
    objectInfoExtra: Option[ObjectExtraInfo] = None,
    totalActiveHotels: Long = 0L,
    totalFilteredHotels: Long = 0L,
    totalFavoriteHotels: Long = 0L,
    totalAvailableHotelsWithoutFilter: Long = 0L,
    isComplete: Boolean = true,
    allProperties: WithHotelIdMap[PropertyData] = JMapBuilder.empty(),
    popularFilter: Seq[PopularFilterResult] = Vector.empty,
    segmentFilter: Seq[PopularFilterResult] = Vector.empty,
    personalizedFilter: Seq[PopularFilterResult] = Vector.empty,
    suggestedPriceType: Option[SuggestedPriceType] = None,
    urgencyDetail: Option[UrgencyDetail] = None,
    searchStatus: Option[SearchStatusAndCriteria] = None,
    stepsResult: Option[Seq[StepResult]] = None,
    pricesHistogram: Option[PriceHistogram] = None,
    sortMatrix: Option[SortMatrixResult] = None,
    recommendedPriceRange: Option[RecommendedPriceRange] = None,
    /** Use By FE to Tags Mse Allocation Request
      */
    isMseAllocation: Option[Boolean] = None,
    dynamicFilters: Option[List[DynamicFilter]] = None,
    soldOutPropertiesData: Option[SoldOutPropertiesData] = None,
    favoritesFilterData: Option[FavoritesFilterData] = None,
    messageInformation: Map[String, MessageInformation] = Map.empty,
    mapLandmarks: Option[GeoPlaceSearchResult] = None,
    recommendedProperty: Seq[Property] = Seq.empty,
    polygonCoordinates: Option[Seq[PolygonCoordinates]] = None,
    priceHistory: Map[HotelIdType, Seq[PriceSnapshot]] = Map.empty,
    likelyToBeSoldoutProperties: Map[HotelIdType, Boolean] = Map.empty,
    cityBookingHistory: Option[CityBookingHistory] = None,
    pricingDisplayPresentation: Option[PricingDisplayPresentation] = None,
    yesterdayBookingsUrgency: Option[YesterdayBookingsUrgency] = None,
    debugResult: Option[DebugResult] = None,
    hadoopExtraData: Option[HadoopExtraData] = None
) extends ComplexSearchResponse

object CitySearchResponse {
  def inComplete: CitySearchResponse =
    CitySearchResponse(property = Properties(Seq.empty, None), matrix = None, isComplete = false)

  def empty: CitySearchResponse = CitySearchResponse(property = Properties(Seq.empty, None), matrix = None)
}

object Status {
  val SUCCESS = "Success"
  val FAILURE = "Failure"
  val TIMEOUT = "Timeout"
}

@SerialVersionUID(1)
case class RecommendedPriceRange(min: Option[Float] = None, max: Option[Float] = None)

@SerialVersionUID(17L)
case class Debug(papiVersion: String, dfapiVersion: String, contentVersion: String, request: PropertyRequest)

@SerialVersionUID(1)
case class InternalUtilityDataFromDF(hasPayNow: Boolean = false, availableRoomIds: Set[Long] = Set.empty)

@SerialVersionUID(1)
case class InternalUtilityDataFromContent(
    contentRoomIds: Set[Long] = Set.empty,
    contentYCSRoomIds: Set[Long] = Set.empty,
    roomNameMap: scala.collection.immutable.Map[Long, String] = scala.collection.immutable.Map.empty
)

@SerialVersionUID(1)
case class InternalUtilityData(
    internalUtilityDataFromDF: Option[InternalUtilityDataFromDF] = None,
    internalUtilityDataFromContent: Option[InternalUtilityDataFromContent] = None
)

/** Response for Property and Properties Endpoint
  */
@JsonIgnoreProperties(
  ignoreUnknown = true,
  value = Array("pseudoCouponMessage", "loggingMetaData", "getAllRooms", "getCheapestRoom", "internalUtilityData")
)
@SerialVersionUID(18L)
case class Property(
    searchId: String,
    propertyId: Long,
    cityId: CityId,
    productType: List[Int],
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    activeHotelsInCity: Option[Long],
    countryId: CountryId,
    @JsonProperty
    areaId: HotelAreaIdType,
    cheapestRoom: Option[EnrichedCheapestRoom],
    needOccupancySearch: Boolean,
    isReady: Boolean,
    supplierId: SupplierId,
    info: Option[InformationResponse],
    features: Option[FeatureGroupTypes],
    reviews: Option[ReviewResponse],
    images: Option[ImagesResponse],
    highlights: Option[Highlights],
    nonHotelAccommodation: Option[NonHotelAccommodation],
    isDisplayAsAmount: Option[Boolean],
    suggestedBasis: Option[ApplyType],
    suggestedPrice: Option[SuggestedPrice],
    recommendations: List[ZenithResponseEnriched] = List.empty,
    recommendationCorrelation: Option[RecommendationCorrelation] = None,
    personalizedRecommendations: List[ZenithResponseEnriched] = List.empty,
    pastBookingsRecommendations: List[ZenithResponseEnriched] = List.empty,
    recommendationHeader: Option[RecommendationHeader],
    tooltip: PropertyTooltip,
    requestedChannelIds: List[Int],
    availableFilters: Option[PropertyFilters],
    enableAPS: Boolean,
    // Used by PropertySearch to return all rooms
    // In case of citysearch, this field will be empty
    masterRooms: Seq[EnrichedMasterRoom],
    // Used in ComplexSearch for returning list of rooms in cheapest group
    // In case of property search, this field will be empty
    rooms: Seq[EnrichedChildRoom],
    infoSummary: Option[InformationSummaryResponse],
    paxSetting: Option[PartnerPaxOption] = None,
    localInformation: Option[LocalInformation] = None,
    checkInOut: Option[CheckInOut] = None,
    synopsis: Option[Synopsis] = None,
    engagement: Option[PropertyEngagement] = None,
    experience: Option[Experiences] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    distance: Option[Double] = None,
    @JsonProperty("pseudoCouponMessage")
    pseudoCouponMessage: Option[EnrichedPseudoCouponMessage],
    dfFeatures: Option[HotelFeatures],
    priceMessaging: Option[EnrichedPriceMessaging],
    promotionEligible: Option[Boolean],
    packageEligible: Option[Boolean],
    booking: Option[EnrichedBookingItem] = None,
    taxType: Option[TaxType] = None,
    supplierSummaries: Map[Long, EnrichedSupplierSummary] = Map.empty,
    isMseProperty: Option[Boolean] = None,
    @deprecated("use getCheapestRoom instead")
    mseCheapestRoom: Option[EnrichedCheapestRoom] = None,
    @deprecated("Move to uspRank", "v3.30.0")
    usp: Option[PropertyUsp] = None,
    uspRanks: Option[List[PropertyUspRank]] = None,
    suggestedRooms: List[EnrichedMasterRoom] = List.empty,
    roomSwapping: List[AlternativeRoom] = List.empty,
    suggestedChildRooms: List[EnrichedChildRoom] = List.empty,
    maxPointsMax: Option[EnrichedPointMax] = None,
    maxPointsMaxBadge: Option[PointsMaxBadge] = None,
    taxMetaData: List[TaxMetaData] = List.empty,
    nearestLandmarkDistanceMessage: Option[String] = None,
    nearestLandmarkDistance: Option[Double] = None,
    @deprecated("DeIntegrated") distanceFromSelectedLandmark: Option[Double] = None,
    selectedLandmarkName: Option[String] = None,
    isFavorite: Option[Boolean] = None,
    friends: List[FriendsInfoType] = List.empty,
    uniqueSellingPoints: Option[USPResponse] = None,
    supportUserLocale: Option[Boolean],
    supportUserLocaleBadge: Option[SymbolAndText],
    hotelAggInfo: Option[HotelAggregationInfo] = None,
    soldOutPrice: Option[SoldOutPricing] = None,
    soldOutRooms: Option[EnrichedSoldOutRooms] = None,
    bookingHistory: Option[BookingHistory] = None,
    cityBookingHistory: Option[CityBookingHistory] = None,
    bookingFormHistory: Option[BookingFormHistory] = None,
    propertyMetaInfo: Option[PropertyMetaInfo] = None,
    topSellingPoints: List[TopSellingPoint] = Nil,
    personalizedInformation: Option[PersonalizedInformation] = None,
    @deprecated("use rankingType instead. sponsored field will be removed")
    sponsored: Boolean = false,
    sponsoredType: Option[Int] = None,
    showSponsoredTag: Option[Boolean] = None,
    sponsoredTrackingData: Option[String] = None,
    rankingType: Int = 0,
    rankingExplanationMessages: Seq[RankingExplanationMessage] = Seq.empty,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    continentId: Option[Long] = None,
    @deprecated("no longer in use")
    isWhiteListHotel: Boolean = false,
    @JsonProperty("internalUtilityData")
    internalUtilityData: InternalUtilityData = new InternalUtilityData,
    closestBeachLandmarkInfo: List[LandmarkInfo] = List.empty,
    supportedSuppliers: Map[Long, EnrichedSupplierSummary] = Map.empty,
    areaBookingCountLastWeek: Option[AreaBookingCountModel] = None,
    @JsonProperty(required = false)
    alternateDates: Seq[AlternateDateSummary] = Seq.empty[AlternateDateSummary],
    stayOccupancy: Option[StayOccupancy] = None,
    bookingMaxOccupancy: Option[BookingMaxOccupancy] = None,
    rateCategory: Option[RateCategoriesResponse] = None,
    propertyRankingInfo: Option[PropertyRanking] = None,
    newPriceSchema: Option[PropertyPriceSchema] = None,
    bathInformation: Option[BathInformationResponse] = None,
    transportationInformation: Option[TransportationInformationResponse] = None,
    longStayRoomId: Option[String] = None,
    longStayPromotions: Vector[PropertyLongStayPromotion] = Vector.empty,

    // as new engagement which coming from meta-api (psapi)
    metaEngagement: Option[MetaEngagement] = None,
    multiRoomSuggestions: List[EnrichedMultiRoomSuggestion] = List.empty,
    isBoutiqueHotel: Int = 0,
    roomBundles: List[RoomBundle] = Nil,
    breakfastPriceFromHotelSetting: Option[Double] = None,
    rocketmiles: Option[RocketMilesPublishedPricing] = None,
    lastYearBookingCount: Option[Int] = None,
    uniqueVisitorPerCity: Option[Int] = None,
    propertyMatchOrdering: Option[Int] = None,
    displayPaymentTypes: Option[Seq[DisplayPaymentType]] = None,
    multiHotelEligible: Option[Boolean],
    cartEligible: Option[Boolean],
    cheapestStayPackageRatePlans: Option[Seq[CheapestStayPackageRatePlans]] = None,
    isOnlyAlternativeRoom: Option[Boolean] = None,
    rateCategories: Option[RateCategoriesResponse] = None,
    propertySearchToken: Option[String] = None,
    cheapestEscapesRoom: Option[EnrichedChildRoom] = None,
    cheapestHourlyRoom: Option[EnrichedMasterRoom] = None,
    isSupportChildRate: Option[Boolean] = None,
    externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay] = None,
    hasHourlyRate: Option[Boolean] = None,
    priceChange: Option[DFPriceChange] = None,
    // Supply Growth
    growthProgramInfo: Option[GrowthProgramInfo] = None,
    taxRegistrationType: Option[TaxRegistrationType] = None
) {

  @JsonProperty("getAllRooms")
  val getAllRooms: Seq[EnrichedChildRoom] =
    if (rooms.isEmpty) { // For Property Search Rooms
      masterRooms.flatMap(_.childrenRooms)
    } else rooms // For Complex Search Rooms

  @JsonProperty("getCheapestRoom")
  val getCheapestRoom: Option[EnrichedChildRoom] =
    getAllRooms.find(_.uid == cheapestRoom.map(_.uid))

  @JsonProperty("getCheapestEligibleRoom")
  val getCheapestEligibleRoom: CheapestRoomWithSuggestion =
    if (isMseProperty.contains(true)) {
      CheapestRoomWithSuggestion(getAllRooms.headOption, false)
    } else {
      if (suggestedChildRooms.isEmpty) {
        val cheapest = cheapestRoom.map(_.uid)
        CheapestRoomWithSuggestion(getAllRooms.find(_.uid == cheapest), false)
      } else {
        CheapestRoomWithSuggestion(suggestedChildRooms.headOption, true)
      }
    }

}

case class LandmarkInfo(
    landmarkId: LandmarkIdType,
    landmarkCityId: CityIdType = 0L,
    landmarkSubtypeCategoryId: LandmarkSubtypeCategoryIdType = 0L,
    landmarkName: String = "",
    landmarkDistance: Double = 0L
)

@SerialVersionUID(1L)
final case class MetaEngagement(
    @JsonProperty(required = false)
    lastBookByCheckIn: Map[Long, DateTime]
)

@SerialVersionUID(1L)
case class AlternateDateSummary(
    checkIn: DateTime,
    los: LengthOfStay,
    pricingSummary: Map[String, PricingSummary]
)

@SerialVersionUID(19L)
case class AreaBookingCountModel(text: String, value: Int, cityTotal: Option[Int] = Some(0))

@SerialVersionUID(19L)
case class PropertyFilters(title: String, tags: Seq[FilterTag], filterGroups: Option[Seq[FilterGroup]] = None)

@SerialVersionUID(20L)
case class FilterTag(
    id: String,
    title: String,
    symbol: String,
    subTags: Option[List[FilterTag]] = None,
    groupId: Option[String] = None,
    syncId: Option[String] = None
)

@SerialVersionUID(22L)
case class FilterGroup(
    id: String,
    groupName: String
)

@SerialVersionUID(21L)
case class PropertyTooltip(taxAndServiceFeeText: Option[String] = None, extraBedTexts: Option[List[String]] = None)

@SerialVersionUID(22L)
case class CheapestRoom(dmcRoomId: String, uid: String)

@SerialVersionUID(23L)
case class EnrichedBookingYCSPromotion(id: Long, text: String, foreignText: String)

@SerialVersionUID(24L)
case class EnrichedBookingCancellation(
    code: String,
    fallbackCode: String,
    noShowPolicy: String,
    defaultNoShowPolicy: String,
    policies: Seq[String],
    defaultPolicies: Seq[String]
)

@SerialVersionUID(91L)
case class EnrichedBookingGiftCardEarning(amount: Double, giftCardGuid: String, offsetDays: Int, expiryDays: Int)

@SerialVersionUID(-585995435816857287L)
case class EnrichedBookingCashbackEarning(
    amount: Double,
    percentage: Double,
    cashbackGuid: String,
    offsetDays: Int,
    expiryDays: Int,
    cashbackVersion: Option[String] = None,
    cashbackType: Option[String] = None,
    tcUrl: Option[String] = None,
    rewardTypeId: Option[Int] = None,
    agodaCashbackValue: Option[Double] = None,
    promoCashbackValue: Option[Double] = None,
    cofundedCashbackValue: Option[Double] = None,
    promotionId: Option[Int] = None
)

@SerialVersionUID(25L)
case class EnrichedBookingRoom(
    uid: String,
    accountingEntity: Option[AccountingEntity],
    availabilityType: AvailabilityType,
    breakfastIncluded: Boolean,
    breakfastInfo: String,
    // cancellation block
    cancellation: EnrichedBookingCancellation,
    cancellationPolicy: String,
    cancellationPolicyCode: String,
    cancellationChargeType: Option[CancellationChargeSettingType],
    // end block
    pricing: BookingPrice,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    dmcId: Option[Long],
    dmcSpecificData: String,
    excluded: String,
    giftCardEarning: Option[EnrichedBookingGiftCardEarning] = None,
    hotelId: HotelIdType,
    included: String,
    isAgodaReception: Boolean = false,
    isNHA: Boolean = false, // aka isSingleRoomHotelAccommodation
    isNotCcRequired: Boolean,
    isPrepay: Boolean,
    // capacity block
    capacity: EnrichedCapacity,
    noOfAdults: Int,
    noOfChildren: Int,
    noOfExtrabeds: Int,
    numberOfRoom: Int,
    occupancy: Int,
    // end block
    partnerLoyaltyPoint: Option[BookingPartnerLoyaltyPoint] = None,
    paymentModels: PaymentModel,
    rateCategory: EnrichedBookingRateCategory,
    rateModel: RateModel,
    totalSaving: Option[Double] = None,
    // Fail-safe for affiliate
    rateModelType: Option[Int] = None,
    roomTypeId: RoomTypeId,
    roomTypeName: String,
    ycsRatePlanId: Long,
    sellInfo: EnrichedBookingSellInfo,
    taxSurchargeInfo: String,
    rateChannel: Option[EnrichedChannel] = None,
    correlationId: String,
    // YCSPromotion block
    promotion: Option[EnrichedBookingYCSPromotion],
    ycsForeignPromotionText: String,
    ycsPromotionID: Long,
    ycsPromotionText: String,
    // end block
    // below won't map to client dll, start block
    availableRooms: Int,
    chargeDiscount: Double,
    displayAmount: Double,
    displayCurrency: String,
    benefit: Seq[EnrichedBenefit],
    exchange: BookingPayment,
    npclnChannel: Option[Int] = None,
    borSupplier: Option[BORSupplier] = None,
    specialRequestOptions: Seq[SpecialRequestId],
    // end block
    // hardcode value fields, to remove but potentially break Affiliate CI
    @deprecated("to remove since FE will set static value by themself")
    hotelRemark: Option[String] = Some(""),
    @deprecated("to remove since FE will set static value by themself")
    lineItemId: Option[Int] = Some(1),
    @deprecated("to remove since FE will set static value by themself")
    pointMultiply: Option[Double] = Some(0.0),
    @deprecated("to remove since FE will set static value by themself")
    roomRemark: Option[String] = Some(""),
    @deprecated("to remove since FE will set static value by themself")
    numberOfBeds: Option[Int] = Some(0),
    // end block
    isAgodaAgency: Boolean, // as known as isMorp
    paymentChannels: List[Int] = List.empty,
    cashbackEarning: Option[EnrichedBookingCashbackEarning] = None,
    @deprecated("experiment de-integrated")
    childRoomTypeId: Option[RoomTypeId] = None,
    @deprecated("experiment de-integrated")
    childRoomName: Option[String] = None,
    promotionsBreakdown: Option[Map[LocalDate, List[Promotion]]] = None,
    channelDiscountSummary: Option[ChannelDiscountSummary] = None,
    finProductInfo: Option[FinanceProductInfo] = None
)

@SerialVersionUID(25L)
case class EnrichedBookingSellInfo(
    downlift: Double,
    downliftUsd: Double,
    priceTemplatedId: Option[Int],
    searchId: String,
    // sellTagId have logic on client side.
    sellTagId: Int,
    isAdvanceGuarantee: Boolean,
    offerId: Option[Int],
    priceOfferId: Option[String],
    fireDrillContract: Option[EnrichedFireDrillContract]
)

@SerialVersionUID(1L)
case class EnrichedFireDrillContract(
    contractId: Int,
    contractTypeId: FireDrillContractType,
    advancePay: Option[Boolean]
)

@SerialVersionUID(25L)
case class EnrichedBookingRateCategory(
    id: Int,
    Code: String,
    applyTo: String,
    Amount: Double,
    benefits: Seq[EnrichedBookingBenefit],
    inventoryType: Option[Int] = None,
    stayPackageType: Option[Int] = None,
    rateCategoryContentToken: Option[String] = None
)

@SerialVersionUID(25L)
case class EnrichedBookingBenefit(amount: Double, id: Long, remark: String)

@SerialVersionUID(26L)
case class EnrichedBookingPayment(
    siteExchangeRate: Double,
    upliftExchangeRate: Double,
    requestDecimal: Int,
    usdDecimalPlace: Int,
    localDecimalPlace: Int
)

@SerialVersionUID(27L)
case class EnrichedBookingItem(
    rooms: List[EnrichedBookingRoom] = Nil,
    los: Int,
    booking: List[EnrichedEBEBooking] = List.empty
)

@SerialVersionUID(1L)
case class ZenithProductHotels(numberOfBookings: Option[Int])

/** DF Response will not Enriched Hotel for Recommendation * */
@SerialVersionUID(28L)
case class RecommendationHotel(
    h: Hotel,
    enrichedChannel: EnrichedChannel,
    cheapestRoomCancellation: EnrichedCancellation,
    cheapestPriceCrossedDisplay: DisplayBasis,
    cheapestPriceCrossedOut: DisplayBasis,
    cheapestRoomCharges: Seq[EnrichedCharge],
    breakfastFlag: CmsFlagData,
    recommendationMseResults: List[RecomendationMseResults],
    productHotels: Option[ZenithProductHotels] = None,
    totalDiscount: Option[Int] = None,
    loyaltyDisplay: Option[LoyaltyDisplay] = None,
    cheapestRoomPackaging: Option[EnrichedPackaging] = None,
    cheapestRoomPayment: Option[EnrichedPayment] = None
)

/** DF Hotel Enriched Information
  */
@SerialVersionUID(29L)
case class EnrichedHotelWithOutContent(
    propertyId: Long,
    cityId: CityId,
    countryId: CountryId,
    areaId: HotelAreaIdType,
    cheapestRoom: Option[EnrichedCheapestRoom],
    needOccupancySearch: Option[Boolean],
    rooms: Seq[EnrichedChildRoom],
    isReady: Option[Boolean],
    supplierId: Option[SupplierId],
    isDisplayAsAmount: Option[Boolean],
    suggestedBasis: Option[ApplyType],
    suggestedPrice: Option[SuggestedPrice],
    tooltip: PropertyTooltip,
    ratePlanSelection: List[Int],
    enableAPS: Boolean,
    hotelSummary: Option[PriceHotelSummary] = None,
    paxSetting: Option[PartnerPaxOption] = None,
    pseudoCouponMessage: Option[EnrichedPseudoCouponMessage],
    dfFeatures: Option[HotelFeatures],
    promotionEligible: Option[Boolean] = None,
    booking: Option[EnrichedBookingItem] = None,
    taxType: Option[TaxType] = None,
    supplierSummaries: Map[Long, EnrichedSupplierSummary] = Map.empty,
    suggestedRooms: List[EnrichedChildRoom] = List.empty,
    roomSwapping: List[AlternativeRoom] = List.empty,
    maxPointsMax: Option[EnrichedPointMax] = None,
    maxPointsMaxBadge: Option[PointsMaxBadge] = None,
    taxMetaData: List[TaxMetaData] = List.empty,
    activeHotelsInCity: Option[Long] = None,
    hotelAggregationInfo: Option[HotelAggregationInfo] = None,
    topSellingPoints: List[TopSellingPoint] = Nil,
    @deprecated("no longer in use")
    isWhiteListHotel: Boolean = false,
    internalUtilityDataFromDF: Option[InternalUtilityDataFromDF] = None,
    stayOccupancy: Option[StayOccupancy] = None,
    multiRoomSuggestions: List[EnrichedMultiRoomSuggestion] = List.empty,
    bookingMaxOccupancy: Option[BookingMaxOccupancy] = None,
    roomBundle: List[RoomBundleEnrichChildRoom] = Nil,
    breakfastPriceFromHotelSetting: Option[Double] = None,
    rocketmiles: Option[RocketMilesPublishedPricing] = None,
    cheapestStayPackageRatePlans: Option[Seq[CheapestStayPackageRatePlans]] = None,
    isOnlyAlternativeRoom: Option[Boolean] = None,
    longStayRoomId: Option[String] = None,
    propertySearchToken: Option[String] = None,
    cheapestEscapesRoom: Option[EnrichedChildRoom] = None,
    cheapestHourlyRoom: Option[EnrichedChildRoom] = None,
    customizableRoomGridOptions: Map[MasterRoomTypeId, List[CustomizableRoomGridOptions]] = Map.empty,
    externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay] = None,
    growthProgramInfo: Option[GrowthProgramInfo] = None,
    priceChange: Option[DFPriceChange] = None,
    taxRegistrationType: Option[TaxRegistrationType] = None
) {

  // for property endpoint, enriching all content
  def toWithAllContent(
      masterRooms: Seq[EnrichedMasterRoom],
      singleRoomNonHotelAccommodation: Option[Boolean],
      suggestedMasterRoom: Seq[EnrichedMasterRoom],
      isMseProperty: Option[Boolean] = None,
      soldOutRooms: Option[EnrichedSoldOutRooms] = None,
      roomBundlesWithContent: List[RoomBundle] = List.empty,
      cheapestHourlyRoom: Option[EnrichedMasterRoom] = None
  ): EnrichedHotelWithContent = {

    /** create bookingItem
      */
    val normalRoomBookingItemWithContent = booking.map { book =>
      overrideEnrichedBookingItemWithContent(book, masterRooms, singleRoomNonHotelAccommodation)
    }

    // For mixandsave single booking flow, we will have 2 more additional BookingItems; otherwise the list is empty
    val mixAndSaveBookingItemWithContents = roomBundle
      .flatMap(_.segments)
      .map { segment =>
        val masterRoomBundleSegmentOpt =
          roomBundlesWithContent.flatMap(_.segments).find(s => s.checkIn == segment.checkIn && s.los == segment.los)
        val mixAndSaveMasterRooms = masterRoomBundleSegmentOpt.map(_.masterRooms).getOrElse(Seq.empty)
        segment.bookingItem.map(book =>
          overrideEnrichedBookingItemWithContent(book, mixAndSaveMasterRooms, singleRoomNonHotelAccommodation)
        )
      }
      .flatten

    val mixAndSaveBookingRooms = mixAndSaveBookingItemWithContents.flatMap(_.rooms)
    val mixAndSaveEbeBookings  = mixAndSaveBookingItemWithContents.flatMap(_.booking)

    // Append mixandsave's rooms and ebebookings to the normal BookingItem if exist
    // If there is no mixandsave BookingItem Then bookingItemWithContent = normalRoomBookingItemWithContent
    val bookingItemWithContent = normalRoomBookingItemWithContent.map { bookingItem =>
      bookingItem.copy(
        rooms = bookingItem.rooms ++ mixAndSaveBookingRooms,
        booking = bookingItem.booking ++ mixAndSaveEbeBookings
      )
    }

    EnrichedHotelWithContent(
      propertyId,
      cityId,
      countryId,
      areaId,
      cheapestRoom,
      needOccupancySearch,
      masterRooms,
      isReady,
      supplierId,
      isDisplayAsAmount,
      suggestedBasis,
      suggestedPrice,
      Seq(),
      tooltip,
      ratePlanSelection,
      enableAPS,
      paxSetting = paxSetting,
      pseudoCouponMessage = pseudoCouponMessage,
      dfFeatures = dfFeatures,
      promotionEligible = promotionEligible,
      booking = bookingItemWithContent,
      taxType = taxType,
      supplierSummaries = supplierSummaries,
      isMseProperty = isMseProperty,
      suggestedRooms = suggestedMasterRoom.toList,
      roomSwapping = this.roomSwapping,
      maxPointsMax = maxPointsMax,
      maxPointsMaxBadge = maxPointsMaxBadge,
      taxMetaData = taxMetaData,
      activeHotelsInCity = activeHotelsInCity,
      soldOutRooms = soldOutRooms,
      topSellingPoints = if (singleRoomNonHotelAccommodation.getOrElse(false)) {
        topSellingPoints.filterNot(_.id == TopSellingPointIds.PriceDifferencePercent)
      } else topSellingPoints,
      internalUtilityData = InternalUtilityData(internalUtilityDataFromDF = internalUtilityDataFromDF),
      stayOccupancy = stayOccupancy,
      multiRoomSuggestions = multiRoomSuggestions,
      bookingMaxOccupancy = bookingMaxOccupancy,
      rocketmiles = rocketmiles,
      isOnlyAlternativeRoom = isOnlyAlternativeRoom,
      cheapestHourlyRoom = cheapestHourlyRoom,
      externalLoyaltyDisplay = externalLoyaltyDisplay,
      growthProgramInfo = growthProgramInfo,
      priceChange = priceChange,
      taxRegistrationType = taxRegistrationType
    )
  }

  def overrideEnrichedBookingItemWithContent(
      book: EnrichedBookingItem,
      masterRooms: Seq[EnrichedMasterRoom],
      singleRoomNonHotelAccommodation: Option[Boolean]
  ): EnrichedBookingItem = {
    // This use the original code
    val overrideRooms = book.rooms.map { r =>
      val roomName = masterRooms
        .filter(_.childrenRooms.exists(_.typeId.contains(r.roomTypeId)))
        .flatMap(_.englishName)
        .headOption
        .getOrElse("")

      val channel =
        masterRooms.flatMap(_.childrenRooms.find(_.typeId.contains(r.roomTypeId))).headOption.flatMap(_.channel)

      val rateCategoryContentToken = masterRooms
        .flatMap(
          _.childrenRooms.find(
            _.uid.contains(r.uid)
          )
        )
        .headOption
        .flatMap(_.ratePlan.flatMap(_.rateCategoryContentToken))

      r.copy(
        roomTypeName = roomName,
        rateChannel = channel,
        isNHA = singleRoomNonHotelAccommodation.getOrElse(false),
        rateCategory = r.rateCategory.copy(rateCategoryContentToken = rateCategoryContentToken)
      )
    }

    book.copy(
      rooms = overrideRooms,
      booking = book.booking.map(b =>
        b.copy(hotel = b.hotel.map { h =>
          h.copy(room = overrideRooms.filter(_.uid == h.room.headOption.map(_.uid).getOrElse("")))
        })
      )
    )
  }

  // for complex search, enriching masterRooms
  def toWithRoomContent(
      masterRooms: Seq[EnrichedMasterRoom],
      isMseProperty: Option[Boolean] = None,
      cheapestHourlyRoom: Option[EnrichedMasterRoom] = None
  ): EnrichedHotelWithContent =
    EnrichedHotelWithContent(
      propertyId,
      cityId,
      countryId,
      areaId,
      cheapestRoom,
      needOccupancySearch,
      masterRooms,
      isReady,
      supplierId,
      isDisplayAsAmount,
      suggestedBasis,
      suggestedPrice,
      rooms,
      tooltip,
      ratePlanSelection,
      enableAPS,
      this.hotelSummary,
      paxSetting = paxSetting,
      pseudoCouponMessage = pseudoCouponMessage,
      dfFeatures = dfFeatures,
      promotionEligible = promotionEligible,
      booking = booking,
      taxType = taxType,
      supplierSummaries = supplierSummaries,
      isMseProperty = isMseProperty,
      suggestedChildRooms = suggestedRooms,
      maxPointsMax = maxPointsMax,
      maxPointsMaxBadge = maxPointsMaxBadge,
      activeHotelsInCity = activeHotelsInCity,
      hotelAggregationInfo = hotelAggregationInfo,
      internalUtilityData = InternalUtilityData(internalUtilityDataFromDF = internalUtilityDataFromDF),
      stayOccupancy = stayOccupancy,
      multiRoomSuggestions = multiRoomSuggestions,
      bookingMaxOccupancy = bookingMaxOccupancy,
      rocketmiles = rocketmiles,
      cheapestStayPackageRatePlans = cheapestStayPackageRatePlans,
      isOnlyAlternativeRoom = isOnlyAlternativeRoom,
      cheapestEscapesRoom = cheapestEscapesRoom,
      cheapestHourlyRoom = cheapestHourlyRoom,
      externalLoyaltyDisplay = externalLoyaltyDisplay,
      growthProgramInfo = growthProgramInfo,
      priceChange = priceChange,
      taxRegistrationType = taxRegistrationType
    )

}

/** DF Hotel Enriched Information and Map Master Room and Room Information from content api
  */
@SerialVersionUID(14L) //scalastyle:ignore
@JsonIgnoreProperties(Array("rooms", "pseudoCouponMessage"))
case class EnrichedHotelWithContent(
    propertyId: Long,
    cityId: CityId,
    countryId: CountryId,
    areaId: HotelAreaIdType,
    cheapestRoom: Option[EnrichedCheapestRoom] = None,
    needOccupancySearch: Option[Boolean] = None,
    masterRooms: Seq[EnrichedMasterRoom],
    isReady: Option[Boolean] = None,
    supplierId: Option[SupplierId] = None,
    isDisplayAsAmount: Option[Boolean] = None,
    suggestedBasis: Option[ApplyType] = None,
    suggestedPrice: Option[SuggestedPrice] = None,
    @JsonProperty("rooms")
    rooms: Seq[EnrichedChildRoom],
    tooltip: PropertyTooltip,
    ratePlanSelection: List[Int],
    enableAPS: Boolean,
    hotelSummary: Option[PriceHotelSummary] = None,
    paxSetting: Option[PartnerPaxOption] = None,
    @JsonProperty("pseudoCouponMessage")
    pseudoCouponMessage: Option[EnrichedPseudoCouponMessage] = None,
    dfFeatures: Option[HotelFeatures] = None,
    promotionEligible: Option[Boolean] = None,
    booking: Option[EnrichedBookingItem] = None,
    taxType: Option[TaxType] = None,
    supplierSummaries: Map[Long, EnrichedSupplierSummary] = Map.empty,
    isMseProperty: Option[Boolean] = None,
    @deprecated("use cheapestRoom instead")
    // mseCheapestRoom: Option[EnrichedCheapestRoom] = None,
    suggestedRooms: List[EnrichedMasterRoom] = List.empty,
    roomSwapping: List[AlternativeRoom] = List.empty,
    suggestedChildRooms: List[EnrichedChildRoom] = List.empty,
    maxPointsMax: Option[EnrichedPointMax] = None,
    maxPointsMaxBadge: Option[PointsMaxBadge] = None,
    taxMetaData: List[TaxMetaData] = List.empty,
    activeHotelsInCity: Option[Long] = None,
    hotelAggregationInfo: Option[HotelAggregationInfo] = None,
    soldOutRooms: Option[EnrichedSoldOutRooms] = None,
    topSellingPoints: List[TopSellingPoint] = Nil,
    internalUtilityData: InternalUtilityData = new InternalUtilityData,
    beddingInfo: Set[BeddingInfo] = Set.empty[BeddingInfo],
    stayOccupancy: Option[StayOccupancy] = None,
    multiRoomSuggestions: List[EnrichedMultiRoomSuggestion] = List.empty,
    bookingMaxOccupancy: Option[BookingMaxOccupancy] = None,
    rocketmiles: Option[RocketMilesPublishedPricing] = None,
    displayPaymentTypes: Option[Seq[DisplayPaymentType]] = None,
    cheapestStayPackageRatePlans: Option[Seq[CheapestStayPackageRatePlans]] = None,
    isOnlyAlternativeRoom: Option[Boolean] = None,
    rateCategories: Option[RateCategoriesResponse] = None,
    cheapestEscapesRoom: Option[EnrichedChildRoom] = None,
    cheapestHourlyRoom: Option[EnrichedMasterRoom] = None,
    isSupportChildRate: Option[Boolean] = None,
    externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay] = None,
    growthProgramInfo: Option[GrowthProgramInfo] = None,
    priceChange: Option[DFPriceChange] = None,
    taxRegistrationType: Option[TaxRegistrationType] = None
)

@SerialVersionUID(69L)
case class EnrichedSupplierSummary(
    supplierId: Long,
    isReady: Boolean,
    supplierType: Option[String] = None,
    supplierHotelId: Option[String] = None
)

@SerialVersionUID(70L)
case class SupplierInformation(
    supplierId: Long,
    supplierDisplayName: String,
    supplierImageUrls: Map[String, String],
    isAgodaBand: Boolean,
    logicalSupplierId: Option[Long],
    dmcType: Option[Int],
    phone: Option[String],
    email: Option[String]
)

@SerialVersionUID(71L)
case class GroupMseRoom(
    mseGroupId: String,
    @deprecated
    childrenRooms: List[EnrichedChildRoom] = List.empty,
    benefits: Seq[EnrichedBenefit] = Seq.empty,
    capacity: Option[EnrichedCapacity] = None,
    cancellation: Option[EnrichedCancellation] = None
)

@SerialVersionUID(30L)
case class EnrichedCheapestRoom(description: String, dmcRoomId: String, uid: String)

@JsonIgnoreProperties(ignoreUnknown = true)
@SerialVersionUID(31L)
case class EnrichedMasterRoom(
    maxOccupancy: Int,
    maxExtraBeds: Int,
    typeId: Long,
    englishName: Option[String],
    name: Option[String],
    images: Vector[ImageResponse],
    facilities: Vector[Feature],
    // TODO change the field name to bedConfiguration after affiliate update their model
    @JsonProperty(value = "bedConfiguration", required = false)
    @encodedName("bedConfiguration")
    bedConfiguration2: Option[BedConfigurationResponse],
    childrenRooms: List[EnrichedChildRoom],
    @JsonProperty(value = "facilityGroups", required = false)
    facilityGroups: Vector[FeatureGroup],
    @JsonProperty(value = "features", required = false)
    features: Vector[RoomFeatureResponse],
    @deprecated(message = "mse groupping is no longer supported")
    groupMseRooms: List[GroupMseRoom] = List.empty,
    isAllowChildren: Option[Boolean] = None,
    isYcsMaster: Option[Boolean] = None,
    @deprecated(message = "use suitabilityTypes")
    suitabilityType: Option[String] = None,
    hotelRoomTypeAlternateName: Option[String] = None,
    numberOfRoom: Option[Int] = None,
    roomSizeInclTerrace: Option[Boolean] = None,
    extrabedChildAge: Option[Int] = None,
    maxInfantInRoom: Option[Int] = None,
    localizations: Option[MasterRoomResponseLocalization] = None,
    isDormitory: Option[Boolean] = None,
    roomReviewInformation: Option[MasterRoomReviewInformation] = None,
    @JsonProperty(required = false)
    suitabilityTypes: Seq[String] = Seq.empty[String],
    alternativeDate: Option[DateTime] = None,
    styleName: Option[String] = None,
    isRoomDayUsed: Option[Boolean] = None,
    topFacilities: Option[Vector[Feature]] = None,
    roomLicenseId: Option[String] = None,
    customizableRoomGridOptions: Option[List[CustomizableRoomGridOptions]] = None,
    isRecommended: Option[Boolean] = None,
    roomDescriptionAI: Option[String] = None,
    videos: Vector[VideoResponse],
    aiRoomShortDescription: Option[String] = None,
    recommendedUpgrade: Option[RecommendedUpgrade] = None
)

@SerialVersionUID(1L)
case class EnrichedPastBookingInfo(
    bookingCount: Int,
    avgPrice: DisplayPrice,
    minPrice: DisplayPrice,
    maxPrice: DisplayPrice
)

@SerialVersionUID(1L)
case class EnrichedPastBookingInfoResponse(enrichedBookingInfo: Map[String, EnrichedPastBookingInfo])

@JsonIgnoreProperties(Array("pricingRoomOption"))
@SerialVersionUID(32L)
case class EnrichedChildRoom(
    uid: Option[String] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    typeId: Option[Long] = None,
    dmcRoomId: Option[String] = None,
    rateModel: Option[Int] = None,
    ratePlan: Option[EnrichedRatePlan] = None,
    benefits: Seq[EnrichedBenefit] = Seq.empty,
    filterTags: Seq[FilterTagType] = Seq.empty,
    isBreakfastIncluded: Option[Boolean] = None,
    channel: Option[EnrichedChannel] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    supplierId: Option[Long] = None,
    subSupplierId: Option[Int] = None,
    availableRooms: Option[Int] = None,
    capacity: Option[EnrichedCapacity] = None,
    hourlyAvailableSlots: Option[Seq[TimeInterval]] = None,
    localCurrencyCode: Option[String] = None,
    requestedCurrencyCode: Option[String] = None,
    isFiredrill: Option[Boolean] = None,
    pricing: Map[String, EnrichedPricing] = Map.empty,
    packaging: Option[EnrichedPackaging] = None,
    promotions: Option[EnrichedPromotion] = None,
    pointsMax: Option[EnrichedPointMax] = None,
    payment: Option[EnrichedPayment] = None,
    dmcPolicyText: Option[EnrichedDMCPolicyText] = None,
    isFit: Option[Boolean] = None,
    @JsonProperty("pricingRoomOption")
    pricingRoomOption: Option[Room] = None,
    tooltip: RoomTooltip = new RoomTooltip(),
    gmtOffset: Option[Int] = None,
    needOccupancySearch: Option[Boolean] = None,
    giftCard: Option[GiftCard] = None,
    offerId: Option[Int] = None,
    priceOfferId: Option[String] = None,
    pseudoCoupon: Option[PseudoCoupon] = None,
    promotionEligible: Option[Boolean] = None,
    packageEligible: Option[Boolean] = None,
    rateRepurposeInfos: Option[Seq[RateRepurposeInfo]] = None,
    isRepurposed: Option[Boolean] = None,
    isAveragePrice: Option[Boolean] = None,
    pointsMaxBadge: Option[PointsMaxBadge] = None,
    npclnChannel: Option[Int] = None,
    priceInfos: Map[String, PriceInfos] = Map.empty,
    isRoomTypeNotGuarantee: Option[Boolean] = None,
    @deprecated("no longer used by FE")
    mseGroupId: Option[String] = None,
    @deprecated(message = "use campaignPromotions instead")
    campaignPromotion: Option[EnrichedCampaignPromotion] = None,
    roomLevelBOR: Option[EnrichedBOR] = None,
    campaignPromotions: Option[List[EnrichedCampaign]] = None,
    loyaltyResponse: Option[LoyaltyPaymentBoundaries] = None,
    paymentFeature: Option[Map[String, PaymentFeature]] = None,
    morErpAdminId: Option[Int] = None,
    hasChildBreakfastSurcharge: Option[Boolean] = Option(false),
    corBreakdown: CorBreakdown = transformers.CorBreakdown(),
    promotionUrgencyMessage: Option[String] = None,
    roomIdentifiers: Option[String] = None,
    corSummary: Option[CorSummary] = None,
    isMultiRoomPromotion: Option[Boolean] = None,
    pricingMessages: Seq[RoomPricingMessage] = Seq.empty,
    roomStatus: Option[RatePlanStatusType] = None,
    fencedRate: Option[FencedRate] = None,
    @deprecated("no longer used by FE")
    priceChange: Option[PriceChange] = None,
    supplierFinancialData: Option[SupplierFinancialData] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    masterTypeId: Option[Long] = None,
    soldOutPredictionTime: Option[TimeInMinute] = None,
    cancellationDuplicated: Option[Boolean] = None,
    roomWithBreakfastUid: Option[RoomUID] = None,
    pricingDisplayPresentation: Option[PricingDisplayPresentation] = None,
    roomWithBreakfastSymmetricUid: Option[RoomUID] = None,
    isMultipleRoomAssignmentPrice: Option[Boolean] = None,
    isOverrideChildTypeRequest: Option[Boolean] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    expiresAt: Option[Long] = None,
    agodaRefundInfo: Option[AgodaRefundInfo] = None,
    loyaltyDisplay: Option[LoyaltyDisplay] = None,
    isEasyCancel: Option[Boolean] = None,
    roundToNineRuleId: Option[Int] = Some(0),
    childRateSettings: Option[Seq[ChildRateSetting]] = None,
    isThaiGovEligible: Option[Boolean] = None,
    cart: Option[Cart] = None,
    multiHotelEligible: Option[Boolean] = None,
    cartEligible: Option[Boolean] = None,
    checkInInformation: Option[PricingCheckInInformation] = None,
    localVoucher: Option[LocalVoucher] = None,
    stayPackageType: Option[Int] = None,
    pulseCampaignMetadata: Option[PulseCampaignMetadata] = None,
    originalRoomDetail: Option[OriginalRoomDetail] = None,
    walletPromotions: Option[Seq[EnrichedWalletPromotion]] = None,
    cashback: Option[Cashback] = None,
    roomOfferNameType: Option[RoomOfferNameType] = None,
    roomOfferBookingHistory: Option[RoomOfferBookingHistory] = None,
    @deprecated("Taiwan campaign is over")
    isTaiwanCampaignEligible: Option[Boolean] = None,
    rewardOptions: Map[String, RewardOption] = Map.empty,
    selectedRewardOption: Option[String] = None,
    asoBenefitDiscountInfo: Option[AsoBenefitDiscountInfo] = None,
    isCartRestricted: Option[Boolean] = Some(false),
    allowMultipleBooking: Option[Boolean] = Some(false),
    @deprecated("experiment de-integrated")
    roomName: Option[String] = None,
    isCheckInTimeRequired: Option[Boolean] = Some(false),
    externalLoyaltyDisplay: Option[ExternalLoyaltyDisplay] = None,
    dynamicRoomMappingType: Option[Int] = None,
    finProductInfo: Option[FinanceProductInfo] = None,
    occupancyMessages: Option[Seq[OccupancyMessage]] = None,
    isJapanChildRateApplied: Option[Boolean] = None,
    directConnectProviderId: Option[Int] = None,
    cashbackRedemptionBoundaries: Option[LoyaltyPaymentBoundaries] = None,
    consolidatedAppliedDiscount: Option[ConsolidatedAppliedDiscount] = None,
    occDisplay: Option[OccupancyDisplay] = None,
    promotionsBreakdown: Map[LocalDate, List[Promotion]] = Map.empty,
    msePricingToken: Option[String] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Long])
    priceAdjustmentId: Option[Long] = None,
    priority: Option[Double] = None
) {

  val guests   = capacity.map(c => c.adults + c.children).getOrElse(0)
  val extraBed = capacity.map(c => c.extraBed).getOrElse(0)

  def displayPRPNAllInclusive(): Double =
    pricing.headOption.map(p => p._2.display.perRoomPerNight.allInclusive).getOrElse(0.0)

  def displayPRPNExclusive(): Double =
    pricing.headOption.map(p => p._2.display.perRoomPerNight.exclusive).getOrElse(0.0)

}

@SerialVersionUID(33L)
case class PointsMaxBadge(showBadge: Boolean, badgeText: String)

//this is temporary model
@SerialVersionUID(34L)
case class PriceInfos(
    hotelId: Long,
    roomTypeId: RoomTypeId,
    ratePlan: Int,
    language: LanguageId,
    currency: String,
    checkIn: DateTime,
    lengthOfStay: Int,
    adults: Int,
    children: Int,
    promotion: Option[DFPromotionWrapper],
    marginPercentage: MarginPercent,
    prices: List[DFPrice],
    cxlCode: String,
    remainingRooms: Int,
    extraBeds: Int,
    breakfast: Boolean,
    allotmentRatePlan: Int,
    steakhouseData: Option[DFDownliftDataWrapper],
    exchangeRate: Option[ExchangeRate],
    dmcData: Option[DFDmcData],
    giftCard: Option[DFGiftCard],
    priceDiffBetweenAPOAndLeastExpensiveRoom: Double,
    /*Booking Form use this from childRoom level
                                         supplierId: SupplierId,*/
    /*Use in RateCategory
                                         rateCategoryId: Long,*/
    rateCategory: Option[RateCategory],
    channelId: Int,
    paymentModel: PaymentModel,
    sellerBrand: String,
    priusData: Option[PriusOutput],
    crossOutRates: Option[CrossOutRates],
    corTypeFlag: CorTypeFlag,
    isFit: Option[Boolean],
    sellAllIn: Double,
    sellAllAgoda: Double,
    chargeByHotel: Double,
    payAtHotel: Boolean,
    sellExWithExtraBed: Double,
    sellInWithExtraBed: Double,
    isYcs: Boolean,
    // Booking Form use paymentOption
    noCreditCard: Boolean,
    // Booking Form use paymentOption
    prepaymentRequired: Boolean,
    isFireDrill: Boolean,
    paymentOptions: Int,
    benefits: List[DFBenefit],
    isDomesticOnly: Boolean,
    accountEntity: Option[AccountingEntity],
    rebate: Option[Rebate],
    npclnChannel: Option[Int],
    pseudoCoupon: Option[DFPseudoCoupon],
    /*Booking Form use this from childRoom level
                                         masterRoomId: Option[RoomTypeId],*/
    promotions: List[DFDailyPromotionWrapper] = List.empty,
    promotionsBreakdown: List[DFDailyPromotionWrapper] = List.empty,
    uid: String,
    isReutilized: Boolean,
    repurposedInfo: Option[RateRepurposeInfo],
    rateRepurposeInfos: Seq[RateRepurposeInfo],
    maxExtraBeds: Int,
    isAveragePrice: Boolean,
    // Pricing from Client Logic
    netExclusive: Double,
    tax: Double,
    fee: Double,
    margin: Double,
    processingFee: Double,
    discount: Double,
    downliftAmount: Double,
    netInclusive: Double,
    sellExclusive: Double,
    sellInclusive: Double,
    /*Booking Form use this from childRoom level
                                         dmcRoomId: Option[String],*/
    priceSummary: DFPriceSummary,
    occupancy: Int,
    cashback: Option[DFCashback] = None
)

@SerialVersionUID(35L)
case class DFDailyPromotionWrapper(
    date: DateTime,
    promotion: DFPromotionWrapper,
    promotionsBreakdown: List[DFPromotionWrapper]
)

@SerialVersionUID(36L)
case class DFDownliftDataWrapper(percent: Double, templateId: Int, source: DownliftSource)

@SerialVersionUID(37L)
case class DFPromotionWrapper(
    id: Int = 0,
    typeId: Int = 0,
    discountType: DiscountType = DiscountTypes.Other,
    cmsTypeId: Int = 0,
    cmsDiscountTypeId: Int = 0,
    minNightStay: Int = 0,
    value: Double = 0.0,
    minRooms: Int = 0,
    minAdvPurchase: Int = 0,
    maxAdvPurchase: Int = 0,
    isStackable: Boolean = false,
    rewardPoint: Int = 1
)

@SerialVersionUID(38L)
case class RoomTooltip(
    corText: Option[String] = None,
    capacityText: Option[String] = None,
    availableRoomsText: Option[String] = None
)

@SerialVersionUID(39L)
case class EnrichedRatePlan(
    id: Long,
    englishName: String,
    name: String,
    promotionTypeId: Option[Int] = None,
    promotionTypeName: Option[String] = None,
    localizedName: Option[String] = None,
    rateCategoryContentToken: Option[String] = None,
    mainImage: Option[RatePlanMainImage] = None,
    checkIn: Option[RateCategoryCheckTime] = None,
    checkOut: Option[RateCategoryCheckTime] = None,
    questionsToBookers: Option[String] = None,
    minNightsStay: Option[Int] = None,
    isChildRateEnabled: Boolean = false,
    isAcceptCustomerRequest: Boolean = false
)

@SerialVersionUID(40L)
case class EnrichedBenefit(
    id: Long,
    description: String,
    defaultDescription: Option[String] = None,
    symbol: String,
    remark: String,
    value: Double,
    unit: BenefitUnit = BenefitUnits.Unknown,
    targetType: BenefitTargetType = BenefitTargetTypes.Regular,
    structuredBenefitInfo: Option[List[StructuredBenefitInfo]] = None,
    benefitValue: Option[DisplayBasis] = None,
    labels: Option[List[String]] = None
)

@SerialVersionUID(41L)
case class EnrichedChannel(id: Long, description: String, symbol: String)

@SerialVersionUID(42L)
case class EnrichedPayment(
    taxReceipt: EnrichedTaxReceipt,
    payLater: EnrichedPayLater,
    cancellation: EnrichedCancellation,
    noCreditCard: EnrichedNoCreditCard,
    payAtHotel: EnrichedPayAtHotel,
    paymentModel: PaymentModel,
    payAtCheckIn: EnrichedPayAtCheckIn,
    m150: Option[M150Info] = None,
    noPrePaymentRequired: Option[EnrichedNoPrePaymentRequired] = None,
    installmentDetails: Option[InstallmentDetails] = None,
    paymentChannels: Option[List[Int]] = None
)

@SerialVersionUID(43L)
case class EnrichedTaxReceipt(
    isEligible: Boolean,
    isDomestic: Boolean,
    title: String,
    description: String,
    symbol: String,
    detail: Option[EnrichedTaxReceiptDetail]
)

@SerialVersionUID(44L)
case class EnrichedPayLater(
    isEligible: Boolean = false,
    authDate: DateTime,
    chargeDate: DateTime,
    title: String = "",
    description: String = "",
    symbol: String
)

@SerialVersionUID(45L)
case class EnrichedCancellation(
    code: String,
    noShowPolicy: String,
    defaultNoShowPolicy: String,
    policies: Seq[String],
    defaultPolicies: Seq[String],
    cancellationType: CancellationType,
    description: String,
    defaultDescription: String,
    freeCancellationDate: Option[DateTime],
    title: String,
    symbol: String,
    cancellationPolicies: Option[CancellationPoliciesResultEntry] = None,
    currentCancellationType: CancellationType = CancellationTypes.None,
    externalUrl: Option[String] = None
)

@SerialVersionUID(46L)
case class EnrichedNoCreditCard(isEligible: Boolean, title: String, description: String, symbol: String)

@SerialVersionUID(47L)
case class EnrichedPayAtHotel(isEligible: Boolean, title: String, description: String, symbol: String)

@SerialVersionUID(48L)
case class EnrichedPromotion(
    id: Long,
    title: String,
    description: String,
    defaultDescription: String,
    symbol: String,
    discount: models.starfruit.Discount,
    @JsonProperty(required = false)
    discountMessage: String = "",
    isFlashDeal: Boolean = false,
    flashDealDescription: String = "",
    @JsonProperty(required = false)
    showDiscountMessage: Boolean = false,
    appliedPromotions: List[AppliedPromotion] = List.empty,
    typeId: Option[Int] = Some(0),
    typeName: Option[String] = None,
    applicableUntil: Option[DateTime] = None
)

@SerialVersionUID(49L)
case class AppliedPromotion()

@SerialVersionUID(50L)
case class EnrichedCharge(
    name: String,
    description: String,
    chargeType: ChargeType,
    breakdown: Seq[EnrichedChargeBreakdown],
    totalToHotel: models.starfruit.Price,
    totalToAgoda: models.starfruit.Price,
    total: models.starfruit.Price
)

@SerialVersionUID(51L)
case class EnrichedPricing(
    display: DisplayBasis,
    crossedOut: DisplayBasis,
    options: Seq[EnrichedCharge],
    charges: Seq[EnrichedCharge],
    extraInfo: Option[ExtraInformation] = None,
    displaySummary: DisplaySummary,
    partner: Option[EnrichedPartners] = None,
    discountMessages: Option[Seq[EnrichedDiscountMessage]] = Some(Seq.empty),
    netDisplay: Option[NetDisplayBasis] = None,
    apsPeek: Option[APSPeek] = None,
    totalDiscountPercent: Int = 0,
    promotionPricePeek: Option[PromotionPricePeek] = None,
    breakfastPrice: Option[DisplayBasis] = None,
    promotionDiscountAmount: Option[Double] = None,
    rocketmiles: Option[RocketmilesPricing] = None,
    packagePriceAndSaving: Option[PackagePriceAndSaving] = None,
    apsPeekWithoutDownlift: Option[APSPeek] = None,
    channelDiscountSummary: Option[ChannelDiscountSummary] = None,
    priceDifferenceWithPreviousCall: Option[PriceDifference] = None,
    promotionsCumulative: Seq[PromotionCumulative] = Seq.empty,
    longStay: Option[LongStay] = None,
    externalLoyaltyPricing: Option[ExternalLoyaltyPricing] = None,
    goToTravelInfo: Option[GoToTravelInfo] = None,
    priceBreakdownPerOccupancy: Option[PriceBreakdownPerOccupancy] = None
)

case class EnrichedPackaging(token: EnrichedPackagingToken, pricing: Map[String, EnrichedPackagingPricing])

case class EnrichedPackagingToken(clientToken: String, interSystemToken: String)

case class EnrichedPackagingPricing(
    display: PackageDisplayBasis,
    crossedOut: PackageDisplayBasis,
    @deprecated("use crossedOutSaving.totalSaving.amount instead")
    saving: PackageDisplayBasis,
    charges: Seq[EnrichedCharge],
    @deprecated("use crossedOutSaving.totalSaving.percent instead")
    totalDiscountPercent: Int = 0,
    @deprecated("use productBreakDown.crossedOut instead but need to be type Property")
    hotelOriginalRate: Option[PackageDisplayBasis] = None,
    priceChange: Option[PackageDisplayBasis] = None,
    @deprecated("use originalPrice for price and crossedOutSaving.originalSaving for saving")
    originalPriceAndSaving: Option[PackageOriginalPriceAndSaving] = None,
    originalPrice: Option[PackageDisplayBasis] = None,
    pseudoCoupon: Option[PackageDisplayBasis] = None,
    rebate: Option[PackageRebate] = None,
    productBreakDown: Seq[EnrichedPackageProductDisplayPrice] = Seq.empty,
    additionalRate: Option[PackageDisplayBasis] = None,
    crossedOutSaving: Option[PackageDetailedSaving] = None,
    additionalRateSaving: Option[PackageDetailedSaving] = None,
    cashbackRebate: Option[PackageRebate] = None
)

case class EnrichedPackageProductDisplayPrice(
    productId: String,
    productType: PackageProductType,
    total: Option[PackageDisplayBasis],
    originalPrice: Option[PackageDisplayBasis],
    totalSaving: Option[PackageSaving],
    channelSaving: Option[PackageSaving],
    crossedOut: Option[PackageDisplayBasis],
    pseudoCoupon: Option[PackageDisplayBasis],
    rebate: Option[PackageRebate],
    charges: Seq[EnrichedCharge],
    additionalRate: Option[PackageDisplayBasis] = None,
    crossedOutSaving: Option[PackageDetailedSaving] = None,
    additionalRateSaving: Option[PackageDetailedSaving] = None
)

@SerialVersionUID(1L)
case class SoldOutPricing(display: DisplayBasis)

@SerialVersionUID(52L)
case class EnrichedPointMax(
    channelId: Long,
    partnerLoyaltyProgramId: Long,
    title: String,
    symbol: String,
    description: String,
    isEligible: Boolean,
    point: Long,
    value: Double
)

@SerialVersionUID(53L)
case class EnrichedChargeBreakdown(
    name: String,
    description: String,
    chargeType: ChargeType,
    id: Int,
    pay: PayType,
    basis: ApplyType,
    quantity: Int,
    percentage: Double,
    price: models.starfruit.Price,
    total: models.starfruit.Price,
    daily: Seq[Daily],
    isInclude: Boolean,
    display: Option[Display],
    protoTypeID: Int,
    netDisplay: Option[Display] = None,
    netPrice: models.starfruit.Price
)

@SerialVersionUID(54L)
case class EnrichedDMCPolicyText(externalData: String, blockId: Option[String], cancellationText: String)

@SerialVersionUID(55L)
case class EnrichedCapacity(
    adults: Int,
    children: Int,
    extraBed: Int,
    occupancy: Int,
    symbol: String,
    @JsonProperty(value = "maxExtraBed", required = false)
    maxExtraBed: Int,
    numberOfGuestsWithoutRoom: Int,
    description: Option[String] = None,
    allowedFreeChildrenAndInfants: Option[Int] = None,
    perOfferMaxFreeChildren: Option[Int] = None,
    perOfferFreeChildren: Option[Int] = None,
    roomAllocationInfo: Option[Seq[RoomAllocationInfo]] = None
)

@SerialVersionUID(56L)
case class EnrichedRoomFlags(isAveragePrice: Boolean)

@SerialVersionUID(57L)
case class EnrichedPartnersCharge(
    id: Int,
    chargeType: ChargeType,
    name: String,
    description: String,
    defaultDescription: Option[String],
    amount: Double,
    percent: Double,
    chargeOption: ChargeOption,
    originalMethod: ApplyType,
    tax: Double,
    fees: Double,
    taxable: Option[Boolean] = None,
    priceBreakdown: PartnersPriceBreakdown,
    taxProtoTypeId: Int,
    whomToPay: Option[WhomToPayType]
)

@SerialVersionUID(58L)
case class EnrichedPartnersPrice(
    perBook: Option[EnrichedPartnersRoomAndExtraBedCharge],
    perNight: Option[EnrichedPartnersRoomAndExtraBedCharge],
    perRoomPerBook: Option[EnrichedPartnersRoomAndExtraBedCharge],
    perRoomPerNight: Option[EnrichedPartnersRoomAndExtraBedCharge],
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    chargeDiscount: Option[Double] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    displayDiscount: Option[Double] = None
)

@SerialVersionUID(59L)
case class EnrichedPartnersSurcharge(
    id: Int,
    name: String,
    description: String,
    defaultDescription: Option[String],
    amount: Double,
    chargeOption: ChargeOption,
    originalMethod: ApplyType,
    tax: Double,
    fees: Double,
    priceBreakdown: PartnersPriceBreakdown,
    margin: String = "n"
)

@SerialVersionUID(60L)
case class EnrichedPartnersRoomAndExtraBedCharge(
    priceBreakdown: Option[PartnersPriceBreakdown],
    price: Option[models.starfruit.Price],
    dailyRate: Seq[PartnersDailyRate] = List.empty,
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    sellInWithDisplayDiscount: Option[Double] = None,
    @JsonDeserialize(contentAs = classOf[java.lang.Double])
    sellInWithChargeDiscount: Option[Double] = None,
    affiliatePrices: Option[AffiliatePrices]
)

@SerialVersionUID(61L)
case class EnrichedPartnerSurcharges(
    perBook: Option[Seq[EnrichedPartnersSurcharge]],
    perNight: Option[Seq[EnrichedPartnersSurcharge]],
    perRoomPerBook: Option[Seq[EnrichedPartnersSurcharge]],
    perRoomPerNight: Option[Seq[EnrichedPartnersSurcharge]]
)

@SerialVersionUID(62L)
case class EnrichedPartnerPromotionSavings(
    perBook: Double,
    perNight: Double,
    perRoomPerBook: Double,
    perRoomPerNight: Double
)

@SerialVersionUID(63L)
case class EnrichedPartnerCancellation(policy: Seq[PartnerCancellationPolicy], policyDate: Seq[PartnersDailyRate])

@SerialVersionUID(64L)
case class EnrichedPartnerTaxAndFeeBreakdown(
    id: Int,
    taxAndFeeType: String,
    description: String,
    defaultDescription: String,
    method: String,
    base: String,
    taxable: String,
    percent: Double,
    amount: Double,
    total: Double,
    option: ChargeOption,
    taxProtoTypeId: Int,
    whomToPay: Option[WhomToPayType] = None
)

@SerialVersionUID(65L)
case class EnrichedPartnerTaxAndFee(
    perBook: Option[Seq[EnrichedPartnerTaxAndFeeBreakdown]],
    perNight: Option[Seq[EnrichedPartnerTaxAndFeeBreakdown]],
    perRoomPerBook: Option[Seq[EnrichedPartnerTaxAndFeeBreakdown]],
    perRoomPerNight: Option[Seq[EnrichedPartnerTaxAndFeeBreakdown]]
)

@SerialVersionUID(66L)
case class EnrichedPartners(
    prices: EnrichedPartnersPrice,
    surcharges: EnrichedPartnerSurcharges,
    promotions: EnrichedPartnerPromotionSavings,
    decimalPlaces: Option[Int] = None,
    cancellation: Option[EnrichedPartnerCancellation] = None,
    taxBreakdown: Option[EnrichedPartnerTaxAndFee] = None,
    included: Option[String] = None,
    excluded: Option[String] = None
)

@SerialVersionUID(67L)
case class EnrichedPseudoCouponMessage(
    code: String,
    description: String,
    downlift: Double,
    couponCongratText: String,
    couponEndingText: String
)

@SerialVersionUID(68L)
case class EnrichedPriceMessaging(isEligible: Boolean, description: String, tooltip: String)

@SerialVersionUID(69L)
case class EnrichedCampaignPromotion(
    campaignId: Int,
    cid: Int,
    promotionCode: String,
    description: String,
    isMarkUse: Boolean,
    campaignType: Option[CampaignType]
)

@SerialVersionUID(70L)
case class EnrichedCampaignDiscount(
    discountAmount: Double,
    discountType: CampaignDiscountType,
    campaignId: Int,
    promotionCode: String,
    promotionText: String,
    childPromotions: List[ChildPromotion] = List.empty
)

@SerialVersionUID(71L)
case class EnrichedCampaign(
    campaignId: Int,
    cid: Int,
    promotionCode: String,
    description: String,
    isMarkUse: Boolean,
    isSelected: Boolean,
    inapplicableReasonString: Option[String],
    inapplicableReason: Option[IneligiblePromotionReason],
    campaignType: CampaignType,
    campaignDiscountType: Option[CampaignDiscountType] = None,
    originalDiscountPercentage: Option[Double] = None,
    isUnderCap: Boolean = false,
    promotionCap: Option[PromotionCap] = None,
    messages: Option[PromotionInfoMessage] = None,
    originalDiscountAmount: Option[Double] = None,
    originalDiscountCurrencyCode: Option[String] = None,
    validDateType: Option[CampaignValidDateType] = None,
    dateValidFrom: Option[DateTime] = None,
    dateValidUntil: Option[DateTime] = None,
    isAutoApply: Boolean = false,
    isAutoApplyBookingForm: Boolean = false,
    isStateIdRequired: Option[Boolean] = None,
    hotelFundingText: Option[String] = None,
    campaignGroupId: Option[Int] = None,
    isCashback: Option[Boolean] = None,
    promotionCodeType: Option[PromotionCodeType] = None,
    status: Option[CampaignStatusType] = None,
    cms: Option[Seq[EnrichedPromoCmsData]] = None,
    @JsonDeserialize(using = classOf[OptionalEnrichedCampaignMessagesMapDeserializer])
    campaignMessages: Option[Map[Int, EnrichedCampaignMessage]] = None,
    analyticsInfo: Option[Map[String, String]] = None
)

@SerialVersionUID(1L)
case class EnrichedWalletPromotion(
    campaignId: Int,
    cid: Int,
    promotionCode: String,
    value: Double,
    discountType: Int,
    currencyCode: Option[String],
    displayName: String,
    cms: Option[Seq[EnrichedPromoCmsData]],
    ineligibleReason: Option[IneligiblePromotionReason],
    promoValidationType: Option[Int],
    campaignType: Option[CampaignType] = None,
    isSelected: Option[Boolean] = None
)

case class EnrichedPromoCmsData(
    id: Int,
    params: Map[String, String],
    targetField: Int
)

@SerialVersionUID(1L)
case class EnrichedCampaignMessage(
    message: String,
    params: Map[String, String]
)

@SerialVersionUID(74L)
case class BORTooltip(title: Option[String] = None, description: Option[String] = None)

@SerialVersionUID(75L)
case class BORInfo(message: Option[String] = None, toolTip: Option[String] = None)

@SerialVersionUID(76L)
case class EnrichedBOR(
    confirmByMins: Int = 0,
    info: BORInfo,
    message: Option[String] = None,
    toolTip: BORTooltip,
    borType: Int = 0
)

@SerialVersionUID(1L)
case class BORSupplier(confirmByMins: Int)

@SerialVersionUID(77L)
case class LocalizationResponse(
    numberFormat: String,
    numberDecimalSeparator: Char,
    numberGroupingSeparator: Char,
    calendarDateFormat: String,
    calendarMonthYearFormat: String,
    showPriceBeforeCurrency: Boolean
)

object LocalizationResponse {

  def apply(locale: String): Option[LocalizationResponse] = {
    val localization = Localization(locale)
    Some(
      LocalizationResponse(
        localization.getNumberFormat,
        localization.getNumberDecimalSeparator,
        localization.getNumberGroupingSeparator,
        localization.getCalendarDateFormat,
        localization.getCalendarMonthYearFormat,
        localization.isShowPriceFirst
      )
    )
  }

}

@SerialVersionUID(78L)
case class EnrichedEBEBooking(
    creditCard: EnrichedEBECreditCard,
    discount: Option[EnrichedCampaignDiscount] = None,
    // guarantee to have only one hotel
    hotel: List[EnrichedEBEHotel]
)

@SerialVersionUID(79L)
case class EnrichedEBECreditCard(
    payment: EnrichedEBEPayment,
    chargeOption: Option[Int],
    fullyAuthDate: Option[DateTime],
    fullyChargeDate: Option[DateTime]
)

@SerialVersionUID(80L)
case class EnrichedEBEPayment(
    exchangeRate: Double,
    giftCardAmount: Double,
    giftCardAmountInUSD: Double,
    paymentAmount: Double,
    paymentAmountInUSD: Double,
    paymentCurrency: String,
    rewardsRedeemedPoint: Long,
    rewardsSaving: Double,
    rewardsSavingInUSD: Double,
    siteExchangeRate: Double,
    upliftAmount: Double,
    upliftExchangeRate: Double,
    // M150 = 1, FXI = 3, None = 0
    exchangeRateOption: Option[Int],
    @deprecated("to remove EBE didn't use. potentially break affiliate CI")
    destinationCurrency: Option[String] = None,
    @deprecated("to remove EBE didn't use. potentially break affiliate CI")
    destinationExchangeRate: Double = 1,
    @deprecated("to remove EBE didn't use. potentially break affiliate CI")
    rateQuoteID: Long = 0,
    cashbackAmount: Option[Double] = None,
    cashbackAmountInUSD: Option[Double] = None,
    externalLoyalty: Option[BookingExternalLoyaltyPayment] = None,
    localCurrencyUpliftAmount: Option[PaymentBreakdown] = None,
    cashbackPayment: Option[BookingCashbackPayment] = None,
    paymentAmountBeforeLoyalty: Option[Double] = None,
    paymentUsdAmountBeforeLoyalty: Option[Double] = None
)

@SerialVersionUID(81L)
case class EnrichedEBEHotel(
    checkIn: DateTime,
    checkOut: DateTime,
    numberOfAdults: Int,
    numberOfChildren: Int,
    numberOfRooms: Int,
    occFreeSearch: Boolean = false,
    room: List[EnrichedBookingRoom],
    stayType: Option[StayType] = None
)

@SerialVersionUID(82L)
case class EnrichedDiscountMessage(
    discountType: Int,
    discountTextMessage: Option[String],
    messageType: Int,
    text: String
)

@SerialVersionUID(83L)
@deprecated("Move to PropertyUspRank", "v3.30.0")
case class PropertyUsp(id: String, text: String, params: List[String])

@SerialVersionUID(84L)
case class M150Info(
    isEligible: Boolean = false,
    text: Option[String] = None,
    usdCurrencyUpliftAmount: Option[Double] = None,
    payInCurrencyUpliftAmount: Option[Double] = None,
    transparencyVersion: Option[M150TransparencyVersionType] = None
)

@SerialVersionUID(85L)
case class PropertyUspRank(
    id: String,
    name: Option[String],
    icon: Option[String],
    value: Option[Double],
    text: Option[String],
    params: List[String],
    rank: Int
)

@SerialVersionUID(86L)
case class DFMetaResult(propertyToken: Option[ResponseStateToken] = None, isBlocked: Option[Boolean] = None)

case class WithPropertyToken[T](meta: DFMetaResult, data: T)

/** Using for popup
  */
@SerialVersionUID(87L)
case class EnrichedTaxReceiptDetail(title: String, description: String)

@SerialVersionUID(88L)
case class EnrichedPayAtCheckIn(
    isEligible: Boolean = false,
    authDate: DateTime,
    chargeDate: DateTime,
    title: String = "",
    description: String = "",
    symbol: String
)

@SerialVersionUID(89L)
case class EnrichedNoPrePaymentRequired(isEligible: Boolean, title: String, description: String, symbol: String)

@SerialVersionUID(1L)
case class EnrichedSuggestedRoom(
    roomUid: String,
    quantity: Int,
    pricing: Map[String, EnrichedPricing],
    pointsMax: Option[EnrichedPointMax],
    pointsMaxBadge: Option[PointsMaxBadge],
    corBreakdown: CorBreakdown,
    pricingMessages: Option[Seq[RoomPricingMessage]]
)

@SerialVersionUID(1L)
case class EnrichedMultiRoomSuggestion(
    suggestedRooms: List[EnrichedSuggestedRoom],
    pricing: Map[String, EnrichedPricing]
)

@SerialVersionUID(91L)
case class MessageInformation(title: String, description: String)

@SerialVersionUID(92L)
case class SoldOutRoomInfo(roomId: RoomIdType, price: DisplayBasis, bookingDateTime: DateTime)

@SerialVersionUID(92L)
case class EnrichedSoldOutRooms(
    masterRooms: List[EnrichedMasterRoom],
    badgeText: String,
    description: String,
    @deprecated("Use this information from soldOutRoomInfo.price")
    soldOutPrices: SoldOutPricesType = Map.empty,
    soldOutRoomInfo: Map[RoomIdType, SoldOutRoomInfo] = Map.empty
)

@SerialVersionUID(93L)
case class TopSellingPoint(
    id: Int,
    text: Option[String] = None,
    icon: Option[String] = None,
    value: Option[Double] = None
)

@SerialVersionUID(94L)
case class RecomendationMseResults(
    supplierId: Long,
    priceBasis: DisplayBasis,
    supplierName: String,
    @deprecated("Use supplierName instead")
    englishSupplierName: String
)

@SerialVersionUID(95L)
case class BeddingInfo(
    id: Int,
    name: String,
    icon: String
)

@SerialVersionUID(1L)
case class PropertyFeature(id: Int, description: String, icon: String)

/** @param inDemand
  *   define as List of Date that property are in demand (checkin date booked more than effective statistical value)
  */
@SerialVersionUID(1L)
case class InDemand(inDemandDate: List[DateTime])

@SerialVersionUID(96L)
case class RoomPricingMessage(location: Int, cmsTexts: Map[Int, String])

@SerialVersionUID(104L)
case class OccupancyMessage(`type`: Int, message: String)

@SerialVersionUID(1L)
case class SoldOutRoomsSummary(masterRoomCount: Int)

@SerialVersionUID(98L)
case class GeoPlaceSearchResult(places: Seq[GeoPlace])

@SerialVersionUID(99L)
case class GeoPlace(
    id: Int,
    landmarkTypeId: Int,
    placeType: Int,
    name: String,
    abbreviation: String,
    latitude: Double,
    longitude: Double
)

@SerialVersionUID(97L)
case class FencedRate(origin: Option[String] = None, fencedRateKey: Option[FencedRateKey] = None)

@SerialVersionUID(103L)
case class CityBookingHistory(
    cityId: CityIdType,
    bookingCountLast24hours: Int = 0,
    bookingCountPastWeek: Int = 0,
    minPerBooking: Int = 0
)

@SerialVersionUID(1L)
case class AlternativeDateWithPrice(date: DateTime, price: Double, priceDifference: Double)

@SerialVersionUID(1L)
case class PricingDisplayPresentation(
    @deprecated("DeIntegrated") alternativeCheapestDates: Seq[AlternativeDateWithPrice] = Nil,
    pastBookingInfoPerRoom: Option[EnrichedPastBookingInfoResponse] = None,
    cxlUpSelling: Option[Seq[CancellationUpSells]] = None,
    breakfastwithCxlUpSelling: Option[BreakfastWithCancellationUpSells] = None,
    agodaRefundInfo: Option[AgodaRefundInfo] = None,
    breakfastUpSelling: Option[BreakfastWithCancellationUpSells] = None,
    roomNightUpselling: Option[RoomNightUpSell] = None
)

@SerialVersionUID(1L)
case class RatePlanMainImage(id: Int, caption: String, locations: Map[String, String])

case class OriginalPrice(
    sellIn: Double,
    sellEx: Double,
    currency: Currency,
    usdSellIn: Double,
    usdSellEx: Double,
    reqSellIn: Double,
    reqSellEx: Double
)

@SerialVersionUID(1L)
case class OriginalRoomDetail(
    originalCancellationType: CancellationType,
    originalBreakfast: Boolean,
    originalOccupancy: Int,
    originalBenefit: List[Int],
    whatChanged: List[RoomCategoryChangeType],
    originalPrice: Option[OriginalPrice] = None
)

@SerialVersionUID(1L)
case class RecommendedUpgrade(
    priceDiff: Double,
    roomSizeDiffPercent: Option[Double]
)
