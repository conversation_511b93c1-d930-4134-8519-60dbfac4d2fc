package enumerations

import enumerations.RecommendationType._
import enumerations.RecommendationTypes.{ Comparison, MapRecommendation, None, PastBookings, RecentlyViewed }
import enumeratum.{ Enum, EnumEntry }

sealed abstract class RecommendationType(val i: Int) extends EnumEntry {
  override def hashCode(): Int = i

  override def equals(obj: Any): Boolean = obj match {
    case r: RecommendationType => r.i == i
    case _                     => false
  }

  def this() = this(hashCode)
}

object RecommendationTypes extends Enum[RecommendationType] {
  lazy val getRecommendationType: Int => RecommendationType = fields.getOrElse(_, RecommendationTypes.None)
  val values                                                = findValues
  val fields                                                = values.map(v => (v.i, v)).toMap

  case object None              extends RecommendationType(NONE)
  case object MapRecommendation extends RecommendationType(MAP)
  case object Comparison        extends RecommendationType(COMPARISON)
  case object RecentlyViewed    extends RecommendationType(RECENTLYVIEWED)
  case object PastBookings      extends RecommendationType(PASTBOOKINGS)
  case object UpTier            extends RecommendationType(UPTIER)

  def getRecommendationTypeString(recommendationType: RecommendationType): String =
    recommendationType match {
      case None              => "None"
      case MapRecommendation => "MapRecommendation"
      case Comparison        => "Comparison"
      case RecentlyViewed    => "RecentlyViewed"
      case PastBookings      => "PastBookings"
      case UpTier            => "UpTier"
      case _                 => "Unknown"
    }

}

object RecommendationType {
  final val NONE           = -1
  final val MAP            = 0
  final val COMPARISON     = 1
  final val RECENTLYVIEWED = 2
  final val PASTBOOKINGS   = 3
  final val UPTIER         = 4
}
