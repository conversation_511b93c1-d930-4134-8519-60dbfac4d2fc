package constant

/** Created by s<PERSON><PERSON><PERSON><PERSON> on 3/14/2017 AD.
  */
object Symbols {

  val BreakfastInclude               = "breakfast-include"
  val BreakfastIncludePropertySymbol = "breakfast"
  val FreeCancellation               = "free-cancellation"
  val PayAtHotel                     = "pay-at-the-place"
  val PayLater                       = "book-now-pay-later"
  val PayAtCheckIn                   = "pay-on-checkin"
  val PointsMax                      = "flight-earn"
  val PointsMaxPropertySymbol        = "pointsmax-line-logo"
  val AreaMatrixResultSymbol         = "ficon-human-large"
  val UnavailableInfoSymbol          = "ficon-triangle-warning"
  val UnavailableUrgencySymbol       = "ficon-time-icon"
  val FilterAveragePriceSymbol       = "ficon-menu-price-display"
  val CheapestRoomFilter             = "mmb-my-rewards"
  val noCCPropertyPageFilter         = "payment-option-no-credit-card"
  val InsiderDealSymbol              = "gift-souvenir-shop"
  val unfilledFavoriteIcon           = ".ficon-favorite"
  val filledFavoriteIcon             = ".ficon-favorite-filled"
  val nonSmokingFilter               = "non-smoking-room"
  val BreakfastAndDinnerInclude      = "half-full-board"
  val chineseBadgeIcon               = "ficon-chinese-friendly"
  val DealOfTheDayIcon               = "ficon-alarm-clock"
  val LuxuryBadgeIcon                = "ficon-luxury"
  val BudgetBadgeIcon                = "ficon-budget"
  val BestValueForMoney              = "ficon-menu-price-display"
  val BestLocationForSoloTraveller   = "ficon-walking"
  val CheapestStarRating             = "ficon-best-value"
  val FreeWifiFilterIcon             = "wifi"
  val kitchenAvailableIcon           = "kitchenette"
  val roomSize                       = "sqm"
  val extraBedIcon                   = "single-bed"
  val balconyTerraceIcon             = "balcony-terrace"
  val FlashSaleIcon                  = "ficon-flash-sale"
  val recommendedForFamiliesIcon     = "family-friendly"
  val bedroomIcon                    = "bedroom"
  val babyCotIcon                    = "baby-cot"
  val twinBedIcon                    = "twin-bed"
  val doubleBedIcon                  = "double-bed"
  val queenBedIcon                   = "queen-bed"
  val kingBedIcon                    = "king-bed"
  val gardenViewIcon                 = "garden"
  val cityViewIcon                   = "city-view"
  val seaViewIcon                    = "sea-view"
  val oceanViewIcon                  = "ocean-view"
  val mountainViewIcon               = "mountain-view"
  val lakeViewIcon                   = "lake-view"
  val poolViewIcon                   = "pool"
  val interConnectingRooms           = "interconnecting-room-available"
  val familyLine                     = "family-line"
  val AirConditioningIcon            = "ficon-solid-air-conditioning"
  val AirportTransferSolidIcon       = "ficon-solid-airport-transfer"
  val BeachIcon                      = "ficon-solid-beach"
  val FitnessIcon                    = "ficon-solid-fitness"
  val FreeWifiIcon                   = "ficon-solid-free-wi-fi"
  val GardenIcon                     = "ficon-solid-garden"
  val HairDryerIcon                  = "ficon-solid-hair-dryer"
  val RestaurantIcon                 = "ficon-solid-restaurant"
  val SpaIcon                        = "ficon-solid-spa"
  val SwimmingPoolIcon               = "ficon-solid-swimming-pool"
  val TransportationIcon             = "ficon-solid-transportation"
  val RoomService24hourIcon          = "ficon-solid-24hour-room-service"
  val BarIcon                        = "ficon-solid-bar"
  val CarParkIcon                    = "ficon-solid-car-park"
  val FrontDesk24hourIcon            = "ficon-solid-24hour-front-desk"

  val beachBadge           = "ficon-solid-beach"
  val NoPrePaymentRequired = ".ficon-prepayment"

  val KidsClubIcon                     = "kids-club"
  val FreeChildAgesMessageIcon         = "child-line"
  val KidsPoolIcon                     = "pool-kids"
  val InterconnectingRoomIcon          = "interconnecting-room-available"
  val DistanceFromPublicTransportation = "train-new"
  val AirportTransferIcon              = "airport-transfer"
  val FamilyRoomIcon                   = "family-room"
  val JapaneseStyleRoomIcon            = "tatami"
  val JapaneseWesternStyleRoomIcon     = "japanese-western-mix"
  val VoucherIcon                      = "congratulations"
  val DayUseIcon                       = "dayuse"
  val OvernightIcon                    = "overnight"

  val LuxuryIcon   = "luxury"
  val PinHeartIcon = "pin-heart-of-city"

  val DinnerIcon   = "dinner"
  val CheckInIcon  = "check-in"
  val CheckOutIcon = "check-out"

  val ASOIcon = "agodaSpecialOffers"

  val BreakfastIncludeDroneIcon          = "fill.food.coffee"
  val FreeCancellationDroneIcon          = "fill.brand.easycancel"
  val roomSizeDroneIcon                  = "fill.facility.room-size"
  val PayLaterDroneIcon                  = "fill.symbol.calendar"
  val PayAtHotelDroneIcon                = "fill.service.hand-cash"
  val PayAtCheckInDroneIcon              = "fill.symbol.calendar-check"
  val PointsMaxDroneIcon                 = "fill.transportation.flight3"
  val noCCPropertyPageFilterDroneIcon    = "fill.symbol.card-no"
  val nonSmokingFilterDroneIcon          = "fill.facility.smoke-no"
  val kitchenAvailableDroneIcon          = "fill.amenity.kitchenware"
  val balconyTerraceDroneIcon            = "fill.facility.balcony"
  val recommendedForFamiliesDroneIcon    = "fill.people.family2"
  val bedroomDroneIcon                   = "fill.symbol.door"
  val babyCotDroneIcon                   = "fill.facility.baby-cot"
  val twinBedDroneIcon                   = "fill.facility.bed-twin"
  val doubleBedDroneIcon                 = "fill.facility.bed-double"
  val queenBedDroneIcon                  = "fill.facility.bed-queen"
  val kingBedDroneIcon                   = "fill.facility.bed-king"
  val gardenViewDroneIcon                = "fill.activity.flower"
  val cityViewDroneIcon                  = "fill.symbol.city"
  val seaViewDroneIcon                   = "fill.activity.beach"
  val oceanViewDroneIcon                 = "fill.activity.beach"
  val mountainViewDroneIcon              = "fill.activity.forest"
  val lakeViewDroneIcon                  = "fill.activity.forest"
  val poolViewDroneIcon                  = "fill.facility.pool-indoor"
  val BreakfastAndDinnerIncludeDroneIcon = "fill.service.roomservice"
  val JapaneseStyleRoomDroneIcon         = "fill.facility.tatami"
  val JapaneseWesternStyleRoomDroneIcon  = "fill.facility.tatami-mix"
  val VoucherDroneIcon                   = "fill.activity.ticket"
  val PinHeartDroneIcon                  = "fill.symbol.city-pin"
  val LuxuryDroneIcon                    = "fill.accommodation.hotel-luxury"
  val DinnerDroneIcon                    = "fill.food.fork-knife"
  val CheckInDroneIcon                   = "fill.symbol.calendar-in"
  val CheckOutDroneIcon                  = "fill.symbol.calendar-out"
  val AirportTransferDroneIcon           = "fill.transportation.car-flight"
  val DayUseDroneIcon                    = "fill.symbol.sun"
  val OvernightDroneIcon                 = "fill.symbol.moon"
  val ASODroneIcon                       = "fill.brand.specialoffers"
}
