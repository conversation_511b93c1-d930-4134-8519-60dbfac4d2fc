name := "models"

Compile / packageBin / publishArtifact := true
Compile / packageSrc / publishArtifact := true

libraryDependencies ++= Seq(
  "com.softwaremill.sttp.tapir" %% "tapir-openapi-docs" % Versions.tapir, // Need for Schema annotation
  // "com.softwaremill.sttp.tapir" %% "tapir-enumeratum"                         % Versions.tapir,// Todo: fix NPC confict and test this
  /** JAVA LIB * */
  "de.ruedigermoeller" % "fst"       % Versions.fst, // TODO: REMOVE After Remove PropertyHttpClient
  "org.slf4j"          % "slf4j-api" % Versions.slf4j,
  "com.fasterxml.jackson.datatype"   % "jackson-datatype-joda"    % Versions.jackson,
  "com.fasterxml.jackson.datatype"   % "jackson-datatype-jsr310"  % Versions.jackson,
  "com.fasterxml.jackson.core"       % "jackson-core"             % Versions.jackson,
  "com.fasterxml.jackson.core"       % "jackson-databind"         % Versions.jackson,
  "com.fasterxml.jackson.core"       % "jackson-annotations"      % Versions.jackson,
  "com.fasterxml.jackson.dataformat" % "jackson-dataformat-smile" % Versions.jackson,
  ("org.asynchttpclient"             % "async-http-client"        % Versions.asyncHttpClient)
    .excludeAll(ExclusionRule(organization = "io.netty"))
    .force(),
  ("org.asynchttpclient" % "async-http-client-netty-utils" % Versions.asyncHttpClient)
    .excludeAll(ExclusionRule(organization = "io.netty")),
  ("org.apache.lucene"      % "lucene-core"           % Versions.lucene).force(),
  "com.agoda.adp.messaging" % "adp-messaging-client"  % Versions.adpMessaging % "compile",
  "com.agoda.adp.messaging" % "adp-messaging-logback" % Versions.adpMessaging,
  "org.mockito"             % "mockito-core"          % Versions.mockito      % Test,

  // for ISO-6709 string parsing: https://github.com/sualeh/pointlocation6709
  "us.fatehi" % "pointlocation6709" % Versions.pointLocation6709,

  /** SCALA LIB * */
  ("com.agoda.dfapi" %% "dfapi_models" % Versions.Agoda.dfapi)
    .excludeAll(
      ExclusionRule(organization = "com.agoda.upi"), // ToDo: temporary to unblock CI. Have to be removed
      ExclusionRule(organization = "io.grpc"),
      ExclusionRule(organization = "io.netty"),
      ExclusionRule(organization = "com.typesafe.akka"),
      ExclusionRule(organization = "com.agoda.experiments"),
      ExclusionRule("io.opentracing"),
      ExclusionRule(organization = "org.specs2"),
      ExclusionRule(organization = "org.scalaz"),
      ExclusionRule("com.agoda", s"supplier-pricing_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda", s"rflows_${scalaBinaryVersion.value}"),
      ExclusionRule("com.beachape", s"enumeratum_${scalaBinaryVersion.value}"),
      ExclusionRule("com.thesamet.scalapb", s"scalapb-runtime_${scalaBinaryVersion.value}"),
      ExclusionRule("com.thesamet.scalapb", s"scalapb-json4s_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.commons", s"ag-grpc-client_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.upi", "upi-models"),
      ExclusionRule("com.agoda", s"ypl_cancellation_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.dfapi", s"ypl-core_${scalaBinaryVersion.value}"),
      ExclusionRule(organization = "com.agoda.everest"),
      ExclusionRule(organization = "com.thesamet.scalapb"),
      ExclusionRule("com.agoda.commons", "ag-consul_2.12"),
      ExclusionRule("org.sangria-graphql", s"sangria_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi-streaming", s"models_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.ml", s"pricing_client_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.dfapi", s"dfapi-models-metadata_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.platform", s"service-context_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.platform", s"service-context-akka-http_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.platform", s"service-context-client-profile_${scalaBinaryVersion.value}")
    ),
  ("com.agoda.dfapi" %% "ypl-models" % Versions.Agoda.dfapi)
    .excludeAll(
      ExclusionRule(organization = "com.thesamet.scalapb"),
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}")
    ),
  ("com.agoda.dfapi" %% "dfapi_price-calculation" % Versions.Agoda.dfapi)
    .excludeAll(
      ExclusionRule(organization = "com.thesamet.scalapb"),
      ExclusionRule("com.agoda.dfapi", s"dfapi_utils_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.papi", s"enum-commons_${scalaBinaryVersion.value}")
    ),
  "com.agoda.commons"           %% "ag-protobuf-scalapb-0-11-x-df" % Versions.Agoda.agProtobuf,
  "com.agoda"                   %% "rflows"                        % Versions.rflows,
  ("com.agoda.content-platform" %% "models"                        % Versions.Agoda.contentPlatform)
    .withSources()
    .changing()
    .exclude("com.agoda.commons", s"ag-couchbase_${scalaBinaryVersion.value}")
    .exclude("com.agoda.commons", s"ag-sql_${scalaBinaryVersion.value}")
    .exclude("joda-time", "joda-time")
    .exclude("com.fasterxml.jackson.module", s"jackson-module-scala_${scalaBinaryVersion.value}")
    .exclude("org.apache.lucene", "lucene-core")
    .excludeAll(
      ExclusionRule("com.softwaremill.sttp.tapir"),
      ExclusionRule("com.agoda.platform", s"service-context_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.platform", s"service-context-akka-http_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.platform", s"service-context-client-profile_${scalaBinaryVersion.value}")
    ),
  "com.agoda.adp.messaging" %% "adp-messaging-client-scala"  % Versions.adpMessaging,
  ("com.agoda.ml"           %% "zenith_model_scalapb-0-11-x" % Versions.zenith_model).withSources().changing(),
  ("com.agoda.commons"      %% "data"                        % Versions.Agoda.commons)
    .withSources()
    .changing()
    .exclude("de.ruedigermoeller", "fst"),
  ("com.agoda.commons"        %% "utils"               % Versions.Agoda.commons).withSources().changing().force(),
  ("com.agoda.commons"        %% "metrics"             % Versions.Agoda.commons).withSources().changing().force(),
  ("com.agoda.commons"        %% "localization"        % Versions.Agoda.commons).withSources().changing().force(),
  ("com.agoda.commons"        %% "sql"                 % Versions.Agoda.commons).withSources().changing().force(),
  ("com.agoda.commons"        %% "traffic"             % Versions.Agoda.commons).withSources(),
  "com.agoda.commons"         %% "ag-logging"          % Versions.Agoda.logging,
  "org.specs2"                %% "specs2-core"         % Versions.specs2               % Test,
  "org.specs2"                %% "specs2-junit"        % Versions.specs2               % Test,
  "org.specs2"                %% "specs2-mock"         % Versions.specs2               % Test,
  "org.scalatest"             %% "scalatest"           % Versions.scalaTest            % Test,
  "com.beachape"              %% "enumeratum"          % Versions.enumeratum,
  "org.scalatestplus"         %% "mockito-3-4"         % Versions.scalaTestPlusMockito % "test",
  ("com.agoda.papi-streaming" %% "models"              % Versions.Agoda.papistreaming).withSources(),
  ("com.agoda.pricestream"    %% "price-stream-models" % Versions.priceStreamClient)
    .excludeAll(ExclusionRule(organization = "com.typesafe.akka")),
  "com.agoda.papi"    %% "enum-commons" % Versions.Agoda.enumcommons,
  "com.agoda.everest" %% "sl-client"    % Versions.slService,

  // Jackson
  "com.fasterxml.jackson.module"   %% "jackson-module-scala"        % Versions.jackson,
  "org.scalatest"                  %% "scalatest"                   % Versions.scalaTest % "test",
  ("com.agoda.experiments"         %% "experiments-platform-client" % Versions.Agoda.experiment).changing(),
  "com.agoda.universal.cms.client" %% "cms-scala-client"            % Versions.Agoda.universalCmsClient,
  ("com.agoda.payment"             %% "payment-client-utils"        % Versions.Agoda.paymentApiClientUtils).excludeAll(
    ExclusionRule("com.fasterxml.jackson.core", "jackson-core"),
    ExclusionRule("com.fasterxml.jackson.core", "jackson-databind"),
    ExclusionRule("com.fasterxml.jackson.module", s"jackson-module-scala_${scalaBinaryVersion.value}"),
    ExclusionRule("com.fasterxml.jackson.datatype", "jackson-datatype-joda"),
    ExclusionRule("com.fasterxml.jackson.datatype", "jackson-datatype-jsr310"),
    ExclusionRule(organization = "io.netty"),
    ExclusionRule(organization = "com.typesafe.scala-logging"),
    ExclusionRule(organization = "com.agoda.commons")
  ),
  ("com.agoda.upi" %% "upi-models" % Versions.Agoda.upi)
    .excludeAll(
      ExclusionRule(organization = "io.circe"),
      ExclusionRule("com.agoda.commons", s"ag-http-client-v2_${scalaBinaryVersion.value}"),
      ExclusionRule("com.agoda.commons", s"ag-graphql-schema_${scalaBinaryVersion.value}")
    ),
  "com.github.nscala-time" %% "nscala-time"  % Versions.nscalaTime,
  ("com.agoda.ml"          %% "jarvis_model" % Versions.jarvis).excludeAll(
    ExclusionRule(organization = "io.netty"),
    ExclusionRule(organization = "io.grpc")
  )
)

libraryDependencies ~= { _.map(moduleID => Versions.commonExclude(moduleID)) }
