# yaml-language-server: $schema=https://devstack.agodadev.io/api/v1/schema/devstack-v1.json
schema: v1
service: propertyapisearchdocker
environments:
  dev-ci:
    description: PAPI Dev
    spec:
      db:
        migration: true
      artifact:
        image:
          repository: papi/search/propertyapi/develop
          tag: latest
          build:
            context: .
            dockerfile: DockerfileGitlab
      resources:
        requests:
          memory: 8Gi
          cpu: "5"
        limits:
          memory: 8Gi
          cpu: "5"
      ports:
        - number: 8080
          protocol: http
        - number: 5005
          protocol: tcp
        - number: 3333
          protocol: tcp
      envs:
        CONFIG_FILE: "dev-ci-devstack.conf"
        TZ: Asia/Bangkok
        DEPLOYMENT: dev-ci
      consul:
        enabled: true
      vault:
        enabled: true
      ingress:
        enabled: true
        port: 8080
      healthCheck:
        startup:
          httpGet:
            port: 8080
            path: /ping
          initialDelaySeconds: 30
          periodSeconds: 5
          failureThreshold: 40
        readiness:
          httpGet:
            port: 8080
            path: /ping
          initialDelaySeconds: 5
          periodSeconds: 5
          failureThreshold: 3
        liveness:
          httpGet:
            port: 8080
            path: /ping
          initialDelaySeconds: 5
          periodSeconds: 5
          failureThreshold: 3
      waitFor:
        services:
          - name: cdb
            port: 1433
          - name: dfcitydb
            port: 1433
    dependencies:
      - registry:
          service: db-cbcache
          environment: dev
          version: latest
      - registry:
          service: dfcitydb
          environment: papi_pinto_qa_data_papi-search-tests
          version: stable
      - registry:
          service: everest-docker
          environment: dev
          version: latest
      - registry:
          service: propertyapicontentdocker
          environment: papi
          version: stable
      - registry:
          service: propertyapipricingdocker
          environment: dev
          version: pull-18151376
      - registry:
          service: featurestore-serving
          environment: dev
          version: latest
      - registry:
          service: pricestreamapidocker
          environment: dev
          version: latest
      - registry:
          service: feast-core
          environment: dev
          version: latest
      - registry:
          service: customerapi
          environment: dev
          version: latest
      - registry:
          service: gp-commission-api
          environment: dev
          version: 921e9b51-5339
      - registry:
          service: cdb
          environment: papi_pinto_qa_data_papi-search-tests
          version: stable
